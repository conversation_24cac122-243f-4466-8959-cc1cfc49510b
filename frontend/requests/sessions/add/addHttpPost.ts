import { sessionLinks } from "@/links/sessions";
import { makeRequest } from "@/utils/api";
import axios from "axios";
import { Dispatch } from "redux";

interface IProp {
  sessionId: string;
  eventId: string;
  accountId: string;
  accountName: string;
  action: "read";
  deleted: boolean;
  properties: {
    name: string;
    description: string;
    value: string;
  }[];
}

export const addHttpPostAction = async (body: IProp, dispatch: Dispatch) => {
  return makeRequest({
    url: sessionLinks.addResponse.httpPost,
    method: "POST",
    data: body,
    dispatch,
    successMessage: "Response added",
  });
}; 