import { makeRequest } from "@/utils/api";
import { integrationsLink } from "@/links/integration";

interface IProp {
  body: {
    orgId: string;
    accountId: string;
  };
  dispatch: any;
}

export const fetchPostRequest = async (data: IProp) => {
  const { body, dispatch } = data;
  const { orgId, accountId } = body;
  return makeRequest({
    url: integrationsLink.postRequest.get(orgId, accountId),
    method: "GET",
    data: body,
    dispatch,
  });
}; 