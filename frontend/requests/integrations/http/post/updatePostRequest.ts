import { makeRequest } from "@/utils/api";
import { integrationsLink } from "@/links/integration";

interface IProp {
  accountId: string;
  body: any;
  dispatch: any;
}

export const updatePostRequest = async (data: IProp) => {
  const { accountId, body, dispatch } = data;
  return makeRequest({
    url: integrationsLink.postRequest.update(body.orgId, accountId),
    method: "PUT",
    data: body,
    dispatch,
    successMessage: "POST request updated",
  });
}; 