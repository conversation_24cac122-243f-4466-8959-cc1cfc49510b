import { makeRequest } from "@/utils/api";
import { integrationsLink } from "@/links/integration";

interface IProp {
  body: any;
  dispatch: any;
}

export const createPostRequest = async (data: IProp) => {
  const { body, dispatch } = data;
  return makeRequest({
    url: integrationsLink.postRequest.create,
    method: "POST",
    data: body,
    dispatch,
    successMessage: "POST request added",
  });
}; 