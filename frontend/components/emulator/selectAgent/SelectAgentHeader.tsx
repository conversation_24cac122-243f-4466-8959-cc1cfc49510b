import { Typography } from "@mui/material";
import React from "react";
import SelectAgentBtn from "./SelectAgentBtn";
import { useSelector } from "react-redux";
import { useRouter } from "next/router";
import { RootState } from "@/store";
import ImportSession from "./ImportSession";

const SelectAgentHeader = () => {
  const router = useRouter();
  const { view } = router.query;
  const handleClick = (view: string) => {
    router.push({
      pathname: "/home/<USER>",
      query: { view },
    });
  };

  return (
    <div className="text-fourth flex justify-between">
      <div className="options">
        <div className="headers flex gap-7 px-3">
          <Typography
            variant="h4"
            sx={{
              color: view === "recent" ? "whitesmoke" : "GrayText",
            }}
            className="cursor-pointer"
            onClick={() => handleClick("recent")}
          >
            Recent
          </Typography>
          {/* <Typography
            variant="h4"
            sx={{
              color: view === "saved" ? "whitesmoke" : "GrayText",
            }}
            className="cursor-pointer"
            onClick={() => handleClick("saved")}
          >
            Saved
          </Typography>
          <Typography
            variant="h4"
            sx={{
              color: view === "query" ? "whitesmoke" : "GrayText",
            }}
            className="cursor-pointer"
            onClick={() => handleClick("query")}
          >
            Query
          </Typography> */}
        </div>
      </div>
      <div className="button flex gap-2">
        <ImportSession />
        <SelectAgentBtn />
      </div>
    </div>
  );
};

export default SelectAgentHeader;
