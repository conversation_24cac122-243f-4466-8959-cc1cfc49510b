import { Typography } from "@mui/material";
import React, { useEffect, useState } from "react";
import { AiOutlineSearch } from "react-icons/ai";
import SingleAgent from "./SingleAgent";
import { getReadyAgents } from "@/requests/agents/basic/getAgent";
import { IAgent } from "@/slices/agents/agentsSlice";
import ButtonSpinner from "@/components/general/buttons/spinner";
import SearchBar from "@/components/organization-tab/general/SearchBar";
import { useDispatch } from "react-redux";

const SelectAgent = () => {
  const dispatch = useDispatch();
  const [agents, setAgents] = useState<IAgent[]>([]);
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    setLoading(true);
    const response = getReadyAgents(
      {
        orgId: localStorage.getItem("orgId") || "",
      },
      dispatch
    );
    response
      .then((res) => {
        setAgents(res?.data.data);
      })
      .finally(() => {
        setLoading(false);
      });
  }, []);

  // Filter agents based on search query
  const filteredAgents = agents?.filter((agent) =>
    agent?.agentName?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(event.target.value);
  };

  return (
    <div className="bg-fourth text-black w-[500px] rounded-lg pb-4">
      <div className="flex justify-between items-center px-6 py-4">
        <Typography variant="h5">Select Agent</Typography>
        <div className="h-[40px] flex items-center">
          <SearchBar
            val={searchQuery}
            onchangeFn={(x: string) => setSearchQuery(x)}
          />
        </div>
      </div>
      {loading ? (
        <div className="w-full flex justify-center items-center">
          <ButtonSpinner />
        </div>
      ) : (
        <>
          <div className="px-6 py-2 flex flex-col gap-3 max-h-[380px] overflow-auto">
            {filteredAgents?.map((agent) => (
              <SingleAgent
                agentName={agent.agentName}
                agentId={agent._id}
                key={"Agent_Ready_" + agent._id}
              />
            ))}
            {agents?.length === 0 && (
              <Typography textAlign={"center"}>
                No agent found. Create one first
              </Typography>
            )}
          </div>
        </>
      )}
    </div>
  );
};

export default SelectAgent;
