import ButtonSpinner from "@/components/general/buttons/spinner";
import { createNewSession } from "@/requests/sessions/basic/createNewSession";
import { snackbarActions } from "@/slices/general/snackbar";
import { RootState } from "@/store";
import { IconButton } from "@mui/material";
import { NextRouter, Router, useRouter } from "next/router";
import React, { useState } from "react";
import { AiOutlineEdit } from "react-icons/ai";
import { useDispatch, useSelector } from "react-redux";
import { Dispatch } from "redux";

interface IProp {
  agentName: string;
  agentId: string;
}

export const handleNewSession = async (body: {
  agentId: string;
  router: NextRouter;
  dispatch: Dispatch;
}) => {
  const { agentId, router, dispatch } = body;
  const response = await createNewSession(
    {
      agentId,
      orgId: localStorage.getItem("orgId") as string,
    },
    dispatch
  );

  console.log({ response });

  if (response?.status === 201) {
    const route = "/home/<USER>/" + response.data.data._id.toString();
    console.log("Should route to ", route);

    await router.push(route); // Add await here

    console.log(route);
  }
};

const SingleAgent: React.FC<IProp> = ({ agentName, agentId }) => {
  const dispatch = useDispatch();
  const router = useRouter();
  // const userId = useSelector((state: RootState) => state.profile.userId);
  const [loading, setLoading] = useState(false);
  const parentUrl = router.asPath;
  const handleSelect = async () => {
    try {
      setLoading(true);
      await handleNewSession({ agentId, router, dispatch });
    } catch (error: any) {
      dispatch(
        snackbarActions.setSnackbar({
          message:
            error.response?.data?.message ||
            "An unexpected error occurred. Please try again.",
          type: "error",
        })
      );
    } finally {
      setLoading(false);
    }
  };

  const handleAgentEdit = (event: React.MouseEvent<HTMLButtonElement>) => {
    event.stopPropagation();
    setLoading(true);
    router.push("/home/<USER>/" + agentId);
  };
  return (
    <div
      className="flex items-center justify-between rounded-lg shadow-md bg-white text-black h-[55px] px-3 py-2 cursor-pointer z-[1]"
      onClick={handleSelect}
    >
      {loading ? (
        <div className="w-full flex justify-center">
          <ButtonSpinner />
        </div>
      ) : (
        <>
          <div className="font-semibold text-[16px]">
            {agentName || "No name"}
          </div>
          <div className="buttons flex z-[2]">
            <IconButton
              color="primary"
              aria-label="add to shopping cart"
              onClick={handleAgentEdit}
            >
              <AiOutlineEdit />
            </IconButton>
          </div>
        </>
      )}
    </div>
  );
};

export default SingleAgent;
