import React, { <PERSON>Event, useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  DialogTitle,
  DialogContent,
  TextField,
  DialogActions,
  Button,
  CircularProgress,
  Autocomplete,
} from "@mui/material";
import DownloadIcon from "@mui/icons-material/Download";
import { importGhlConvo } from "@/requests/sessions/basic/importGhlConvo";
import { useDispatch, useSelector } from "react-redux";
import { IAgent } from "@/slices/agents/agentsSlice";
import { emulatorActions } from "@/slices/emulator/emulatorSlice";
import { ISession } from "./RecentSessions/SessionLists";
import { getReadyAgents } from "@/requests/agents/basic/getAgent";
import { snackbarActions } from "@/slices/general/snackbar";
import { getChannelAccounts } from "@/requests/channels/getChannelAccounts";
import { IAccount } from "@/slices/settings/channels/channelsList";
import { RootState } from "@/store";
import { useRouter } from "next/router";
import { ImSpinner8 } from "react-icons/im";

const ImportSession = () => {
  const dispatch = useDispatch();
  const router = useRouter();
  const channelList = useSelector(
    (state: RootState) => state.channelsList.channelList
  );
  const [open, setOpen] = useState(false);
  const [agents, setAgents] = useState<IAgent[]>([]);
  const [agentLoading, setAgentLoading] = useState(false);
  const [channelsLoading, setChannelsLoading] = useState(false);
  const [selectedChannel, setSelectedChannel] = useState<{
    id: string;
    name: string;
    value: string;
    canSessionImport?: boolean;
  } | null>(null);
  const [providerList, setProviderList] = useState<string[]>([]);
  const [selectedAgent, setSelectedAgent] = useState<IAgent | null>(null);
  const [locationIdList, setLocationIdList] = useState<
    { name: string; locationId: string }[]
  >([]);
  const [selectedLocation, setSelectedLocation] = useState<{
    name: string;
    locationId: string;
  } | null>(null);
  const [conversationId, setConversationId] = useState("");
  const [loading, setLoading] = useState(false);
  const orgId = localStorage.getItem("orgId") as string;

  const handleClickOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  useEffect(() => {
    const fetchLocationIdList = async () => {
      setChannelsLoading(true);
      const response = await getChannelAccounts(
        { orgId, provider: selectedChannel?.value },
        dispatch
      );
      setChannelsLoading(false);
      if (response?.status === 200) {
        const locationsList = response.data.channels.map(
          (channel: IAccount) => ({
            name: channel.name,
            locationId: channel.keyId,
          })
        );
        setLocationIdList(locationsList);
      }
    };
    fetchLocationIdList();
  }, [dispatch, orgId, selectedChannel]);

  useEffect(() => {
    setAgentLoading(true);
    const response = getReadyAgents({ orgId }, dispatch);
    response.then((res) => {
      setAgents(res?.data.data);
      setAgentLoading(false);
    });
  }, [dispatch, orgId]);

  const handleImport = async (event: FormEvent) => {
    event.preventDefault();
    if (!selectedAgent) {
      dispatch(
        snackbarActions.setSnackbar({
          message: "Please select an agent",
          type: "error",
        })
      );
    }
    setLoading(true);
    const response = await importGhlConvo(
      {
        conversationId,
        locationId: selectedLocation?.locationId as string,
        agentId: selectedAgent?._id as string,
        orgId,
      },
      dispatch
    );
    setLoading(false);
    if (response?.status === 201) {
      const session: ISession = response.data;
      const sessionId = session._id;
      dispatch(emulatorActions.addSingleSession(session));
      handleClose();
      router.push("/home/<USER>/" + sessionId);
    }
  };

  return (
    <>
      <Button
        variant="contained"
        startIcon={<DownloadIcon />}
        onClick={handleClickOpen}
        size="medium"
      >
        Import
      </Button>
      <Dialog open={open} onClose={handleClose}>
        <DialogTitle>Import Session</DialogTitle>
        <DialogContent sx={{ width: "500px" }}>
          <Autocomplete
            sx={{ mt: 1 }}
            options={agents}
            getOptionLabel={(option) => option?.agentName || "No name"}
            renderInput={(params) => (
              <TextField
                {...params}
                label="Agent"
                variant="outlined"
                required
                InputProps={{
                  ...params.InputProps,
                  endAdornment: (
                    <>
                      {agentLoading ? (
                        <CircularProgress color="inherit" size={20} />
                      ) : null}
                      {params.InputProps.endAdornment}
                    </>
                  ),
                }}
              />
            )}
            value={selectedAgent}
            onChange={(event, newValue) => setSelectedAgent(newValue)}
          />

          <Autocomplete
            sx={{ mt: 1 }}
            options={channelList}
            getOptionDisabled={(option) => option.canSessionImport === false}
            getOptionLabel={(option) => option?.name || "No name"}
            renderInput={(params) => (
              <TextField
                {...params}
                label="Channel"
                variant="outlined"
                required
                InputProps={{
                  ...params.InputProps,
                  endAdornment: (
                    <>
                      {false ? (
                        <CircularProgress color="inherit" size={20} />
                      ) : null}
                      {params.InputProps.endAdornment}
                    </>
                  ),
                }}
              />
            )}
            value={selectedChannel}
            onChange={(event, newValue) => {
              setLocationIdList([]);
              setSelectedChannel(newValue);
              setSelectedLocation(null);
            }}
          />

          <Autocomplete
            sx={{ mt: 1 }}
            options={locationIdList}
            getOptionLabel={(option) => option?.name || "No name"}
            renderInput={(params) => (
              <TextField
                {...params}
                label="Location"
                variant="outlined"
                required
                InputProps={{
                  ...params.InputProps,
                  endAdornment: (
                    <>
                      {channelsLoading ? (
                        <CircularProgress color="inherit" size={20} />
                      ) : null}
                      {params.InputProps.endAdornment}
                    </>
                  ),
                }}
              />
            )}
            value={selectedLocation}
            onChange={(event, newValue) => setSelectedLocation(newValue)}
          />

          <TextField
            margin="dense"
            id="conversationId"
            label="Conversation ID"
            type="text"
            fullWidth
            value={conversationId}
            onChange={(e) => setConversationId(e.target.value)}
            required
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClose} color="primary">
            Cancel
          </Button>
          <Button onClick={handleImport} color="primary" disabled={loading}>
            Import {loading && <ImSpinner8 className="animate-spin ml-2" />}
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default ImportSession;
