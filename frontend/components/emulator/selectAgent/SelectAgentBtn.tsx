import GeneralModal from "@/components/general/modal";
import { Button } from "@mui/material";
import React, { useState } from "react";
import SelectAgent from "./SelectAgent";

const SelectAgentBtn = () => {
  const [openModal, setOpenModal] = useState(false);
  const handleCloseModal = () => {
    setOpenModal(false);
  };
  return (
    <>
      <GeneralModal open={openModal} onClose={handleCloseModal}>
        <SelectAgent />
      </GeneralModal>
      <Button
        variant="contained"
        onClick={() => {
          setOpenModal(true);
        }}
      >
        New session
      </Button>
    </>
  );
};

export default SelectAgentBtn;
