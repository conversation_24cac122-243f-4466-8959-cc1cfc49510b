import { Typography } from "@mui/material";
import React, { useEffect, useState } from "react";
import FilterButton from "./FilterButton";
import { useRouter } from "next/router";
import { getOrgSessions } from "@/requests/sessions/basic/getOrgSessions";
import { getOrgAgents } from "@/requests/agents/basic/getAgent";
import { useDispatch } from "react-redux";

const RecentSessionHeader = () => {
  const dispatch = useDispatch();
  const [agentList, setAgentList] = useState<{ name: string; value: string }[]>(
    []
  );
  useEffect(() => {
    const response = getOrgAgents(
      {
        orgId: localStorage.getItem("orgId") || "",
      },
      dispatch
    );

    response.then((resData) => {
      const resAgentList = resData?.data.data;
      console.log({ resAgentList });

      const newAgentList = resAgentList?.map((item: any) => ({
        name: item.agentName || "No name",
        value: item._id,
      }));
      setAgentList(newAgentList);
    });
  }, []);
  useEffect(() => {
    console.log({ agentList });
  }, [agentList]);
  return (
    <div className="flex items-center justify-between mb-4">
      <div className="name w-3/12 pl-4">
        <Typography variant="h6">Session name</Typography>
      </div>
      <div className="options w-9/12 flex gap-5">
        <FilterButton
          optionId="saved"
          optionName="All"
          optionList={[
            { name: "All", value: "all" },
            { name: "Saved", value: "true" },
            { name: "Not saved", value: "false" },
          ]}
        />
        <FilterButton
          optionId="createdBy"
          optionName="Created by anyone"
          optionList={[
            { name: "Created by anyone", value: "createdByAnyone" },
            { name: "Created by me", value: "createdByMe" },
            { name: "Created by others", value: "createdByOthers" },
          ]}
        />
        <FilterButton
          optionId="sortBy"
          optionName="Last used"
          optionList={[
            { name: "Last used", value: "lastUsed" },
            { name: "Last created", value: "lastCreated" },
          ]}
        />
        <FilterButton
          optionId="agent"
          optionName="All agents"
          optionList={[{ name: "All agents", value: "all" }].concat(agentList)}
        />
      </div>
    </div>
  );
};

export default RecentSessionHeader;
