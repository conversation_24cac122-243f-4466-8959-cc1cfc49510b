import { Button, Typography } from "@mui/material";
import React, { useEffect, useState } from "react";
import { AiFillCaretDown } from "react-icons/ai";
import OptionsModal from "./OptionsModal";
import { useRouter } from "next/router";

interface IProp {
  optionId: string;
  optionName: string;
  optionList: {
    name: string;
    value: string;
  }[];
}

const FilterButton: React.FC<IProp> = ({
  optionId,
  optionName,
  optionList,
}) => {
  console.log({ optionId, optionName, optionList });
  const router = useRouter();
  const { createdBy, sortBy, agent, saved } = router.query;
  const [open, setOpen] = useState(false);
  const handleClose = () => {
    setOpen(false);
  };
  useEffect(() => {
    console.log({ createdBy });
  }, [createdBy]);
  return (
    <div
      className={`ownership ${
        optionId === "saved" ? "w-2/12" : "w-4/12"
      } relative`}
    >
      <Button
        variant="text"
        color="inherit"
        endIcon={<AiFillCaretDown />}
        className="w-full"
        onClick={() => {
          setOpen(!open);
        }}
      >
        <Typography variant="inherit" style={{ textTransform: "none" }}>
          {optionId === "saved" &&
            (optionList.find((item) => item.value === saved)?.name ||
              optionName)}
          {optionId === "createdBy" &&
            optionList.find((item) => item.value === createdBy)?.name}
          {optionId === "sortBy" &&
            optionList.find((item) => item.value === sortBy)?.name}
          {optionId === "agent" &&
            optionList.find((item) => item.value === agent)?.name}
        </Typography>
      </Button>
      <OptionsModal
        optionsFor={optionId}
        optionList={optionList}
        open={open}
        handleCloseFn={handleClose}
      />
    </div>
  );
};

export default FilterButton;
