import { Chip, Typography } from "@mui/material";
import React from "react";
import { ISession } from "./SessionLists";
import { useRouter } from "next/router";
import Link from "next/link";

const SingleSessionInList: React.FC<{
  details: ISession;
}> = ({ details }) => {
  const router = useRouter();
  const { sortBy } = router.query;
  const { _id, active, basic, dates, saved } = details;
  console.log("UID ", basic, dates);

  return (
    <div className="">
      {
        <Link href={"/home/<USER>/" + _id}>
          <div className="flex items-center justify-between py-5 px-3 hover:bg-gray-600 cursor-pointer rounded-2xl">
            <div className="name w-3/12 flex justify-between">
              <Typography variant="subtitle1">{basic?.sessionName}</Typography>
            </div>
            <div className="options w-9/12 flex gap-5">
              <div className="ownership w-2/12 text-center">
                {saved && (
                  <Chip variant="filled" color="success" label="Saved" />
                )}
              </div>
              <div className="ownership w-4/12 text-center">
                <Typography variant="subtitle1">{basic?.userName}</Typography>
              </div>
              <div className="dateFilter w-4/12 text-center">
                <Typography variant="subtitle1">
                  {sortBy === "lastUsed"
                    ? new Date(dates.lastUpdated * 1000).toLocaleString()
                    : new Date(dates.createdAt * 1000).toLocaleString()}
                </Typography>
              </div>
              <div className="statusType w-4/12 text-center">
                <Typography variant="subtitle1">
                  {basic.agentName || "No name - " + basic.agentId}
                </Typography>
              </div>
            </div>
          </div>
        </Link>
      }
      <div className="px-3">
        <hr />
      </div>
    </div>
  );
};

export default SingleSessionInList;
