import { Button, Skeleton, Typography } from "@mui/material";
import React, { useEffect, useState } from "react";
import SingleSessionInList from "./SingleSessionInList";
import { useRouter } from "next/router";
import {
  IFetchSessionsProp,
  getOrgSessions,
} from "@/requests/sessions/basic/getOrgSessions";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store";
import { AiOutlineReload } from "react-icons/ai";
import { organizationAction } from "@/slices/organization/organizationSlice";
import { emulatorActions } from "@/slices/emulator/emulatorSlice";
import { ImSpinner8 } from "react-icons/im";

export interface ISession {
  _id: string;
  active: boolean;
  saved: boolean;
  basic: {
    orgId: string;
    userId: string;
    agentId: string;
    sessionName: string;
    userName?: string;
    agentName?: string;
  };
  dates: { createdAt: number; lastUpdated: number };
}

interface IProp {
  handleNoSession: () => void;
}

const SessionLists: React.FC<IProp> = ({ handleNoSession }) => {
  const dispatch = useDispatch();
  const router = useRouter();
  const currentOrgId = useSelector(
    (state: RootState) => state.organization.currentOrganizationId
  );
  const currentOrgData = useSelector((state: RootState) => state.organization);
  const { view, sortBy, createdBy, saved, agent } = router.query;
  const [loading, setLoading] = useState(false);
  const [moreLoading, setMoreLoading] = useState(false);
  // const [sessionList, setSessionList] = useState<ISession[]>([]);
  const sessionList = useSelector(
    (state: RootState) => state.emulator.sessionList
  );
  const [offset, setOffset] = useState(0);
  const limit = 10;
  const [initialLoad, setInitialLoad] = useState(true);
  const loadMoreSessionClicked = () => {
    setOffset((offset) => offset + limit);
  };
  useEffect(() => {
    router.push({
      pathname: "/home/<USER>",
      query: {
        view,
        sortBy,
        createdBy,
        agent,
        saved,
      },
    });
  }, [currentOrgId]);
  useEffect(() => {
    const { createdBy, sortBy, agent, saved } = router.query as {
      createdBy: "createdByMe" | "createdByOthers" | "createdByAnyone";
      sortBy: "lastUsed" | "lastCreated";
      agent: string;
      saved: "all";
    };
    if (!createdBy || !sortBy || !agent || !saved) return;
    const orgId = localStorage.getItem("orgId") as string;
    const payload: IFetchSessionsProp = {
      createdBy,
      agent,
      sortBy,
      orgId,
      saved,
      limit,
      offset,
    };
    console.log({
      payload,
    });

    setLoading(true);
    const response = getOrgSessions(payload, dispatch);
    response
      .then((resData) => {
        const resDataArr: ISession[] = resData?.data.data;
        //This is the initial load. Dont concat. Directly push
        // setSessionList(resDataArr);
        dispatch(emulatorActions.updateSessionList(resDataArr));
        console.log("res data ", resData?.data.data);
      })
      .catch((err) => console.log(err))
      .finally(() => {
        setLoading(false);
        setInitialLoad(false);
      });
  }, [router.query, currentOrgData.currentOrganizationId]);
  useEffect(() => {
    const { createdBy, sortBy, agent, saved } = router.query as {
      createdBy: "createdByMe" | "createdByOthers" | "createdByAnyone";
      sortBy: "lastUsed" | "lastCreated";
      agent: string;
      saved: "all";
    };
    if (!createdBy || !sortBy || !agent || !saved) return;
    const orgId = localStorage.getItem("orgId") as string;
    const payload = {
      createdBy,
      agent,
      sortBy,
      orgId,
      saved,
      limit,
      offset,
    };
    console.log({
      payload,
    });

    setMoreLoading(true);
    const response = getOrgSessions(payload, dispatch);
    response
      .then((resData) => {
        const resDataArr: ISession[] = resData?.data.data;
        //This is on the load more click. Concat this
        const newArr: ISession[] = sessionList.concat(resDataArr);
        // setSessionList(newArr);
        dispatch(emulatorActions.updateSessionList(newArr));
        console.log({ newArr });

        if (newArr.length === 0) {
          handleNoSession();
        }
      })
      .catch((err) => console.log(err))
      .finally(() => {
        setMoreLoading(false);
      });
  }, [offset]);
  useEffect(() => {
    console.log({ sessionList });
  }, [sessionList]);
  if (loading) {
    return (
      <div className="flex justify-center items-center">
        <ImSpinner8 className="animate-spin" size={38} />
      </div>
    );
  }
  return (
    <div className="flex flex-col gap-10">
      <div className="flex flex-col">
        {sessionList?.map((item) => (
          <SingleSessionInList details={item} key={"Session_Session_" + item} />
        ))}
        {moreLoading &&
          Array(6)
            .fill([0])
            .map((_, index) => (
              <Skeleton
                variant="rectangular"
                sx={{
                  width: "full",
                  height: "50px",
                  marginBottom: "10px",
                  bgcolor: "grey.700",
                  borderRadius: "10px",
                }}
                key={"SkeletonKey_AgentCard_" + index}
              />
            ))}
        {!moreLoading && (
          <Button
            startIcon={<AiOutlineReload />}
            variant="contained"
            sx={{ width: "fit-content", my: 2 }}
            onClick={loadMoreSessionClicked}
          >
            Load more
          </Button>
        )}
      </div>
    </div>
  );
};

export default SessionLists;
