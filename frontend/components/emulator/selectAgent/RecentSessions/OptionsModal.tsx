import { useRouter } from "next/router";
import React from "react";

interface IProp {
  optionsFor: string;
  optionList: {
    name: string;
    value: string;
  }[];
  open: boolean;
  handleCloseFn: () => void;
}

const OptionsModal: React.FC<IProp> = ({
  optionsFor,
  optionList,
  open,
  handleCloseFn,
}) => {
  const router = useRouter();
  const handleSelect = (event: React.MouseEvent<HTMLDivElement>) => {
    const selectedValue = event.currentTarget.getAttribute("data-value");
    router.push({
      pathname: "/home/<USER>",
      query: {
        ...router.query,
        [optionsFor]: selectedValue,
      },
    });
    handleCloseFn();
  };
  return (
    <>
      {open && (
        <>
          <div className="absolute top-[40px] w-full z-50">
            <div className="bg-white border border-gray-300 max-h-[500px] overflow-y-auto">
              {optionList.map((option, index) => (
                <React.Fragment
                  key={"Option_key_select" + optionsFor + option.value}
                >
                  <div
                    className="option text-black px-4 py-2 cursor-pointer hover:bg-gray-300"
                    data-value={option.value}
                    key={"Option_key_select" + optionsFor + option.value}
                    onClick={handleSelect}
                  >
                    {option.name}
                  </div>
                  {optionList.length - 1 !== index && (
                    <div
                      className="border-b border-black"
                      key={"Option_line" + optionsFor + option.value}
                    ></div>
                  )}
                </React.Fragment>
              ))}
            </div>
          </div>
          <div
            className="w-screen h-screen top-0 left-0 fixed bg-transparent z-40"
            onClick={handleCloseFn}
          ></div>
        </>
      )}
    </>
  );
};

export default OptionsModal;
