import React, { useState } from "react";
import RecentSessionHeader from "./RecentSessionHeader";
import SessionLists from "./SessionLists";

interface IProp {
  handleNoSession: () => void
}

const RecentSessionList: React.FC<IProp> = ({ handleNoSession }) => {
  return (
    <div className="text-fourth mt-5">
      <RecentSessionHeader />
      <SessionLists handleNoSession={handleNoSession} />
    </div>
  );
};

export default RecentSessionList;
