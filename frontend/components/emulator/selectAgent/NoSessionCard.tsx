
import { Box, Card, CardContent, Typography } from '@mui/material';
import Image from 'next/image';
import React from 'react';
import SelectAgentBtn from './SelectAgentBtn';

const NoSessionsCard: React.FC = () => {
    return (
        <Card sx={{ my: 2 }}>
            <CardContent>
                <Box
                    display={'flex'}
                    flexDirection='column'
                    justifyContent='center'
                    alignItems='center'
                    gap={2}
                >
                    <Box>
                        {/* Replace the img src with your actual illustration link */}
                        <Image src={'/images/emulator/bg-8.svg'} alt='Svg' width={300} height={300}></Image>
                    </Box>
                    <Typography variant="h6">
                        No emulator session found. Click <SelectAgentBtn /> to create a new session
                    </Typography>
                </Box>
            </CardContent>
        </Card>
    );
};

export default NoSessionsCard;
