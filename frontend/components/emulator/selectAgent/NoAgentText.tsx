import React from "react";
import SelectAgentBtn from "./SelectAgentBtn";
import { useSelector } from "react-redux";
import { RootState } from "@/store";
import { useRouter } from "next/router";

const NoAgentText = () => {
  const router = useRouter();
  const { view } = router.query;
  return (
    <div className="text-fourth text-[24px] flex justify-center mt-20">
      No {view} agents. <span className="mx-3">{<SelectAgentBtn />}</span> to
      start an emulator session
    </div>
  );
};

export default NoAgentText;
