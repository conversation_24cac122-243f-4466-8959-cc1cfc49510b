import Line from "@/components/general/shapes/horizontalLine";
import { RootState } from "@/store";
import { Button, IconButton, createTheme } from "@mui/material";
import React, { useEffect, useState } from "react";
import { AiOutlinePlusCircle } from "react-icons/ai";
import { BsFillTrashFill } from "react-icons/bs";
import { useDispatch, useSelector } from "react-redux";
import GoogleSheetAction from "./addAction/GoogleSheetAction";
import GoogleCalendarAction from "./addAction/GoogleCalendarAction";
import GhlCalendarAction from "./addAction/GhlCalendarAction";
import SelectActionType from "./SelectActionType";
import SelectAccount from "./SelectAccount";
import SelectProvider from "./SelectProvider";
import {
  CommonDataSource,
  GhlCalendarDataSource,
  GoogleCalendarDataSource,
  GoogleSheetDataSource,
  ISingleIntegration,
} from "@/slices/settings/integrations/integrationList";
import { getIntegrationAccounts } from "@/requests/integrations/getIntegrationAccounts";
import { useRouter } from "next/router";
import { CONSTANTS } from "@/helpers/constants";
import GhlChannelEmulatorInputs from "./addAction/GhlChannelEmulatorInputs";
import WebsiteAction from "./addAction/WebsiteAction";
import GenerateJsonAction from "./addAction/GenerateJsonAction";
import {
  IActionGhlChannel,
  IGenerateJsonAction,
  IHttpGetAction,
  ISingleAction,
} from "@/slices/agents/agentsSlice";
import GhlChannelCustomFieldEmulatorInputs from "./addAction/GhlChannelCustomFieldEmulatorInputs";
import GoogleDocsAction from "./addAction/GoogleDocsAction";
import HttpGetAction from "./addAction/HttpGetAction";
import SlackAction from "./addAction/SlackAction";

export const darkTheme = createTheme({
  components: {
    MuiMenu: {
      styleOverrides: {
        paper: {
          backgroundColor: "#393E46",
        },
      },
    },
    MuiMenuItem: {
      styleOverrides: {
        root: {
          color: "#ffffff",
        },
      },
    },
    MuiOutlinedInput: {
      styleOverrides: {
        root: {
          "&:hover .MuiOutlinedInput-notchedOutline": {
            borderColor: "#ffffff",
          },
          "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
            borderColor: "#ffffff",
          },
        },
        notchedOutline: {
          borderColor: "#ffffff",
        },
      },
    },
    MuiSelect: {
      styleOverrides: {
        icon: {
          color: "#ffffff",
        },
      },
    },
  },
});

export interface IValueProp {
  providerName: string;
  activity: string;
  accountId: string;
  accountName: string;
}

const AddNewAction = () => {
  const dispatch = useDispatch();
  const router = useRouter();
  const integrationProviders = useSelector(
    (state: RootState) => state.integrationList.integrationProvidersList
  );
  const emulatorResourceList = useSelector(
    (state: RootState) => state.emulator.resourceList
  );
  const responseDetails = useSelector(
    (state: RootState) => state.emulator.responseDetails
  );
  const { sessionId } = router.query as { sessionId: string };
  const [selectedAccount, setSelectedAccount] = useState<ISingleAction>();
  const [dataSources, setDataSources] = useState<ISingleIntegration[]>([]);
  const [selectedDataSource, setSelectedDataSource] =
    useState<ISingleIntegration>();
  const [add, setAdd] = useState(false);
  const [currentProvider, setCurrentProvider] = useState("");
  const [value, setValue] = useState<IValueProp>({
    providerName: "",
    activity: "",
    accountId: "",
    accountName: "",
  });
  useEffect(() => {
    const response = getIntegrationAccounts(
      {
        orgId: localStorage.getItem("orgId") || "",
      },
      dispatch
    );
    response.then((resData) => {
      if (resData?.status === 200) {
        console.log(
          "Setting the datasources to this ",
          resData.data.datasources
        );

        setDataSources(resData.data.datasources);
      }
    });
  }, []);

  const toggleAdd = () => {
    if (add === true) {
      setValue({
        providerName: "",
        activity: "",
        accountId: "",
        accountName: "",
      });
    }
    setAdd(!add);
  };
  useEffect(() => {
    console.log("Provider name changed ", { value });
  }, [value.providerName]);
  useEffect(() => {
    setValue({ ...value, accountId: "", accountName: "" });
  }, [value.providerName, value.activity]);
  useEffect(() => {
    setSelectedDataSource(
      dataSources?.find((item) => item.accountId === value.accountId)
    );
  }, [value.accountId]);
  // useEffect(() => {
  //   console.log({ dataSources, selectedDataSource });
  // }, [dataSources, selectedDataSource]);
  return (
    <div>
      {!add && (
        <div className="">
          <Button
            variant="contained"
            className="bg-blue-500"
            endIcon={<AiOutlinePlusCircle />}
            onClick={toggleAdd}
          >
            Add
          </Button>
        </div>
      )}
      {add && (
        <div className="">
          <div className="flex flex-col gap-5">
            <div className="flex">
              <div className="w-11/12 font-semibold text-xl flex items-center">
                Add new action
              </div>
              <div className="w-1/12 text-right">
                <IconButton aria-label="delete" color="error" size="large">
                  <BsFillTrashFill fontSize="inherit" onClick={toggleAdd} />
                </IconButton>
              </div>
            </div>
            <SelectProvider
              onChangeFn={(providerName) =>
                setValue({ ...value, providerName })
              }
            />
            <SelectActionType
              value={value}
              onChangeFn={(activity) => setValue({ ...value, activity })}
            />
            <SelectAccount
              accountNameChange={(accountName) =>
                setValue({ ...value, accountName: accountName })
              }
              accountIdChange={(accountId) =>
                setValue({ ...value, accountId: accountId })
              }
              value={value}
              accountSelectedFn={(account: ISingleAction) =>
                setSelectedAccount(account)
              }
            />
          </div>

          {value.accountId !== "" && (
            <div className="bg-fourth w-11/12 mt-5  rounded-sm  text-gray-700">
              <div className="header font-semibold px-4 py-3">
                {
                  emulatorResourceList.find(
                    (item) => item.providerId === value.providerName
                  )?.name
                }
              </div>
              <Line />
              <div className="px-4 py-3">
                {value.providerName === "googleSheet" && (
                  <GoogleSheetAction
                    dataSource={selectedDataSource as GoogleSheetDataSource}
                    eventId={responseDetails.eventId as string}
                    sessionId={sessionId}
                    value={value}
                    toggleNewAction={toggleAdd}
                  />
                )}
                {value.providerName === "website" && (
                  <WebsiteAction
                    dataSource={selectedDataSource as CommonDataSource}
                    eventId={responseDetails.eventId as string}
                    sessionId={sessionId}
                    value={value}
                    toggleNewAction={toggleAdd}
                  />
                )}
                {value.providerName === CONSTANTS.PROVIDERS.GOOGLE_DOCS && (
                  <GoogleDocsAction
                    dataSource={selectedDataSource as CommonDataSource}
                    eventId={responseDetails.eventId as string}
                    sessionId={sessionId}
                    value={value}
                    toggleNewAction={toggleAdd}
                  />
                )}
                {value.providerName === CONSTANTS.PROVIDERS.SLACK && (
                  <SlackAction
                    dataSource={selectedDataSource as CommonDataSource}
                    eventId={responseDetails.eventId as string}
                    sessionId={sessionId}
                    value={value}
                    toggleNewAction={toggleAdd}
                  />
                )}
                {value.providerName === CONSTANTS.PROVIDERS.GOOGLE_CALENDAR && (
                  <GoogleCalendarAction
                    dataSource={selectedDataSource as GoogleCalendarDataSource}
                    eventId={responseDetails.eventId as string}
                    sessionId={sessionId}
                    value={value}
                    toggleNewAction={toggleAdd}
                  />
                )}
                {value.providerName === "ghlCalendar" && (
                  <GhlCalendarAction
                    dataSource={selectedDataSource as GhlCalendarDataSource}
                    eventId={responseDetails.eventId as string}
                    sessionId={sessionId}
                    value={value}
                    toggleNewAction={toggleAdd}
                  />
                )}
                {value.providerName === CONSTANTS.PROVIDERS.GHL_CHANNEL &&
                  value.activity === "customField" && (
                    <GhlChannelCustomFieldEmulatorInputs
                      eventId={responseDetails.eventId as string}
                      sessionId={sessionId}
                      toggleNewAction={toggleAdd}
                      selectedAccountDetails={
                        selectedAccount as IActionGhlChannel
                      }
                    />
                  )}
                {value.providerName === CONSTANTS.PROVIDERS.GHL_CHANNEL &&
                  value.activity === "tag" && (
                    <GhlChannelEmulatorInputs
                      eventId={responseDetails.eventId as string}
                      sessionId={sessionId}
                      value={value}
                      toggleNewAction={toggleAdd}
                    />
                  )}
                {value.providerName === CONSTANTS.PROVIDERS.GENERATE_JSON && (
                  <GenerateJsonAction
                    eventId={responseDetails.eventId as string}
                    sessionId={sessionId}
                    value={value}
                    toggleNewAction={toggleAdd}
                    selectedAccountDetails={
                      selectedAccount as IGenerateJsonAction
                    }
                  />
                )}
                {value.providerName === CONSTANTS.PROVIDERS.HTTP_GET && (
                  <HttpGetAction
                    eventId={responseDetails.eventId as string}
                    sessionId={sessionId}
                    value={value}
                    toggleNewAction={toggleAdd}
                    selectedAccountDetails={selectedAccount as IHttpGetAction}
                  />
                )}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default AddNewAction;
