import ButtonSpinner from "@/components/general/buttons/spinner";
import { editBotMessage } from "@/requests/sessions/editResponse/editBotMessage";
import {
  IBotMessage,
  IBotResponse,
  emulatorActions,
} from "@/slices/emulator/emulatorSlice";
import { RootState } from "@/store";
import { Button, IconButton, TextField } from "@mui/material";
import { useRouter } from "next/router";
import React, { useEffect, useState } from "react";
import { AiFillEdit } from "react-icons/ai";
import { useDispatch, useSelector } from "react-redux";

const MessageDetail1 = () => {
  const dispatch = useDispatch();
  const router = useRouter();
  const { sessionId } = router.query;
  const responseDetails = useSelector(
    (state: RootState) => state.emulator.responseDetails
  );
  const [loading, setLoading] = useState<boolean>(false);
  const [message, setMessage] = useState<string>("");
  const [edit, setEdit] = useState<boolean>(false);

  const messageObject = responseDetails.botResponse?.find(
    (botEvent) => botEvent.kind === "message"
  ) as IBotResponse;
  const eventId = messageObject.eventId;

  useEffect(() => {
    setMessage((messageObject.eventData as IBotMessage).message);
  }, [messageObject]);

  const handleSave = async () => {
    setLoading(true);
    console.log({ sessionId, eventId, message });
    const response = await editBotMessage(
      {
        sessionId: sessionId as string,
        eventId,
        message,
      },
      dispatch
    );
    if (response?.status === 200) {
      dispatch(
        emulatorActions.botResponseUpdated({
          parentEventId: responseDetails.eventId as string,
          response: { ...messageObject, eventData: { message } },
        })
      );
    }
    setLoading(false);
    setEdit(false);
  };
  const messageWithLineBreaks = message.split('\n').map((line, index) => (
    <React.Fragment key={index}>
      {line}
      <br />
    </React.Fragment>
  ));
  return (
    <div className="mt-3 mb-10 text-[18px]">
      <div className="font-semibold mb-2">Last message: </div>
      <div className="flex">
        {!edit && (
          <div className="w-11/12 max-h-[100px] line-clamp-3">{messageWithLineBreaks}</div>
        )}
        {edit && (
         <TextField
         value={message}
         onChange={(e) => {
           setMessage(e.target.value);
         }}
         sx={{ bgcolor: "white" }}
         multiline
         minRows={4}
         maxRows={8}
         fullWidth
         variant="outlined"
         InputProps={{
           inputProps: { style: { resize: "vertical" } },
         }}
       />
        )}
        {!edit && (
          <div className="w-1/12 flex items-start">
            <IconButton aria-label="delete">
              <AiFillEdit
                className="text-fourth"
                onClick={() => setEdit(true)}
              />
            </IconButton>
          </div>
        )}
      </div>
      {edit && (
        <div className="text-right mt-3">
          <Button
            variant="text"
            onClick={() => {
              setMessage((messageObject.eventData as IBotMessage).message);
              setEdit(false);
            }}
          >
            Cancel
          </Button>
          <Button
            variant="contained"
            onClick={handleSave}
            className="bg-blue-500"
            disabled={loading ? true : false}
          >
            Save
            {loading && <ButtonSpinner />}
          </Button>
        </div>
      )}
    </div>
  );
};

export default MessageDetail1;
