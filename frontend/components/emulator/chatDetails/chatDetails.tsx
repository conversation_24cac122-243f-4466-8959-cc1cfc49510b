import React, { useEffect } from "react";
import DetailsHeader from "./detailsHeader";
import MessageDetail1 from "./messageDetail1";
import MessageDetail2 from "./messageDetail2";
import { useSelector } from "react-redux";
import { RootState } from "@/store";
import AddNewAction from "./AddNewAction";

const ChatDetails = () => {
  const isDetailsOpen = useSelector(
    (state: RootState) => state.emulator.detailsTabOpened
  );
  const integrationList = useSelector(
    (state: RootState) => state.integrationList.integrationAccountsList
  );
  return (
    <>
      {isDetailsOpen && (
        <div
          className={`px-4 left-border h-[calc(100vh-60px)] overflow-y-auto`}
        >
          <div className="pb-5">
            <DetailsHeader />
            <MessageDetail1 />
            <MessageDetail2 />
            <AddNewAction />
          </div>
        </div>
      )}
    </>
  );
};

export default ChatDetails;
