import React, { useEffect } from "react";
import KeyValue, { jsonGenerateDummy } from "./keyValue";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store";
import { IEvent, emulatorActions } from "@/slices/emulator/emulatorSlice";

const MessageDetail2 = () => {
  const dispatch = useDispatch();
  const responseDetails = useSelector(
    (state: RootState) => state.emulator.responseDetails
  );
  const sessionDetails = useSelector(
    (state: RootState) => state.emulator.session
  );
  useEffect(() => {
    dispatch(
      emulatorActions.getResponseDetails({
        eventId: responseDetails.eventId as string,
      })
    );
  }, [sessionDetails, dispatch]);
  return (
    <div className="my-3">
      {responseDetails.botResponse?.map((botEvent) => (
        <KeyValue
          details={botEvent}
          // details={jsonGenerateDummy}
          parentEventId={responseDetails.eventId as string}
          key={"Key-value-" + botEvent.eventId}
        />
      ))}
    </div>
  );
};

export default MessageDetail2;
