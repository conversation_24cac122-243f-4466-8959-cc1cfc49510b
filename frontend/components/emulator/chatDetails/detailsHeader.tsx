import { emulatorActions } from "@/slices/emulator/emulatorSlice";
import { IconButton } from "@mui/material";
import React from "react";
import { RxCross1 } from "react-icons/rx";
import { useDispatch } from "react-redux";

const DetailsHeader = () => {
  const dispatch = useDispatch();
  return (
    <>
      <div className=" text-heading2 font-medium flex justify-between items-center">
        <div className="name">Details</div>
        {/* <div
          className="w-fit h-fit rounded-full cursor-pointer p-2 hover:bg-gray-500 transition-all"
          onClick={() => {
            dispatch(emulatorActions.toggleDetailsTabOpened(false));
          }}
        >
          <RxCross1 className="text-[24px]" />
        </div> */}
        <IconButton
          aria-label="delete"
          size="large"
          color="primary"
          onClick={() => {
            dispatch(emulatorActions.toggleDetailsTabOpened(false));
          }}
        >
          <RxCross1 fontSize="inherit" className="text-fourth" />
        </IconButton>
      </div>
      <div className="h-[1px] bg-fourth"></div>
    </>
  );
};

export default DetailsHeader;
