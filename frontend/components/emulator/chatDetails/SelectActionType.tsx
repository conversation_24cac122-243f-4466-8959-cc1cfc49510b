import { Theme<PERSON>rovider } from "@emotion/react";
import {
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  SelectChangeEvent,
} from "@mui/material";
import React from "react";
import { IValueProp, darkTheme } from "./AddNewAction";
import { useSelector } from "react-redux";
import { RootState } from "@/store";

interface IProp {
  value: IValueProp;
  onChangeFn: (providerName: string) => void;
}

const SelectActionType: React.FC<IProp> = ({ value, onChangeFn }) => {
  const integrationData = useSelector(
    (state: RootState) => state.integrationList.integrationProvidersList
  );
  const emulatorResourceList = useSelector(
    (state: RootState) => state.emulator.resourceList
  );
  const [actionType, setActionType] = React.useState("");

  const handleChange = (event: SelectChangeEvent) => {
    setActionType(event.target.value as string);
    onChangeFn(event.target.value as string);
  };
  return (
    <div>
      <div className="flex">
        <div className="w-11/12">
          <ThemeProvider theme={darkTheme}>
            <FormControl
              fullWidth
              variant="outlined"
              style={{ color: "#ffffff" }}
            >
              <InputLabel
                id="demo-simple-select-label"
                style={{ color: "#ffffff" }}
              >
                Action type
              </InputLabel>
              <Select
                labelId="demo-simple-select-label"
                id="demo-simple-select"
                value={actionType}
                label="Action type"
                onChange={handleChange}
                style={{ color: "#ffffff", backgroundColor: "#393E46" }}
              >
                {emulatorResourceList
                  .find((item) => item.providerId === value.providerName)
                  ?.activity.map((item) => (
                    <MenuItem key={item} value={item}>
                      {item === "customField"
                        ? "CUSTOM FIELD"
                        : item === "events"
                        ? "FETCH EVENTS"
                        : item.toUpperCase()}
                    </MenuItem>
                  ))}
              </Select>
            </FormControl>
          </ThemeProvider>
        </div>
      </div>
    </div>
  );
};

export default SelectActionType;
