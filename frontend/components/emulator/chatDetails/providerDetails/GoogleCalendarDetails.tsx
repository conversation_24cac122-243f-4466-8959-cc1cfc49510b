import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useRouter } from "next/router";
import {
  <PERSON><PERSON>,
  <PERSON>ton,
  IconButton,
  Tooltip,
  Typography,
  TextField,
  Chip,
  Accordion,
  AccordionSummary,
  AccordionDetails,
} from "@mui/material";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import {
  DesktopDateTimePicker,
  LocalizationProvider,
} from "@mui/x-date-pickers";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";
import { AiFillEdit } from "react-icons/ai";
import { BsFillTrashFill } from "react-icons/bs";
import { ImSpinner8 } from "react-icons/im";
import { MdRestore } from "react-icons/md";
import InfoOutlinedIcon from "@mui/icons-material/InfoOutlined";
import { editGoogleCalendarResponse } from "@/requests/sessions/editResponse/editGoogleCalendarResponse";
import {
  IBotResponse,
  IGoogleCalendar,
  emulatorActions,
} from "@/slices/emulator/emulatorSlice";
import { RootState } from "@/store";

dayjs.extend(utc);
dayjs.extend(timezone);

interface IGoogleCalendarDetails {
  parentEventId: string;
  details: IBotResponse;
  handleOpen: () => void;
}

const formatDate = (date: Date, timezone: string) => {
  return dayjs(date).tz(timezone).format("M/D/YYYY h:mm:ss A");
};

const GoogleCalendarDetails: React.FC<IGoogleCalendarDetails> = ({
  parentEventId,
  details,
  handleOpen,
}) => {
  const dispatch = useDispatch();
  const router = useRouter();
  const timezones = useSelector(
    (state: RootState) => state.organization.timezones
  );
  const timezonesLoading = useSelector(
    (state: RootState) => state.organization.timezonesLoading
  );

  const { sessionId } = router.query;
  const [loading, setLoading] = useState<boolean>(false);
  const calendarEvents =
    (details.eventData as IGoogleCalendar)?.calendarEvents || [];
  const [currentTimezone, setCurrentTimezone] = useState<string>();
  const [timezoneError, setTimezoneError] = useState(false);
  const [editingEventIndex, setEditingEventIndex] = useState<number | null>(
    null
  );
  const [editedEvents, setEditedEvents] =
    useState<IGoogleCalendar["calendarEvents"]>(calendarEvents);
  const [expandedAccordion, setExpandedAccordion] = useState<number | false>(
    false
  );

  const userTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

  useEffect(() => {
    const accountId = details.accountId as string;
    const timezone = timezones.find(
      (timezone) => timezone.accountId === accountId
    );
    if (!timezone) {
      setTimezoneError(true);
    }
    setCurrentTimezone(timezone?.timezone ?? userTimezone);
  }, [timezones, details.accountId, userTimezone]);

  const handleAccordionChange =
    (panel: number) => (event: React.SyntheticEvent, isExpanded: boolean) => {
      setExpandedAccordion(isExpanded ? panel : false);
    };

  const handleEdit = (index: number) => {
    setEditingEventIndex(index);
    setExpandedAccordion(index);
  };

  const handleCancel = () => {
    setEditingEventIndex(null);
    setEditedEvents(calendarEvents);
  };

  const handleSave = async () => {
    if (editingEventIndex === null) return;

    setLoading(true);
    const payload = {
      sessionId: sessionId as string,
      eventId: details.eventId,
      action: "events",
      accountId: details.accountId as string,
      accountName: details.accountName as string,
      deleted: details?.deleted || false,
      calendarEvents: editedEvents,
    };

    try {
      const response = await editGoogleCalendarResponse(payload, dispatch);
      if (response?.status === 200) {
        setEditingEventIndex(null);
        dispatch(
          emulatorActions.botResponseUpdated({
            parentEventId,
            response: {
              ...details,
              action: payload.action,
              eventData: { calendarEvents: payload.calendarEvents },
            },
          })
        );
      }
    } catch (error) {
      console.error("Error updating Google Calendar event:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleEventChange = (
    index: number,
    field: keyof IGoogleCalendar["calendarEvents"][0],
    value: any
  ) => {
    setEditedEvents((prevEvents) =>
      prevEvents.map((event, i) =>
        i === index ? { ...event, [field]: value } : event
      )
    );
  };

  const handleAddAttendee = (index: number, email: string) => {
    if (
      email &&
      !editedEvents[index].attendees.some((a) => a.email === email)
    ) {
      setEditedEvents((prevEvents) =>
        prevEvents.map((event, i) =>
          i === index
            ? { ...event, attendees: [...event.attendees, { email }] }
            : event
        )
      );
    }
  };

  const handleRemoveAttendee = (index: number, email: string) => {
    setEditedEvents((prevEvents) =>
      prevEvents.map((event, i) =>
        i === index
          ? {
              ...event,
              attendees: event.attendees.filter((a) => a.email !== email),
            }
          : event
      )
    );
  };

  if (timezonesLoading)
    return (
      <div className="flex justify-center">
        <ImSpinner8 className="animate-spin text-[24px]" />
      </div>
    );

  return (
    <div className="space-y-4">
      {calendarEvents.map((event, index) => (
        <Accordion
          key={index}
          expanded={expandedAccordion === index}
          onChange={handleAccordionChange(index)}
        >
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography>{event.summary}</Typography>
          </AccordionSummary>
          <AccordionDetails>
            <div className="space-y-4">
              {editingEventIndex === index ? (
                <>
                  <TextField
                    fullWidth
                    label="Summary"
                    value={editedEvents[index].summary}
                    onChange={(e) =>
                      handleEventChange(index, "summary", e.target.value)
                    }
                  />
                  <TextField
                    fullWidth
                    label="Description"
                    multiline
                    rows={3}
                    value={editedEvents[index].description}
                    onChange={(e) =>
                      handleEventChange(index, "description", e.target.value)
                    }
                  />
                  <LocalizationProvider dateAdapter={AdapterDayjs}>
                    <DesktopDateTimePicker
                      label="Start Time"
                      value={dayjs(editedEvents[index].startDate)}
                      onChange={(newValue) =>
                        handleEventChange(
                          index,
                          "startDate",
                          newValue?.toISOString()
                        )
                      }
                      timezone={currentTimezone}
                      sx={{ width: "100%" }}
                    />
                    <DesktopDateTimePicker
                      label="End Time"
                      value={dayjs(editedEvents[index].endDate)}
                      onChange={(newValue) =>
                        handleEventChange(
                          index,
                          "endDate",
                          newValue?.toISOString()
                        )
                      }
                      timezone={currentTimezone}
                      sx={{ width: "100%" }}
                    />
                  </LocalizationProvider>
                  <div className="space-y-2">
                    <TextField
                      label="Add Attendee"
                      onKeyPress={(e) =>
                        e.key === "Enter" &&
                        handleAddAttendee(
                          index,
                          (e.target as HTMLInputElement).value
                        )
                      }
                    />
                    <div className="flex flex-wrap gap-2">
                      {editedEvents[index].attendees.map((attendee) => (
                        <Chip
                          key={attendee.email}
                          label={attendee.email}
                          onDelete={() =>
                            handleRemoveAttendee(index, attendee.email)
                          }
                        />
                      ))}
                    </div>
                  </div>
                </>
              ) : (
                <>
                  <div className="flex">
                    <div className="w-4/12">Summary</div>
                    <div className="w-8/12 italic">{event.summary}</div>
                  </div>
                  <div className="flex">
                    <div className="w-4/12">Description</div>
                    <div className="w-8/12 italic">{event.description}</div>
                  </div>
                  <div className="flex">
                    <div className="w-4/12">Start Time</div>
                    <div className="w-8/12 italic">
                      {formatDate(
                        new Date(event.startDate),
                        currentTimezone as string
                      )}
                    </div>
                  </div>
                  <div className="flex">
                    <div className="w-4/12">End Time</div>
                    <div className="w-8/12 italic">
                      {formatDate(
                        new Date(event.endDate),
                        currentTimezone as string
                      )}
                    </div>
                  </div>
                  <div className="flex">
                    <div className="w-4/12">Attendees</div>
                    <div className="w-8/12 italic">
                      {event.attendees.map((attendee) => (
                        <div key={attendee.email}>{attendee.email}</div>
                      ))}
                    </div>
                  </div>
                </>
              )}
              <div className="flex justify-end space-x-2">
                {editingEventIndex === index ? (
                  <>
                    <Button variant="text" onClick={handleCancel}>
                      Cancel
                    </Button>
                    <Button
                      variant="contained"
                      className="bg-blue-500"
                      onClick={handleSave}
                      disabled={loading}
                      id="emulatorActionEdit_btn"
                    >
                      {loading ? "Saving..." : "Save"}
                      {loading && <ImSpinner8 className="ml-2 animate-spin" />}
                    </Button>
                  </>
                ) : (
                  <>
                    <IconButton
                      aria-label="edit"
                      onClick={() => handleEdit(index)}
                    >
                      <AiFillEdit />
                    </IconButton>
                    {!details?.deleted ? (
                      <IconButton
                        aria-label="delete"
                        color="error"
                        onClick={handleOpen}
                      >
                        <BsFillTrashFill />
                      </IconButton>
                    ) : (
                      <IconButton
                        aria-label="restore"
                        color="info"
                        onClick={handleOpen}
                      >
                        <MdRestore />
                      </IconButton>
                    )}
                  </>
                )}
              </div>
            </div>
          </AccordionDetails>
        </Accordion>
      ))}
      <div className="flex items-center">
        <Typography variant="subtitle2" sx={{ width: "33.3%" }}>
          Timezone
        </Typography>
        <Typography variant="subtitle2">{currentTimezone}</Typography>
        <Tooltip
          title={`The selected time is based on ${currentTimezone} timezone.`}
        >
          <InfoOutlinedIcon fontSize="inherit" sx={{ ml: 1 }} />
        </Tooltip>
      </div>
      {timezoneError && (
        <Alert severity="warning">
          {`Couldn't find a timezone attached to your calendar. Defaulted to your
          current timezone`}
        </Alert>
      )}
    </div>
  );
};

export default GoogleCalendarDetails;
