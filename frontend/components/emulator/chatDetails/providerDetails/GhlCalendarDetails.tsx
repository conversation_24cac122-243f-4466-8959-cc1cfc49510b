// "use client";

import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>conButton, Toolt<PERSON>, Typo<PERSON> } from "@mui/material";
import React, { useEffect, useState } from "react";
import { AiFillEdit } from "react-icons/ai";
import dayjs, { Dayjs } from "dayjs";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { DesktopDateTimePicker } from "@mui/x-date-pickers/DesktopDateTimePicker";
import {
  IBotResponse,
  IGhlCalendar,
  emulatorActions,
} from "@/slices/emulator/emulatorSlice";
import { useDispatch, useSelector } from "react-redux";
import { useRouter } from "next/router";
import { editGhlCalendarResponse } from "@/requests/sessions/editResponse/editGhlCalendarResponse";
import ButtonSpinner from "@/components/general/buttons/spinner";
import { BsFillTrashFill } from "react-icons/bs";
import { MdRestore } from "react-icons/md";
import { RootState } from "@/store";
import InfoOutlinedIcon from "@mui/icons-material/InfoOutlined";
import utc from "dayjs/plugin/utc.js";
import timezone from "dayjs/plugin/timezone.js";
import { ImSpinner8 } from "react-icons/im";

interface IGoogleCalendarDetails {
  parentEventId: string;
  details: IBotResponse;
  handleOpen: () => void;
}
dayjs.extend(utc);
dayjs.extend(timezone);

const formatDate = (date: Date, timezone: string) => {
  return dayjs(date).tz(timezone).format("M/D/YYYY h:mm:ss A");
};

const GhlCalendarDetails: React.FC<IGoogleCalendarDetails> = ({
  parentEventId,
  details,
  handleOpen,
}) => {
  const dispatch = useDispatch();
  const router = useRouter();
  const timezones = useSelector(
    (state: RootState) => state.organization.timezones
  );
  const timezonesLoading = useSelector(
    (state: RootState) => state.organization.timezonesLoading
  );

  const { sessionId } = router.query;
  const [loading, setLoading] = useState<boolean>(false);
  const eventData = details.eventData as IGhlCalendar;
  const [currentTimezone, setCurrentTimezone] = useState<string>();
  const [timezoneError, setTimezoneError] = useState(false);
  const [edit, setEdit] = useState<boolean>(false);
  const [startTimevalue, setStartTimevalue] = React.useState<Dayjs | null>(
    null
  );
  const [endTimevalue, setEndTimevalue] = React.useState<Dayjs | null>(null);

  const userTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

  useEffect(() => {
    const accountId = details.accountId as string;
    const timezone = timezones.find(
      (timezone) => timezone.accountId === accountId
    );
    if (!!!timezone) {
      setTimezoneError(true);
    }
    setCurrentTimezone(timezone?.timezone ?? userTimezone);
  }, [timezones]);
  useEffect(() => {
    if (eventData?.startDate) {
      setStartTimevalue(dayjs(eventData.startDate));
    } else {
      setStartTimevalue(null);
    }
    if (eventData?.endDate) {
      setEndTimevalue(dayjs(eventData.endDate));
    } else {
      setEndTimevalue(null);
    }
  }, [eventData?.startDate, eventData?.endDate]);

  const handleSave = async () => {
    setLoading(true);
    const payload = {
      eventId: details.eventId,
      sessionId: sessionId as string,
      accountId: details.accountId as string,
      accountName: details.accountName as string,
      ...(details && details.hasOwnProperty("accountId")
        ? { accountId: details.accountId }
        : {}),
      deleted: details?.deleted || false, //If the deleted property is not present, then its not deleted
      action: "read",
      startDate: startTimevalue?.toDate() as Date,
      endDate: endTimevalue?.toDate() as Date,
    };
    console.log({ payload });

    const response = await editGhlCalendarResponse(payload, dispatch);
    if (response?.status === 200) {
      setEdit(false);
      dispatch(
        emulatorActions.botResponseUpdated({
          parentEventId,
          response: {
            ...details,
            action: payload.action,
            eventData: {
              startDate: payload.startDate?.toString(),
              endDate: payload.endDate?.toString(),
            },
          },
        })
      );
    }

    setLoading(false);
  };
  if (timezonesLoading)
    return (
      <div className="flex justify-center">
        <ImSpinner8 className="animate-spin text-[24px]" />
      </div>
    );
  return (
    <div className="">
      <div className={`${!edit && "flex"}`}>
        <div className="w-10/12 flex flex-col gap-3">
          {/* Start time */}
          <div className={`${!edit && "flex"}`}>
            <div className="w-4/12">Start Time</div>
            {!edit && (
              <div className="w-8/12 italic">
                {startTimevalue
                  ? formatDate(
                      startTimevalue.toDate(),
                      currentTimezone as string
                    )
                  : "No date found"}
              </div>
            )}
            {edit && (
              <div className="w-full italic">
                {
                  <LocalizationProvider dateAdapter={AdapterDayjs}>
                    <DesktopDateTimePicker
                      sx={{ width: "100%" }}
                      label=""
                      value={startTimevalue}
                      timezone={currentTimezone}
                      onChange={(newValue) => setStartTimevalue(newValue)}
                    />
                  </LocalizationProvider>
                }
              </div>
            )}
          </div>
          {/* End time */}
          <div className={`${!edit && "flex"}`}>
            <div className="w-4/12">End Time</div>
            {!edit && (
              <div className="w-8/12 italic">
                {endTimevalue
                  ? formatDate(endTimevalue.toDate(), currentTimezone as string)
                  : "No date found"}
              </div>
            )}
            {edit && (
              <div className="w-full italic">
                {
                  <LocalizationProvider dateAdapter={AdapterDayjs}>
                    <DesktopDateTimePicker
                      sx={{ width: "100%" }}
                      label=""
                      timezone={currentTimezone}
                      value={endTimevalue}
                      onChange={(newValue) => setEndTimevalue(newValue)}
                    />
                  </LocalizationProvider>
                }
              </div>
            )}
          </div>
          <div className="flex items-center">
            <Typography variant="subtitle2" sx={{ width: "33.3%" }}>
              Timezone
            </Typography>
            <Typography variant="subtitle2">{currentTimezone}</Typography>
            <Tooltip
              title={`The selected time is based on ${currentTimezone} timezone.`}
            >
              <InfoOutlinedIcon fontSize="inherit" sx={{ ml: 1 }} />
            </Tooltip>
          </div>
          {timezoneError && (
            <Alert severity="warning">
              Couldn&apos;t find a timezone attached to your calendar. Defaulted
              to your current timezone
            </Alert>
          )}
        </div>
        {!edit && (
          <div className="w-1/12 flex items-center">
            <IconButton aria-label="edit">
              <AiFillEdit onClick={() => setEdit(true)} />
            </IconButton>
            {!details?.deleted && (
              <IconButton aria-label="delete" color="error">
                <BsFillTrashFill onClick={() => handleOpen()} />
              </IconButton>
            )}
            {!!details?.deleted && (
              <IconButton aria-label="delete" color="info">
                <MdRestore onClick={() => handleOpen()} />
              </IconButton>
            )}
          </div>
        )}
      </div>
      {edit && (
        <div className="text-right mt-2">
          <Button variant="text" onClick={() => setEdit(false)}>
            Cancel
          </Button>
          <Button
            variant="contained"
            className="bg-blue-500"
            onClick={handleSave}
            disabled={loading ? true : false}
            id="emulatorActionEdit_btn"
          >
            Save
            {loading && <ButtonSpinner />}
          </Button>
        </div>
      )}
    </div>
  );
};

export default GhlCalendarDetails;
