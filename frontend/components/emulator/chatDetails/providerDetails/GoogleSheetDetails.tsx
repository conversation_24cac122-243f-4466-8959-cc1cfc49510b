import ButtonSpinner from "@/components/general/buttons/spinner";
import { editGoogleSheetResponse } from "@/requests/sessions/editResponse/editGoogleSheetResponse";
import {
  IBotResponse,
  IGoogleSheet,
  emulatorActions,
} from "@/slices/emulator/emulatorSlice";
import { Button, IconButton, TextField } from "@mui/material";
import { useRouter } from "next/router";
import React, { useState } from "react";
import { AiFillEdit } from "react-icons/ai";
import { BsFillTrashFill } from "react-icons/bs";
import { MdRestore } from "react-icons/md";
import { useDispatch } from "react-redux";

interface IProp {
  parentEventId: string;
  details: IBotResponse;
  handleOpen: () => void;
}

const GoogleSheetsDetails: React.FC<IProp> = ({
  parentEventId,
  details,
  handleOpen,
}) => {
  const dispatch = useDispatch();
  const router = useRouter();
  const { sessionId } = router.query;
  const eventData = details.eventData as IGoogleSheet;
  const [loading, setLoading] = useState<boolean>(false);
  const [edit, setEdit] = useState<boolean>(false);
  const [startRowValue, setStartRowValue] = useState<string>(
    eventData?.rowStart || ""
  );
  const [endRowValue, setEndRowValue] = useState<string>(
    eventData?.rowEnd || ""
  );
  if (details.action === "read") {
    return (
      <div className="flex justify-between">
        <div className="">{details.accountName || "No name"} sheet read</div>
        <div className="">
          {!details?.deleted && (
            <IconButton aria-label="delete" color="error">
              <BsFillTrashFill onClick={() => handleOpen()} />
            </IconButton>
          )}
          {!!details?.deleted && (
            <IconButton aria-label="delete" color="info">
              <MdRestore onClick={() => handleOpen()} />
            </IconButton>
          )}
        </div>
      </div>
    );
  }

  const handleSave = async () => {
    setLoading(true);
    const response = await editGoogleSheetResponse(
      {
        sessionId: sessionId as string,
        action: "read",
        eventId: details.eventId,
        accountId: details.accountId as string,
        accountName: details.accountName as string,
        rowStart: startRowValue,
        rowEnd: endRowValue,
      },
      dispatch
    );
    console.log({ response });
    setLoading(false);
    if (response?.status === 200) {
      setEdit(false);
      //edit it in the slice
      console.log({ parentEventId, response: details });

      dispatch(
        emulatorActions.botResponseUpdated({
          parentEventId: parentEventId,
          response: {
            ...details,
            action: "read",
            eventData: {
              rowStart: startRowValue,
              rowEnd: endRowValue,
            },
          },
        })
      );
    }
  };

  return (
    <div className="">
      <div className={`${!edit && "flex"}`}>
        <div className="w-10/12 flex flex-col gap-3">
          {/* Start row */}
          <div className={`${!edit && "flex"}`}>
            <div className="w-4/12">Start Row</div>
            {!edit && <div className="w-8/12 italic">{startRowValue}</div>}
            {edit && (
              <div className="w-full italic">
                <TextField
                  id="start-row"
                  label=""
                  variant="outlined"
                  value={startRowValue}
                  onChange={(e) => {
                    setStartRowValue(e.target.value);
                  }}
                />
              </div>
            )}
          </div>
          {/* End row */}
          <div className={`${!edit && "flex"}`}>
            <div className="w-4/12">End row</div>
            {!edit && <div className="w-8/12 italic">{endRowValue}</div>}
            {edit && (
              <div className="w-full italic">
                <TextField
                  id="end-row"
                  label=""
                  variant="outlined"
                  value={endRowValue}
                  onChange={(e) => {
                    setEndRowValue(e.target.value);
                  }}
                />
              </div>
            )}
          </div>
        </div>
        {!edit && (
          <div className="w-1/12 flex items-center">
            <IconButton aria-label="edit">
              <AiFillEdit onClick={() => setEdit(true)} />
            </IconButton>
            {!details?.deleted && (
              <IconButton aria-label="delete" color="error">
                <BsFillTrashFill onClick={() => handleOpen()} />
              </IconButton>
            )}
            {!!details?.deleted && (
              <IconButton aria-label="delete" color="info">
                <MdRestore onClick={() => handleOpen()} />
              </IconButton>
            )}
          </div>
        )}
      </div>
      {edit && (
        <div className="text-right mt-2">
          <Button
            variant="text"
            onClick={() => {
              setEdit(false);
              setStartRowValue(eventData.rowStart);
              setEndRowValue(eventData.rowEnd);
            }}
          >
            Cancel
          </Button>
          <Button
            variant="contained"
            className="bg-blue-500"
            onClick={handleSave}
            disabled={loading ? true : false}
          >
            Save
            {loading && <ButtonSpinner />}
          </Button>
        </div>
      )}
    </div>
  );
};

export default GoogleSheetsDetails;
