import React, { useState } from "react";
import { Box, <PERSON>con<PERSON><PERSON><PERSON>, Stack } from "@mui/material";
import { AiFillEdit } from "react-icons/ai";
import { BsFillTrashFill } from "react-icons/bs";
import {
  IBotResponse,
  IHTtpGetRequestEventData,
  emulatorActions,
} from "@/slices/emulator/emulatorSlice";
import HttpGetView from "./HttpGetView";
import HttpGetEdit from "./HttpGetEdit";
import { editHttpGetAction } from "@/requests/sessions/editResponse/editHttpGet";
import { useDispatch } from "react-redux";
import { useRouter } from "next/router";

interface IProp {
  parentEventId: string;
  details: IBotResponse;
  handleOpen: () => void;
}

interface IHttpProperty {
  name: string;
  description: string;
  value: string;
}

const HttpGetDetails: React.FC<IProp> = ({
  details,
  handleOpen,
  parentEventId,
}) => {
  const dispatch = useDispatch();
  const router = useRouter();
  const { sessionId } = router.query as { sessionId: string };
  const [isEditing, setIsEditing] = useState(false);
  const httpProperties = (details.eventData as IHTtpGetRequestEventData)
    .properties as IHttpProperty[];

  const handleEditStart = () => setIsEditing(true);
  const handleEditEnd = () => setIsEditing(false);

  const handleSave = async (updatedProperties: IHttpProperty[]) => {
    const response = await editHttpGetAction(
      {
        eventId: details.eventId,
        sessionId,
        properties: updatedProperties,
      },
      dispatch
    );
    console.log("Saving:", updatedProperties);
    if (response?.status === 200) {
      handleEditEnd();
      dispatch(
        emulatorActions.botResponseUpdated({
          parentEventId,
          response: {
            ...details,
            eventData: {
              properties: updatedProperties,
            },
          },
        })
      );
    }
  };

  return (
    <Stack direction="column" spacing={2} width="100%">
      {isEditing ? (
        <HttpGetEdit
          properties={httpProperties}
          onSave={handleSave}
          onCancel={handleEditEnd}
        />
      ) : (
        <Stack
          direction="row"
          justifyContent="space-between"
          alignItems="flex-start"
          width="100%"
        >
          <HttpGetView properties={httpProperties} />
          <Stack direction="row" gap={1}>
            <IconButton aria-label="edit" onClick={handleEditStart}>
              <AiFillEdit />
            </IconButton>
            <IconButton aria-label="delete" color="error" onClick={handleOpen}>
              <BsFillTrashFill />
            </IconButton>
          </Stack>
        </Stack>
      )}
    </Stack>
  );
};

export default HttpGetDetails;
