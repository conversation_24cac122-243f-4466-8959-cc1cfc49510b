import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  TextField,
  Button,
  Typography,
  CircularProgress,
} from "@mui/material";

interface IHttpProperty {
  name: string;
  description: string;
  value: string;
}

interface IHttpGetEditProps {
  properties: IHttpProperty[];
  onSave: (updatedProperties: IHttpProperty[]) => Promise<void>;
  onCancel: () => void;
}

const HttpGetEdit: React.FC<IHttpGetEditProps> = ({
  properties,
  onSave,
  onCancel,
}) => {
  const [editedProperties, setEditedProperties] = useState(properties);
  const [isSaving, setIsSaving] = useState(false);

  const handlePropertyChange = (index: number, value: string) => {
    const updatedProperties = [...editedProperties];
    updatedProperties[index] = { ...updatedProperties[index], value };
    setEditedProperties(updatedProperties);
  };

  const handleSave = async () => {
    setIsSaving(true);
    try {
      await onSave(editedProperties);
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <Stack direction="column" gap={2} width="100%">
      {editedProperties.map((property, index) => (
        <Stack key={index} direction="column" gap={1}>
          <Typography variant="subtitle1" fontWeight={700}>
            {property.name}
          </Typography>
          <Typography variant="body2" color="textSecondary">
            {property.description}
          </Typography>
          <TextField
            label="Value"
            value={property.value}
            onChange={(e) => handlePropertyChange(index, e.target.value)}
            fullWidth
          />
        </Stack>
      ))}
      <Stack direction="row" justifyContent="flex-end" spacing={2}>
        <Button onClick={onCancel} variant="outlined" disabled={isSaving}>
          Cancel
        </Button>
        <Button
          onClick={handleSave}
          variant="contained"
          disabled={isSaving}
          startIcon={
            isSaving ? <CircularProgress size={20} color="inherit" /> : null
          }
        >
          {isSaving ? "Saving..." : "Save"}
        </Button>
      </Stack>
    </Stack>
  );
};

export default HttpGetEdit;
