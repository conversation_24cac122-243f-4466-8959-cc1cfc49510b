import { IBotResponse } from "@/slices/emulator/emulatorSlice";
import { IconButton, Stack, Typography } from "@mui/material";
import { BsFillTrashFill } from "react-icons/bs";
import { MdRestore } from "react-icons/md";

interface IProp {
  parentEventId: string;
  details: IBotResponse;
  handleOpen: () => void;
}

const BasicDetails: React.FC<IProp> = ({
  details,
  parentEventId,
  handleOpen,
}) => {
  return (
    <>
      <Stack direction={"row"} justifyContent={"space-between"}>
        <Typography variant="body1">Action fired successfully</Typography>
        <IconButton aria-label="delete" color="error">
          {!details?.deleted && (
            <IconButton aria-label="delete" color="error">
              <BsFillTrashFill onClick={() => handleOpen()} />
            </IconButton>
          )}
          {!!details?.deleted && (
            <IconButton aria-label="delete" color="info">
              <MdRestore onClick={() => handleOpen()} />
            </IconButton>
          )}
        </IconButton>
      </Stack>
    </>
  );
};

export default BasicDetails;
