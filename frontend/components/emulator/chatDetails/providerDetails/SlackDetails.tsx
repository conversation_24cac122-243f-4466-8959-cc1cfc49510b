import React, { useState } from "react";
import {
  IBotResponse,
  ISlackEventData,
  emulatorActions,
} from "@/slices/emulator/emulatorSlice";
import {
  Box,
  Button,
  CircularProgress,
  IconButton,
  Stack,
  TextField,
  Typography,
  Paper,
} from "@mui/material";
import { Edit as EditIcon, Delete as DeleteIcon } from "@mui/icons-material";
import { editSlackAction } from "@/requests/sessions/editResponse/editSlackResponse";
import { useDispatch } from "react-redux";
import { useRouter } from "next/router";

interface SlackDetailsProps {
  parentEventId: string;
  details: IBotResponse;
  handleOpen: () => void;
}

const SlackDetails: React.FC<SlackDetailsProps> = ({
  details,
  handleOpen,
  parentEventId,
}) => {
  const dispatch = useDispatch();
  const router = useRouter();
  const { sessionId } = router.query as { sessionId: string };
  const eventData = details.eventData as ISlackEventData;
  const [text, setText] = useState(eventData?.slackResponse?.text);
  const [isEditing, setIsEditing] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");

  const handleTextChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setText(event.target.value);
    setError("");
  };

  const handleSave = async () => {
    if (!text.trim()) {
      setError("Message cannot be empty");
      return;
    }

    setIsLoading(true);
    setError("");

    try {
      const response = await editSlackAction(
        {
          eventId: details.eventId,
          sessionId,
          action: "write",
          slackResponse: {
            text,
            type: eventData?.slackResponse.type,
          },
        },
        dispatch
      );
      setIsEditing(false);
      if (response?.status === 200) {
        dispatch(
          emulatorActions.botResponseUpdated({
            parentEventId,
            response: {
              ...details,
              eventData: {
                slackResponse: {
                  text,
                  type: eventData?.slackResponse.type,
                },
              },
            },
          })
        );
      }
    } catch (err) {
      console.error("Error updating Slack message:", err);
      setError("Failed to update message. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    setText(eventData?.slackResponse?.text);
    setIsEditing(false);
    setError("");
  };

  return (
    <Stack direction="column" spacing={2} width="100%">
      {isEditing ? (
        <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
          <TextField
            variant="outlined"
            label="Slack Message"
            placeholder="Type your message here..."
            value={text}
            onChange={handleTextChange}
            fullWidth
            multiline
            rows={4}
            error={!!error}
            helperText={error}
            disabled={isLoading}
            InputProps={{
              sx: { borderRadius: 2 },
            }}
          />
          <Stack direction="row" justifyContent="flex-end" spacing={1}>
            <Button
              variant="outlined"
              onClick={handleCancel}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button
              variant="contained"
              onClick={handleSave}
              disabled={!text.trim() || isLoading}
              startIcon={
                isLoading ? (
                  <CircularProgress size={20} color="inherit" />
                ) : null
              }
            >
              {isLoading ? "Saving" : "Save"}
            </Button>
          </Stack>
        </Box>
      ) : (
        <Stack
          direction="row"
          justifyContent="space-between"
          alignItems="flex-start"
          width="100%"
        >
          <Box>
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
              Text Response
            </Typography>
            <Typography variant="body1">
              {eventData?.slackResponse?.text ?? "No response"}
            </Typography>
          </Box>
          <Stack direction="row" spacing={1}>
            <IconButton
              aria-label="edit"
              onClick={() => setIsEditing(true)}
              color="primary"
            >
              <EditIcon />
            </IconButton>
            <IconButton aria-label="delete" color="error" onClick={handleOpen}>
              <DeleteIcon />
            </IconButton>
          </Stack>
        </Stack>
      )}
    </Stack>
  );
};

export default SlackDetails;
