import React from "react";
import { Stack, Typography } from "@mui/material";

interface IHttpProperty {
  name: string;
  description: string;
  value: string;
}

interface IHttpGetViewProps {
  properties: IHttpProperty[];
}

const HttpGetView: React.FC<IHttpGetViewProps> = ({ properties }) => (
  <Stack direction="column" gap={2} width="100%">
    {properties.map((property, index) => (
      <Stack key={index} direction="column">
        <Typography variant="subtitle1" fontWeight={700}>
          {property.name}
        </Typography>
        <Typography variant="body2" color="textSecondary">
          {property.description}
        </Typography>
        <Typography variant="body1">{property.value}</Typography>
      </Stack>
    ))}
  </Stack>
);

export default HttpGetView;
