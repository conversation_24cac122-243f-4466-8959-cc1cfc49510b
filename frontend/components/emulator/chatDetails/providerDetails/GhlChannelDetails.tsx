import {
  IBotResponse,
  IGhlChannel,
  emulatorActions,
} from "@/slices/emulator/emulatorSlice";
import {
  Button,
  IconButton,
  Stack,
  TextField,
  Typography,
} from "@mui/material";
import React, { useState } from "react";
import { BsFillTrashFill } from "react-icons/bs";
import EditIcon from "@mui/icons-material/Edit";
import { AiFillEdit } from "react-icons/ai";
import { useRouter } from "next/router";
import { useDispatch } from "react-redux";
import { editGhlCustomValue } from "@/requests/sessions/editResponse/editGhlCustomValue";
import ButtonSpinner from "@/components/general/buttons/spinner";
import { FaTrashAlt } from "react-icons/fa";
import { MdRestore } from "react-icons/md";

interface IProp {
  parentEventId: string;
  details: IBotResponse;
  handleOpen: () => void;
}

const GhlChannelDetails: React.FC<IProp> = ({
  parentEventId,
  details,
  handleOpen,
}) => {
  const dispatch = useDispatch();
  const router = useRouter();
  const { sessionId } = router.query as { sessionId: string };
  const eventData = details?.eventData as IGhlChannel;
  const [loading, setLoading] = useState(false);
  const [customFieldKey, setCustomFieldKey] = useState(
    eventData?.customFieldData?.fieldKey || ""
  );
  const [customFieldValue, setCustomFieldValue] = useState(
    eventData?.customFieldData?.field_value || ""
  );

  const [edit, setEdit] = useState(false);
  const handleCustomFieldEdit = async () => {
    const body = {
      eventId: details.eventId as string,
      accountId: details.accountId as string,
      accountName: details.accountName as string,
      sessionId,
      action: "customField",
      customField: {
        fieldKey: customFieldKey,
        field_value: customFieldValue,
      },
    };
    setLoading(true);
    const response = await editGhlCustomValue(body, dispatch);
    setLoading(false);
    if (response?.status === 200) {
      setEdit(false);
      dispatch(
        emulatorActions.botResponseUpdated({
          parentEventId,
          response: {
            ...details,
            eventData: {
              customFieldData: {
                fieldKey: customFieldKey,
                field_value: customFieldValue,
              },
            },
          },
        })
      );
    }
  };
  return (
    <>
      {details.action === "tag" && (
        <div className="flex justify-between">
          <div className="">{details?.accountName || "No name"} tag read.</div>
          <div className="">
            {!details?.deleted && (
              <IconButton aria-label="delete" color="error">
                <BsFillTrashFill onClick={() => handleOpen()} />
              </IconButton>
            )}
            {!!details?.deleted && (
              <IconButton aria-label="delete" color="info">
                <MdRestore onClick={() => handleOpen()} />
              </IconButton>
            )}
          </div>
        </div>
      )}

      {details.action === "customField" && (
        <Stack direction={"column"} spacing={1}>
          {/* <Typography>{details.accountName}</Typography> */}
          <Stack direction={"row"} spacing={1}>
            <Typography flexGrow={1}>
              {customFieldKey} is set to{" "}
              <span className="font-semibold">
                {eventData?.customFieldData?.field_value}
              </span>
            </Typography>
            <IconButton
              aria-label="edit"
              size="medium"
              onClick={() => setEdit(true)}
            >
              <AiFillEdit fontSize="inherit" />
            </IconButton>
            {!details?.deleted && (
              <IconButton aria-label="delete" color="error">
                <BsFillTrashFill onClick={() => handleOpen()} />
              </IconButton>
            )}
            {!!details?.deleted && (
              <IconButton aria-label="delete" color="info">
                <MdRestore onClick={() => handleOpen()} />
              </IconButton>
            )}
          </Stack>
          {edit && (
            <>
              <TextField
                label={customFieldKey}
                value={customFieldValue}
                onChange={(e) => setCustomFieldValue(e.target.value)}
              />
              <Stack direction={"row"} justifyContent={"end"} spacing={1}>
                <Button
                  variant="text"
                  color="error"
                  onClick={() => setEdit(false)}
                >
                  Cancel
                </Button>
                <Button
                  variant="contained"
                  color="primary"
                  onClick={handleCustomFieldEdit}
                  disabled={loading}
                >
                  Save {loading && <ButtonSpinner />}
                </Button>
              </Stack>
            </>
          )}
        </Stack>
      )}
    </>
  );
};

export default GhlChannelDetails;
