import JsonViewer, { RawJsonViewer } from "@/components/JsonViewer";
import { editGenerateJsonResponse } from "@/requests/sessions/editResponse/editGenerateJsonResponse";
import {
  IBotResponse,
  IGenerateJson,
  ISinglejsonData,
  emulatorActions,
} from "@/slices/emulator/emulatorSlice";
import {
  Button,
  IconButton,
  Stack,
  TextField,
  Tooltip,
  Typography,
} from "@mui/material";
import { useRouter } from "next/router";
import React, { useEffect, useState } from "react";
import { AiFillEdit } from "react-icons/ai";
import { BsFillTrashFill } from "react-icons/bs";
import { MdEditOff, MdRestore } from "react-icons/md";
import { useDispatch } from "react-redux";
import InfoIcon from "@mui/icons-material/Info";
import ButtonSpinner from "@/components/general/buttons/spinner";

interface IProp {
  parentEventId: string;
  details: IBotResponse;
  handleOpen: () => void;
}

interface IEditJsonViewer {
  parentEventId: string;
  details: IBotResponse;
  setEditFn: (x: boolean) => void;
}

export const EditJsonViewer: React.FC<IEditJsonViewer> = ({
  details,
  parentEventId,
  setEditFn,
}) => {
  const dispatch = useDispatch();
  const router = useRouter();

  const { sessionId } = router.query as { sessionId: string };
  const jsonData = (details?.eventData as IGenerateJson)?.properties;
  console.log({ details, jsonData });
  const [loading, setLoading] = useState(false);
  const [editedDetails, setEditedDetails] = useState(jsonData);

  const handleTextFieldChange =
    (index: number) => (event: React.ChangeEvent<HTMLInputElement>) => {
      const newDetails = JSON.parse(JSON.stringify(editedDetails));
      newDetails[index].value = event.target.value;
      setEditedDetails(newDetails);
    };

  const handleSave = async () => {
    const propertyList = editedDetails.map((item) => ({
      ...item,
      propertyId: item.id,
    }));
    const payload = {
      eventId: details.eventId,
      accountId: details.accountId,
      accountName: details.accountName,
      sessionId,
      properties: propertyList,
    } as {
      sessionId: string;
      eventId: string;
      accountId: string;
      accountName: string;
      properties: {
        name: string;
        description: string;
        type: string;
        propertyId: string;
        value: string;
      }[];
    };
    setLoading(true);
    const response = await editGenerateJsonResponse(payload, dispatch);
    setLoading(false);
    if (response?.status === 200) {
      setEditFn(false);
      dispatch(
        emulatorActions.botResponseUpdated({
          parentEventId,
          response: {
            ...details,
            eventData: {
              properties: editedDetails,
            },
          },
        })
      );
    }
  };

  return (
    <>
      <Stack direction="column" gap={2}>
        {editedDetails?.map((detail, index) => (
          <Stack direction="column" spacing={2} key={"details_idx_" + index}>
            <Stack direction={"row"} alignItems={"center"} gap={1}>
              <Typography>{detail.name}</Typography>
              <Tooltip title={detail.description}>
                <InfoIcon className="cursor-pointer text-gray-500" />
              </Tooltip>
            </Stack>
            <TextField
              variant="outlined"
              value={detail?.value}
              id={"edit_name_" + index}
              key={"edit_name_" + index}
              onChange={handleTextFieldChange(index)}
              sx={{ width: 1 }}
            />
          </Stack>
        ))}
      </Stack>
      <Stack direction={"row"} gap={1} justifyContent={"end"} marginTop={2}>
        <Button variant="contained" onClick={handleSave} disabled={loading}>
          Save {loading ? <ButtonSpinner /> : null}
        </Button>
      </Stack>
    </>
  );
};
const GenerateJsonDetails: React.FC<IProp> = ({
  parentEventId,
  details,
  handleOpen,
}) => {
  const jsonData = (details?.eventData as IGenerateJson)?.properties;
  useEffect(() => {
    console.log("AFter useEffect ", { jsonData });
  }, [details]);
  // const jsonData = details.eventData.properties as ISinglejsonData[];
  const [edit, setEdit] = useState(false);
  return (
    <div>
      <div className="flex">
        <div className="w-11/12 overflow-auto">
          {!edit ? <RawJsonViewer details={jsonData} /> : null}
          {edit ? (
            <EditJsonViewer
              details={details}
              parentEventId={parentEventId}
              setEditFn={(x: boolean) => setEdit(x)}
            />
          ) : null}
        </div>
        <div className="w-2/12 flex justify-end items-start ml-1">
          {!edit ? (
            <IconButton aria-label="edit" onClick={() => setEdit(true)}>
              <AiFillEdit />
            </IconButton>
          ) : (
            <IconButton aria-label="edit" onClick={() => setEdit(false)}>
              <MdEditOff />
            </IconButton>
          )}
          {!details?.deleted && (
            <IconButton aria-label="delete" color="error">
              <BsFillTrashFill onClick={() => handleOpen()} />
            </IconButton>
          )}
          {!!details?.deleted && (
            <IconButton aria-label="delete" color="info">
              <MdRestore onClick={() => handleOpen()} />
            </IconButton>
          )}
        </div>
      </div>
    </div>
  );
};

export default GenerateJsonDetails;
