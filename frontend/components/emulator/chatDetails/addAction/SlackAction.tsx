import React, { useState } from "react";
import { useDispatch } from "react-redux";
import { TextField, Button, Box, CircularProgress } from "@mui/material";
import { emulatorActions } from "@/slices/emulator/emulatorSlice";
import {
  ISlackEmulatorActionPayload,
  addSlack,
} from "@/requests/sessions/add/addSlack";
import { CommonDataSource } from "@/slices/settings/integrations/integrationList";
import { IValueProp } from "../AddNewAction";

interface SlackActionProps {
  dataSource: CommonDataSource;
  sessionId: string;
  eventId: string;
  value: IValueProp;
  toggleNewAction: () => void;
}

const SlackAction: React.FC<SlackActionProps> = ({
  dataSource,
  eventId,
  sessionId,
  toggleNewAction,
  value,
}) => {
  const dispatch = useDispatch();
  const [text, setText] = useState("");
  const [error, setError] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  const handleTextChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setText(event.target.value);
    setError("");
  };

  const handleSubmit = async () => {
    if (!text.trim()) {
      setError("Message cannot be empty");
      return;
    }

    setIsLoading(true);
    setError("");

    const body: ISlackEmulatorActionPayload = {
      sessionId,
      eventId,
      accountId: value.accountId,
      accountName: value.accountName,
      action: "write",
      sender: "bot",
      kind: "slack",
      slackResponse: {
        type: "predictive",
        text: text.trim(),
      },
    };

    try {
      const response = await addSlack(body, dispatch);
      if (response?.status === 200) {
        dispatch(
          emulatorActions.newActionAdded({
            response: response.data.data,
            parentEventId: body.eventId,
          })
        );
        toggleNewAction();
      } else {
        throw new Error("Unexpected response status");
      }
    } catch (err) {
      console.error("Error adding Slack action:", err);
      setError("Failed to send message. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
      <TextField
        variant="outlined"
        label="Slack Message"
        placeholder="Type your message here..."
        value={text}
        onChange={handleTextChange}
        fullWidth
        multiline
        rows={4}
        error={!!error}
        helperText={error}
        disabled={isLoading}
        InputProps={{
          sx: { borderRadius: 2 },
        }}
      />
      <Box sx={{ display: "flex", justifyContent: "flex-end" }}>
        <Button
          variant="contained"
          onClick={handleSubmit}
          disabled={!text.trim() || isLoading}
          sx={{
            bgcolor: "primary.main",
            "&:hover": { bgcolor: "primary.dark" },
            borderRadius: 2,
            textTransform: "none",
          }}
        >
          {isLoading ? <CircularProgress size={24} color="inherit" /> : "Save"}
        </Button>
      </Box>
    </Box>
  );
};

export default SlackAction;
