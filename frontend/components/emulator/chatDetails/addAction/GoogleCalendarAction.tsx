import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>po<PERSON>,
  Text<PERSON>ield,
  Chip,
  Card,
  CardContent,
  IconButton,
} from "@mui/material";
import {
  DesktopDateTimePicker,
  LocalizationProvider,
} from "@mui/x-date-pickers";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";
import { ImSpinner8 } from "react-icons/im";
import AccessTimeIcon from "@mui/icons-material/AccessTime";
import DeleteIcon from "@mui/icons-material/Delete";
import { addGoogleCalendarResponse } from "@/requests/sessions/add/addGoogleCalendar";
import { emulatorActions } from "@/slices/emulator/emulatorSlice";
import { RootState } from "@/store";
import { GoogleCalendarDataSource } from "@/slices/settings/integrations/integrationList";
import { IValueProp } from "../AddNewAction";
import { Add } from "@mui/icons-material";

dayjs.extend(utc);
dayjs.extend(timezone);

interface IProp {
  dataSource: GoogleCalendarDataSource;
  sessionId: string;
  eventId: string;
  value: IValueProp;
  toggleNewAction: () => void;
}

interface CalendarEvent {
  id: string;
  summary: string;
  description: string;
  startDate: dayjs.Dayjs | null;
  endDate: dayjs.Dayjs | null;
  attendees: string[];
}

const GoogleCalendarAction: React.FC<IProp> = ({
  dataSource,
  sessionId,
  eventId,
  value,
  toggleNewAction,
}) => {
  const dispatch = useDispatch();
  const timezones = useSelector(
    (state: RootState) => state.organization.timezones
  );
  const timezonesLoading = useSelector(
    (state: RootState) => state.organization.timezonesLoading
  );
  const [currentTimezone, setCurrentTimezone] = useState<string>();
  const [calendarEvents, setCalendarEvents] = useState<CalendarEvent[]>([]);
  const [currentEvent, setCurrentEvent] = useState<CalendarEvent>({
    id: "",
    summary: "",
    description: "",
    startDate: null,
    endDate: null,
    attendees: [],
  });
  const [newAttendee, setNewAttendee] = useState("");
  const [timezoneError, setTimezoneError] = useState(false);
  const [loading, setLoading] = useState(false);

  const userTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

  useEffect(() => {
    const accountId = value?.accountId as string;
    const timezone = timezones?.find(
      (timezone) => timezone.accountId === accountId
    );
    if (!timezone) {
      setTimezoneError(true);
    }
    setCurrentTimezone(timezone?.timezone ?? userTimezone);
  }, [timezones, value?.accountId, userTimezone]);

  const handleAddAttendee = () => {
    if (newAttendee && !currentEvent.attendees.includes(newAttendee)) {
      setCurrentEvent((prev) => ({
        ...prev,
        attendees: [...prev.attendees, newAttendee],
      }));
      setNewAttendee("");
    }
  };

  const handleRemoveAttendee = (email: string) => {
    setCurrentEvent((prev) => ({
      ...prev,
      attendees: prev.attendees.filter((a) => a !== email),
    }));
  };

  const handleAddEvent = () => {
    if (
      currentEvent.summary &&
      currentEvent.startDate &&
      currentEvent.endDate
    ) {
      setCalendarEvents((prev) => [
        ...prev,
        { ...currentEvent, id: Date.now().toString() },
      ]);
      setCurrentEvent({
        id: "",
        summary: "",
        description: "",
        startDate: null,
        endDate: null,
        attendees: [],
      });
    } else {
      alert("Please fill in at least the summary, start time, and end time.");
    }
  };

  const handleRemoveEvent = (id: string) => {
    setCalendarEvents((prev) => prev.filter((event) => event.id !== id));
  };

  const handleEditEvent = (id: string) => {
    const eventToEdit = calendarEvents.find((event) => event.id === id);
    if (eventToEdit) {
      setCurrentEvent(eventToEdit);
      setCalendarEvents((prev) => prev.filter((event) => event.id !== id));
    }
  };

  const handleSubmit = async () => {
    if (calendarEvents.length === 0) {
      alert("Please add at least one event");
      return;
    }

    const body = {
      sessionId,
      eventId,
      action: value.activity,
      accountId: value.accountId,
      accountName: value.accountName,
      deleted: false,
      eventData: {
        calendarEvents: calendarEvents.map((event) => ({
          summary: event.summary,
          description: event.description,
          startDate: event.startDate?.toISOString() ?? "",
          endDate: event.endDate?.toISOString() ?? "",
          attendees: event.attendees.map((email) => ({ email })),
        })),
      },
    };

    setLoading(true);
    try {
      const response = await addGoogleCalendarResponse(body, dispatch);
      if (response?.status === 200) {
        toggleNewAction();
        const data = response.data.data;
        dispatch(
          emulatorActions.newActionAdded({
            response: data,
            parentEventId: body.eventId,
          })
        );
      }
    } catch (error) {
      console.error("Error adding Google Calendar events:", error);
    } finally {
      setLoading(false);
    }
  };

  if (timezonesLoading) {
    return (
      <div className="flex justify-center">
        <ImSpinner8 className="animate-spin text-2xl" />
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <Typography variant="h6">Add New Event</Typography>
      <TextField
        fullWidth
        label="Summary"
        value={currentEvent.summary}
        onChange={(e) =>
          setCurrentEvent((prev) => ({ ...prev, summary: e.target.value }))
        }
      />
      <TextField
        fullWidth
        label="Description"
        multiline
        rows={3}
        value={currentEvent.description}
        onChange={(e) =>
          setCurrentEvent((prev) => ({ ...prev, description: e.target.value }))
        }
      />
      <LocalizationProvider dateAdapter={AdapterDayjs}>
        <DesktopDateTimePicker
          label="Start time"
          value={currentEvent.startDate}
          onChange={(newValue) =>
            setCurrentEvent((prev) => ({ ...prev, startDate: newValue }))
          }
          timezone={currentTimezone}
          sx={{ width: "100%" }}
        />
        <DesktopDateTimePicker
          label="End time"
          value={currentEvent.endDate}
          onChange={(newValue) =>
            setCurrentEvent((prev) => ({ ...prev, endDate: newValue }))
          }
          timezone={currentTimezone}
          sx={{ width: "100%", mt: 2 }}
        />
      </LocalizationProvider>
      <div className="flex items-center gap-2">
        <TextField
          label="Add Attendee"
          value={newAttendee}
          onChange={(e) => setNewAttendee(e.target.value)}
          onKeyPress={(e) => e.key === "Enter" && handleAddAttendee()}
          sx={{ flexGrow: 1 }}
        />
        <Button onClick={handleAddAttendee} variant="text" startIcon={<Add />}>
          Add
        </Button>
      </div>
      <div className="flex flex-wrap gap-2">
        {currentEvent.attendees.map((email) => (
          <Chip
            key={email}
            label={email}
            onDelete={() => handleRemoveAttendee(email)}
          />
        ))}
      </div>
      <Button
        variant="contained"
        onClick={handleAddEvent}
        startIcon={<Add />}
        disabled={
          !currentEvent.summary ||
          !currentEvent.startDate ||
          !currentEvent.endDate
        }
      >
        {currentEvent.id ? "Update Event" : "Add Event"}
      </Button>

      {calendarEvents.length > 0 && (
        <div>
          <Typography variant="h6" sx={{ mt: 4, mb: 2 }}>
            Added Events
          </Typography>
          {calendarEvents.map((event) => (
            <Card key={event.id} sx={{ mb: 2 }}>
              <CardContent>
                <Typography variant="h6">{event.summary}</Typography>
                <Typography variant="body2">{event.description}</Typography>
                <Typography variant="body2">
                  Start: {event.startDate?.format("YYYY-MM-DD HH:mm")}
                </Typography>
                <Typography variant="body2">
                  End: {event.endDate?.format("YYYY-MM-DD HH:mm")}
                </Typography>
                <Typography variant="body2">
                  Attendees: {event.attendees.join(", ")}
                </Typography>
                <Stack direction="row" spacing={1} sx={{ mt: 1 }}>
                  <Button
                    size="small"
                    onClick={() => handleEditEvent(event.id)}
                  >
                    Edit
                  </Button>
                  <Button
                    size="small"
                    color="error"
                    onClick={() => handleRemoveEvent(event.id)}
                  >
                    Delete
                  </Button>
                </Stack>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      <Tooltip title="Timezone">
        <Stack direction="row" spacing={1} alignItems="center">
          <AccessTimeIcon fontSize="small" />
          <Typography variant="body2">{currentTimezone}</Typography>
        </Stack>
      </Tooltip>
      {timezoneError && (
        <Alert severity="warning">
          {`Couldn't find a timezone attached to your calendar. Defaulted to your
          current timezone.`}
        </Alert>
      )}
      <div className="text-right">
        <Button
          variant="contained"
          onClick={handleSubmit}
          disabled={loading || calendarEvents.length === 0}
        >
          {loading ? "Saving..." : "Save All Events"}
        </Button>
      </div>
    </div>
  );
};

export default GoogleCalendarAction;
