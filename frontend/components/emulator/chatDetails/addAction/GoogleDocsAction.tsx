import { CommonDataSource } from "@/slices/settings/integrations/integrationList";
import React from "react";
import { IValueProp } from "../AddNewAction";
import { Button } from "@mui/material";
import { useDispatch } from "react-redux";
import { emulatorActions } from "@/slices/emulator/emulatorSlice";
import { addGoogleDoc } from "@/requests/sessions/add/addGoogleDocs";

interface IProp {
  dataSource: CommonDataSource;
  sessionId: string;
  eventId: string;
  value: IValueProp;
  toggleNewAction: () => void;
}

const GoogleDocsAction: React.FC<IProp> = ({
  dataSource,
  eventId,
  sessionId,
  toggleNewAction,
  value,
}) => {
  const dispatch = useDispatch();
  const handleSubmit = async () => {
    toggleNewAction();
    let body = {
      sessionId,
      eventId,
      accountId: value.accountId,
      accountName: value.accountName,
      action: value.activity,
    };
    console.log({ body });
    const response = await addGoogleDoc(body, dispatch);
    if (response?.status === 200) {
      dispatch(
        emulatorActions.newActionAdded({
          response: response.data.data,
          parentEventId: body.eventId,
        })
      );
    }
  };
  return (
    <>
      <div className="">
        <span className="font-bold">{value.accountName || "No name"}</span>{" "}
        google doc will be saved
      </div>
      <div className="pt-3 text-right">
        <Button
          variant="contained"
          className="bg-blue-500"
          onClick={handleSubmit}
        >
          Save
        </Button>
      </div>
    </>
  );
};

export default GoogleDocsAction;
