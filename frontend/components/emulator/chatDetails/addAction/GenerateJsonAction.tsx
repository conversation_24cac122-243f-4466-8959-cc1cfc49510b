import React, { useEffect, useState } from "react";
import { IValueProp } from "../AddNewAction";
import { IGenerateJsonAction } from "@/slices/agents/agentsSlice";
import { <PERSON><PERSON>, Stack, TextField, Tooltip, Typography } from "@mui/material";
import {
  ISinglejsonData,
  emulatorActions,
} from "@/slices/emulator/emulatorSlice";
import { addGenerateJsonResponse } from "@/requests/sessions/add/addGenerateJson";
import { useDispatch } from "react-redux";
import InfoIcon from "@mui/icons-material/Info";
import ButtonSpinner from "@/components/general/buttons/spinner";

interface IProp {
  sessionId: string;
  eventId: string;
  value: IValueProp;
  toggleNewAction: () => void;
  selectedAccountDetails: IGenerateJsonAction;
}

const GenerateJsonAction: React.FC<IProp> = ({
  sessionId,
  eventId,
  value,
  toggleNewAction,
  selectedAccountDetails,
}) => {
  const dispatch = useDispatch();
  const [loading, setLoading] = useState(false);
  const [jsonDetails, setJsonDetails] = useState<ISinglejsonData[]>([]);
  useEffect(() => {
    setJsonDetails(
      selectedAccountDetails?.jsonObjects?.properties.map((item) => ({
        ...item,
        value: "",
      })) || []
    );
  }, [selectedAccountDetails?.jsonObjects?.properties]);
  useEffect(() => {
    console.log({
      jsonDetails,
    });
  }, [jsonDetails]);

  const handleSubmit = async () => {
    setLoading(true);
    const response = await addGenerateJsonResponse(
      {
        sessionId,
        eventId,
        accountId: selectedAccountDetails.accountId as string,
        accountName: selectedAccountDetails.accountName as string,
        action: "read",
        deleted: false,
        properties: jsonDetails.map((item) => ({
          ...item,
          propertyId: item.id,
        })) as {
          name: string;
          description: string;
          type: string;
          propertyId: string;
          value: string;
        }[],
      },
      dispatch
    );
    setLoading(false);
    if (response?.status === 200) {
      toggleNewAction();
      const data = response.data.data;
      console.log({ response: data, parentEventId: eventId });

      dispatch(
        emulatorActions.newActionAdded({
          response: data,
          parentEventId: eventId,
        })
      );
    }
  };

  return (
    <Stack gap={2}>
      {selectedAccountDetails?.jsonObjects?.properties.map((item, index) => (
        <>
          <Stack direction={"column"} gap={1}>
            <Stack direction={"row"} alignItems={"center"} gap={1}>
              <Typography>{item.name}</Typography>
              <Tooltip title={item.description}>
                <InfoIcon className="cursor-pointer text-gray-500" />
              </Tooltip>
            </Stack>
            <TextField
              variant="outlined"
              key={"add_json_value" + item.id}
              onChange={(e) => {
                setJsonDetails((prev) =>
                  prev?.map((x) =>
                    x.id === item.id ? { ...x, value: e.target.value } : x
                  )
                );
              }}
            />
          </Stack>
        </>
      ))}
      <div className="text-right">
        <Button variant="contained" onClick={handleSubmit} disabled={loading}>
          Save {loading ? <ButtonSpinner /> : null}
        </Button>
      </div>
    </Stack>
  );
};

export default GenerateJsonAction;
