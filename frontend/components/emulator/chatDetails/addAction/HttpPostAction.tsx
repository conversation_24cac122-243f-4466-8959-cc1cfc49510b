import React, { useState, useEffect } from "react";
import { IValueProp } from "../AddNewAction";
import { IHttpPostAction } from "@/slices/agents/agentsSlice";
import { <PERSON><PERSON>, Stack, TextField, Tooltip, Typography } from "@mui/material";
import InfoIcon from "@mui/icons-material/Info";
import ButtonSpinner from "@/components/general/buttons/spinner";
import { useDispatch } from "react-redux";
import { addHttpPostAction } from "@/requests/sessions/add/addHttpPost";
import { emulatorActions } from "@/slices/emulator/emulatorSlice";

interface IProp {
  sessionId: string;
  eventId: string;
  value: IValueProp;
  toggleNewAction: () => void;
  selectedAccountDetails: IHttpPostAction | undefined;
}

interface IProperty {
  name: string;
  description: string;
  value: string;
}

const HttpPostAction: React.FC<IProp> = ({
  eventId,
  selectedAccountDetails,
  sessionId,
  toggleNewAction,
  value,
}) => {
  const dispatch = useDispatch();
  const [loading, setLoading] = useState(false);
  const [properties, setProperties] = useState<IProperty[]>([]);

  useEffect(() => {
    if (selectedAccountDetails?.httpRequestDetails) {
      const initialProperties = [
        ...(selectedAccountDetails.httpRequestDetails.pathVariables || []),
        ...(selectedAccountDetails.httpRequestDetails.queryParameters || []),
        ...(selectedAccountDetails.httpRequestDetails.bodyParameters || []),
      ]
        .filter((item) => !item.value || item.value.trim() === "")
        .map((item) => ({
          name: item.name,
          description: item.description,
          value: "",
        }));
      setProperties(initialProperties);
    }
  }, [selectedAccountDetails]);

  const handleChange = (name: string, newValue: string) => {
    setProperties((prevProperties) => {
      const index = prevProperties.findIndex((prop) => prop.name === name);
      if (index !== -1) {
        const updatedProperties = [...prevProperties];
        updatedProperties[index] = {
          ...updatedProperties[index],
          value: newValue,
        };
        return updatedProperties;
      }
      return prevProperties;
    });
  };

  const handleSubmit = async () => {
    console.log("Properties to be saved:", properties);
    setLoading(true);
    const response = await addHttpPostAction(
      {
        sessionId,
        eventId,
        accountId: selectedAccountDetails?.accountId as string,
        accountName: selectedAccountDetails?.accountName as string,
        action: "read",
        deleted: false,
        properties,
      },
      dispatch
    );
    setLoading(false);
    if (response?.status === 200) {
      toggleNewAction();
      const data = response.data.data;
      dispatch(
        emulatorActions.newActionAdded({
          response: data,
          parentEventId: eventId,
        })
      );
    }
  };

  const renderFields = (
    items: Array<{ name: string; description: string; value?: string }> = [],
    prefix: string
  ) =>
    items.map((item, index) => {
      if (!item.value || item.value.trim() === "") {
        return (
          <PropertyField
            key={`${prefix}_${eventId}_${index}`}
            item={item}
            onChange={handleChange}
            value={
              properties.find((prop) => prop.name === item.name)?.value || ""
            }
          />
        );
      }
      return null;
    });

  if (!selectedAccountDetails?.httpRequestDetails) {
    return <Typography>No HTTP POST details available</Typography>;
  }

  return (
    <Stack gap={2}>
      <Typography variant="h6">Path variables</Typography>
      {renderFields(
        selectedAccountDetails.httpRequestDetails.pathVariables,
        "path"
      )}
      <Typography variant="h6">Query parameters</Typography>
      {renderFields(
        selectedAccountDetails.httpRequestDetails.queryParameters,
        "query"
      )}
      <Typography variant="h6">Body parameters</Typography>
      {renderFields(
        selectedAccountDetails.httpRequestDetails.bodyParameters,
        "body"
      )}
      <div className="text-right">
        <Button variant="contained" onClick={handleSubmit} disabled={loading}>
          Save {loading ? <ButtonSpinner /> : null}
        </Button>
      </div>
    </Stack>
  );
};

interface IPropertyFieldProps {
  item: { name: string; description: string };
  onChange: (name: string, value: string) => void;
  value: string;
}

const PropertyField: React.FC<IPropertyFieldProps> = ({
  item,
  onChange,
  value,
}) => (
  <Stack direction="column" gap={1}>
    <Stack direction="row" alignItems="center" gap={1}>
      <Typography>{item.name}</Typography>
      <Tooltip title={item.description}>
        <InfoIcon className="cursor-pointer text-gray-500" />
      </Tooltip>
    </Stack>
    <TextField
      variant="outlined"
      value={value}
      onChange={(e) => onChange(item.name, e.target.value)}
    />
  </Stack>
);

export default HttpPostAction; 