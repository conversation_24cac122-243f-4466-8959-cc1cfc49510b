import { IEvent, emulatorActions } from "@/slices/emulator/emulatorSlice";
import {
  GoogleSheetDataSource,
  ISingleIntegration,
} from "@/slices/settings/integrations/integrationList";
import { <PERSON><PERSON>, TextField } from "@mui/material";
import React, { useState, useEffect } from "react";
import { IValueProp } from "../AddNewAction";
import { addGoogleSheetResponse } from "@/requests/sessions/add/addGoogleSheetResponse";
import { useDispatch } from "react-redux";

interface IProp {
  dataSource: GoogleSheetDataSource;
  sessionId: string;
  eventId: string;
  value: IValueProp;
  toggleNewAction: () => void;
}
const GoogleSheetAction: React.FC<IProp> = ({
  dataSource,
  eventId,
  sessionId,
  value,
  toggleNewAction,
}) => {
  const dispatch = useDispatch();
  const [colRange, setColRange] = useState<string>("");
  const [columns, setColumns] = useState<string[]>([]);

  const letterToNumber = (str: string) => {
    let column = 0,
      length = str.length;
    for (let i = 0; i < length; i++) {
      column += (str.charCodeAt(i) - 64) * Math.pow(26, length - i - 1);
    }
    return column;
  };

  const numberToLetter = (num: number) => {
    let columnLetter = "",
      temp;
    while (num > 0) {
      temp = (num - 1) % 26;
      columnLetter = String.fromCharCode(temp + 65) + columnLetter;
      num = (num - temp - 1) / 26;
    }
    return columnLetter;
  };
  useEffect(() => {
    if (dataSource && dataSource.headers) {
      setColRange(dataSource.headers);
    }
  }, [dataSource]);

  useEffect(() => {
    if (colRange) {
      const parts = colRange.split(":");
      if (parts.length !== 2) return;

      const [start, end] = parts.map((cell) =>
        cell.substring(0, cell.length - 1)
      );
      const startNum = letterToNumber(start);
      const endNum = letterToNumber(end);
      const columns = Array.from(
        { length: endNum - startNum + 1 },
        (_, i) => numberToLetter(startNum + i) + "1"
      );
      setColumns(columns);
    }
  }, [colRange]);
  const handleSubmit = async () => {
    toggleNewAction();
    let body = {
      sessionId,
      eventId,
      accountId: value.accountId,
      accountName: value.accountName,
      action: value.activity,
    };
    console.log({ body });
    const response = await addGoogleSheetResponse(body, dispatch);
    if (response?.status === 200) {
      dispatch(
        emulatorActions.newActionAdded({
          response: response.data.data,
          parentEventId: body.eventId,
        })
      );
    }
  };
  if (value.activity === "read") {
    return (
      <>
        <div className="">
          <span className="font-bold">{value.accountName || "No name"}</span>{" "}
          google sheet will be saved
        </div>
        <div className="pt-3 text-right">
          <Button
            variant="contained"
            className="bg-blue-500"
            onClick={handleSubmit}
          >
            Save
          </Button>
        </div>
      </>
    );
  }
  return (
    <div>
      <div className="flex flex-col gap-3">
        {columns.map((column, index) => (
          <TextField
            key={index}
            id={`outlined-basic-${index}`}
            label={column}
            variant="outlined"
          />
        ))}
      </div>
      <div className="pt-3 text-right">
        <Button
          variant="contained"
          className="bg-blue-500"
          onClick={handleSubmit}
        >
          Save
        </Button>
      </div>
    </div>
  );
};

export default GoogleSheetAction;
