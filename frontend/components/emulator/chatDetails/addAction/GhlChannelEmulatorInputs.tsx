import { Button } from "@mui/material";
import React from "react";
import { IValueProp } from "../AddNewAction";
import { useDispatch } from "react-redux";
import { emulatorActions } from "@/slices/emulator/emulatorSlice";
import { addGhlTagResponse } from "@/requests/sessions/add/addGhlTag";

interface IProp {
  sessionId: string;
  eventId: string;
  value: IValueProp;
  toggleNewAction: () => void;
}

const GhlChannelEmulatorInputs: React.FC<IProp> = ({
  sessionId,
  eventId,
  value,
  toggleNewAction,
}) => {
  const dispatch = useDispatch();
  const handleSubmit = async () => {
    const body = {
      sessionId,
      eventId,
      action: "tag",
      accountId: value.accountId,
    };
    console.log({ body });
    const response = await addGhlTagResponse(body, dispatch);
    if (response?.status === 200) {
      dispatch(
        emulatorActions.newActionAdded({
          response: response.data.data,
          parentEventId: body.eventId,
        })
      );
      toggleNewAction();
    }
  };
  return (
    <>
      <div className="">
        <span className="font-bold">{value.accountName || "No name"}</span> GHL
        tag will be added.
      </div>
      <div className="pt-3 text-right">
        <Button
          variant="contained"
          className="bg-blue-500"
          onClick={handleSubmit}
        >
          Save
        </Button>
      </div>
    </>
  );
};

export default GhlChannelEmulatorInputs;
