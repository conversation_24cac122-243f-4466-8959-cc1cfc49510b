import { CommonDataSource } from "@/slices/settings/integrations/integrationList";
import React from "react";
import { IValueProp } from "../AddNewAction";
import { Alert, Button } from "@mui/material";
import { addWebsite } from "@/requests/sessions/add/addWebsite";
import { useDispatch } from "react-redux";
import { emulatorActions } from "@/slices/emulator/emulatorSlice";

interface IProp {
  dataSource: CommonDataSource;
  sessionId: string;
  eventId: string;
  value: IValueProp;
  toggleNewAction: () => void;
}

const WebsiteAction: React.FC<IProp> = ({
  dataSource,
  eventId,
  sessionId,
  toggleNewAction,
  value,
}) => {
  const dispatch = useDispatch();
  const handleSubmit = async () => {
    toggleNewAction();
    let body = {
      sessionId,
      eventId,
      accountId: value.accountId,
      accountName: value.accountName,
      action: value.activity,
    };
    console.log({ body });
    const response = await addWebsite(body, dispatch);
    if (response?.status === 200) {
      dispatch(
        emulatorActions.newActionAdded({
          response: response.data.data,
          parentEventId: body.eventId,
        })
      );
    }
  };
  return (
    <>
      <div className="">
        <span className="font-bold">{value.accountName || "No name"}</span>{" "}
        website will be saved
      </div>
      <Alert severity="info" sx={{ mt: 2 }}>
        Sorry, this functionality is under maintainance
      </Alert>
      <div className="pt-3 text-right">
        <Button
          variant="contained"
          className="bg-blue-500"
          onClick={handleSubmit}
          disabled
        >
          Save
        </Button>
      </div>
    </>
  );
};

export default WebsiteAction;
