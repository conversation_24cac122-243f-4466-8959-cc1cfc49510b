import ButtonSpinner from "@/components/general/buttons/spinner";
import { CONSTANTS } from "@/helpers/constants";
import { addGhlCustomFieldResponse } from "@/requests/sessions/add/addGhlCustomValue";
import { IActionGhlChannel } from "@/slices/agents/agentsSlice";
import { emulatorActions } from "@/slices/emulator/emulatorSlice";
import { Button, Stack, TextField } from "@mui/material";
import React, { useState } from "react";
import { useDispatch } from "react-redux";

interface IProp {
  sessionId: string;
  eventId: string;
  toggleNewAction: () => void;
  selectedAccountDetails: IActionGhlChannel;
}

const GhlChannelCustomFieldEmulatorInputs: React.FC<IProp> = ({
  eventId,
  selectedAccountDetails,
  sessionId,
  toggleNewAction,
}) => {
  const dispatch = useDispatch();
  const [fieldValue, setFieldValue] = useState("");
  const [loading, setLoading] = useState(false);
  const handleSubmit = async () => {
    const body = {
      sessionId,
      eventId,
      accountId: selectedAccountDetails?.accountId as string,
      accountName: selectedAccountDetails?.accountName as string,
      kind: CONSTANTS.PROVIDERS.GHL_CHANNEL,
      action: "customField",
      deleted: false,
      customField: {
        fieldKey: selectedAccountDetails?.accountName || "",
        field_value: fieldValue,
      },
    };
    console.log({ body });
    setLoading(true);
    const response = await addGhlCustomFieldResponse(body, dispatch);
    setLoading(false);
    if (response?.status === 200) {
      toggleNewAction();
      const data = response.data.data;
      console.log({ response: data, parentEventId: eventId });

      dispatch(
        emulatorActions.newActionAdded({
          response: data,
          parentEventId: eventId,
        })
      );
    }
  };
  return (
    <Stack gap={2}>
      <TextField
        variant="outlined"
        label={selectedAccountDetails?.accountName || ""}
        value={fieldValue}
        onChange={(e) => setFieldValue(e.target.value)}
        key="custom_field_add"
      />
      <div className="text-right">
        <Button variant="contained" onClick={handleSubmit} disabled={loading}>
          Save {loading ? <ButtonSpinner /> : null}
        </Button>
      </div>
    </Stack>
  );
};

export default GhlChannelCustomFieldEmulatorInputs;
