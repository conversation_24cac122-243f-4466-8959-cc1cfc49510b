import { IEvent, emulatorActions } from "@/slices/emulator/emulatorSlice";
import { GhlCalendarDataSource } from "@/slices/settings/integrations/integrationList";
import { <PERSON><PERSON>, Button, Stack, Tooltip, Typography } from "@mui/material";
import {
  DesktopDateTimePicker,
  LocalizationProvider,
} from "@mui/x-date-pickers";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import dayjs, { Dayjs } from "dayjs";
import React, { useEffect, useState } from "react";
import { IValueProp } from "../AddNewAction";
import { addGhlCalendarResponse } from "@/requests/sessions/add/addGhlCalendar";
import { useDispatch, useSelector } from "react-redux";
import utc from "dayjs/plugin/utc.js";
import timezone from "dayjs/plugin/timezone.js";
import { RootState } from "@/store";
import AccessTimeIcon from "@mui/icons-material/AccessTime";
import { ImSpinner8 } from "react-icons/im";

dayjs.extend(utc);
dayjs.extend(timezone);
interface IProp {
  dataSource: GhlCalendarDataSource;
  sessionId: string;
  eventId: string;
  value: IValueProp;
  toggleNewAction: () => void;
}

const GhlCalendarAction: React.FC<IProp> = ({
  dataSource,
  sessionId,
  eventId,
  value,
  toggleNewAction,
}) => {
  const dispatch = useDispatch();
  const timezones = useSelector(
    (state: RootState) => state.organization.timezones
  );
  const timezonesLoading = useSelector(
    (state: RootState) => state.organization.timezonesLoading
  );
  const [currentTimezone, setCurrentTimezone] = useState<string>();
  const [startTimevalue, setStartTimevalue] = React.useState<Dayjs | null>(
    null
  );
  const [timezoneError, setTimezoneError] = useState(false);
  const [endTimevalue, setEndTimevalue] = React.useState<Dayjs | null>(null);
  const userTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
  const [loading, setLoading] = useState(false);
  useEffect(() => {
    console.log(
      startTimevalue?.toDate() as Date,
      endTimevalue?.toDate() as Date
    );
  }, [startTimevalue, endTimevalue]);
  useEffect(() => {
    console.log({
      dataSource,
      value,
    });
  }, [dataSource, value]);
  useEffect(() => {
    const accountId = value?.accountId as string;
    const timezone = timezones?.find(
      (timezone) => timezone.accountId === accountId
    );
    if (!!!timezone) {
      setTimezoneError(true);
    }
    // const timezone = dataSource.timezone;
    setCurrentTimezone(timezone?.timezone ?? userTimezone);
  }, [timezones]);

  const handleSubmit = async () => {
    const body = {
      sessionId,
      eventId,
      startDate: startTimevalue?.toISOString() as string,
      endDate: endTimevalue?.toISOString() as string,
      action: value.activity,
      accountId: value.accountId,
      accountName: value.accountName,
    };
    console.log({ body });
    setLoading(true);
    const response = await addGhlCalendarResponse(body, dispatch);
    if (response?.status === 200) {
      toggleNewAction();
      const data = response.data.data;
      dispatch(
        emulatorActions.newActionAdded({
          response: data,
          parentEventId: body.eventId,
        })
      );
    }
    setLoading(false);
  };
  if (timezonesLoading)
    return (
      <div className="flex justify-center">
        <ImSpinner8 className="animate-spin text-[24px]" />
      </div>
    );
  return (
    <div>
      <div className="w-full">
        <div className="">Start time</div>
        <LocalizationProvider dateAdapter={AdapterDayjs}>
          <DesktopDateTimePicker
            sx={{ width: "100%" }}
            label=""
            timezone={currentTimezone}
            value={startTimevalue}
            onChange={(newValue) => setStartTimevalue(newValue)}
          />
        </LocalizationProvider>
        <div className="">End time</div>
        <LocalizationProvider dateAdapter={AdapterDayjs}>
          <DesktopDateTimePicker
            sx={{ width: "100%" }}
            label=""
            value={endTimevalue}
            timezone={currentTimezone}
            onChange={(newValue) => setEndTimevalue(newValue)}
          />
        </LocalizationProvider>
      </div>
      <Tooltip title="Timezone">
        <Stack direction={"row"} gap={1} pt={1}>
          <>
            <AccessTimeIcon fontSize="small" />
            <Typography variant="body2">{currentTimezone}</Typography>
          </>
        </Stack>
      </Tooltip>
      {timezoneError && (
        <Alert severity="warning">
          Couldn&apos;t find a timezone attached to your calendar. Defaulted to
          your current timezone
        </Alert>
      )}
      <div className="pt-3 text-right">
        <Button
          variant="contained"
          className="bg-blue-500"
          onClick={handleSubmit}
          disabled={loading}
        >
          Save
        </Button>
      </div>
    </div>
  );
};

export default GhlCalendarAction;
