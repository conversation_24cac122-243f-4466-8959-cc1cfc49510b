import { Theme<PERSON>rovider } from "@emotion/react";
import {
  Button,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  SelectChangeEvent,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import { IValueProp, darkTheme } from "./AddNewAction";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store";
import { getAgent } from "@/requests/agents/basic/getAgent";
import { ISingleAction } from "@/slices/agents/agentsSlice";
import { GrAddCircle } from "react-icons/gr";
import { IoIosAddCircle } from "react-icons/io";
import { settingsUrl } from "@/slices/subOptions/subOptionSlice";
import { grey } from "@mui/material/colors";

interface IProp {
  accountIdChange: (accountId: string) => void;
  accountNameChange: (accountName: string) => void;
  value: IValueProp;
  accountSelectedFn: (account: ISingleAction) => void;
}

type AccountType = {
  [activity: string]: ISingleAction[];
};

type TransformedDataType = { [provider: string]: AccountType };

const SelectAccount: React.FC<IProp> = ({
  accountIdChange,
  accountNameChange,
  value,
  accountSelectedFn,
}) => {
  const dispatch = useDispatch();
  const agentId = useSelector(
    (state: RootState) => state.emulator.session.basic.agentId
  );
  const [filteredAccounts, setFilteredAccounts] = useState<TransformedDataType>(
    {}
  );
  const [accountId, setAccountId] = useState("");
  const [accounts, setAccounts] = useState<ISingleAction[]>([]);
  const href = ["website"].includes(value.providerName)
    ? settingsUrl + "/data"
    : settingsUrl + "/integrations";
  useEffect(() => {
    if (value.accountId === "") {
      setAccountId("");
    }
  }, [value]);
  const handleChange = (event: SelectChangeEvent) => {
    const accountId = event.target.value as string;
    if (!accountId) {
      return;
    }
    setAccountId(accountId);
    console.log("Here are the values ", { value });

    accountIdChange(accountId);
  };
  useEffect(() => {
    if (value.accountId) {
      const accountName =
        accounts.find((item) => item.accountId === value.accountId)
          ?.accountName || "";
      accountNameChange(accountName);
    }
  }, [value.accountId]);
  useEffect(() => {
    const response = getAgent({ agentId }, dispatch);
    response.then((res) => {
      if (res?.status === 200) {
        setAccounts(res.data.data.actions || []);
        console.log(res.data.data.actions);
      }
    });
  }, []);
  useEffect(() => {
    console.log({ accounts, filteredAccounts, value });
    const transformedData = accounts.reduce(
      (acc: TransformedDataType, account) => {
        if (!acc[account.providerName]) {
          acc[account.providerName] = {
            read: [],
            write: [],
            tag: [],
            customField: [],
            events: [],
          };
        }

        acc[account.providerName][account.activity]?.push(account);
        return acc;
      },
      {}
    );
    console.log({ transformedData });

    setFilteredAccounts(transformedData);
  }, [accounts]);
  useEffect(() => {
    if (accountId) {
      const account = filteredAccounts[value.providerName][value.activity].find(
        (item) => item.accountId === accountId
      );
      accountSelectedFn(account as ISingleAction);
      console.log({ account, accountId });
    }
  }, [accountId]);
  return (
    <div>
      <div className="flex">
        <div className="w-11/12">
          <ThemeProvider theme={darkTheme}>
            <FormControl
              fullWidth
              variant="outlined"
              style={{ color: "#ffffff" }}
            >
              <InputLabel
                id="demo-simple-select-label"
                style={{ color: "#ffffff" }}
              >
                Select account
              </InputLabel>
              <Select
                labelId="demo-simple-select-label"
                id="demo-simple-select"
                value={accountId}
                label="Select account"
                onChange={handleChange}
                style={{ color: "#ffffff", backgroundColor: "#393E46" }}
              >
                {filteredAccounts[value.providerName] &&
                  filteredAccounts[value.providerName][value.activity] &&
                  filteredAccounts[value.providerName][value.activity].map(
                    (item) => (
                      <MenuItem
                        value={item.accountId}
                        key={
                          "select_account_key_" + item.accountId + item.actionId
                        }
                      >
                        {item.accountName || "No name"}
                      </MenuItem>
                    )
                  )}
                {value.providerName && (
                  <MenuItem key={"select_account_key_add"}>
                    <Button
                      variant="text"
                      href={href}
                      target="_self"
                      startIcon={<IoIosAddCircle />}
                      fullWidth
                      sx={{ color: grey[100] }}
                    >
                      Add new resource
                    </Button>
                  </MenuItem>
                )}
              </Select>
            </FormControl>
          </ThemeProvider>
        </div>
      </div>
    </div>
  );
};

export default SelectAccount;
