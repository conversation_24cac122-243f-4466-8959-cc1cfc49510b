import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Button,
  Chip,
  IconButton,
  Typography,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import { AiFillCaretDown, AiFillEdit, AiOutlineDown } from "react-icons/ai";
import GoogleCalendarDetails from "./providerDetails/GoogleCalendarDetails";
import { logos } from "@/helpers/images";
import GhlCalendarDetails from "./providerDetails/GhlCalendarDetails";
import {
  IBotResponse,
  IGenerateJson,
  IGoogleSheet,
  emulatorActions,
} from "@/slices/emulator/emulatorSlice";
import GoogleSheetsDetails from "./providerDetails/GoogleSheetDetails";
import Image from "next/image";
import GeneralModal from "@/components/general/modal";
import GeneralModalContent from "@/components/general/generalModalContent";
import { useRouter } from "next/router";
import { red } from "@mui/material/colors";
import { deleteResponse } from "@/requests/sessions/deleteResponse/deleteResponse";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store";
import Link from "next/link";
import { BiLinkExternal } from "react-icons/bi";
import BasicDetails from "./providerDetails/BasicDetails";
import GhlChannelDetails from "./providerDetails/GhlChannelDetails";
import { CONSTANTS } from "@/helpers/constants";
import GenerateJsonDetails from "./providerDetails/GenerateJsonDetails";
import { restoreResponse } from "@/requests/sessions/restoreResponse/restore";
import HttpGetDetails from "./providerDetails/HttpGetDetails";
import SlackDetails from "./providerDetails/SlackDetails";

interface IProp {
  details: IBotResponse;
  parentEventId: string;
}

export const jsonGenerateDummy: IBotResponse = {
  sender: "bot",
  eventId: crypto.randomUUID(),
  kind: CONSTANTS.PROVIDERS.GENERATE_JSON,
  accountName: "Generate JSON 1",
  accountId: crypto.randomUUID(),
  deleted: false,
  action: "write",
  timestamp: Date.now(),
  eventData: {
    properties: [
      {
        name: "name",
        description: "this is the user name",
        type: "string",
        id: crypto.randomUUID(),
        value: "John",
      },
      {
        name: "email",
        description: "this is user email",
        type: "string",
        id: crypto.randomUUID(),
        value: "<EMAIL>",
      },
    ],
  },
};

const KeyValue: React.FC<IProp> = ({ details, parentEventId }) => {
  const dispatch = useDispatch();
  const sessionData = useSelector((state: RootState) => state.emulator.session);
  const router = useRouter();
  const { sessionId } = router.query as { sessionId: string };
  const [expanded, setExpanded] = useState<string | false>(false);
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    console.log({ sessionData });
  }, [sessionData]);

  const handleChange =
    (panel: string) => (event: React.SyntheticEvent, isExpanded: boolean) => {
      setExpanded(isExpanded ? panel : false);
    };

  const handleClose = () => {
    setOpen(false);
  };
  const handleDelete = async () => {
    try {
      console.log({ sessionId, eventId: details.eventId });
      setLoading(true);
      const response = await deleteResponse(
        {
          sessionId,
          eventId: details.eventId,
        },
        dispatch
      );
      setLoading(false);
      handleClose();
      if (response?.status === 200) {
        dispatch(
          emulatorActions.botResponseUpdated({
            parentEventId,
            response: {
              ...details,
              deleted: true,
            },
          })
        );
        dispatch(
          emulatorActions.getResponseDetails({ eventId: parentEventId })
        );
      }
    } catch (error) {
      console.log(error);
    }
  };

  const handleRestore = async () => {
    try {
      console.log({ sessionId, eventId: details.eventId });
      setLoading(true);
      const response = await restoreResponse(
        {
          sessionId,
          eventId: details.eventId,
        },
        dispatch
      );
      setLoading(false);
      handleClose();
      if (true || response?.status === 200) {
        dispatch(
          emulatorActions.botResponseUpdated({
            parentEventId,
            response: {
              ...details,
              deleted: false,
            },
          })
        );
        dispatch(
          emulatorActions.getResponseDetails({ eventId: parentEventId })
        );
      }
    } catch (error) {
      console.log(error);
    }
  };

  if (details.kind === "message") return null;
  return (
    <>
      <GeneralModal open={open} onClose={handleClose}>
        <GeneralModalContent
          header="Warning"
          content={
            details?.deleted
              ? "Do you want to restore this action?"
              : "Are you sure you want to delete this action?"
          }
          rightBtn={details?.deleted ? "Restore" : "Delete"}
          isLoading={loading}
          onClose={handleClose}
          customFn={details?.deleted ? handleRestore : handleDelete}
        />
      </GeneralModal>
      <Accordion
        expanded={expanded === "panel1"}
        onChange={handleChange("panel1")}
        sx={{ backgroundColor: details.deleted ? red[200] : "" }}
      >
        <AccordionSummary
          expandIcon={<AiFillCaretDown />}
          aria-controls="panel1bh-content"
          id="panel1bh-header"
        >
          <div className="flex w-full">
            <div className="w-8/12 flex">
              <div className="w-[50px]">
                <Image
                  alt={logos[details.kind]?.name || "No name"}
                  width={100}
                  height={100}
                  title={logos[details.kind]?.name || "No name"}
                  src={"/squareLogos" + logos[details.kind]?.logo || ""}
                  className="h-[30px] w-auto"
                />
              </div>
              <Typography sx={{ width: "60%", flexShrink: 0 }}>
                {/* {logos[details.kind]?.name} */}
                {details.accountName || "No name"}
              </Typography>
            </div>
            <div className="w-4/12 text-center flex justify-end gap-2 mx-1">
              {details.reference && (
                <Link href={details.reference} target="_blank">
                  <Chip
                    label={"Details"}
                    color="warning"
                    variant="outlined"
                    icon={<BiLinkExternal />}
                    sx={{ cursor: "pointer" }}
                  />
                </Link>
              )}
              {details?.deleted && (
                <Chip
                  label={details.deleted ? "Deleted" : ""}
                  color="error"
                  variant="filled"
                />
              )}
              <Chip label={details.action} color="primary" variant="outlined" />
            </div>
          </div>
        </AccordionSummary>
        <AccordionDetails>
          {details.kind === "googleCalendar" && (
            <GoogleCalendarDetails
              parentEventId={parentEventId}
              details={details}
              key={"dateTimeKeyGoogleCalendar"}
              handleOpen={() => setOpen(true)}
            />
          )}
          {details.kind === "ghlCalendar" && (
            <GhlCalendarDetails
              parentEventId={parentEventId}
              details={details}
              key={"dateTimeKeyGhlCalendar"}
              handleOpen={() => setOpen(true)}
            />
          )}
          {details.kind === "googleSheet" && (
            <GoogleSheetsDetails
              parentEventId={parentEventId}
              details={details}
              key={"googleSheetKey"}
              handleOpen={() => setOpen(true)}
            />
          )}
          {details.kind === CONSTANTS.PROVIDERS.GHL_CHANNEL && (
            <GhlChannelDetails
              parentEventId={parentEventId}
              details={details}
              key={"ghlChannelKey"}
              handleOpen={() => setOpen(true)}
            />
          )}
          {details.kind === CONSTANTS.PROVIDERS.GENERATE_JSON && (
            <GenerateJsonDetails
              parentEventId={parentEventId}
              key={"generateJsonKey"}
              details={details}
              handleOpen={() => setOpen(true)}
            />
          )}
          {details.kind === CONSTANTS.PROVIDERS.HTTP_GET && (
            <HttpGetDetails
              parentEventId={parentEventId}
              key={"httpGetKey"}
              details={details}
              handleOpen={() => setOpen(true)}
            />
          )}
          {details.kind === CONSTANTS.PROVIDERS.SLACK && (
            <SlackDetails
              parentEventId={parentEventId}
              key={"slackKey"}
              details={details}
              handleOpen={() => setOpen(true)}
            />
          )}
          {["website", CONSTANTS.PROVIDERS.GOOGLE_DOCS].includes(
            details.kind
          ) && (
            <BasicDetails
              parentEventId={parentEventId}
              details={details}
              handleOpen={() => setOpen(true)}
            />
          )}
          {}
        </AccordionDetails>
      </Accordion>
    </>
  );
};

export default KeyValue;
