import { ThemeProvider } from "@emotion/react";
import {
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  SelectChangeEvent,
} from "@mui/material";
import React, { useState } from "react";
import { darkTheme } from "./AddNewAction";
import { useSelector } from "react-redux";
import { RootState } from "@/store";

interface IProp {
  onChangeFn: (providerName: string) => void;
}

const SelectProvider: React.FC<IProp> = ({ onChangeFn }) => {
  const integrationProviders = useSelector(
    (state: RootState) => state.integrationList.integrationProvidersList
  );
  let emulatorResourceList = useSelector(
    (state: RootState) => state.emulator.resourceList
  );
  const sortedEmulatorResourceList = emulatorResourceList
    ? [...emulatorResourceList].sort((a, b) => {
        if (a.name < b.name) return -1;
        if (a.name > b.name) return 1;
        return 0;
      })
    : [];
  const [currentProvider, setCurrentProvider] = useState("");

  const handleChange = (event: SelectChangeEvent) => {
    // setAge(event.target.value as string);
    setCurrentProvider(event.target.value as string);
    onChangeFn(event.target.value as string);
    console.log(event.target.value as string);
  };
  return (
    <div className="w-11/12">
      <ThemeProvider theme={darkTheme}>
        <FormControl fullWidth variant="outlined" style={{ color: "#ffffff" }}>
          <InputLabel
            id="demo-simple-select-label"
            style={{ color: "#ffffff" }}
          >
            Select provider
          </InputLabel>
          <Select
            labelId="demo-simple-select-label"
            id="demo-simple-select"
            value={currentProvider}
            label="Select provider"
            onChange={handleChange}
            style={{ color: "#ffffff", backgroundColor: "#393E46" }}
          >
            {sortedEmulatorResourceList.map((item) => (
              <MenuItem
                value={item.providerId}
                key={"provider_KEY_" + item.providerId}
              >
                {item.name}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </ThemeProvider>
    </div>
  );
};

export default SelectProvider;
