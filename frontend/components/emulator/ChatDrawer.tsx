import React, { useState, useRef, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import Link from "next/link";
import {
  Drawer,
  Box,
  Typography,
  IconButton,
  TextField,
  Button,
  Paper,
  Stack,
  Avatar,
  CircularProgress,
  Skeleton,
  Chip,
} from "@mui/material";
import {
  Launch as LaunchIcon,
  RestartAlt as RestartIcon,
  Send as SendIcon,
} from "@mui/icons-material";
import { v4 as uuidv4 } from "uuid";
import {
  IBotMessage,
  IBotResponse,
  IEvent,
  emulatorActions,
} from "@/slices/emulator/emulatorSlice";
import { sendMessage } from "@/requests/sessions/chat/sendMessage";
import { createNewSession } from "@/requests/sessions/basic/createNewSession";
import { StatusCard } from "./StatusCard";
import CloseIcon from "@mui/icons-material/Close";
import { TrainingStatusIndicator } from "../agents/TrainingStatusIndicator";
import { IAgent } from "@/slices/agents/agentsSlice";
import { RootState } from "@/store";

interface ChatDrawerProps {
  agentId: string;
  open: boolean;
  onClose: () => void;
  chooseAgentAutocomplete?: React.ReactNode;
}

interface MessageDisplayProps {
  message: string;
  sender: "human" | "bot";
  timestamp: number;
}

const PRESET_MESSAGES = [
  "Hello, who is this?",
  "What can you help me with?",
  "Tell me about yourself",
];

const MessageSkeleton: React.FC<{ align: "left" | "right" }> = ({ align }) => (
  <Box
    sx={{
      display: "flex",
      justifyContent: align === "right" ? "flex-end" : "flex-start",
      mb: 2,
    }}
  >
    <Box
      sx={{
        display: "flex",
        gap: 1,
        maxWidth: "70%",
        flexDirection: align === "right" ? "row-reverse" : "row",
      }}
    >
      <Skeleton variant="circular" width={32} height={32} />
      <Box sx={{ maxWidth: 300 }}>
        <Skeleton variant="rounded" width={200} height={60} />
        <Skeleton variant="text" width={100} sx={{ mt: 0.5 }} />
      </Box>
    </Box>
  </Box>
);

const MessageDisplay: React.FC<MessageDisplayProps> = ({
  message,
  sender,
  timestamp,
}) => (
  <Box
    sx={{
      display: "flex",
      justifyContent: sender === "human" ? "flex-end" : "flex-start",
      mb: 2,
    }}
  >
    <Box
      sx={{
        display: "flex",
        gap: 1,
        maxWidth: "70%",
        flexDirection: sender === "human" ? "row-reverse" : "row",
      }}
    >
      <Avatar
        sx={{
          bgcolor: sender === "human" ? "primary.main" : "secondary.main",
          width: 32,
          height: 32,
        }}
      >
        {sender === "human" ? "U" : "B"}
      </Avatar>
      <Box>
        <Paper
          elevation={1}
          sx={{
            p: 1.5,
            borderRadius: 2,
            bgcolor: sender === "human" ? "primary.light" : "grey.100",
            color: sender === "human" ? "white" : "text.primary",
          }}
        >
          <Typography variant="body1">{message}</Typography>
        </Paper>
        <Typography
          variant="caption"
          sx={{
            mt: 0.5,
            display: "block",
            textAlign: sender === "human" ? "right" : "left",
            color: "text.secondary",
          }}
        >
          {new Date(timestamp).toLocaleTimeString()}
        </Typography>
      </Box>
    </Box>
  </Box>
);

export const ChatDrawer: React.FC<ChatDrawerProps> = ({
  agentId,
  open,
  onClose,
  chooseAgentAutocomplete,
}) => {
  const dispatch = useDispatch();
  const [isStartingNewSession, setIsStartingNewSession] = useState(false);
  const [isMessageSending, setIsMessageSending] = useState(false);
  const currentAgent: IAgent = useSelector(
    (state: RootState) => state.agents.currentAgent
  );
  const [message, setMessage] = useState("");
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const session = useSelector((state: any) => state.emulator.session);
  const events = session.events;

  const orgId = localStorage.getItem("orgId") as string;

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [events]);

  const createNewSessionRoute = async () => {
    setIsStartingNewSession(true);
    try {
      const response = await createNewSession({ orgId, agentId }, dispatch);
      if (response.status === 201) {
        const data = response.data.data;
        dispatch(emulatorActions.sessionFetched(data));
      }
    } catch (error) {
      console.error("Failed to create new session:", error);
    } finally {
      setIsStartingNewSession(false);
    }
  };

  useEffect(() => {
    if (agentId) {
      createNewSessionRoute();
    }
  }, [agentId]); // Added agentId dependency

  const handleSend = async (customMessage?: string) => {
    const messageToSend = customMessage || message.trim();
    const eventId = uuidv4();
    const newEvent: IEvent = {
      eventId,
      sender: "human",
      message: messageToSend,
      timestamp: Date.now(),
    };

    dispatch(emulatorActions.userMessageAdded(newEvent));
    // Only clear the input field if we're using the state message
    if (!customMessage) {
      setMessage("");
    }

    try {
      setIsMessageSending(true);
      const response = await sendMessage(
        {
          query: messageToSend,
          sessionId: session._id as string,
        },
        dispatch
      );
      if (response?.status === 200) {
        dispatch(
          emulatorActions.botResponseFetched({
            response: response.data.data,
            eventId,
          })
        );
      }
    } catch (error) {
      console.error("Failed to send message:", error);
      dispatch(emulatorActions.errorOccured({ eventId }));
    } finally {
      setIsMessageSending(false);
    }
  };

  const handleKeyPress = async (event: React.KeyboardEvent) => {
    if (event.key === "Enter" && !event.shiftKey) {
      event.preventDefault();
      if (message.trim()) {
        // Only send if there is a message
        await handleSend();
      }
    }
  };

  const handleRestart = () => {
    createNewSessionRoute();
  };

  const handlePresetMessage = async (preset: string) => {
    await handleSend(preset); // Automatically send the message
  };

  const handleSendBtnClick = async () => {
    if (message.trim()) {
      await handleSend();
    }
  };

  const renderContent = () => {
    if (!agentId) {
      return <StatusCard type="no-agent" />;
    }

    if (isStartingNewSession) {
      return <StatusCard type="loading" />;
    }

    if (events.length === 0) {
      return <StatusCard type="no-messages" />;
    }

    return (
      <>
        {events.map((event: IEvent) => (
          <React.Fragment key={event.eventId}>
            <MessageDisplay
              message={event.message}
              sender={event.sender}
              timestamp={event.timestamp}
            />
            {event.botResponse?.map((response: IBotResponse) => {
              const botMessage = response.eventData as IBotMessage;
              if (response.kind === "message" && botMessage.message) {
                return (
                  <MessageDisplay
                    key={response.eventId}
                    message={botMessage.message}
                    sender="bot"
                    timestamp={response.timestamp}
                  />
                );
              }
              return null;
            })}
          </React.Fragment>
        ))}
        {isMessageSending && <MessageSkeleton align="left" />}
        <div ref={messagesEndRef} />
      </>
    );
  };

  return (
    <Drawer
      anchor="right"
      open={open}
      onClose={onClose}
      PaperProps={{
        sx: { width: { xs: "100%", sm: 800 }, maxWidth: "100%" },
      }}
    >
      <Box sx={{ display: "flex", flexDirection: "column", height: "100%" }}>
        {/* Header */}
        <Box
          sx={{
            p: 2,
            borderBottom: 1,
            borderColor: "divider",
            display: "flex",
            alignItems: "center",
            gap: 2,
          }}
        >
          <Stack
            direction="row"
            spacing={2}
            alignItems="center"
            sx={{ flex: 1 }}
          >
            <IconButton
              onClick={onClose}
              size="small"
              title="Close conversation"
            >
              <CloseIcon />
            </IconButton>
            <Typography variant="h6">Chat</Typography>
          </Stack>
          <Stack direction="row" alignItems="center" spacing={1}>
            {chooseAgentAutocomplete && (
              <Box sx={{ flex: 1 }}>{chooseAgentAutocomplete}</Box>
            )}
            {!chooseAgentAutocomplete && (
              <TrainingStatusIndicator
                agent={{
                  _id: currentAgent._id,
                  name: currentAgent.agentName,
                  isTraining: currentAgent.processingJobs?.length > 0,
                  activeTraining: currentAgent.processingJobs?.map((job) => ({
                    startedAt: new Date(),
                    status: "in_progress",
                    type: job.jobType,
                    details: `${job.fileName} is being processed.`,
                  })),
                }}
                onRetry={() => {}}
              />
            )}
            <Link
              href={`/home/<USER>/${session._id}`}
              target="_blank"
              style={{ textDecoration: "none" }}
            >
              <IconButton size="small" title="Open in playground.">
                <LaunchIcon />
              </IconButton>
            </Link>
            <IconButton
              onClick={handleRestart}
              size="small"
              title="Restart conversation"
              disabled={isStartingNewSession}
            >
              <RestartIcon />
            </IconButton>
          </Stack>
        </Box>

        {/* Messages */}
        <Box
          sx={{
            flexGrow: 1,
            p: 2,
            overflowY: "auto",
            bgcolor: "grey.50",
          }}
        >
          {renderContent()}
        </Box>

        {/* Input */}
        <Box
          sx={{
            p: 2,
            borderTop: 1,
            borderColor: "divider",
            bgcolor: "background.paper",
          }}
        >
          <Stack spacing={1}>
            {/* Only show preset messages when there are no events (new conversation) */}
            {events.length === 0 && (
              <Stack
                direction="row"
                spacing={1}
                sx={{ overflowX: "auto", pb: 1 }}
              >
                {PRESET_MESSAGES.map((preset) => (
                  <Chip
                    key={preset}
                    label={preset}
                    onClick={() => handlePresetMessage(preset)}
                    variant="outlined"
                    sx={{ cursor: "pointer" }}
                    disabled={isMessageSending || !agentId}
                  />
                ))}
              </Stack>
            )}
            <Stack direction="row" spacing={1}>
              <TextField
                fullWidth
                multiline
                maxRows={4}
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="Type your message..."
                size="small"
                variant="outlined"
                disabled={isMessageSending || !agentId}
              />
              <Button
                variant="contained"
                onClick={handleSendBtnClick}
                disabled={!message.trim() || isMessageSending || !agentId}
                sx={{ minWidth: 100 }}
                endIcon={
                  isMessageSending ? (
                    <CircularProgress size={20} />
                  ) : (
                    <SendIcon />
                  )
                }
              >
                {isMessageSending ? "Sending" : "Send"}
              </Button>
            </Stack>
          </Stack>
        </Box>
      </Box>
    </Drawer>
  );
};

export default ChatDrawer;
