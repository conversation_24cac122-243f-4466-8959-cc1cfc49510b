import SnackBar from "@/components/general/snackbar";
import { generateUniqueId } from "@/helpers/generateId";
import { sendMessage } from "@/requests/sessions/chat/sendMessage";
import { emulatorActions } from "@/slices/emulator/emulatorSlice";
import { Box, Button, Menu, MenuItem, TextField } from "@mui/material";
import { useRouter } from "next/router";
import React, { FC, useState, KeyboardEvent, ChangeEvent } from "react";
import { AiOutlineSend } from "react-icons/ai";
import { useDispatch } from "react-redux";
import KeyboardArrowDownIcon from "@mui/icons-material/KeyboardArrowDown";
import { blue } from "@mui/material/colors";
import SmartToyIcon from "@mui/icons-material/SmartToy";
import ButtonSpinner from "@/components/general/buttons/spinner";
import { snackbarActions } from "@/slices/general/snackbar";

const MessageBar: FC = () => {
  const dispatch = useDispatch();
  const router = useRouter();
  const { sessionId } = router.query;
  const [message, setMessage] = useState<string>("");
  const [loading, setLoading] = useState(false);
  const handleSendMessage = async () => {
    // put your send message code here
    const messageReady = message;
    if (messageReady === "") {
      dispatch(
        snackbarActions.setSnackbar({
          message: "Cannot send empty message",
          type: "warning",
        })
      );
      return;
    }
    setMessage("");
    const eventId = generateUniqueId(6);
    const timestamp = Math.floor(Date.now() / 1000);
    console.log(message);
    dispatch(
      emulatorActions.userMessageAdded({
        eventId,
        timestamp,
        message: messageReady,
        sender: "human",
      })
    );
    try {
      const response = await sendMessage(
        {
          query: messageReady,
          sessionId: sessionId as string,
        },
        dispatch
      );
      if (response?.status === 200) {
        dispatch(
          emulatorActions.botResponseFetched({
            response: response.data.data,
            eventId,
          })
        );
      }
    } catch (error: any) {
      console.log({ error });
      dispatch(emulatorActions.errorOccured({ eventId }));
    }
  };

  const handleKeyDown = (event: KeyboardEvent<HTMLInputElement>) => {
    if (event.key === "Enter" && !event.shiftKey) {
      event.preventDefault(); // Prevent form submission
      handleSendMessage();
    }
  };

  const handleChange = (event: ChangeEvent<HTMLInputElement>) => {
    setMessage(event.target.value);
  };
  const [anchorEl, setAnchorEl] = useState(null);

  const handleClick = (event: any) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleSendAsBot = async () => {
    if (message === "") {
      dispatch(
        snackbarActions.setSnackbar({
          message: "Cannot send empty message",
          type: "warning",
        })
      );
      return;
    }
    const messageReady = message;
    setMessage("");
    setLoading(true);
    const response = await sendMessage(
      {
        query: messageReady,
        sessionId: sessionId as string,
        sender: "bot",
      },
      dispatch
    );
    setLoading(false);
    if (response?.status === 200) {
      dispatch(emulatorActions.sendAsBot(response.data.data));
    }
    handleClose();
  };

  return (
    <>
      <Box sx={{ display: "flex", alignItems: "center", gap: 1, my: 1 }}>
        <TextField
          fullWidth
          variant="outlined"
          placeholder="Type your message..."
          value={message}
          onChange={handleChange}
          onKeyDown={handleKeyDown}
          multiline
          minRows={1}
          maxRows={8} // Limit the number of lines to 8
          sx={{
            bgcolor: "white",
            flex: 1, // Allow it to take up available space
            height: "auto", // Adjust height to auto for multiline TextField
            "& .MuiOutlinedInput-root": {
              // Targeting the root to adjust height
              height: "100%",
            },
          }}
        />

        <div className="flex">
          <Button
            variant="contained"
            endIcon={<AiOutlineSend />}
            sx={{
              width: "120px",
              borderTopRightRadius: 0,
              borderBottomRightRadius: 0,
              height: 50, // Set the same height as the TextField
              bgcolor: "linear-gradient(to bottom right, third, purple.700)",
            }}
            onClick={handleSendMessage}
            id="emulatorSend_btn"
          >
            Send
          </Button>
          <Box
            onClick={handleClick}
            sx={{
              bgcolor: blue[700],
              display: "flex",
              alignItems: "center",
              borderTopRightRadius: 4,
              borderBottomRightRadius: 4,
              cursor: "pointer",
              "&:hover": {
                bgcolor: blue[800],
              },
            }}
          >
            <KeyboardArrowDownIcon />
          </Box>
          <Menu
            anchorEl={anchorEl}
            open={Boolean(anchorEl)}
            onClose={handleClose}
            anchorOrigin={{
              vertical: "top",
              horizontal: "right",
            }}
            transformOrigin={{
              vertical: "bottom",
              horizontal: "right",
            }}
          >
            <MenuItem onClick={handleSendAsBot} disabled={loading}>
              Send as bot{" "}
              <span className="flex items-cente mx-2">
                <SmartToyIcon />
              </span>
              {loading && <ButtonSpinner />}
            </MenuItem>
          </Menu>
        </div>
      </Box>
    </>
  );
};

export default MessageBar;
