import React from "react";
import ChatHeader from "./chatHeader";
import ChatContent from "./chatContent";
import MessageBar from "./messageBar";
import { useSelector } from "react-redux";
import { RootState } from "@/store";

const ChatWindow = () => {
  const detailOpened = useSelector(
    (state: RootState) => state.emulator.detailsTabOpened
  );
  return (
    <div
      className={`flex flex-col h-[calc(100vh-60px)] px-3 ${
        detailOpened &&
        "border-r-[1px] border-y-0 border-l-0 border-solid border-r-white"
      } `}
    >
      <ChatHeader />
      <ChatContent />
      <MessageBar />
    </div>
  );
};

export default ChatWindow;
