import React, { useEffect, useRef } from "react";
import Message from "./message";
import { useSelector } from "react-redux";
import { RootState } from "@/store";
import { IBotMessage } from "@/slices/emulator/emulatorSlice";
import TypingAnimation from "@/components/general/effects/TypingAnimation";
import MessageLoading from "./MessageLoading";
import NoChat from "./NoChat";

const ChatContent = () => {
  const events = useSelector(
    (state: RootState) => state.emulator?.session?.events
  );
  const messagesEndRef = useRef<HTMLDivElement | null>(null);
  const scrollToBottom = () => {
    // Scroll the last message into view
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };
  useEffect(() => {
    scrollToBottom();
  }, [events]);
  return (
    <div className="flex-grow mt-2 overflow-y-auto">
      {events.length === 0 && <NoChat />}
      {events?.map((singleEvent, index) => (
        <div key={index}>
          <Message
            by={singleEvent.sender}
            message={singleEvent?.message}
            eventId={singleEvent.eventId}
            key={singleEvent.eventId}
            failed={singleEvent?.error || false}
          />
          {singleEvent?.botResponse?.map((botResponse, responseIndex) =>
            botResponse.kind === "message" ? (
              <Message
                key={`${index}-${responseIndex}`}
                by={botResponse.sender}
                message={(botResponse.eventData as IBotMessage)?.message}
                eventId={singleEvent.eventId}
                failed={false}
              />
            ) : null
          )}
          {singleEvent.botResponse == undefined &&
            singleEvent?.error !== true && <MessageLoading />}
        </div>
      ))}
      <div ref={messagesEndRef} />
    </div>
  );
};

export default ChatContent;
