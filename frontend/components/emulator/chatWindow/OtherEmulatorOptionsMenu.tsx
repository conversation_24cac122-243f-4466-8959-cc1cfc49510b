import * as React from "react";
import { styled, alpha } from "@mui/material/styles";
import Button from "@mui/material/Button";
import Menu, { MenuProps } from "@mui/material/Menu";
import MenuItem from "@mui/material/MenuItem";
import SaveIcon from "@mui/icons-material/Save";
import NoteAddIcon from "@mui/icons-material/NoteAdd";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store";
import { saveSession } from "@/requests/sessions/chat/saveSession";
import { useRouter } from "next/router";
import { emulatorActions } from "@/slices/emulator/emulatorSlice";
import { unsaveSession } from "@/requests/sessions/chat/unsaveSession";
import { handleNewSession } from "../selectAgent/SingleAgent";
import DeleteIcon from "@mui/icons-material/Delete";
import GeneralModal from "@/components/general/modal";
import GeneralModalContent from "@/components/general/generalModalContent";
import { deleteSession } from "@/requests/sessions/basic/deleteSession";
import { emulatorUrl } from "@/slices/subOptions/subOptionSlice";
import { duplicateSession } from "@/requests/sessions/basic/duplicateSession";
import FileCopyIcon from "@mui/icons-material/FileCopy";

export const StyledMenu = styled((props: MenuProps) => (
  <Menu
    elevation={0}
    anchorOrigin={{
      vertical: "bottom",
      horizontal: "right",
    }}
    transformOrigin={{
      vertical: "top",
      horizontal: "right",
    }}
    {...props}
  />
))(({ theme }) => ({
  "& .MuiPaper-root": {
    borderRadius: 6,
    marginTop: theme.spacing(1),
    minWidth: 180,
    color:
      theme.palette.mode === "light"
        ? "rgb(55, 65, 81)"
        : theme.palette.grey[300],
    boxShadow:
      "rgb(255, 255, 255) 0px 0px 0px 0px, rgba(0, 0, 0, 0.05) 0px 0px 0px 1px, rgba(0, 0, 0, 0.1) 0px 10px 15px -3px, rgba(0, 0, 0, 0.05) 0px 4px 6px -2px",
    "& .MuiMenu-list": {
      padding: "4px 0",
    },
    "& .MuiMenuItem-root": {
      "& .MuiSvgIcon-root": {
        fontSize: 18,
        color: theme.palette.text.secondary,
        marginRight: theme.spacing(1.5),
      },
      "&:active": {
        backgroundColor: alpha(
          theme.palette.primary.main,
          theme.palette.action.selectedOpacity
        ),
      },
    },
  },
}));

interface IProp {
  handleClose: () => void;
}

export default function OtherEmulatorOptionsMenu({ handleClose }: IProp) {
  const dispatch = useDispatch();
  const router = useRouter();
  // const userId = useSelector((state: RootState) => state.profile.userId);
  const agentId = useSelector(
    (state: RootState) => state.emulator.session.basic.agentId
  );
  const { sessionId } = router.query as { sessionId: string };
  const saved = useSelector((state: RootState) => state.emulator.session.saved);
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);
  const [modalOpen, setModalOpen] = React.useState(false);
  const [loading, setLoading] = React.useState(false);
  const orgId = localStorage.getItem("orgId") ?? "";
  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };
  //   const handleClose = () => {
  //     setAnchorEl(null);
  //   };
  const handleSave = async () => {
    handleClose();
    const response = await saveSession(
      { sessionId: sessionId as string },
      dispatch
    );
    if (response?.status === 200) {
      dispatch(emulatorActions.sessionSaved(true));
    }
  };
  const handleUnsave = async () => {
    handleClose();
    const response = await unsaveSession(
      { sessionId: sessionId as string },
      dispatch
    );
    if (response?.status === 200) {
      dispatch(emulatorActions.sessionSaved(false));
    }
  };

  const handleNewSessionCreate = async () => {
    handleClose();
    await handleNewSession({ agentId, router, dispatch });
  };

  const handleSessionDelete = async () => {
    const response = await deleteSession({ sessionId }, dispatch);
    if (response?.status === 200) {
      router.push(process.env.NEXT_PUBLIC_FRONTEND_URL + emulatorUrl);
    }
  };

  const handleDuplicateSession = async () => {
    handleClose();
    const response = await duplicateSession(
      { sessionId, orgId, agentId },
      dispatch
    );
    if (response?.status === 201) {
      const route =
        process.env.NEXT_PUBLIC_FRONTEND_URL +
        "/home/<USER>/" +
        response.data.data._id.toString();
      window.open(route, "_blank");
    }
  };

  return (
    <>
      <GeneralModal
        open={modalOpen}
        onClose={() => {
          setModalOpen(false);
        }}
      >
        <GeneralModalContent
          header="Warning"
          content="Are you sure you want to delete this session?"
          rightBtn="Delete"
          isLoading={loading}
          onClose={handleClose}
          customFn={handleSessionDelete}
        />
      </GeneralModal>
      <div>
        <MenuItem
          onClick={saved ? handleUnsave : handleSave}
          id="emulatorSessionSaved_btn"
          disableRipple
        >
          <SaveIcon />
          {saved ? "Unsave session" : "Save session"}
        </MenuItem>
        <MenuItem onClick={handleNewSessionCreate} disableRipple>
          <NoteAddIcon />
          New session
        </MenuItem>
        <MenuItem onClick={handleDuplicateSession} disableRipple>
          <FileCopyIcon />
          Duplicate session
        </MenuItem>
        <MenuItem onClick={() => setModalOpen(true)} disableRipple>
          <DeleteIcon />
          Delete session
        </MenuItem>
      </div>
    </>
  );
}
