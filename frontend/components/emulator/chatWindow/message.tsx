import React, { FC, useEffect } from "react";
import Icon from "./icon";
import MessageBubble from "./messageBubble";
import { BsInfoLg } from "react-icons/bs";
import { useDispatch, useSelector } from "react-redux";
import { emulatorActions } from "@/slices/emulator/emulatorSlice";
import { Tooltip, Typography } from "@mui/material";
import { RootState } from "@/store";
import DeleteMessage from "./DeleteMessage";
import { useRouter } from "next/router";

interface messageProps {
  by: string;
  message: string;
  eventId: string;
  failed: boolean;
}

const Message: FC<messageProps> = ({
  by,
  message,
  eventId,
  failed = false,
}) => {
  const dispatch = useDispatch();
  const router = useRouter();
  const sessionId = router.query.sessionId as string;
  const sessionData = useSelector((state: RootState) => state.emulator.session);
  // useEffect(() => {
  //   dispatch(emulatorActions.getResponseDetails({ eventId }));
  // }, [sessionData]);
  if (message === "") return null;
  return (
    <>
      {by === "bot" && (
        <>
          <div className="flex my-3 ml-4 gap-x-4 justify-start">
            <Icon by={by} />
            <MessageBubble message={message} by={by} />
            <Tooltip title="Response info">
              <div
                className="w-[33px] h-[33px] bg-gradient-to-br from-[#616161] to-[#9e9e9e] p-2 cursor-pointer rounded-full hover:scale-[1.1] transition-all"
                onClick={() => {
                  console.log(
                    "Will get all the bot events associated with this eventId ",
                    eventId
                  );

                  dispatch(emulatorActions.toggleDetailsTabOpened(true));
                  dispatch(emulatorActions.getResponseDetails({ eventId }));
                }}
              >
                <BsInfoLg className="" />
              </div>
            </Tooltip>
          </div>
        </>
      )}
      {by === "human" && (
        <>
          {message && (
            <div className="flex my-3 mr-4 gap-x-4 justify-end">
              <DeleteMessage eventId={eventId} sessionId={sessionId} />
              <div className="flex item-center gap-3">
                {failed && (
                  <Typography
                    variant="body1"
                    color={"error"}
                    sx={{ display: "flex", alignItems: "center" }}
                  >
                    Failed
                  </Typography>
                )}
                <MessageBubble message={message} by={by} />
              </div>
              <Icon by={by} />
            </div>
          )}
        </>
      )}
    </>
  );
};

export default Message;
