import { Card, CardContent, Typography, useTheme } from "@mui/material";
import { grey, indigo } from "@mui/material/colors";
import React, { FC } from "react";

interface messageProps {
  by: string;
  message: string;
}

const MessageBubble: FC<messageProps> = ({ by, message }) => {
  const theme = useTheme();

  const userStyles = {
    background: `linear-gradient(to bottom right, ${indigo[600]}, ${indigo[500]})`,
    color: theme.palette.common.white,
  };

  const botStyles = {
    background: `linear-gradient(to bottom right, ${grey[700]}, ${grey[500]})`,
    color: theme.palette.common.white,
  };

  const style = by === "human" ? userStyles : botStyles;
  const messageWithLineBreaks = message?.split("\n").map((line, index) => (
    <React.Fragment key={index}>
      {line}
      <br />
    </React.Fragment>
  ));
  return (
    <>
      <Card
        sx={{
          maxWidth: 500,
          borderRadius: 3,
          py: 0,
          marginBottom: 2,
          marginLeft: by === "human" ? "auto" : 0,
          ...style,
        }}
      >
        <CardContent sx={{ "&:last-child": { paddingBottom: 2 }, py: 2 }}>
          <Typography variant="body1">{messageWithLineBreaks}</Typography>
        </CardContent>
      </Card>
    </>
  );
};

export default MessageBubble;
