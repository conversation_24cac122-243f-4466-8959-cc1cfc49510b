import React, { useEffect, useState } from "react";
import { BsThreeDotsVertical } from "react-icons/bs";
import { FiExternalLink, FiSettings } from "react-icons/fi";
import { IoReloadSharp } from "react-icons/io5";
import OtherEmulatorOptions from "./otherEmulatorOptions";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store";
import { RxCross1 } from "react-icons/rx";
import Tooltip from "@mui/material/Tooltip";
import { Chip, IconButton, TextField } from "@mui/material";
import { AiOutlineCheck, AiOutlineCheckCircle } from "react-icons/ai";
import { useRouter } from "next/router";
import { agentsUrl } from "@/slices/subOptions/subOptionSlice";
import { GridMoreVertIcon } from "@mui/x-data-grid";
import OtherEmulatorOptionsMenu, {
  StyledMenu,
} from "./OtherEmulatorOptionsMenu";
import { getSessionAgentName } from "@/requests/sessions/basic/getAgentName";
import EditIcon from "@mui/icons-material/Edit";
import DoneIcon from "@mui/icons-material/Done";
import CloseIcon from "@mui/icons-material/Close";
import { editSessionName } from "@/requests/sessions/basic/editSessionName";
import { emulatorActions } from "@/slices/emulator/emulatorSlice";
import { TrainingStatusIndicator } from "@/components/agents/TrainingStatusIndicator";
import { IAgent } from "@/slices/agents/agentsSlice";

const ChatHeader = () => {
  const dispatch = useDispatch();
  const router = useRouter();
  const { sessionId } = router.query as { sessionId: string };
  const currentAgent: IAgent = useSelector(
    (state: RootState) => state.agents.currentAgent
  );
  const detailOpened = useSelector(
    (state: RootState) => state.emulator.detailsTabOpened
  );
  const header = useSelector(
    (state: RootState) => state.emulator.session?.basic.sessionName
  );
  const agentId = useSelector(
    (state: RootState) => state.emulator.session?.basic.agentId
  );
  const saved = useSelector(
    (state: RootState) => state.emulator.session?.saved
  );
  const [agentName, setAgentName] = useState("No name");
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const [edit, setEdit] = useState<boolean>(false);
  const [sessionName, setSessionName] = useState<string>("");
  const open = Boolean(anchorEl);

  const handleSessionNameChange = async () => {
    setEdit(false);
    const response = await editSessionName(
      {
        sessionId,
        name: sessionName,
      },
      dispatch
    );
    if (response?.status === 200) {
      dispatch(emulatorActions.sessionNameChanged(sessionName));
    }
  };

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };

  // const [open, setOpen] = useState(false);
  // useEffect(() => {}, []);
  // useEffect(() => {
  //   if (open && detailOpened) {
  //     setOpen(false);
  //   }
  // }, [detailOpened]);
  const handleChipClick = () => {
    router.push(agentsUrl + "/" + agentId);
  };
  useEffect(() => {
    const response = getSessionAgentName({ sessionId }, dispatch);
    response.then((resData) => {
      if (resData?.status === 200) {
        setAgentName(resData.data.data?.agentName);
      }
    });
  }, []);
  useEffect(() => {
    setSessionName(header);
  }, [header]);
  return (
    <>
      <div className="text-heading2 py-2 font-medium flex justify-between">
        <div className="name flex gap-2 items-center">
          {!edit && (
            <Tooltip title={sessionName}>
              <span className="text-3xl max-w-[400px] truncate">{header}</span>
            </Tooltip>
          )}
          {edit && (
            <TextField
              variant="outlined"
              value={sessionName}
              onChange={(event) => {
                setSessionName(event.target.value);
              }}
              sx={{ bgcolor: "white" }}
            />
          )}
          {!edit && (
            <IconButton
              aria-label="delete"
              color="inherit"
              onClick={() => setEdit(true)}
            >
              <EditIcon />
            </IconButton>
          )}
          {edit && (
            <>
              <IconButton
                aria-label="delete"
                color="inherit"
                onClick={handleSessionNameChange}
              >
                <DoneIcon color="success" />
              </IconButton>
              <IconButton
                aria-label="delete"
                color="inherit"
                onClick={() => setEdit(false)}
              >
                <CloseIcon color="error" />
              </IconButton>
            </>
          )}
          <span className="max-w-[200px] flex items-center">
            <Tooltip title={agentName} onClick={handleChipClick}>
              <Chip
                icon={<FiExternalLink />}
                className="truncate"
                label={agentName}
                color="primary"
                variant="filled"
              />
            </Tooltip>
          </span>
          {saved && (
            <Chip
              icon={<AiOutlineCheck />}
              label="Saved"
              color="info"
              variant="outlined"
            />
          )}
        </div>
        <div className="otherOptions flex items-center gap-5 text-[24px] relative">
          {/* <Tooltip title="Restart Session">
            <IconButton>
              <IoReloadSharp className="text-white" />
            </IconButton>
          </Tooltip>
          <Tooltip title="Settings">
            <IconButton>
              <FiSettings className="text-white" />
            </IconButton>
          </Tooltip> */}
          <TrainingStatusIndicator
            agent={{
              _id: currentAgent._id,
              name: currentAgent.agentName,
              isTraining: currentAgent.processingJobs.length > 0,
              activeTraining: currentAgent.processingJobs.map((job) => ({
                startedAt: new Date(),
                status: "in_progress",
                type: job.jobType,
                details: `${job.fileName} is being processed.`,
              })),
            }}
            onRetry={() => {}}
          />
          <IconButton
            aria-label="more"
            id="long-button"
            aria-controls={open ? "long-menu" : undefined}
            aria-expanded={open ? "true" : undefined}
            aria-haspopup="true"
            onClick={handleClick}
            sx={{ color: "white" }}
          >
            <BsThreeDotsVertical />
          </IconButton>
          <StyledMenu
            id="demo-customized-menu"
            MenuListProps={{
              "aria-labelledby": "demo-customized-button",
            }}
            anchorEl={anchorEl}
            open={open}
            onClose={handleClose}
          >
            <OtherEmulatorOptionsMenu handleClose={() => setAnchorEl(null)} />
          </StyledMenu>

          {/* {open && (
            <OtherEmulatorOptions
              handleOpen={(x) => setOpen(x)}
              key={"Other-options"}
            />
          )} */}
        </div>
      </div>
      <div className="h-[1px] bg-fourth"></div>
    </>
  );
};

export default ChatHeader;
