import { saveSession } from "@/requests/sessions/chat/saveSession";
import { unsaveSession } from "@/requests/sessions/chat/unsaveSession";
import { emulatorActions } from "@/slices/emulator/emulatorSlice";
import { RootState } from "@/store";
import { Tooltip } from "@mui/material";
import { useRouter } from "next/router";
import React from "react";
import { AiOutlineHistory, AiOutlineSave } from "react-icons/ai";
import { BiImport } from "react-icons/bi";
import { IoCreateOutline } from "react-icons/io5";
import { useDispatch, useSelector } from "react-redux";

interface IProp {
  handleOpen: (x: boolean) => void;
}

const OtherEmulatorOptions: React.FC<IProp> = ({ handleOpen }) => {
  const dispatch = useDispatch();
  const saved = useSelector((state: RootState) => state.emulator.session.saved);
  const router = useRouter();
  const { sessionId } = router.query;
  const handleSave = async () => {
    handleOpen(false);
    const response = await saveSession(
      { sessionId: sessionId as string },
      dispatch
    );
    if (response?.status === 200) {
      dispatch(emulatorActions.sessionSaved(true));
    }
  };
  const handleUnsave = async () => {
    handleOpen(false);
    const response = await unsaveSession(
      { sessionId: sessionId as string },
      dispatch
    );
    if (response?.status === 200) {
      dispatch(emulatorActions.sessionSaved(false));
    }
  };
  return (
    <div className="font-medium bg-gradient-to-br from-slate-100 to-gray-200 text-primary absolute top-[70px] right-0 w-[300px] rounded-xl">
      <Tooltip title="The session will be fed into the learning resource of this agent.">
        <div
          className="flex items-center px-3 py-2 border-b-[2px] border-gray-500 cursor-pointer hover:bg-slate-300 hover:rounded-t-xl transition-all"
          onClick={saved ? handleUnsave : handleSave}
        >
          <div className="w-[50px] mx-3">
            <AiOutlineSave className="text-[32px]" />
          </div>
          <div className="">{saved ? "Unsave" : "Save"}</div>
        </div>
      </Tooltip>
      <div className="flex items-center px-3 py-2 border-b-[2px] border-gray-500 cursor-pointer hover:bg-slate-300">
        <div className="w-[50px] mx-3">
          <IoCreateOutline className="text-[32px]" />
        </div>
        <div className="">New session</div>
      </div>
      <div className="flex items-center px-3 py-2 cursor-pointer hover:bg-slate-300 hover:rounded-b-xl">
        <div className="w-[50px] mx-3">
          <AiOutlineHistory className="text-[32px]" />
        </div>
        <div className="">Show history</div>
      </div>
    </div>
  );
};

export default OtherEmulatorOptions;
