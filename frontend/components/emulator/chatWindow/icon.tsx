import { Avatar } from "@mui/material";
import { grey, indigo } from "@mui/material/colors";
import React, { FC } from "react";
import { BsRobot } from "react-icons/bs";
import { FaUserTie } from "react-icons/fa";

interface iconProps {
  by: string;
}

const Icon: FC<iconProps> = ({ by }) => {
  return (
    <>
      {by === "human" && (
        <Avatar
          sx={{
            background: `linear-gradient(135deg, ${indigo[500]},  ${indigo[600]})`,
          }}
        >
          <FaUserTie />
        </Avatar>
      )}
      {by === "bot" && (
        <Avatar
          sx={{
            background: `linear-gradient(135deg, ${grey[600]}, ${grey[500]})`,
          }}
        >
          <BsRobot />
        </Avatar>
      )}
    </>
  );
};

export default Icon;
