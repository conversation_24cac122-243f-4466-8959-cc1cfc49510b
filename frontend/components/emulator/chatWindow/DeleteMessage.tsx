import React, { useState } from "react";
import {
  Dialog,
  DialogTitle,
  DialogActions,
  Button,
  IconButton,
} from "@mui/material";
import DeleteIcon from "@mui/icons-material/Delete";
import { deleteMessage } from "@/requests/sessions/chat/deleteMessage";
import { useDispatch } from "react-redux";
import { ImSpinner8 } from "react-icons/im";
import { emulatorActions } from "@/slices/emulator/emulatorSlice";

interface IProp {
  eventId: string;
  sessionId: string;
}

const DeleteMessage: React.FC<IProp> = ({ eventId, sessionId }) => {
  const dispatch = useDispatch();
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);

  const handleClickOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  const handleDelete = async () => {
    setLoading(true);
    const response = await deleteMessage({ sessionId, eventId }, dispatch);
    setLoading(false);
    if (response.status === 200) {
      dispatch(emulatorActions.deleteHumanResponse({ eventId }));
      handleClose();
    }
  };

  return (
    <div>
      <IconButton color="error" onClick={handleClickOpen}>
        <DeleteIcon />
      </IconButton>
      <Dialog open={open} onClose={handleClose}>
        <DialogTitle>Are you sure you want to delete this message?</DialogTitle>
        <DialogActions>
          <Button onClick={handleClose}>Cancel</Button>
          <Button
            onClick={handleDelete}
            color="primary"
            disabled={loading}
            autoFocus
          >
            Delete {loading && <ImSpinner8 className="animate-spin" />}
          </Button>
        </DialogActions>
      </Dialog>
    </div>
  );
};

export default DeleteMessage;
