import { But<PERSON> } from "@mui/material";
import React from "react";
import { AiOutlineSend } from "react-icons/ai";

const NoChat = () => {
  return (
    <div className="h-full flex items-center justify-center">
      <div className="bg-gradient-to-r from-slate-50 to-slate-200 text-gray-700 text-[22px] px-7 py-5 rounded-xl">
        <div className="">Welcome to a new emulator session.</div>
        <div className="">You don&apos;t have any messages yet.</div>
        <div className="flex gap-2">
          Click on the <Button
            variant="contained"
            size="small"
            endIcon={<AiOutlineSend />}
            sx={{
              bgcolor: 'linear-gradient(to bottom right, third, purple.700)',
              '&:hover': {
                transform: 'scale(1.05)',
                transition: 'all'
              }
            }}
          >
            Send
          </Button>button to begin interacting with the
          Chatbot.{" "}
        </div>
      </div>
    </div>
  );
};

export default NoChat;
