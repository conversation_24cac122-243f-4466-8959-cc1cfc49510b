import React from "react";
import { <PERSON>complete, TextField, CircularProgress } from "@mui/material";

interface Agent {
  agentName: string;
  _id: string;
}

interface AgentSelectorProps {
  agents: Agent[];
  value: Agent | null;
  onChange: (agent: Agent | null) => void;
  disabled?: boolean;
  loading?: boolean;
}

export const AgentSelector: React.FC<AgentSelectorProps> = ({
  agents,
  value,
  onChange,
  disabled = false,
  loading = false,
}) => {
  return (
    <Autocomplete
      value={value}
      onChange={(_, newValue) => onChange(newValue)}
      options={agents}
      getOptionLabel={(option) => option.agentName}
      renderInput={(params) => (
        <TextField
          {...params}
          placeholder="Select a chatbot"
          size="small"
          InputProps={{
            ...params.InputProps,
            endAdornment: (
              <>
                {loading ? (
                  <CircularProgress color="inherit" size={20} sx={{ mr: 2 }} />
                ) : null}
                {params.InputProps.endAdornment}
              </>
            ),
          }}
        />
      )}
      loading={loading}
      disabled={disabled || loading}
      sx={{ width: 300 }}
    />
  );
};

export default AgentSelector;
