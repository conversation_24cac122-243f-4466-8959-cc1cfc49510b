import React from "react";
import { Box, Paper, Typography, CircularProgress } from "@mui/material";
import {
  SupportAgent as AgentIcon,
  Chat as ChatIcon,
} from "@mui/icons-material";

interface StatusCardProps {
  type: "no-agent" | "no-messages" | "loading";
}

export const StatusCard: React.FC<StatusCardProps> = ({ type }) => {
  const getContent = () => {
    switch (type) {
      case "loading":
        return {
          icon: <CircularProgress size={40} />,
          title: "Starting new session...",
          description: "Please wait while we connect you to the agent.",
        };
      case "no-agent":
        return {
          icon: <AgentIcon sx={{ fontSize: 40, color: "action.active" }} />,
          title: "No Chatbot Selected",
          description: "Please select a chatbot to start the conversation.",
        };
      case "no-messages":
        return {
          icon: <ChatIcon sx={{ fontSize: 40, color: "action.active" }} />,
          title: "Start Conversation",
          description: "Send a message to begin chatting with the agent.",
        };
    }
  };

  const content = getContent();

  return (
    <Box
      sx={{
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        p: 2,
        height: "100%",
      }}
    >
      <Paper
        elevation={0}
        sx={{
          p: 4,
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          gap: 2,
          maxWidth: 400,
          bgcolor: "grey.50",
          borderRadius: 2,
        }}
      >
        {content.icon}
        <Typography variant="h6" align="center">
          {content.title}
        </Typography>
        <Typography variant="body2" color="text.secondary" align="center">
          {content.description}
        </Typography>
      </Paper>
    </Box>
  );
};
