import { useRouter } from "next/router";
import React, { useState } from "react";
import ChatDrawer from "./ChatDrawer";
import { But<PERSON>, Tooltip } from "@mui/material";
import AssistantIcon from "@mui/icons-material/Assistant";
import { useSelector } from "react-redux";
import { RootState } from "@/store";

const AgentEmulatorDrawer = () => {
  const router = useRouter();
  const { ...newQuery } = router.query as {
    agentId: string;
    "new-agent"?: string;
  };
  const currentAgent = useSelector(
    (state: RootState) => state.agents.currentAgent
  );
  const { prompts } = currentAgent;
  const isPromptActive = Boolean(prompts?.prompt);
  const isEmulatorDisabled = !isPromptActive;

  const { agentId, "new-agent": newAgent } = newQuery;
  const [isOpen, setIsOpen] = useState(newAgent === "true");

  const handleOpen = () => {
    if (isPromptActive) {
      setIsOpen(true);
    }
  };

  const removeNewAgentFromQuery = () => {
    const updatedQuery = { ...newQuery };
    delete updatedQuery["new-agent"];
    router.push({
      pathname: router.pathname,
      query: updatedQuery,
    });
  };

  const handleClose = () => {
    setIsOpen(false);
    removeNewAgentFromQuery();
  };

  const buttonClasses = `flex items-center gap-2 shadow-lg transition-all duration-200 rounded-lg px-4 py-2 ${
    isPromptActive
      ? "bg-blue-600 hover:bg-blue-700 text-white cursor-pointer"
      : "bg-gray-400 text-gray-100 cursor-not-allowed"
  }`;

  return (
    <div>
      <Tooltip
        title={
          isPromptActive
            ? ""
            : "Please set an active prompt before using the emulator"
        }
        placement="top"
      >
        <span>
          {" "}
          {/* Wrapper needed for disabled button tooltip */}
          <Button
            onClick={handleOpen}
            className={buttonClasses}
            disabled={!isPromptActive}
          >
            <AssistantIcon className="w-5 h-5" />
            <span>Chat with Assistant</span>
          </Button>
        </span>
      </Tooltip>

      {!isEmulatorDisabled && (
        <ChatDrawer agentId={agentId} open={isOpen} onClose={handleClose} />
      )}
    </div>
  );
};

export default AgentEmulatorDrawer;
