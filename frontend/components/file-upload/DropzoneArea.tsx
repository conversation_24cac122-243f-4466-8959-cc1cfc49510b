import { FileUploadConfig } from "@/types/file";
import React from "react";
import { useDropzone } from "react-dropzone";
import { CloudUpload } from "@mui/icons-material";

interface DropzoneAreaProps {
  config: FileUploadConfig;
  onDrop: (acceptedFiles: File[]) => void;
}

export const DropzoneArea: React.FC<DropzoneAreaProps> = ({
  config,
  onDrop,
}) => {
  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: config.acceptedFileTypes,
    maxFiles: config.maxFiles,
    maxSize: config.maxFileSize,
  });

  return (
    <div
      {...getRootProps()}
      className={`p-8 text-center cursor-pointer border-2 border-dashed rounded-lg
        ${
          isDragActive
            ? "bg-gray-50 border-blue-500"
            : "bg-white border-gray-300"
        }`}
    >
      <input {...getInputProps()} />
      <CloudUpload className="mx-auto h-12 w-12 text-gray-400" />
      <h3 className="mt-2 text-sm font-medium text-gray-900">
        {isDragActive
          ? "Drop the files here"
          : "Drag & drop files here, or click to select files"}
      </h3>
      <p className="mt-1 text-xs text-gray-500">
        Allowed files: PDF, TXT, DOC, DOCX, XLS, XLSX (Max {config.maxFiles}{" "}
        files, {config.maxFileSize! / (1024 * 1024)}MB each)
      </p>
    </div>
  );
};
