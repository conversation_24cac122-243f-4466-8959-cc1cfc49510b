import React from "react";
import { Delete } from "@mui/icons-material";
import { FileWithPreview } from "@/types/file";
import {
  IconButton,
  List,
  ListItem,
  ListItemSecondaryAction,
  ListItemText,
} from "@mui/material";

interface FileListProps {
  files: FileWithPreview[];
  onRemove: (file: FileWithPreview) => void;
}

export const FileList: React.FC<FileListProps> = ({ files, onRemove }) => (
  <List>
    {files.map((file) => (
      <ListItem
        key={file.name}
        className="flex justify-between items-center py-2"
      >
        <ListItemText
          className="flex-1"
          primary={file.name}
          secondary={`${(file.size / 1024 / 1024).toFixed(2)} MB`}
        />
        <ListItemSecondaryAction>
          <IconButton
            onClick={() => onRemove(file)}
            className="hover:bg-gray-100 p-2 rounded-full"
          >
            <Delete className="h-5 w-5" />
          </IconButton>
        </ListItemSecondaryAction>
      </ListItem>
    ))}
  </List>
);
