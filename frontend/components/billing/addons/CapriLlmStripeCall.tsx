import { paymentForLlm } from "@/requests/billing/paymentForLlm";
import {
  <PERSON><PERSON>,
  DialogActions,
  DialogContent,
  DialogContentText,
  TextField,
  InputAdornment,
} from "@mui/material";
import { Stripe, loadStripe } from "@stripe/stripe-js";
import React, { useState } from "react";
import { ImSpinner8 } from "react-icons/im";
import { useDispatch } from "react-redux";

interface IProp {
  handleClose: () => void;
}

const stripePromise = loadStripe(
  process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY as string
);

const CapriLlmStripeCall: React.FC<IProp> = ({ handleClose }) => {
  const dispatch = useDispatch();
  const [value, setValue] = useState<number>(10);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubscribe = async () => {
    setLoading(true);
    const response = await paymentForLlm(
      { orgId: localStorage.getItem("orgId") as string, amount: value },
      dispatch
    );
    setLoading(false);
    if (response?.status === 200) {
      // const { url } = response.data.link;
      // window.location.href = url;
      const data = response.data;
      const paymentIntentClientSecret = data.link.paymentIntentClientSecret;
      const stripe = (await stripePromise) as Stripe;
      // Confirm the PaymentIntent
      stripe
        .confirmCardPayment(paymentIntentClientSecret)
        .then(function (result) {
          if (result.error) {
            // Display the error to the customer
            console.log(result.error.message);
          } else {
            if (result.paymentIntent.status === "succeeded") {
              // The payment has been processed!
              console.log("Payment succeeded");
            } else if (result.paymentIntent.status === "requires_action") {
              // Handle additional steps
              console.log("Additional authentication required");
            } else {
              // The payment failed
              console.log("Payment failed");
            }
          }
        });
      handleClose();
    }
  };

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue =
      event.target.value === "" ? 10 : Number(event.target.value);
    if (inputValue > 500) {
      setError("Input value cannot be above $500");
    } else if (inputValue < 10) {
      setError("Input value cannot be below $10");
    } else {
      setError(null);
    }
    setValue(inputValue);
  };

  return (
    <>
      <DialogContent>
        <DialogContentText>
          Capri&apos;s self hosted LLM are the best
        </DialogContentText>

        <TextField
          value={value}
          onChange={handleInputChange}
          InputProps={{
            startAdornment: <InputAdornment position="start">$</InputAdornment>,
          }}
          variant="outlined"
          inputProps={{
            step: 10,
            min: 10,
            max: 500,
            type: "number",
          }}
          fullWidth
          sx={{ border: 0 }}
          error={!!error}
          helperText={error ?? "Enter an amount between $10 and $500"}
        />
      </DialogContent>
      <DialogActions>
        <Button onClick={handleClose} color="primary">
          Cancel
        </Button>
        <Button
          onClick={handleSubscribe}
          color="primary"
          disabled={loading || !!error}
        >
          Subscribe {loading && <ImSpinner8 className="ml-2 animate-spin" />}
        </Button>
      </DialogActions>
    </>
  );
};

export default CapriLlmStripeCall;
