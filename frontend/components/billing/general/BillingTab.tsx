import * as React from "react";
import Tabs from "@mui/material/Tabs";
import Tab from "@mui/material/Tab";
import Box from "@mui/material/Box";
import { useRouter } from "next/router";

function a11yProps(index: number) {
  return {
    id: `simple-tab-${index}`,
    "aria-controls": `simple-tabpanel-${index}`,
  };
}

const TabArr = ["general", "add-ons", "payment-methods"];

const BillingTab = () => {
  const router = useRouter();
  const [value, setValue] = React.useState(0);

  React.useEffect(() => {
    const show = router.query.show;
    if (show) {
      const tabIndex = TabArr.indexOf(show.toString());
      if (tabIndex !== -1) {
        setValue(tabIndex);
      }
    }
  }, [router.query.show]);

  const handleChange = (event: React.SyntheticEvent, newValue: number) => {
    setValue(newValue);
    const { ...newQuery } = router.query;
    delete newQuery.show;
    router.replace({
      pathname: router.pathname,
      query: { ...newQuery, show: TabArr[newValue] },
    });
  };

  return (
    <Box sx={{ width: "100%" }}>
      <Box sx={{ borderBottom: 1, borderColor: "divider" }}>
        <Tabs
          value={value}
          onChange={handleChange}
          aria-label="basic tabs example"
          sx={{
            "& .MuiTab-root": { color: "white" },
            "& .Mui-selected": { color: "#2e89ff" },
          }}
        >
          <Tab label="General" {...a11yProps(0)} />
          <Tab label="Add ons" {...a11yProps(1)} />
          <Tab label="Payment methods" {...a11yProps(2)} />
        </Tabs>
      </Box>
    </Box>
  );
};

export default BillingTab;
