import { capitalizeFirstLetter } from "@/helpers/basic";
import { PaymentMethod } from "@/pages/home/<USER>";
import { getPaymentMethods } from "@/requests/billing/getPaymentMethods";
import { setDefaultPaymentMethod } from "@/requests/billing/setDefaultPaymentMethod";
import {
  Box,
  Button,
  Card,
  CardContent,
  Chip,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Divider,
  Grid,
  Paper,
  Skeleton,
  Stack,
  Typography,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import AddIcon from "@mui/icons-material/Add";
import { addPaymentMethod } from "@/requests/organizations/billing/addPaymentMethod";
import { useRouter } from "next/router";

interface IProp {
  paymentMethods: PaymentMethod[];
  handlePaymentMethodsUpdate: (x: PaymentMethod[]) => void;
}

const PaymentMethods: React.FC<IProp> = ({
  paymentMethods,
  handlePaymentMethodsUpdate,
}) => {
  const dispatch = useDispatch();
  const router = useRouter();
  const orgId = localStorage.getItem("orgId") as string;
  const { success } = router.query as { success: "false" | "true" | undefined };
  const [loading, setLoading] = useState(false);
  const [addPaymentLoading, setAddPaymentLoading] = useState(false);

  const handleSetDefault = async (id: string) => {
    const response = await setDefaultPaymentMethod(
      { orgId, paymentMethodId: id },
      dispatch
    );

    if (response?.status === 200) {
      const updatedPaymentMethods = paymentMethods?.map((method) => {
        if (method.id === id) {
          return { ...method, isDefault: true };
        }
        return { ...method, isDefault: false };
      });
      handlePaymentMethodsUpdate(updatedPaymentMethods);
    }
  };

  const handleAddPaymentMethod = async () => {
    setAddPaymentLoading(true);
    const response = await addPaymentMethod({ orgId }, dispatch);
    setAddPaymentLoading(false);
    if (response?.status === 200) {
      const url = response.data.url;
      //open a new window in the same page
      window.open(url, "_self");
    }
  };

  const [open, setOpen] = useState(false);

  useEffect(() => {
    if (success === "true" || success === "false") {
      setOpen(true);
    }
  }, [success]);

  const handleClose = () => {
    setOpen(false);
    //remove the success from the url
    const { ...newQuery } = router.query;
    delete newQuery.success;
    router.push({
      pathname: router.pathname,
      query: newQuery,
    });
  };

  if (loading)
    return (
      <>
        {Array.from({ length: 3 }).map((_, index) => (
          <Grid item sm={12} md={6} key={index}>
            <Skeleton
              variant="rectangular"
              height={150}
              sx={{ bgcolor: "grey.700", borderRadius: "5px" }}
            />
          </Grid>
        ))}
      </>
    );
  if (!paymentMethods || paymentMethods.length === 0) {
    return (
      <Paper sx={{ py: 4, textAlign: "center" }}>
        <Typography variant="h6">No payment methods added</Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={handleAddPaymentMethod}
          disabled={addPaymentLoading}
        >
          Add{addPaymentLoading && "ing"}
        </Button>
      </Paper>
    );
  }
  return (
    <>
      <Dialog open={open} onClose={handleClose}>
        <DialogTitle>{success === "true" ? "Success" : "Failure"}</DialogTitle>
        <DialogContent>
          <DialogContentText>
            {success === "true"
              ? "Payment method added successfully"
              : "Failed to add payment method"}
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClose} color="primary">
            Close
          </Button>
        </DialogActions>
      </Dialog>
      <Grid container spacing={{ xs: 1, sm: 3 }} alignItems={"stretch"}>
        {paymentMethods?.map((method) => (
          <Grid item sm={12} md={6} key={method.id} style={{ display: "flex" }}>
            <Card style={{ flex: 1, display: "flex", flexDirection: "column" }}>
              <CardContent>
                <Stack direction={"column"} spacing={2}>
                  <Typography variant="h6">
                    {capitalizeFirstLetter(method.brand)} **** **** ****{" "}
                    {method.last4}
                  </Typography>
                  <Typography variant="body2">
                    Exp. {method.exp_month}/{method.exp_year}
                  </Typography>
                  {method.isDefault ? (
                    <Chip
                      label="Default"
                      color="primary"
                      size="small"
                      sx={{ width: "fit-content" }}
                    />
                  ) : (
                    <Chip
                      label="Set default"
                      size="small"
                      variant="outlined"
                      sx={{ width: "fit-content" }}
                      onClick={() => {
                        handleSetDefault(method.id);
                      }}
                    />
                  )}
                </Stack>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>
      <Stack
        direction={"row"}
        spacing={2}
        sx={{ mb: 2 }}
        justifyContent={"start"}
        mt={2}
      >
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={handleAddPaymentMethod}
          disabled={addPaymentLoading}
        >
          Add{addPaymentLoading && "ing"}
        </Button>
      </Stack>
    </>
  );
};

export default PaymentMethods;
