import React, { useEffect, useState } from "react";
import {
  <PERSON>,
  Typo<PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  CardContent,
  Stack,
} from "@mui/material";
import AccountBalanceWalletIcon from "@mui/icons-material/AccountBalanceWallet";
import LlmAddonDialog from "./LlmAddonDialog";
import CapriLlmStripeCall from "../addons/CapriLlmStripeCall";
import Dialog from "@mui/material/Dialog";
import DialogTitle from "@mui/material/DialogTitle";
import { useDispatch } from "react-redux";
import { getAddonAmount } from "@/requests/organizations/billing/getAddonAmount";

interface BalanceAndOptionsProps {
  description: string;
}

const BalanceAndOptions: React.FC<BalanceAndOptionsProps> = ({
  description,
}) => {
  const dispatch = useDispatch();
  const [addonAmount, setAddonAmount] = React.useState(0);
  const [loading, setLoading] = React.useState(false);
  const [open, setOpen] = useState(false);
  const [openAutoPay, setOpenAutoPay] = useState(false);

  const orgId = localStorage.getItem("orgId") as string;
  useEffect(() => {
    const getAddonAmountFn = async () => {
      setLoading(true);
      const response = await getAddonAmount({ orgId }, dispatch);
      setLoading(false);
      if ((response.status = 200)) {
        const amount = response.data.addonAmount;
        setAddonAmount(amount);
      }
    };
    getAddonAmountFn();
  }, [dispatch, orgId]);
  const handleClose = () => {
    setOpen(false);
  };

  const handleAutoPayClose = () => {
    setOpenAutoPay(false);
  };

  const handleClickOpenPayment = () => {
    setOpen(true);
  };
  const handleClickOpenAutoPay = () => {
    setOpenAutoPay(true);
  };
  return (
    <>
      <Card sx={{ width: "100%", bgcolor: "background.paper" }}>
        <CardContent>
          <Stack
            direction="row"
            alignItems="center"
            justifyContent="space-between"
            spacing={2}
          >
            <Box sx={{ display: "flex", alignItems: "center" }}>
              <Box>
                <Typography variant="h6" component="div">
                  {loading
                    ? "Fetching available balance..."
                    : `$${addonAmount.toFixed(2)}`}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {description}
                </Typography>
              </Box>
            </Box>
            <Stack direction="row" spacing={1}>
              <Button
                variant="outlined"
                color="primary"
                size="small"
                onClick={handleClickOpenPayment}
              >
                GET
              </Button>
              <Button
                variant="outlined"
                color="primary"
                size="small"
                onClick={handleClickOpenAutoPay}
              >
                AUTO PAY
              </Button>
            </Stack>
          </Stack>
        </CardContent>
      </Card>
      <Dialog fullWidth onClose={handleClose} open={open}>
        <DialogTitle>Get Capri hosted LLM</DialogTitle>
        <CapriLlmStripeCall handleClose={handleClose} />
      </Dialog>
      <Dialog fullWidth onClose={handleAutoPayClose} open={openAutoPay}>
        <LlmAddonDialog handleClose={handleAutoPayClose} />
      </Dialog>
    </>
  );
};

export default BalanceAndOptions;
