import React, { useState } from "react";
import BillingCard from "./BillingCard";
import { FaLanguage } from "react-icons/fa6";
import { useRouter } from "next/router";
import { Alert, Grid, Stack } from "@mui/material";
import Link from "next/link";
import { RiVoiceprintLine } from "react-icons/ri";
import BalanceAndOptions from "./BalanceAndOptions";

interface IProp {
  haveDefaultPaymentMethod: boolean;
}

const BillingCardsAddon: React.FC<IProp> = ({ haveDefaultPaymentMethod }) => {
  const router = useRouter();

  const handleClickHostedLlmUsage = () => {
    router.push("/home/<USER>");
  };
  const handleClickHostedVoiceUsage = () => {
    router.push("/home/<USER>");
  };

  return (
    <>
      {!!!haveDefaultPaymentMethod && (
        <Alert severity="warning" sx={{ my: 2, width: "fit-content" }}>
          Set up your billing details{" "}
          <Link href="/home/<USER>" className="underline">
            here
          </Link>{" "}
          to use Capri hosted LLM.
        </Alert>
      )}
      <Stack direction={"column"} gap={2}>
        <BalanceAndOptions description="Current balance" />
        <Grid container spacing={{ xs: 1, sm: 3 }} alignItems={"stretch"}>
          <BillingCard
            header="Capri hosted LLM"
            mainLine=""
            moreText="Use Capri's hosted LLM to manage your language models."
            iconDetails={{
              icon: FaLanguage,
              color: "your_desired_color",
            }}
            buttons={[
              {
                text: "View usage",
                variant: "text",
                color: "primary",
                disabled: !!!haveDefaultPaymentMethod,
                handleClick: handleClickHostedLlmUsage,
              },
            ]}
          />
          <BillingCard
            header="Voice"
            mainLine=""
            moreText="Use Capri's hosted Voice solution."
            iconDetails={{
              icon: RiVoiceprintLine,
              color: "your_desired_color",
            }}
            buttons={[
              {
                text: "View usage",
                variant: "text",
                color: "primary",
                disabled: !!!haveDefaultPaymentMethod,
                handleClick: handleClickHostedVoiceUsage,
              },
            ]}
          />
        </Grid>
      </Stack>
    </>
  );
};

export default BillingCardsAddon;
