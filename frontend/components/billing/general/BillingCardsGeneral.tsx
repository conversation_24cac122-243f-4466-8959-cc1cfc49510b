import React, { useEffect, useState } from "react";
import BillingCard from "@/components/billing/general/BillingCard";
import { MdOutlinePriceChange } from "react-icons/md";
import { MdManageSearch } from "react-icons/md";
import { AiOutlineUserAdd } from "react-icons/ai";
import { BiSupport } from "react-icons/bi";
import { capitalizeFirstLetter } from "@/helpers/basic";
import { getStripeCustomerPortalLink } from "@/requests/billing/getStripeCustomerPortalLink";
import { useDispatch } from "react-redux";
import { useUser } from "@auth0/nextjs-auth0/client";
import { getBillingDetails } from "@/requests/organizations/billing/getBillingDetails";
import { useRouter } from "next/router";
import { Chip, Grid, Skeleton } from "@mui/material";
import { useCurrentPlan } from "@/hooks/useCurrentPlan";

function getDaysRemaining(startDate: number): string {
  const start = new Date(startDate);
  const end = new Date(start.getTime() + 14 * 24 * 60 * 60 * 1000); // 14 days later
  const now = new Date();

  if (now > end) {
    return "Trial expired.";
  } else {
    const diffTime = Math.abs(end.getTime() - now.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return `${diffDays} days remaining`;
  }
}

const BillingCardsGeneral = () => {
  const dispatch = useDispatch();
  const router = useRouter();
  const { disableExternalAiProviders, currentPlan, currentPlanFullName } =
    useCurrentPlan();
  const { user } = useUser();
  const pathname = router.pathname;
  const [billingDetails, setBillingDetails] = useState<
    Partial<{
      plan: string;
      status: string;
      billingCycle: "monthly" | "yearly";
      havePaymentInfo: boolean;
      allowedAgents: number;
      startDate: number;
      customerId: string;
      email: string;
      userName: string;
    }>
  >({});
  const [billingExpired, setBillingExpired] = useState(false);
  const [nextBillingDate, setNextBillingDate] = useState<Date | null>(null);
  const [loading, setLoading] = useState(false);
  const orgId = localStorage.getItem("orgId") as string;

  const chipVariant = (status: string) => {
    switch (status) {
      case "active":
        return "success";
      case "cancelled":
        return "warning";
      case "suspended":
        return "error";
      default:
        return "default";
    }
  };

  useEffect(() => {
    setLoading(true);
    const response = getBillingDetails(
      {
        orgId: localStorage.getItem("orgId") || "",
      },
      dispatch
    );
    response
      .then((resData) => {
        console.log({ resData });

        if (resData?.status === 200) {
          setBillingDetails(resData.data.billingDetails);
          setBillingExpired(resData.data.billingExpired);
          setNextBillingDate(resData.data.nextPaymentDate);
          console.log({ billingDetails });
        }
      })
      .finally(() => {
        setLoading(false);
      });
  }, [router.query, orgId]);

  const editPlan = () => {
    router.push(pathname + "/upgrade");
  };

  const handleManageBilling = async () => {
    const response = await getStripeCustomerPortalLink(
      { orgId: localStorage.getItem("orgId") || "" },
      dispatch
    );
    console.log({ response });

    if (response?.status === 200) {
      const link = response.data.link;
      window.open(link, "_blank");
    }
  };

  const contactSupport = () => {
    const email = "<EMAIL>";
    const subject = "Need support in billing";
    const body = `Hello. This is ${user?.name} having user email ${
      user?.email
    }. The organization Id is ${localStorage.getItem("orgId") || "NULL"}`;

    window.open(
      `mailto:${email}?subject=${encodeURIComponent(
        subject
      )}&body=${encodeURIComponent(body)}`
    );
  };
  if (loading)
    return (
      <>
        <Grid container spacing={{ xs: 1, sm: 3 }} alignItems={"stretch"}>
          {Array.from({ length: 3 }).map((_, index) => (
            <Grid item sm={12} md={6} key={index}>
              <Skeleton
                variant="rectangular"
                height={210}
                sx={{ bgcolor: "grey.700", borderRadius: "5px" }}
              />
            </Grid>
          ))}
        </Grid>
      </>
    );
  return (
    <>
      <Grid container spacing={{ xs: 1, sm: 3 }} alignItems={"stretch"}>
        <BillingCard
          header="Plan detail"
          mainLine={currentPlanFullName}
          moreText={
            <>
              {billingDetails?.plan === "trial" ? (
                <div className="">
                  {getDaysRemaining(billingDetails?.startDate as number)}
                </div>
              ) : (
                <Chip
                  color={chipVariant(billingDetails?.status as string)}
                  label={
                    capitalizeFirstLetter(billingDetails?.status as string) ||
                    "No status"
                  }
                />
              )}
            </>
          }
          iconDetails={{
            icon: MdOutlinePriceChange,
            color: "your_desired_color",
          }}
          buttons={[
            {
              text: "Edit plan",
              variant: "text",
              color: "primary",
              handleClick: editPlan,
              // disabled: true,
            },
            // {
            //   text: "Cancel plan",
            //   variant: "text",
            //   color: "error",
            //   handleClick: cancelPlanClick,
            //   // disabled: true,
            // },
          ]}
        />
        <BillingCard
          header="Manage billing"
          mainLine=""
          moreText="Manage your Stripe billing information, view invoices, etc."
          iconDetails={{
            icon: MdManageSearch,
            color: "your_desired_color",
          }}
          buttons={[
            {
              text: "Manage billing",
              variant: "text",
              color: "primary",
              handleClick: handleManageBilling,
              // disabled:
              //   (billingDetails?.plan as string) === "trial" || false,
            },
          ]}
        />
        <BillingCard
          header="Contact support"
          mainLine="<EMAIL>"
          moreText="Contact our support team for any help"
          iconDetails={{
            icon: BiSupport,
            color: "your_desired_color",
          }}
          buttons={[
            {
              text: "Contact support",
              variant: "text",
              color: "primary",
              handleClick: contactSupport,
            },
          ]}
        />
      </Grid>
    </>
  );
};

export default BillingCardsGeneral;
