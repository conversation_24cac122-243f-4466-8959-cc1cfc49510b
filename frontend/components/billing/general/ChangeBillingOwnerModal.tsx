import { But<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, TextField, Typography } from "@mui/material";
import React from "react";

interface IProp {
  handleCloseFn: () => void;
}

const ChangeBillingOwnerModal: React.FC<IProp> = ({ handleCloseFn }) => {
  return (
    <Stack sx={{ bgcolor: "white", width: "450px", borderRadius: 2 }}>
      <Typography variant="h4" padding={2}>
        Change user
      </Typography>
      <Divider />
      <Stack padding={2}>
        {/* <Typography variant="h6">This will be the new user</Typography> */}
        <TextField
          id="outlined-basic-new-user"
          label="User email"
          variant="outlined"
          helperText="This will be the new billing email"
        />
      </Stack>
      <Stack direction={"row"} gap={2} padding={2} justifyContent={"flex-end"}>
        <Button variant="text" onClick={handleCloseFn}>
          Cancel
        </Button>
        <Button variant="contained">Update</Button>
      </Stack>
    </Stack>
  );
};

export default ChangeBillingOwnerModal;
