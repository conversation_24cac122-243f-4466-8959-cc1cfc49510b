import React, { FormEvent, useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  FormControlLabel,
  Switch,
  TextField,
  DialogActions,
  Button,
  Stack,
  InputAdornment,
} from "@mui/material";
import { useDispatch, useSelector } from "react-redux";
import { updateAutoPay } from "@/requests/organizations/billing/updateAutopay";
import { RootState } from "@/store";
import { ImSpinner8 } from "react-icons/im";
import { organizationAction } from "@/slices/organization/organizationSlice";

interface IProp {
  handleClose: () => void;
}

const LlmAddonDialog: React.FC<IProp> = ({ handleClose }) => {
  const dispatch = useDispatch();
  const orgLoading = useSelector(
    (state: RootState) => state.organization.loading
  );
  const autoPay = useSelector((state: RootState) => state.organization.autoPay);

  const [loading, setLoading] = useState<boolean>(false);

  const [autoRecharge, setA<PERSON><PERSON>echarge] = useState(
    autoPay?.autoRecharge ?? false
  );
  const [threshold, setThreshold] = useState<number | null>(
    autoPay?.threshold ?? null
  );
  const [rechargeAmount, setRechargeAmount] = useState<number | null>(
    autoPay?.rechargeAmount ?? null
  );
  //   const [error, setError] = useState<string | null>(null);
  const [thresholdError, setThresholdError] = useState<string | null>(null);
  const [rechargeAmountError, setRechargeAmountError] = useState<string | null>(
    null
  );

  const handleSwitchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setAutoRecharge(event.target.checked);
  };

  const handleThresholdChange = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const value = Number(event.target.value);
    if (value > 500) {
      setThresholdError("Value cannot be above $500");
    } else if (value < 1) {
      setThresholdError("Value cannot be below $1");
    } else {
      setThresholdError(null);
    }
    setThreshold(value);
  };

  const handleRechargeAmountChange = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const value = Number(event.target.value);
    if (value > 500) {
      setRechargeAmountError("Value cannot be above $500");
    } else if (value < 10) {
      setRechargeAmountError("Value cannot be below $10");
    } else {
      setRechargeAmountError(null);
    }
    setRechargeAmount(value);
  };

  const orgId = localStorage.getItem("orgId") as string;

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    setLoading(true);
    const autoPay = {
      autoRecharge,
      threshold: threshold as number,
      rechargeAmount: rechargeAmount as number,
    };
    const response = await updateAutoPay(
      {
        orgId,
        autoPay,
      },
      dispatch
    );
    if (response?.status === 200) {
      dispatch(organizationAction.setAutoPay(autoPay));
      handleClose();
    }
    setLoading(false);
  };

  if (orgLoading)
    return (
      <div className="my-10 mx-auto">
        <ImSpinner8 className="animate-spin text-[24px]" />
      </div>
    );

  return (
    <Dialog open={true} onClose={handleClose}>
      <form onSubmit={handleSubmit}>
        <DialogTitle>Automatic Recharge</DialogTitle>
        <DialogContent>
          <FormControlLabel
            control={
              <Switch checked={autoRecharge} onChange={handleSwitchChange} />
            }
            label="Would you like to set up automatic recharge?"
          />
          {autoRecharge && (
            <>
              <Stack direction={"column"} gap={2} mt={2}>
                <TextField
                  value={threshold}
                  onChange={handleThresholdChange}
                  label="When credit balance falls below"
                  type="number"
                  inputProps={{ min: 1, max: 500 }}
                  error={!!thresholdError}
                  helperText={
                    thresholdError ?? "Enter an amount between $1 and $500"
                  }
                  fullWidth
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">$</InputAdornment>
                    ),
                  }}
                />
                <TextField
                  value={rechargeAmount}
                  onChange={handleRechargeAmountChange}
                  label="Bring credit balance back up to"
                  type="number"
                  inputProps={{ min: 10, max: 500 }}
                  error={!!rechargeAmountError}
                  helperText={
                    rechargeAmountError ??
                    "Enter an amount between $10 and $500"
                  }
                  fullWidth
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">$</InputAdornment>
                    ),
                  }}
                />
              </Stack>
            </>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClose} color="primary">
            Cancel
          </Button>
          <Button
            color="primary"
            disabled={
              !!!threshold ||
              !!!rechargeAmount ||
              !!thresholdError ||
              !!rechargeAmountError ||
              !!loading
            }
            type="submit"
          >
            Submit {loading && <ImSpinner8 className="animate-spin ml-2" />}
          </Button>
        </DialogActions>
      </form>
    </Dialog>
  );
};

export default LlmAddonDialog;
