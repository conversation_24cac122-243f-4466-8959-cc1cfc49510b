import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>rid,
  IconButton,
  Typo<PERSON>,
} from "@mui/material";
import React, { ReactNode } from "react";
import { IconType } from "react-icons/lib";
import { MdOutlinePriceChange } from "react-icons/md";

interface IProp {
  header: string;
  mainLine: string;
  moreText: ReactNode;
  iconDetails: {
    icon: IconType;
    color: string;
  };
  buttons: {
    text: string;
    variant: "contained" | "outlined" | "text";
    color: "error" | "primary";
    disabled?: boolean;
    handleClick?: () => void;
  }[];
}

const BillingCard: React.FC<IProp> = ({
  header,
  mainLine,
  iconDetails,
  moreText,
  buttons,
}) => {
  return (
    <Grid item sm={12} md={6} style={{ display: "flex" }}>
      <Card style={{ flex: 1, display: "flex", flexDirection: "column" }}>
        <CardHeader
          avatar={
            <IconButton>
              {React.createElement(iconDetails.icon, {
                style: { color: iconDetails.color },
              })}
            </IconButton>
          }
          title={header}
          subheader={mainLine}
        />
        <CardContent style={{ flex: 1 }}>
          <Typography variant="subtitle1" color="textSecondary">
            {moreText}
          </Typography>
        </CardContent>
        <Divider />
        <CardActions>
          {buttons.map((button, index) => (
            <Button
              key={index}
              size="small"
              color={button.color}
              disabled={button?.disabled || false}
              variant={button.variant}
              onClick={button.handleClick}
            >
              {button.text}
            </Button>
          ))}
        </CardActions>
      </Card>
    </Grid>
  );
};

export default BillingCard;
