import React from "react";
import { TBillingCycle } from "./Plans";

interface IProp {
  selected: TBillingCycle;
  onChangeFn: (billingCycle: TBillingCycle) => void;
}

const BillingCycleChoose: React.FC<IProp> = ({ selected, onChangeFn }) => {
  return (
    <div className="flex justify-center items-center">
      <div className=" text-lg px-1 py-1 rounded-lg bg-slate-600 flex">
        <div
          className={`px-7 py-3  rounded-l-lg hover:bg-gray-700 transition-all cursor-pointer ${
            selected === "monthly" ? "bg-slate-800" : "bg-slate-600"
          }`}
          onClick={() => {
            onChangeFn("monthly");
          }}
        >
          Monthly
        </div>
        <div
          className={`px-7 py-3 rounded-r-lg hover:bg-gray-700 transition-all cursor-pointer ${
            selected === "yearly" ? "bg-slate-800" : "bg-slate-600"
          }`}
          onClick={() => {
            onChangeFn("yearly");
          }}
        >
          Yearly
        </div>
      </div>
    </div>
  );
};

export default BillingCycleChoose;
