import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ontent,
  Divider,
  Stack,
  Typography,
} from "@mui/material";
import React from "react";
import { AiOutlineCheck, AiOutlineCheckCircle } from "react-icons/ai";
import { ImCancelCircle } from "react-icons/im";
import { IPlanFeatures, TBillingCycle } from "./Plans";
import { grey, lightBlue } from "@mui/material/colors";
import {
  TPlanName,
  choosePlan,
} from "@/requests/organizations/billing/choosePlan";
import { loadStripe } from "@stripe/stripe-js";
import { useRouter } from "next/router";
import { useDispatch } from "react-redux";
import { snackbarActions } from "@/slices/general/snackbar";

interface IProp {
  plan: string;
  price: string;
  features: IPlanFeatures[];
  active: boolean;
  planId: TPlanName;
  billingCycle: TBillingCycle;
}

if (!process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY) {
  throw new Error("Stripe error");
}
const stripePromise = loadStripe(
  process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY
);

const BillingCard: React.FC<IProp> = ({
  plan,
  price,
  features,
  active,
  planId,
  billingCycle,
}) => {
  const dispatch = useDispatch();
  const router = useRouter();
  const handleChoosePlan = async () => {
    try {
      if (planId === "custom") {
        //handle custom plan
      } else {
        const response = await choosePlan(
          {
            orgId: localStorage.getItem("orgId") || "",
            planName: planId,
            billingCycle
          },
          dispatch
        );
        console.log("Res data afasd", response?.data);

        if (response?.status === 200) {
          if (response.data.data.checkoutSessionId) {
            //first time checkout
            const checkoutSessionId = response.data.data.checkoutSessionId;
            const stripe = await stripePromise;
            if (stripe) {
              const result = await stripe?.redirectToCheckout({
                sessionId: checkoutSessionId,
              });
              if (result && result.error) {
                //failed
                console.log("Stripe error: ", result.error);
              }
            }
          } else if (response.data.data.subscriptionId) {
            //success
            const newQuery = {
              ...router.query,
              success: "true",
            };
            router.push({
              pathname: router.pathname,
              query: newQuery,
            });
          }
        }
      }
    } catch (error: any) {
      dispatch(
        snackbarActions.setSnackbar({
          message:
            error.response?.data?.message ||
            "An unexpected error occurred. Please try again.",
          type: "error",
        })
      );
    }
  };
  return (
    <Card
      variant="outlined"
      sx={{
        width: "300px",
        bgcolor: grey[100],
        ...(active
          ? {
              transform: "scale(1.05)",
              bgcolor: lightBlue[50], // This is a light gray color. Adjust to your desired darker shade of white.
            }
          : {}),
      }}
    >
      <CardContent>
        <Stack
          direction={"column"}
          alignItems={"center"}
          gap={3}
          sx={{ alignItems: "initial" }}
        >
          <Stack
            direction={"column"}
            alignItems={"center"}
            sx={{ color: grey[800] }}
          >
            <Typography variant="h5" fontWeight={100} gutterBottom>
              {plan}
            </Typography>
            <Stack direction={"row"} alignItems={"flex-end"}>
              <Typography variant="h4" fontWeight={800}>
                {price}
              </Typography>
              {planId !== "custom" && (
                <Typography variant="body1" paddingY={1}>
                  /mon
                </Typography>
              )}
            </Stack>
            <Typography variant="body2">billed {billingCycle}</Typography>
          </Stack>
          <CardActions>
            {!active && (
              <Button
                variant="contained"
                color="primary"
                size="large"
                fullWidth
                onClick={handleChoosePlan}
              >
                Choose plan
              </Button>
            )}
            {active && (
              <Button
                startIcon={<AiOutlineCheck />}
                variant="contained"
                color="success"
                size="large"
                fullWidth
                disabled
              >
                Active
              </Button>
            )}
          </CardActions>
          <Divider />
          <Stack direction={"column"} gap={1}>
            {features.map((feature) => (
              <Stack
                direction={"row"}
                gap={1}
                alignItems={"center"}
                key={"Feature_name_" + feature.name}
              >
                {feature.included ? (
                  <Box>
                    <AiOutlineCheckCircle color="green" />
                  </Box>
                ) : (
                  <Box>
                    <ImCancelCircle color="red" />
                  </Box>
                )}
                <Typography variant="body1" component="div" key={feature.name}>
                  {feature.name}
                </Typography>
              </Stack>
            ))}
          </Stack>
        </Stack>
      </CardContent>
    </Card>
  );
};

export default BillingCard;
