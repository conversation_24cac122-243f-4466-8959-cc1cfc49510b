import { Stack } from "@mui/material";
import React, { useEffect, useState } from "react";
import BillingCard from "./BillingCard";
import { getBillingDetails } from "@/requests/organizations/billing/getBillingDetails";
import { TPlanName } from "@/requests/organizations/billing/choosePlan";
import { useRouter } from "next/router";
import { useDispatch } from "react-redux";
import BillingCycleChoose from "./BillingCycleChoose";
import PageLoadingSpinner from "@/components/general/PageLoadingSpinner";

export interface IPlanFeatures {
  name: string;
  included: boolean;
}

export type TBillingCycle = "monthly" | "yearly";

const features: {
  starter: IPlanFeatures[];
  basic: IPlanFeatures[];
  pro: IPlanFeatures[];
  enterprise: IPlanFeatures[];
  custom: IPlanFeatures[];
} = {
  starter: [
    {
      name: "Unlimited active agents",
      included: true,
    },
    {
      name: "Access to Capri hosted models.",
      included: true,
    },

    { name: "Unlimited training sessions", included: true },
    { name: "Unlimited Google sheet connections", included: true },
    { name: "Unlimited CRM connections", included: true },
    { name: "Web-scraping", included: true },
  ],
  basic: [
    { name: "Everything included in Starter, plus", included: true },
    { name: "1 active agent", included: true },
    { name: "Use your own models", included: true },
  ],
  pro: [
    { name: "Everything included in Basic, plus", included: true },
    { name: "10 active agents", included: true },
    { name: "Conversations Tab", included: true },
  ],
  enterprise: [
    { name: "Everything included in Pro, plus", included: true },
    { name: "200 active agents", included: true },
    { name: "Rebilling", included: true },
  ],
  custom: [{ name: "Unlimited agents", included: true }],
};

const Plans = () => {
  const dispatch = useDispatch();
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [currentPlan, setCurrentPlan] = useState<TPlanName>("trial");
  const [billingCycle, setBillingCycle] = useState<TBillingCycle>("monthly");
  const [status, setStatus] = useState<"active" | "cancelled" | "suspended">(
    "active"
  );
  useEffect(() => {
    setLoading(true);
    const response = getBillingDetails(
      {
        orgId: localStorage.getItem("orgId") || "",
      },
      dispatch
    );
    response
      .then((resData) => {
        const planName = resData?.data?.billingDetails?.plan;
        const billingCycle =
          resData?.data?.billingDetails?.billingCycle || "monthly";
        const status = resData?.data?.billingDetails?.status ?? "active";
        if (resData?.status === 200) {
          setCurrentPlan(planName);
          setBillingCycle(billingCycle);
          setStatus(status);
        }
      })
      .finally(() => {
        setLoading(false);
      });
  }, [router.query]);

  useEffect(() => {
    console.log({ billingCycle });
  }, [billingCycle]);
  if (loading) {
    return <PageLoadingSpinner />;
  }
  return (
    <>
      <BillingCycleChoose
        selected={billingCycle}
        onChangeFn={(val: TBillingCycle) => setBillingCycle(val)}
      />
      <Stack direction={"row"} gap={2}>
        {/* <BillingCard
        plan="Trial"
        price="FREE"
        features={planFeatures}
        active={true}
      /> */}
        <BillingCard
          plan="Starter"
          planId="trial"
          price={billingCycle === "monthly" ? "$0" : "$0"}
          features={features.starter}
          active={currentPlan === "trial" && status === "active"}
          billingCycle={billingCycle}
        />
        <BillingCard
          plan="Basic"
          planId="basic"
          price={billingCycle === "monthly" ? "$99" : "$83"}
          features={features.basic}
          active={currentPlan === "basic" && status === "active"}
          billingCycle={billingCycle}
        />
        <BillingCard
          plan="Pro"
          planId="pro"
          price={billingCycle === "monthly" ? "$149" : "$117"}
          features={features.pro}
          active={currentPlan === "pro" && status === "active"}
          billingCycle={billingCycle}
        />
        <BillingCard
          plan="Enterprise"
          planId="enterprise"
          price={billingCycle === "monthly" ? "$297" : "$216"}
          features={features.enterprise}
          active={currentPlan === "enterprise" && status === "active"}
          billingCycle={billingCycle}
        />
        {/* <BillingCard
        plan="Custom"
        planId="custom"
        price="Contact us"
        features={features.custom}
        active={currentPlan === "custom"}
      /> */}
      </Stack>
    </>
  );
};

export default Plans;
