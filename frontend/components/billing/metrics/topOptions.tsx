import * as React from "react";
import Box from "@mui/material/Box";
import InputLabel from "@mui/material/InputLabel";
import MenuItem from "@mui/material/MenuItem";
import FormControl from "@mui/material/FormControl";
import Select, { SelectChangeEvent } from "@mui/material/Select";

const TopOptions = () => {
  const [age, setAge] = React.useState("");

  const handleChange = (event: SelectChangeEvent) => {
    setAge(event.target.value as string);
  };
  return (
    <div className="flex justify-between">
      <div className="agency my-10">
        <Box
          sx={{
            minWidth: 190,
            color: "white",
            border: "2px solid white",
            borderRadius: "5px",
            background: "gray",
          }}
        >
          <FormControl fullWidth>
            <InputLabel
              id="demo-simple-select-label"
              sx={{
                color: "white",
                "&::before": {
                  borderColor: "transparent transparent red transparent",
                },
              }}
            >
              Choose account
            </InputLabel>
            <Select
              labelId="demo-simple-select-label"
              id="demo-simple-select"
              value={age}
              label="Choose account"
              onChange={handleChange}
            >
              <MenuItem value={10}>Ten</MenuItem>
              <MenuItem value={20}>Twenty</MenuItem>
              <MenuItem value={30}>Thirty</MenuItem>
            </Select>
          </FormControl>
        </Box>
      </div>
      <div className="dates text-white">
        {/* <LocalizationProvider dateAdapter={AdapterDayjs}>
          <DemoContainer components={["DateRangePicker"]}>
            <DateRangePicker localeText={{ start: "Start", end: "End" }} />
          </DemoContainer>
        </LocalizationProvider> */}
      </div>
    </div>
  );
};

export default TopOptions;
