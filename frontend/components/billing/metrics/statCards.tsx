import React from "react";

interface StatCardProps {
  header: string;
  color: string;
  data: string;
}

const StatCards: React.FC<StatCardProps> = ({ header, color, data }) => {
  return (
    <div
      className={`flex-grow-[1] text-fourth w-[350px]  border-[2px] border-gray-400 px-10 py-5 rounded-md bg-gradient-to-br from-emerald-200 to-emerald-400`}
    >
      <div className="header text-[32px] text-center font-bold text-gray-700">
        {header}
      </div>
      <div className="stat text-[42px] text-center mt-8 m-5 font-extrabold text-gray-700">
        {data}
      </div>
    </div>
  );
};

export default StatCards;
