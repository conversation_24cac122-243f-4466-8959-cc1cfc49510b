import React, { useState } from "react";
import Drawer from "@mui/material/Drawer";
import IconButton from "@mui/material/IconButton";
import EditIcon from "@mui/icons-material/Edit";
import { DialogTitle, Tooltip } from "@mui/material";
import { Member, rebillingActions } from "@/slices/rebilling/rebillingSlice";
import EditBillingMemberForm from "./EditBillingMemberForm";
import { useDispatch } from "react-redux";

interface IProp {
  member: Member;
}

const EditRebillingUser: React.FC<IProp> = ({ member }) => {
  const dispatch = useDispatch();
  const [open, setOpen] = useState(false);

  const handleClickOpen = () => {
    setOpen(true);
    dispatch(rebillingActions.editMemberAdded(member));
  };

  const handleClose = () => {
    setOpen(false);
  };

  return (
    <div>
      <Tooltip title="Edit details">
        <IconButton color="primary" onClick={handleClickOpen}>
          <EditIcon />
        </IconButton>
      </Tooltip>
      <Drawer anchor="right" open={open} onClose={handleClose}>
        <DialogTitle sx={{ paddingBottom: 0 }}>Edit Billing User</DialogTitle>
        <EditBillingMemberForm handleClose={handleClose} />
      </Drawer>
    </div>
  );
};

export default EditRebillingUser;
