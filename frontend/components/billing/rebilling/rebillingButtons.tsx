import React from "react";
import Button from "@mui/material/Button";
import Stack from "@mui/material/Stack";
import { IoIosAddCircleOutline } from "react-icons/io";
import { useRouter } from "next/router";

const RebillingButtons = () => {
  const router = useRouter();
  return (
    <div className="my-2">
      <div className="flex justify-end">
        <Button
          variant="contained"
          className="bg-blue-500"
          endIcon={<IoIosAddCircleOutline />}
          onClick={() => {
            router.push(router.asPath.replace("/view", ""));
          }}
        >
          Add new
        </Button>
      </div>
    </div>
  );
};

export default RebillingButtons;
