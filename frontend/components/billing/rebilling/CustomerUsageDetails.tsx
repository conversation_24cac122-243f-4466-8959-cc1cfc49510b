import React, { useEffect } from "react";
// import "react-date-range/dist/styles.css"; // main style file
// import "react-date-range/dist/theme/default.css"; // theme css file
import LineGraph from "@/components/d3/line-graph";
import RebillingStats from "./RebillingStats";
import { getMemberUsage } from "@/requests/rebilling/getMemberUsage";
import { useRouter } from "next/router";
import { useDispatch } from "react-redux";
import { Member } from "@/slices/rebilling/rebillingSlice";
import { ImSpinner8 } from "react-icons/im";
import { formatNumber } from "@/helpers/basic";

type Metrics = {
  numOfTokensUsed: number;
  cost: number;
  numOfMessagesSent: number;
  earned: number;
};

type MetricsWithDate = Metrics & {
  date: number;
};

interface IProp {
  member: Member;
}

const CustomerUsageDetails: React.FC<IProp> = ({ member }) => {
  const dispatch = useDispatch();
  const router = useRouter();
  const [loading, setLoading] = React.useState(true);
  const { id, from, to } = router.query as {
    id: string;
    from: string;
    to: string;
  };
  const [totalTokens, setTotalTokens] = React.useState(0);
  const [totalCost, setTotalCost] = React.useState(0);
  const [totalEarned, setTotalEarned] = React.useState(0);
  const [totalNumOfMessagesSent, setTotalNumOfMessagesSent] = React.useState(0);
  const [usage, setUsage] = React.useState<MetricsWithDate[]>([]);
  useEffect(() => {
    const memberUsage = async () => {
      setLoading(true);
      const response = await getMemberUsage({ id, from, to }, dispatch);
      if (response.status === 200) {
        setUsage(response.data);
        const totals: {
          totalNumOfTokensUsed: number;
          totalCost: number;
          totalNumOfMessagesSent: number;
          totalEarned: number;
        } = response.data.reduce(
          (
            totals: {
              totalNumOfTokensUsed: number;
              totalCost: number;
              totalNumOfMessagesSent: number;
              totalEarned: number;
            },
            item: MetricsWithDate
          ) => {
            totals.totalNumOfTokensUsed += item.numOfTokensUsed;
            totals.totalCost += item.cost;
            totals.totalNumOfMessagesSent += item.numOfMessagesSent;
            totals.totalEarned += item.earned;
            return totals;
          },
          {
            totalNumOfTokensUsed: 0,
            totalCost: 0,
            totalEarned: 0,
            totalNumOfMessagesSent: 0,
          }
        );
        setTotalTokens(totals.totalNumOfTokensUsed);
        setTotalCost(totals.totalCost);
        setTotalEarned(totals.totalEarned);
        setTotalNumOfMessagesSent(totals.totalNumOfMessagesSent);
      }
      setLoading(false);
    };
    memberUsage();
  }, [from, to]);
  if (loading) {
    return (
      <>
        <div className="flex justify-center mt-8">
          <ImSpinner8 className="animate-spin text-[32px]" />
        </div>
      </>
    );
  }
  return (
    <div className="flex flex-col gap-4">
      <div className="flex flex-col gap-4">
        <RebillingStats
          earned={formatNumber(totalEarned / 100)}
          cost={formatNumber(totalCost)}
          tokens={formatNumber(totalTokens)}
          numOfMessagesSent={formatNumber(totalNumOfMessagesSent, true)}
          charge_by={member?.charge_by}
        />
        <LineGraph data={usage} />
      </div>
    </div>
  );
};

export default CustomerUsageDetails;
