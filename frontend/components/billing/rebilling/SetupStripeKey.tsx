import React from "react";
import { But<PERSON>, Typography, Box } from "@mui/material";
import StripeKeyForm from "./StripeKeyForm";

const SetupStripeKey = () => {
  return (
    <Box
      display="flex"
      flexDirection="column"
      alignItems="center"
      justifyContent="center"
      py={6}
      gap={2}
      sx={{ bgcolor: "background.default", color: "text.primary" }}
    >
      <Typography variant="h5" component="div">
        To enable rebilling, please enter your Stripe API key.
      </Typography>
      <StripeKeyForm />
    </Box>
  );
};

export default SetupStripeKey;
