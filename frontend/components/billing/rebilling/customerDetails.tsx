import Option from "@/components/settings/crm_ai/option";
import React from "react";

const CustomerDetails = () => {
  return (
    <>
      <Option
        id="stripeName"
        name="Customer name"
        value=""
        summary="Some summary about the option"
        insertType="input"
        inputType="text"
        options={[]}
      />
      <hr />
      <Option
        id="chooseStripeCustomer"
        name="Choose stripe customer"
        value=""
        summary="Some summary about the option"
        insertType="select"
        inputType="text"
        options={[
          { label: "Person 1", value: "p1" },
          { label: "Person 2", value: "p2" },
        ]}
      />
      <hr />
      <Option
        id="stripeSubscriptions"
        name="Stripe subscriptions"
        value=""
        summary="Some summary about the option"
        insertType="select"
        inputType="text"
        options={[
          { label: "Person 1", value: "p1" },
          { label: "Person 2", value: "p2" },
        ]}
      />
      <hr />
      <Option
        id="unitSelection"
        name="Unit Selection"
        value=""
        summary="Some summary about the option"
        insertType="select"
        inputType="text"
        options={[
          { label: "Person 1", value: "p1" },
          { label: "Person 2", value: "p2" },
        ]}
      />
    </>
  );
};

export default CustomerDetails;
