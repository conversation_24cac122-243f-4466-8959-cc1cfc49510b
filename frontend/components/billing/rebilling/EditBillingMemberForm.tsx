import { updateMember } from "@/requests/rebilling/updateMember";
import { Member, rebillingActions } from "@/slices/rebilling/rebillingSlice";
import { RootState } from "@/store";
import {
  Button,
  FormControl,
  FormHelperText,
  Input,
  InputLabel,
  FormControlLabel,
  Switch,
  TextField,
  Typography,
  Autocomplete,
  Select,
  MenuItem,
  SelectChangeEvent,
  Alert,
} from "@mui/material";
import React, { FormEvent, useEffect } from "react";
import { ImSpinner8 } from "react-icons/im";
import { useDispatch, useSelector } from "react-redux";

interface IProp {
  handleClose: () => void;
}

const EditBillingMemberForm: React.FC<IProp> = ({ handleClose }) => {
  const dispatch = useDispatch();
  const editMember = useSelector(
    (state: RootState) => state.rebilling.editMember
  );
  const [loading, setLoading] = React.useState(false);
  const [state, setState] = React.useState({
    _id: "",
    nickname: "",
    channel_account_id: "",
    name: "",
    email: "",
    charge_by: "tokens" as Member["charge_by"],
    billing_unit: 1000,
    max_usage: ********,
    disabled: false,
  });
  useEffect(() => {
    if (editMember) {
      setState({
        _id: editMember._id || "",
        nickname: editMember.nickname || "",
        channel_account_id: editMember.channel_account_id || "",
        name: editMember.name || "",
        email: editMember.email || "",
        charge_by: editMember.charge_by || "tokens",
        billing_unit: editMember.billing_unit || 1000,
        max_usage: editMember.max_usage || 1000000,
        disabled: editMember.disabled || false,
      });
    }
  }, [editMember]);
  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    setState((prev) => ({ ...prev, [e.target.name]: e.target.value }));
  };

  const handleSelectChange = (e: SelectChangeEvent<Member["charge_by"]>) => {
    setState((prev) => ({ ...prev, [e.target.name || ""]: e.target.value }));
  };

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    setLoading(true);
    const response = await updateMember(state, dispatch);
    setLoading(false);
    if (response.status === 200) {
      dispatch(rebillingActions.memberUpdated(state));
      handleClose();
    }
  };
  if (!!!editMember) {
    return (
      <div className="flex justify-center">
        <ImSpinner8 className="animate-spin" />
      </div>
    );
  }
  return (
    <div className="w-[550px] py-4 px-6">
      <form onSubmit={handleSubmit} className="flex flex-col gap-4">
        <FormControl variant="standard">
          <InputLabel htmlFor="id-nickname">Nickname</InputLabel>
          <Input
            id="id-nickname"
            name="nickname"
            value={state.nickname}
            onChange={handleChange}
          />
        </FormControl>
        <FormControl variant="standard">
          <InputLabel htmlFor="id-name">Name</InputLabel>
          <Input
            id="id-name"
            name="name"
            value={state.name}
            onChange={handleChange}
          />
        </FormControl>
        <FormControl variant="standard">
          <InputLabel htmlFor="id-email">Email</InputLabel>
          <Input
            id="id-email"
            name="email"
            value={state.email}
            onChange={handleChange}
          />
        </FormControl>
        <FormControl variant="standard">
          <InputLabel id="label-charge-by">Chage by</InputLabel>
          <Select
            labelId="label-charge-by"
            id="demo-simple-select-standard"
            name="charge_by"
            value={state.charge_by}
            onChange={handleSelectChange}
            label="Chage by"
          >
            <MenuItem value={"tokens"}>Tokens</MenuItem>
            <MenuItem value={"message"}>Message</MenuItem>
          </Select>
        </FormControl>
        {state.charge_by === "tokens" && (
          <>
            <Alert severity="info">
              We will record 1 usage record in Stripe for every{" "}
              <b>{state?.billing_unit}</b> tokens used by this customer
            </Alert>
            <FormControl variant="standard">
              <InputLabel htmlFor="id-billing_unit">Billing unit</InputLabel>
              <Input
                id="id-billing_unit"
                name="billing_unit"
                type="number"
                disabled
                endAdornment={
                  <Typography variant="body1" px={2}>
                    tokens
                  </Typography>
                }
                value={state.billing_unit}
                onChange={handleChange}
              />
              <FormHelperText id="component-helper-billing_unit">
                This is your billing unit
              </FormHelperText>
            </FormControl>
            {/* <FormControl variant="standard">
              <InputLabel htmlFor="id-max_usage">Max usage</InputLabel>
              <Input
                id="id-max_usage"
                name="max_usage"
                type="number"
                endAdornment={
                  <Typography variant="body1" px={2}>
                    tokens
                  </Typography>
                }
                value={state.max_usage}
                onChange={handleChange}
              />
              <FormHelperText id="component-helper-max_usage">
                This is your max usage
              </FormHelperText>
            </FormControl> */}
          </>
        )}
        {state?.charge_by === "message" && (
          <>
            <Alert severity="info">
              We will record 1 usage record in Stripe for every message by the
              AI attached to this account
            </Alert>
          </>
        )}
        <FormControlLabel
          control={
            <Switch
              checked={state.disabled}
              onChange={(e) =>
                setState((prev) => ({ ...prev, disabled: e.target.checked }))
              }
              name="disabled"
              inputProps={{ "aria-label": "secondary checkbox" }}
            />
          }
          label="Disable"
        />
        {/* Add more fields as needed */}
        <Button type="submit" variant="contained" disabled={loading}>
          Update Member{" "}
          {loading && <ImSpinner8 className="ml-1 animate-spin" />}
        </Button>
      </form>
    </div>
  );
};

export default EditBillingMemberForm;
