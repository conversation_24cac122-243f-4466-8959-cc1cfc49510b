import { rebillingActions } from "@/slices/rebilling/rebillingSlice";
import React, { useEffect } from "react";
import { useDispatch } from "react-redux";
import { useRouter } from "next/router";
import Option from "@/components/settings/crm_ai/option";

const StripeKey = () => {
  const dispatch = useDispatch();
  let key: string = "";
  const router = useRouter();
  return (
    // <div>
    //   <div className=" text-[24px]">
    //     Enter your Stripe API key to enable rebilling
    //   </div>
    //   <div className="py-4">
    //     <input
    //       type="text"
    //       placeholder="sk-xxxxxx"
    //       className="w-full px-4 py-2 text-[22px] rounded-md shadow-md outline-none"
    //       name=""
    //       id=""
    //       value={key.length > 0 ? key : ""}
    //     />
    //   </div>
    //   <div className="flex justify-between">
    //     <div
    //       className="w-1/3 px-3 py-2 text-center font-normal text-[22px] bg-gray-400 rounded-lg shadow-lg mt-5 cursor-pointer"
    //       onClick={() => {
    //         router.push(router.asPath.replace("add", ""));
    //       }}
    //     >
    //       Back
    //     </div>
    //     <div
    //       className="w-1/3 px-3 py-2 text-center font-normal text-[22px] bg-third rounded-lg shadow-lg mt-5 cursor-pointer"
    //       onClick={() => {
    //         dispatch(rebillingActions.stageChanged(1));
    //       }}
    //     >
    //       Next
    //     </div>
    //   </div>
    // </div>
    <>
      <Option
        id="stripeKey"
        name="Stripe API key"
        value=""
        summary="Some summary about the option"
        insertType="input"
        inputType="text"
        options={[]}
      />
      <hr />
    </>
  );
};

export default StripeKey;
