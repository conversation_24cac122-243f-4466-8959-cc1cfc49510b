import React from "react";
import { Card, CardContent, Typography } from "@mui/material";

type RebillingStatCardProps = {
  title: string;
  value: string;
  description: string;
};

const RebillingStatCard: React.FC<RebillingStatCardProps> = ({
  title,
  value,
  description,
}) => {
  return (
    <Card>
      <CardContent>
        <Typography variant="h6" component="div">
          {title}
        </Typography>
        <Typography variant="h4" component="div">
          {value}
        </Typography>
        <Typography variant="body2" color="text.secondary">
          {description}
        </Typography>
      </CardContent>
    </Card>
  );
};

export default RebillingStatCard;
