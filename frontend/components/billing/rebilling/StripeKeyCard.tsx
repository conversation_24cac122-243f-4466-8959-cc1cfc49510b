import { Icon<PERSON>utton, Paper, Stack, <PERSON>lt<PERSON>, Typography } from "@mui/material";
import React from "react";
import RemoveRedEyeIcon from "@mui/icons-material/RemoveRedEye";
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from "@mui/icons-material/Delete";
import { useSelector } from "react-redux";
import { RootState } from "@/store";
import { FaStripeS } from "react-icons/fa6";
import { grey } from "@mui/material/colors";

const StripeKeyCard = () => {
  const stripe_api_key = useSelector(
    (state: RootState) => state.rebilling.rebilling?.stripe_api_key
  );
  const stripe_api_key_encrypted =
    stripe_api_key?.slice(0, 3) +
    "********************" +
    stripe_api_key?.slice(-3);
  return (
    <Stack
      component={Paper}
      direction={"row"}
      alignItems={"center"}
      justifyContent={"space-between"}
      p={2}
      borderRadius={2}
    >
      <Stack direction={"row"} gap={4} alignItems={"center"}>
        <Typography variant="h6" component="div">
          Stripe API key
        </Typography>
        <Typography variant="h6" component="div">
          {stripe_api_key_encrypted}
        </Typography>
      </Stack>
      <Stack direction={"row"} gap={2}>
        {/* <IconButton aria-label="view" color="secondary">
          <RemoveRedEyeIcon />
        </IconButton> */}
        <IconButton aria-label="edit" color="primary">
          <EditIcon />
        </IconButton>
        <IconButton aria-label="remove" color="error">
          <DeleteIcon />
        </IconButton>
      </Stack>
    </Stack>
  );
};

export default StripeKeyCard;
