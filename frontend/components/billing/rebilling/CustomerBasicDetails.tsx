import { Chip, Stack } from "@mui/material";
import React from "react";
import MailOutlineIcon from "@mui/icons-material/MailOutline";
import PersonOutlineIcon from "@mui/icons-material/PersonOutline";
import CalendarMonthIcon from "@mui/icons-material/CalendarMonth";
import { FaStripeS } from "react-icons/fa";
import DateSelector from "./DateSelector";
import FunctionsIcon from "@mui/icons-material/Functions";
import { capitalizeFirstLetter } from "@/helpers/text/stringMethods";
import ContactPhoneIcon from "@mui/icons-material/ContactPhone";
import TooltipItem from "./TooltipItem";

type CustomerBasicDetailsProps = {
  name: string;
  email: string;
  stripeCustomerId: string;
  dateJoined: string;
  disabled: boolean;
  charge_by: string;
  channel_account_name: string;
};

const CustomerBasicDetails: React.FC<CustomerBasicDetailsProps> = ({
  name,
  email,
  stripeCustomerId,
  dateJoined,
  disabled,
  charge_by,
  channel_account_name,
}) => {
  return (
    <Stack direction={"row"} justifyContent={"space-between"}>
      <Stack direction={"row"} gap={2} flexWrap={"wrap"} pr={2}>
        <TooltipItem
          title="Customer name"
          icon={<PersonOutlineIcon />}
          text={name}
        />
        <TooltipItem
          title="Customer email"
          icon={<MailOutlineIcon />}
          text={email}
        />
        <TooltipItem
          title="Stripe customer_id"
          icon={<FaStripeS />}
          text={stripeCustomerId}
        />
        <TooltipItem
          title="Date added"
          icon={<CalendarMonthIcon />}
          text={dateJoined}
        />
        <TooltipItem
          title="Charge by"
          icon={<FunctionsIcon />}
          text={capitalizeFirstLetter(charge_by)}
        />
        <TooltipItem
          title="Channel account name"
          icon={<ContactPhoneIcon />}
          text={channel_account_name}
        />
        <Stack direction={"row"} gap={1} alignItems={"center"}>
          <Chip
            color={disabled ? "error" : "success"}
            size="small"
            label={disabled ? "Disabled" : "Active"}
          />
        </Stack>
      </Stack>
      <DateSelector />
    </Stack>
  );
};

export default CustomerBasicDetails;
