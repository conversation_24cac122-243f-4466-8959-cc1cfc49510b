import React, { useState } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  TextField,
  DialogActions,
  Button,
  DialogContentText,
} from "@mui/material";
import { useDispatch } from "react-redux";
import { rollbackStripeKey } from "@/requests/rebilling/rollbackStripeKey";

interface IProp {
  id: string;
}

const RollStripeKeyDialog: React.FC<IProp> = ({ id }) => {
  const dispatch = useDispatch();
  const [open, setOpen] = useState(false);
  const [stripeKey, setStripeKey] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(false);

  const handleClickOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setStripeKey(event.target.value);
    setError(event.target.value === "");
  };

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    if (stripeKey === "") {
      setError(true);
    } else {
      console.log(stripeKey);
      setLoading(true);
      const response = await rollbackStripeKey(
        { id, stripe_api_key: stripeKey },
        dispatch
      );
      setLoading(false);
      if (response?.status === 200) {
        handleClose();
      }
    }
  };

  return (
    <div>
      <Button variant="contained" onClick={handleClickOpen}>
        Roll Stripe Key
      </Button>
      <Dialog open={open} fullWidth onClose={handleClose}>
        <DialogTitle>Roll Stripe Key</DialogTitle>
        <DialogContent>
          <DialogContentText>
            To roll back a Stripe API key, please enter the key you want to roll
            back to in the field below. Make sure this key is valid and has the
            necessary permissions.
          </DialogContentText>

          <form onSubmit={handleSubmit}>
            <TextField
              error={error}
              helperText={error && "Stripe key is required"}
              autoFocus
              margin="dense"
              id="stripeKey"
              label="Stripe Key"
              type="text"
              fullWidth
              value={stripeKey}
              onChange={handleChange}
            />
            <DialogActions>
              <Button onClick={handleClose}>Cancel</Button>
              <Button type="submit" variant="contained" disabled={loading}>
                Submit{loading && "ing..."}
              </Button>
            </DialogActions>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default RollStripeKeyDialog;
