import { Stack, Tooltip, Typography } from "@mui/material";
import { grey } from "@mui/material/colors";
import React from "react";

type TooltipItemProps = {
  title: string;
  icon: React.ReactElement;
  text?: string;
};

const TooltipItem: React.FC<TooltipItemProps> = ({ title, icon, text }) => {
  if (!!!text || text === "") {
    return null;
  }
  return (
    <Tooltip title={title} arrow placement="top">
      <Stack direction={"row"} gap={1} alignItems={"center"}>
        {React.cloneElement(icon, { fontSize: "small" })}
        <Typography variant="subtitle2" color={grey[400]}>
          {text}
        </Typography>
      </Stack>
    </Tooltip>
  );
};

export default TooltipItem;
