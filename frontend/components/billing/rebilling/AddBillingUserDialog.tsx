import React, { <PERSON><PERSON><PERSON>, useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Drawer,
  <PERSON><PERSON><PERSON><PERSON>,
  DialogContent,
  DialogTitle,
  FormControl,
  Autocomplete,
  Chip,
  Box,
  InputLabel,
  Input,
  Typography,
  FormHelperText,
  InputAdornment,
  Select,
  MenuItem,
  Alert,
} from "@mui/material";
import AddIcon from "@mui/icons-material/Add";
import { getAiProviders } from "@/requests/aiProvider/getAiProviders";
import { useDispatch, useSelector } from "react-redux";
import { getStripeProducts } from "@/requests/rebilling/getStripeProducts";
import { getChannelAccounts } from "@/requests/channels/getChannelAccounts";
import { addMember } from "@/requests/rebilling/addMember";
import { snackbarActions } from "@/slices/general/snackbar";
import {
  BillingProvider,
  rebillingActions,
} from "@/slices/rebilling/rebillingSlice";
import { ImSpinner8 } from "react-icons/im";
import { getStripePriceIds } from "@/requests/rebilling/getStripePriceIds";
import { RootState } from "@/store";
import { getAllTokens } from "@/requests/organizations/tokens/get";

interface Channel {
  name: string;
  // accountId: string;
  keyId: string;
}

interface Product {
  id: string;
  name: string;
  default_price: string;
}

interface Price {
  id: string;
  unit_amount: number;
  currency: string;
}

const AddBillingUserDialog = () => {
  const dispatch = useDispatch();
  const rebilling = useSelector((state: RootState) => state.rebilling);
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [channelLoading, setChannelLoading] = useState(false);
  const [nickname, setNickname] = useState("");
  const [customer, setCustomer] = useState("");
  const [chargeBy, setChargeBy] = useState("tokens"); // ["tokens", "amount"]
  const [billingUnit, setBillingUnit] = useState(1000);
  const [maxUsage, setMaxUsage] = useState(********);
  const [providers, setProviders] = useState<BillingProvider[]>(
    rebilling.providers
  );
  const [selectedProvider, setSelectedProvider] =
    useState<BillingProvider | null>(null);

  const [channels, setChannels] = useState<Channel[]>([]);
  const [selectedChannel, setSelectedChannel] = useState<Channel | null>(null);

  const [productsLoading, setProductsLoading] = useState(false);
  const [products, setProducts] = useState<Product[]>([]);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);

  const [prices, setPrices] = useState<Price[]>([]);
  const [selectedPrice, setSelectedPrice] = useState<Price | null>(null);

  useEffect(() => {
    const getChannels = async () => {
      setChannelLoading(true);
      const response = await getChannelAccounts(
        {
          orgId: localStorage.getItem("orgId") || "",
          provider: selectedProvider?.id,
        },
        dispatch
      );
      setChannelLoading(false);
      if (response?.status === 200) {
        const resData = response.data.channels;
        setChannels(resData);
      }
    };
    const getTokens = async () => {
      setChannelLoading(true);
      const response = await getAllTokens(
        { orgId: localStorage.getItem("orgId") || "" },
        dispatch
      );
      setChannelLoading(false);
      if (response?.status === 200) {
        const data = response.data.map(
          (item: { name: string; token: string }) => ({
            name: item.name,
            keyId: item.token,
          })
        );
        setChannels(data);
      }
    };
    setChannels([]);
    setSelectedChannel(null);
    if (selectedProvider?.id === "publicApi") {
      getTokens();
    } else {
      getChannels();
    }
  }, [selectedProvider]);

  useEffect(() => {
    const getStripeDetailsFetch = async () => {
      const response = await getStripeProducts(
        {
          orgId: localStorage.getItem("orgId") || "",
        },
        dispatch
      );
      console.log({ response });

      if (response?.status === 200) {
        const fetchedProducts: Product[] = response?.data?.data.map(
          (product: any) => ({
            id: product.id,
            name: product.name,
            default_price: product.default_price,
          })
        );

        setProducts(fetchedProducts);
      }
    };
    getStripeDetailsFetch();
  }, []);

  useEffect(() => {
    if (selectedProduct) {
      const getStripePricesFetch = async () => {
        setProductsLoading(true);
        const response = await getStripePriceIds(
          {
            orgId: localStorage.getItem("orgId") || "",
            productId: selectedProduct.id,
          },
          dispatch
        );
        setProductsLoading(false);

        if (response?.status === 200) {
          const fetchedPrices: Price[] = response?.data?.data
            .filter((price: any) => price?.recurring?.usage_type === "metered")
            .map((price: any) => ({
              id: price.id,
              unit_amount: price.unit_amount,
              currency: price.currency,
            }));

          setPrices(fetchedPrices);
        }
      };
      getStripePricesFetch();
    }
  }, [selectedProduct]);

  const handleClickOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  const handleSubmit = async (event: FormEvent) => {
    event.preventDefault();
    // handle the submission of the form
    const body = {
      orgId: localStorage.getItem("orgId") || "",
      nickname,
      stripe_customer_id: customer,
      billing_unit: billingUnit,
      max_usage: maxUsage,
      stripe_product_id: selectedProduct?.id as string,
      stripe_price_id: selectedPrice?.id as string,
      provider: selectedProvider?.id as BillingProvider["id"],
      channel_account_id: selectedChannel?.keyId as string,
      channel_account_name: selectedChannel?.name,
      charge_by: chargeBy,
    };
    setLoading(true);
    const response = await addMember(body, dispatch);
    setLoading(false);
    if (response?.status === 201) {
      dispatch(rebillingActions.memberAdded(response.data));
      handleClose();
      //reset the values
      setNickname("");
      setCustomer("");
      setChargeBy("tokens");
      setBillingUnit(1000);
      setSelectedProvider(null);
      setSelectedChannel(null);
      setSelectedProduct(null);
      setSelectedPrice(null);
    }
  };

  return (
    <div>
      <Button
        variant="contained"
        startIcon={<AddIcon />}
        onClick={handleClickOpen}
      >
        Add user
      </Button>
      <Drawer anchor="right" open={open} onClose={handleClose}>
        <DialogTitle sx={{ paddingBottom: 0 }}>Add Billing User</DialogTitle>
        <form onSubmit={handleSubmit}>
          <DialogContent
            sx={{
              display: "flex",
              flexDirection: "column",
              gap: 2,
              width: "550px",
            }}
          >
            <FormControl fullWidth margin="dense" required>
              <TextField
                variant="standard"
                label="Nickname"
                required
                id="customerId"
                type="text"
                value={nickname}
                onChange={(event) => setNickname(event.target.value)}
              />
            </FormControl>
            <FormControl fullWidth required>
              <Autocomplete
                id="provider"
                options={rebilling.providers}
                getOptionLabel={(option) => option.name}
                value={selectedProvider}
                onChange={(event, newValue) => {
                  setSelectedProvider(newValue);
                }}
                renderInput={(params) => (
                  <>
                    <TextField
                      {...params}
                      label="Select Provider"
                      variant="standard"
                      required
                    />
                  </>
                )}
              />
            </FormControl>
            <FormControl fullWidth required>
              <Autocomplete
                id="channel"
                options={channels}
                getOptionLabel={(option) => option.name}
                value={selectedChannel}
                onChange={(event, newValue) => {
                  setSelectedChannel(newValue);
                }}
                renderInput={(params) => (
                  <>
                    <TextField
                      {...params}
                      label={`Select ${
                        providers.find(
                          (item) => item.id === selectedProvider?.id
                        )?.name ?? ""
                      } Channel`}
                      variant="standard"
                      required
                      InputProps={{
                        ...params.InputProps,
                        endAdornment: (
                          <>
                            {channelLoading ? (
                              <ImSpinner8
                                className="animate-spin"
                                color="inherit"
                                size={20}
                              />
                            ) : null}
                            {params.InputProps.endAdornment}
                          </>
                        ),
                      }}
                    />
                  </>
                )}
              />
            </FormControl>

            <FormControl fullWidth margin="dense" required>
              <Autocomplete
                id="product"
                options={products}
                getOptionLabel={(option) => option.name}
                value={selectedProduct}
                onChange={(event, newValue) => {
                  setSelectedProduct(newValue);
                }}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label="Select Product"
                    variant="standard"
                    required
                  />
                )}
                renderOption={(props, option, { selected }) => (
                  <li {...props} key={option.id}>
                    <Box>
                      {option.name} <Chip label={option.id} size="small" />
                    </Box>
                  </li>
                )}
              />
            </FormControl>
            <FormControl fullWidth margin="dense" required>
              <Autocomplete
                id="price"
                options={prices}
                getOptionLabel={(option) =>
                  `${option.id} - ${option.unit_amount * 0.01} ${
                    option.currency
                  }`
                }
                value={selectedPrice}
                onChange={(event, newValue) => {
                  setSelectedPrice(newValue);
                }}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label="Select Price"
                    variant="standard"
                    required
                    InputProps={{
                      ...params.InputProps,
                      endAdornment: (
                        <>
                          {productsLoading ? (
                            <ImSpinner8
                              className="animate-spin"
                              color="inherit"
                              size={20}
                            />
                          ) : null}
                          {params.InputProps.endAdornment}
                        </>
                      ),
                    }}
                  />
                )}
                renderOption={(props, option, { selected }) => (
                  <li {...props} key={option.id}>
                    <Box>
                      {option.unit_amount} {option.currency}{" "}
                      <Chip label={option.id} size="small" />
                    </Box>
                  </li>
                )}
              />
            </FormControl>
            <FormControl fullWidth margin="dense" required>
              <TextField
                variant="standard"
                label="Stripe Customer ID"
                id="customerId"
                type="text"
                required
                value={customer}
                onChange={(event) => setCustomer(event.target.value)}
              />
            </FormControl>
            <FormControl variant="standard">
              <InputLabel id="label-charge-by">Chage by</InputLabel>
              <Select
                labelId="label-charge-by"
                id="demo-simple-select-standard"
                value={chargeBy}
                onChange={(e) => {
                  setChargeBy(e.target.value as string);
                }}
                label="Chage by"
              >
                <MenuItem value={"tokens"}>Tokens</MenuItem>
                <MenuItem value={"message"}>Message</MenuItem>
              </Select>
            </FormControl>
            {chargeBy === "tokens" && (
              <>
                <Alert severity="info">
                  We will record 1 usage record in Stripe for every{" "}
                  <b>{billingUnit} tokens</b> used by this customer
                </Alert>
                <FormControl fullWidth margin="dense" required>
                  <TextField
                    variant="standard"
                    label="Billing Unit"
                    id="billing-unit"
                    type="number"
                    // disabled
                    required
                    InputProps={{
                      endAdornment: (
                        <InputAdornment position="start">
                          <Typography variant="body1" px={2}>
                            tokens
                          </Typography>
                        </InputAdornment>
                      ),
                    }}
                    value={billingUnit}
                    onChange={(event) =>
                      setBillingUnit(Number(event.target.value))
                    }
                  />
                  <FormHelperText id="component-helper-max_usage">
                    This is your billing unit
                  </FormHelperText>
                </FormControl>
                {/* <FormControl fullWidth margin="dense" required>
                  <TextField
                    variant="standard"
                    label="Maximum usage"
                    id="max-usage"
                    type="number"
                    InputProps={{
                      endAdornment: (
                        <InputAdornment position="start">
                          <Typography variant="body1" px={2}>
                            tokens
                          </Typography>
                        </InputAdornment>
                      ),
                    }}
                    value={maxUsage}
                    onChange={(event) =>
                      setMaxUsage(Number(event.target.value))
                    }
                  />
                  <FormHelperText id="component-helper-max_usage">
                    This is your max usage
                  </FormHelperText>
                </FormControl> */}
              </>
            )}
            {chargeBy === "message" && (
              <>
                <Alert severity="info">
                  We will record 1 usage record in Stripe for every message by
                  the AI attached to this account
                </Alert>
              </>
            )}
          </DialogContent>
          <DialogActions>
            <Button onClick={handleClose}>Cancel</Button>
            <Button type="submit" variant="contained" disabled={loading}>
              Submit {loading && <ImSpinner8 className="ml-1 animate-spin" />}
            </Button>
          </DialogActions>
        </form>
      </Drawer>
    </div>
  );
};

export default AddBillingUserDialog;
