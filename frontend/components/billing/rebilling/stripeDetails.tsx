// import React, { useState } from "react";
// import Select, {
//   StylesConfig,
//   ActionMeta,
//   GroupBase,
//   SingleValue,
//   MultiValue,
// } from "react-select";
// import { useDispatch } from "react-redux";
// import { rebillingActions } from "@/slices/billing/rebillingSlice";

// const customStyles: StylesConfig<Option, boolean, GroupBase<Option>> = {
//   control: (provided) => ({
//     ...provided,
//     borderColor: "#ccc",
//     boxShadow: "none",
//     cursor: "pointer",
//     borderRadius: "5px",
//   }),
//   clearIndicator: (provided, { selectProps: { isDisabled } }) => ({
//     ...provided,
//     cursor: isDisabled ? "not-allowed" : "pointer", // Use selectProps to access isDisabled prop
//   }),
// };
// type Option = {
//   label: string;
//   value: string;
//   isDisabled?: boolean;
// };
// const StripeDetails = () => {
//   const dispatch = useDispatch();
//   const options: Option[] = [
//     { label: "Select an option...", value: "" },
//     { label: "Option 1", value: "option1" },
//     { label: "Option 2", value: "option2" },
//     { label: "Option 3", value: "option3" },
//   ];

//   return (
//     <>
//       <div>
//         <div className="">
//           <div className="heading">Name</div>
//           <div className="options">
//             <input className="w-full px-3 py-2 rounded-md outline-none" />
//           </div>
//         </div>
//         <div className="">
//           <div className="heading">Choose stripe customer</div>
//           <div className="options">
//             <Select options={options} styles={customStyles} />
//           </div>
//         </div>
//         <div className="">
//           <div className="heading">Stripe product</div>
//           <div className="options">
//             <Select options={options} styles={customStyles} />
//           </div>
//         </div>
//         <div className="">
//           <div className="heading">Unit selection</div>
//           <div className="options">
//             <Select options={options} styles={customStyles} />
//           </div>
//         </div>
//         <div className="flex justify-between">
//           <div
//             className="w-1/3 px-3 py-2 text-center font-normal text-[22px] bg-gray-400 rounded-lg shadow-lg mt-5 cursor-pointer"
//             onClick={() => {
//               dispatch(rebillingActions.stageChanged(0));
//             }}
//           >
//             Back
//           </div>
//           <div className="w-1/3 px-3 py-2 text-center font-normal text-[22px] bg-third rounded-lg shadow-lg mt-5 cursor-pointer">
//             Save
//           </div>
//         </div>
//       </div>
//       <style jsx>{`
//         .heading {
//           font-size: 24px;
//           margin: 4px 0;
//         }
//         .options {
//         }
//       `}</style>
//     </>
//   );
// };

// export default StripeDetails;
