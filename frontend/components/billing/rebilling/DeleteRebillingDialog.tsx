import React, { useState } from "react";
import {
  <PERSON>ton,
  <PERSON><PERSON>,
  <PERSON>alogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  TextField,
  FormHelperText,
} from "@mui/material";
import { useDispatch } from "react-redux";
import { rebillingActions } from "@/slices/rebilling/rebillingSlice";
import { useRouter } from "next/router";
import { deleteRebilling } from "@/requests/rebilling/deleteRebilling";

interface IProp {
  id: string;
}

const DeleteRebillingDialog: React.FC<IProp> = ({ id }) => {
  const dispatch = useDispatch();
  const router = useRouter();
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [input, setInput] = useState("");
  const [error, setError] = useState(false);

  const handleClickOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
    setError(false);
  };

  const handleDelete = async () => {
    if (input === "delete") {
      // Process the form action
      setLoading(true);
      const response = await deleteRebilling({ id }, dispatch);
      setLoading(false);
      if (response?.status === 200) {
        dispatch(rebillingActions.deleteRebilling());
        router.push("/home/<USER>/rebilling");
      }
    } else {
      setError(true);
      return;
    }
    setOpen(false);
  };

  return (
    <div>
      <Button
        variant="outlined"
        color="error"
        fullWidth
        onClick={handleClickOpen}
      >
        Delete Rebilling
      </Button>
      <Dialog open={open} onClose={handleClose}>
        <DialogTitle>Delete Rebilling</DialogTitle>
        <DialogContent>
          <DialogContentText>
            To delete this rebilling, please enter &quot;delete&quot; below.
            Please be aware that this action is irreversible.
          </DialogContentText>
          <TextField
            autoFocus
            margin="dense"
            id="name"
            label="Confirmation message"
            type="text"
            fullWidth
            value={input}
            onChange={(e) => setInput(e.target.value)}
            error={error}
          />
          {error && (
            <FormHelperText error>
              Incorrect confirmation message.
            </FormHelperText>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClose}>Cancel</Button>
          <Button
            onClick={handleDelete}
            variant="contained"
            color="error"
            disabled={loading}
          >
            {loading ? "Deleting..." : "Delete"}
          </Button>
        </DialogActions>
      </Dialog>
    </div>
  );
};

export default DeleteRebillingDialog;
