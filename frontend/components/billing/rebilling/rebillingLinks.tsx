import React from "react";
import Link from "next/link";
import { useRouter } from "next/router";
import { AiFillWarning } from "react-icons/ai";
import Tooltip from "@mui/material/Tooltip";

interface Options {
  buttonName: string;
  linkTo: string;
  warning: boolean;
}

const RebillingLinks: React.FC<Options> = ({ buttonName, linkTo, warning }) => {
  const router = useRouter();
  const isStripeKeyValid: boolean = true;
  return (
    <>
      {isStripeKeyValid ? (
        <Link
          href={router.asPath + linkTo}
          className="inline-block cursor-pointer"
        >
          <div className="w-fit flex items-center justify-between text-[18px] py-3 px-4 border-[2px] border-cyan-50 rounded-3xl hover:bg-secondary transition-all">
            <span>{buttonName}</span>
          </div>
        </Link>
      ) : (
        <Tooltip title="Add your Stripe API key first" placement="bottom">
          <div className="w-fit flex items-center justify-between  text-[18px] py-3 px-4 border-[2px] border-cyan-50 rounded-3xl hover:bg-secondary transition-all">
            <span>{buttonName}</span>
          </div>
        </Tooltip>
      )}
    </>
  );
};

export default RebillingLinks;
