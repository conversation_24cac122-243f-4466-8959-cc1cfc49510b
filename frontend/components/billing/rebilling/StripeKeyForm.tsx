import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>ield,
  <PERSON>alog,
  <PERSON>alog<PERSON><PERSON>,
  <PERSON>alogContent,
  DialogTitle,
  Slide,
  DialogContentText,
} from "@mui/material";
import { TransitionProps } from "@mui/material/transitions";
import { createRebilling } from "@/requests/rebilling/createRebilling";
import { useDispatch } from "react-redux";
import { rebillingActions } from "@/slices/rebilling/rebillingSlice";

const Transition = React.forwardRef(function Transition(
  props: TransitionProps & {
    children: React.ReactElement<any, any>;
  },
  ref: React.Ref<unknown>
) {
  return <Slide direction="up" ref={ref} {...props} />;
});

interface IProp {
  btnText?: string;
  saveExisitingData?: boolean;
}

const StripeKeyForm: React.FC<IProp> = ({
  btnText = "Enter Stripe Key",
  saveExisitingData = false,
}) => {
  const dispatch = useDispatch();
  const [stripeKey, setStripeKey] = React.useState("");
  const [open, setOpen] = React.useState(false);
  const [loading, setLoading] = React.useState(false);
  const handleClose = () => {
    setOpen(false);
  };
  const handleSubmit = async () => {
    // handle the submission of the stripe key
    console.log(stripeKey);
    setLoading(true);
    const response = await createRebilling(
      {
        organization: localStorage.getItem("orgId") || "",
        stripe_api_key: stripeKey,
      },
      dispatch
    );
    setLoading(false);
    if (response.status === 201) {
      handleClose();
      dispatch(rebillingActions.getRebillingSuccess(response.data));
      stripeKey.includes("_test") && dispatch(rebillingActions.setTest(true));
    }
  };

  return (
    <>
      <Button variant="contained" onClick={() => setOpen(true)}>
        {btnText}
      </Button>
      <Dialog
        open={open}
        onClose={handleClose}
        TransitionComponent={Transition}
        keepMounted
      >
        <DialogTitle>Enter Your Stripe Key</DialogTitle>
        <DialogContent>
          <DialogContentText id="alert-dialog-slide-description">
            Let us know your Stripe key to enable rebilling. You can find it in
            your Stripe dashboard.
          </DialogContentText>
          <TextField
            autoFocus
            margin="dense"
            id="stripeKey"
            label="Stripe Key"
            type="text"
            fullWidth
            value={stripeKey}
            onChange={(e) => setStripeKey(e.target.value)}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClose}>Cancel</Button>
          <Button disabled={loading} variant="contained" onClick={handleSubmit}>
            Submit{loading && "ting..."}
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default StripeKeyForm;
