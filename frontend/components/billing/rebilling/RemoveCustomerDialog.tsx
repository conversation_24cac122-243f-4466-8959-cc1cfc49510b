import React, { useState } from "react";
import {
  Button,
  <PERSON>alog,
  <PERSON>alogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  TextField,
  FormHelperText,
} from "@mui/material";
import { deleteMember } from "@/requests/rebilling/deleteMember";
import { useDispatch } from "react-redux";
import { rebillingActions } from "@/slices/rebilling/rebillingSlice";
import { useRouter } from "next/router";
import { ImSpinner8 } from "react-icons/im";

interface IProp {
  id: string;
}

const RemoveCustomerDialog: React.FC<IProp> = ({ id }) => {
  const dispatch = useDispatch();
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [open, setOpen] = useState(false);
  const [input, setInput] = useState("");
  const [error, setError] = useState(false);

  const handleClickOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
    setError(false);
  };

  const handleDelete = async () => {
    if (input === "delete") {
      // Process the form action
      setLoading(true);
      const response = await deleteMember({ id }, dispatch);
      setLoading(false);
      if (response?.status === 200) {
        dispatch(rebillingActions.memberDeleted(id));
        router.push("/home/<USER>/rebilling");
      }
    } else {
      setError(true);
      return;
    }
    setOpen(false);
  };

  return (
    <div>
      <Button variant="contained" color="error" onClick={handleClickOpen}>
        Delete User
      </Button>
      <Dialog open={open} onClose={handleClose}>
        <DialogTitle>Delete User</DialogTitle>
        <DialogContent>
          <DialogContentText>
            To delete this user, please enter &quot;delete&quot; below. Please
            be aware that this action is irreversible.
          </DialogContentText>
          <TextField
            autoFocus
            margin="dense"
            id="name"
            label="Confirmation message"
            type="text"
            fullWidth
            value={input}
            onChange={(e) => setInput(e.target.value)}
            error={error}
          />
          {error && (
            <FormHelperText error>
              Incorrect confirmation message.
            </FormHelperText>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClose}>Cancel</Button>
          <Button
            onClick={handleDelete}
            variant="contained"
            color="error"
            disabled={loading}
          >
            Delete {loading && <ImSpinner8 className="animate-spin ml-2" />}
          </Button>
        </DialogActions>
      </Dialog>
    </div>
  );
};

export default RemoveCustomerDialog;
