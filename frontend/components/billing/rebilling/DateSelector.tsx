import { Box, Button, Popover, Typography } from "@mui/material";
import { useRouter } from "next/router";
import React, { useState } from "react";
import { DateRange } from "react-date-range";
import { addDays, endOfDay, startOfDay } from "date-fns";

// Import the CSS for react-date-range
import "react-date-range/dist/styles.css";
import "react-date-range/dist/theme/default.css";

const DateSelector = () => {
  const router = useRouter();
  const { from, to } = router.query as {
    from: string;
    to: string;
  };

  // Initialize date state
  const [dateRange, setDateRange] = useState({
    startDate: from
      ? new Date(Number(from))
      : startOfDay(addDays(new Date(), -7)),
    endDate: to ? new Date(Number(to)) : endOfDay(new Date()),
    key: "selection",
  });

  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleRangeChange = (ranges: any) => {
    setDateRange(ranges.selection);
  };

  const handleApply = () => {
    const startDate = dateRange.startDate.getTime();
    const endDate = dateRange.endDate.getTime();

    delete router.query.from;
    delete router.query.to;
    router.push(
      {
        query: {
          ...router.query,
          from: startDate,
          to: endDate,
        },
      },
      undefined,
      { shallow: true }
    );
    handleClose();
  };

  const formatDate = (date: Date) => {
    const options: Intl.DateTimeFormatOptions = {
      year: "numeric",
      month: "long",
      day: "numeric",
    };
    return date.toLocaleDateString(undefined, options);
  };

  // Create predefined ranges
  const staticRanges = [
    {
      label: "Today",
      range: () => ({
        startDate: startOfDay(new Date()),
        endDate: endOfDay(new Date()),
      }),
    },
    {
      label: "Yesterday",
      range: () => ({
        startDate: startOfDay(addDays(new Date(), -1)),
        endDate: endOfDay(addDays(new Date(), -1)),
      }),
    },
    {
      label: "Last 7 Days",
      range: () => ({
        startDate: startOfDay(addDays(new Date(), -6)),
        endDate: endOfDay(new Date()),
      }),
    },
    {
      label: "Last 30 Days",
      range: () => ({
        startDate: startOfDay(addDays(new Date(), -29)),
        endDate: endOfDay(new Date()),
      }),
    },
    {
      label: "Last 90 Days",
      range: () => ({
        startDate: startOfDay(addDays(new Date(), -89)),
        endDate: endOfDay(new Date()),
      }),
    },
  ];

  const buttonText = `${formatDate(dateRange.startDate)} - ${formatDate(
    dateRange.endDate
  )}`;

  return (
    <div className="min-w-fit">
      <Button variant="outlined" color="inherit" onClick={handleClick}>
        {buttonText}
      </Button>
      <Popover
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleClose}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "left",
        }}
        transformOrigin={{
          vertical: "top",
          horizontal: "left",
        }}
      >
        <Box sx={{ p: 2, bgcolor: "white" }}>
          <DateRange
            ranges={[dateRange]}
            onChange={handleRangeChange}
            months={1}
            direction="horizontal"
            moveRangeOnFirstSelection={false}
            rangeColors={["#2196f3"]}
          />
          <Box sx={{ display: "flex", justifyContent: "flex-end", mt: 2 }}>
            <Button onClick={handleClose} sx={{ mr: 1 }}>
              Cancel
            </Button>
            <Button variant="contained" onClick={handleApply}>
              Apply
            </Button>
          </Box>
        </Box>
      </Popover>
    </div>
  );
};

export default DateSelector;
