import ClearFilters from "@/components/general/ClearFilters";
import FilterMenu from "@/components/general/FilterMenu";
import { RootState } from "@/store";
import { Clear } from "@mui/icons-material";
import React from "react";
import { useSelector } from "react-redux";

const RebillingFilterOptions = () => {
  const rebillingProviders = useSelector(
    (state: RootState) => state.rebilling.providers
  );
  return (
    <div className="flex gap-2">
      <FilterMenu
        options={[
          {
            id: "active",
            name: "Active",
          },
          {
            id: "disabled",
            name: "Disabled",
          },
          {
            id: "all",
            name: "All",
          },
        ]}
        propertyName="status"
        btnText="Status"
      />
      <FilterMenu
        options={rebillingProviders}
        propertyName="provider"
        btnText="Provider"
      />
      <ClearFilters queryParams={["provider", "status"]} />
    </div>
  );
};

export default RebillingFilterOptions;
