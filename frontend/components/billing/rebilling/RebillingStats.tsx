import React from "react";
import RebillingStatCard from "./RebillingStatCard";
import { Grid } from "@mui/material";
import { Member } from "@/slices/rebilling/rebillingSlice";

interface IProp {
  tokens: string;
  cost: string;
  earned: string; //in cents
  numOfMessagesSent: string;
  charge_by: Member["charge_by"];
}

const RebillingStats: React.FC<IProp> = ({
  earned,
  cost,
  tokens,
  numOfMessagesSent,
  charge_by,
}) => {
  console.log({ earned, cost, tokens });

  return (
    <Grid container spacing={1}>
      <Grid item xs={12} sm={12} md={3}>
        <RebillingStatCard
          title="Tokens"
          value={String(tokens)}
          description="Total number of tokens used"
        />
      </Grid>
      <Grid item xs={12} sm={12} md={3}>
        <RebillingStatCard
          title="Earned"
          value={`$${String(earned)}`}
          description="Total amount earned"
        />
      </Grid>
      <Grid item xs={12} sm={12} md={3}>
        <RebillingStatCard
          title="Costs"
          value={`$${String(cost)}`}
          description="Total cost of tokens used"
        />
      </Grid>
      <Grid item xs={12} sm={12} md={3}>
        <RebillingStatCard
          title="Messages"
          value={`${String(numOfMessagesSent)}`}
          description="Total messages sent"
        />
      </Grid>
      {/* <Grid item xs={12} sm={12} md={6} lg={4}>
        <RebillingStatCard
          title="Charge by"
          value={charge_by[0]?.toUpperCase() + charge_by?.slice(1)}
          description="Charging method"
        />
      </Grid> */}
    </Grid>
  );
};

export default RebillingStats;
