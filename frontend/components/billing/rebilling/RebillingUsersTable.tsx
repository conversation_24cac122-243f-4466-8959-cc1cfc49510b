import SearchBar from "@/components/organization-tab/general/SearchBar";
import {
  Box,
  Button,
  Chip,
  IconButton,
  Tooltip,
  Typography,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableCell from "@mui/material/TableCell";
import TableContainer from "@mui/material/TableContainer";
import TableHead from "@mui/material/TableHead";
import TableRow from "@mui/material/TableRow";
import Paper from "@mui/material/Paper";
import InfoOutlinedIcon from "@mui/icons-material/InfoOutlined";
import AddBillingUserDialog from "./AddBillingUserDialog";
import NoRebillingUserCard from "./NoRebillingUserCard";
import { useRouter } from "next/router";
import { useSelector } from "react-redux";
import { RootState } from "@/store";
import EditRebillingUser from "./EditRebillingUser";
import Image from "next/image";
import { logos } from "@/helpers/images";
import RebillingFilterOptions from "./RebillingFilterOptions";

const RebillingUsersTable = () => {
  const { pathname } = useRouter();
  const router = useRouter();
  const { status = "all", provider = "all" } = router.query as {
    status: string;
    provider: string;
  };
  const [search, setSearch] = useState("");
  const members = useSelector(
    (state: RootState) => state.rebilling.rebilling?.members
  );
  const [filteredMembers, setFilteredMembers] = useState(members);
  const providers = useSelector(
    (state: RootState) => state.rebilling.providers
  );

  useEffect(() => {
    console.log({ status, provider, search });
  }, [status, provider, search]);

  useEffect(() => {
    setFilteredMembers(
      members?.filter((member) => {
        const lowerCaseSearch = search.toLowerCase();
        console.log(
          status !== "all" && (status === "disabled") !== member.disabled,
          provider !== "all" && provider !== member.provider
        );
        // Filter based on status
        if (status !== "all" && (status === "disabled") !== member.disabled) {
          return false;
        }

        // Filter based on provider
        if (provider !== "all" && provider !== member.provider) {
          return false;
        }

        // Search functionality
        return (
          member.nickname?.toLowerCase().includes(lowerCaseSearch) ||
          member.name?.toLowerCase().includes(lowerCaseSearch) ||
          member.email?.toLowerCase().includes(lowerCaseSearch) ||
          member.stripe_customer_id?.toLowerCase().includes(lowerCaseSearch)
        );
      })
    );
  }, [members, status, provider, search]);
  return (
    <div className="flex flex-col gap-2">
      <div className="flex justify-between">
        <div className="flex gap-2">
          <div className="w-[250px]">
            <SearchBar
              val={search}
              onchangeFn={(x: string) => setSearch(x)}
              searchText="Search users..."
            />
          </div>
          <RebillingFilterOptions />
        </div>
        <AddBillingUserDialog />
      </div>
      {members?.length === 0 && <NoRebillingUserCard />}
      {filteredMembers && filteredMembers?.length > 0 && (
        <TableContainer component={Paper}>
          <Table sx={{ minWidth: 650 }} aria-label="simple table">
            <TableHead>
              <TableRow>
                <TableCell></TableCell>
                <TableCell>Nickname</TableCell>
                <TableCell>Customer</TableCell>
                <TableCell>Email</TableCell>
                <TableCell>Account</TableCell>
                <TableCell>Customer ID</TableCell>
                <TableCell>Status</TableCell>
                <TableCell></TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {!!filteredMembers &&
                filteredMembers.map((row, index) => (
                  <TableRow
                    key={row?._id + index}
                    sx={{ "&:last-child td, &:last-child th": { border: 0 } }}
                  >
                    <TableCell component="th" scope="row">
                      <Box
                        display="flex"
                        justifyContent="center"
                        alignItems="center"
                      >
                        <Tooltip
                          title={
                            providers.find((item) => item.id === row.provider)
                              ?.name ?? "No provider"
                          }
                        >
                          <Image
                            src={
                              row.provider
                                ? "/squareLogos" + logos[row.provider]?.logo
                                : ""
                            }
                            width={25}
                            height={25}
                            alt={row.provider ?? "Logo"}
                          />
                        </Tooltip>
                      </Box>
                    </TableCell>
                    <TableCell component="th" scope="row">
                      {row.nickname || "No nickname"}
                    </TableCell>
                    <TableCell component="th" scope="row">
                      {row.name ?? "No name"}
                    </TableCell>
                    <TableCell>{row?.email ?? "No email"}</TableCell>
                    <TableCell>
                      {row?.channel_account_name ?? "No name"}
                    </TableCell>
                    <TableCell>{row.stripe_customer_id}</TableCell>
                    {/* <TableCell>{row.channel_account_id}</TableCell> */}
                    <TableCell>
                      <Chip
                        variant="outlined"
                        color={row.disabled ? "error" : "success"}
                        size="small"
                        label={row.disabled ? "Disabled" : "Active"}
                      />
                    </TableCell>

                    <TableCell align="right" sx={{ display: "flex" }}>
                      <Tooltip title="More Info">
                        <IconButton
                          aria-label="more-info"
                          color="info"
                          href={pathname + `/details/${row?._id}`}
                        >
                          <InfoOutlinedIcon />
                        </IconButton>
                      </Tooltip>

                      <EditRebillingUser member={row} />
                    </TableCell>
                  </TableRow>
                ))}
            </TableBody>
          </Table>
        </TableContainer>
      )}
    </div>
  );
};

export default RebillingUsersTable;
