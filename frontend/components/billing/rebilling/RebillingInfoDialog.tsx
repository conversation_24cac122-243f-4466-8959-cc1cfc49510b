import {
  Icon<PERSON>utton,
  Dialog,
  DialogTitle,
  DialogContent,
  Typography,
  Box,
  Stack,
} from "@mui/material";
import InfoOutlinedIcon from "@mui/icons-material/InfoOutlined";
import React, { useState } from "react";
import { useSelector } from "react-redux";
import { RootState } from "@/store";
import DeleteRebillingDialog from "./DeleteRebillingDialog";
import StripeKeyForm from "./StripeKeyForm";
import RollStripeKeyDialog from "./RollStripeKeyDialog";

const RebillingInfoDialog = () => {
  const rebilling = useSelector(
    (state: RootState) => state.rebilling.rebilling
  );
  const stripe_api_key_encrypted =
    rebilling?.stripe_api_key && rebilling?.stripe_api_key.includes("_test")
      ? "sk_test_**************" + rebilling?.stripe_api_key.slice(-3)
      : "sk_live_**************" + rebilling?.stripe_api_key.slice(-3);
  const [open, setOpen] = useState(false);

  const handleClickOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  if (!!!rebilling) return null;
  return (
    <div>
      <IconButton
        aria-label="rebilling-details"
        size="large"
        color="inherit"
        onClick={handleClickOpen}
      >
        <InfoOutlinedIcon />
      </IconButton>
      <Dialog open={open && !!rebilling} onClose={handleClose}>
        <DialogTitle>Rebilling Details</DialogTitle>
        <DialogContent
          sx={{ display: "flex", flexDirection: "column", gap: 2 }}
        >
          <Box
            sx={{
              display: "flex",
              justifyContent: "space-between",
              width: "100%",
            }}
          >
            <Box sx={{ width: 150 }}>
              <Typography variant="body1">Stripe API Key:</Typography>
            </Box>
            <Typography variant="body1">{stripe_api_key_encrypted}</Typography>
          </Box>
          <Box
            sx={{
              display: "flex",
              justifyContent: "space-between",
              width: "100%",
            }}
          >
            <Box sx={{ width: 150 }}>
              <Typography variant="body1">Created At:</Typography>
            </Box>
            <Typography variant="body1">
              {new Date(rebilling?.createdAt as string).toLocaleString()}
            </Typography>
          </Box>
          <Box
            sx={{
              display: "flex",
              justifyContent: "space-between",
              width: "100%",
            }}
          >
            <Box sx={{ width: 150 }}>
              <Typography variant="body1">Last updated:</Typography>
            </Box>
            <Typography variant="body1">
              {new Date(rebilling?.updatedAt as string).toLocaleString()}
            </Typography>
          </Box>
          {rebilling?._id && (
            <>
              <Stack direction="row" spacing={2}>
                <DeleteRebillingDialog id={rebilling?._id} />
                <RollStripeKeyDialog id={rebilling?._id} />
              </Stack>
              {/* <StripeKeyForm btnText="Change Stripe key" /> */}
            </>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default RebillingInfoDialog;
