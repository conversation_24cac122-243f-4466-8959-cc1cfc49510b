import React, { FormEvent } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, CircularProgress } from "@mui/material";
import HttpUrl from "../http/HttpUrl";
import HttpTab from "../http/HttpTab";
import HttpTabContent from "../http/HttpTabContent";
import HttpParams from "../http/HttpParams";
import HttpAuthorization from "../http/HttpAuthorization";
import HttpHeaders from "../http/HttpHeaders";
import HttpResponse from "../http/HttpResponse";
import HttpBody from "../http/HttpBody";
import { ImSpinner8 } from "react-icons/im";
import HttpName from "../http/HttpName";
import useHttpPostIntegration from "@/hooks/useHttpPostIntegration";

export type Tab = "Params" | "Authorization" | "Headers" | "Body" | "Response";

export interface IHttpResponse {
  status: number;
  statusText: string;
  headers: Record<string, string>;
  data: any;
}

export interface IHttpPostRequest {
  name: string;
  url: string;
  queryParameters: {
    name: string;
    value: string;
    description: string;
  }[];
  pathVariables: {
    name: string;
    value: string;
    description: string;
  }[];
  headers: {
    name: string;
    value: string;
    description: string;
  }[];
  bodyParameters: {
    name: string;
    value: string;
    description: string;
  }[];
  bodyType: "application/json" | "application/x-www-form-urlencoded";
  authorizationType: "No Auth" | "API Key" | "OAuth 2.0";
  apiKey?: {
    key: string;
    value: string;
    addTo: "Header" | "Query";
  };
  responseBuilder: string;
}

interface IProp {
  accountId?: string;
}

const HttpPostComponent: React.FC<IProp> = ({ accountId }) => {
  const {
    httpRequest,
    setHttpRequest,
    currentTab,
    setCurrentTab,
    response,
    loading,
    saveLoading,
    error,
    initialLoading,
    handleSendRequest,
    handleSubmit,
  } = useHttpPostIntegration(accountId);

  const onSubmit = async (e: FormEvent) => {
    e.preventDefault();
    await handleSubmit();
  };

  if (initialLoading) {
    return (
      <div className="w-full flex justify-center mt-10">
        <CircularProgress />
      </div>
    );
  }

  return (
    <form onSubmit={onSubmit}>
      <div className="w-[900px]">
        <HttpName
          name={httpRequest.name}
          onNameChange={(name: string) =>
            setHttpRequest({ ...httpRequest, name })
          }
        />
        <HttpUrl
          type="POST"
          url={httpRequest.url}
          loading={loading}
          urlOnChange={(url) => setHttpRequest({ ...httpRequest, url })}
          onFire={handleSendRequest}
        />
        <HttpTab
          currentTab={currentTab}
          onTabChange={(tab) => setCurrentTab(tab)}
          showBodyTab={true}
        />
        <HttpTabContent />
        {currentTab === "Params" && (
          <HttpParams
            url={httpRequest.url}
            queryParams={httpRequest.queryParameters}
            pathVariables={httpRequest.pathVariables}
            onQueryParamChange={(queryParam) =>
              setHttpRequest({ ...httpRequest, queryParameters: queryParam })
            }
            onPathVariableChange={(pathVariable) =>
              setHttpRequest({ ...httpRequest, pathVariables: pathVariable })
            }
          />
        )}
        {currentTab === "Authorization" && (
          <HttpAuthorization
            authorizationType={httpRequest.authorizationType}
            apiKey={httpRequest.apiKey}
            onAuthorizationTypeChange={(authType) =>
              setHttpRequest({ ...httpRequest, authorizationType: authType })
            }
            onApiKeyChange={(newApiKey) =>
              setHttpRequest({ ...httpRequest, apiKey: newApiKey })
            }
          />
        )}
        {currentTab === "Headers" && (
          <HttpHeaders
            headers={httpRequest.headers}
            onHeadersChange={(header) =>
              setHttpRequest({ ...httpRequest, headers: header })
            }
          />
        )}
        {currentTab === "Body" && (
          <HttpBody
            bodyParameters={httpRequest.bodyParameters}
            bodyType={httpRequest.bodyType}
            onBodyParametersChange={(bodyParams) =>
              setHttpRequest({ ...httpRequest, bodyParameters: bodyParams })
            }
            onBodyTypeChange={(bodyType) =>
              setHttpRequest({ ...httpRequest, bodyType: bodyType })
            }
          />
        )}
        {currentTab === "Response" && (
          <HttpResponse
            response={response}
            responseBuilder={httpRequest.responseBuilder}
            setResponseBuilder={(responseBuilder) =>
              setHttpRequest({
                ...httpRequest,
                responseBuilder: responseBuilder,
              })
            }
          />
        )}
      </div>
      {error && <div className="text-red-500 mt-2">{error}</div>}
      <Stack justifyContent="end" direction="row" mt={2}>
        <Button
          variant="contained"
          sx={{ width: "fit-content" }}
          type="submit"
          disabled={saveLoading}
        >
          Save {saveLoading && <ImSpinner8 className="animate-spin ml-2" />}
        </Button>
      </Stack>
    </form>
  );
};

export default HttpPostComponent; 