import { Box, Typography } from "@mui/material";
import React from "react";
import HttpPostComponent from "./HttpPostComponent";

interface IProp {
  accountId: string;
}

const HttpPostDrawer: React.FC<IProp> = ({ accountId }) => {
  return (
    <>
      <Typography variant="h5" paddingY={2} paddingX={4}>
        Edit integration
      </Typography>
      <Box px={4}>
        <HttpPostComponent accountId={accountId} />
      </Box>
    </>
  );
};

export default HttpPostDrawer; 