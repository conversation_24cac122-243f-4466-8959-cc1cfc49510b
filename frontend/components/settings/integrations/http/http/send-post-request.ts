import axios from "axios";
import { IHttpPostRequest } from "../httpPost/HttpPostComponent";
import { Dispatch } from "redux";
import { snackbarActions } from "@/slices/general/snackbar";

export async function sendPostRequest(
  httpRequest: IHttpPostRequest,
  dispatch: Dispatch
) {
  // Replace path variables in the URL
  let url = httpRequest.url;
  httpRequest.pathVariables.forEach((variable) => {
    url = url.replace(`:${variable.name}`, variable.value);
  });

  // Add query parameters to the URL
  const urlObj = new URL(url);
  httpRequest.queryParameters.forEach((param) => {
    urlObj.searchParams.append(param.name, param.value);
  });

  // Add API key to the URL or headers
  let headers = httpRequest.headers.reduce<{ [key: string]: string }>(
    (obj, item) => {
      obj[item.name] = item.value;
      return obj;
    },
    {}
  );

  // Prepare request body
  let requestBody: string | FormData;
  if (httpRequest.bodyType === "application/json") {
    headers["Content-Type"] = "application/json";
    const bodyObject: { [key: string]: string } = {};
    httpRequest.bodyParameters.forEach((param) => {
      if (param.name && param.value) {
        bodyObject[param.name] = param.value;
      }
    });
    requestBody = JSON.stringify(bodyObject);
  } else {
    headers["Content-Type"] = "application/x-www-form-urlencoded";
    const formData = new URLSearchParams();
    httpRequest.bodyParameters.forEach((param) => {
      if (param.name && param.value) {
        formData.append(param.name, param.value);
      }
    });
    requestBody = formData.toString();
  }

  if (httpRequest.apiKey && httpRequest.authorizationType === "API Key") {
    if (httpRequest.apiKey.addTo === "Query") {
      urlObj.searchParams.append(
        httpRequest.apiKey.key,
        httpRequest.apiKey.value
      );
    } else if (httpRequest.apiKey.addTo === "Header") {
      headers = {
        ...headers,
        [httpRequest.apiKey.key]: httpRequest.apiKey.value,
      };
    }
  }

  try {
    console.log({ headers, requestBody });

    const response = await axios.post(urlObj.toString(), requestBody, { headers });
    console.log(response.data);
    return response;
  } catch (error: any) {
    console.error(error);
    dispatch(
      snackbarActions.setSnackbar({
        message: error?.message ?? "Error occurred while firing the POST request",
        type: "error",
      })
    );
  }
} 