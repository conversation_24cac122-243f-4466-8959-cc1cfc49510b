import React from "react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>ack,
  Box,
  TextField,
  Button,
  IconButton,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
} from "@mui/material";
import DeleteIcon from "@mui/icons-material/Delete";
import AddIcon from "@mui/icons-material/Add";

interface IHttpParameter {
  name: string;
  description: string;
  value: string;
}

interface IHttpBodyProps {
  bodyParameters: IHttpParameter[];
  bodyType: "application/json" | "application/x-www-form-urlencoded";
  onBodyParametersChange: (bodyParams: IHttpParameter[]) => void;
  onBodyTypeChange: (bodyType: "application/json" | "application/x-www-form-urlencoded") => void;
}

const HttpBody: React.FC<IHttpBodyProps> = ({
  bodyParameters,
  bodyType,
  onBodyParametersChange,
  onBodyTypeChange,
}) => {
  const handleParameterChange = (
    index: number,
    field: keyof IHttpParameter,
    value: string
  ) => {
    const updatedParams = [...bodyParameters];
    updatedParams[index] = { ...updatedParams[index], [field]: value };
    onBodyParametersChange(updatedParams);
  };

  const addParameter = () => {
    onBodyParametersChange([
      ...bodyParameters,
      { name: "", description: "", value: "" },
    ]);
  };

  const removeParameter = (index: number) => {
    const updatedParams = bodyParameters.filter((_, i) => i !== index);
    onBodyParametersChange(updatedParams);
  };

  return (
    <Box mt={2}>
      <Stack spacing={2}>
        <Typography variant="h6">Request Body</Typography>
        
        <FormControl size="small" sx={{ minWidth: 300 }}>
          <InputLabel>Body Type</InputLabel>
          <Select
            value={bodyType}
            label="Body Type"
            onChange={(e) => onBodyTypeChange(e.target.value as "application/json" | "application/x-www-form-urlencoded")}
          >
            <MenuItem value="application/json">JSON (application/json)</MenuItem>
            <MenuItem value="application/x-www-form-urlencoded">Form-encoded (application/x-www-form-urlencoded)</MenuItem>
          </Select>
        </FormControl>

        <Typography variant="subtitle2" color="textSecondary">
          Body Parameters:
        </Typography>

        {bodyParameters.map((param, index) => (
          <Box key={index} border="1px solid #e0e0e0" borderRadius={2} p={2}>
            <Stack spacing={2}>
              <Stack direction="row" spacing={2} alignItems="center">
                <TextField
                  label="Parameter Name"
                  value={param.name}
                  onChange={(e) =>
                    handleParameterChange(index, "name", e.target.value)
                  }
                  size="small"
                  sx={{ flex: 1 }}
                />
                <TextField
                  label="Value"
                  value={param.value}
                  onChange={(e) =>
                    handleParameterChange(index, "value", e.target.value)
                  }
                  size="small"
                  sx={{ flex: 1 }}
                />
                <IconButton
                  onClick={() => removeParameter(index)}
                  color="error"
                  size="small"
                >
                  <DeleteIcon />
                </IconButton>
              </Stack>
              <TextField
                label="Description"
                value={param.description}
                onChange={(e) =>
                  handleParameterChange(index, "description", e.target.value)
                }
                size="small"
                multiline
                rows={2}
                fullWidth
              />
            </Stack>
          </Box>
        ))}

        <Button
          variant="outlined"
          startIcon={<AddIcon />}
          onClick={addParameter}
          sx={{ alignSelf: "flex-start" }}
        >
          Add Body Parameter
        </Button>

        <Typography variant="body2" color="textSecondary">
          {bodyType === "application/json" 
            ? "Parameters will be sent as JSON in the request body with Content-Type: application/json"
            : "Parameters will be sent as form data with Content-Type: application/x-www-form-urlencoded"
          }
        </Typography>
        
        <Typography variant="body2" color="primary" sx={{ fontStyle: "italic" }}>
          💡 Sample API: {bodyType === "application/json" 
            ? "Try the example above with JSONPlaceholder to create a test user"
            : "Form data is useful for traditional web forms and file uploads"
          }
        </Typography>
      </Stack>
    </Box>
  );
};

export default HttpBody; 