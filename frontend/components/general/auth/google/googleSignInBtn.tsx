import { integrationModalActions } from "@/slices/settings/integrations/integrationList";
import { useRouter } from "next/router";
import React, { useEffect, useMemo, useState } from "react";
import { useDispatch } from "react-redux";
import ChooseCalendar from "./ChooseCalendar";
import CalendarAdvancedSettings from "./CalendarAdvancedSettings";
import { Checkbox, FormControlLabel } from "@mui/material";
import Link from "next/link";
import { useGoogleCalendarRedirectUrl } from "@/hooks/useGoogleCalendarRedirectUrl";
import { useGoogleSignInPopup } from "@/hooks/useGoogleSignInPopup";

export type Calendar = {
  id: string;
  summary: string;
  timeZone: string;
  accessRole: "none" | "freeBusyReader" | "reader" | "writer" | "owner";
};

export enum DayOfWeek {
  Monday = "Monday",
  Tuesday = "Tuesday",
  Wednesday = "Wednesday",
  Thursday = "Thursday",
  Friday = "Friday",
  Saturday = "Saturday",
  Sunday = "Sunday",
}

export type CalendarAdvancedDetails = {
  eventName: string;
  eventDuration: number;
  eventLocation: string;
  locationValue: string;
  eventDescription: string;
  dateRange: number; //number of days in future which it can book
  availableHours: Array<{
    day: DayOfWeek;
    timeSlots: Array<{
      start: string;
      end: string;
    }>;
  }>;
  acceptTnc: boolean;
  // startTimeIncrements: number;
};

export const defaultGoogleCalendarAdvancedDetails = {
  eventName: "",
  eventDuration: 15,
  eventLocation: "",
  locationValue: "",
  eventDescription: "",
  dateRange: 30,
  availableHours: [],
  acceptTnc: true,
};

const GoogleSignInBtn = () => {
  const router = useRouter();
  const dispatch = useDispatch();
  const { googleAuth, currentStep } = router.query;
  const orgId = useMemo(() => localStorage.getItem("orgId") || "", []);
  const { redirectUrl, loading } = useGoogleCalendarRedirectUrl(orgId);
  const [dataFromChild, setDataFromChild] = useState<Calendar[]>([]);
  const [calendar, setCalendar] = useState<Calendar | null>(null);
  const openPopup = useGoogleSignInPopup(setDataFromChild);

  const [checked, setChecked] = useState<boolean>(false);
  const [calendarAdvancedDetails, setCalendarAdvancedDetails] =
    useState<CalendarAdvancedDetails>(defaultGoogleCalendarAdvancedDetails);

  useEffect(() => {
    if (dataFromChild.length) {
      dispatch(integrationModalActions.changeModalClosedStatus(true));
    }
  }, [dataFromChild, dispatch]);

  const handleGoogleLogin = () => {
    if (redirectUrl) {
      openPopup(redirectUrl);
    }
  };

  const handleCheckboxChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setChecked(event.target.checked);
  };

  return (
    <>
      {dataFromChild.length > 0 &&
      googleAuth === "success" &&
      currentStep === "calendar-choose" ? (
        <ChooseCalendar
          calendars={dataFromChild}
          onCalendarSelect={(id: string) =>
            setCalendar(
              dataFromChild.find((calendar) => calendar.id === id) ?? null
            )
          }
        />
      ) : dataFromChild.length > 0 &&
        googleAuth === "success" &&
        currentStep === "timeslot" ? (
        <CalendarAdvancedSettings
          calendar={calendar}
          details={calendarAdvancedDetails}
          onDetailsChange={setCalendarAdvancedDetails}
        />
      ) : (
        <div className="login flex flex-col justify-center my-3">
          <FormControlLabel
            control={
              <Checkbox
                checked={checked}
                onChange={handleCheckboxChange}
                color="primary"
              />
            }
            label={
              <>
                I acknowledge that my free/busy information from Google Calendar
                will be shared with AI tools. See our{" "}
                <Link
                  href={`${process.env.NEXT_PUBLIC_FRONTEND_URL}/privacy`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="underline"
                >
                  Privacy Policy
                </Link>{" "}
                for details. To stop sharing, remove this calendar integration
                from your organization settings.
              </>
            }
          />
          <button
            className="google-login-button"
            disabled={loading || !checked}
            onClick={handleGoogleLogin}
          >
            <span className="google-icon"></span>
            <span className="button-text">Sign in with Google</span>
          </button>
        </div>
      )}
      <style jsx>{`
        .google-login-button {
          display: flex;
          align-items: center;
          justify-content: center;
          background-color: #fff;
          color: #757575;
          border: 1px solid #dbdbdb;
          border-radius: 4px;
          padding: 8px 16px;
          font-size: 14px;
          cursor: pointer;
          transition: background-color 0.3s;
        }

        .google-login-button:hover {
          background-color: #f1f1f1;
        }

        .google-login-button:disabled {
          background-color: #dbdbdb;
          color: #aaa;
          cursor: not-allowed;
        }

        .google-icon {
          background-image: url("https://www.gstatic.com/firebasejs/ui/2.0.0/images/auth/google.svg");
          background-size: cover;
          width: 18px;
          height: 18px;
          margin-right: 8px;
        }

        .button-text {
          font-weight: 500;
        }
      `}</style>
    </>
  );
};

export default GoogleSignInBtn;
