import {
  Box,
  Checkbox,
  FormControl,
  FormControlLabel,
  IconButton,
  InputAdornment,
  Stack,
  TextField,
  Typography,
  styled,
  Button,
} from "@mui/material";
import React, { useState } from "react";
import AccessTimeIcon from "@mui/icons-material/AccessTime";
import DeleteIcon from "@mui/icons-material/Delete";
import AddIcon from "@mui/icons-material/Add";
import { capitalizeFirstLetter } from "@/helpers/basic";
import { CalendarAdvancedDetails, DayOfWeek } from "./googleSignInBtn";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { TimePicker } from "@mui/x-date-pickers/TimePicker";
import dayjs from "dayjs";

interface IProp {
  availableHours: CalendarAdvancedDetails["availableHours"];
  setAvailableHours: (x: CalendarAdvancedDetails["availableHours"]) => void;
}

interface TimeSlot {
  start: string;
  end: string;
}

const HighlightedBox = styled(Box)(({ theme }) => ({
  backgroundColor: theme.palette.action.selected,
}));

const TimeSlotPicker: React.FC<IProp> = ({
  availableHours,
  setAvailableHours,
}) => {
  const defaultTimeSlot = {
    start: "09:00",
    end: "17:00",
  };
  const daysOfWeek = Object.values(DayOfWeek);

  const handleCheckboxChange = (
    event: React.ChangeEvent<HTMLInputElement>,
    day: DayOfWeek
  ) => {
    if (event.target.checked) {
      setAvailableHours([
        ...availableHours,
        {
          day,
          timeSlots: [{ ...defaultTimeSlot }],
        },
      ]);
    } else {
      setAvailableHours(availableHours.filter((elem) => elem.day !== day));
    }
  };

  const addTimeSlot = (day: DayOfWeek) => {
    const newAvailableHours = availableHours.map((elem) =>
      elem.day === day
        ? {
            ...elem,
            timeSlots: [...elem.timeSlots, { ...defaultTimeSlot }],
          }
        : elem
    );
    setAvailableHours(newAvailableHours);
  };

  const removeTimeSlot = (day: DayOfWeek, slotIndex: number) => {
    const newAvailableHours = availableHours
      .map((elem) => {
        if (elem.day === day) {
          const newTimeSlots = elem.timeSlots.filter(
            (_, index) => index !== slotIndex
          );
          return newTimeSlots.length === 0
            ? undefined
            : { ...elem, timeSlots: newTimeSlots };
        }
        return elem;
      })
      .filter((elem): elem is NonNullable<typeof elem> => elem !== undefined);

    setAvailableHours(newAvailableHours);
  };

  const updateTimeSlot = (
    day: DayOfWeek,
    slotIndex: number,
    field: keyof TimeSlot,
    value: string
  ) => {
    const newAvailableHours = availableHours.map((elem) =>
      elem.day === day
        ? {
            ...elem,
            timeSlots: elem.timeSlots.map((slot, idx) =>
              idx === slotIndex ? { ...slot, [field]: value } : slot
            ),
          }
        : elem
    );
    setAvailableHours(newAvailableHours);
  };

  return (
    <HighlightedBox sx={{ p: 2 }}>
      <Typography variant="h5" gutterBottom>
        Available Hours
      </Typography>
      <Stack direction="column" spacing={2}>
        {daysOfWeek.map((day) => {
          const daySchedule = availableHours.find((elem) => elem.day === day);
          const isChecked = !!daySchedule;

          return (
            <Stack
              direction="column"
              spacing={2}
              key={day}
              sx={{ borderBottom: 1, borderColor: "divider", pb: 2 }}
            >
              <Stack
                direction="row"
                alignItems="center"
                justifyContent="space-between"
              >
                <FormControl sx={{ width: "120px" }}>
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={isChecked}
                        onChange={(e) => handleCheckboxChange(e, day)}
                      />
                    }
                    label={capitalizeFirstLetter(day).slice(0, 3).toUpperCase()}
                  />
                </FormControl>
                {isChecked && (
                  <Button
                    startIcon={<AddIcon />}
                    size="small"
                    onClick={() => addTimeSlot(day)}
                    variant="outlined"
                  >
                    Add Time Slot
                  </Button>
                )}
              </Stack>

              {!isChecked && (
                <Typography variant="body1" color="text.secondary">
                  Unavailable
                </Typography>
              )}

              {daySchedule?.timeSlots.map((timeSlot, slotIndex) => (
                <Stack
                  key={slotIndex}
                  direction="row"
                  alignItems="center"
                  spacing={2}
                  sx={{ ml: 4 }}
                >
                  <LocalizationProvider dateAdapter={AdapterDayjs}>
                    <TimePicker
                      value={dayjs(`2022-04-17T${timeSlot.start}`)}
                      onChange={(newValue) =>
                        updateTimeSlot(
                          day,
                          slotIndex,
                          "start",
                          newValue?.format("HH:mm") || timeSlot.start
                        )
                      }
                      slotProps={{ textField: { size: "small" } }}
                    />
                    <Typography>to</Typography>
                    <TimePicker
                      value={dayjs(`2022-04-17T${timeSlot.end}`)}
                      onChange={(newValue) =>
                        updateTimeSlot(
                          day,
                          slotIndex,
                          "end",
                          newValue?.format("HH:mm") || timeSlot.end
                        )
                      }
                      slotProps={{ textField: { size: "small" } }}
                    />
                  </LocalizationProvider>
                  <IconButton
                    size="small"
                    onClick={() => removeTimeSlot(day, slotIndex)}
                    sx={{
                      visibility:
                        daySchedule.timeSlots.length > 1 ? "visible" : "hidden",
                    }}
                  >
                    <DeleteIcon fontSize="small" />
                  </IconButton>
                </Stack>
              ))}
            </Stack>
          );
        })}
      </Stack>
    </HighlightedBox>
  );
};

export default TimeSlotPicker;
