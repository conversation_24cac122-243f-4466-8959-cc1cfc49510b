import React, { useEffect, useState } from "react";
import { useRouter } from "next/router";
import SearchBar from "../organization-tab/general/SearchBar";
import { <PERSON><PERSON>, <PERSON> } from "@mui/material";

import AgentSort from "./AgentSort";
import StatusSort from "./StatusSort";
import FilterConversationStatus from "./FilterConversationStatus";
import CloseIcon from "@mui/icons-material/Close";

const ConversationsSort = () => {
  const [searchValue, setSearchValue] = useState("");
  const router = useRouter();
  const { agent, channel, status } = router.query;

  useEffect(() => {
    if (channel) {
      setSearchValue(channel as string);
    } else {
      setSearchValue("");
    }
  }, [channel]);

  const getFilteredConversations = () => {
    const { query } = router;
    if (searchValue) {
      query.channel = searchValue;
    } else {
      delete query.channel;
    }
    router.push({
      pathname: router.pathname,
      query,
    });
  };

  const resetFilters = () => {
    const { query } = router;
    delete query.agent;
    delete query.status;
    delete query.channel;

    router.push({
      pathname: router.pathname,
      query,
    });
  };

  useEffect(() => {}, [searchValue]);

  const handleKeyPress = (event: React.KeyboardEvent) => {
    if (event.key === "Enter") {
      getFilteredConversations();
    }
  };

  return (
    <div className="flex gap-2 mb-2">
      <SearchBar
        val={searchValue}
        onchangeFn={(x: string) => setSearchValue(x)}
        searchText={"Search channels..."}
        onKeyPress={handleKeyPress}
      />
      {!!channel && (
        <Chip
          variant="filled"
          color="primary"
          label={channel}
          onDelete={() => {
            const { query } = router;
            delete query.channel;
            router.push({
              pathname: router.pathname,
              query,
            });
            setSearchValue("");
          }}
        />
      )}
      <AgentSort />
      <FilterConversationStatus />
      {(!!agent || !!channel || !!status) && (
        <Button
          color="inherit"
          startIcon={<CloseIcon />}
          onClick={resetFilters}
        >
          Reset
        </Button>
      )}
    </div>
  );
};

export default ConversationsSort;
