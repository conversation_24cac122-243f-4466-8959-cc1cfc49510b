import * as React from "react";
import Button from "@mui/material/Button";
import Menu from "@mui/material/Menu";
import MenuItem from "@mui/material/MenuItem";
import AddCircleOutlineIcon from "@mui/icons-material/AddCircleOutline";
import { useRouter } from "next/router";
import { getOrgAgents } from "@/requests/agents/basic/getAgent";
import { useDispatch } from "react-redux";
import { ImSpinner8 } from "react-icons/im";

export default function AgentSort() {
  const dispatch = useDispatch();
  const router = useRouter();
  const { agent: agentId } = router.query;
  const [loading, setLoading] = React.useState(false);
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const [selectedAgent, setSelectedAgent] = React.useState<{
    _id: string;
    agentName: string;
  }>();
  const [agentList, setAgentList] = React.useState<
    { _id: string; agentName: string }[]
  >([]);
  const open = Boolean(anchorEl);
  React.useEffect(() => {
    const orgId = localStorage.getItem("orgId");
    if (!orgId) {
      return;
    }
    
    setLoading(true);
    getOrgAgents(
      { orgId, onlyName: true },
      dispatch
    ).then((res) => {
      setLoading(false);
      setAgentList(res.data.data);
    }).catch((error) => {
      setLoading(false);
      console.error('Failed to load agents:', error);
    });
  }, []);

  React.useEffect(() => {
    if (agentId) {
      const agent = agentList.find((agent) => agent._id === agentId);
      console.log("This is the agent, ", agent, agentId);

      setSelectedAgent(agent);
    } else {
      setSelectedAgent(undefined);
    }
  }, [agentId, agentList]);
  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };
  const changeAgentIdQueryParam = (agentId: string) => {
    router.push({
      pathname: router.pathname,
      query: { ...router.query, agent: agentId },
    });
    const agent = agentList.find((agent) => agent._id === agentId);
    setSelectedAgent(agent);
  };

  return (
    <div>
      <Button
        variant="outlined"
        color="inherit"
        startIcon={<AddCircleOutlineIcon />}
        id="basic-button"
        aria-controls={open ? "basic-menu" : undefined}
        aria-haspopup="true"
        aria-expanded={open ? "true" : undefined}
        onClick={handleClick}
        style={{ textTransform: "none" }}
      >
        Agent{" "}
        {loading && !!agentId && (
          <p className="animate-pulse ml-1">| Loading...</p>
        )}{" "}
        {selectedAgent ? " | " + selectedAgent.agentName : null}
      </Button>
      <Menu
        id="basic-menu"
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        MenuListProps={{
          "aria-labelledby": "basic-button",
        }}
      >
        {loading && (
          <MenuItem className="flex justify-center" disabled>
            <ImSpinner8 className="animate-spin" />
          </MenuItem>
        )}
        {!!!loading &&
          agentList.map((agent) => (
            <MenuItem
              onClick={() => {
                changeAgentIdQueryParam(agent._id);
                handleClose();
              }}
              key={agent._id}
            >
              {agent.agentName || "No name"}
            </MenuItem>
          ))}
      </Menu>
    </div>
  );
}
