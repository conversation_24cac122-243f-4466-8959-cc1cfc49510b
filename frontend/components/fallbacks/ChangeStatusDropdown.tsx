import * as React from "react";
import Button from "@mui/material/Button";
import Menu from "@mui/material/Menu";
import MenuItem from "@mui/material/MenuItem";
import { Chip, Divider, Typography } from "@mui/material";
import { RolloverStatus } from "./ConversationsTable";
import { MenuItemContent } from "./FilterConversationStatus";
import { changeRolloverStatus } from "@/requests/rollovers/changeRolloverStatus";
import { useDispatch } from "react-redux";
import RefreshIcon from "@mui/icons-material/Refresh";

export default function ChangeStatusDropdown({
  rollover,
  contactId,
  onChange,
}: {
  rollover: boolean;
  contactId: string;
  onChange: (status: "rollover" | "replied") => void;
}) {
  const dispatch = useDispatch();
  const [loading, setLoading] = React.useState(false);
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);
  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };
  const switchStatus = async (status: "rollover" | "replied") => {
    console.log(status);
    const set = status === "rollover"; //set is true if status is rollover
    setLoading(true);
    handleClose();
    const response = await changeRolloverStatus({ contactId, set }, dispatch);
    setLoading(false);
    if (response?.status === 200) {
      onChange(status);
    }
  };
  const handleClose = () => {
    setAnchorEl(null);
  };

  return (
    <div>
      <Chip
        component={Button}
        color={rollover ? "warning" : "success"}
        sx={{ textTransform: "none" }}
        label={!!loading ? "Loading..." : rollover ? "Rollover" : "Replied"}
        id="basic-rollover-chip"
        aria-controls={open ? "basic-menu" : undefined}
        aria-haspopup="true"
        aria-expanded={open ? "true" : undefined}
        onClick={handleClick}
      />
      <Menu
        id="basic-menu"
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        MenuListProps={{
          "aria-labelledby": "basic-button",
        }}
      >
        <MenuItemContent
          text="Rollover"
          show={!!rollover}
          onClick={() => {
            switchStatus("rollover");
          }}
        />
        <MenuItemContent
          text="Replied"
          show={!!!rollover}
          onClick={() => {
            switchStatus("replied");
          }}
        />
      </Menu>
    </div>
  );
}
