import React, { useEffect, useState } from "react";
import {
  DataGrid,
  GridColDef,
  GridCellParams,
  GridRenderCellParams,
} from "@mui/x-data-grid";
import {
  createTheme,
  ThemeProvider,
  Button,
  IconButton,
  Tooltip,
  Chip,
} from "@mui/material";
import { grey } from "@mui/material/colors";
import { logos } from "@/helpers/images";
import Image from "next/image";
import { AiFillCheckCircle } from "react-icons/ai";
import { MdCancel } from "react-icons/md";
import { useRouter } from "next/router";
import Link from "next/link";
import { getAll } from "@/requests/fallbacks/basic/getAll";
import NoFallbackFound from "../NoFallbackFound";
import { useDispatch } from "react-redux";
import { updateStatus } from "@/requests/fallbacks/basic/updateStatus";
import CheckCircleOutlineIcon from "@mui/icons-material/CheckCircleOutline";
import CancelOutlinedIcon from "@mui/icons-material/CancelOutlined";
import { ImSpinner8 } from "react-icons/im";
import InfoIcon from "@mui/icons-material/Info";

export interface IFallbackData {
  _id: string;
  providerName: string;
  dateTime: string;
  contactMessage: string;
  aiResponse: string;
  agentName: string;
  orgId: string;
  agentId: string;
  refUrl: string;
  status: string;
}

const darkTheme = createTheme({
  palette: {
    mode: "light",
  },
});

export default function FallbacksTable() {
  const dispatch = useDispatch();
  const router = useRouter();
  const { asPath, pathname } = router;

  const [paginationModel, setPaginationModel] = useState({
    page: 0,
    pageSize: 7,
  });
  const [rowCount, setRowCount] = useState(0);
  const [fallbacks, setFallbacks] = useState<IFallbackData[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    console.log({ paginationModel });

    async function fetchFallbacks() {
      try {
        setLoading(true);
        const response = await getAll(
          {
            orgId: localStorage.getItem("orgId") || "",
            offset: paginationModel.page * paginationModel.pageSize,
            limit: paginationModel.pageSize,
          },
          dispatch
        );
        setLoading(false);
        if (response?.status === 200) {
          setFallbacks(response.data.data);
          setRowCount(response.data.total);
        }
      } catch (error: any) {
        console.error(
          "There was a problem with the fetch operation:",
          error.message
        );
      }
    }

    fetchFallbacks();
  }, [paginationModel]);

  const fallbackUpdated = async (data: {
    fallbackId: string;
    status: "acknowledged" | "rejected";
  }) => {
    const { fallbackId, status } = data;
    const response = await updateStatus({ fallbackId, status }, dispatch);
    if (response?.status === 201) {
      // setFallbacks((prevFallbacks) =>
      //   prevFallbacks.filter((fallback) => fallback._id !== fallbackId)
      // );
      setFallbacks((prevFallbacks) =>
        prevFallbacks.map((fallback) => {
          if (fallback._id === fallbackId) {
            return { ...fallback, status };
          }
          return fallback;
        })
      );
    }
  };

  const columns: GridColDef[] = [
    {
      field: "providerName",
      headerName: "Provider",
      renderCell: (params: GridRenderCellParams) => (
        <div className="flex items-center gap-2">
          <Image
            alt="Something"
            width={100}
            height={100}
            title={logos[params.value as string]?.name || "No name"}
            src={logos[params.value as string]?.logo || ""}
            className="w-[70px] h-auto"
          />
          {/* {logos[params.value as string]?.name} */}
        </div>
      ),
    },
    {
      field: "dateTime",
      headerName: "Time",
      width: 160,
      renderCell: (params: GridRenderCellParams) => {
        const epochTime = params.value as number;
        const date = new Date(epochTime * 1000); //convert to ms
        const formattedDateTime = date.toLocaleString();
        return <div className="line-clamp-2">{formattedDateTime}</div>;
      },
    },
    {
      field: "agentName",
      headerName: "Agent",
      flex: 1,
      renderCell: (params: GridRenderCellParams) => {
        return (
          <Tooltip
            title={params.row.agentName || params.row.agentId || "No agent"}
          >
            <div className="truncate">
              <Link href={"/home/<USER>/" + params.row.agentId}>
                {params.row.agentName || params.row.agentId || "No agent"}
              </Link>
            </div>
          </Tooltip>
        );
      },
    },
    {
      field: "contactMessage",
      headerName: "Contact Message",
      flex: 1,
      renderCell: (params: GridRenderCellParams) => {
        return (
          <Tooltip title={params.row.contactMessage}>
            <div className="truncate">{params.row.contactMessage}</div>
          </Tooltip>
        );
      },
    },
    {
      field: "aiResponse",
      headerName: "AI Response",
      flex: 1,
      renderCell: (params: GridRenderCellParams) => {
        return (
          <Tooltip title={params.row.aiResponse}>
            <div className="truncate">{params.row.aiResponse}</div>
          </Tooltip>
        );
      },
    },
    {
      field: "status",
      headerName: "Status",
      flex: 1,
      renderCell: (params: GridRenderCellParams) => {
        const val = params.value as string;
        const color =
          val === "acknowledged"
            ? "success"
            : val === "rejected"
            ? "error"
            : "info";
        const icon =
          val === "acknowledged" ? (
            <CheckCircleOutlineIcon />
          ) : val === "rejected" ? (
            <CancelOutlinedIcon />
          ) : (
            <></>
          );
        return (
          // <Chip label={val} color={color} variant="outlined" icon={icon} />
          <div className="">{val.slice(0, 1).toUpperCase() + val.slice(1)}</div>
        );
      },
    },
    {
      field: "actions",
      headerName: "Actions",
      width: 180,
      renderCell: (params: GridRenderCellParams) => {
        return (
          <div>
            <Tooltip title="Acknowledge">
              <IconButton aria-label="acknowledge" size="large" color="success">
                <AiFillCheckCircle
                  fontSize="inherit"
                  onClick={() => {
                    fallbackUpdated({
                      fallbackId: params.row._id,
                      status: "acknowledged",
                    });
                  }}
                />
              </IconButton>
            </Tooltip>
            <Tooltip title="Reject">
              <IconButton aria-label="reject" size="large" color="error">
                <MdCancel
                  fontSize="inherit"
                  onClick={() => {
                    fallbackUpdated({
                      fallbackId: params.row._id,
                      status: "rejected",
                    });
                  }}
                />
              </IconButton>
            </Tooltip>
            <Tooltip title="More info">
              <IconButton
                aria-label="info"
                size="large"
                color="primary"
                href={pathname + "/" + params.id}
              >
                <InfoIcon fontSize="inherit" />
              </IconButton>
            </Tooltip>
          </div>
        );
      },
    },
  ];

  return (
    <>
      {fallbacks?.length !== 0 && (
        <ThemeProvider theme={darkTheme}>
          <div
            style={{
              width: "100%",
              backgroundColor: darkTheme.palette.background.default,
            }}
          >
            <DataGrid
              getRowId={(row) => row._id}
              rows={fallbacks}
              columns={columns}
              paginationMode="server"
              rowCount={rowCount}
              loading={loading}
              pageSizeOptions={[2]}
              paginationModel={paginationModel}
              onPaginationModelChange={setPaginationModel}
            />
          </div>
        </ThemeProvider>
      )}
      {loading === true && (
        <div className="flex justify-center mt-20">
          <ImSpinner8 className="animate-spin h-10 w-10" />
        </div>
      )}
      {loading === false && fallbacks?.length === 0 && <NoFallbackFound />}
    </>
  );
}
