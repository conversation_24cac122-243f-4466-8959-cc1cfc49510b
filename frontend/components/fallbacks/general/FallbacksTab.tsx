import React from "react";
import { <PERSON>, Chip, Container, Stack, Tab, Tabs, Tooltip } from "@mui/material";
import { useRouter } from "next/router";
import { FaCrown } from "react-icons/fa6";

const FallbacksTab = () => {
  const router = useRouter();
  const { show } = router.query;
  const value = show === "training" ? 1 : 0;

  const handleChange = (event: React.ChangeEvent<{}>, newValue: number) => {
    router.push({
      query: { show: newValue === 1 ? "training" : "conversations" },
    });
  };
  return (
    <Box sx={{ borderBottom: 1, borderColor: "divider" }}>
      <Tabs
        value={value}
        onChange={handleChange}
        aria-label="basic tabs example"
        sx={{
          "& .MuiTab-root": { color: "white" },
          "& .Mui-selected": { color: "#2e89ff" },
          "& .Mui-disabled": { color: "#b3b3b3" }, // Changed color to a brighter grey
        }}
      >
        <Tab
          label={
            <>
              <Stack direction={"row"} alignItems={"center"} gap={1}>
                Conversations{" "}
                <Chip
                  size="small"
                  label="Pro"
                  color="warning"
                  icon={<FaCrown />}
                />
              </Stack>
            </>
          }
        />
        <Tab label="Training" />
      </Tabs>
    </Box>
  );
};

export default FallbacksTab;
