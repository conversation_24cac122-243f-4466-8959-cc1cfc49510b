import { Stack, Typography } from "@mui/material";
import React from "react";
import { IFallbackData } from "./FallbacksTable";

interface IProp {
  title: keyof IFallbackData;
  value: string;
}

const displayNameMapper: { [K in keyof IFallbackData]?: string } = {
  _id: "ID",
  orgId: "Organization",
  agentId: "Agent",
  status: "Current Status",
  refUrl: "Reference",
  providerName: "Provider",
  dateTime: "Date & Time",
  contactMessage: "Contact Message",
  aiResponse: "AI Response",
};

const KeyValue: React.FC<IProp> = ({ title, value }) => {
  if (title === "_id") {
    return <Stack></Stack>;
  }
  return (
    <Stack direction={"row"} gap={2}>
      <Typography variant="h6" width={"170px"}>
        {displayNameMapper[title] || "Not found"}
      </Typography>
      <Typography variant="h6">:</Typography>
      <Typography variant="h6">{value}</Typography>
    </Stack>
  );
};

export default KeyValue;
