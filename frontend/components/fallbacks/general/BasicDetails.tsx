import React from "react";
import {
  <PERSON>,
  ClickAwayListener,
  Grow,
  MenuItem,
  MenuList,
  Paper,
  Popper,
  Stack,
} from "@mui/material";
import Link from "next/link";
import PsychologyIcon from "@mui/icons-material/Psychology";
import AccessTimeIcon from "@mui/icons-material/AccessTime";
import OpenInNewIcon from "@mui/icons-material/OpenInNew";
import VisibilityOffIcon from "@mui/icons-material/VisibilityOff";
import { BiBrain } from "react-icons/bi";
import InfoIcon from "@mui/icons-material/Info";
import ChangeStatusDropdown from "../ChangeStatusDropdown";

const BasicDetails = ({
  agentName,
  agentId,
  channelName,
  contactId,
  refUrl,
  rollover,
  rolloverDate,
  onRolloverChange,
}: {
  agentName: string;
  agentId: string;
  channelName: string;
  contactId: string;
  refUrl: string;
  rollover: boolean;
  rolloverDate: string;
  onRolloverChange: (state: "rollover" | "replied") => void;
}) => {
  const [open, setOpen] = React.useState(false);
  const anchorRef = React.useRef<HTMLButtonElement>(null);

  return (
    <>
      <Stack gap={2}>
        <Stack direction={"row"} gap={4}>
          {agentId ? (
            <Link
              href={"/home/<USER>/" + agentId}
              className="flex gap-2 items-center hover:underline"
            >
              <BiBrain size={"24px"} />
              {agentName || "No name"}
            </Link>
          ) : null}
          <Link
            href={refUrl ? refUrl : "#"}
            target="_blank"
            className="flex gap-2 items-center hover:underline"
          >
            <OpenInNewIcon />
            {channelName || "No channel name"}
          </Link>
          <Link
            href={
              contactId && agentId
                ? `/home/<USER>/${agentId}/details/${contactId}`
                : "#"
            }
            className="flex gap-2 items-center hover:underline"
          >
            <InfoIcon />
            Details
          </Link>
          <ChangeStatusDropdown
            contactId={contactId}
            rollover={rollover}
            onChange={(state: "rollover" | "replied") => {
              onRolloverChange(state);
            }}
          />
        </Stack>
        <Stack direction={"row"}>
          <div className="flex gap-2 items-center text-[14px]">
            <AccessTimeIcon fontSize="small" />
            {new Date(rolloverDate).toLocaleString("en-US", {
              year: "numeric",
              month: "long",
              day: "numeric",
              hour: "2-digit",
              minute: "2-digit",
            })}
          </div>
        </Stack>
      </Stack>
    </>
  );
};

export default BasicDetails;
