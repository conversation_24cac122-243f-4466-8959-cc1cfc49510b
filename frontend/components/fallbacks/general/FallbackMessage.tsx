import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON>nt,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Typography,
} from "@mui/material";
import { grey, indigo, red } from "@mui/material/colors";
import React from "react";
import PersonIcon from "@mui/icons-material/Person";
import PsychologyIcon from "@mui/icons-material/Psychology";
import DoneAllIcon from "@mui/icons-material/DoneAll";
import ErrorOutlineIcon from "@mui/icons-material/ErrorOutline";
import { ImSpinner8 } from "react-icons/im";

interface IProp {
  message: string;
  by: "human" | "bot";
  status: string;
  dateAdded: string;
  updateStatusSuccess: () => void;
  loading: boolean;
}

const FallbackMessage: React.FC<IProp> = ({
  message,
  by,
  status,
  updateStatusSuccess,
  loading,
  dateAdded,
}) => {
  const textColor = by === "bot" ? "white" : "black";
  const secondaryColor = by === "bot" ? grey[100] : grey[700];
  const cardBgColor =
    by === "bot"
      ? `linear-gradient(to bottom right, ${indigo[600]}, ${indigo[500]})`
      : `linear-gradient(to bottom right, ${grey[100]}, ${grey[200]})`;
  const icon = by === "human" ? <PersonIcon /> : <PsychologyIcon />;
  const messageWithLineBreaks = message?.split("\n").map((line, index) => (
    <React.Fragment key={index}>
      {line}
      <br />
    </React.Fragment>
  ));
  return (
    <div
      className={`flex my-3 mx-4 gap-x-4 items-start ${
        by === "human" ? "justify-start" : "justify-end"
      }`}
    >
      {by === "human" && (
        <Avatar
          sx={{
            background: cardBgColor,
            color: textColor,
          }}
        >
          {icon}
        </Avatar>
      )}
      <Card
        sx={{
          maxWidth: 500,
          borderRadius: 3,
          py: 0,
          marginBottom: 2,
          color: textColor,
          background: status === "pending" ? red[400] : cardBgColor,
        }}
      >
        <CardContent>
          <Typography variant="body1">{messageWithLineBreaks}</Typography>
        </CardContent>
        <Stack direction={"row"} alignItems={"center"} gap={2} px={2} pb={1}>
          {status === "delivered" ? (
            <Tooltip title="Delivered">
              <DoneAllIcon />
            </Tooltip>
          ) : (
            <Tooltip title="Not sent">
              <ErrorOutlineIcon />
            </Tooltip>
          )}
          <Typography variant="body2" color={secondaryColor}>
            {new Date(dateAdded).toLocaleString()}
          </Typography>
          {status === "pending" && (
            <Button
              variant="text"
              style={{ textTransform: "none" }}
              color="inherit"
              onClick={updateStatusSuccess}
              disabled={loading}
            >
              Send now{" "}
              {loading && (
                <ImSpinner8 size={14} className="ml-2 animate-spin" />
              )}
            </Button>
          )}
        </Stack>
      </Card>
      {by === "bot" && (
        <div className="flex flex-col items-center gap-2">
          <Avatar
            sx={{
              background: cardBgColor,
              color: textColor,
            }}
          >
            {icon}
          </Avatar>
        </div>
      )}
    </div>
  );
};

export default FallbackMessage;
