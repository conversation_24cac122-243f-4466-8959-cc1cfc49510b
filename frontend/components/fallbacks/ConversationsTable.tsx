import React, { useEffect, useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Typography,
  Button,
  Card,
  Skeleton,
} from "@mui/material";

import InfoIcon from "@mui/icons-material/Info";
import SendMessageDialog from "./SendMessageDialog";
import { getAllRollovers } from "@/requests/rollovers/getAll";
import { useDispatch, useSelector } from "react-redux";
import { ImSpinner8 } from "react-icons/im";
import Link from "next/link";
import AccessTimeIcon from "@mui/icons-material/AccessTime";

import FilterConversationStatus from "./FilterConversationStatus";
import { useRouter } from "next/router";
import Image from "next/image";
import { RootState } from "@/store";
import ConversationsSort from "./ConversationsSort";
import NoConversationFound from "./NoConversationFound";
import ChangeStatusDropdown from "./ChangeStatusDropdown";
import NotAvailableForThisPlan from "../general/NotAvailableForThisPlan";

export type RolloverStatus = "all" | "rollover" | "replied";

type Rollover = {
  agentId: string;
  agentName: string;
  channelName: string;
  lastMessage: string;
  lastMessageAt: string;
  rollover: boolean;
  rolloverReason: string;
  rolloverDate: string;
  contactId: string;
  channelAccountId: string;
};

const ConversationsTable = () => {
  const dispatch = useDispatch();
  const organizationData = useSelector(
    (state: RootState) => state.organization
  );

  const router = useRouter();
  const {
    status = "rollover",
    agent,
    channel,
  } = router.query as {
    status: RolloverStatus;
    agent: string;
    channel: string;
  };
  const [rollovers, setRollovers] = useState<Rollover[]>([]);
  const [loading, setLoading] = useState(false);
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  // const [currentPage, setCurrentPage] = useState(1);
  const [totalPage, setTotalPage] = useState(1);

  useEffect(() => {
    // Reset rollovers and page when status changes
    setRollovers([]);
    setPage(1);
  }, [status]);

  useEffect(() => {
    const getRollbacks = async () => {
      setLoading(true);
      const response = await getAllRollovers(
        {
          orgId: localStorage.getItem("orgId") || "",
          page,
          limit,
          status,
          agent,
          channel,
        },
        dispatch
      );
      setLoading(false);
      if (response?.status === 200) {
        const rolloversArr = response.data.result;
        if (page > 1) {
          setRollovers((prev) => [...prev, ...rolloversArr]);
        } else {
          setRollovers(rolloversArr);
        }
        setTotalPage(response.data.totalPage);
      }
    };

    getRollbacks();
  }, [page, limit, status, agent, channel]);

  useEffect(() => {
    console.log({ rollovers });
  }, [rollovers]);

  const changeRolloverStatus = (index: number, status: RolloverStatus) => {
    const newRollovers = [...rollovers];
    newRollovers[index].rollover = status === "rollover";
    setRollovers(newRollovers);
    console.log("Rollover state changed for index ", index);
  };
  if (
    !!!loading &&
    organizationData.billingPlan !== "pro" &&
    organizationData.billingPlan !== "enterprise"
  ) {
    return <NotAvailableForThisPlan />;
  }
  return (
    <>
      <ConversationsSort />
      {!!!loading && rollovers.length === 0 ? (
        <NoConversationFound />
      ) : (
        <TableContainer component={Paper}>
          {!!loading && (
            <>
              <div className="w-full px-2">
                <Skeleton sx={{ height: "60px" }} />
                <Skeleton sx={{ height: "60px" }} />
                <Skeleton sx={{ height: "60px" }} />
              </div>
            </>
          )}
          {!!!loading && (
            <Table sx={{ minWidth: 650 }} aria-label="simple table">
              <TableHead>
                <TableRow>
                  <TableCell align="right">Channel</TableCell>
                  <TableCell align="right">Agent</TableCell>
                  {/* <TableCell align="right">Date</TableCell> */}
                  <TableCell align="right">Rollover reason</TableCell>
                  <TableCell align="right">Last message</TableCell>
                  <TableCell align="right">Status</TableCell>
                  <TableCell align="right"></TableCell>
                </TableRow>
              </TableHead>

              <TableBody>
                {rollovers.map((rollover, index) => (
                  <TableRow key={rollover.channelAccountId + index}>
                    <TableCell component="th" scope="row" align="right">
                      {rollover?.channelName}
                    </TableCell>
                    <TableCell component="th" scope="row" align="right">
                      <Link
                        href={
                          rollover?.agentId
                            ? "/home/<USER>/" + rollover?.agentId
                            : "#"
                        }
                      >
                        {rollover?.agentName || "No name"}
                      </Link>
                    </TableCell>
                    {/* <TableCell align="right">
                    {new Date(rollover?.rolloverDate)?.toLocaleString()}
                  </TableCell> */}
                    <TableCell align="right" className="w-3/12">
                      {rollover?.rolloverReason}
                    </TableCell>
                    <TableCell align="right" className="">
                      <>
                        <div className="line-clamp-5 max-w-[300px]">
                          {rollover?.lastMessage}
                        </div>
                        <div className="mt-3 flex gap-2 items-center justify-end text-gray-500">
                          <AccessTimeIcon fontSize="small" />
                          <Typography variant="body2">
                            {new Date(
                              rollover?.lastMessageAt
                            )?.toLocaleString()}
                          </Typography>
                        </div>
                      </>
                    </TableCell>
                    <TableCell align="right">
                      {/* <Button sx={{ textTransform: "none" }}>
                        {rollover?.rollover ? "Rollover" : "Replied"}
                      </Button> */}
                      <ChangeStatusDropdown
                        rollover={rollover?.rollover}
                        contactId={rollover?.contactId}
                        onChange={(x: "rollover" | "replied") =>
                          changeRolloverStatus(index, x)
                        }
                      />
                    </TableCell>
                    <TableCell align="right">
                      <div className="flex gap-2">
                        <SendMessageDialog
                          contactId={rollover?.contactId}
                          onMessageSent={() =>
                            changeRolloverStatus(index, "replied")
                          }
                        />
                        <Link
                          href={`/home/<USER>/rollovers/${rollover.contactId}`}
                        >
                          <IconButton aria-label="info" size="large">
                            <InfoIcon fontSize="inherit" />
                          </IconButton>
                        </Link>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </TableContainer>
      )}

      {page < totalPage ? (
        <div className="w-full text-center mt-2">
          <Button disabled={loading} onClick={() => setPage(page + 1)}>
            Show more {loading && <ImSpinner8 className="ml-2" />}
          </Button>
        </div>
      ) : null}
    </>
  );
};

export default ConversationsTable;
