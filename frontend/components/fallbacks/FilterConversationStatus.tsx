import * as React from "react";
import Button from "@mui/material/Button";
import Menu from "@mui/material/Menu";
import MenuItem from "@mui/material/MenuItem";
import {
  IconButton,
  ListItemIcon,
  ListItemText,
  MenuList,
  Paper,
} from "@mui/material";
import FilterListIcon from "@mui/icons-material/FilterList";
import { RolloverStatus } from "./ConversationsTable";
import Check from "@mui/icons-material/Check";
import { useRouter } from "next/router";
import AddCircleOutlineIcon from "@mui/icons-material/AddCircleOutline";

export function ListChecked() {
  return (
    <ListItemIcon>
      <Check />
    </ListItemIcon>
  );
}
export function MenuItemContent({
  show,
  text,
  onClick,
}: {
  show: boolean;
  text: string;
  onClick: () => void;
}) {
  if (show) {
    return (
      <MenuItem onClick={onClick}>
        <ListChecked />
        {text}
      </MenuItem>
    );
  } else {
    return (
      <MenuItem onClick={onClick}>
        <ListItemText inset>{text}</ListItemText>
      </MenuItem>
    );
  }
}

export default function FilterConversationStatus() {
  const router = useRouter();
  const { status = "rollover" } = router.query as { status: RolloverStatus };
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);
  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };

  const changeStatusQueryParam = (newStatus: RolloverStatus) => {
    router.push({
      pathname: router.pathname,
      query: { ...router.query, status: newStatus },
    });
  };

  return (
    <div>
      <Button
        variant="outlined"
        color="inherit"
        startIcon={<AddCircleOutlineIcon />}
        id="basic-button"
        aria-controls={open ? "basic-menu" : undefined}
        aria-haspopup="true"
        aria-expanded={open ? "true" : undefined}
        onClick={handleClick}
        style={{ textTransform: "none" }}
      >
        Status | {status}
      </Button>
      <Menu
        id="basic-menu"
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        sx={{ py: 0 }}
        MenuListProps={{
          "aria-labelledby": "basic-button",
        }}
      >
        <MenuList dense sx={{ width: 320 }}>
          <MenuItemContent
            show={status === "rollover"}
            text="Rollover"
            onClick={() => {
              changeStatusQueryParam("rollover");
              handleClose();
            }}
          />
          <MenuItemContent
            show={status === "replied"}
            text="Replied"
            onClick={() => {
              changeStatusQueryParam("replied");
              handleClose();
            }}
          />
          <MenuItemContent
            show={status === "all"}
            text="All"
            onClick={() => {
              changeStatusQueryParam("all");
              handleClose();
            }}
          />
        </MenuList>
      </Menu>
    </div>
  );
}
