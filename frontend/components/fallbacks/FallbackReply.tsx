import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>Field } from "@mui/material";
import React, { useEffect, useRef, useState } from "react";
import FallbackMessage from "./general/FallbackMessage";
import SendIcon from "@mui/icons-material/Send";
import {
  Message,
  Rollover,
} from "@/pages/home/<USER>/rollovers/[contactId]";
import { replyToRollover } from "@/requests/rollovers/replyToRollover";
import { useDispatch } from "react-redux";
import { ImSpinner8 } from "react-icons/im";

const FallbackReply = ({
  rolloverData,
  showAll,
  loading: btnLoading,
  toggleShowAll,
  onMessageSent,
}: {
  rolloverData: Rollover;
  showAll: boolean;
  loading: boolean;
  toggleShowAll: (x: boolean) => void;
  onMessageSent: () => void;
}) => {
  const dispatch = useDispatch();
  const [messageCount, setMessageCount] = useState(2);
  const [conversation, setConversation] = useState<Message[]>([]);
  const [firstConversationLoad, setFirstConversationLoad] = useState(false);
  const [reply, setReply] = useState("");
  const endOfMessagesRef = useRef<HTMLDivElement | null>(null);
  const [loading, setLoading] = useState(false);
  const [sendAgainLoading, setSendAgainLoading] = useState(false);
  useEffect(() => {
    console.log({ conversationIz: rolloverData.conversation });

    setConversation(rolloverData.conversation);
  }, [rolloverData.conversation]);

  useEffect(() => {
    if (conversation && !!!firstConversationLoad) {
      setFirstConversationLoad(true);
      scrollToBottom();
    }
  }, [conversation]);

  useEffect(() => {
    console.log({ conversation });
  }, [conversation]);

  const addRolloverReply = (message: Message) => {
    console.log({ messageIs: message });

    setConversation((prev) => [...prev, message]);
  };

  const scrollToBottom = () => {
    endOfMessagesRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  const replyToThisConversation = async () => {
    setLoading(true);
    const response = await replyToRollover(
      {
        orgId: localStorage.getItem("orgId") || "",
        channelName: rolloverData.channelName,
        locationId: rolloverData.locationId,
        type: conversation[conversation.length - 1].messageType,
        contactId: rolloverData.contactId,
        message: reply,
      },
      dispatch
    );
    setLoading(false);
    console.log({ response });

    if (response?.status === 201) {
      onMessageSent();
      addRolloverReply({
        dateAdded: new Date().toISOString(),
        direction: "outbound",
        message: reply,
        messageType: conversation[conversation.length - 1].direction,
        status: "delivered",
      });

      setReply("");
      scrollToBottom();
    }
  };
  const updateMessageStatusSuccess = async (index: number, status: string) => {
    const conversationMessage = conversation[index];
    console.log({ conversationMessage });
    setSendAgainLoading(true);
    const response = await replyToRollover(
      {
        orgId: localStorage.getItem("orgId") || "",
        channelName: rolloverData.channelName,
        locationId: rolloverData.locationId,
        type: conversationMessage.messageType,
        contactId: rolloverData.contactId,
        message: conversationMessage.message,
      },
      dispatch
    );
    setSendAgainLoading(false);
    if (response?.status === 201) {
      setConversation((prev) => {
        const newConversation = [...prev];
        newConversation[index].status = status;
        return newConversation;
      });
      onMessageSent();
    }
  };
  return (
    <>
      <Stack>
        {(!!!showAll || btnLoading) && (
          <Button
            size="small"
            sx={{ width: "fit-content", mx: "auto" }}
            onClick={() => toggleShowAll(true)}
            disabled={btnLoading}
          >
            View entire conversation{" "}
            {btnLoading && <ImSpinner8 className="animate-spin ml-2" />}
          </Button>
        )}
        {conversation &&
          conversation.map((message, index) => (
            <FallbackMessage
              by={message.direction === "inbound" ? "human" : "bot"}
              message={message.message}
              dateAdded={message.dateAdded}
              key={"Message" + index}
              status={message.status}
              updateStatusSuccess={() => {
                updateMessageStatusSuccess(index, "delivered");
              }}
              loading={sendAgainLoading}
            />
          ))}
      </Stack>
      <Divider />
      <Stack direction={"row"} gap={2} p={2}>
        <form
          onSubmit={(e) => {
            e.preventDefault();
            replyToThisConversation();
          }}
          className="w-full"
        >
          <Stack direction={"row"} gap={2} p={2}>
            <TextField
              id="outlined-basic-message-input"
              label="Type your message"
              type="text"
              variant="outlined"
              value={reply}
              onChange={(e) => setReply(e.target.value)}
              fullWidth
            />
            <Button
              variant="contained"
              endIcon={
                loading ? <ImSpinner8 className="animate-spin" /> : <SendIcon />
              }
              type="submit"
              disabled={loading}
            >
              Send
            </Button>
            <div ref={endOfMessagesRef} />
          </Stack>
        </form>
      </Stack>
    </>
  );
};

export default FallbackReply;
