import { Stack, Typography } from "@mui/material";
import Image from "next/image";
import React from "react";

const NoFallbackFound = () => {
  return (
    <Stack alignItems={"center"} gap={2}>
      <Image
        src={"/images/fallbacks/Empty-amico.svg"}
        alt="Empty list"
        width={300}
        height={300}
      />
      <Typography variant="h4">Horray! No fallbacks found 🥳</Typography>
    </Stack>
  );
};

export default NoFallbackFound;
