import * as React from "react";
import Button from "@mui/material/Button";
import TextField from "@mui/material/TextField";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogContentText from "@mui/material/DialogContentText";
import DialogTitle from "@mui/material/DialogTitle";
import { IconButton } from "@mui/material";
import ReplyIcon from "@mui/icons-material/Reply";
import FallbackReply from "./FallbackReply";
import { Rollover } from "@/pages/home/<USER>/rollovers/[contactId]";
import { getSingleRollover } from "@/requests/rollovers/getSingleRollover";
import { useDispatch } from "react-redux";
import { ImSpinner8 } from "react-icons/im";

export default function SendMessageDialog({
  contactId,
  onMessageSent,
}: {
  contactId: string;
  onMessageSent: () => void;
}) {
  const dispatch = useDispatch();
  const [open, setOpen] = React.useState(false);
  const [showAll, setShowAll] = React.useState(false);
  const [loading, setLoading] = React.useState(false);

  const [rolloverData, setRolloverData] = React.useState<Rollover | null>(null);
  React.useEffect(() => {
    if (contactId && open) {
      setLoading(true);
      const response = getSingleRollover({ contactId, showAll }, dispatch);
      response.then((resData) => {
        setLoading(false);
        if (resData?.status === 200) {
          console.log({ dataz: resData.data });

          setRolloverData(resData.data);
        }
      });
    }
  }, [contactId, showAll, open]);

  // React.useEffect(() => {
  //   if (showAll) {
  //   }
  // }, [showAll]);

  const handleClickOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  return (
    <React.Fragment>
      <IconButton
        aria-label="reply"
        size="large"
        color="primary"
        onClick={handleClickOpen}
      >
        <ReplyIcon fontSize="inherit" />
      </IconButton>
      <Dialog open={open} onClose={handleClose} fullWidth>
        <DialogTitle>Reply to this conversation</DialogTitle>
        <DialogContent sx={{ pb: 0, px: 0 }}>
          {!!rolloverData ? (
            <FallbackReply
              rolloverData={rolloverData}
              showAll={showAll}
              loading={loading}
              toggleShowAll={(x: boolean) => setShowAll(x)}
              onMessageSent={onMessageSent}
            />
          ) : (
            <div className="flex justify-center py-4">
              <ImSpinner8 className="h-8 w-8 animate-spin" />
            </div>
          )}
        </DialogContent>
      </Dialog>
    </React.Fragment>
  );
}
