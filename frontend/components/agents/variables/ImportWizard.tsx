import React, { useState } from "react";
import {
  But<PERSON>,
  <PERSON>po<PERSON>,
  List,
  ListItem,
  ListItemText,
  Checkbox,
  FormControlLabel,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Stepper,
  Step,
  StepLabel,
  ListItemAvatar,
  Avatar,
  CircularProgress,
  Stack,
  FormGroup,
} from "@mui/material";
import { useImportWizard } from "@/hooks/useImportWizard";
import DownloadIcon from "@mui/icons-material/Download";
import { logos } from "@/helpers/images";

interface IProp {
  agentId: string;
}

interface Property {
  propertyName: string;
  selected: boolean;
}

const ImportWizard: React.FC<IProp> = ({ agentId }) => {
  const [open, setOpen] = useState(false);
  const [activeStep, setActiveStep] = useState(0);
  const {
    providers,
    selectedProvider,
    accounts,
    loading,
    selectedAccount,
    properties,
    selectProvider,
    selectAccount,
    toggleProperty,
    importData,
  } = useImportWizard(agentId);

  const handleOpen = () => setOpen(true);
  const handleClose = () => {
    setOpen(false);
    setActiveStep(0);
  };

  const handleNext = () => setActiveStep((prevStep) => prevStep + 1);
  const handleBack = () => setActiveStep((prevStep) => prevStep - 1);

  const steps = ["Select Provider", "Select Account", "Choose Properties"];

  // Calculate if all properties are selected
  const areAllPropertiesSelected = properties.every((prop) => prop.selected);

  // Handle select all toggle
  const handleSelectAll = () => {
    const newSelectedState = !areAllPropertiesSelected;
    properties.forEach((prop) => {
      if (prop.selected !== newSelectedState) {
        toggleProperty(prop.propertyName);
      }
    });
  };

  const renderStepContent = (step: number) => {
    if (loading) {
      return (
        <Stack direction="row" justifyContent="center" my={4}>
          <CircularProgress />
        </Stack>
      );
    }

    switch (step) {
      case 0:
        return (
          <List>
            {providers.map((provider) => (
              <ListItem
                key={provider.providerVisibleName}
                button
                disabled={provider.disabled}
                onClick={() => {
                  selectProvider(provider.providerId);
                  handleNext();
                }}
              >
                <ListItemAvatar>
                  <Avatar
                    alt={provider.providerVisibleName}
                    src={provider.image}
                    sx={{ width: 40, height: 40, p: 1 }}
                  />
                </ListItemAvatar>
                <ListItemText
                  primary={`Import from ${provider.providerVisibleName}`}
                  secondary={provider.description}
                />
              </ListItem>
            ))}
          </List>
        );
      case 1:
        return (
          <List>
            {accounts.map((account) => (
              <ListItem
                key={account.keyId}
                button
                onClick={() => {
                  selectAccount(account);
                  handleNext();
                }}
              >
                <ListItemText
                  primary={account.name}
                  secondary={`Provider: ${logos[account.providerName].name}`}
                />
              </ListItem>
            ))}
          </List>
        );
      case 2:
        return (
          <FormGroup>
            <ListItem>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={areAllPropertiesSelected}
                    onChange={handleSelectAll}
                    indeterminate={
                      properties.some((prop) => prop.selected) &&
                      !areAllPropertiesSelected
                    }
                  />
                }
                label={
                  <Typography variant="subtitle1" fontWeight="bold">
                    Select All Properties
                  </Typography>
                }
              />
            </ListItem>
            <List>
              {properties.map((prop) => (
                <ListItem key={prop.propertyName}>
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={prop.selected}
                        onChange={() => toggleProperty(prop.propertyName)}
                      />
                    }
                    label={prop.propertyName}
                  />
                </ListItem>
              ))}
            </List>
          </FormGroup>
        );
      default:
        return null;
    }
  };

  const isImportButtonEnabled =
    activeStep === steps.length - 1 && properties.some((prop) => prop.selected);

  return (
    <>
      <Button
        startIcon={<DownloadIcon />}
        variant="outlined"
        color="primary"
        className="mt-4"
        onClick={handleOpen}
      >
        Import from CRM
      </Button>
      <Dialog open={open} onClose={handleClose} fullWidth maxWidth="md">
        <DialogTitle>Import Variables</DialogTitle>
        <DialogContent>
          <Stepper activeStep={activeStep}>
            {steps.map((label) => (
              <Step key={label}>
                <StepLabel>{label}</StepLabel>
              </Step>
            ))}
          </Stepper>
          {renderStepContent(activeStep)}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClose}>Cancel</Button>
          {activeStep > 0 && <Button onClick={handleBack}>Back</Button>}
          {activeStep === steps.length - 1 && (
            <Button
              onClick={() => {
                importData();
                handleClose();
              }}
              color="primary"
              variant="contained"
              disabled={!isImportButtonEnabled}
            >
              Import
            </Button>
          )}
        </DialogActions>
      </Dialog>
    </>
  );
};

export default ImportWizard;
