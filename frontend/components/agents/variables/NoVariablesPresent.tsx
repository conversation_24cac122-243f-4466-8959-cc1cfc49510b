import { FC } from "react";
import {
  <PERSON>,
  Card,
  CardContent,
  Typo<PERSON>,
  Button,
  useTheme,
  Link,
} from "@mui/material";
import DataObjectIcon from "@mui/icons-material/DataObject";
import LoomDialog from "@/components/general/LoomDialog";

interface EmptyFAQCardProps {
  onAddFaq: () => void;
}

const EmptyFAQCard: FC<EmptyFAQCardProps> = ({ onAddFaq }) => {
  const theme = useTheme();

  return (
    <Card
      sx={{
        textAlign: "center",
        p: 4,
        backgroundColor: theme.palette.background.default,
        border: `1px dashed ${theme.palette.divider}`,
        borderRadius: 2,
      }}
    >
      <CardContent>
        <Box
          sx={{
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            gap: 2,
          }}
        >
          <Box
            sx={{
              backgroundColor: theme.palette.primary.light,
              borderRadius: "50%",
              p: 2,
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              mb: 2,
            }}
          >
            <DataObjectIcon
              sx={{
                fontSize: 40,
                color: theme.palette.primary.main,
              }}
            />
          </Box>

          <Typography variant="h5" component="h2" gutterBottom>
            No Variables Added Yet
          </Typography>

          <Typography
            variant="body2"
            color="textSecondary"
            sx={{ maxWidth: 500, mb: 3 }}
          >
            Use variables to reference dynamic values in your prompt. For
            example, the current date or day of the week, contact-specific
            fields like name and email, or static variables you set like bot
            name and business name. For reference, check{" "}
            <LoomDialog
              triggerComponent={<Link>here</Link>}
              title="Variables feature"
              videoUrl="https://www.loom.com/share/cf5c9f3018ee46e3b34e3256b57e40b7?sid=243d0e7b-b741-4f7a-8fab-9d2915d7ca61"
            />
          </Typography>

          <Button
            variant="contained"
            color="primary"
            onClick={onAddFaq}
            size="large"
            sx={{
              borderRadius: 2,
              textTransform: "none",
              px: 4,
            }}
          >
            Add Your First Variable
          </Button>
        </Box>
      </CardContent>
    </Card>
  );
};

export default EmptyFAQCard;
