import React, { useState } from "react";
import {
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Chip,
  Stack,
  CircularProgress,
} from "@mui/material";
import { Edit, Delete } from "@mui/icons-material";
import { IVariable } from "@/slices/agents/agentsSlice";

interface VariablesListProps {
  variables: IVariable[];
  onEdit: (variableId: string) => void;
  onDelete: (variableId: string) => Promise<void>;
}

const variableTypeNames = {
  static: "Static",
  dynamic: "Dynamic",
};

const VariablesList: React.FC<VariablesListProps> = ({
  variables,
  onEdit,
  onDelete,
}) => {
  const [loadingDelete, setLoadingDelete] = useState(false);
  return (
    <List>
      {variables.map((variable, index) => (
        <ListItem key={index} className="bg-white mb-2 rounded-md shadow-sm">
          <ListItemText
            primary={variable.name}
            secondary={
              <>
                {variable.value ? (
                  <p>{variable.value}</p>
                ) : (
                  <p className="italic">Value not set</p>
                )}
                <Chip
                  label={variableTypeNames[variable.type]}
                  color={variable.type === "static" ? "primary" : "secondary"}
                  size="small"
                  className="mt-1"
                />
              </>
            }
          />
          <ListItemSecondaryAction>
            <Stack direction={"row"} alignItems={"center"} gap={2}>
              <IconButton
                edge="end"
                color="default"
                aria-label="edit"
                onClick={() => onEdit(variable._id as string)}
              >
                <Edit />
              </IconButton>
              {loadingDelete ? (
                <CircularProgress size={24} />
              ) : (
                <IconButton
                  edge="end"
                  color="error"
                  aria-label="delete"
                  onClick={async () => {
                    setLoadingDelete(true);
                    await onDelete(variable._id as string);
                    setLoadingDelete(false);
                  }}
                >
                  <Delete />
                </IconButton>
              )}
            </Stack>
          </ListItemSecondaryAction>
        </ListItem>
      ))}
    </List>
  );
};

export default VariablesList;
