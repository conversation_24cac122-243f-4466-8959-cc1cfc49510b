import React, { useEffect, useState } from "react";
import { Box, Button, Stack } from "@mui/material";
import { Add } from "@mui/icons-material";
import NoVariablesPresent from "./NoVariablesPresent";
import VariablesList from "./VariablesList";
import AddVariableModal from "./AddVariableModel";
import ImportWizard from "./ImportWizard";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store";
import { IVariable, agentsAction } from "@/slices/agents/agentsSlice";
import { addVariable } from "@/requests/agents/variables/addVariable";
import { updateVariable } from "@/requests/agents/variables/updateVariable";
import { deleteVariable } from "@/requests/agents/variables/deleteVariable";

const convertFieldKeyToAgentScope = (text: string) => {
  return `{{agent.${text}}}`;
};

const VariablesTab: React.FC = () => {
  const dispatch = useDispatch();
  const agent = useSelector((state: RootState) => state.agents.currentAgent);
  const [variables, setVariables] = useState<IVariable[]>(
    agent?.variables || []
  );
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [editingVariableId, setEditingVariable] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    setVariables(agent.variables);
  }, [agent.variables]);

  const handleAddVariable = async (newVariable: IVariable) => {
    setLoading(true);
    const response = await addVariable(
      {
        agentId: agent._id,
        variable: {
          ...newVariable,
          fieldKey: convertFieldKeyToAgentScope(newVariable.name),
        },
      },
      dispatch
    );
    setLoading(false);
    if (response.status === 201) {
      const variablesList = response.data.data;
      dispatch(agentsAction.addVariablesList(variablesList));
    }
    setIsAddModalOpen(false);
  };

  const handleEditVariable = async (variableId: string) => {
    setEditingVariable(variableId);
    setIsAddModalOpen(true);
  };

  const handleUpdateVariable = async (updatedVariable: IVariable) => {
    setLoading(true);
    const response = await updateVariable(
      {
        agentId: agent._id,
        variableId: editingVariableId!,
        variable: {
          ...updatedVariable,
          fieldKey: convertFieldKeyToAgentScope(updatedVariable.name),
        },
      },
      dispatch
    );
    setLoading(false);
    if (response.status === 200) {
      const variablesList = response.data.data;
      dispatch(agentsAction.addVariablesList(variablesList));
    }
    setIsAddModalOpen(false);
    setEditingVariable(null);
  };

  const handleDeleteVariable = async (variableId: string) => {
    const response = await deleteVariable(
      { agentId: agent._id, variableId },
      dispatch
    );
    if (response.status === 200) {
      const variablesList = response.data.data.variables;
      dispatch(agentsAction.addVariablesList(variablesList));
    }
  };

  return (
    <Box mt={2}>
      <div className="p-4 bg-gray-100 rounded-lg shadow">
        <h2 className="text-2xl font-bold mb-4">Variables</h2>
        {variables?.length > 0 ? (
          <VariablesList
            variables={variables}
            onEdit={handleEditVariable}
            onDelete={handleDeleteVariable}
          />
        ) : (
          <NoVariablesPresent
            onAddFaq={() => {
              setIsAddModalOpen(true);
            }}
          />
        )}
        <Stack direction={"row"} gap={2}>
          <Button
            startIcon={<Add />}
            variant="contained"
            color="primary"
            onClick={() => {
              setEditingVariable(null);
              setIsAddModalOpen(true);
            }}
            className="mt-4"
          >
            Add Variable
          </Button>
          <ImportWizard agentId={agent._id} />
        </Stack>

        <AddVariableModal
          open={isAddModalOpen}
          loading={loading}
          onClose={() => setIsAddModalOpen(false)}
          onAdd={handleAddVariable}
          onUpdate={handleUpdateVariable}
          editingVariable={
            editingVariableId !== null
              ? variables.find((variable) => variable._id === editingVariableId)
              : undefined
          }
        />
      </div>
    </Box>
  );
};

export default VariablesTab;
