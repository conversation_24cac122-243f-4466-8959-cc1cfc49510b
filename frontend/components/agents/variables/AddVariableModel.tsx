import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>alogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  SelectChangeEvent,
  CircularProgress,
} from "@mui/material";
import { IVariable } from "@/slices/agents/agentsSlice";

interface AddVariableModalProps {
  open: boolean;
  loading: boolean;
  onClose: () => void;
  onAdd: (variable: IVariable) => void;
  onUpdate: (variable: IVariable) => void;
  editingVariable?: IVariable;
}

const AddVariableModal: React.FC<AddVariableModalProps> = ({
  open,
  loading,
  onClose,
  onAdd,
  onUpdate,
  editingVariable,
}) => {
  const [variable, setVariable] = useState<IVariable>({
    name: "",
    value: "",
    type: "static",
  });
  const [nameError, setNameError] = useState<string | null>(null);

  useEffect(() => {
    if (editingVariable) {
      setVariable(editingVariable);
      validateName(editingVariable.name);
    } else {
      setVariable({ name: "", value: "", type: "static" });
      setNameError(null);
    }
  }, [editingVariable]);

  const handleChange = (
    e:
      | React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
      | SelectChangeEvent<"static" | "dynamic">
  ) => {
    const { name, value } = e.target;
    if (name === "name") {
      // Only allow alphanumeric characters, hyphens, and underscores
      const sanitizedValue = value.replace(/[^a-zA-Z0-9-_]/g, "");
      setVariable((prev) => ({ ...prev, [name]: sanitizedValue }));
      validateName(sanitizedValue);
    } else {
      setVariable((prev) => ({ ...prev, [name]: value }));
    }
  };

  const validateName = (name: string) => {
    if (name.length === 0) {
      setNameError(null);
    } else if (!/^[a-zA-Z0-9-_]+$/.test(name)) {
      setNameError(
        "Only alphanumeric characters, hyphens, and underscores are allowed"
      );
    } else {
      setNameError(
        `This will be available as {{agent.${name}}} in the prompts`
      );
    }
  };

  const handleSubmit = () => {
    if (editingVariable) {
      onUpdate(variable);
    } else {
      onAdd(variable);
    }
    setVariable({ name: "", value: "", type: "static" });
    setNameError(null);
  };

  const isSubmitDisabled =
    loading ||
    variable.name.length === 0 ||
    !/^[a-zA-Z0-9-_]+$/.test(variable.name);

  return (
    <Dialog fullWidth open={open} onClose={onClose}>
      <DialogTitle>
        {editingVariable ? "Edit Variable" : "Add New Variable"}
      </DialogTitle>
      <DialogContent>
        <TextField
          autoFocus
          margin="dense"
          name="name"
          label="Key"
          type="text"
          fullWidth
          value={variable.name}
          onChange={handleChange}
          error={!!nameError && !nameError.includes("available as")}
          helperText={nameError}
          inputProps={{
            pattern: "[a-zA-Z0-9-_]*",
            title:
              "Only alphanumeric characters, hyphens, and underscores are allowed",
          }}
        />
        <TextField
          margin="dense"
          name="value"
          label="Value"
          type="text"
          fullWidth
          value={variable.value}
          onChange={handleChange}
        />
        <FormControl sx={{ display: "none" }} fullWidth margin="dense">
          <InputLabel>Type</InputLabel>
          <Select
            name="type"
            label="Type"
            value={variable.type}
            onChange={handleChange}
          >
            <MenuItem value="static">Static</MenuItem>
            <MenuItem value="dynamic" disabled>
              Dynamic
            </MenuItem>
          </Select>
        </FormControl>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} color="primary">
          Cancel
        </Button>
        <Button
          onClick={handleSubmit}
          color="primary"
          disabled={isSubmitDisabled}
        >
          {loading ? (
            <CircularProgress size={24} />
          ) : editingVariable ? (
            "Update"
          ) : (
            "Add"
          )}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default AddVariableModal;
