import { Alert, Box, CircularProgress, Typography } from "@mui/material";
import React from "react";

interface NameValidationProps {
  name: string;
  isChecking: boolean;
  validationResult?: {
    isNameUnique?: boolean;
    message?: string;
  };
  minLength?: number;
  customSuccessMessage?: string;
  customErrorMessage?: string;
}

const NameValidation = ({
  name,
  isChecking,
  validationResult,
  minLength = 3,
  customSuccessMessage = "This name is available for use!",
  customErrorMessage = "This name is already taken. Please choose another.",
}: NameValidationProps) => {
  // Don't show anything if name is shorter than minimum length
  if (name.length < minLength) {
    return null;
  }

  // Show loading state
  if (isChecking) {
    return (
      <Box sx={{ display: "flex", alignItems: "center", gap: 1, mt: 1 }}>
        <CircularProgress size={20} />
        <Typography variant="body2" color="text.secondary">
          Checking name availability...
        </Typography>
      </Box>
    );
  }

  // Show success state
  if (validationResult?.isNameUnique) {
    return (
      <Alert severity="success" className="mt-2">
        {customSuccessMessage}
      </Alert>
    );
  }

  // Show error state
  if (validationResult?.isNameUnique === false) {
    return (
      <Alert severity="error" className="mt-2">
        {validationResult.message || customErrorMessage}
      </Alert>
    );
  }

  return null;
};

export default NameValidation;
