import React, { useEffect, useState } from "react";
import {
  Box,
  TextField,
  Autocomplete,
  CircularProgress,
  Button,
  Typography,
  FormControlLabel,
  Checkbox,
} from "@mui/material";
import { getOrgSnapshot } from "@/requests/agent-snapshots/get-org-snapshot";
import { createAgentFromSnapshot } from "@/requests/agent-snapshots/create-agent-from-snapshot";
import { useDispatch } from "react-redux";
import { useRouter } from "next/router";
import { useCheckAgentName } from "@/hooks/useCheckAgentName";
import {
  PROPERTY_LABELS,
  SYNCABLE_PROPERTIES,
} from "@/components/organization-tab/iframe/snapshots/property-labels";

interface Snapshot {
  _id: string;
  name: string;
  description: string;
}

interface BuildAgentFromSnapshotProps {
  onSubmit: (data: any) => void;
  onFirstPageBack: () => void;
}

export const BuildAgentFromSnapshot: React.FC<BuildAgentFromSnapshotProps> = ({
  onSubmit,
  onFirstPageBack,
}) => {
  const dispatch = useDispatch();
  const router = useRouter();
  const { folder } = router.query as { folder: string };
  const [snapshots, setSnapshots] = useState<Snapshot[]>([]);
  const [selectedSnapshot, setSelectedSnapshot] = useState<Snapshot | null>(
    null
  );
  const [agentName, setAgentName] = useState("");
  const { isChecking: isCheckingName, validationResult: nameValidationResult } =
    useCheckAgentName(agentName);
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedProperties, setSelectedProperties] = useState<string[]>([]);
  const [selectAllProperties, setSelectAllProperties] = useState(false);

  const orgId = localStorage.getItem("orgId");

  useEffect(() => {
    const fetchSnapshots = async () => {
      setIsLoading(true);
      try {
        const response = await getOrgSnapshot(
          { organizationId: orgId || "" },
          dispatch
        );
        setSnapshots(response.data);
      } catch (error) {
        console.error("Failed to fetch snapshots:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchSnapshots();
  }, [dispatch, orgId]);

  const handlePropertySelectAll = (checked: boolean) => {
    setSelectAllProperties(checked);
    setSelectedProperties(checked ? [...SYNCABLE_PROPERTIES] : []);
  };

  const handlePropertyToggle = (property: string) => {
    setSelectedProperties((prev) => {
      const newSelection = prev.includes(property)
        ? prev.filter((p) => p !== property)
        : [...prev, property];
      setSelectAllProperties(
        newSelection.length === SYNCABLE_PROPERTIES.length
      );
      return newSelection;
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedSnapshot || !agentName) return;

    setIsSubmitting(true);
    try {
      const response = await createAgentFromSnapshot(
        {
          snapshotId: selectedSnapshot._id,
          agentName,
          folderId: folder,
          propertiesToSync: selectedProperties,
        },
        dispatch
      );
      onSubmit({ success: true });
      await router.push(`/home/<USER>/${response.data._id}`);
    } catch (error) {
      console.error("Failed to create agent:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Box component="form" onSubmit={handleSubmit}>
      <Typography variant="h6" gutterBottom>
        Build from Snapshot
      </Typography>
      <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
        Select a snapshot, provide a name, and choose properties to sync for
        your new agent
      </Typography>

      <Autocomplete
        options={snapshots}
        loading={isLoading}
        getOptionLabel={(option) => option.name}
        renderOption={(props, option) => (
          <li {...props}>
            <Box>
              <Typography variant="body1">{option.name}</Typography>
              <Typography variant="body2" color="text.secondary">
                {option.description}
              </Typography>
            </Box>
          </li>
        )}
        value={selectedSnapshot}
        onChange={(_, newValue) => setSelectedSnapshot(newValue)}
        renderInput={(params) => (
          <TextField
            {...params}
            label="Select Snapshot"
            required
            InputProps={{
              ...params.InputProps,
              endAdornment: (
                <>
                  {isLoading && <CircularProgress size={20} />}
                  {params.InputProps.endAdornment}
                </>
              ),
            }}
          />
        )}
        sx={{ mb: 3 }}
      />

      <TextField
        fullWidth
        label="Agent Name"
        value={agentName}
        onChange={(e) => setAgentName(e.target.value)}
        required
        error={!isCheckingName && nameValidationResult.isNameUnique === false}
        helperText={
          !isCheckingName && nameValidationResult.isNameUnique === false
            ? "This name is already taken"
            : ""
        }
        InputProps={{
          endAdornment: isCheckingName && (
            <CircularProgress size={20} sx={{ mr: 1 }} />
          ),
        }}
        sx={{ mb: 3 }}
      />

      <Box sx={{ mb: 3 }}>
        <Typography variant="subtitle1" gutterBottom>
          Properties to Sync
        </Typography>
        <FormControlLabel
          control={
            <Checkbox
              checked={selectAllProperties}
              onChange={(e) => handlePropertySelectAll(e.target.checked)}
            />
          }
          label="Select All Properties"
          sx={{ mb: 1 }}
        />
        <Box sx={{ display: "flex", flexDirection: "column", gap: 1 }}>
          {SYNCABLE_PROPERTIES.map((property) => (
            <FormControlLabel
              key={property}
              control={
                <Checkbox
                  checked={selectedProperties.includes(property)}
                  onChange={() => handlePropertyToggle(property)}
                />
              }
              label={PROPERTY_LABELS[property]}
            />
          ))}
        </Box>
      </Box>

      <Box sx={{ display: "flex", justifyContent: "space-between", mt: 2 }}>
        <Button variant="outlined" onClick={onFirstPageBack}>
          Back
        </Button>
        <Button
          type="submit"
          variant="contained"
          disabled={
            !selectedSnapshot ||
            !agentName ||
            isSubmitting ||
            isCheckingName ||
            nameValidationResult.isNameUnique === false ||
            selectedProperties.length === 0
          }
        >
          {isSubmitting ? (
            <>
              Creating... <CircularProgress size={20} sx={{ ml: 1 }} />
            </>
          ) : (
            "Create Agent"
          )}
        </Button>
      </Box>
    </Box>
  );
};
