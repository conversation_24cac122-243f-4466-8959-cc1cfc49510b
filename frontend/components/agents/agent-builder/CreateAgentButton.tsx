import React from "react";
import { Button, CircularProgress } from "@mui/material";
import { styled } from "@mui/material/styles";
import { AgentStatusType } from "@/hooks/useAgentSocket";

interface CreateAgentButtonProps {
  variant?: "text" | "outlined" | "contained";
  color?:
    | "inherit"
    | "primary"
    | "secondary"
    | "success"
    | "error"
    | "info"
    | "warning";
  onClick: () => void;
  disabled?: boolean;
  status?: AgentStatusType | null;
}

const StyledButton = styled(Button)(({ theme }) => ({
  minWidth: 180,
  position: "relative",
  "& .MuiCircularProgress-root": {
    position: "absolute",
    left: "50%",
    marginLeft: -12,
  },
}));

const getButtonText = (status: AgentStatusType | null): string => {
  switch (status) {
    case "creating":
      return "Creating Chatbot...";
    case "queuing_files":
      return "Queuing files...";
    case "ready":
      return "Chatbot Ready";
    case "error":
      return "Error Creating Chatbot";
    default:
      return "Create Chatbot";
  }
};

const isLoading = (status: AgentStatusType | null): boolean => {
  return status === "creating" || status === "queuing_files";
};

export const CreateAgentButton: React.FC<CreateAgentButtonProps> = ({
  variant = "contained",
  color = "primary",
  onClick,
  disabled = false,
  status = null,
}) => {
  const loading = isLoading(status);
  const buttonText = getButtonText(status);
  const isError = status === "error";
  const isReady = status === "ready";

  return (
    <StyledButton
      variant={variant}
      color={isError ? "error" : isReady ? "success" : color}
      onClick={onClick}
      disabled={disabled || loading}
      type="submit"
    >
      {buttonText}
      {loading && <CircularProgress size={24} color="inherit" />}
    </StyledButton>
  );
};
