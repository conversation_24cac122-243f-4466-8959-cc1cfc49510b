import React, { useState, useEffect } from "react";
import {
  TextField,
  Autocomplete,
  Checkbox,
  FormGroup,
  FormControlLabel,
  Button,
  Box,
  CardContent,
  Typography,
  CircularProgress,
  Alert,
  AlertT<PERSON>le,
  FormLabel,
  styled,
} from "@mui/material";
import { CheckCir<PERSON>, Error } from "@mui/icons-material";
import { checkIfUniqueName } from "@/requests/agents/basic/checkIfUniqueName";
import { useDispatch } from "react-redux";
import { getOrgAgents } from "@/requests/agents/basic/getAgent";
import { useRouter } from "next/router";
import { useCreateChatbot } from "@/hooks/useCreateChatbot";
import NameValidation from "./NameValidation";
import { useCheckAgentName } from "@/hooks/useCheckAgentName";

interface Agent {
  _id: string;
  agentName: string;
}

interface BuildRawFormProps {
  onSubmit: (data: any) => Promise<void>;
  onFirstPageBack: () => void;
}

const StyledForm = styled("form")(({ theme }) => ({
  display: "flex",
  flexDirection: "column",
  gap: theme.spacing(3),
}));

export const BuildRawForm: React.FC<BuildRawFormProps> = ({
  onSubmit,
  onFirstPageBack,
}) => {
  const dispatch = useDispatch();
  const router = useRouter();
  const { createChatbot, isLoading, error } = useCreateChatbot();
  const { folder } = router.query as { folder: string };
  const [botName, setBotName] = useState("");
  const { isChecking: isCheckingName, validationResult: nameValidationResult } =
    useCheckAgentName(botName);
  const [selectedAgent, setSelectedAgent] = useState<Agent | null>(null);
  const [isLoadingAgents, setIsLoadingAgents] = useState(false);
  const [availableAgents, setAvailableAgents] = useState<Agent[]>([]);
  const [agentLoadError, setAgentLoadError] = useState<string | null>(null);
  const [configOptions, setConfigOptions] = useState({
    aiProviders: false,
    prompts: false,
    dataSources: false,
    savedSessions: false,
  });

  const orgId = localStorage.getItem("orgId") as string;

  // Fetch agent list on component mount
  useEffect(() => {
    const fetchAgents = async () => {
      setIsLoadingAgents(true);
      setAgentLoadError(null);
      try {
        const response = await getOrgAgents(
          { orgId, onlyName: true },
          dispatch
        );
        if (response?.status === 200) {
          setAvailableAgents(response.data.data);
        }
      } catch (error) {
        console.error("Error fetching agents:", error);
        setAgentLoadError(
          "Failed to load available agents. Please try again later."
        );
      } finally {
        setIsLoadingAgents(false);
      }
    };

    fetchAgents();
  }, [orgId, dispatch]);

  const handleConfigOptionChange = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    setConfigOptions({
      ...configOptions,
      [event.target.name]: event.target.checked,
    });
  };

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    if (!nameValidationResult.isNameUnique) return;

    try {
      await createChatbot({
        agentName: botName,
        agentId: selectedAgent?._id || null,
        orgId,
        agentImports: selectedAgent ? configOptions : {},
        folder,
      });
    } catch (err) {
      console.error("Error creating chatbot:", err);
    }
  };

  const isFormValid =
    botName.length >= 3 && nameValidationResult.isNameUnique && !isCheckingName;

  // Render error message if exists
  const renderError = () => {
    if (error) {
      return (
        <Alert severity="error" sx={{ mt: 2 }}>
          <AlertTitle>Error</AlertTitle>
          {error}
        </Alert>
      );
    }
    return null;
  };

  return (
    <Box>
      <Typography variant="h6" gutterBottom>
        Create New Chatbot
      </Typography>
      <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
        Configure your new chatbot settings
      </Typography>
      {renderError()}
      <StyledForm onSubmit={handleSubmit}>
        <Box>
          <TextField
            fullWidth
            label="Bot Name"
            value={botName}
            onChange={(e) => setBotName(e.target.value)}
            required
            error={
              botName.length >= 3 && nameValidationResult.isNameUnique === false
            }
            helperText={
              botName.length > 0 && botName.length < 3
                ? "Bot name must be at least 3 characters long"
                : ""
            }
          />
          <NameValidation
            name={botName}
            isChecking={isCheckingName}
            validationResult={nameValidationResult}
            customErrorMessage="This bot name is already taken. Please choose another."
          />
        </Box>

        <Box>
          <Autocomplete
            options={availableAgents}
            getOptionLabel={(option) => option.agentName}
            renderInput={(params) => (
              <TextField
                {...params}
                label="Import Configuration (Optional)"
                helperText="Select an existing agent to import its configuration"
                InputProps={{
                  ...params.InputProps,
                  endAdornment: (
                    <>
                      {isLoadingAgents && (
                        <CircularProgress color="inherit" size={20} />
                      )}
                      {params.InputProps.endAdornment}
                    </>
                  ),
                }}
              />
            )}
            value={selectedAgent}
            onChange={(_, newValue) => setSelectedAgent(newValue)}
            loading={isLoadingAgents}
            disabled={isLoadingAgents}
            fullWidth
          />
          {agentLoadError && (
            <Alert severity="error" sx={{ mt: 1 }}>
              {agentLoadError}
            </Alert>
          )}
        </Box>

        {selectedAgent && (
          <Box>
            <FormLabel component="legend" sx={{ mb: 1 }}>
              Configuration Options
            </FormLabel>
            <FormGroup>
              {[
                { name: "aiProviders", label: "AI Providers" },
                { name: "prompts", label: "Prompts" },
                { name: "dataSources", label: "Data Sources" },
                { name: "savedSessions", label: "Saved Sessions" },
              ].map(({ name, label }) => (
                <FormControlLabel
                  key={name}
                  control={
                    <Checkbox
                      checked={
                        configOptions[name as keyof typeof configOptions]
                      }
                      onChange={handleConfigOptionChange}
                      name={name}
                    />
                  }
                  label={label}
                />
              ))}
            </FormGroup>
          </Box>
        )}

        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            mt: 2,
            gap: 2,
          }}
        >
          <Button onClick={onFirstPageBack} variant="outlined">
            Back
          </Button>
          <Button
            type="submit"
            variant="contained"
            color="primary"
            disabled={!isFormValid || isLoading}
          >
            {isLoading ? (
              <>
                <CircularProgress size={20} sx={{ mr: 1 }} />
                Creating chatbot...
              </>
            ) : (
              "Create Chatbot"
            )}
          </Button>
        </Box>
      </StyledForm>
    </Box>
  );
};

export default BuildRawForm;
