import React, { useEffect, useState } from "react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Box,
  Stepper,
  Step,
  StepLabe<PERSON>,
  Typography,
  IconButton,
  Autocomplete,
} from "@mui/material";
import DeleteIcon from "@mui/icons-material/Delete";
import Add from "@mui/icons-material/Add";
import IntegrateCalendar from "@/components/home/<USER>/steps/IntgerateCalendar";
import { IQuickActionProp } from "@/components/home/<USER>/QuickActionFaqs";
import { useUser } from "@auth0/nextjs-auth0/client";
import { useDispatch } from "react-redux";
import { checkIfUniqueName } from "@/requests/agents/basic/checkIfUniqueName";
import NameValidation from "./NameValidation";
import EmptyFAQCard from "./EmptyFAQCard";
import { agentStatusDisplayMap, useAgentApi } from "@/hooks/useAgentSocket";
import { RoleOption, roleOptions } from "@/helpers/agent-templates";

interface GuidedBuildFormProps {
  onSubmit: (data: any) => void;
  initialData?: {
    name: string;
    description: string;
    website: string;
    faqs: Array<{ question: string; answer: string }>;
  };
  onFirstPageBack: () => void;
}

export type Faq = { question: string; answer: string };

export type FormDataType = {
  name: string;
  description: string;
  website?: string;
  faqs: Faq[];
  calendarDetails?: IQuickActionProp["calendarDetails"];
};

const isValidUrl = (url: string): boolean => {
  if (!url) return true;
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
};

export const GuidedBuildForm: React.FC<GuidedBuildFormProps> = ({
  onSubmit,
  initialData,
  onFirstPageBack,
}) => {
  const dispatch = useDispatch();
  const { user } = useUser();
  const [activeStep, setActiveStep] = useState(0);
  const [isCheckingName, setIsCheckingName] = useState(false);
  const [nameValidationResult, setNameValidationResult] = useState<{
    isNameUnique?: boolean;
    agentId?: string;
  }>({});
  const [formData, setFormData] = useState<FormDataType>({
    name: "",
    description: "",
    website: "",
    faqs: [],
  });
  const [websiteError, setWebsiteError] = useState("");
  const [errorMessage, setErrorMessage] = useState("");
  const { error, initializeAgent, status } = useAgentApi();

  const orgId = localStorage.getItem("orgId") as string;

  useEffect(() => {
    if (initialData) {
      setFormData((prevData) => ({
        ...prevData,
        ...initialData,
      }));
    }
  }, [initialData]);

  useEffect(() => {
    const checkBotName = async () => {
      if (formData.name.length < 3) {
        setNameValidationResult({});
        return;
      }

      setIsCheckingName(true);
      try {
        const response = await checkIfUniqueName(
          { agentName: formData.name },
          dispatch
        );
        const responseData = response.data;
        setNameValidationResult(
          responseData as { isNameUnique: boolean; agentId: string }
        );
      } catch (error) {
        console.error("Error checking bot name:", error);
      } finally {
        setIsCheckingName(false);
      }
    };
    const debounceTimer = setTimeout(checkBotName, 500);
    return () => clearTimeout(debounceTimer);
  }, [formData.name, dispatch]);

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = event.target;

    if (name === "website") {
      if (!isValidUrl(value)) {
        setWebsiteError("Please enter a valid URL (e.g., https://example.com)");
      } else {
        setWebsiteError("");
      }
    }

    setFormData((prevData) => ({
      ...prevData,
      [name]: type === "checkbox" ? checked : value,
    }));
  };

  const handleRoleChange = (
    _: React.SyntheticEvent,
    value: RoleOption | null
  ) => {
    setFormData((prevData) => ({
      ...prevData,
      description: value?.description || "",
    }));
  };

  // Rest of the component remains the same...
  const handleFaqChange = (
    index: number,
    field: "question" | "answer",
    value: string
  ) => {
    const newFaqs = [...formData.faqs];
    newFaqs[index][field] = value;
    setFormData((prevData) => ({
      ...prevData,
      faqs: newFaqs,
    }));
  };

  const addFaq = () => {
    setFormData((prevData) => ({
      ...prevData,
      faqs: [...prevData.faqs, { question: "", answer: "" }],
    }));
  };

  const deleteFaq = (index: number) => {
    setFormData((prevData) => ({
      ...prevData,
      faqs: prevData.faqs.filter((_, i) => i !== index),
    }));
  };

  const isFirstStepValid = () => {
    if (
      formData.name.length < 3 ||
      !nameValidationResult.isNameUnique ||
      !formData.description ||
      isCheckingName
    ) {
      return false;
    }
    if (formData.website && !isValidUrl(formData.website)) {
      return false;
    }
    return true;
  };

  const handleNext = () => {
    if (activeStep === 0 && !isFirstStepValid()) {
      setErrorMessage(
        "Please ensure the bot name is valid and unique, and the website URL is valid before proceeding."
      );
      return;
    }

    if (activeStep === 1) {
      const hasEmptyAnswer = formData.faqs.some((faq) => faq.answer === "");
      if (hasEmptyAnswer) {
        setErrorMessage("Please fill in all answers before proceeding.");
        return;
      }
    }
    setErrorMessage("");
    setActiveStep((prevStep) => prevStep + 1);
  };

  const handleBack = () => {
    if (activeStep === 0) {
      onFirstPageBack();
    }
    setActiveStep((prevStep) => prevStep - 1);
  };

  const handleSubmit = (event: React.FormEvent) => {
    event.preventDefault();
  };

  const handleInitialize = () => {
    // initializeAgent({
    //   agentConfig: formData,
    // });
  };

  const steps = ["Basic Information", "FAQs", "Link Calendar"];

  return (
    <form onSubmit={handleSubmit}>
      <Stepper activeStep={activeStep} alternativeLabel>
        {steps.map((label) => (
          <Step key={label}>
            <StepLabel>{label}</StepLabel>
          </Step>
        ))}
      </Stepper>
      {activeStep === 0 && (
        <Box sx={{ mt: 2 }}>
          <TextField
            fullWidth
            label="Internal Name"
            name="name"
            value={formData.name}
            onChange={handleInputChange}
            margin="normal"
            required
            error={
              formData.name.length >= 3 &&
              nameValidationResult.isNameUnique === false
            }
            helperText={
              formData.name.length > 0 && formData.name.length < 3
                ? "Bot name must be at least 3 characters long"
                : ""
            }
          />
          <NameValidation
            name={formData.name}
            isChecking={isCheckingName}
            validationResult={nameValidationResult}
            customErrorMessage="This bot name is already taken. Please choose another."
          />
          <Autocomplete
            fullWidth
            options={roleOptions}
            getOptionLabel={(option) => option.label}
            onChange={handleRoleChange}
            renderInput={(params) => (
              <TextField
                {...params}
                label="Chatbot Role"
                margin="normal"
                required
                helperText="Choose a role to automatically set the description"
              />
            )}
          />
          <TextField
            fullWidth
            label="Website"
            name="website"
            value={formData.website}
            onChange={handleInputChange}
            error={Boolean(websiteError)}
            helperText={websiteError || "This website will be scrapped."}
            margin="normal"
          />
          {errorMessage && (
            <Typography color="error" sx={{ mt: 2 }}>
              {errorMessage}
            </Typography>
          )}
        </Box>
      )}
      {activeStep === 1 && (
        <Box sx={{ mt: 2 }}>
          {!formData.faqs || formData.faqs.length === 0 ? (
            <EmptyFAQCard onAddFaq={addFaq} />
          ) : (
            <>
              {formData.faqs.map((faq, index) => (
                <Box
                  key={index}
                  sx={{ display: "flex", alignItems: "flex-start", mb: 2 }}
                >
                  <Box sx={{ flexGrow: 1 }}>
                    <TextField
                      fullWidth
                      label={`Question ${index + 1}`}
                      value={faq.question}
                      onChange={(e) =>
                        handleFaqChange(index, "question", e.target.value)
                      }
                      margin="normal"
                      required
                    />
                    <TextField
                      fullWidth
                      label={`Answer ${index + 1}`}
                      value={faq.answer}
                      onChange={(e) =>
                        handleFaqChange(index, "answer", e.target.value)
                      }
                      margin="normal"
                      multiline
                      rows={2}
                      required
                    />
                  </Box>
                  <IconButton
                    color="error"
                    onClick={() => deleteFaq(index)}
                    sx={{ mt: 1, ml: 2 }}
                  >
                    <DeleteIcon />
                  </IconButton>
                </Box>
              ))}
              <Button variant="outlined" onClick={addFaq} startIcon={<Add />}>
                Add FAQ
              </Button>
            </>
          )}

          {errorMessage && (
            <Typography color="error" sx={{ mt: 2 }}>
              {errorMessage}
            </Typography>
          )}
        </Box>
      )}
      {activeStep === 2 && (
        <Box sx={{ mt: 2 }}>
          <IntegrateCalendar
            calendarDetails={formData?.calendarDetails}
            setCalendarDetails={(x: IQuickActionProp["calendarDetails"]) =>
              setFormData({ ...formData, calendarDetails: x })
            }
          />
        </Box>
      )}
      <Box sx={{ mt: 2, display: "flex", justifyContent: "space-between" }}>
        <Button onClick={handleBack}>Back</Button>
        {activeStep === steps.length - 1 ? (
          <Button
            type="submit"
            variant="contained"
            color="primary"
            disabled={Boolean(status?.status)}
            onClick={handleInitialize}
          >
            {status?.status
              ? agentStatusDisplayMap[status.status]
              : "Create Chatbot"}
          </Button>
        ) : (
          <Button
            variant="contained"
            color="primary"
            onClick={handleNext}
            disabled={activeStep === 0 && !isFirstStepValid()}
          >
            Next
          </Button>
        )}
      </Box>
    </form>
  );
};
