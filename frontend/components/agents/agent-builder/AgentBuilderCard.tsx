import React from "react";
import { Card, CardContent, Typography } from "@mui/material";
import { grey } from "@mui/material/colors";

interface AgentBuilderCardProps {
  title: string;
  description: string;
  selected: boolean;
  onSelect: () => void;
}

const AgentBuilderCard: React.FC<AgentBuilderCardProps> = ({
  title,
  description,
  selected,
  onSelect,
}) => {
  return (
    <Card
      variant="outlined"
      sx={{
        cursor: "pointer",
        "&:hover": { borderColor: "primary.main" },
        bgcolor: selected ? grey[200] : "",
      }}
      onClick={onSelect}
    >
      <CardContent>
        <Typography variant="subtitle1">{title}</Typography>
        <Typography variant="body2" color="text.secondary">
          {description}
        </Typography>
      </CardContent>
    </Card>
  );
};

export default AgentBuilderCard;
