import React from "react";
import { <PERSON><PERSON>ield, IconButton, Button, Box, Typography } from "@mui/material";
import DeleteIcon from "@mui/icons-material/Delete";
import Add from "@mui/icons-material/Add";
import { Faq } from "./guided-build.types";
import EmptyFAQCard from "../EmptyFAQCard";

interface FaqSectionProps {
  faqs: Faq[];
  onFaqChange: (
    index: number,
    field: "question" | "answer",
    value: string
  ) => void;
  onAddFaq: () => void;
  onDeleteFaq: (index: number) => void;
  errorMessage: string;
}

export const FaqSection: React.FC<FaqSectionProps> = ({
  faqs,
  onFaqChange,
  onAddFaq,
  onDeleteFaq,
  errorMessage,
}) => (
  <Box sx={{ mt: 2 }}>
    {!faqs || faqs.length === 0 ? (
      <EmptyFAQCard onAddFaq={onAddFaq} />
    ) : (
      <>
        {faqs.map((faq, index) => (
          <Box
            key={index}
            sx={{ display: "flex", alignItems: "flex-start", mb: 2 }}
          >
            <Box sx={{ flexGrow: 1 }}>
              <TextField
                fullWidth
                label={`Question ${index + 1}`}
                value={faq.question}
                onChange={(e) => onFaqChange(index, "question", e.target.value)}
                margin="normal"
                required
              />
              <TextField
                fullWidth
                label={`Answer ${index + 1}`}
                value={faq.answer}
                onChange={(e) => onFaqChange(index, "answer", e.target.value)}
                margin="normal"
                multiline
                rows={2}
                required
              />
            </Box>
            <IconButton
              color="error"
              onClick={() => onDeleteFaq(index)}
              sx={{ mt: 1, ml: 2 }}
            >
              <DeleteIcon />
            </IconButton>
          </Box>
        ))}
        <Button variant="outlined" onClick={onAddFaq} startIcon={<Add />}>
          Add FAQ
        </Button>
      </>
    )}
    {errorMessage && (
      <Typography color="error" sx={{ mt: 2 }}>
        {errorMessage}
      </Typography>
    )}
  </Box>
);
