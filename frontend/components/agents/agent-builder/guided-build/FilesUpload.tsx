import { DropzoneArea } from "@/components/file-upload/DropzoneArea";
import { FileList } from "@/components/file-upload/FileList";
import { useFileUpload } from "@/hooks/useFileUpload";
import { FileUploadConfig, FileWithPreview } from "@/types/file";
import { <PERSON><PERSON>, <PERSON>ton, Typography } from "@mui/material";
import React, { useEffect } from "react";
import { FormDataType } from "./guided-build.types";

export const DEFAULT_CONFIG: FileUploadConfig = {
  maxFiles: 5,
  maxFileSize: 10 * 1024 * 1024, // 10MB
  acceptedFileTypes: {
    "application/pdf": [".pdf"],
    "text/plain": [".txt"],
    "application/msword": [".doc"],
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document": [
      ".docx",
    ],
    "application/vnd.ms-excel": [".xls"],
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": [
      ".xlsx",
    ],
  },
};

interface IProp {
  config?: FileUploadConfig;
  formData: FormDataType;
  onFilesChange: (files: FileWithPreview[]) => void;
}

const FilesUpload: React.FC<IProp> = ({
  config = DEFAULT_CONFIG,
  formData,
  onFilesChange,
}) => {
  const { files, setFiles, isSubmitting, setIsSubmitting, onDrop, removeFile } =
    useFileUpload(config);
  const [error, setError] = React.useState<string | null>(null);

  useEffect(() => {
    onFilesChange(files);
  }, [files]);

  const handleSubmit = async () => {
    try {
    } catch (error) {
    } finally {
    }
  };
  return (
    <div className="space-y-4">
      {error && (
        <Alert variant="standard" severity="error">
          <p>{error}</p>
        </Alert>
      )}

      <DropzoneArea config={config} onDrop={onDrop} />

      {files.length > 0 && (
        <div className="space-y-4">
          <FileList files={files} onRemove={removeFile} />
        </div>
      )}
    </div>
  );
};

export default FilesUpload;
