import { TextField } from "@mui/material";
import React from "react";
import { FormDataType } from "./guided-build.types";

interface IProp {
  formData: FormDataType;
  onInputChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  websiteError: string;
}

const WebsiteUpload: React.FC<IProp> = ({
  formData,
  onInputChange,
  websiteError,
}) => {
  return (
    <TextField
      fullWidth
      label="Website"
      name="website"
      value={formData.website}
      onChange={onInputChange}
      error={Boolean(websiteError)}
      helperText={websiteError || "This website will be crawled."}
      margin="normal"
    />
  );
};

export default WebsiteUpload;
