import { Stack, Typography } from "@mui/material";
import React from "react";
import FilesUpload from "./FilesUpload";
import WebsiteUpload from "./WebsiteUpload";
import { FormDataType } from "./guided-build.types";
import { FileWithPreview } from "@/types/file";

interface IProp {
  formData: FormDataType;
  onInputChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  onFilesChange: (files: FileWithPreview[]) => void;
  websiteError: string;
}

const AddKnowledgeStep: React.FC<IProp> = ({
  formData,
  onInputChange,
  onFilesChange,
  websiteError,
}) => {
  return (
    <>
      <Typography variant="h6" gutterBottom>
        (Optional) Add knowledge
      </Typography>
      <Typography variant="body2" color="text.secondary">
        {`In this step, you can enhance the AI's understanding by crawling a website or uploading relevant documents. This will provide additional insights and context about your company or specific use case.`}
      </Typography>

      <Stack
        direction="column"
        gap={2}
        pb={1}
        sx={{
          maxHeight: "90vh",
          overflowY: "auto",
          width: "100%",
        }}
      >
        {" "}
        <WebsiteUpload
          formData={formData}
          onInputChange={onInputChange}
          websiteError={websiteError}
        />
        <FilesUpload formData={formData} onFilesChange={onFilesChange} />
      </Stack>
    </>
  );
};

export default AddKnowledgeStep;
