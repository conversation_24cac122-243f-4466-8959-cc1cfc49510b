import { useState } from "react";
import { Faq, FormDataType } from "./guided-build.types";

export const useFormValidation = () => {
  const [websiteError, setWebsiteError] = useState("");
  const [errorMessage, setErrorMessage] = useState("");

  const isValidUrl = (url: string): boolean => {
    if (!url) return true;
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  };

  const validateFirstStep = (
    formData: FormDataType,
    nameValidationResult: { isNameUnique?: boolean },
    isCheckingName: boolean
  ): boolean => {
    if (
      formData.name.length < 3 ||
      !nameValidationResult.isNameUnique ||
      !formData.description ||
      isCheckingName
    ) {
      return false;
    }
    if (formData.website && !isValidUrl(formData.website)) {
      return false;
    }
    return true;
  };

  const validateFaqs = (faqs: Faq[]): boolean => {
    return !faqs.some((faq) => faq.answer === "");
  };

  return {
    websiteError,
    setWebsiteError,
    errorMessage,
    setErrorMessage,
    isValidUrl,
    validateFirstStep,
    validateFaqs,
  };
};
