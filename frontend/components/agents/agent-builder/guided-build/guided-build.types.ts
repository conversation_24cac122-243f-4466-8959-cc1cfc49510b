import { IQuickActionProp } from "@/components/home/<USER>/QuickActionFaqs";
import { FileWithPreview } from "@/types/file";

export type Faq = {
  question: string;
  answer: string;
};

export type FormDataType = {
  name: string;
  description: string;
  website?: string;
  faqs: Faq[];
  files: FileWithPreview[];
  channel: {
    account: AccountOption | null;
    isCreateFirstTrigger: boolean;
    isImportVariables: boolean;
  };
  calendarDetails?: IQuickActionProp["calendarDetails"];
};

export type NameValidationResult = {
  isNameUnique?: boolean;
  agentId?: string;
};

export interface GuidedBuildFormProps {
  onSubmit: (data: FormDataType) => void;
  initialData?: {
    name: string;
    description: string;
    website: string;
    faqs: Faq[];
  };
  onFirstPageBack: () => void;
}

export interface Account {
  accountId: string;
  name: string;
  providerName: string;
}

export interface AccountOption extends Account {
  isAddNew?: boolean;
}
