import { useState, useEffect } from "react";
import { useDispatch } from "react-redux";
import { checkIfUniqueName } from "@/requests/agents/basic/checkIfUniqueName";
import { NameValidationResult } from "./guided-build.types";

export const useNameValidation = (name: string) => {
  const dispatch = useDispatch();
  const [isCheckingName, setIsCheckingName] = useState(false);
  const [nameValidationResult, setNameValidationResult] =
    useState<NameValidationResult>({});

  useEffect(() => {
    const checkBotName = async () => {
      if (name.length < 3) {
        setNameValidationResult({});
        return;
      }

      setIsCheckingName(true);
      try {
        const response = await checkIfUniqueName({ agentName: name }, dispatch);
        setNameValidationResult(response.data);
      } catch (error) {
        console.error("Error checking bot name:", error);
      } finally {
        setIsCheckingName(false);
      }
    };

    const debounceTimer = setTimeout(checkBotName, 500);
    return () => clearTimeout(debounceTimer);
  }, [name, dispatch]);

  return { isCheckingName, nameValidationResult };
};
