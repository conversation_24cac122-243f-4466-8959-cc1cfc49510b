import { IVariable, agentsAction } from "@/slices/agents/agentsSlice";
import React, { useEffect, useState } from "react";
import {
  Box,
  Typography,
  Button,
  Stack,
  Paper,
  CircularProgress,
  Alert,
} from "@mui/material";
import EditIcon from "@mui/icons-material/Edit";
import SaveIcon from "@mui/icons-material/Save";
import CancelIcon from "@mui/icons-material/Cancel";
import LocalOfferIcon from "@mui/icons-material/LocalOffer";
import EnhancedTextField from "../../editAgent/dataSources/TextField";
import { FormDataType } from "./guided-build.types";
import { getVariablesByAccountId } from "@/requests/agents/variables/getVariablesByAccountId";
import { useDispatch } from "react-redux";

interface IProps {
  prompt?: string;
  variables?: IVariable[];
  onSave?: (newPrompt: string) => void;
  channelDetails: FormDataType["channel"];
}

const DEFAULT_PROMPT = "This is a test";

const ReviewStep: React.FC<IProps> = ({
  prompt = DEFAULT_PROMPT,
  onSave,
  channelDetails,
}) => {
  const dispatch = useDispatch();
  const [loading, setLoading] = useState(false);
  const [variables, setVariables] = useState<IVariable[]>([]);
  const [isEditing, setIsEditing] = useState(false);
  const [editedPrompt, setEditedPrompt] = useState(prompt);

  const handleEdit = () => {
    setIsEditing(true);
    setEditedPrompt(prompt);
  };

  const handleSave = () => {
    onSave?.(editedPrompt);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setIsEditing(false);
    setEditedPrompt(prompt);
  };

  const handleChange = (value: string) => {
    setEditedPrompt(value);
  };

  const fetchCustomValues = async (accountId: string) => {
    if (!accountId) return [];
    setLoading(true);
    try {
      const response = await getVariablesByAccountId({ accountId }, dispatch);
      if (response.status === 200) {
        return response.data;
      }
      return [];
    } catch (error) {
      console.error("Error fetching custom values:", error);
      return [];
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    const accountId = channelDetails.account?.accountId;
    if (channelDetails.isImportVariables && accountId) {
      fetchCustomValues(accountId).then((data) => {
        setVariables(data);
        dispatch(agentsAction.addVariablesList(data));
      });
    } else {
      setVariables([]);
      dispatch(agentsAction.addVariablesList([]));
    }
  }, [channelDetails.account?.accountId, channelDetails.isImportVariables]);

  return (
    <Stack spacing={3}>
      <Box>
        <Box
          display="flex"
          justifyContent="space-between"
          alignItems="flex-start"
          mb={1}
        >
          <Box>
            <Typography variant="h6" gutterBottom>
              (Optional) Prompt Review
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Some info about this step
            </Typography>
          </Box>
        </Box>

        <Typography variant="subtitle2" gutterBottom sx={{ mt: 3 }}>
          Prompt Content
        </Typography>

        {isEditing ? (
          <EnhancedTextField
            fullWidth
            label=""
            multiline
            minRows={4}
            maxRows={12}
            required
            id={`Prompt_content_${crypto.randomUUID()}`}
            value={editedPrompt}
            onChange={handleChange}
            isRippleEffect={true}
          />
        ) : (
          <Paper
            elevation={1}
            sx={{
              p: 2,
              backgroundColor: (theme) => theme.palette.background.default,
            }}
          >
            <Typography
              variant="body1"
              sx={{
                whiteSpace: "pre-wrap",
                backgroundColor: (theme) => theme.palette.background.paper,
                borderRadius: 1,
              }}
            >
              {prompt}
            </Typography>
          </Paper>
        )}
        <Box>
          {!isEditing ? (
            <Button
              onClick={handleEdit}
              color="primary"
              startIcon={<EditIcon />}
              size="small"
              sx={{ mt: 1 }}
            >
              Edit
            </Button>
          ) : (
            <Stack direction="row" spacing={1} sx={{ mt: 1 }}>
              <Button
                onClick={handleSave}
                color="primary"
                startIcon={<SaveIcon />}
                size="small"
              >
                Save
              </Button>
              <Button
                onClick={handleCancel}
                color="error"
                startIcon={<CancelIcon />}
                size="small"
              >
                Cancel
              </Button>
            </Stack>
          )}
        </Box>

        <Box sx={{ mt: 3 }}>
          {/* <Typography variant="subtitle2" gutterBottom>
            Available Variables:
          </Typography> */}

          {loading ? (
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                gap: 2,
                bgcolor: "background.paper",
                p: 3,
                borderRadius: 1,
                justifyContent: "center",
              }}
            >
              <CircularProgress size={24} />
              <Typography variant="body2" color="text.secondary">
                Fetching custom values from GHL...
              </Typography>
            </Box>
          ) : variables.length > 0 ? (
            <Alert
              icon={<LocalOfferIcon />}
              severity="info"
              sx={{ alignItems: "center" }}
            >
              {variables.length} custom values imported from Highlevel.
            </Alert>
          ) : null}
        </Box>
      </Box>
    </Stack>
  );
};

export default ReviewStep;
