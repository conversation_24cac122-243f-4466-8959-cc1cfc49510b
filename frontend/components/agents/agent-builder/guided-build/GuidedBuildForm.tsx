import React, { useState, useEffect } from "react";
import { <PERSON>, But<PERSON>, Stepper, Step, StepLabel } from "@mui/material";
import { useUser } from "@auth0/nextjs-auth0/client";
import { agentStatusDisplayMap, useAgentApi } from "@/hooks/useAgentSocket";
import IntegrateCalendar from "@/components/home/<USER>/steps/IntgerateCalendar";
import { FormDataType, GuidedBuildFormProps } from "./guided-build.types";
import { useFormValidation } from "./useGuidedBuildForm";
import { useNameValidation } from "./useNameValidation";
import { RoleOption } from "@/helpers/agent-templates";
import { BasicInformation } from "./BasicInformationStep";
import { FaqSection } from "./FaqsStep";
import AddKnowledgeStep from "./AddKnowledgeStep";
import { FileWithPreview } from "@/types/file";
import LinkChannelStep from "./LinkChannelStep";
import ReviewStep from "./ReviewStep";

export const GuidedBuildForm: React.FC<GuidedBuildFormProps> = ({
  onSubmit,
  initialData,
  onFirstPageBack,
}) => {
  const { user } = useUser();
  const [activeStep, setActiveStep] = useState(0);
  const [formData, setFormData] = useState<FormDataType>({
    name: "",
    description: "",
    website: "",
    faqs: [],
    files: [],
    channel: {
      account: null,
      isCreateFirstTrigger: true,
      isImportVariables: true,
    },
  });

  const { error, initializeAgent, status } = useAgentApi();
  const {
    websiteError,
    setWebsiteError,
    errorMessage,
    setErrorMessage,
    validateFirstStep,
    validateFaqs,
  } = useFormValidation();
  const { isCheckingName, nameValidationResult } = useNameValidation(
    formData.name
  );

  useEffect(() => {
    if (initialData) {
      initialData.faqs = [];
      setFormData((prevData) => ({
        ...prevData,
        ...initialData,
      }));
    }
  }, [initialData]);

  const isValidUrl = (url: string): boolean => {
    if (!url) return true;
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  };

  useEffect(() => {
    console.log("New val ", formData);
  }, [formData]);

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = event.target;
    if (name === "website") {
      setWebsiteError(!isValidUrl(value) ? "Please enter a valid URL" : "");
    }
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleFilesChange = (files: FileWithPreview[]) => {
    setFormData((prev) => ({ ...prev, files }));
  };

  const handleChannelDetailsChange = (
    newChannelDetail: FormDataType["channel"]
  ) => {
    setFormData((prev) => ({ ...prev, channel: newChannelDetail }));
  };

  const handleRoleChange = (
    _: React.SyntheticEvent,
    value: RoleOption | null
  ) => {
    setFormData((prev) => ({
      ...prev,
      description: value?.description || "",
    }));
  };

  const handleFaqChange = (
    index: number,
    field: "question" | "answer",
    value: string
  ) => {
    const newFaqs = [...formData.faqs];
    newFaqs[index][field] = value;
    setFormData((prev) => ({ ...prev, faqs: newFaqs }));
  };

  const handleNext = () => {
    if (
      activeStep === 0 &&
      !validateFirstStep(formData, nameValidationResult, isCheckingName)
    ) {
      setErrorMessage("Please ensure all fields are valid before proceeding.");
      return;
    }

    if (activeStep === 1 && !validateFaqs(formData.faqs)) {
      setErrorMessage("Please fill in all answers before proceeding.");
      return;
    }

    setErrorMessage("");
    setActiveStep((prev) => prev + 1);
  };

  const handleBack = () => {
    if (activeStep === 0) {
      onFirstPageBack();
    }
    setActiveStep((prev) => prev - 1);
  };

  const handleChangeDescription = (description: string) => {
    setFormData((prev) => ({
      ...prev,
      description,
    }));
  };

  const handleInitialize = () => {
    formData.faqs = []; //Currently dont allow FAQs to be added
    initializeAgent({ agentConfig: formData });
  };

  const steps = [
    "Basic Information",
    //    "FAQs", //Temporalily disabling FAQs
    "Add Knowledge",
    "Link Channel",
    "Link Calendar",
    // "Review",
  ];

  return (
    <form onSubmit={(e) => e.preventDefault()}>
      <Stepper activeStep={activeStep} alternativeLabel>
        {steps.map((label) => (
          <Step key={label}>
            <StepLabel>{label}</StepLabel>
          </Step>
        ))}
      </Stepper>

      {activeStep === 0 && (
        <BasicInformation
          formData={formData}
          onInputChange={handleInputChange}
          onRoleChange={handleRoleChange}
          isCheckingName={isCheckingName}
          nameValidationResult={nameValidationResult}
          errorMessage={errorMessage}
        />
      )}

      {/* {activeStep === 1 && (
        <FaqSection
          faqs={formData.faqs}
          onFaqChange={handleFaqChange}
          onAddFaq={() =>
            setFormData((prev) => ({
              ...prev,
              faqs: [...prev.faqs, { question: "", answer: "" }],
            }))
          }
          onDeleteFaq={(index) =>
            setFormData((prev) => ({
              ...prev,
              faqs: prev.faqs.filter((_, i) => i !== index),
            }))
          }
          errorMessage={errorMessage}
        />
      )} */}
      {activeStep === 1 && (
        <>
          <Box sx={{ mt: 2 }}>
            <AddKnowledgeStep
              formData={formData}
              onInputChange={handleInputChange}
              onFilesChange={handleFilesChange}
              websiteError={websiteError}
            />
          </Box>
        </>
      )}
      {activeStep === 2 && (
        <>
          <Box sx={{ mt: 2 }}>
            <LinkChannelStep
              channelDetails={formData["channel"]}
              onChannelDetailsChange={handleChannelDetailsChange}
            />
          </Box>
        </>
      )}
      {activeStep === 3 && (
        <Box sx={{ mt: 2 }}>
          <IntegrateCalendar
            calendarDetails={formData.calendarDetails}
            setCalendarDetails={(x) =>
              setFormData((prev) => ({ ...prev, calendarDetails: x }))
            }
          />
        </Box>
      )}
      {/* {activeStep === 4 && (
        <Box sx={{ mt: 2 }}>
          <ReviewStep
            prompt={formData["description"]}
            onSave={(newDescription: string) => {
              handleChangeDescription(newDescription);
            }}
            channelDetails={formData["channel"]}
          />
        </Box>
      )} */}

      <Box sx={{ mt: 2, display: "flex", justifyContent: "space-between" }}>
        <Button onClick={handleBack}>Back</Button>
        {activeStep === steps.length - 1 ? (
          <Button
            variant="contained"
            color="primary"
            disabled={Boolean(status?.status)}
            onClick={handleInitialize}
          >
            {status?.status
              ? agentStatusDisplayMap[status.status]
              : "Create Chatbot"}
          </Button>
        ) : (
          <Button
            variant="contained"
            color="primary"
            onClick={handleNext}
            disabled={
              activeStep === 0 &&
              !validateFirstStep(formData, nameValidationResult, isCheckingName)
            }
          >
            Next
          </Button>
        )}
      </Box>
    </form>
  );
};
