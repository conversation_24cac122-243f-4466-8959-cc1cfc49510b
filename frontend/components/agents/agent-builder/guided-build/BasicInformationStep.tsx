import React from "react";
import { TextField, Autocomplete, Box, Typography } from "@mui/material";
import { RoleOption, roleOptions } from "@/helpers/agent-templates";
import { FormDataType, NameValidationResult } from "./guided-build.types";
import NameValidation from "../NameValidation";

interface BasicInformationProps {
  formData: FormDataType;
  onInputChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  onRoleChange: (event: React.SyntheticEvent, value: RoleOption | null) => void;
  isCheckingName: boolean;
  nameValidationResult: NameValidationResult;
  errorMessage: string;
}

export const BasicInformation: React.FC<BasicInformationProps> = ({
  formData,
  onInputChange,
  onRoleChange,
  isCheckingName,
  nameValidationResult,
  errorMessage,
}) => (
  <Box sx={{ mt: 2 }}>
    <TextField
      fullWidth
      label="Account Bot Name"
      name="name"
      value={formData.name}
      onChange={onInputChange}
      margin="normal"
      required
      error={
        formData.name.length >= 3 && nameValidationResult.isNameUnique === false
      }
      helperText={
        formData.name.length > 0 && formData.name.length < 3
          ? "Bot name must be at least 3 characters long"
          : ""
      }
    />
    <NameValidation
      name={formData.name}
      isChecking={isCheckingName}
      validationResult={nameValidationResult}
      customErrorMessage="This bot name is already taken. Please choose another."
    />
    <Autocomplete
      fullWidth
      options={roleOptions}
      getOptionLabel={(option) => option.label}
      onChange={onRoleChange}
      renderInput={(params) => (
        <TextField
          {...params}
          label="Chatbot Role"
          margin="normal"
          required
          helperText="Choose a role to automatically set the description"
        />
      )}
    />
    {errorMessage && (
      <Typography color="error" sx={{ mt: 2 }}>
        {errorMessage}
      </Typography>
    )}
  </Box>
);
