import useHighLevelChannelConnect from "@/hooks/useHighLevelChannelConnect";
import {
  <PERSON><PERSON>,
  <PERSON>ton,
  <PERSON>ack,
  Typo<PERSON>,
  Toolt<PERSON>,
  Divider,
} from "@mui/material";
import AddIcon from "@mui/icons-material/Add";
import DownloadIcon from "@mui/icons-material/Download";
import CheckIcon from "@mui/icons-material/Check";
import React, { useEffect } from "react";
import { AccountOption, FormDataType } from "./guided-build.types";
import { OpenInNew } from "@mui/icons-material";

interface IProp {
  channelDetails: FormDataType["channel"];
  onChannelDetailsChange: (channelDetails: FormDataType["channel"]) => void;
}

const LinkChannelStep: React.FC<IProp> = ({
  channelDetails,
  onChannelDetailsChange,
}) => {
  const { handleConnectClick, dataFromChild } = useHighLevelChannelConnect();

  // Handle account data from HighLevel
  useEffect(() => {
    if (dataFromChild?.providerName) {
      const { accountId, name, providerName } = dataFromChild;
      onChannelDetailsChange({
        ...channelDetails,
        account: {
          accountId: accountId || "",
          name: name || "",
          providerName,
        },
      });
    }
  }, [dataFromChild]); // eslint-disable-line react-hooks/exhaustive-deps

  const toggleOption = (
    option: "isCreateFirstTrigger" | "isImportVariables"
  ) => {
    onChannelDetailsChange({
      ...channelDetails,
      [option]: !channelDetails[option],
    });
  };

  // Common button styles for selected/unselected states
  const getButtonStyles = (isSelected: boolean) => ({
    minWidth: "200px",
    height: "45px",
    border: isSelected ? "2px solid" : "1px solid",
    backgroundColor: isSelected ? "action.selected" : "transparent",
    "&:hover": {
      backgroundColor: isSelected ? "action.selected" : "action.hover",
      border: isSelected ? "2px solid" : "1px solid",
    },
  });

  return (
    <>
      <Typography variant="h6" gutterBottom>
        (Optional) Connect to HighLevel
      </Typography>
      <Typography variant="body2" color="text.secondary" paragraph>
        {`In this step, connect your AI to your CRM system. This will allow automatic import of variables and custom fields from your CRM account. It also sets up the trigger or listener needed for AI interactions. Currently, this feature supports GoHighLevel, with more CRM integrations coming soon.`}
      </Typography>

      <Stack
        direction="column"
        gap={2}
        pb={1}
        sx={{
          maxHeight: "90vh",
          overflowY: "auto",
          width: "100%",
        }}
      >
        {!channelDetails.account ? (
          <Button
            variant="outlined"
            onClick={handleConnectClick}
            startIcon={<AddIcon />}
            fullWidth
          >
            Sign in with HighLevel
          </Button>
        ) : (
          <>
            <Alert severity="success">Account fetched</Alert>

            {/* <Divider sx={{ my: 2 }} /> */}

            {/* <Typography variant="h6" sx={{ mt: 2 }}>
              Additional Actions
            </Typography>

            <Stack
              direction="row"
              gap={2}
              sx={{
                flexWrap: "wrap",
                alignItems: "center",
              }}
            >
              <Tooltip title="Set up automated workflows with HighLevel">
                <Button
                  variant="outlined"
                  color="primary"
                  onClick={() => toggleOption("isCreateFirstTrigger")}
                  startIcon={
                    channelDetails.isCreateFirstTrigger ? (
                      <CheckIcon />
                    ) : (
                      <AddIcon />
                    )
                  }
                  sx={getButtonStyles(channelDetails.isCreateFirstTrigger)}
                >
                  Create Trigger
                </Button>
              </Tooltip>

              <Tooltip title="Import existing variables from HighLevel">
                <Button
                  variant="outlined"
                  color="primary"
                  onClick={() => toggleOption("isImportVariables")}
                  startIcon={
                    channelDetails.isImportVariables ? (
                      <CheckIcon />
                    ) : (
                      <DownloadIcon />
                    )
                  }
                  sx={getButtonStyles(channelDetails.isImportVariables)}
                >
                  Import Variables
                </Button>
              </Tooltip>
            </Stack> */}
          </>
        )}
        <Alert severity="info">
          Make sure you are logged in to{" "}
          <a
            href="https://app.gohighlevel.com"
            target="_blank"
            rel="noopener noreferrer"
            className="underline"
          >
            app.gohighlevel.com
            <OpenInNew
              sx={{
                fontSize: "1rem",
                marginLeft: "2px",
                verticalAlign: "text-bottom",
              }}
            />
          </a>{" "}
          in this browser.
        </Alert>
      </Stack>
    </>
  );
};

export default LinkChannelStep;
