import { FC } from "react";
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  useTheme,
} from "@mui/material";
import { QuestionAnswer } from "@mui/icons-material";

interface EmptyFAQCardProps {
  onAddFaq: () => void;
}

const EmptyFAQCard: FC<EmptyFAQCardProps> = ({ onAddFaq }) => {
  const theme = useTheme();

  return (
    <Card
      sx={{
        textAlign: "center",
        p: 4,
        backgroundColor: theme.palette.background.default,
        border: `1px dashed ${theme.palette.divider}`,
        borderRadius: 2,
      }}
    >
      <CardContent>
        <Box
          sx={{
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            gap: 2,
          }}
        >
          <Box
            sx={{
              backgroundColor: theme.palette.primary.light,
              borderRadius: "50%",
              p: 2,
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              mb: 2,
            }}
          >
            <QuestionAnswer
              sx={{
                fontSize: 40,
                color: theme.palette.primary.main,
              }}
            />
          </Box>

          <Typography variant="h5" component="h2" gutterBottom>
            No FAQs Added Yet
          </Typography>

          <Typography
            variant="body1"
            color="textSecondary"
            sx={{ maxWidth: 450, mb: 3 }}
          >
            Start building your FAQ section by adding questions and answers that
            will help your users find the information they need quickly.
          </Typography>

          <Button
            variant="contained"
            color="primary"
            onClick={onAddFaq}
            size="large"
            sx={{
              borderRadius: 2,
              textTransform: "none",
              px: 4,
            }}
          >
            Add Your First FAQ
          </Button>
        </Box>
      </CardContent>
    </Card>
  );
};

export default EmptyFAQCard;
