import React, { useState, ReactElement } from "react";
import { Typo<PERSON>, Box, Grid, Drawer, But<PERSON> } from "@mui/material";
import { useCreateChatbot } from "@/hooks/useCreateChatbot";
import { BuildRawForm } from "./BuildRawForm";
// import { GuidedBuildForm } from "./GuideBuildForm";
import AgentBuilderCard from "./AgentBuilderCard";
import {
  QuickAgentTemplate,
  quickAgentTemplates,
} from "@/helpers/agent-templates";
import { GuidedBuildForm } from "./guided-build/GuidedBuildForm";
import { BuildAgentFromSnapshot } from "./BuildAgentFromSnapshot";

interface ChatbotCreationFlowProps {
  trigger: ReactElement;
}

const ChatbotCreationFlow: React.FC<ChatbotCreationFlowProps> = ({
  trigger,
}) => {
  const [step, setStep] = useState<"initial" | "templates" | "form">("initial");
  const [buildOption, setBuildOption] = useState<
    "raw" | "guided" | "snapshot" | null
  >(null);
  const [selectedTemplate, setSelectedTemplate] =
    useState<QuickAgentTemplate | null>(null);
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const { createChatbot, isLoading, error } = useCreateChatbot();

  const handleDrawerOpen = () => setIsDrawerOpen(true);
  const handleDrawerClose = () => setIsDrawerOpen(false);

  const handleBuildOptionChange = (option: "raw" | "guided" | "snapshot") => {
    setBuildOption(option);
  };

  const handleTemplateSelect = (template: QuickAgentTemplate) => {
    setSelectedTemplate(template);
  };

  const handleNext = () => {
    if (step === "initial") {
      if (
        buildOption === "raw" ||
        buildOption === "guided" ||
        buildOption === "snapshot"
      ) {
        setStep("form");
      }
    }
  };

  const handleBack = () => {
    if (step === "templates") {
      setStep("initial");
      setBuildOption(null);
    } else if (step === "form") {
      if (buildOption === "guided") {
        setStep("initial");
        setSelectedTemplate(null);
      } else {
        setStep("initial");
        setBuildOption(null);
      }
    }
  };

  const handleSubmit = async (data: any) => {
    await createChatbot(data);
    // Handle post-creation logic here
  };

  const renderInitialStep = () => (
    <Box>
      <Typography variant="h6" gutterBottom>
        Create a bot
      </Typography>
      <Typography variant="body2" color="text.secondary" paragraph>
        {`Choose how you'd like to create your bot`}
      </Typography>
      <Grid container spacing={2}>
        <Grid item xs={12} sm={6} key="guided">
          <AgentBuilderCard
            title="Take a Guided Build"
            description="Follow a step-by-step guide to build your project with recommended settings."
            selected={buildOption === "guided"}
            onSelect={() => handleBuildOptionChange("guided")}
          />
        </Grid>
        <Grid item xs={12} sm={6} key="raw">
          <AgentBuilderCard
            title="Manual Build"
            description="Build your project from scratch without any guidance. This gives you complete flexibility."
            selected={buildOption === "raw"}
            onSelect={() => handleBuildOptionChange("raw")}
          />
        </Grid>
        <Grid item xs={12} sm={6} key="snapshot">
          <AgentBuilderCard
            title="Snapshot Build"
            description="Build your bot from a snapshot. This helps keeping the agent in sync."
            selected={buildOption === "snapshot"}
            onSelect={() => handleBuildOptionChange("snapshot")}
          />
        </Grid>
      </Grid>
      <Box sx={{ mt: 2, display: "flex", justifyContent: "flex-end" }}>
        <Button
          variant="contained"
          color="primary"
          onClick={handleNext}
          disabled={!buildOption}
        >
          Next
        </Button>
      </Box>
    </Box>
  );

  const renderTemplatesStep = () => (
    <Box>
      <Typography variant="h6" gutterBottom>
        Choose a template
      </Typography>
      <Typography variant="body2" color="text.secondary" paragraph>
        Select a template that best fits your needs
      </Typography>
      <Grid container spacing={2}>
        {quickAgentTemplates.map((template) => (
          <Grid item xs={12} sm={6} key={template.id}>
            <AgentBuilderCard
              title={template.name}
              description={template.description}
              selected={selectedTemplate?.id === template.id}
              onSelect={() => handleTemplateSelect(template)}
            />
          </Grid>
        ))}
      </Grid>
      <Box sx={{ mt: 2, display: "flex", justifyContent: "space-between" }}>
        <Button variant="outlined" onClick={handleBack}>
          Back
        </Button>
        <Button
          variant="contained"
          color="primary"
          onClick={handleNext}
          disabled={!selectedTemplate}
        >
          Next
        </Button>
      </Box>
    </Box>
  );

  const renderForm = () => {
    if (buildOption === "raw") {
      return (
        <BuildRawForm onSubmit={handleSubmit} onFirstPageBack={handleBack} />
      );
    } else if (buildOption === "guided") {
      return (
        <GuidedBuildForm
          onSubmit={handleSubmit}
          initialData={selectedTemplate?.data}
          onFirstPageBack={handleBack}
        />
      );
    } else if (buildOption === "snapshot") {
      return (
        <BuildAgentFromSnapshot
          onSubmit={() => {}}
          onFirstPageBack={handleBack}
        />
      );
    }
    return null;
  };

  return (
    <>
      {React.cloneElement(trigger, { onClick: handleDrawerOpen })}
      <Drawer anchor="right" open={isDrawerOpen} onClose={handleDrawerClose}>
        <Box sx={{ width: 700, p: 3 }}>
          {step === "initial" && renderInitialStep()}
          {step === "templates" && renderTemplatesStep()}
          {step === "form" && renderForm()}
        </Box>
      </Drawer>
    </>
  );
};

export default ChatbotCreationFlow;
