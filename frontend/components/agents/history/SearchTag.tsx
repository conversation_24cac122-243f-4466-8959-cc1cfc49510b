import { Chip } from "@mui/material";
import React from "react";
import CloseIcon from "@mui/icons-material/Close";
import { useRouter } from "next/router";

const SearchTag = () => {
  const router = useRouter();
  const { contactId } = router.query as { contactId: string };

  const handleDelete = () => {
    const { contactId, ...rest } = router.query;
    router.push({
      query: rest,
    });
  };
  if (!!!contactId) return null;
  return (
    <Chip
      label={contactId}
      onDelete={handleDelete}
      deleteIcon={<CloseIcon />}
      variant="outlined"
    />
  );
};

export default SearchTag;
