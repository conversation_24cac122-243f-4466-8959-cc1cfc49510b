import React from "react";
import { <PERSON><PERSON>, Box, Chip, Stack, Typography } from "@mui/material";
import { Accordion, AccordionSummary, AccordionDetails } from "@mui/material";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import { BsRobot } from "react-icons/bs";
import { blue, grey } from "@mui/material/colors";
import { FaUserTie } from "react-icons/fa6";
import { IConversation } from "@/pages/home/<USER>/[agentId]/details/[contactId]";

type IProp = {
  data: IConversation;
};

const SingleConversationBlock: React.FC<IProp> = ({ data }) => {
  const { direction, message, tokens, actions, aiProvider, dateAdded } = data;
  return (
    <Box>
      <Stack
        direction={"row"}
        alignItems={"center"}
        justifyContent={"space-between"}
      >
        <Stack direction={"row"}>
          <Avatar
            sx={{
              background: `linear-gradient(45deg, ${grey[400]}, ${grey[100]})`,
              height: 56,
              width: 56,
            }}
          >
            {direction === "outbound" ? (
              <BsRobot fill={grey[700]} size={32} />
            ) : (
              <FaUserTie fill={blue[700]} size={32} />
            )}
          </Avatar>
          <Stack direction={"column"} marginLeft={2}>
            <Typography variant="h6">
              {" "}
              {direction === "outbound" ? "Bot" : "User"}
            </Typography>
            <Typography variant="body2" color={grey[400]}>
              {new Date(dateAdded).toLocaleString() || "No date found"}
              {direction === "outbound"
                ? " | " + (aiProvider || "No AI provider found")
                : null}
            </Typography>
          </Stack>
        </Stack>
        <Chip label={direction} variant="filled" sx={{ bgcolor: grey[300] }} />
      </Stack>
      <Typography variant="h6" marginTop={2} gutterBottom>
        {message}
      </Typography>
      {direction === "outbound" ? (
        <>
          <Typography variant="body1" color={grey[300]} my={2}>
            Tokens: {tokens}
          </Typography>
          <Typography variant="body1" sx={{ fontWeight: 600 }} gutterBottom>
            Bot Events:
          </Typography>
          <Stack direction={"column"} gap={1}>
            {actions?.length === 0 ? (
              <>
                <Typography variant="body1" color={grey[300]}>
                  No events
                </Typography>
              </>
            ) : null}
            {actions?.map((item, index) => (
              <Accordion key={index}>
                <AccordionSummary
                  expandIcon={<ExpandMoreIcon />}
                  aria-controls="panel1a-content"
                  id="panel1a-header"
                >
                  <Typography variant="body1">Action {index + 1}</Typography>
                </AccordionSummary>
                <AccordionDetails>
                  <Typography>
                    {Object.entries(item).map(([key, value]) => (
                      <React.Fragment key={key}>
                        {key}: {value.toString()} <br />
                      </React.Fragment>
                    ))}
                  </Typography>
                </AccordionDetails>
              </Accordion>
            ))}
          </Stack>
        </>
      ) : null}
    </Box>
  );
};

export default SingleConversationBlock;
