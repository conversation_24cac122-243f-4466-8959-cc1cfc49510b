import React from "react";
import Dialog from "@mui/material/Dialog";
import DialogTitle from "@mui/material/DialogTitle";
import DialogContent from "@mui/material/DialogContent";
import DialogContentText from "@mui/material/DialogContentText";
import DialogActions from "@mui/material/DialogActions";
import Button from "@mui/material/Button";
import DownloadIcon from "@mui/icons-material/Download";
import { Alert, IconButton } from "@mui/material";
import { importGhlConvo } from "@/requests/sessions/basic/importGhlConvo";
import { useDispatch } from "react-redux";
import { useRouter } from "next/router";
import { ImSpinner8 } from "react-icons/im";

interface IProp {
  locationId: string;
  contactId: string;
  agentId: string;
}

const ImportToSessionDialog: React.FC<IProp> = ({
  agentId,
  contactId,
  locationId,
}) => {
  const dispatch = useDispatch();
  const router = useRouter();
  const [open, setOpen] = React.useState(false);
  const [loading, setLoading] = React.useState(false);
  const orgId = localStorage.getItem("orgId") as string;
  const disable = !locationId || !contactId || !agentId || !orgId;

  const handleClickOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  const handleConfirm = async () => {
    // Handle the confirm action here
    setLoading(true);
    const response = await importGhlConvo(
      {
        contactId,
        locationId,
        agentId,
        orgId,
      },
      dispatch
    );
    setLoading(false);
    if (response?.status === 201) {
      const sessionId = response?.data?._id;
      handleClose();
      router.push("/home/<USER>/" + sessionId);
    }
  };

  return (
    <div>
      <IconButton onClick={handleClickOpen}>
        <DownloadIcon />
      </IconButton>
      <Dialog open={open} onClose={handleClose}>
        <DialogTitle>Import Conversation</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Do you want to import this conversation to the emulator sessions?
          </DialogContentText>
          {disable && (
            <Alert severity="error">Couldn&apos;t find all parameters</Alert>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClose}>Cancel</Button>
          <Button onClick={handleConfirm} disabled={disable || loading}>
            Yes {loading && <ImSpinner8 className="animate-spin ml-2" />}
          </Button>
        </DialogActions>
      </Dialog>
    </div>
  );
};

export default ImportToSessionDialog;
