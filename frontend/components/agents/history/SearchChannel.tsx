import * as React from "react";
import Button from "@mui/material/Button";
import TextField from "@mui/material/TextField";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogTitle from "@mui/material/DialogTitle";
import SearchIcon from "@mui/icons-material/Search";
import { IconButton } from "@mui/material";
import { useRouter } from "next/router";

export default function SearchChannel() {
  const [open, setOpen] = React.useState(false);
  const [contactId, setContactId] = React.useState("");
  const router = useRouter();

  const handleClickOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  const handleSearch = () => {
    router.push({
      query: { ...router.query, contactId },
    });
    handleClose();
  };

  return (
    <React.Fragment>
      <IconButton
        color="primary"
        aria-label="add to shopping cart"
        onClick={handleClickOpen}
      >
        <SearchIcon />
      </IconButton>
      <Dialog open={open} onClose={handleClose}>
        <DialogTitle>Search by Contact ID</DialogTitle>
        <DialogContent sx={{ width: "450px" }}>
          <TextField
            autoFocus
            required
            margin="dense"
            id="contactId"
            name="Contact ID"
            label="Contact ID"
            type="text"
            fullWidth
            variant="standard"
            value={contactId}
            onChange={(e) => setContactId(e.target.value)}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClose}>Cancel</Button>
          <Button onClick={handleSearch}>Search</Button>
        </DialogActions>
      </Dialog>
    </React.Fragment>
  );
}
