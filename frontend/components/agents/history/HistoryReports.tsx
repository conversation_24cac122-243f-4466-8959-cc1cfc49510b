import React, { useState, useEffect } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  TablePagination,
  Button,
  Card,
  CardContent,
  Typography,
  CircularProgress,
  Box,
  Stack,
  Tooltip,
  IconButton,
} from "@mui/material";
import { useRouter } from "next/router";
import { getHistory } from "@/requests/agents/history/getHistory";
import { useDispatch } from "react-redux";
import Link from "next/link";
import Image from "next/image";
import { grey } from "@mui/material/colors";
import SearchChannel from "./SearchChannel";
import SearchTag from "./SearchTag";
import FileCopyIcon from "@mui/icons-material/FileCopy";
import { snackbarActions } from "@/slices/general/snackbar";
import InfoIcon from "@mui/icons-material/Info";
import ImportToSessionDialog from "./ImportToSessionDialog";

interface ILogEntry {
  contactId: string;
  channel: string;
  lastMessage: string;
  lastMessageTime: string;
  totalTokens: number;
  numberOfMessages: number;
  resourceId: string;
  conversationId: string;
}

const HistoryReports: React.FC = () => {
  const dispatch = useDispatch();
  const router = useRouter();
  const { agentId, contactId } = router.query as {
    agentId: string;
    contactId: string;
  };
  const [logEntries, setLogEntries] = useState<ILogEntry[]>([]);
  const [page, setPage] = useState(1); // page indexing starts from 1
  const [rowsPerPage, setRowsPerPage] = useState(5); // initial rows per page
  const [totalPages, setTotalPages] = useState(0);
  const [loading, setLoading] = useState(true); // add a loading state

  useEffect(() => {
    const fetchLogEntries = async () => {
      setLoading(true); // set loading to true before fetching data
      const response = await getHistory({
        body: { agentId, page, limit: rowsPerPage, contactId },
        dispatch,
      });
      if (response?.status === 200) {
        setLogEntries(response.data.history);
        setTotalPages(response.data.totalPage); // set total pages
      }
      setLoading(false); // set loading to false after fetching data
    };

    fetchLogEntries();
  }, [page, rowsPerPage, contactId]); // add page and rowsPerPage to the dependency array

  useEffect(() => {
    setPage(1); // reset page to 1 when contactId changes
  }, [contactId]);

  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage + 1); // increment newPage by 1 because page indexing starts from 1
  };

  const handleChangeRowsPerPage = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(1); // reset page to 1 when rows per page changes
  };

  const handleCopyContactId = (x: string) => {
    navigator.clipboard.writeText(x).then(() => {
      dispatch(
        snackbarActions.setSnackbar({
          message: "Contact ID copied",
          type: "success",
        })
      );
    });
  };

  return (
    <>
      {loading ? (
        <Box
          sx={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            mt: 10,
          }}
        >
          <CircularProgress />
        </Box>
      ) : (
        <>
          {logEntries.length === 0 && !!!contactId ? (
            <Stack alignItems={"center"}>
              <Typography variant="h5" color={grey[300]} mt={10}>
                No logs found.
              </Typography>
            </Stack>
          ) : (
            <TableContainer component={Paper} sx={{ marginTop: 2 }}>
              <Table sx={{ minWidth: 650 }} aria-label="simple table">
                <TableHead>
                  <TableRow>
                    <TableCell align="center">Channel</TableCell>
                    <TableCell align="center">
                      <div className="flex flex-col items-center justify-center">
                        <div className="flex items-center">
                          Contact ID <SearchChannel />
                        </div>
                        <SearchTag />
                      </div>
                    </TableCell>
                    <TableCell sx={{ width: "25%" }} align="center">
                      Last Message
                    </TableCell>
                    <TableCell sx={{ width: "15%" }} align="center">
                      Last Message Time
                    </TableCell>
                    <TableCell sx={{ width: "10%" }} align="center">
                      Total Tokens
                    </TableCell>
                    <TableCell sx={{ width: "15%" }} align="center">
                      Number of Messages
                    </TableCell>
                    <TableCell align="center"></TableCell>
                    <TableCell align="center"></TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {logEntries.map((row, index) => (
                    <TableRow key={index}>
                      <TableCell align="center" component="th" scope="row">
                        {row.channel}
                      </TableCell>
                      <TableCell align="center" component="th" scope="row">
                        <div className="flex items-center">
                          <Tooltip title={row.contactId}>
                            <div className="truncate max-w-[150px]">
                              {row.contactId}
                            </div>
                          </Tooltip>
                          <Tooltip title="Copy to clipboard">
                            <IconButton
                              onClick={() => handleCopyContactId(row.contactId)}
                            >
                              <FileCopyIcon />
                            </IconButton>
                          </Tooltip>
                        </div>
                      </TableCell>
                      <TableCell align="center">
                        <div className="line-clamp-5">{row.lastMessage}</div>
                      </TableCell>
                      <TableCell align="center">
                        {new Date(row.lastMessageTime).toLocaleString()}
                      </TableCell>
                      <TableCell align="center">{row.totalTokens}</TableCell>
                      <TableCell align="center">
                        {row.numberOfMessages}
                      </TableCell>
                      <TableCell align="center">
                        <ImportToSessionDialog
                          locationId={row.resourceId}
                          contactId={row.contactId}
                          agentId={agentId}
                        />
                      </TableCell>
                      <TableCell align="center">
                        <Link
                          href={`/home/<USER>/${agentId}/details/${row?.contactId}`}
                          passHref
                        >
                          <IconButton>
                            <InfoIcon color="info" />
                          </IconButton>
                        </Link>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
              <TablePagination
                rowsPerPageOptions={[5, 10, 25]}
                component="div"
                count={totalPages * rowsPerPage} // calculate total count based on total pages and rows per page
                rowsPerPage={rowsPerPage}
                page={page - 1} // decrement page by 1 because TablePagination's page prop is zero-based
                onPageChange={handleChangePage}
                onRowsPerPageChange={handleChangeRowsPerPage}
              />
            </TableContainer>
          )}
        </>
      )}
    </>
  );
};

export default HistoryReports;
