import React from "react";
import {
  Tooltip,
  CircularProgress,
  Chip,
  Paper,
  Box,
  Typography,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Alert,
  useTheme,
} from "@mui/material";
import {
  CloudUpload as UploadIcon,
  Language as WebIcon,
  Error as ErrorIcon,
  CheckCircle as SuccessIcon,
  Warning as WarningIcon,
} from "@mui/icons-material";
import { alpha, styled } from "@mui/material/styles";
import { grey } from "@mui/material/colors";

interface TrainingStatus {
  type: "file-upload" | "website-crawl" | "website-scrap";
  startedAt: Date;
  status: "in_progress" | "completed" | "failed";
  details?: string;
  error?: string;
}

interface Agent {
  _id: string;
  name: string;
  isTraining: boolean;
  activeTraining: TrainingStatus[];
}

interface Props {
  agent: Agent;
  onRetry?: (trainingType: TrainingStatus["type"]) => void;
}
const StyledPaper = styled(Paper)(({ theme }) => ({
  display: "inline-flex",
  alignItems: "center",
  padding: theme.spacing(0.5, 1.5),
  borderRadius: theme.shape.borderRadius * 3,
  //   backgroundColor: theme.palette.background.paper,
  backgroundColor: "whitesmoke",
  boxShadow: "none",
  border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
}));

const StatusChip = styled(Chip)(({ theme, color = "default" }) => ({
  height: 24,
  "& .MuiChip-label": {
    paddingLeft: theme.spacing(0.75),
    paddingRight: theme.spacing(0.75),
    fontSize: "0.75rem",
    fontWeight: 500,
  },
  "& .MuiChip-icon": {
    fontSize: 16,
    marginLeft: theme.spacing(0.5),
  },
  ...(color === "error" && {
    backgroundColor: alpha(theme.palette.error.main, 0.1),
    color: theme.palette.error.main,
    "& .MuiChip-icon": {
      color: theme.palette.error.main,
    },
  }),
  ...(color === "success" && {
    backgroundColor: alpha(theme.palette.success.main, 0.1),
    color: theme.palette.success.main,
    "& .MuiChip-icon": {
      color: theme.palette.success.main,
    },
  }),
  ...(color === "warning" && {
    backgroundColor: alpha(theme.palette.warning.main, 0.1),
    color: theme.palette.warning.main,
    "& .MuiChip-icon": {
      color: theme.palette.warning.main,
    },
  }),
  ...(color === "info" && {
    backgroundColor: alpha(theme.palette.info.main, 0.1),
    color: theme.palette.info.main,
    "& .MuiChip-icon": {
      color: theme.palette.info.main,
    },
  }),
}));

const TooltipContent: React.FC<{ trainings: TrainingStatus[] }> = ({
  trainings,
}) => {
  const theme = useTheme();

  return (
    <Box sx={{ maxWidth: 300 }}>
      <List dense sx={{maxHeight: 350, overflowY: "scroll"}} disablePadding>
        {trainings.map((training, index) => {
          const isError = training.status === "failed";
          const isCompleted = training.status === "completed";
          const icon = isError
            ? ErrorIcon
            : isCompleted
            ? SuccessIcon
            : training.type === "file-upload"
            ? UploadIcon
            : WebIcon;

          return (
            <ListItem key={index} sx={{ py: 0.5 }}>
              <ListItemIcon sx={{ minWidth: 36 }}>
                {React.createElement(icon, {
                  color: isError
                    ? "error"
                    : isCompleted
                    ? "success"
                    : "primary",
                  sx: { fontSize: 20 },
                })}
              </ListItemIcon>
              <ListItemText
                primary={
                  <Typography variant="body2" sx={{ fontWeight: 500 }}>
                    {training.type === "file-upload"
                      ? "Processing Files"
                      : "Scraping Website"}
                  </Typography>
                }
                secondary={
                  <>
                    {training.details && (
                      <Typography
                        variant="caption"
                        display="block"
                        sx={{ mt: 0.5 }}
                      >
                        {training.details}
                      </Typography>
                    )}
                    {isError && training.error && (
                      <Alert severity="error">{training.error}</Alert>
                    )}
                  </>
                }
              />
            </ListItem>
          );
        })}
      </List>
    </Box>
  );
};

export const TrainingStatusIndicator: React.FC<Props> = ({
  agent,
  onRetry,
}) => {
  const theme = useTheme();
  const hasError = agent.activeTraining?.some((t) => t.status === "failed");
  const inProgress = agent.activeTraining?.some(
    (t) => t.status === "in_progress"
  );

  if (!agent.isTraining && !hasError) return null;

  const activeTrainings = agent.activeTraining?.filter(
    (t) => t.status === "in_progress" || t.status === "failed"
  );

  const getStatusColor = () => {
    if (hasError) return "error";
    if (inProgress) return "primary";
    return "success";
  };

  const getStatusLabel = () => {
    if (hasError) return "Training Failed";
    if (inProgress) return "Training in Progress";
    return "Training Complete";
  };

  return (
    <Tooltip
      title={<TooltipContent trainings={activeTrainings} />}
      arrow
      placement="top"
      PopperProps={{
        sx: {
          "& .MuiTooltip-tooltip": {
            backgroundColor: theme.palette.background.paper,
            color: theme.palette.text.primary,
            boxShadow: theme.shadows[4],
            p: 1,
          },
        },
      }}
    >
      <StyledPaper>
        {inProgress && (
          <CircularProgress size={20} thickness={5} sx={{ mr: 0.5 }} />
        )}
        {/* {hasError && (
          <WarningIcon color="error" sx={{ fontSize: 20, mr: 0.5 }} />
        )} */}
        <StatusChip
          label={getStatusLabel()}
          color={getStatusColor()}
          // @ts-ignore
          icon={hasError ? <WarningIcon color="error" /> : null}
          variant="filled"
          size="small"
        />
      </StyledPaper>
    </Tooltip>
  );
};
