import React, { useState, useEffect } from "react";
import {
  <PERSON>ton,
  <PERSON>alog,
  DialogActions,
  DialogContent,
  DialogTitle,
  IconButton,
  CircularProgress,
} from "@mui/material";
import { Edit } from "@mui/icons-material";
import { IVoiceConfig, agentsAction } from "@/slices/agents/agentsSlice";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store";
import { updateVoiceConfiguration } from "@/requests/voice/updateVoiceConfiguration";
import VoiceConfigForm, {
  VoiceConfigFormData,
  defaultConfig,
} from "./VoiceConfigFormData";

interface IProp {
  agentId: string;
}

const VoiceConfigEdit: React.FC<IProp> = ({ agentId }) => {
  const dispatch = useDispatch();
  const voiceConfig = useSelector(
    (state: RootState) => state.agents.currentAgent.voiceConfig
  ) as IVoiceConfig;
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [config, setConfig] = useState<VoiceConfigFormData>({
    channel: voiceConfig.channel,
    firstMessage: voiceConfig.firstMessage,
    firstMessageMode: voiceConfig.firstMessageMode,
    // assistantId: voiceConfig.assistantId,
    backchannelingEnabled: voiceConfig.backchannelingEnabled,
    backgroundDenoisingEnabled: voiceConfig.backgroundDenoisingEnabled,
    backgroundSound: voiceConfig.backgroundSound,
    // enabled: voiceConfig.enabled,
    model: voiceConfig.model,
    provider: voiceConfig.provider,
    silenceTimeoutSeconds: voiceConfig.silenceTimeoutSeconds,
    voice: voiceConfig.voice,
    // tools: voiceConfig.tools,
    phoneDetails: voiceConfig.phoneDetails,
  });

  useEffect(() => {
    setConfig({
      channel: voiceConfig.channel,
      firstMessage: voiceConfig.firstMessage,
      firstMessageMode: voiceConfig.firstMessageMode,
      // assistantId: voiceConfig.assistantId,
      backchannelingEnabled: voiceConfig.backchannelingEnabled,
      backgroundDenoisingEnabled: voiceConfig.backgroundDenoisingEnabled,
      backgroundSound: voiceConfig.backgroundSound,
      // enabled: voiceConfig.enabled,
      model: voiceConfig.model,
      provider: voiceConfig.provider,
      silenceTimeoutSeconds: voiceConfig.silenceTimeoutSeconds,
      voice: voiceConfig.voice,
      // tools: voiceConfig.tools,
      phoneDetails: voiceConfig.phoneDetails,
    });
  }, [voiceConfig]);

  const handleOpenDialog = () => setOpen(true);
  const handleCloseDialog = () => setOpen(false);

  const handleConfigChange = (newConfig: VoiceConfigFormData) => {
    setConfig(newConfig);
  };

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    setLoading(true);
    try {
      // const body: Partial<IVoiceConfig> = {
      //   firstMessage: config.firstMessage,
      //   firstMessageMode: config.firstMessageMode,
      //   tools: config.tools,
      // };
      console.log({ ...defaultConfig, agentId, ...config });

      const response = await updateVoiceConfiguration(
        { ...defaultConfig, agentId, ...config },
        dispatch
      );
      if (response.status === 200) {
        dispatch(agentsAction.updateVoiceConfig({ ...config }));
      }
      handleCloseDialog();
    } catch (error) {
      console.error("Error updating voice configuration:", error);
      // Handle error (e.g., show an error message to the user)
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <IconButton
        aria-label="edit"
        color="primary"
        onClick={handleOpenDialog}
        size="small"
      >
        <Edit fontSize="small" />
      </IconButton>
      <Dialog open={open} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
        <form onSubmit={handleSubmit}>
          <DialogTitle>Edit Voice Configuration</DialogTitle>
          <DialogContent>
            <VoiceConfigForm config={config} onChange={handleConfigChange} />
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseDialog} disabled={loading}>
              Cancel
            </Button>
            <Button
              type="submit"
              variant="contained"
              disabled={
                loading ||
                (config.firstMessageMode === "assistant-speaks-first" &&
                  !config.firstMessage)
              }
            >
              {loading ? <CircularProgress size={24} /> : "Save Changes"}
            </Button>
          </DialogActions>
        </form>
      </Dialog>
    </>
  );
};

export default VoiceConfigEdit;
