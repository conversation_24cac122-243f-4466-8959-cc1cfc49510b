import React, { useState } from "react";
import { Paper, Typography, Box, Button } from "@mui/material";
import InfoIcon from "@mui/icons-material/Info";
import AddConnectionDialog from "./AddConnectionDialog";
import { IVoiceConfig, agentsAction } from "@/slices/agents/agentsSlice";
import { useDispatch } from "react-redux";
import { setChannel } from "@/requests/voice/setChannel";
import { useRouter } from "next/router";

const NoChannelConfigured: React.FC = () => {
  const dispatch = useDispatch();
  const router = useRouter();
  const { agentId } = router.query as { agentId: string };
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const handleOpenDialog = () => setIsDialogOpen(true);
  const handleCloseDialog = () => setIsDialogOpen(false);

  const orgId = localStorage.getItem("orgId") as string;

  const handleSave = async (account: IVoiceConfig["channel"]) => {
    const response = await setChannel(
      { orgId, accountId: account?.accountId as string, agentId },
      dispatch
    );
    if (response.status === 200) {
      dispatch(agentsAction.updateConnectedVoiceChannel(account));
    }
  };

  return (
    <Paper
      elevation={3}
      sx={{
        p: 3,
        mt: 2,
        bgcolor: "info.light",
        color: "info.contrastText",
        display: "flex",
        alignItems: "center",
        borderRadius: 2,
      }}
    >
      <InfoIcon sx={{ fontSize: 40, mr: 2 }} />
      <Box sx={{ flexGrow: 1 }}>
        <Typography variant="h6" gutterBottom>
          No connection
        </Typography>
        <Typography variant="body1" sx={{ mb: 2 }}>
          Your voice agent is currently not connected to any channel.
        </Typography>
        <Button onClick={handleOpenDialog} variant="contained" color="primary">
          Add Connection
        </Button>
      </Box>
      <AddConnectionDialog
        open={isDialogOpen}
        onClose={handleCloseDialog}
        onSubmit={handleSave}
      />
    </Paper>
  );
};

export default NoChannelConfigured;
