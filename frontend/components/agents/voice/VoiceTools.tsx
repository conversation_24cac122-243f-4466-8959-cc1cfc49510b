import React, { useState } from "react";
import { Typo<PERSON>, Button } from "@mui/material";
import { Add } from "@mui/icons-material";
import { ToolsStructure } from "@/slices/agents/agentsSlice";
import NoToolPresentBanner from "./NoToolPresentBanner";
import ToolDialog from "./ToolDialog";
import ToolList from "./ToolList";

interface VoiceToolsProps {
  tools: ToolsStructure[];
}

const VoiceTools: React.FC<VoiceToolsProps> = ({ tools }) => {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [selectedTool, setSelectedTool] = useState<ToolsStructure | null>(null);

  const handleOpenDialog = (tool: ToolsStructure | null) => {
    setSelectedTool(tool);
    setIsDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setIsDialogOpen(false);
    setSelectedTool(null);
  };

  return (
    <div className="p-4 bg-gray-100 rounded-lg shadow">
      <h2 className="text-2xl font-bold mb-4">Tools</h2>
      {!tools || tools.length === 0 ? (
        <NoToolPresentBanner />
      ) : (
        <ToolList tools={tools} onToolAction={handleOpenDialog} />
      )}
      {/* <Button
        startIcon={<Add />}
        variant="contained"
        color="primary"
        onClick={() => handleOpenDialog(null)}
        className="mt-4"
      >
        Add Tool
      </Button> */}
      <ToolDialog
        isOpen={isDialogOpen}
        onClose={handleCloseDialog}
        tool={selectedTool}
      />
    </div>
  );
};

export default VoiceTools;
