import React, { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON>po<PERSON>,
  IconButton,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>b,
  CircularProgress,
  Paper,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import { getCallHistory } from "@/requests/agents/voice/getCallHistory";
import { useDispatch } from "react-redux";

// Tab components

interface Message {
  role: "User" | "AI" | "Assistant";
  content: string;
}
const TranscriptsTab: React.FC<{ transcript: string }> = ({ transcript }) => {
  const parseTranscript = (text: string): Message[] => {
    const lines = text.split("\n");
    const messages: Message[] = [];
    let currentMessage: Message | null = null;

    lines.forEach((line) => {
      if (
        line.startsWith("User:") ||
        line.startsWith("AI:") ||
        line.startsWith("Assistant:")
      ) {
        if (currentMessage) messages.push(currentMessage);
        const [role, ...contentParts] = line.split(":");
        currentMessage = {
          role: role as "User" | "AI" | "Assistant",
          content: contentParts.join(":").trim(),
        };
      } else if (currentMessage) {
        currentMessage.content += " " + line.trim();
      }
    });

    if (currentMessage) messages.push(currentMessage);
    return messages;
  };

  const messages = parseTranscript(transcript);

  return (
    <Paper
      sx={{
        color: "white",
        px: 2,
        height: "100%",
        overflowY: "auto",
      }}
    >
      {messages.map((message, index) => (
        <Box key={index} sx={{ mb: 2 }}>
          <Typography
            sx={{
              color: message.role === "User" ? "primary.main" : "success.main",
              fontWeight: "bold",
            }}
          >
            {message.role === "AI" ? "Assistant" : message.role}
          </Typography>
          <Typography sx={{ color: "text.secondary" }}>
            {message.content}
          </Typography>
        </Box>
      ))}
    </Paper>
  );
};

const AnalysisTab: React.FC<{
  analysis: { summary: string; successEvaluation: string };
}> = ({ analysis }) => (
  <Box>
    <Typography variant="h6">Summary</Typography>
    <Typography>{analysis.summary}</Typography>
    <Typography variant="h6" sx={{ mt: 2 }}>
      Success Evaluation
    </Typography>
    <Typography>{analysis.successEvaluation}</Typography>
  </Box>
);

const MessagesTab: React.FC<{ messages: unknown[] }> = ({ messages }) => {
  return (
    <Box className="p-4">
      {messages.map((message, index) => (
        <Box key={index} className="mb-4 p-4 bg-gray-800 rounded-lg">
          <Typography className="font-mono text-sm text-white whitespace-pre-wrap overflow-x-auto">
            {JSON.stringify(message, null, 2)}
          </Typography>
        </Box>
      ))}
    </Box>
  );
};
interface CallDetailsDrawerProps {
  open: boolean;
  onClose: () => void;
  callId: string;
}

export interface CallDetails {
  transcript: string;
  analysis: {
    summary: string;
    successEvaluation: string;
  };
  messages: unknown[];
}

const CallDetailsDrawer: React.FC<CallDetailsDrawerProps> = ({
  open,
  onClose,
  callId,
}) => {
  const dispatch = useDispatch();
  const [loading, setLoading] = useState(true);
  const [callDetails, setCallDetails] = useState<CallDetails | null>(null);
  const [activeTab, setActiveTab] = useState(0);

  useEffect(() => {
    const fetchCallHistory = async () => {
      setLoading(true);
      try {
        const response = await getCallHistory({ callId }, dispatch);
        if (response.status === 200) {
          const resData = response.data;
          const data = {
            transcript: resData.artifact.transcript,
            analysis: resData.analysis,
            messages: resData.messages,
          };
          setCallDetails(data);
        }
      } catch (error) {
        console.error("Error fetching call history:", error);
      } finally {
        setLoading(false);
      }
    };
    if (open) {
      fetchCallHistory();
    }
  }, [callId, open, dispatch]);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  const renderTabContent = () => {
    if (!callDetails) return null;

    switch (activeTab) {
      case 0:
        return <TranscriptsTab transcript={callDetails.transcript} />;
      case 1:
        return <AnalysisTab analysis={callDetails.analysis} />;
      case 2:
        return <MessagesTab messages={callDetails.messages} />;
      default:
        return null;
    }
  };

  return (
    <Drawer
      anchor="right"
      open={open}
      onClose={onClose}
      PaperProps={{
        sx: { width: "600px" },
      }}
    >
      <Box
        sx={{ p: 2, height: "100%", display: "flex", flexDirection: "column" }}
      >
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            mb: 2,
          }}
        >
          <Typography variant="h6">Call Details</Typography>
          <IconButton onClick={onClose}>
            <CloseIcon />
          </IconButton>
        </Box>
        <Divider />
        <Box sx={{ mt: 2, mb: 2 }}>
          <Typography>
            <strong>Call ID:</strong> {callId}
          </Typography>
        </Box>
        {loading ? (
          <Box
            sx={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              flexGrow: 1,
            }}
          >
            <CircularProgress />
          </Box>
        ) : (
          <>
            <Tabs value={activeTab} onChange={handleTabChange} sx={{ mb: 2 }}>
              <Tab label="Transcripts" />
              <Tab label="Analysis" />
              <Tab label="Messages" />
            </Tabs>
            <Box sx={{ flexGrow: 1, overflow: "auto" }}>
              {renderTabContent()}
            </Box>
          </>
        )}
      </Box>
    </Drawer>
  );
};

export default CallDetailsDrawer;
