import React, { useEffect, useState } from "react";
import VoiceHistory from "./VoiceHistoryTable";
import { getAllCallHistory } from "@/requests/agents/voice/getAllCallHistory";
import { useDispatch } from "react-redux";

interface IProp {
  agentId: string;
}
const VoiceHistoryView: React.FC<IProp> = ({ agentId }) => {
  const dispatch = useDispatch();
  const [callHistory, setCallHistory] = useState([]);
  const [limit, setLimit] = useState(5);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  useEffect(() => {
    const fetchHistory = async () => {
      const response = await getAllCallHistory({ agentId, limit }, dispatch);
      if (response.status === 200) {
        const data = response.data.history?.map((item: any) => ({
          type: item.type,
          callId: item.callId,
          totalCost: item.totalCost,
          createdAt: item.createdAt,
          success: item.analysis.successEvaluation === "true",
          summary: item.analysis.summary,
        }));
        setCallHistory(data);
      }
    };
    fetchHistory();
  }, []);
  return (
    <div className="">
      <h2 className="text-2xl font-bold mt-6 mb-4 text-gray-100">
        Call History
      </h2>
      <VoiceHistory
        callHistory={callHistory}
        onPageChange={(page) => setCurrentPage(page)}
        currentPage={currentPage}
        totalPages={totalPages}
      />
    </div>
  );
};

export default VoiceHistoryView;
