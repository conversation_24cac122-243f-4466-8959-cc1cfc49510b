import React, { useState } from "react";
import {
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  IconButton,
  Paper,
  CircularProgress,
  Chip,
  Stack,
  Typography,
} from "@mui/material";
import { Delete, Restore } from "@mui/icons-material";
import { ToolsStructure, agentsAction } from "@/slices/agents/agentsSlice";
import { logos } from "@/helpers/images";
import { red } from "@mui/material/colors";
import { useDispatch } from "react-redux";
import { useRouter } from "next/router";
import { updateToolStatus } from "@/requests/voice/updateToolStatus";

interface ToolListProps {
  tools: ToolsStructure[];
  onToolAction: (tool: ToolsStructure) => void;
}

const ToolList: React.FC<ToolListProps> = ({ tools, onToolAction }) => {
  const dispatch = useDispatch();
  const router = useRouter();
  const { agentId } = router.query as { agentId: string };
  const [loadingToolId, setLoadingToolId] = useState<string | null>(null);

  const handleToolAction = async (tool: ToolsStructure) => {
    setLoadingToolId(tool.toolId);
    try {
      const newStatus = !tool.active;
      const response = await updateToolStatus(
        { active: newStatus, agentId, toolId: tool.toolId },
        dispatch
      );
      if (response.status === 200) {
        dispatch(
          agentsAction.toggleToolStatus({
            active: newStatus,
            toolId: tool.toolId,
          })
        );
      } else {
        throw new Error("Failed to update tool status");
      }
    } catch (error) {
      console.error("Error updating tool status:", error);
      // Here you might want to show an error message to the user
    } finally {
      setLoadingToolId(null);
    }
  };

  return (
    <List>
      {tools.map((tool) => (
        <Paper
          key={tool.toolId}
          elevation={3}
          sx={{
            mb: 2,
            backgroundColor: tool.active ? "white" : red[100],
            transition: "background-color 0.3s ease",
          }}
        >
          <ListItem
            secondaryAction={
              loadingToolId === tool.toolId ? (
                <CircularProgress size={24} />
              ) : (
                <IconButton
                  edge="end"
                  aria-label={tool.active ? "delete" : "restore"}
                  onClick={() => handleToolAction(tool)}
                >
                  {tool.active ? <Delete /> : <Restore />}
                </IconButton>
              )
            }
          >
            <ListItemAvatar>
              <Avatar
                src={"/squareLogos" + logos[tool.providerName]?.logo || ""}
                alt={tool.providerName}
              />
            </ListItemAvatar>
            <ListItemText
              primary={tool.accountName}
              secondary={
                <React.Fragment>
                  <Stack direction="row" gap={1} alignItems="center">
                    <Typography
                      component="span"
                      variant="body2"
                      color="text.secondary"
                    >
                      {logos[tool.providerName].name ?? "No name"}
                    </Typography>
                    <Chip
                      label={tool.activity}
                      size="small"
                      color={tool.active ? "primary" : "default"}
                    />
                  </Stack>
                </React.Fragment>
              }
            />
          </ListItem>
        </Paper>
      ))}
    </List>
  );
};

export default ToolList;
