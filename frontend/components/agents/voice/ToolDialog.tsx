import React from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
} from "@mui/material";
import { ToolsStructure } from "@/slices/agents/agentsSlice";

interface ToolDialogProps {
  isOpen: boolean;
  onClose: () => void;
  tool: ToolsStructure | null;
}

const ToolDialog: React.FC<ToolDialogProps> = ({ isOpen, onClose, tool }) => {
  const handleConfirm = () => {
    // Handle the confirmation action here
    // This could involve adding a new tool or updating an existing one
    onClose();
  };

  return (
    <Dialog open={isOpen} onClose={onClose}>
      <DialogTitle>
        {tool ? (tool.active ? "Remove Tool" : "Restore Tool") : "Add Tool"}
      </DialogTitle>
      <DialogContent>
        {tool
          ? `Are you sure you want to ${
              tool.active ? "remove" : "restore"
            } the tool "${tool.accountName}"?`
          : "Add a new tool here. You might want to add form fields for the new tool's details."}
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
        <Button onClick={handleConfirm} autoFocus>
          Confirm
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ToolDialog;
