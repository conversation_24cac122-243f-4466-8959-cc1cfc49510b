import React from "react";
import { CircularProgress, IconButton, styled } from "@mui/material";
import { Stop, Mic } from "@mui/icons-material";

// Assuming these are defined elsewhere in your project
enum CALL_STATUS {
  ACTIVE = "ACTIVE",
  LOADING = "LOADING",
  INACTIVE = "INACTIVE",
}

interface UseVapiReturn {
  toggleCall: () => void;
  callStatus: CALL_STATUS;
  audioLevel: number;
}

type ButtonColor = "error" | "warning" | "success";

interface StyledIconButtonProps {
  audioLevel: number;
  customColor: ButtonColor;
  scale: number;
}

const StyledIconButton = styled(IconButton, {
  shouldForwardProp: (prop) =>
    prop !== "audioLevel" && prop !== "customColor" && prop !== "scale",
})<StyledIconButtonProps>(({ theme, audioLevel, customColor, scale }) => ({
  borderRadius: "50%",
  width: 50 * scale,
  height: 50 * scale,
  color: "white",
  border: "none",
  boxShadow: `${1 * scale}px ${1 * scale}px ${
    (10 + audioLevel * 40) * scale
  }px ${audioLevel * 10 * scale}px ${theme.palette[customColor].main}`,
  backgroundColor: theme.palette[customColor].main,
  "&:hover": {
    backgroundColor: theme.palette[customColor].dark,
  },
}));

interface AssistantButtonProps extends UseVapiReturn {
  scale?: number;
  isDisabled: boolean;
}

const AssistantButton: React.FC<AssistantButtonProps> = ({
  toggleCall,
  callStatus = CALL_STATUS.INACTIVE,
  audioLevel = 0,
  scale = 1.5,
  isDisabled,
}) => {
  const getButtonColor = (): ButtonColor => {
    switch (callStatus) {
      case CALL_STATUS.ACTIVE:
        return "error";
      case CALL_STATUS.LOADING:
        return "warning";
      default:
        return "success";
    }
  };

  const color = getButtonColor();

  return (
    <StyledIconButton
      onClick={toggleCall}
      audioLevel={audioLevel}
      customColor={color}
      scale={scale}
      disabled={isDisabled}
    >
      {callStatus === CALL_STATUS.ACTIVE ? (
        <Stop sx={{ fontSize: 24 * scale }} />
      ) : callStatus === CALL_STATUS.LOADING ? (
        <CircularProgress color="inherit" size={24 * scale} />
      ) : (
        <Mic sx={{ fontSize: 24 * scale }} />
      )}
    </StyledIconButton>
  );
};

export { AssistantButton };
