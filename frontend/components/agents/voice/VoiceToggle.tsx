import React from "react";
import { Switch, FormControlLabel, Typography, Box } from "@mui/material";
import { styled } from "@mui/material/styles";

interface VoiceToggleProps {
  isActive: boolean;
  onChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  disabled?: boolean;
  ariaLabel?: string;
}

const StyledFormControlLabel = styled(FormControlLabel)(({ theme }) => ({
  marginRight: theme.spacing(1),
  "& .MuiSwitch-root": {
    marginRight: theme.spacing(1),
  },
}));

const VoiceToggle: React.FC<VoiceToggleProps> = ({
  isActive,
  onChange,
  disabled = false,
  ariaLabel = "Toggle voice",
}) => {
  return (
    <Box
      display="flex"
      alignItems="center"
      component="div"
      role="group"
      aria-label="Voice control"
    >
      <StyledFormControlLabel
        control={
          <Switch
            checked={isActive}
            onChange={onChange}
            disabled={disabled}
            color="primary"
            size="small"
            inputProps={{
              "aria-label": ariaLabel,
            }}
          />
        }
        label=""
      />
      <Typography variant="body1" component="span">
        {isActive ? "Active" : "Inactive"}
      </Typography>
    </Box>
  );
};

export default React.memo(VoiceToggle);
