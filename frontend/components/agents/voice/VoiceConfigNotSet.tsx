import { Settings } from "@mui/icons-material";
import { Button, Paper, Stack, Typography } from "@mui/material";
import React from "react";
import VoiceConfigAdd from "./VoiceConfigAdd";
import { useRouter } from "next/router";

const VoiceConfigNotSet = () => {
  const router = useRouter();
  const { agentId } = router.query as { agentId: string };
  return (
    <Paper sx={{ py: 10, mt: 2 }}>
      <Stack
        direction={"column"}
        justifyContent={"center"}
        alignItems={"center"}
        gap={2}
      >
        <Typography variant="h6">
          {"Configure your voice settings to get started."}
        </Typography>
        <VoiceConfigAdd agentId={agentId} />
      </Stack>
    </Paper>
  );
};

export default VoiceConfigNotSet;
