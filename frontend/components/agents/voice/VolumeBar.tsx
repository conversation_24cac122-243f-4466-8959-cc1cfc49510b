import React from "react";
import { Box, styled } from "@mui/material";

const BarContainer = styled(Box)(({ theme }) => ({
  width: "100%",
  height: 10,
  backgroundColor: theme.palette.grey[500],
  borderRadius: 10,
  overflow: "hidden",
  display: "flex",
}));

const Bar = styled(Box)<{ $level: number }>(({ theme, $level }) => ({
  height: "100%",
  width: `${$level * 100}%`,
  background: `linear-gradient(90deg, 
    ${theme.palette.success.main} 0%, 
    ${theme.palette.warning.main} 50%, 
    ${theme.palette.error.main} 100%)`,
  transition: "width 0.3s ease-in-out",
}));

interface VolumeBarProps {
  level: number;
}

const VolumeBar: React.FC<VolumeBarProps> = ({ level }) => {
  // Ensure the level is between 0 and 1
  const clampedLevel = Math.max(0, Math.min(1, level));

  return (
    <BarContainer>
      <Bar $level={clampedLevel} />
    </BarContainer>
  );
};

export default VolumeBar;
