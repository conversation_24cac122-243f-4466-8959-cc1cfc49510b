import React, { useState, useCallback } from "react";
import {
  IconButton,
  IconButtonProps,
  styled,
  keyframes,
  useTheme,
} from "@mui/material";
import MicIcon from "@mui/icons-material/Mic";
import { Call } from "@vapi-ai/web/dist/api";

const pulse = keyframes`
  0% {
    box-shadow: 0 0 0 0 rgba(25, 118, 210, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(25, 118, 210, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(25, 118, 210, 0);
  }
`;

const wave = keyframes`
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
`;

const AnimatedIconButton = styled(IconButton)<
  IconButtonProps & { isRecording: boolean }
>(({ theme, isRecording }) => ({
  color: theme.palette.primary.main,
  transition: theme.transitions.create(
    ["background-color", "transform", "color"],
    {
      duration: theme.transitions.duration.short,
    }
  ),
  width: theme.typography.h4.fontSize,
  height: theme.typography.h4.fontSize,
  "& .MuiSvgIcon-root": {
    fontSize: theme.typography.h4.fontSize,
  },
  "&:hover": {
    backgroundColor: theme.palette.action.hover,
  },
  ...(isRecording && {
    animation: `${pulse} 2s infinite`,
    backgroundColor: theme.palette.primary.main,
    color: theme.palette.primary.contrastText,
    "&:hover": {
      backgroundColor: theme.palette.primary.dark,
    },
  }),
}));

const AnimatedMicIcon = styled(MicIcon)<{ isRecording: boolean }>(
  ({ isRecording }) => ({
    transition: "transform 0.3s ease-in-out",
    ...(isRecording && {
      animation: `${wave} 1s ease-in-out infinite`,
    }),
  })
);

interface AnimatedMicIconButtonProps extends Omit<IconButtonProps, "onClick"> {
  onRecordToggle?: (isRecording: boolean) => void;
  onStart: () => Promise<Call | null>;
  onStop: () => void;
}

const AnimatedMicIconButton: React.FC<AnimatedMicIconButtonProps> = ({
  onRecordToggle,
  onStart,
  onStop,
  ...props
}) => {
  const [isRecording, setIsRecording] = useState(false);
  const theme = useTheme();

  const handleClick = useCallback(async () => {
    try {
      if (!isRecording) {
        const call = await onStart();
        if (call) {
          setIsRecording(true);
          onRecordToggle?.(true);
        }
      } else {
        onStop();
        setIsRecording(false);
        onRecordToggle?.(false);
      }
    } catch (error) {
      console.error("Error toggling recording:", error);
      // Optionally, you can add error handling UI here
    }
  }, [isRecording, onStart, onStop, onRecordToggle]);

  return (
    <AnimatedIconButton
      onClick={handleClick}
      isRecording={isRecording}
      aria-label={isRecording ? "Stop recording" : "Start recording"}
      disabled={props.disabled}
      {...props}
    >
      <AnimatedMicIcon isRecording={isRecording} />
    </AnimatedIconButton>
  );
};

export default AnimatedMicIconButton;
