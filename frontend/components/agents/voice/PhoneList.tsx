import React from "react";
import {
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Stack,
} from "@mui/material";
import { Edit, Delete } from "@mui/icons-material";
import { PhoneDetails, agentsAction } from "@/slices/agents/agentsSlice";
import { deletePhone } from "@/requests/agents/voice/deletePhone";
import { useDispatch } from "react-redux";

interface PhoneListProps {
  phoneDetails: PhoneDetails[];
  onEdit: (index: number) => void;
  setPhoneDetails: React.Dispatch<React.SetStateAction<PhoneDetails[]>>;
}

const PhoneList: React.FC<PhoneListProps> = ({
  phoneDetails,
  onEdit,
  setPhoneDetails,
}) => {
  const dispatch = useDispatch();

  const handleDelete = async (phoneId: string) => {
    const response = await deletePhone({ phoneId }, dispatch);

    if (response.status === 200) {
      const phoneId = response.data.phoneId;
      dispatch(agentsAction.deletePhone(phoneId));
    }
  };

  return (
    <List>
      {phoneDetails.map((phone, index) => (
        <ListItem key={index} className="bg-white mb-2 rounded-md shadow-sm">
          <ListItemText
            primary={phone.name}
            secondary={
              <>
                <p>{phone.phoneNumber}</p>
                {phone.fallbackDestination && (
                  <p className="text-sm text-gray-600">
                    Fallback: {phone.fallbackDestination.number}
                  </p>
                )}
              </>
            }
          />
          <ListItemSecondaryAction>
            <Stack direction={"row"} gap={2}>
              <IconButton
                edge="end"
                aria-label="edit"
                onClick={() => onEdit(index)}
              >
                <Edit />
              </IconButton>
              <IconButton
                edge="end"
                aria-label="delete"
                onClick={() => handleDelete(phone.phoneId)}
              >
                <Delete />
              </IconButton>
            </Stack>
          </ListItemSecondaryAction>
        </ListItem>
      ))}
    </List>
  );
};

export default PhoneList;
