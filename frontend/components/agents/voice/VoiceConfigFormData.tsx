import React from "react";
import {
  <PERSON><PERSON><PERSON>,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  <PERSON>ack,
  SelectChangeEvent,
  Switch,
  FormControl<PERSON><PERSON><PERSON>,
  <PERSON>lide<PERSON>,
} from "@mui/material";
import {
  VoiceProvider,
  VoiceId,
  FirstMessageMode,
  OpenAIModel,
  BackgroundSound,
  allowedOpenAiModels,
  AIProviders,
} from "./voice.type";
import { IVoiceConfig } from "@/slices/agents/agentsSlice";

export type VoiceConfigFormData = Omit<
  IVoiceConfig,
  "assistantId" | "enabled" | "tools"
>;

const STABILITY_MIN = 0;
const STABILITY_MAX = 1;
const STABILITY_DEFAULT = 0.5;
const SIMILARITY_BOOST_MIN = 0;
const SIMILARITY_BOOST_MAX = 1;
const SIMILARITY_BOOST_DEFAULT = 0.5;
const STYLE_MIN = 0;
const STYLE_MAX = 1;
const STYLE_DEFAULT = 0.5;
const OPTIMIZE_STREAMING_LATENCY_MIN = 1;
const OPTIMIZE_STREAMING_LATENCY_MAX = 4;
const OPTIMIZE_STREAMING_LATENCY_DEFAULT = 2;
const SILENCE_TIMEOUT_MIN = 10;
const SILENCE_TIMEOUT_MAX = 60;
const SILENCE_TIMEOUT_DEFAULT = 30;

export const defaultConfig: VoiceConfigFormData = {
  channel: null,
  provider: VoiceProvider.ElevenLabs,
  firstMessage: "Hello there, how can I help you ?",
  firstMessageMode: FirstMessageMode.AssistantSpeaksFirst,
  model: {
    model: OpenAIModel.GPT4O,
    provider: "openai",
  },
  voice: {
    fillerInjectionEnabled: true,
    provider: VoiceProvider.ElevenLabs,
    voiceId: VoiceId.Sarah,
    stability: STABILITY_DEFAULT,
    similarityBoost: SIMILARITY_BOOST_DEFAULT,
    style: STYLE_DEFAULT,
    optimizeStreamingLatency: OPTIMIZE_STREAMING_LATENCY_DEFAULT,
    useSpeakerBoost: true,
  },
  backchannelingEnabled: true,
  backgroundDenoisingEnabled: true,
  silenceTimeoutSeconds: SILENCE_TIMEOUT_DEFAULT,
  backgroundSound: BackgroundSound.Office,
  phoneDetails: [],
};

interface VoiceConfigFormProps {
  config: VoiceConfigFormData;
  onChange: (newConfig: VoiceConfigFormData) => void;
}

const VoiceConfigForm: React.FC<VoiceConfigFormProps> = ({
  config,
  onChange,
}) => {
  const mergedConfig = React.useMemo(
    () => ({
      ...defaultConfig,
      ...config,
      model: { ...defaultConfig.model, ...config.model },
      voice: { ...defaultConfig.voice, ...config.voice },
    }),
    [config]
  );

  const handleChange = <T extends keyof VoiceConfigFormData>(
    name: T,
    value: VoiceConfigFormData[T]
  ) => {
    onChange({ ...mergedConfig, [name]: value });
  };

  const handleVoiceChange = <T extends keyof VoiceConfigFormData["voice"]>(
    name: T,
    value: VoiceConfigFormData["voice"][T]
  ) => {
    onChange({
      ...mergedConfig,
      voice: { ...mergedConfig.voice, [name]: value },
    });
  };

  const handleModelChange = <T extends keyof VoiceConfigFormData["model"]>(
    name: T,
    value: VoiceConfigFormData["model"][T]
  ) => {
    onChange({
      ...mergedConfig,
      model: { ...mergedConfig.model, [name]: value },
    });
  };

  const handleTextChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = event.target;
    if (name === "firstMessage") {
      handleChange(name, value);
    } else if (name === "provider" && name in mergedConfig.model) {
      handleModelChange(name, value);
    }
  };

  const handleSelectChange = (event: SelectChangeEvent<string>) => {
    const { name, value } = event.target;
    if (name === "firstMessageMode") {
      handleChange(name, value as FirstMessageMode);
    } else if (name === "backgroundSound") {
      handleChange(name, value as BackgroundSound);
    } else if (name === "model" && name in mergedConfig.model) {
      handleModelChange(name, value as OpenAIModel);
    } else if (name === "provider") {
      if (name in mergedConfig.voice) {
        handleVoiceChange(name, value as VoiceProvider);
      } else if (name in mergedConfig.model) {
        handleModelChange(name, value as AIProviders);
      }
    } else if (name === "voiceId") {
      handleVoiceChange(name, value as VoiceId);
    }
  };

  const handleSwitchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = event.target;
    if (
      name === "backchannelingEnabled" ||
      name === "backgroundDenoisingEnabled"
    ) {
      handleChange(name, checked);
    } else if (
      name === "fillerInjectionEnabled" ||
      name === "useSpeakerBoost"
    ) {
      handleVoiceChange(name, checked);
    }
  };

  const handleSliderChange =
    (name: string) => (_event: Event, newValue: number | number[]) => {
      if (name === "silenceTimeoutSeconds") {
        handleChange(name, newValue as number);
      } else if (name in mergedConfig.voice) {
        handleVoiceChange(
          name as keyof VoiceConfigFormData["voice"],
          newValue as number
        );
      }
    };

  return (
    <Stack spacing={2} mt={1}>
      <FormControl fullWidth>
        <InputLabel id="model-provider-label">Model Provider</InputLabel>
        <Select
          labelId="model-provider-label"
          name="provider"
          value={mergedConfig.model?.provider || ""}
          onChange={handleSelectChange}
          label="Model Provider"
        >
          {Object.values(AIProviders).map((provider) => (
            <MenuItem key={provider} value={provider}>
              {provider}
            </MenuItem>
          ))}
        </Select>
      </FormControl>
      <FormControl fullWidth>
        <InputLabel id="model-label">Model</InputLabel>
        <Select
          labelId="model-label"
          name="model"
          value={mergedConfig.model?.model || ""}
          onChange={handleSelectChange}
          label="Model"
        >
          {allowedOpenAiModels.map((model) => (
            <MenuItem key={model} value={model}>
              {model}
            </MenuItem>
          ))}
        </Select>
      </FormControl>
      <FormControl fullWidth required>
        <InputLabel id="first-message-mode-label">
          First Message Mode
        </InputLabel>
        <Select
          labelId="first-message-mode-label"
          name="firstMessageMode"
          value={mergedConfig.firstMessageMode}
          onChange={handleSelectChange}
          label="First Message Mode"
        >
          {Object.values(FirstMessageMode).map((mode) => (
            <MenuItem key={mode} value={mode}>
              {mode}
            </MenuItem>
          ))}
        </Select>
      </FormControl>
      {mergedConfig.firstMessageMode ===
        FirstMessageMode.AssistantSpeaksFirst && (
        <TextField
          name="firstMessage"
          label="First Message"
          value={mergedConfig.firstMessage}
          onChange={handleTextChange}
          fullWidth
          multiline
          rows={3}
          required
        />
      )}
      <FormControlLabel
        control={
          <Switch
            checked={mergedConfig.backchannelingEnabled}
            onChange={handleSwitchChange}
            name="backchannelingEnabled"
          />
        }
        label="Backchanneling Enabled"
      />
      <FormControlLabel
        control={
          <Switch
            checked={mergedConfig.backgroundDenoisingEnabled}
            onChange={handleSwitchChange}
            name="backgroundDenoisingEnabled"
          />
        }
        label="Background Denoising Enabled"
      />
      <FormControl fullWidth>
        <InputLabel id="background-sound-label">Background Sound</InputLabel>
        <Select
          labelId="background-sound-label"
          name="backgroundSound"
          value={mergedConfig.backgroundSound}
          onChange={handleSelectChange}
          label="Background Sound"
        >
          {Object.values(BackgroundSound).map((sound) => (
            <MenuItem key={sound} value={sound}>
              {sound}
            </MenuItem>
          ))}
        </Select>
      </FormControl>
      <FormControl fullWidth>
        <InputLabel htmlFor="silence-timeout-slider">
          Silence Timeout (seconds)
        </InputLabel>
        <Slider
          name="silenceTimeoutSeconds"
          value={mergedConfig.silenceTimeoutSeconds}
          onChange={handleSliderChange("silenceTimeoutSeconds")}
          aria-labelledby="silence-timeout-slider"
          valueLabelDisplay="auto"
          step={1}
          marks
          min={SILENCE_TIMEOUT_MIN}
          max={SILENCE_TIMEOUT_MAX}
        />
      </FormControl>
      <FormControl fullWidth>
        <InputLabel id="voice-provider-label">Voice Provider</InputLabel>
        <Select
          labelId="voice-provider-label"
          name="provider"
          value={mergedConfig.voice?.provider}
          onChange={handleSelectChange}
          label="Voice Provider"
        >
          {Object.values(VoiceProvider).map((provider) => (
            <MenuItem key={provider} value={provider}>
              {provider}
            </MenuItem>
          ))}
        </Select>
      </FormControl>
      <FormControl fullWidth>
        <InputLabel id="voice-id-label">Voice ID</InputLabel>
        <Select
          labelId="voice-id-label"
          name="voiceId"
          value={mergedConfig.voice?.voiceId}
          onChange={handleSelectChange}
          label="Voice ID"
        >
          {Object.values(VoiceId).map((id) => (
            <MenuItem key={id} value={id}>
              {id}
            </MenuItem>
          ))}
        </Select>
      </FormControl>
      <FormControlLabel
        control={
          <Switch
            checked={mergedConfig.voice?.fillerInjectionEnabled}
            onChange={handleSwitchChange}
            name="fillerInjectionEnabled"
          />
        }
        label="Filler Injection Enabled"
      />
      <FormControl fullWidth>
        <InputLabel htmlFor="stability-slider">Stability</InputLabel>
        <Slider
          name="stability"
          value={mergedConfig.voice?.stability}
          onChange={handleSliderChange("stability")}
          aria-labelledby="stability-slider"
          valueLabelDisplay="auto"
          step={0.1}
          marks
          min={STABILITY_MIN}
          max={STABILITY_MAX}
        />
      </FormControl>
      <FormControl fullWidth>
        <InputLabel htmlFor="similarity-boost-slider">
          Similarity Boost
        </InputLabel>
        <Slider
          name="similarityBoost"
          value={mergedConfig.voice?.similarityBoost}
          onChange={handleSliderChange("similarityBoost")}
          aria-labelledby="similarity-boost-slider"
          valueLabelDisplay="auto"
          step={0.1}
          marks
          min={SIMILARITY_BOOST_MIN}
          max={SIMILARITY_BOOST_MAX}
        />
      </FormControl>
      <FormControl fullWidth>
        <InputLabel htmlFor="style-slider">Style</InputLabel>
        <Slider
          name="style"
          value={mergedConfig.voice?.style}
          onChange={handleSliderChange("style")}
          aria-labelledby="style-slider"
          valueLabelDisplay="auto"
          step={0.1}
          marks
          min={STYLE_MIN}
          max={STYLE_MAX}
        />
      </FormControl>
      <FormControl fullWidth>
        <InputLabel htmlFor="optimize-streaming-latency-slider">
          Optimize Streaming Latency
        </InputLabel>
        <Slider
          name="optimizeStreamingLatency"
          value={mergedConfig.voice?.optimizeStreamingLatency}
          onChange={handleSliderChange("optimizeStreamingLatency")}
          aria-labelledby="optimize-streaming-latency-slider"
          valueLabelDisplay="auto"
          step={1}
          marks
          min={OPTIMIZE_STREAMING_LATENCY_MIN}
          max={OPTIMIZE_STREAMING_LATENCY_MAX}
        />
      </FormControl>
      <FormControlLabel
        control={
          <Switch
            checked={mergedConfig.voice?.useSpeakerBoost}
            onChange={handleSwitchChange}
            name="useSpeakerBoost"
          />
        }
        label="Use Speaker Boost"
      />
    </Stack>
  );
};

export default VoiceConfigForm;
