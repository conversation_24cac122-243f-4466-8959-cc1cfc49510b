import React, { useState, useEffect, useMemo } from "react";
import {
  Button,
  Switch,
  TextField,
  Typography,
  Paper,
  FormControlLabel,
  Box,
  Divider,
  CircularProgress,
  FormHelperText,
} from "@mui/material";
import { Add, PhoneDisabled } from "@mui/icons-material";
import { useDispatch, useSelector } from "react-redux";
import { useRouter } from "next/router";
import { RootState } from "@/store";
import { getAllPhoneDetails } from "@/requests/agents/voice/getAllPhoneDetails";
import {
  IVoiceConfig,
  PhoneDetails,
  TransferCallDetails,
} from "@/slices/agents/agentsSlice";
import PhoneList from "./PhoneList";
import PhoneDialog from "./PhoneDialog";
import { patchTransferCall } from "@/requests/agents/voice/patchTransferCall";
import { agentsAction } from "@/slices/agents/agentsSlice";

const PhoneNumberConnection: React.FC = () => {
  const dispatch = useDispatch();
  const router = useRouter();
  const { agentId } = router.query as { agentId: string };
  const {
    assistantId,
    phoneDetails: phoneDetailsFromStore,
    transferCall,
  } = useSelector(
    (state: RootState) => state.agents.currentAgent.voiceConfig
  ) as IVoiceConfig;
  const [phoneDetails, setPhoneDetails] = useState<PhoneDetails[]>(
    phoneDetailsFromStore
  );
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingIndex, setEditingIndex] = useState<number | null>(null);

  // Keep the original server state
  const [originalTransferCallSettings, setOriginalTransferCallSettings] =
    useState<TransferCallDetails | null>(transferCall || null);

  const [transferCallSettings, setTransferCallSettings] =
    useState<TransferCallDetails>(
      transferCall || {
        transferCallEnabled: false,
        transferCallNumber: "",
        transferMessage: "",
        transferCallPrompt: "",
      }
    );
  const [isTransferCallEnabled, setIsTransferCallEnabled] = useState(
    transferCall?.transferCallEnabled || false
  );
  const [isSaving, setIsSaving] = useState(false);

  // Check if settings have changed
  const hasChanges = useMemo(() => {
    if (!originalTransferCallSettings) return true;

    return (
      originalTransferCallSettings.transferCallEnabled !==
        transferCallSettings.transferCallEnabled ||
      originalTransferCallSettings.transferCallNumber !==
        transferCallSettings.transferCallNumber ||
      originalTransferCallSettings.transferMessage !==
        transferCallSettings.transferMessage ||
      originalTransferCallSettings.transferCallPrompt !==
        transferCallSettings.transferCallPrompt
    );
  }, [originalTransferCallSettings, transferCallSettings]);

  const orgId = localStorage.getItem("orgId") as string;

  useEffect(() => {
    setPhoneDetails(phoneDetailsFromStore);
  }, [phoneDetailsFromStore]);

  useEffect(() => {
    if (transferCall) {
      setOriginalTransferCallSettings(transferCall);
      setTransferCallSettings(transferCall);
      setIsTransferCallEnabled(transferCall.transferCallEnabled);
    }
  }, [transferCall]);

  useEffect(() => {
    const fetchPhoneDetails = async () => {
      const response = await getAllPhoneDetails({ agentId }, dispatch);
      if (response.status === 200) {
        const data = response.data;
        data && setPhoneDetails(data);
      }
    };
    fetchPhoneDetails();
  }, [dispatch, agentId]);

  const handleOpenDialog = (index: number | null) => {
    setEditingIndex(index);
    setIsDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setIsDialogOpen(false);
    setEditingIndex(null);
  };

  const handleTransferCallToggle = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    setIsTransferCallEnabled(event.target.checked);
    setTransferCallSettings({
      ...transferCallSettings,
      transferCallEnabled: event.target.checked,
    });
  };

  const handleTransferCallTextChange = (
    event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
    field: keyof Omit<TransferCallDetails, "transferCallEnabled">
  ) => {
    setTransferCallSettings({
      ...transferCallSettings,
      [field]: event.target.value,
    });
  };

  const saveTransferCallSettings = async () => {
    setIsSaving(true);
    try {
      const response = await patchTransferCall(
        {
          agentId,
          ...transferCallSettings,
        },
        dispatch
      );

      if (response.status === 200) {
        dispatch(agentsAction.updateTransferCall(transferCallSettings));
        // Update original settings after successful save
        setOriginalTransferCallSettings({ ...transferCallSettings });
      }
    } catch (error) {
      console.error("Error saving transfer call settings:", error);
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="p-4 bg-gray-100 rounded-lg shadow">
      <h2 className="text-2xl font-bold mb-4">Phone Number Connections</h2>
      {phoneDetails && phoneDetails.length > 0 ? (
        <PhoneList
          phoneDetails={phoneDetails}
          onEdit={handleOpenDialog}
          setPhoneDetails={setPhoneDetails}
        />
      ) : (
        <Paper
          elevation={1}
          sx={{
            py: 4,
            px: 3,
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            borderRadius: 2,
            backgroundColor: "#f9f9f9",
            border: "1px dashed #ccc",
          }}
        >
          <PhoneDisabled
            sx={{ fontSize: 48, color: "text.secondary", mb: 2 }}
          />
          <Typography variant="h6" color="text.secondary" gutterBottom>
            No phone numbers added yet
          </Typography>
          <Typography
            variant="body2"
            color="text.secondary"
            align="center"
            sx={{ mb: 2 }}
          >
            Add a phone number to connect with your agent&apos;s voice
            capabilities
          </Typography>
        </Paper>
      )}
      <Button
        startIcon={<Add />}
        variant="contained"
        color="primary"
        onClick={() => handleOpenDialog(null)}
        className="mt-4"
        sx={{ mt: 3 }}
      >
        Add Phone Number
      </Button>

      <Box mt={2}>
        <FormControlLabel
          control={
            <Switch
              checked={isTransferCallEnabled}
              onChange={handleTransferCallToggle}
            />
          }
          label="Enable Transfer Call"
        />
        <FormHelperText sx={{ ml: 6 }}>
          When enabled, calls can be transferred to another phone number with
          custom messaging.
        </FormHelperText>

        {isTransferCallEnabled && (
          <Box className="mt-3 space-y-3">
            <TextField
              fullWidth
              label="Transfer Call Number"
              variant="outlined"
              name="transferCallNumber"
              value={transferCallSettings.transferCallNumber}
              onChange={(e) =>
                handleTransferCallTextChange(e, "transferCallNumber")
              }
              margin="normal"
            />

            <TextField
              fullWidth
              label="Transfer Message"
              variant="outlined"
              name="transferMessage"
              value={transferCallSettings.transferMessage}
              onChange={(e) =>
                handleTransferCallTextChange(e, "transferMessage")
              }
              margin="normal"
              multiline
              rows={2}
            />

            <TextField
              fullWidth
              label="Transfer Call Prompt"
              variant="outlined"
              name="transferCallPrompt"
              value={transferCallSettings.transferCallPrompt}
              onChange={(e) =>
                handleTransferCallTextChange(e, "transferCallPrompt")
              }
              margin="normal"
              multiline
              rows={2}
            />
          </Box>
        )}

        {hasChanges && (
          <Box className="mt-4">
            <Button
              variant="contained"
              color="primary"
              onClick={saveTransferCallSettings}
              disabled={isSaving}
            >
              {isSaving ? (
                <CircularProgress size={24} color="inherit" />
              ) : (
                "Save"
              )}
            </Button>
          </Box>
        )}
      </Box>

      <PhoneDialog
        isOpen={isDialogOpen}
        onClose={handleCloseDialog}
        editingIndex={editingIndex}
        phoneDetails={phoneDetails}
        orgId={orgId}
        agentId={agentId}
        assistantId={assistantId}
      />
    </div>
  );
};

export default PhoneNumberConnection;
