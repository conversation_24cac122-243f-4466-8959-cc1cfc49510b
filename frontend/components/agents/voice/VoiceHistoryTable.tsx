import React, { useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Typography,
  IconButton,
  Box,
  Tooltip,
  useTheme,
} from "@mui/material";
import {
  ContentCopy,
  KeyboardArrowLeft,
  KeyboardArrowRight,
} from "@mui/icons-material";
import { formatDate } from "@/helpers/basic";
import CallDetailsDrawer from "./CallDetailsDrawer";

export interface CallHistory {
  type: keyof typeof callTypeValues;
  callId: string;
  totalCost: number;
  createdAt: string;
  success: boolean;
  summary: string;
}

interface VoiceHistoryProps {
  callHistory: CallHistory[];
  onPageChange: (page: number) => void;
  currentPage: number;
  totalPages: number;
}

const callTypeValues: { [key: string]: string } = {
  webCall: "Web",
  inboundPhoneCall: "Inbound call",
  outboundPhoneCall: "Outbound call",
};

const VoiceHistory: React.FC<VoiceHistoryProps> = ({
  callHistory,
  onPageChange,
  currentPage,
  totalPages,
}) => {
  const theme = useTheme();
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [selectedCallId, setSelectedCallId] = useState("");

  const handleCopyCallId = (event: React.MouseEvent, callId: string) => {
    event.stopPropagation();
    navigator.clipboard.writeText(callId);
  };

  const truncateSummary = (summary: string, maxLength: number) => {
    return summary?.length > maxLength
      ? `${summary?.substring(0, maxLength)}...`
      : summary;
  };

  const handleRowClick = (callId: string) => {
    setSelectedCallId(callId);
    setDrawerOpen(true);
  };

  const handleDrawerClose = () => {
    setDrawerOpen(false);
  };

  return (
    <Box sx={{ width: "100%", overflowX: "auto", mt: 4 }}>
      <TableContainer
        component={Paper}
        sx={{
          backgroundColor: "rgb(243 244 246)",
          color: theme.palette.text.primary,
          maxHeight: 400,
        }}
      >
        <Table stickyHeader aria-label="voice history table">
          <TableHead>
            <TableRow>
              <TableCell sx={{ backgroundColor: "rgb(229 231 235)" }}>
                Type
              </TableCell>
              <TableCell sx={{ backgroundColor: "rgb(229 231 235)" }}>
                Call ID
              </TableCell>
              <TableCell sx={{ backgroundColor: "rgb(229 231 235)" }}>
                Call Cost
              </TableCell>
              <TableCell sx={{ backgroundColor: "rgb(229 231 235)" }}>
                Created At
              </TableCell>
              <TableCell sx={{ backgroundColor: "rgb(229 231 235)" }}>
                Success
              </TableCell>
              <TableCell sx={{ backgroundColor: "rgb(229 231 235)" }}>
                Summary
              </TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {callHistory?.map((call) => (
              <TableRow
                key={call.callId}
                sx={{
                  "&:last-child td, &:last-child th": { border: 0 },
                  cursor: "pointer",
                  "&:hover": { backgroundColor: "rgba(0, 0, 0, 0.04)" },
                }}
                onClick={() => handleRowClick(call.callId)}
              >
                <TableCell>{callTypeValues[call.type]}</TableCell>
                <TableCell>
                  {call.callId.slice(0, 8)}...
                  <Tooltip title="Copy Call ID">
                    <IconButton
                      size="small"
                      onClick={(e) => handleCopyCallId(e, call.callId)}
                      sx={{ ml: 1 }}
                    >
                      <ContentCopy />
                    </IconButton>
                  </Tooltip>
                </TableCell>
                <TableCell>${call.totalCost.toFixed(2)}</TableCell>
                <TableCell>{formatDate(call.createdAt)}</TableCell>
                <TableCell>
                  <Typography
                    variant="body2"
                    sx={{
                      bgcolor: call.success ? "success.main" : "error.main",
                      color: "white",
                      p: 0.5,
                      borderRadius: 1,
                      display: "inline-block",
                    }}
                  >
                    {call.success ? "Success" : "Failed"}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Tooltip title={call.summary} arrow placement="top">
                    <Typography>{truncateSummary(call.summary, 50)}</Typography>
                  </Tooltip>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
      <Box
        sx={{
          display: "flex",
          justifyContent: "center",
          mt: 2,
          color: "rgb(243 244 246)",
        }}
      >
        <IconButton
          onClick={() => onPageChange(currentPage - 1)}
          disabled={currentPage === 1}
        >
          <KeyboardArrowLeft />
        </IconButton>
        <Typography sx={{ mx: 2, alignSelf: "center" }}>
          Page {currentPage} of {totalPages}
        </Typography>
        <IconButton
          onClick={() => onPageChange(currentPage + 1)}
          disabled={currentPage === totalPages}
        >
          <KeyboardArrowRight />
        </IconButton>
      </Box>

      <CallDetailsDrawer
        open={drawerOpen}
        onClose={handleDrawerClose}
        callId={selectedCallId}
      />
    </Box>
  );
};

export default VoiceHistory;
