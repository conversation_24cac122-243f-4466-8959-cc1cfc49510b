import React, { useState, useEffect, useCallback } from "react";
import {
  <PERSON><PERSON>,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Autocomplete,
  TextField,
  CircularProgress,
} from "@mui/material";
import { IVoiceConfig } from "@/slices/agents/agentsSlice";
import { CONSTANTS } from "@/helpers/constants";
import { getChannelAccounts } from "@/requests/channels/getChannelAccounts";
import { useDispatch } from "react-redux";

interface AddConnectionDialogProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (channelData: IVoiceConfig["channel"]) => void;
  channel?: IVoiceConfig["channel"];
}

interface Provider {
  providerName: string;
  displayName: string;
}

interface Account {
  accountId: string;
  name: string;
  providerName: string;
  keyId: string;
}

const providerMap: Provider[] = [
  { providerName: CONSTANTS.PROVIDERS.GHL_CHANNEL, displayName: "Highlevel" },
  { providerName: CONSTANTS.PROVIDERS.HUBSPOT, displayName: "Hubspot" },
  // Add more providers as needed
];

const AddConnectionDialog: React.FC<AddConnectionDialogProps> = ({
  open,
  onClose,
  onSubmit,
  channel,
}) => {
  const dispatch = useDispatch();
  const [selectedProvider, setSelectedProvider] = useState<Provider | null>(
    null
  );
  const [selectedAccount, setSelectedAccount] = useState<Account | null>(null);
  const [accounts, setAccounts] = useState<Account[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const orgId = localStorage.getItem("orgId") as string;

  const fetchAccounts = useCallback(async () => {
    if (!selectedProvider?.providerName) return;

    setLoading(true);
    setError(null);
    try {
      const response = await getChannelAccounts(
        { orgId, provider: selectedProvider.providerName },
        dispatch
      );
      if (response.status !== 200) {
        throw new Error("Failed to fetch accounts");
      }
      const data: Account[] = await response.data.channels;
      setAccounts(data);
    } catch (err) {
      setError("Error fetching accounts. Please try again.");
      console.error("Error fetching accounts:", err);
    } finally {
      setLoading(false);
    }
  }, [selectedProvider, orgId, dispatch]);

  useEffect(() => {
    if (open && selectedProvider?.providerName) {
      fetchAccounts();
    }
  }, [open, selectedProvider, fetchAccounts]);

  useEffect(() => {
    if (channel) {
      const provider = providerMap.find(
        (p) => p.providerName === channel.providerName
      );
      setSelectedProvider(provider || null);
      setSelectedAccount({
        accountId: channel.accountId,
        name: channel.name,
        providerName: channel.providerName,
        keyId: channel.keyId,
      });
    }
  }, [channel]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (selectedProvider && selectedAccount) {
      onSubmit({
        accountId: selectedAccount.accountId,
        providerName: selectedProvider.providerName,
        name: selectedAccount.name,
        keyId: selectedAccount.keyId,
      });
      onClose();
    }
  };

  return (
    <Dialog open={open} onClose={onClose} fullWidth>
      <form onSubmit={handleSubmit}>
        <DialogTitle>Add New Connection</DialogTitle>
        <DialogContent>
          <Autocomplete
            options={providerMap}
            getOptionLabel={(option) => option.displayName}
            renderInput={(params) => (
              <TextField {...params} label="Provider" margin="normal" />
            )}
            value={selectedProvider}
            onChange={(_, newValue) => {
              setSelectedProvider(newValue);
              setSelectedAccount(null);
            }}
            fullWidth
          />
          <Autocomplete
            options={accounts.filter(
              (account) =>
                !selectedProvider ||
                account.providerName === selectedProvider.providerName
            )}
            getOptionLabel={(option) => option.name}
            renderInput={(params) => (
              <TextField {...params} label="Account" margin="normal" />
            )}
            value={selectedAccount}
            onChange={(_, newValue) => setSelectedAccount(newValue)}
            fullWidth
            disabled={!selectedProvider || loading}
            loading={loading}
          />
          {loading && <CircularProgress size={24} />}
          {error && <p style={{ color: "red" }}>{error}</p>}
        </DialogContent>
        <DialogActions>
          <Button onClick={onClose}>Cancel</Button>
          <Button
            type="submit"
            variant="contained"
            color="primary"
            disabled={!selectedProvider || !selectedAccount || loading}
          >
            Connect
          </Button>
        </DialogActions>
      </form>
    </Dialog>
  );
};

export default AddConnectionDialog;
