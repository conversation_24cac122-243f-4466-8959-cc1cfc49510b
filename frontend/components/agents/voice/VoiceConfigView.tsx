import React, { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store";
import { Paper, Stack, Typography, Chip, Tooltip } from "@mui/material";
import VoiceConfigEdit from "./VoiceConfigEdit";
import VoiceToggle from "./VoiceToggle";
import { toggleStatus } from "@/requests/voice/toggleStatus";
import { useRouter } from "next/router";
import { agentsAction } from "@/slices/agents/agentsSlice";

const VoiceConfigView: React.FC = () => {
  const dispatch = useDispatch();
  const router = useRouter();
  const { agentId } = router.query as { agentId: string };
  const voiceConfig = useSelector(
    (state: RootState) => state.agents.currentAgent.voiceConfig
  );

  useEffect(() => {
    console.log({ voiceConfig });
  }, [voiceConfig]);

  const configItems = [
    { label: "Assistant ID", value: voiceConfig?.assistantId },
    { label: "First Message", value: voiceConfig?.firstMessage },
    { label: "First Message Mode", value: voiceConfig?.firstMessageMode },
    {
      label: "Model",
      value: `${voiceConfig?.model?.provider} / ${voiceConfig?.model?.model}`,
    },
    { label: "Voice Provider", value: voiceConfig?.voice?.provider },
    { label: "Voice ID", value: voiceConfig?.voice?.voiceId },
    { label: "Stability", value: voiceConfig?.voice?.stability },
    { label: "Similarity Boost", value: voiceConfig?.voice?.similarityBoost },
    { label: "Style", value: voiceConfig?.voice?.style },
    {
      label: "Optimize Streaming Latency",
      value: voiceConfig?.voice?.optimizeStreamingLatency,
    },
    {
      label: "Silence Timeout",
      value: `${voiceConfig?.silenceTimeoutSeconds} seconds`,
    },
    { label: "Background Sound", value: voiceConfig?.backgroundSound },
  ];

  const toggleVoiceStatus = async () => {
    const previousEnabledStatus = voiceConfig?.enabled ?? false;
    const newEnabledStatus = !previousEnabledStatus;
    dispatch(agentsAction.toggleVoiceStatus(newEnabledStatus));
    const response = await toggleStatus(
      { agentId, enabled: newEnabledStatus },
      dispatch
    );
    if (response.status !== 200) {
      dispatch(agentsAction.toggleVoiceStatus(previousEnabledStatus));
    }
  };

  const renderChip = (label: string, value: boolean | undefined) => (
    <Tooltip title={`${label}: ${value ? "Enabled" : "Disabled"}`}>
      <Chip
        label={label}
        color={value ? "success" : "default"}
        size="small"
        variant="outlined"
      />
    </Tooltip>
  );

  return (
    <div className="p-4 bg-gray-100 rounded-lg shadow">
      <Stack spacing={3}>
        <Stack
          direction="row"
          alignItems="center"
          justifyContent="space-between"
        >
          <Stack direction="row" alignItems="center" spacing={2}>
            <h2 className="text-2xl font-bold mb-4">Voice Settings</h2>
            <VoiceConfigEdit agentId={agentId} />
          </Stack>
          <VoiceToggle
            isActive={voiceConfig?.enabled ?? false}
            onChange={toggleVoiceStatus}
          />
        </Stack>
        {voiceConfig ? (
          <>
            <Stack direction="row" spacing={1}>
              {renderChip(
                "Filler Injection",
                voiceConfig.voice?.fillerInjectionEnabled
              )}
              {renderChip("Speaker Boost", voiceConfig.voice?.useSpeakerBoost)}
              {renderChip("Backchanneling", voiceConfig.backchannelingEnabled)}
              {renderChip(
                "Background Denoising",
                voiceConfig.backgroundDenoisingEnabled
              )}
            </Stack>
            <Stack spacing={2}>
              {configItems.map((item, index) => (
                <Stack
                  key={index}
                  direction="row"
                  justifyContent="space-between"
                  alignItems="center"
                >
                  <Typography variant="body2" color="text.secondary">
                    {item.label}:
                  </Typography>
                  <Typography variant="body2" fontWeight="medium">
                    {item.value?.toString() ?? "N/A"}
                  </Typography>
                </Stack>
              ))}
            </Stack>
          </>
        ) : (
          <Typography variant="body2" color="text.secondary">
            No voice configuration available.
          </Typography>
        )}
      </Stack>
    </div>
  );
};

export default VoiceConfigView;
