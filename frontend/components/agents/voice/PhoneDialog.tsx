import React, { useState, FormEvent, useEffect } from "react";
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Switch,
  FormControlLabel,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  CircularProgress,
} from "@mui/material";
import { PhoneDetails, agentsAction } from "@/slices/agents/agentsSlice";
import { useDispatch } from "react-redux";
import { addPhone } from "@/requests/agents/voice/addPhone";
import { updatePhone } from "@/requests/agents/voice/updatePhone";

interface PhoneDialogProps {
  isOpen: boolean;
  onClose: () => void;
  editingIndex: number | null;
  phoneDetails: PhoneDetails[];
  orgId: string;
  agentId: string;
  assistantId: string;
}

const PhoneDialog: React.FC<PhoneDialogProps> = ({
  isOpen,
  onClose,
  editingIndex,
  phoneDetails,
  orgId,
  agentId,
  assistantId,
}) => {
  const dispatch = useDispatch();
  const [hasFallback, setHasFallback] = useState(false);
  const [provider, setProvider] = useState<"twilio" | "">("");
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (editingIndex !== null) {
      const currentPhone = phoneDetails[editingIndex];
      setHasFallback(!!currentPhone.fallbackDestination);
      setProvider(currentPhone.provider);
    } else {
      setHasFallback(false);
      setProvider("");
    }
  }, [editingIndex, phoneDetails]);

  const handleSave = async (event: FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    setIsLoading(true);

    try {
      const formData = new FormData(event.currentTarget);
      const newDetails: Omit<PhoneDetails, "phoneId"> = {
        phoneNumber: formData.get("number") as string,
        name: formData.get("name") as string,
        provider: provider as "twilio",
        fallbackDestination: hasFallback
          ? {
              type: "number",
              number: formData.get("fallbackNumber") as string,
              message: formData.get("fallbackMessage") as string,
              description: formData.get("fallbackDescription") as string,
            }
          : null,
      };

      if (provider === "twilio" && editingIndex === null) {
        newDetails.twilioAccountSid = formData.get(
          "twilioAccountSid"
        ) as string;
        newDetails.twilioAuthToken = formData.get("twilioAuthToken") as string;
      }

      if (editingIndex !== null) {
        const response = await updatePhone(
          {
            ...newDetails,
            orgId,
            agentId,
            assistantId,
            phoneId: phoneDetails[editingIndex].phoneId,
          },
          dispatch
        );
        if (response.status === 200) {
          dispatch(agentsAction.updatePhone(response.data));
        }
      } else {
        const response = await addPhone(
          {
            ...newDetails,
            orgId,
            agentId,
            assistantId,
          },
          dispatch
        );
        if (response.status === 201) {
          dispatch(agentsAction.addPhone(response.data));
        }
      }
    } finally {
      setIsLoading(false);
      onClose();
    }
  };

  return (
    <Dialog open={isOpen} onClose={onClose}>
      <form onSubmit={handleSave}>
        <DialogTitle>
          {editingIndex !== null ? "Edit" : "Add"} Phone Number
        </DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            name="name"
            label="Name"
            type="text"
            fullWidth
            variant="outlined"
            defaultValue={
              editingIndex !== null ? phoneDetails[editingIndex].name : ""
            }
          />
          <TextField
            margin="dense"
            name="number"
            label="Phone Number"
            type="tel"
            fullWidth
            variant="outlined"
            defaultValue={
              editingIndex !== null
                ? phoneDetails[editingIndex].phoneNumber
                : ""
            }
          />
          <FormControl fullWidth margin="dense">
            <InputLabel id="provider-label">Provider</InputLabel>
            <Select
              labelId="provider-label"
              value={provider}
              onChange={(e) => setProvider(e.target.value as "twilio" | "")}
              label="Provider"
              required
            >
              <MenuItem value="">
                <em>None</em>
              </MenuItem>
              <MenuItem value="twilio">Twilio</MenuItem>
            </Select>
          </FormControl>
          {provider === "twilio" && editingIndex === null && (
            <>
              <TextField
                margin="dense"
                name="twilioAccountSid"
                label="Twilio Account SID"
                type="text"
                fullWidth
                variant="outlined"
                required
              />
              <TextField
                margin="dense"
                name="twilioAuthToken"
                label="Twilio Auth Token"
                type="password"
                fullWidth
                variant="outlined"
                required
              />
            </>
          )}
          <FormControlLabel
            control={
              <Switch
                checked={hasFallback}
                onChange={(e) => setHasFallback(e.target.checked)}
              />
            }
            label="Enable Fallback Destination"
          />
          {hasFallback && (
            <>
              <TextField
                margin="dense"
                name="fallbackNumber"
                label="Fallback Number"
                type="tel"
                fullWidth
                variant="outlined"
                defaultValue={
                  editingIndex !== null
                    ? phoneDetails[editingIndex].fallbackDestination?.number
                    : ""
                }
              />
              <TextField
                margin="dense"
                name="fallbackMessage"
                label="Fallback Message"
                type="text"
                fullWidth
                variant="outlined"
                multiline
                rows={2}
                defaultValue={
                  editingIndex !== null
                    ? phoneDetails[editingIndex].fallbackDestination?.message
                    : ""
                }
              />
              <TextField
                margin="dense"
                name="fallbackDescription"
                label="Fallback Description"
                type="text"
                fullWidth
                variant="outlined"
                multiline
                rows={2}
                defaultValue={
                  editingIndex !== null
                    ? phoneDetails[editingIndex].fallbackDestination
                        ?.description
                    : ""
                }
              />
            </>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={onClose} disabled={isLoading}>
            Cancel
          </Button>
          <Button type="submit" disabled={isLoading}>
            {isLoading ? (
              <CircularProgress size={24} color="inherit" />
            ) : (
              "Save"
            )}
          </Button>
        </DialogActions>
      </form>
    </Dialog>
  );
};

export default PhoneDialog;
