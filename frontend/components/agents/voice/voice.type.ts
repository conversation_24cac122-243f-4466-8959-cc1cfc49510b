export enum AIProvider {
  OpenAI = "openai",
  // Add other providers as needed
}

export enum OpenAIModel {
  GPT4O = "gpt-4o",
  GPT4OMini = "gpt-4o-mini",
  GPT4Turbo = "gpt-4-turbo",
  GPT4TurboPreview = "gpt-4-turbo-preview",
  GPT41106Preview = "gpt-4-1106-preview",
  GPT4 = "gpt-4",
}

export enum AIProviders {
  OPENAI = "openai",
}

export const allowedOpenAiModels = [OpenAIModel.GPT4O, OpenAIModel.GPT4OMini];

// Enum for first message mode
export enum FirstMessageMode {
  AssistantSpeaksFirst = "assistant-speaks-first",
  AssistantSpeaksFirstWithModelGeneratedMessage = "assistant-speaks-first-with-model-generated-message",
  AssistantWaitsForUser = "assistant-waits-for-user",
  // Add other modes if applicable
}

// Enum for voice providers
export enum VoiceProvider {
  ElevenLabs = "11labs",
  // Add other providers if applicable
}

// Enum for voice IDs
export enum VoiceId {
  <PERSON> = "burt",
  <PERSON> = "marissa",
  <PERSON> = "and<PERSON>",
  <PERSON> = "sarah",
  <PERSON> = "phillip",
  <PERSON> = "steve",
  <PERSON> = "joseph",
  Myra = "myra",
  Paula = "paula",
  Ryan = "ryan",
  Drew = "drew",
  Paul = "paul",
  MrB = "mrb",
  Matilda = "matilda",
  Mark = "mark",
}

// Enum for background sounds
export enum BackgroundSound {
  Off = "off",
  Office = "office",
}

// Type for voice configuration
export type VoiceConfig = {
  fillerInjectionEnabled: boolean;
  provider: VoiceProvider;
  voiceId: VoiceId;
  stability: number; // 0 to 1
  similarityBoost: number; // 0 to 1
  style: number; // 0 to 1
  optimizeStreamingLatency: 1 | 2 | 3 | 4;
  useSpeakerBoost: boolean;
};

// Type for model configuration
export type ModelConfig = {
  model: OpenAIModel;
  provider: AIProvider;
};

// Main type for agent configuration
export type AgentConfig = {
  agentId: string;
  firstMessage: string;
  firstMessageMode: FirstMessageMode;
  model: ModelConfig;
  voice: VoiceConfig;
  backchannelingEnabled: boolean;
  backgroundDenoisingEnabled: boolean;
  silenceTimeoutSeconds: number;
  backgroundSound: BackgroundSound;
};
