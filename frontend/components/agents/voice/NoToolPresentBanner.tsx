import React from "react";
import { Paper, Typography, Box } from "@mui/material";
import InfoIcon from "@mui/icons-material/Info";

const NoToolPresentBanner = () => {
  return (
    <Paper
      elevation={3}
      sx={{
        p: 3,
        mt: 2,
        bgcolor: "info.light",
        color: "info.contrastText",
        display: "flex",
        alignItems: "center",
        borderRadius: 2,
      }}
    >
      <InfoIcon sx={{ fontSize: 40, mr: 2 }} />
      <Box>
        <Typography variant="h6" gutterBottom>
          No Tools Added
        </Typography>
        <Typography variant="body1">
          {`Your voice agent currently has no tools configured. To enhance its capabilities, 
                    please add tools in the agent configuration settings. Once added, they will 
                    automatically sync and appear here.`}
        </Typography>
      </Box>
    </Paper>
  );
};

export default NoToolPresentBanner;
