import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>alogA<PERSON>,
  <PERSON>alogContent,
  DialogTitle,
} from "@mui/material";
import { Settings } from "@mui/icons-material";
import VoiceConfigForm, {
  VoiceConfigFormData,
  defaultConfig,
} from "./VoiceConfigFormData";
import { configureVoice } from "@/requests/voice/configure";
import { useDispatch } from "react-redux";
import { agentsAction } from "@/slices/agents/agentsSlice";

interface IProp {
  agentId: string;
}

const VoiceConfigAdd: React.FC<IProp> = ({ agentId }) => {
  const dispatch = useDispatch();
  const [open, setOpen] = useState(false);
  const [config, setConfig] = useState<VoiceConfigFormData>(defaultConfig);

  const handleOpenDialog = () => setOpen(true);
  const handleCloseDialog = () => setOpen(false);

  const handleConfigChange = (newConfig: VoiceConfigFormData) => {
    setConfig(newConfig);
  };

  const onSave = async (config: VoiceConfigFormData) => {
    // Implement your save logic here
    console.log("Saving config:", config);
    const response = await configureVoice({ agentId, ...config }, dispatch);
    if (response.status === 201) {
      const data = response.data.data;
      dispatch(agentsAction.activateVoice(data));
    }
  };

  const handleSubmit = (event: React.FormEvent) => {
    event.preventDefault();
    onSave(config);
    handleCloseDialog();
  };

  return (
    <>
      <Button
        variant="contained"
        startIcon={<Settings />}
        onClick={handleOpenDialog}
      >
        Get started
      </Button>
      <Dialog open={open} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
        <form onSubmit={handleSubmit}>
          <DialogTitle>Configure Voice Settings</DialogTitle>
          <DialogContent>
            <VoiceConfigForm config={config} onChange={handleConfigChange} />
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseDialog}>Cancel</Button>
            <Button
              type="submit"
              variant="contained"
              disabled={
                config.firstMessageMode === "assistant-speaks-first" &&
                !config.firstMessage
              }
            >
              Save
            </Button>
          </DialogActions>
        </form>
      </Dialog>
    </>
  );
};

export default VoiceConfigAdd;
