import React from "react";
import { <PERSON><PERSON>, <PERSON>, Paper, Stack, Typography } from "@mui/material";
import VoicePlaygroundStat from "./VoicePlaygroundStat";

import { useVapi } from "@/hooks/useVapi";
import { AssistantButton } from "./AssistantButton";
import { useSelector } from "react-redux";
import { RootState } from "@/store";
import { MIN_BALANCE_FOR_VOICE } from "@/helpers/constants";

interface IProp {
  vapiAssistantId: string;
}

const VoicePlayground: React.FC<IProp> = ({ vapiAssistantId }) => {
  const {
    toggleCall,
    toolCallFunctions,
    callStatus,
    audioLevel,
    activeTranscript,
  } = useVapi(vapiAssistantId);
  const availableBalance = useSelector(
    (state: RootState) => state.organization.availableBalance
  );
  const requiresPayment = availableBalance < MIN_BALANCE_FOR_VOICE;
  return (
    <Paper sx={{ width: "40%", px: 2, py: 1, height: "fit-content" }}>
      <Stack direction={"row"} justifyContent={"space-between"}>
        <Typography variant="subtitle1">Playground</Typography>
      </Stack>
      {requiresPayment && (
        <Alert severity="warning" sx={{ mb: 2 }}>
          Your current balance is ${availableBalance.toFixed(2)}. Voice requires
          you to maintain a minimum of ${MIN_BALANCE_FOR_VOICE}. Go to{" "}
          <Link href="/home/<USER>">billing</Link>.
        </Alert>
      )}
      <Stack direction={"row"} justifyContent={"center"}>
        <AssistantButton
          audioLevel={audioLevel}
          callStatus={callStatus}
          toggleCall={toggleCall}
          isDisabled={requiresPayment}
        ></AssistantButton>
      </Stack>
      <VoicePlaygroundStat
        audioLevel={audioLevel}
        callStatus={callStatus}
        activeTranscript={activeTranscript}
        toolCallFunctions={toolCallFunctions}
      />
    </Paper>
  );
};

export default VoicePlayground;
