import { RootState } from "@/store";
import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import VoiceConfigNotSet from "./VoiceConfigNotSet";
import { Box, CircularProgress, Stack } from "@mui/material";
import VoiceConfigView from "./VoiceConfigView";
import VoicePlayground from "./VoicePlayground";
import VoiceTools from "./VoiceTools";
import { useRouter } from "next/router";
import { getVoiceConfig } from "@/requests/voice/getVoiceConfig";
import { agentsAction } from "@/slices/agents/agentsSlice";
import VoiceConnection from "./VoiceConnection";
import PhoneNumberConnection from "./PhoneNumberConnection";
import VoiceHistoryView from "./VoiceHistoryView";

const VoiceTab = () => {
  const dispatch = useDispatch();
  const router = useRouter();
  const { agentId } = router.query as { agentId: string };
  const [loading, setLoading] = useState(false);
  useEffect(() => {
    const fetchVoiceConfig = async () => {
      setLoading(true);
      const response = await getVoiceConfig({ agentId }, dispatch);
      setLoading(false);
      if (response?.status === 200) {
        const data = response?.data;
        dispatch(agentsAction.updateVoiceConfig(data));
      }
    };
    fetchVoiceConfig();
  }, []);
  const voiceConfig =
    useSelector((state: RootState) => state.agents.currentAgent.voiceConfig) ??
    null;
  if (loading) {
    return (
      <div className="flex justify-center mt-10">
        <CircularProgress />
      </div>
    );
  }
  if (!voiceConfig || Object.keys(voiceConfig).length === 0) {
    return <VoiceConfigNotSet />;
  }
  return (
    <Box>
      <Stack direction={"row"} mt={2} gap={2}>
        <Box sx={{ width: "60%" }}>
          <Stack spacing={2}>
            <VoiceConfigView />
            <VoiceConnection />
            <PhoneNumberConnection />
            <VoiceTools tools={voiceConfig.tools} />
          </Stack>
        </Box>
        <VoicePlayground vapiAssistantId={voiceConfig.assistantId} />
      </Stack>
      <VoiceHistoryView agentId={agentId} />
    </Box>
  );
};

export default VoiceTab;
