import React, { useState } from "react";
import {
  Avatar,
  CircularProgress,
  IconButton,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Paper,
  Stack,
  Typography,
} from "@mui/material";
import { Edit } from "@mui/icons-material";
import { logos } from "@/helpers/images";
import NoChannelConfigured from "./NoChannelConfigured";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store";
import AddConnectionDialog from "./AddConnectionDialog"; // Make sure to import this component
import { IVoiceConfig, agentsAction } from "@/slices/agents/agentsSlice";
import { setChannel } from "@/requests/voice/setChannel";
import { useRouter } from "next/router";

const VoiceConnection: React.FC = () => {
  const dispatch = useDispatch();
  const router = useRouter();
  const { agentId } = router.query as { agentId: string };
  const channelConnected = useSelector(
    (state: RootState) => state.agents.currentAgent.voiceConfig?.channel
  );
  const isChannelConfigured = Boolean(channelConnected);

  const orgId = localStorage.getItem("orgId") as string;

  // Add state for managing the dialog
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  // Handler for opening the dialog
  const handleOpenDialog = () => {
    setIsDialogOpen(true);
  };

  // Handler for closing the dialog
  const handleCloseDialog = () => {
    setIsDialogOpen(false);
  };

  // Handler for saving changes in the dialog
  const handleSave = async (account: IVoiceConfig["channel"]) => {
    const response = await setChannel(
      { orgId, accountId: account?.accountId as string, agentId },
      dispatch
    );
    if (response.status === 200) {
      dispatch(agentsAction.updateConnectedVoiceChannel(account));
    }
  };

  console.log({ channelConnected });

  return (
    <div className="p-4 bg-gray-100 rounded-lg shadow">
      <h2 className="text-2xl font-bold mb-4">Channel Connection</h2>
      {isChannelConfigured && channelConnected ? (
        <List>
          <Paper
            elevation={3}
            sx={{
              mb: 2,
              backgroundColor: "white",
              transition: "background-color 0.3s ease",
            }}
          >
            <ListItem
              secondaryAction={
                <IconButton
                  edge="end"
                  aria-label="edit"
                  onClick={handleOpenDialog} // Changed to use the new handler
                >
                  <Edit />
                </IconButton>
              }
            >
              <ListItemAvatar>
                <Avatar
                  src={`/squareLogos${
                    logos[channelConnected.providerName]?.logo || ""
                  }`}
                  alt={channelConnected.providerName}
                />
              </ListItemAvatar>
              <ListItemText
                primary={channelConnected.name}
                secondary={
                  <Stack direction="row" gap={1} alignItems="center">
                    <Typography
                      component="span"
                      variant="body2"
                      color="text.secondary"
                    >
                      {logos[channelConnected.providerName]?.name || "No name"}
                    </Typography>
                  </Stack>
                }
              />
            </ListItem>
          </Paper>
        </List>
      ) : (
        <NoChannelConfigured />
      )}

      {/* Add the AddConnectionDialog component */}
      <AddConnectionDialog
        open={isDialogOpen}
        onClose={handleCloseDialog}
        onSubmit={handleSave}
        channel={channelConnected}
      />
    </div>
  );
};

export default VoiceConnection;
