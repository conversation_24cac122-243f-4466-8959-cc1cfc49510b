import React from "react";
import { capitalizeFirstLetter } from "@/helpers/basic";
import {
  CALL_STATUS,
  TranscriptMessage,
} from "@/lib/types/vapi-conversation.type";
import { Chip, Stack, Typography, Box } from "@mui/material";
import { ToolCallFunction } from "@vapi-ai/web/dist/api";
import VolumeBar from "./VolumeBar";

interface IStatInput {
  propertyName: string;
  value: React.ReactNode;
}

const StatInput: React.FC<IStatInput> = ({ propertyName, value }) => {
  return (
    <Stack direction="row" justifyContent="space-between" alignItems="center">
      <Typography variant="body2" color="text.secondary" minWidth={100}>
        {propertyName}
      </Typography>
      <Box flexGrow={1} maxWidth="calc(100% - 120px)">
        {React.isValidElement(value) ? (
          value
        ) : (
          <Typography variant="body2">{value}</Typography>
        )}
      </Box>
    </Stack>
  );
};

interface IProp {
  callStatus: CALL_STATUS;
  audioLevel: number;
  activeTranscript: TranscriptMessage | null;
  toolCallFunctions: ToolCallFunction[];
}

const VoicePlaygroundStat: React.FC<IProp> = ({
  audioLevel,
  callStatus,
  activeTranscript,
  toolCallFunctions,
}) => {
  return (
    <Stack direction="column" gap={1}>
      <StatInput
        propertyName="Status"
        value={capitalizeFirstLetter(callStatus)}
      />
      <StatInput
        propertyName="Speaker"
        value={activeTranscript?.role ?? "None"}
      />
      <StatInput
        propertyName="Volume"
        value={<VolumeBar level={audioLevel} />}
      />
      <StatInput
        propertyName="Transcript"
        value={activeTranscript?.transcript ?? "..."}
      />
      <StatInput
        propertyName="Tool Calls"
        value={<code>{JSON.stringify(toolCallFunctions)}</code> ?? "[]"}
      />
    </Stack>
  );
};

export default VoicePlaygroundStat;
