import React, { useState } from "react";
import {
  Text<PERSON>ield,
  Button,
  Box,
  IconButton,
  Typo<PERSON>,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  Paper,
} from "@mui/material";
import {
  Delete as DeleteIcon,
  Add as AddIcon,
  Save as SaveIcon,
} from "@mui/icons-material";
import { useSelector, useDispatch } from "react-redux";
import { RootState } from "@/store";
import { updateFaqs } from "@/requests/agents/faqs/updateFaqs";
import { useRouter } from "next/router";
import EmptyFAQCard from "../agent-builder/EmptyFAQCard";

interface FAQ {
  id: string;
  question: string;
  answer: string;
}

const FaqTab: React.FC = () => {
  const router = useRouter();
  const { agentId } = router.query as { agentId: string };
  const initialFaqs =
    useSelector((state: RootState) => state.agents.currentAgent.faqs) || [];
  const [loading, setLoading] = useState(false);
  const [faqs, setFaqs] = useState<FAQ[]>(initialFaqs);
  const [unsavedChanges, setUnsavedChanges] = useState(false);
  const [openConfirmDialog, setOpenConfirmDialog] = useState(false);
  const [faqToDelete, setFaqToDelete] = useState<string | null>(null);
  const dispatch = useDispatch();

  const handleFaqChange = (
    id: string,
    field: "question" | "answer",
    value: string
  ) => {
    setFaqs((prevFaqs) =>
      prevFaqs.map((faq) => (faq.id === id ? { ...faq, [field]: value } : faq))
    );
    setUnsavedChanges(true);
  };

  const addFaq = () => {
    const newFaq: FAQ = {
      id: Math.random().toString(36).substring(7),
      question: "",
      answer: "",
    };
    setFaqs((prevFaqs) => [...prevFaqs, newFaq]);
    setUnsavedChanges(true);
  };

  const confirmDelete = (id: string) => {
    setFaqToDelete(id);
    setOpenConfirmDialog(true);
  };

  const deleteFaq = () => {
    if (faqToDelete) {
      setFaqs((prevFaqs) => prevFaqs.filter((faq) => faq.id !== faqToDelete));
      setUnsavedChanges(true);
      setOpenConfirmDialog(false);
      setFaqToDelete(null);
    }
  };

  const handleSave = async () => {
    // Here you would typically dispatch an action to update the store/backend
    // dispatch(updateFaqs(faqs));
    setLoading(true);
    const response = await updateFaqs({ agentId, faqs }, dispatch);
    if (response.status === 200) {
    }
    setLoading(false);
    setUnsavedChanges(false);
  };

  return (
    <Box mt={2}>
      <div className="p-4 bg-gray-100 rounded-lg shadow">
        <h2 className="text-2xl font-bold mb-4">FAQs</h2>
        <Box>
          {unsavedChanges && (
            <Alert severity="warning" sx={{ mb: 2 }}>
              {"You have unsaved changes. Don't forget to save!"}
            </Alert>
          )}
          {!faqs?.length && <EmptyFAQCard onAddFaq={addFaq} />}
          {faqs.map((faq) => (
            <Paper key={faq.id} elevation={2} sx={{ mb: 3, p: 2 }}>
              <Box sx={{ display: "flex", alignItems: "flex-start", gap: 2 }}>
                <Box sx={{ flexGrow: 1 }}>
                  <TextField
                    fullWidth
                    label="Question"
                    value={faq.question}
                    onChange={(e) =>
                      handleFaqChange(faq.id, "question", e.target.value)
                    }
                    margin="normal"
                    variant="outlined"
                  />
                  <TextField
                    fullWidth
                    label="Answer"
                    value={faq.answer}
                    onChange={(e) =>
                      handleFaqChange(faq.id, "answer", e.target.value)
                    }
                    margin="normal"
                    variant="outlined"
                    multiline
                    rows={3}
                  />
                </Box>
                <IconButton
                  color="error"
                  onClick={() => confirmDelete(faq.id)}
                  sx={{ mt: 2 }}
                >
                  <DeleteIcon />
                </IconButton>
              </Box>
            </Paper>
          ))}

          <Box sx={{ display: "flex", gap: 2, mt: 3 }}>
            <Button variant="outlined" startIcon={<AddIcon />} onClick={addFaq}>
              Add FAQ
            </Button>
            <Button
              variant="contained"
              color="primary"
              startIcon={<SaveIcon />}
              onClick={handleSave}
              disabled={!unsavedChanges || loading}
            >
              {loading ? "Saving changes..." : "Save Changes"}
            </Button>
          </Box>

          <Dialog
            open={openConfirmDialog}
            onClose={() => setOpenConfirmDialog(false)}
          >
            <DialogTitle>Confirm Delete</DialogTitle>
            <DialogContent>
              <Typography>
                Are you sure you want to delete this FAQ? This action cannot be
                undone.
              </Typography>
            </DialogContent>
            <DialogActions>
              <Button onClick={() => setOpenConfirmDialog(false)}>
                Cancel
              </Button>
              <Button onClick={deleteFaq} color="error" variant="contained">
                Delete
              </Button>
            </DialogActions>
          </Dialog>
        </Box>
      </div>
    </Box>
  );
};

export default FaqTab;
