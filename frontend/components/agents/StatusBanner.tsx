import React from "react";
import { Paper, Typography, Button, Stack } from "@mui/material";
import { styled } from "@mui/material/styles";
import { PowerSettingsNew as PowerIcon } from "@mui/icons-material";

interface StatusBannerProps {
  isConnected: boolean;
  onConnect: () => void;
  onBannerDismiss: () => void;
}

const StatusBanner: React.FC<StatusBannerProps> = ({
  isConnected,
  onConnect,
  onBannerDismiss
}) => {
  return (
    <BannerContainer elevation={0}>
      <StatusIndicator>
        <StatusDot $isConnected={isConnected} />
        <Typography
          variant="h6"
          sx={{
            color: "text.primary",
            fontSize: "1rem",
            fontWeight: 500,
          }}
        >
          {isConnected ? "Chatbot Connected" : "Chatbot Not Connected to CRM"}
        </Typography>
      </StatusIndicator>
      <Stack direction={"row"} gap={2}>
        <ConnectButton
          variant="contained"
          onClick={onConnect}
          disabled={isConnected}
          startIcon={<PowerIcon />}
          $isConnected={isConnected}
        >
          {isConnected ? "Connected" : "Connect"}
        </ConnectButton>
        <Button variant="text" onClick={onBannerDismiss}>Dismiss</Button>
      </Stack>
    </BannerContainer>
  );
};

const BannerContainer = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(2),
  background: "gba(255, 255, 255, 0.05)",
  backdropFilter: "blur(10px)",
  //   marginBottom: theme.spacing(3),
  display: "flex",
  justifyContent: "space-between",
  alignItems: "center",
  borderRadius: theme.spacing(1),
  border: `1px solid ${
    theme.palette.mode === "dark"
      ? "rgba(255, 255, 255, 0.1)"
      : "rgba(0, 0, 0, 0.1)"
  }`,
}));

const StatusIndicator = styled("div")({
  display: "flex",
  alignItems: "center",
  gap: "12px",
});

const StatusDot = styled("div")<{ $isConnected: boolean }>(
  ({ $isConnected, theme }) => ({
    width: 10,
    height: 10,
    borderRadius: "50%",
    backgroundColor: $isConnected
      ? theme.palette.success.main
      : theme.palette.error.main,
    boxShadow: `0 0 10px ${
      $isConnected ? theme.palette.success.main : theme.palette.error.main
    }`,
  })
);

const ConnectButton = styled(Button)<{ $isConnected: boolean }>(
  ({ $isConnected, theme }) => ({
    textTransform: "none",
    backgroundColor: $isConnected
      ? theme.palette.success.dark
      : theme.palette.primary.main,
    "&:hover": {
      backgroundColor: $isConnected
        ? theme.palette.success.main
        : theme.palette.primary.dark,
    },
    "&.Mui-disabled": {
      backgroundColor: theme.palette.success.dark,
      color: theme.palette.common.white,
    },
  })
);

export default StatusBanner;
