import React, { FormEvent, useState } from "react";
import { GrAddCircle } from "react-icons/gr";
import { NextRouter, useRouter } from "next/router";
import Link from "next/link";
import { useDispatch, useSelector } from "react-redux";
import { agentsAction } from "@/slices/agents/agentsSlice";
import { generateUniqueId } from "@/helpers/generateId";
import {
  createAgent,
  createAgentExtract,
} from "@/requests/agents/basic/createAgent";
import { AnyAction, Dispatch } from "redux";
import { agentsUrl } from "@/slices/subOptions/subOptionSlice";
import {
  Box,
  Button,
  Checkbox,
  Chip,
  Divider,
  FormControl,
  FormControlLabel,
  FormGroup,
  Grid,
  InputLabel,
  MenuItem,
  Select,
  SelectChangeEvent,
  Stack,
  TextField,
  Typography,
} from "@mui/material";
import GeneralModal from "@/components/general/modal";
import ButtonSpinner from "@/components/general/buttons/spinner";
import { grey } from "@mui/material/colors";
import { FaWandMagicSparkles } from "react-icons/fa6";
import { BiImport } from "react-icons/bi";
import { RootState } from "@/store";

const body = {
  agentName: "",
  // status: "draft",
  disabled: true,
  aiProvider: {
    providerName: "",
    credentialId: "",
    accountId: "",
    accountName: "",
  },
  prompts: {
    currentActive: "",
    prompt: [],
  },
  actions: [],
};

export const addAgentLogic = async (
  router: NextRouter,
  dispatch: Dispatch<AnyAction>
): Promise<void> => {
  const response = await createAgent(
    {
      ...body,
      orgId: localStorage.getItem("orgId") || "",
    },
    dispatch
  );

  if (response?.status === 201) {
    await router.push(agentsUrl + "/" + response.data.data._id);
    dispatch(agentsAction.newAgentsAdded(response.data.data));
  }
};

const AddAgentCard = () => {
  const dispatch = useDispatch();
  const router = useRouter();
  const agentList = useSelector((state: RootState) => state.agents.agentsList);
  const parentPath = router.asPath;
  const [loading, setLoading] = useState(false);
  const [open, setOpen] = useState(false);
  const [name, setName] = useState("");
  const [importType, setImportType] = useState<"" | "exisitingAgent" | "ai">(
    ""
  );
  const [selectedAgent, setSelectedAgent] = useState("");
  const [agentImports, setAgentImports] = useState<{
    aiProvider: Boolean;
    prompts: Boolean;
    dataSources: Boolean;
    savedSessions: Boolean;
  }>({
    aiProvider: false,
    prompts: false,
    dataSources: false,
    savedSessions: false,
  });
  const handleAgentChange = (event: SelectChangeEvent) => {
    setSelectedAgent(event.target.value);
  };

  const handleClose = async () => {
    setOpen(false);
  };

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    // handleAddAgent();
    let newBody = {
      ...body,
      agentName: name,
      orgId: localStorage.getItem("orgId") || "",
    };
    let payload = {};
    if (importType === "") {
      payload = {
        ...newBody,
      };
    } else if (importType === "exisitingAgent") {
      // payload = {
      //   ...newBody,
      //   exisitingAgentData,
      // };
      payload = {
        agentId: selectedAgent,
        agentName: name,
        agentImports,
      };
    }
    console.log({ payload, bool: "exisitingAgentData" in payload });
    setLoading(true);
    if (importType === "") {
      const response = await createAgent(payload, dispatch);
      if (response?.status === 201) {
        await router.push(agentsUrl + "/" + response.data.data._id);
        dispatch(agentsAction.newAgentsAdded(response.data.data));
      }
    } else {
      const response = await createAgentExtract(payload, dispatch);
      if (response?.status === 200) {
        await router.push(agentsUrl + "/" + response.data.data._id);
        dispatch(agentsAction.newAgentsAdded(response.data.data));
      }
    }
    setLoading(false);
  };

  return (
    <>
      <GeneralModal open={open} onClose={handleClose}>
        <FormControl
          fullWidth
          sx={{
            bgcolor: "white",
            paddingTop: 2,
            borderRadius: 2,
            color: grey[800],
            width: "500px",
          }}
        >
          <Typography variant="h4" gutterBottom paddingX={2}>
            Create an agent
          </Typography>
          <Divider />
          <form action="" onSubmit={handleSubmit}>
            <Stack direction={"column"} gap={2} paddingX={2} paddingTop={2}>
              <TextField
                id="outlined-agentName"
                label="Agent name"
                variant="outlined"
                type="text"
                value={name}
                helperText="This will be the name of your agent. Agents are the brain of your workflow."
                onChange={(e) => setName(e.target.value)}
                required
                fullWidth
              />
              <Stack direction={"row"} gap={2} sx={{ display: "" }}>
                <Button
                  variant={
                    importType === "exisitingAgent" ? "contained" : "outlined"
                  }
                  startIcon={<BiImport />}
                  size="small"
                  sx={{
                    textTransform: "none",
                  }}
                  onClick={() => setImportType("exisitingAgent")}
                >
                  Import from exisiting agent
                </Button>
                <Button
                  variant={importType === "ai" ? "contained" : "outlined"}
                  startIcon={<FaWandMagicSparkles />}
                  size="small"
                  sx={{ textTransform: "none", display: "none" }}
                  onClick={() => setImportType("ai")}
                  disabled
                >
                  Generate with AI
                </Button>
                {importType !== "" && (
                  <Chip
                    variant="filled"
                    color="error"
                    label="Clear"
                    clickable
                    onClick={() => setImportType("")}
                  />
                )}
              </Stack>
              {importType === "exisitingAgent" && (
                <>
                  <Stack gap={1}>
                    <FormControl fullWidth>
                      <InputLabel id="demo-simple-select-label">
                        Agent
                      </InputLabel>
                      <Select
                        labelId="demo-simple-select-label"
                        id="demo-simple-select"
                        value={selectedAgent}
                        label="Age"
                        required
                        onChange={handleAgentChange}
                      >
                        {agentList.map((item) => (
                          <MenuItem value={item._id} key={"Option_" + item._id}>
                            {item.agentName || `No name - ${item._id}`}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                    <FormGroup>
                      <Typography variant="inherit" fontWeight={"400"}>
                        Choose Scope
                      </Typography>
                      <FormControlLabel
                        control={
                          <Checkbox
                            onChange={() => {
                              setAgentImports({
                                ...agentImports,
                                aiProvider: !agentImports.aiProvider,
                              });
                            }}
                          />
                        }
                        label="AI Provider"
                      />
                      <FormControlLabel
                        control={
                          <Checkbox
                            onChange={() => {
                              setAgentImports({
                                ...agentImports,
                                prompts: !agentImports.prompts,
                              });
                            }}
                          />
                        }
                        label="Prompts"
                      />
                      <FormControlLabel
                        control={
                          <Checkbox
                            onChange={() => {
                              setAgentImports({
                                ...agentImports,
                                dataSources: !agentImports.dataSources,
                              });
                            }}
                          />
                        }
                        label="Data sources"
                      />
                      <FormControlLabel
                        control={
                          <Checkbox
                            onChange={() => {
                              setAgentImports({
                                ...agentImports,
                                savedSessions: !agentImports.savedSessions,
                              });
                            }}
                          />
                        }
                        label="Saved sessions"
                      />
                    </FormGroup>
                  </Stack>
                </>
              )}
              <Stack direction={"row"} justifyContent={"end"} paddingY={1}>
                <Button variant="contained" type="submit" disabled={loading}>
                  Create {loading && <ButtonSpinner />}
                </Button>
              </Stack>
            </Stack>
          </form>
        </FormControl>
      </GeneralModal>
      <Grid item sm={12} md={6} lg={4}>
        <div
          className={`h-[210px] border-[1px] border-slate-600 bg-gradient-to-tr from-slate-300 to-slate-400 rounded-2xl text-center flex items-center justify-center cursor-pointer hover:scale-[1.05] transition-all hover:bg-gradient-to-tl ${
            loading ? "opacity-50 pointer-events-none" : ""
          }`}
          // onClick={loading ? undefined : handleAddAgent}
          onClick={() => {
            setOpen(true);
          }}
        >
          <div>
            <div>
              <GrAddCircle className="text-[50px] mx-auto text-gray-800" />
            </div>
            <div className="text-[24px] text-gray-800 font-medium">Add new</div>
          </div>
        </div>
      </Grid>
    </>
  );
};

export default AddAgentCard;
