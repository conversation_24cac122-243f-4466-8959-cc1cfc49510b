import React from "react";
import {
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Checkbox,
  Paper,
  Typography,
  Button,
  Stack,
  Box,
} from "@mui/material";
import CheckBoxOutlineBlankIcon from "@mui/icons-material/CheckBoxOutlineBlank";
import CheckBoxIcon from "@mui/icons-material/CheckBox";

interface LinksListProps {
  links: Array<{ url: string; selected: boolean }>;
  onLinkSelect: (index: number) => void;
  onSelectAll: () => void;
  onUnselectAll: () => void;
  selectedCount: number;
}

export const LinksList: React.FC<LinksListProps> = ({
  links,
  onLinkSelect,
  onSelectAll,
  onUnselectAll,
  selectedCount,
}) => (
  <>
    <Box
      sx={{
        display: "flex",
        justifyContent: "space-between",
        alignItems: "center",
      }}
    >
      <Typography variant="subtitle1">
        {`${selectedCount} links selected`}
      </Typography>
      <Stack direction="row" spacing={2}>
        <Button
          variant="outlined"
          onClick={onSelectAll}
          disabled={links.length > 200}
        >
          Select All
        </Button>
        <Button variant="outlined" onClick={onUnselectAll}>
          Unselect All
        </Button>
      </Stack>
    </Box>

    <Paper sx={{ maxHeight: 400, overflow: "auto" }}>
      <List dense>
        {links.map((link, index) => (
          <ListItem
            key={index}
            onClick={() => onLinkSelect(index)}
            sx={{ cursor: "pointer" }}
          >
            <ListItemIcon>
              <Checkbox
                icon={<CheckBoxOutlineBlankIcon />}
                checkedIcon={<CheckBoxIcon />}
                checked={link.selected}
              />
            </ListItemIcon>
            <ListItemText primary={link.url} sx={{ wordBreak: "break-all" }} />
          </ListItem>
        ))}
      </List>
    </Paper>
  </>
);
