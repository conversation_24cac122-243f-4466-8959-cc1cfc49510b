import React, { useState } from "react";
import { Box, Typography, IconButton, Chip, Tooltip } from "@mui/material";
import DeleteIcon from "@mui/icons-material/Delete";
import InsertDriveFileIcon from "@mui/icons-material/InsertDriveFile";
import PictureAsPdfIcon from "@mui/icons-material/PictureAsPdf";
import DescriptionIcon from "@mui/icons-material/Description";
import TableChartIcon from "@mui/icons-material/TableChart";
import { styled } from "@mui/material/styles";
import { ACCEPTED_FILE_TYPES } from "./Dropzone";
import ContentCopyIcon from "@mui/icons-material/ContentCopy";
import RemoveKnowledgeConfirmationDialog from "./RemoveKnowledgeDialog";
import { snackbarActions } from "@/slices/general/snackbar";
import { useDispatch } from "react-redux";
import LanguageIcon from "@mui/icons-material/Language";
import { JobTypeExplicit } from "@/slices/agents/agentsSlice";

type AcceptedMimeTypes = keyof typeof ACCEPTED_FILE_TYPES;

interface FileStatusProps {
  accountId: string;
  fileName: string;
  jobType: JobTypeExplicit;
  status: "processing" | "processed";
  onDelete: () => void;
  characterCount?: number;
  mimeType: AcceptedMimeTypes;
}

const StyledFileBox = styled(Box)(({ theme }) => ({
  display: "flex",
  alignItems: "center",
  padding: theme.spacing(2),
  borderRadius: theme.shape.borderRadius,
  backgroundColor: theme.palette.background.paper,
  boxShadow: theme.shadows[1],
  "&:hover": {
    boxShadow: theme.shadows[3],
  },
}));

const FileTypeChip = styled(Chip)(({ theme }) => ({
  marginLeft: theme.spacing(1),
  backgroundColor: theme.palette.primary.light,
  color: theme.palette.primary.contrastText,
  fontWeight: "bold",
  "& .MuiChip-icon": {
    color: theme.palette.primary.contrastText,
  },
}));

export const getFileTypeInfo = (mimeType: AcceptedMimeTypes) => {
  switch (mimeType) {
    case "application/pdf":
      return { label: "PDF", icon: <PictureAsPdfIcon /> };
    case "text/plain":
      return { label: "TXT", icon: <DescriptionIcon /> };
    case "application/msword":
    case "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
      return { label: "DOC", icon: <DescriptionIcon /> };
    case "application/vnd.ms-excel":
    case "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":
      return { label: "XLS", icon: <TableChartIcon /> };
    default:
      return { label: "File", icon: <InsertDriveFileIcon /> };
  }
};

const getResourceInfo = (jobType: JobTypeExplicit) => {
  switch (jobType) {
    case "file-upload":
      return { label: "File upload", icon: <InsertDriveFileIcon /> };
    case "website-crawl":
      return { label: "Website crawl", icon: <LanguageIcon /> };
    case "website-scrap":
      return { label: "Website scrap", icon: <LanguageIcon /> };
    default:
      return { label: "File upload", icon: <InsertDriveFileIcon /> };
  }
};

const FileStatus: React.FC<FileStatusProps> = ({
  accountId,
  fileName,
  jobType,
  status,
  onDelete,
  characterCount,
  mimeType,
}) => {
  const dispatch = useDispatch();
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const fileTypeInfo = getFileTypeInfo(mimeType);
  const jobTypeInfo = getResourceInfo(jobType);

  const handleDeleteClick = () => {
    setIsDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = () => {
    onDelete();
    setIsDeleteDialogOpen(false);
  };

  const handleDeleteCancel = () => {
    setIsDeleteDialogOpen(false);
  };

  const handleActionIdCopy = async () => {
    await navigator.clipboard.writeText(accountId);
    dispatch(
      snackbarActions.setSnackbar({
        message: "Knowledge source ID copied",
        type: "success",
      })
    );
  };

  return (
    <>
      <StyledFileBox>
        <Box display="flex" alignItems="center" flexGrow={1}>
          {React.cloneElement(jobTypeInfo.icon, {
            color: "primary",
            sx: { mr: 2 },
          })}
          <Box>
            <Typography variant="subtitle1" component="div">
              {fileName}
            </Typography>
            <Box display="flex" alignItems="center" gap={1} mt={0.5}>
              {mimeType && (
                <FileTypeChip
                  icon={fileTypeInfo.icon}
                  label={fileTypeInfo.label}
                  size="small"
                />
              )}
              {status === "processing" && (
                <Chip label={status} color="warning" size="small" />
              )}
              {status === "processed" && characterCount !== undefined && (
                <Typography
                  variant="body2"
                  color="text.secondary"
                  sx={{ ml: 1 }}
                >
                  {characterCount} characters
                </Typography>
              )}
            </Box>
          </Box>
        </Box>
        <Tooltip title="Knowledge source ID">
          <IconButton
            onClick={handleActionIdCopy}
            size="small"
            aria-label="copy"
          >
            <ContentCopyIcon />
          </IconButton>
        </Tooltip>
        <Tooltip title="Delete">
          <IconButton
            onClick={handleDeleteClick}
            size="small"
            aria-label="delete"
          >
            <DeleteIcon />
          </IconButton>
        </Tooltip>
      </StyledFileBox>
      <RemoveKnowledgeConfirmationDialog
        open={isDeleteDialogOpen}
        onClose={handleDeleteCancel}
        onConfirm={handleDeleteConfirm}
        fileName={fileName}
      />
    </>
  );
};

export default FileStatus;
