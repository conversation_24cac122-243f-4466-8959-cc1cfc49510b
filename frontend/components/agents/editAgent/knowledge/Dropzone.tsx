// components/Dropzone.tsx
import React, { useCallback, useState } from "react";
import { useDropzone, Accept } from "react-dropzone";
import {
  Box,
  Typography,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Button,
  CircularProgress,
  Theme,
} from "@mui/material";
import {
  CloudUpload as CloudUploadIcon,
  Delete as DeleteIcon,
} from "@mui/icons-material";
import { useDispatch } from "react-redux";
import { snackbarActions } from "@/slices/general/snackbar";
import { agentsAction } from "@/slices/agents/agentsSlice";

export const ACCEPTED_FILE_TYPES: Accept = {
  "application/pdf": [".pdf"],
  "text/plain": [".txt"],
  "application/msword": [".doc"],
  "application/vnd.openxmlformats-officedocument.wordprocessingml.document": [
    ".docx",
  ],
  "application/vnd.ms-excel": [".xls"],
  "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": [
    ".xlsx",
  ],
};
const MAX_FILES = 5;
const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB in bytes

interface FileWithPreview extends File {
  preview: string;
}

interface IProp {
  agentId: string;
  onFileUpload: () => void;
}

const Dropzone: React.FC<IProp> = ({ agentId, onFileUpload }) => {
  const dispatch = useDispatch();
  const [files, setFiles] = useState<FileWithPreview[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const onDrop = useCallback((acceptedFiles: File[]) => {
    const newFiles = acceptedFiles.map((file) =>
      Object.assign(file, { preview: URL.createObjectURL(file) })
    );
    console.log({newFiles});
    
    setFiles((prevFiles) => [...prevFiles, ...newFiles].slice(0, MAX_FILES));
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: ACCEPTED_FILE_TYPES,
    maxFiles: MAX_FILES,
    maxSize: MAX_FILE_SIZE,
  });

  const removeFile = (file: FileWithPreview) => {
    setFiles((prevFiles) => prevFiles.filter((f) => f !== file));
    URL.revokeObjectURL(file.preview);
  };

  React.useEffect(() => {
    return () => files.forEach((file) => URL.revokeObjectURL(file.preview));
  }, [files]);

  const handleSubmit = async () => {
    setIsSubmitting(true);
    const formData = new FormData();
    files.forEach((file) => {
      formData.append("files", file);
    });

    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_BACKEND_URL}/agent/${agentId}/file-upload`,
        {
          method: "POST",
          body: formData,
          credentials: "include",
        }
      );

      if (!response.ok) {
        throw new Error("File processing failed");
      }

      const result = await response.json();
      dispatch(
        snackbarActions.setSnackbar({
          message: result.message,
          type: "success",
        })
      );
      onFileUpload();
      console.log("Processing jobs are ", result?.processingJobs);

      dispatch(
        agentsAction.fileProcessingJobAdded(result?.processingJobs ?? [])
      );
      setFiles([]);
      // You can add further actions here, such as showing a success message
    } catch (error) {
      console.error("Error processing files:", error);
      // You can add error handling here, such as showing an error message
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Box>
      <Paper
        {...getRootProps()}
        sx={{
          p: 2,
          mb: 2,
          textAlign: "center",
          cursor: "pointer",
          backgroundColor: (theme: Theme) =>
            isDragActive
              ? theme.palette.action.hover
              : theme.palette.background.paper,
        }}
      >
        <input {...getInputProps()} />
        <CloudUploadIcon fontSize="large" color="primary" />
        <Typography variant="h6" component="div" gutterBottom>
          {isDragActive
            ? "Drop the files here"
            : "Drag & drop files here, or click to select files"}
        </Typography>
        <Typography variant="body2" color="textSecondary">
          Allowed files: PDF, TXT, DOC, DOCX, XLS, XLSX (Max 5 files, 10MB each)
        </Typography>
      </Paper>
      {files.length > 0 && (
        <>
          <List>
            {files.map((file) => (
              <ListItem key={file.name}>
                <ListItemText
                  primary={file.name}
                  secondary={`${(file.size / 1024 / 1024).toFixed(2)} MB`}
                />
                <ListItemSecondaryAction>
                  <IconButton
                    edge="end"
                    aria-label="delete"
                    onClick={() => removeFile(file)}
                  >
                    <DeleteIcon />
                  </IconButton>
                </ListItemSecondaryAction>
              </ListItem>
            ))}
          </List>
          <Button
            variant="contained"
            color="primary"
            onClick={handleSubmit}
            disabled={isSubmitting}
            startIcon={isSubmitting ? <CircularProgress size={20} /> : null}
          >
            {isSubmitting ? "Processing..." : "Submit Files"}
          </Button>
        </>
      )}
    </Box>
  );
};

export default Dropzone;
