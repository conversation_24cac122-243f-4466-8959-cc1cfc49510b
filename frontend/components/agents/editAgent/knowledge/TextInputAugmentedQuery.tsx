import { addTextContent } from "@/requests/agents/knowledgeSources/addTextContent";
import { agentsAction } from "@/slices/agents/agentsSlice";
import { RootState } from "@/store";
import { 
  Box, 
  Button, 
  CircularProgress, 
  Stack, 
  TextField, 
  Typography,
  FormHelperText
} from "@mui/material";
import React, { ChangeEvent, useState } from "react";
import { useDispatch, useSelector } from "react-redux";

interface IProp {
  agentId: string;
  onSuccess: () => void;
}

const CHARACTER_LIMIT = 25000;

const TextInputAugmentedQuery: React.FC<IProp> = ({ agentId, onSuccess }) => {
  const dispatch = useDispatch();
  const { textContentKnowledgeSource } = useSelector(
    (state: RootState) => state.agents.currentAgent
  );
  const [loading, setLoading] = useState(false);
  const [textContent, setTextContent] = useState(
    textContentKnowledgeSource?.textContent || ""
  );

  const isChangesMade = textContent !== textContentKnowledgeSource?.textContent;
  const charactersRemaining = CHARACTER_LIMIT - textContent.length;
  const isOverLimit = charactersRemaining < 0;

  const handleTextContentChange = (e: ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    if (newValue.length <= CHARACTER_LIMIT) {
      setTextContent(newValue);
    }
  };

  const handleSave = async () => {
    if (isOverLimit) return;
    
    setLoading(true);
    const response = await addTextContent({ agentId, textContent }, dispatch);
    setLoading(false);
    if (response.status === 201) {
      const data = response.data;
      dispatch(agentsAction.upsertTextContentKnowledgeSource(data));
      onSuccess();
    }
  };

  return (
    <Box>
      <TextField
        placeholder="Enter text content"
        multiline
        minRows={4}
        maxRows={24}
        value={textContent}
        onChange={handleTextContentChange}
        fullWidth
        error={isOverLimit}
        helperText={
          <Stack 
            direction="row" 
            justifyContent="space-between" 
            alignItems="center"
            width="100%"
          >
            <FormHelperText error={isOverLimit}>
              {isOverLimit 
                ? "Text exceeds maximum length"
                : ""}
            </FormHelperText>
            <Typography
              variant="caption"
              color={isOverLimit ? "error" : "textSecondary"}
            >
              {textContent.length} / {CHARACTER_LIMIT}
            </Typography>
          </Stack>
        }
      />
      <Stack direction="row" justifyContent="end" spacing={2} sx={{ mt: 2 }}>
        <Button
          variant="contained"
          disabled={loading || !isChangesMade || isOverLimit}
          onClick={handleSave}
        >
          {loading && <CircularProgress size={18} sx={{ mr: 1 }} />}
          Save
        </Button>
      </Stack>
    </Box>
  );
};

export default TextInputAugmentedQuery;