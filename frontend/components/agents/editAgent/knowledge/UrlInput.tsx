import React from "react";
import {
  <PERSON><PERSON><PERSON>,
  Button,
  Stack,
  CircularProgress,
  Box,
  Paper,
} from "@mui/material";
import LinkIcon from "@mui/icons-material/Link";

interface UrlInputProps {
  url: string;
  onUrlChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  onFetchLinks: () => void;
  isLoading: boolean;
  error: string | null;
  hasLinks: boolean;
}

export const UrlInput: React.FC<UrlInputProps> = ({
  url,
  onUrlChange,
  onFetchLinks,
  isLoading,
  error,
  hasLinks,
}) => (
  <Box>
    <Stack
      spacing={2}
      direction={{ xs: "column", sm: "row" }}
      alignItems="center"
    >
      <TextField
        fullWidth
        label="Enter website URL"
        variant="outlined"
        value={url}
        onChange={onUrlChange}
        placeholder="https://example.com"
        error={!!error && !hasLinks}
        helperText={error && !hasLinks ? error : ""}
        size="small"
      />
      <Box sx={{ whiteSpace: "nowrap" }}>
        <Button
          variant="contained"
          onClick={onFetchLinks}
          disabled={isLoading || !url}
          startIcon={isLoading ? <CircularProgress size={20} /> : <LinkIcon />}
        >
          {isLoading ? "Loading..." : "Fetch Links"}
        </Button>
      </Box>
    </Stack>
  </Box>
);
