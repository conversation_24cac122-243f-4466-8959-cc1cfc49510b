import React, { useEffect, useState, useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useRouter } from "next/router";
import { RootState } from "@/store";
import { fetchFileUploadStatus } from "@/requests/agents/fileUpload/getStatus";
import Line from "@/components/general/shapes/horizontalLine";
import SubHeading from "@/components/settings/crm_ai/subHeading";
import UploadKnowledgeModal from "./UploadKnowledgeModal";
import FileStatus from "./FileStatus";
import { agentsAction } from "@/slices/agents/agentsSlice";
import {
  deleteKnowledgeSourceProcessedFiles,
  deleteKnowledgeSourceProcessingJobs,
} from "@/requests/agents/knowledgeSources/deleteKnowledgeSource";
import { Button } from "@mui/material";

const UploadKnowledge = () => {
  const dispatch = useDispatch();
  const router = useRouter();
  const { agentId } = router.query as { agentId: string };
  const currentAgent = useSelector((state: RootState) => state.agents.currentAgent);
  const [add, setAdd] = useState(false);
  const [apiCallCount, setApiCallCount] = useState(0);
  const [expanded, setExpanded] = useState(false);
  const timerRef = useRef<number | null>(null);

  const allFiles = [
    ...currentAgent.processingJobs.map(job => ({ ...job, status: "processing" as const })),
    ...currentAgent.processedFiles.map(job => ({ ...job, status: "processed" as const }))
  ];
  const totalFiles = allFiles.length;

  useEffect(() => {
    console.log({ currentAgent });
  }, [currentAgent]);

  const handleAdd = () => {
    setAdd(!add);
  };

  const onFileUpload = () => {
    setApiCallCount(0);
  };

  useEffect(() => {
    if (currentAgent.processedFiles.length === 0) {
      setAdd(true);
    }
  }, [currentAgent.processedFiles]);

  useEffect(() => {
    const checkProcessingJobs = async () => {
      if (currentAgent.processingJobs.length > 0 && apiCallCount < 30) {
        try {
          const response = await fetchFileUploadStatus({ agentId }, dispatch);
          const data = response?.data;
          if (response.status === 200) {
            dispatch(agentsAction.updateProcessingJobs(data.processingJobs));
            dispatch(agentsAction.updateProcessedFiles(data.processedFiles));
          }

          setApiCallCount((prevCount) => prevCount + 1);

          if (data.processingJobs.length > 0) {
            timerRef.current = window.setTimeout(checkProcessingJobs, 5000);
          }
        } catch (error) {
          console.error("Error checking processing status:", error);
        }
      }
    };

    timerRef.current = window.setTimeout(checkProcessingJobs, 5000);

    return () => {
      if (timerRef.current !== null) {
        window.clearTimeout(timerRef.current);
      }
    };
  }, [currentAgent.processingJobs, apiCallCount, dispatch, agentId]);

  const deleteProcessedKnowledgeSource = async (accountId: string) => {
    const response = await deleteKnowledgeSourceProcessedFiles({
      body: { accountId, agentId: currentAgent._id },
      dispatch,
    });
    if (response.status === 200) {
      dispatch(agentsAction.removeKnowledgeSourceProcessedFiles(accountId));
    }
  };

  const deleteProcessingKnowledgeSource = async (accountId: string) => {
    const response = await deleteKnowledgeSourceProcessingJobs({
      body: { accountId, agentId: currentAgent._id },
      dispatch,
    });
    if (response.status === 200) {
      dispatch(agentsAction.removeKnowledgeSourceProcessingJobs(accountId));
    }
  };

  const toggleExpand = () => {
    setExpanded(!expanded);
  };

  const renderFiles = () => {
    const filesToShow = expanded ? allFiles : allFiles.slice(0, 3);

    return (
      <>
        {filesToShow.map((file, index) => (
          <FileStatus
            accountId={file.accountId}
            fileName={file.fileName}
            jobType={file.jobType}
            status={file.status}
            characterCount={file.status === "processed" ? file.characterCount : undefined}
            onDelete={() => 
              file.status === "processed" 
                ? deleteProcessedKnowledgeSource(file.accountId)
                : deleteProcessingKnowledgeSource(file.accountId)
            }
            key={`${file.status}_files_${index}`}
            mimeType={file.mimeType as string}
          />
        ))}
        {totalFiles > 3 && (
          <div className="flex justify-center mt-2">
            <Button
              onClick={toggleExpand}
            >
              {expanded 
                ? "Show less" 
                : `Show ${totalFiles - 3} more (${currentAgent.processingJobs.length} processing, ${currentAgent.processedFiles.length} processed)`
              }
            </Button>
          </div>
        )}
      </>
    );
  };

  return (
    <div className="border-[1px] border-x-slate-300 rounded-md bg-fourth my-5">
      <SubHeading value="Knowledge sources" addBtn={add} addBtnFn={handleAdd} />
      <Line />
      {add && (
        <UploadKnowledgeModal
          closeModal={() => setAdd(false)}
          onFileUpload={onFileUpload}
        />
      )}
      {renderFiles()}
    </div>
  );
};

export default UploadKnowledge;