import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, Button, CircularProgress } from "@mui/material";
import { UrlInput } from "./UrlInput";
import { LinksList } from "./LinksList";
import { useWebScraping } from "@/hooks/useWebScraping";

interface WebsiteScraperProps {
  agentId: string;
  onSuccess: () => void;
}

export const WebsiteScraper: React.FC<WebsiteScraperProps> = ({
  agentId,
  onSuccess,
}) => {
  const [url, setUrl] = useState("");
  const {
    links,
    isLoading,
    isScraping,
    error,
    fetchLinks,
    startScraping,
    handleLinkSelection,
    handleSelectAll,
    handleUnselectAll,
    selectedCount,
    resetState
  } = useWebScraping(agentId);

  const handleUrlChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setUrl(event.target.value);
  };

  const handleFetchLinks = () => {
    fetchLinks(url, agentId);
  };

  const handleStartScraping = async () => {
    const result = await startScraping(agentId);
    if (result.success) {
      setUrl("");
      resetState();
      onSuccess();
    }
  };
  return (
    <Stack spacing={3}>
      <UrlInput
        url={url}
        onUrlChange={handleUrlChange}
        onFetchLinks={handleFetchLinks}
        isLoading={isLoading}
        error={error}
        hasLinks={links.length > 0}
      />

      {links.length > 0 && (
        <LinksList
          links={links}
          onLinkSelect={handleLinkSelection}
          onSelectAll={handleSelectAll}
          onUnselectAll={handleUnselectAll}
          selectedCount={selectedCount}
        />
      )}

      {error && <Alert severity="error">{error}</Alert>}

      {links.length > 0 && (
        <Button
          variant="contained"
          color="primary"
          onClick={handleStartScraping}
          disabled={isScraping || selectedCount === 0}
          startIcon={isScraping && <CircularProgress size={20} />}
        >
          Start Scraping
        </Button>
      )}
    </Stack>
  );
};
