import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON>, <PERSON>ack, Tabs, Tab } from "@mui/material";
import { useRouter } from "next/router";
import Dropzone from "./Dropzone";
import { WebsiteScraper } from "./WebsiteScrapper";
import TextInputAugmentedQuery from "./TextInputAugmentedQuery";

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

interface UploadKnowledgeModalProps {
  closeModal: () => void;
  onFileUpload: () => void;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`upload-tabpanel-${index}`}
      aria-labelledby={`upload-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const UploadKnowledgeModal: React.FC<UploadKnowledgeModalProps> = ({
  closeModal,
  onFileUpload,
}) => {
  const router = useRouter();
  const { agentId } = router.query as { agentId: string };
  const [activeTab, setActiveTab] = useState(0);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  const a11yProps = (index: number) => ({
    id: `upload-tab-${index}`,
    "aria-controls": `upload-tabpanel-${index}`,
  });

  return (
    <Box>
      <Tabs
        value={activeTab}
        onChange={handleTabChange}
        aria-label="upload options tabs"
        sx={{ borderBottom: 1, borderColor: "divider" }}
      >
        <Tab label="File Upload" {...a11yProps(0)} />
        <Tab label="Website" {...a11yProps(1)} />
        <Tab label="Text" {...a11yProps(2)} />
      </Tabs>

      <TabPanel value={activeTab} index={0}>
        <Dropzone agentId={agentId} onFileUpload={onFileUpload} />
      </TabPanel>

      <TabPanel value={activeTab} index={1}>
        <WebsiteScraper agentId={agentId} onSuccess={onFileUpload} />
      </TabPanel>

      <TabPanel value={activeTab} index={2}>
        <TextInputAugmentedQuery agentId={agentId} onSuccess={onFileUpload} />
      </TabPanel>

      <Box sx={{ p: 2, borderTop: 1, borderColor: "divider" }}>
        <Stack direction="row" justifyContent="end">
          <Button variant="text" color="error" onClick={closeModal}>
            Close
          </Button>
        </Stack>
      </Box>
    </Box>
  );
};

export default UploadKnowledgeModal;
