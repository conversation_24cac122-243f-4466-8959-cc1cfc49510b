import Line from "@/components/general/shapes/horizontalLine";
import SubHeading from "@/components/settings/crm_ai/subHeading";
import React, { useEffect, useState } from "react";
import AddDataSource from "./addDataSource";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store";
import SingleDataSource from "./SingleDataSource";
import { getIntegrationAccounts } from "@/requests/integrations/getIntegrationAccounts";
import {
  ISingleIntegration,
  integrationModalActions,
} from "@/slices/settings/integrations/integrationList";
import { getOrgDataResource } from "@/requests/organizations/basic/getOrgDataResource";
import { ISingleAction } from "@/slices/agents/agentsSlice";

const DataSources = () => {
  const dispatch = useDispatch();
  const dataSources = useSelector(
    (state: RootState) => state.agents.currentAgent?.actions
  );
  const [resourcesList, setResourcesList] = useState<ISingleIntegration[]>([]);
  const [add, setAdd] = useState(false);
  const handleAdd = () => {
    setAdd(!add);
  };
  useEffect(() => {
    const response = getOrgDataResource(
      {
        orgId: localStorage.getItem("orgId") || "",
      },
      dispatch
    );
    // const response = getIntegrationAccounts({
    //   orgId: localStorage.getItem("orgId") || "",
    // });
    response
      .then((res) => {
        console.log({ res });

        if (res?.status == 200) {
          dispatch(
            integrationModalActions.integrationListAdded(res?.data.datasources)
          );
        }
      })
      .catch((error) => {
        console.log(error.message);
      });
  }, []);
  const handleAddClose = () => {
    setAdd(false);
  };
  return (
    <div className="border-[1px] border-x-slate-300 rounded-md bg-fourth my-5">
      <SubHeading value="Actions" addBtn={add} addBtnFn={handleAdd} />
      <Line />
      {add && <AddDataSource onCancelClick={handleAddClose} type="add" />}
      {dataSources?.map((action, index) => (
        <div className="" key={"Single_prompt_div" + action?.actionId}>
          <SingleDataSource
            dataSource={action as ISingleAction}
            onCancelClick={handleAddClose}
          />
          {index !== dataSources.length - 1 && <Line />}
        </div>
      ))}
    </div>
  );
};

export default DataSources;
