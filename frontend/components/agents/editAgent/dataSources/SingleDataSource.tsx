import GeneralModalContent from "@/components/general/generalModalContent";
import GeneralModal from "@/components/general/modal";
import { Button, Chip, IconButton, Link, Tooltip } from "@mui/material";
import Image from "next/image";
import React, { useEffect, useState } from "react";
import { AiFillEdit } from "react-icons/ai";
import { BsFillTrashFill } from "react-icons/bs";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store";
import { deleteAgentAction } from "@/requests/agents/actions/deleteAgentAction";
import {
  IGenerateJsonAction,
  ISingleAction,
  agentsAction,
} from "@/slices/agents/agentsSlice";
import { logos } from "@/helpers/images";
import AddDataSource from "./addDataSource";
import { CONSTANTS } from "@/helpers/constants";
import ContentCopyIcon from "@mui/icons-material/ContentCopy";
import { snackbarActions } from "@/slices/general/snackbar";
interface IProp {
  dataSource: ISingleAction;
  onCancelClick: () => void;
}

const SingleDataSource: React.FC<IProp> = ({ dataSource, onCancelClick }) => {
  console.log({ dataSource });

  const dispatch = useDispatch();
  const currentAgent = useSelector(
    (state: RootState) => state.agents.currentAgent
  );
  const [isCopied, setIsCopied] = useState(false);
  const [edit, setEdit] = useState(false);
  const [open, setOpen] = useState(false); //for the warning modal
  const [loading, setLoading] = useState(false); //for the warning modal
  const redirectLink = ["website"].includes(dataSource.providerName)
    ? "/home/<USER>/data"
    : [CONSTANTS.PROVIDERS.GHL_CHANNEL].includes(dataSource.providerName)
    ? ""
    : "/home/<USER>/integrations";
  const handleClose = () => {
    setOpen(false);
  };
  const toggleEdit = () => {
    setEdit(!edit);
  };
  const handleDelete = async () => {
    setLoading(true);
    //perform the axios request for the delete prompt
    const body = {
      agentId: currentAgent?._id,
      actionId: dataSource.actionId as string,
    };
    try {
      const response = await deleteAgentAction(body, dispatch);
      if (response?.status == 200) {
        dispatch(agentsAction.actionDeleted({ actionId: body.actionId }));
      }
    } catch (error: any) {
      console.log(error?.message);
    }

    setLoading(false);
  };
  const handleEditClose = () => {
    setEdit(false);
  };
  const handleAgentCopy = async () => {
    const content = dataSource?.actionId ?? "";
    try {
      // Using navigator.clipboard.writeText for modern browsers
      await navigator.clipboard.writeText(content);
      setIsCopied(true);
    } catch (err) {
      // Fallback to document.execCommand for older browsers
      const textArea = document.createElement("textarea");
      textArea.value = content;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand("copy");
      document.body.removeChild(textArea);
      setIsCopied(true);
    }
    dispatch(
      snackbarActions.setSnackbar({
        message: "Action ID copied",
        type: "success",
      })
    );
  };
  return (
    <>
      <GeneralModal open={open} onClose={handleClose}>
        <GeneralModalContent
          header="Warning"
          content="Are you sure you want to delete this action?"
          rightBtn="Delete"
          isLoading={loading}
          onClose={handleClose}
          customFn={handleDelete}
        />
      </GeneralModal>
      <div className="flex items-center justify-between  text-[18px] py-3 px-4">
        <div className="w-2/12 flex pl-3 gap-2">
          {dataSource?.providerName ? (
            <div className="">
              <Image
                alt="Action-logo"
                src={
                  "/squareLogos" + logos[dataSource?.providerName]?.logo || ""
                }
                width={30}
                height={30}
              />
            </div>
          ) : dataSource?.providerName === CONSTANTS.PROVIDERS.GHL_CHANNEL &&
            dataSource?.activity === "standardField" ? (
            "Prompt is prefilled"
          ) : (
            "Not set"
          )}
          <Link href={redirectLink || "#"} sx={{ maxWidth: "130px" }}>
            <Tooltip title={dataSource?.accountName || "No name"}>
              <Chip
                label={dataSource?.accountName || "No name"}
                color="primary"
                variant="outlined"
                className="max-w-[100px] truncate"
                sx={{ mr: 1 }}
              />
            </Tooltip>
          </Link>
        </div>
        {edit === false && (
          <div className="w-7/12">
            {dataSource?.providerName === CONSTANTS.PROVIDERS.GHL_CHANNEL &&
            dataSource?.activity === "standardField"
              ? "Prompt is prefilled"
              : dataSource?.promptContent || "Not set"}
          </div>
        )}
        <div className="w-3/12 flex items-center gap-3 justify-end">
          {edit === false ? (
            <>
              {<Chip label={dataSource?.activity || "No activity"} />}
              <div className="">
                <Tooltip title="Copy Action ID">
                  <IconButton
                    aria-label="delete"
                    size="large"
                    onClick={handleAgentCopy}
                  >
                    <ContentCopyIcon />
                  </IconButton>
                </Tooltip>
              </div>
              <Button
                variant="outlined"
                startIcon={<AiFillEdit />}
                onClick={toggleEdit}
              >
                Edit
              </Button>
            </>
          ) : (
            <IconButton
              aria-label="delete"
              color="error"
              onClick={() => {
                setOpen(true);
              }}
            >
              <BsFillTrashFill />
            </IconButton>
          )}
        </div>
      </div>
      {edit && (
        <AddDataSource
          type="edit"
          dataSource={dataSource}
          onCancelClick={handleEditClose}
        />
        // <SingleDataSourceInner
        //   actionId={dataSource.actionId}
        //   accountId={dataSource.accountId}
        //   promptContent={dataSource.promptContent}
        //   providerName={dataSource.providerName}
        //   activity={dataSource.activity}
        //   editFn={toggleEdit}
        // />
      )}
    </>
  );
};

export default SingleDataSource;
