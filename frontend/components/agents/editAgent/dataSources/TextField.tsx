import React, { useState, useRef, useCallback } from "react";
import {
  TextField as <PERSON>iTextField,
  TextFieldProps,
  InputAdornment,
  IconButton,
  Popper,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListSubheader,
  Typography,
  Box,
  Tooltip,
  keyframes,
  styled,
} from "@mui/material";
import LocalOfferIcon from "@mui/icons-material/LocalOffer";
import InfoOutlinedIcon from "@mui/icons-material/InfoOutlined";
import { useSelector } from "react-redux";
import { RootState } from "@/store";

const rippleAnimation = keyframes`
  0% {
    box-shadow: 0 0 0 0 rgba(25, 118, 210, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(25, 118, 210, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(25, 118, 210, 0);
  }
`;

const RippleIconButton = styled(IconButton)(({ theme }) => ({
  position: "relative",
  "&::before": {
    content: '""',
    position: "absolute",
    width: "100%",
    height: "100%",
    borderRadius: "50%",
    animation: `${rippleAnimation} 1.5s infinite`,
  },
}));

interface Variable {
  name: string;
  group: "Native" | "User Defined" | "Uphex";
  description?: string;
  fieldKey: string;
}

const nativeVariables: Variable[] = [
  {
    name: "Current date",
    group: "Native",
    description: "Current date",
    fieldKey: "{{sys.dateNow}}",
  },
  {
    name: "Current time",
    group: "Native",
    description: "Current time",
    fieldKey: "{{sys.timeNow}}",
  },
  {
    name: "Tomorrow's date",
    group: "Native",
    description: "Tomorrow's date",
    fieldKey: "{{sys.dateTomorrow}}",
  },
  {
    name: "Current date and time",
    group: "Native",
    description: "Current date and time",
    fieldKey: "{{sys.dateTimeNow}}",
  },
  {
    name: "Day of the week",
    group: "Native",
    description: "Day of the week (e.g., Monday)",
    fieldKey: "{{sys.dayOfWeek}}",
  },
  {
    name: "Day of the month",
    group: "Native",
    description: "Day of the month (e.g., 1st)",
    fieldKey: "{{sys.dayOfMonth}}",
  },
  {
    name: "Month name",
    group: "Native",
    description: "Month name (e.g., January)",
    fieldKey: "{{sys.monthName}}",
  },
  {
    name: "Current year",
    group: "Native",
    description: "Current year",
    fieldKey: "{{sys.yearNow}}",
  },
  {
    name: "Week of the year",
    group: "Native",
    description: "Current week of the year",
    fieldKey: "{{sys.weekOfYear}}",
  },
];

const uphexDefinedVariables: Variable[] = [
  {
    name: "Ad Title",
    group: "Uphex",
    description: "Ad Title from Uphex",
    fieldKey: "{{uphex.adTitle}}",
  },
  {
    name: "Ad Message",
    group: "Uphex",
    description: "Ad Message from Uphex",
    fieldKey: "{{uphex.adMessage}}",
  },
];

interface AgentVariable {
  _id?: string;
  name: string;
  type: "static" | "dynamic";
  value?: string;
  providerName?: string;
  fieldKey: string;
}

interface EnhancedTextFieldProps extends Omit<TextFieldProps, "onChange"> {
  onChange: (value: string) => void;
  isRippleEffect?: boolean;
}

const EnhancedTextField: React.FC<EnhancedTextFieldProps> = ({
  onChange,
  isRippleEffect = false,
  ...props
}) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const agentVariables =
    (useSelector(
      (state: RootState) => state.agents.currentAgent.variables
    ) as AgentVariable[]) || [];
  const inputRef = useRef<HTMLInputElement>(null);

  const handleVariableButtonClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(anchorEl ? null : event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
    setSearchTerm("");
  };

  const open = Boolean(anchorEl);

  const allVariables = [
    ...nativeVariables,
    ...uphexDefinedVariables,
    ...agentVariables.map((variable) => ({
      name: variable.name,
      group: "User Defined",
      fieldKey: variable.fieldKey,
    })),
  ];

  const filteredVariables = allVariables.filter((variable) =>
    variable.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const groupedVariables = filteredVariables.reduce((acc, variable) => {
    if (!acc[variable.group]) {
      acc[variable.group] = [];
    }
    acc[variable.group].push(variable as Variable);
    return acc;
  }, {} as Record<string, Variable[]>);

  const handleVariableSelect = useCallback(
    (fieldKey: string) => {
      if (inputRef.current) {
        const cursorPosition = inputRef.current.selectionStart || 0;
        const currentValue = inputRef.current.value;

        const newValue =
          currentValue.slice(0, cursorPosition) +
          fieldKey +
          currentValue.slice(cursorPosition);

        onChange(newValue);

        setTimeout(() => {
          if (inputRef.current) {
            const newPosition = cursorPosition + fieldKey.length;
            inputRef.current.setSelectionRange(newPosition, newPosition);
            inputRef.current.focus();
          }
        }, 0);
      }
      handleClose();
    },
    [onChange]
  );

  const IconComponent = isRippleEffect ? RippleIconButton : IconButton;

  return (
    <Box position="relative">
      <MuiTextField
        {...props}
        sx={{
          "& .MuiOutlinedInput-root": {
            paddingRight: "40px", // Add padding for the icon
          },
          ...props.sx,
        }}
        onChange={(e) => onChange(e.target.value)}
        inputRef={inputRef}
        InputProps={{
          ...props.InputProps,
          endAdornment: (
            <InputAdornment position="end">
              <IconComponent
                edge="end"
                onClick={handleVariableButtonClick}
                sx={{
                  position: "absolute",
                  right: 17,
                  top: "15%",
                  transform: "translateY(-50%)",
                }}
              >
                <LocalOfferIcon />
              </IconComponent>
            </InputAdornment>
          ),
        }}
      />

      <Popper
        open={open}
        anchorEl={anchorEl}
        placement="bottom-end"
        sx={{ zIndex: 1300 }}
      >
        <Paper
          elevation={3}
          sx={{ width: 250, maxHeight: 300, overflow: "auto" }}
        >
          <Box
            sx={{
              position: "sticky",
              top: 0,
              zIndex: 1,
              backgroundColor: "background.paper",
            }}
          >
            <MuiTextField
              fullWidth
              variant="outlined"
              size="small"
              placeholder="Search variables"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              sx={{ m: 1, width: "calc(100% - 16px)" }}
            />
          </Box>
          <Box sx={{ maxHeight: 240, overflow: "auto" }}>
            {Object.entries(groupedVariables).map(([group, vars]) => (
              <List
                key={group}
                subheader={
                  <ListSubheader component="div">
                    {group} variables
                  </ListSubheader>
                }
                dense
              >
                {vars.map((variable) => (
                  <ListItem
                    key={variable.name}
                    button
                    onClick={() => handleVariableSelect(variable.fieldKey)}
                    sx={{ display: "flex", alignItems: "center" }}
                  >
                    <ListItemText primary={variable.name} />
                    {variable.description && (
                      <Tooltip title={variable.description} arrow>
                        <IconButton size="small" color="primary" sx={{ ml: 1 }}>
                          <InfoOutlinedIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    )}
                  </ListItem>
                ))}
              </List>
            ))}
            {filteredVariables.length === 0 && (
              <Typography variant="body2" color="text.secondary" sx={{ p: 2 }}>
                No variables found.
              </Typography>
            )}
          </Box>
        </Paper>
      </Popper>
    </Box>
  );
};

export default EnhancedTextField;
