import React, { useMemo } from "react";
import {
  Autocomplete,
  Box,
  FormControl,
  Slider,
  TextField,
  Typography,
  Stack,
} from "@mui/material";
import {
  singleAiModelState,
  AiProviderCompany,
  CAPRI_HOSTED_COMPANY_IDS,
} from "@/slices/settings/ai-crm/aiList";
import { IAiProvider } from "@/slices/agents/agentsSlice";
import { useSelector } from "react-redux";
import { RootState } from "@/store";
import { CONSTANTS } from "@/helpers/constants";
import { useCurrentPlan } from "@/hooks/useCurrentPlan";

interface GroupedModel extends singleAiModelState {
  companyId: string;
  companyName: string;
  accountId: string;
  accountName: string;
}

interface IActionAdvancedSettings {
  aiProvider: IAiProvider;
  maxTokensAllowed?: number;
}

interface ActionsAdvancedSettingsFormProps {
  advancedSettings: IActionAdvancedSettings;
  handleAiProviderChange: (x: {
    accId: string;
    accName: string;
    comId: string;
    modName: string;
  }) => void;
  handleMaxTokensChange: (x: number) => void;
}

const MAX_TOKENS_MIN = 1;
const MAX_TOKENS_MAX = 1000;

const ActionsAdvancedSettingsForm: React.FC<
  ActionsAdvancedSettingsFormProps
> = ({ advancedSettings, handleAiProviderChange, handleMaxTokensChange }) => {
  const aiAccountsList =
    useSelector((state: RootState) => state.ai.aiAccountsList) || [];
  const { aiProviderList } = useCurrentPlan();
  const groupedModels = useMemo(() => {
    const capriHostedModels: GroupedModel[] = [];
    const otherModels: GroupedModel[] = [];

    aiProviderList.forEach((company) => {
      const companyModels = company.models.map((model) => ({
        ...model,
        companyId: company.companyId,
        companyName: company.name,
        accountName: model.modelName,
        accountId: model.id,
        isCapriHosted: CAPRI_HOSTED_COMPANY_IDS.includes(company.companyId),
      }));

      if (CAPRI_HOSTED_COMPANY_IDS.includes(company.companyId)) {
        capriHostedModels.push(...companyModels);
      } else {
        otherModels.push(...companyModels);
      }
    });

    return [...capriHostedModels, ...otherModels];
  }, [aiProviderList]);

  const selectedModel = useMemo(
    () =>
      groupedModels.find(
        (model) =>
          model.companyId === advancedSettings.aiProvider.companyId &&
          model.modelName === advancedSettings.aiProvider.modelName
      ),
    [groupedModels, advancedSettings.aiProvider]
  );

  const getAvailableAccounts = (companyId: AiProviderCompany) => {
    return (
      aiAccountsList.find((company) => company.companyId === companyId)
        ?.accounts || []
    );
  };

  const selectedAccount = useMemo(
    () =>
      getAvailableAccounts(advancedSettings.aiProvider.companyId).find(
        (account) => account.accountId === advancedSettings.aiProvider.accountId
      ),
    [aiAccountsList, advancedSettings.aiProvider]
  );

  const showAccountDropdown = !CAPRI_HOSTED_COMPANY_IDS.includes(
    selectedModel?.companyId ?? ""
  );

  return (
    <Stack spacing={3}>
      <FormControl fullWidth>
        <Autocomplete
          value={selectedModel || null}
          options={groupedModels}
          groupBy={(option) =>
            CAPRI_HOSTED_COMPANY_IDS.includes(option.companyId)
              ? "Capri AI hosted models"
              : option.companyName
          }
          getOptionLabel={(option) => option.name}
          renderInput={(params) => (
            <TextField {...params} label="Model" placeholder="Select a model" />
          )}
          renderGroup={(params) => (
            <Box key={params.key}>
              <Typography
                variant="subtitle2"
                sx={{
                  px: 2,
                  py: 1,
                  backgroundColor: "grey.100",
                  fontWeight: "bold",
                }}
              >
                {params.group}
              </Typography>
              {params.children}
            </Box>
          )}
          onChange={(_, newValue) => {
            if (newValue) {
              // For hosted providers, use the model's account info
              // For non-hosted providers, clear the account info so default selection can work
              if (CAPRI_HOSTED_COMPANY_IDS.includes(newValue.companyId)) {
                handleAiProviderChange({
                  comId: newValue.companyId,
                  modName: newValue.modelName,
                  accId: newValue.accountId,
                  accName: newValue.accountName,
                });
              } else {
                handleAiProviderChange({
                  comId: newValue.companyId,
                  modName: newValue.modelName,
                  accId: "",
                  accName: "",
                });
              }
            }
          }}
          isOptionEqualToValue={(option, value) =>
            option.companyId === value?.companyId &&
            option.modelName === value?.modelName
          }
        />
      </FormControl>

      {showAccountDropdown && (
        <FormControl fullWidth>
          <Autocomplete
            value={selectedAccount || null}
            options={getAvailableAccounts(
              advancedSettings.aiProvider.companyId
            )}
            getOptionLabel={(option) => option.name}
            renderInput={(params) => <TextField {...params} label="Account" />}
            onChange={(_, newValue) => {
              if (newValue) {
                handleAiProviderChange({
                  comId: advancedSettings.aiProvider.companyId,
                  modName: advancedSettings.aiProvider.modelName,
                  accId: newValue.accountId,
                  accName: newValue.name,
                });
              }
            }}
          />
        </FormControl>
      )}

      <Box>
        <Typography gutterBottom>Maximum Tokens</Typography>
        <Slider
          value={advancedSettings.maxTokensAllowed || MAX_TOKENS_MIN}
          min={MAX_TOKENS_MIN}
          max={MAX_TOKENS_MAX}
          onChange={(_, value) => handleMaxTokensChange(value as number)}
          valueLabelDisplay="auto"
        />
      </Box>
    </Stack>
  );
};

export default ActionsAdvancedSettingsForm;
