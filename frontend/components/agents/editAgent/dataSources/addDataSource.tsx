import {
  Button,
  Checkbox,
  FormControl,
  FormControlLabel,
  InputLabel,
  MenuItem,
  Select,
  Stack,
  Switch,
  <PERSON>lt<PERSON>,
  FormHelperText,
  Slider,
  Typography,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import GHLChannelInputs, { AntSwitch } from "./addDataSource/ghlChannelInputs";
import AccountInputs from "./addDataSource/AccountInputs";
import { useRouter } from "next/router";
import {
  IAccountAction,
  IActionAdvancedSettings,
  IActionGhlChannel,
  IAiProvider,
  IGenerateJsonAction,
  IHttpGetAction,
  IHttpPostAction,
  IGhlCalendarAction,
  ISingleAction,
  ISlackAction,
  ProviderName,
  IGhlEmailAction,
  ISmsAction,
  IGoogleCalendarAction,
} from "@/slices/agents/agentsSlice";
import AdvancedSettings from "./advancedSettings/AdvancedSettings";
import ButtonSpinner from "@/components/general/buttons/spinner";
import { CONSTANTS } from "@/helpers/constants";
import GenerateJsonInput from "./addDataSource/generateJson/GenerateJsonInput";
import { useSelector } from "react-redux";
import { RootState } from "@/store";
import ImportPrompt from "./ImportPrompt";
import HttpGetAccountSelect from "./addDataSource/http/httpGet/HttpGetAccountSelect";
import HttpPostAccountSelect from "./addDataSource/http/httpPost/HttpPostAccountSelect";
import SlackAccountSelect from "./addDataSource/slack/SlackAccountSelect";
import GhlCalendarInput from "./addDataSource/ghlCalendar/GhlCalendarInput";
import EnhancedTextField from "./TextField";
import { InfoOutlined } from "@mui/icons-material";
import ActionsAdvancedSettingsForm from "./ActionsAdvancedSettingsForm";
import GhlEmailActionSelect from "./addDataSource/ghlEmail/GhlEmailActionSelect";
import SmsActionSelect from "./addDataSource/sms/SmsActionSelect";
import GoogleCalendarInput from "./addDataSource/googleCalendar/GoogleCalendarInput";
import { CAPRI_HOSTED_COMPANY_IDS } from "@/slices/settings/ai-crm/aiList";
import { useCurrentPlan } from "@/hooks/useCurrentPlan";

const menuItems = [
  { value: CONSTANTS.PROVIDERS.GHL_CHANNEL, label: "GHL Channel" },
  { value: CONSTANTS.PROVIDERS.GHL_EMAIL, label: "Email" },
  { value: "ghlCalendar", label: "GHL Calendar" },
  { value: CONSTANTS.PROVIDERS.GOOGLE_CALENDAR, label: "Google Calendar" },
  { value: "googleSheet", label: "Google Sheet" },
  { value: "website", label: "Website" },
  { value: CONSTANTS.PROVIDERS.GENERATE_JSON, label: "Generate JSON" },
  { value: CONSTANTS.PROVIDERS.GOOGLE_DOCS, label: "Google docs" },
  { value: CONSTANTS.PROVIDERS.HTTP_GET, label: "HTTP GET" },
  { value: CONSTANTS.PROVIDERS.HTTP_POST, label: "HTTP POST" },
  { value: CONSTANTS.PROVIDERS.SLACK, label: "Slack" },
  { value: CONSTANTS.PROVIDERS.SMS, label: "SMS" },
];
menuItems.sort((a, b) => a.label.localeCompare(b.label));

interface IProp {
  type: "add" | "edit";
  dataSource?: ISingleAction;
  onCancelClick: () => void;
}

export interface IAdvancedSettings {
  aiProvider: IAiProvider;
  maxTokensAllowed?: number;
}

export const DEFAULT_MAX_TOKENS_ALLOWED = 450;

const AddDataSource: React.FC<IProp> = ({
  dataSource,
  onCancelClick,
  type,
}) => {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const currentAgent = useSelector(
    (state: RootState) => state.agents.currentAgent
  );
  const aiAccountsList =
    useSelector((state: RootState) => state.ai.aiAccountsList) || [];
  const { aiProviderList } = useCurrentPlan();
  const [isAdvancedSettings, setIsAdvancedSettings] = useState(
    dataSource?.isAdvancedSettings ?? false
  );
  const [hidePromptContent, setHidePromptContent] = useState(false);
  const [advancedSettings, setAdvancedSettings] =
    useState<IActionAdvancedSettings>({
      aiProvider: {
        accountId:
          dataSource?.advancedSettings?.aiProvider?.accountId ??
          currentAgent?.aiProvider?.accountId ??
          "",
        accountName:
          dataSource?.advancedSettings?.aiProvider?.accountName ??
          currentAgent?.aiProvider?.accountName ??
          "",
        companyId:
          dataSource?.advancedSettings?.aiProvider?.companyId ||
          currentAgent?.aiProvider?.companyId ||
          "",
        modelName:
          dataSource?.advancedSettings?.aiProvider?.modelName ||
          currentAgent?.aiProvider?.modelName ||
          "",
      },
      maxTokensAllowed:
        dataSource?.advancedSettings?.maxTokensAllowed ||
        DEFAULT_MAX_TOKENS_ALLOWED,
    });
  useEffect(() => {
    console.log({
      check: dataSource?.advancedSettings?.aiProvider,
      dataSource,
    });

    setAdvancedSettings({
      aiProvider: {
        accountId:
          dataSource?.advancedSettings?.aiProvider?.accountId ||
          currentAgent?.aiProvider?.accountId ||
          "",
        accountName:
          dataSource?.advancedSettings?.aiProvider?.accountName ||
          currentAgent?.aiProvider?.accountName ||
          "",
        companyId:
          dataSource?.advancedSettings?.aiProvider?.companyId ||
          currentAgent?.aiProvider?.companyId ||
          "",
        modelName:
          dataSource?.advancedSettings?.aiProvider?.modelName ||
          currentAgent?.aiProvider?.modelName ||
          "",
      },
      maxTokensAllowed:
        dataSource?.advancedSettings?.maxTokensAllowed ||
        DEFAULT_MAX_TOKENS_ALLOWED,
    });
  }, [dataSource]);

  const [data, setData] = useState<ISingleAction>();
  const [submitted, setSubmitted] = React.useState(false);

  // Set default AI provider account when advanced settings are enabled
  useEffect(() => {
    if (isAdvancedSettings && advancedSettings.aiProvider.companyId && advancedSettings.aiProvider.modelName) {
      // Check if account is not already set
      if (!advancedSettings.aiProvider.accountId) {
        // For hosted providers, automatically set the account based on the model
        if (CAPRI_HOSTED_COMPANY_IDS.includes(advancedSettings.aiProvider.companyId)) {
          const accountId =
            aiProviderList
              .find((aiProvider) => aiProvider.companyId === advancedSettings.aiProvider.companyId)
              ?.models.find((model) => model.modelName === advancedSettings.aiProvider.modelName)?.id || "";
          const accountName = "Capri Hosted LLM";

          if (accountId) {
            setAdvancedSettings((prevSettings) => ({
              ...prevSettings,
              aiProvider: {
                ...prevSettings.aiProvider,
                accountId,
                accountName,
              },
            }));
          }
        } else {
          // For non-hosted providers, select the first available account
          const availableAccounts = aiAccountsList
            .find((company) => company.companyId === advancedSettings.aiProvider.companyId)
            ?.accounts || [];

          if (availableAccounts.length > 0) {
            const firstAccount = availableAccounts[0];
            setAdvancedSettings((prevSettings) => ({
              ...prevSettings,
              aiProvider: {
                ...prevSettings.aiProvider,
                accountId: firstAccount.accountId,
                accountName: firstAccount.name,
              },
            }));
          }
        }
      }
    }
  }, [isAdvancedSettings, advancedSettings.aiProvider.companyId, advancedSettings.aiProvider.modelName, aiProviderList, aiAccountsList]);



  useEffect(() => {}, [advancedSettings]);
  useEffect(() => {
    if (dataSource) {
      const { isAdvancedSettings, advancedSettings, ...rest } = dataSource;

      // setData(rest as ISingleAction);
      setData(dataSource);
    }
  }, [dataSource]);

  const handleChange = (newValue: string) => {
    setData({
      ...data,
      promptContent: newValue,
    } as ISingleAction);
  };
  return (
    <div className="flex flex-col gap-3 my-3 px-5">
      <FormControl
        sx={{
          display: "flex",
          flexDirection: "column",
          gap: 1,
        }}
      >
        <InputLabel id="demo-simple-select-label" required>
          Provider
        </InputLabel>
        <Select
          fullWidth
          label="Provider"
          value={data?.providerName || ""}
          onChange={(e) => {
            const newProvider = e.target.value as ProviderName;
            if (newProvider === CONSTANTS.PROVIDERS.GHL_CHANNEL) {
              setData({
                accountId: "",
                providerName: newProvider,
                activity: "tag",
                metaData: {},
                promptContent: "",
                isAdvancedSettings,
                advancedSettings,
              } as IActionGhlChannel);
            } else {
              setData({
                providerName: newProvider,
                activity: "read",
                promptContent: "",
                accountId: "",
                isAdvancedSettings,
                advancedSettings,
              } as IAccountAction);
            }
          }}
        >
          {menuItems.map((item) => (
            <MenuItem value={item.value} key={item.value}>
              {item.label}
            </MenuItem>
          ))}
        </Select>

        {data?.providerName === CONSTANTS.PROVIDERS.GHL_CHANNEL && (
          <GHLChannelInputs
            data={data as IActionGhlChannel}
            hidePromptContent={hidePromptContent}
            setHidePromptContent={setHidePromptContent}
            actionId={dataSource?.actionId}
            submitted={submitted}
            setSubmit={() => setSubmitted(false)}
            onCloseFn={() => onCancelClick()}
            type={type}
            isAdvancedSettings={isAdvancedSettings}
            advancedSettings={advancedSettings}
          />
        )}
        {data?.providerName === CONSTANTS.PROVIDERS.GHL_CALENDAR && (
          <GhlCalendarInput
            data={data as IGhlCalendarAction}
            onDataChange={(x: ISingleAction) => setData(x)}
            actionId={dataSource?.actionId}
            submitted={submitted}
            setSubmit={() => setSubmitted(false)}
            onCloseFn={() => onCancelClick()}
            type={type}
            isAdvancedSettings={isAdvancedSettings}
            advancedSettings={advancedSettings}
          />
        )}
        {data?.providerName === CONSTANTS.PROVIDERS.GENERATE_JSON && (
          <GenerateJsonInput
            data={data as IGenerateJsonAction}
            actionId={dataSource?.actionId}
            submitted={submitted}
            setSubmit={() => setSubmitted(false)}
            onCloseFn={() => onCancelClick()}
            type={type}
            isAdvancedSettings={isAdvancedSettings}
            advancedSettings={advancedSettings}
          />
        )}
        {data?.providerName === CONSTANTS.PROVIDERS.HTTP_GET && (
          <HttpGetAccountSelect
            data={data as IHttpGetAction}
            actionId={dataSource?.actionId}
            submitted={submitted}
            setSubmit={() => setSubmitted(false)}
            onCloseFn={() => onCancelClick()}
            type={type}
            isAdvancedSettings={isAdvancedSettings}
            advancedSettings={advancedSettings}
          />
        )}
        {data?.providerName === CONSTANTS.PROVIDERS.HTTP_POST && (
          <HttpPostAccountSelect
            data={data as IHttpPostAction}
            actionId={dataSource?.actionId}
            submitted={submitted}
            setSubmit={() => setSubmitted(false)}
            onCloseFn={() => onCancelClick()}
            type={type}
            isAdvancedSettings={isAdvancedSettings}
            advancedSettings={advancedSettings}
          />
        )}
        {data?.providerName === CONSTANTS.PROVIDERS.SLACK && (
          <SlackAccountSelect
            data={data as ISlackAction}
            actionId={dataSource?.actionId}
            submitted={submitted}
            setSubmit={() => setSubmitted(false)}
            onCloseFn={() => onCancelClick()}
            type={type}
            isAdvancedSettings={isAdvancedSettings}
            advancedSettings={advancedSettings}
          />
        )}
        {data?.providerName === CONSTANTS.PROVIDERS.GHL_EMAIL && (
          <GhlEmailActionSelect
            data={data as IGhlEmailAction}
            actionId={dataSource?.actionId}
            submitted={submitted}
            setSubmit={() => setSubmitted(false)}
            onCloseFn={() => onCancelClick()}
            type={type}
            isAdvancedSettings={isAdvancedSettings}
            advancedSettings={advancedSettings}
          />
        )}
        {data?.providerName === CONSTANTS.PROVIDERS.SMS && (
          <SmsActionSelect
            data={data as ISmsAction}
            actionId={dataSource?.actionId}
            submitted={submitted}
            setSubmit={() => setSubmitted(false)}
            onCloseFn={() => onCancelClick()}
            type={type}
            isAdvancedSettings={isAdvancedSettings}
            advancedSettings={advancedSettings}
          />
        )}
        {data?.providerName === CONSTANTS.PROVIDERS.GOOGLE_CALENDAR && (
          <GoogleCalendarInput
            data={data as IGoogleCalendarAction}
            onDataChange={(x: ISingleAction) => setData(x)}
            actionId={dataSource?.actionId}
            submitted={submitted}
            setSubmit={() => setSubmitted(false)}
            onCloseFn={() => onCancelClick()}
            type={type}
            isAdvancedSettings={isAdvancedSettings}
            advancedSettings={advancedSettings}
          />
        )}
        {data &&
          [
            // "ghlCalendar",
            // "googleCalendar",
            "googleSheet",
            "website",
            CONSTANTS.PROVIDERS.GOOGLE_DOCS,
            // CONSTANTS.PROVIDERS.GOOGLE_CALENDAR,
          ].includes(data.providerName) && (
            <AccountInputs
              data={data as IAccountAction}
              onDataChange={(x: ISingleAction) => setData(x)}
              actionId={dataSource?.actionId}
              submitted={submitted}
              setSubmit={() => setSubmitted(false)}
              onCloseFn={() => onCancelClick()}
              type={type}
              isAdvancedSettings={isAdvancedSettings}
              advancedSettings={advancedSettings}
            />
          )}
        <FormControl fullWidth sx={{ mt: 2 }}>
          <InputLabel id="eval-type-label">Evaluation Type</InputLabel>
          <Select
            labelId="eval-type-label"
            id="eval-type"
            value={data?.evalType || "DEFAULT"}
            label="Evaluation Type"
            onChange={(e) =>
              setData({
                ...(data as ISingleAction),
                evalType: e.target.value as "DEFAULT" | "ALWAYS" | "CONFIDENCE",
                // Reset confidenceValue to default when switching away from CONFIDENCE
                confidenceValue:
                  e.target.value === "CONFIDENCE"
                    ? data?.confidenceValue || 70
                    : undefined,
              })
            }
          >
            <MenuItem value="DEFAULT">
              <Typography variant="body1" sx={{ mr: 1 }}>
                Default
              </Typography>
              <Typography
                variant="caption"
                sx={{ display: "block", color: "text.secondary" }}
              >
                AI decides whether to run this action based on context
              </Typography>
            </MenuItem>
            <MenuItem value="ALWAYS">
              <Typography variant="body1" sx={{ mr: 1 }}>
                Always
              </Typography>
              <Typography
                variant="caption"
                sx={{ display: "block", color: "text.secondary" }}
              >
                Always run this action regardless of context
              </Typography>
            </MenuItem>
            <MenuItem value="CONFIDENCE">
              <Typography variant="body1" sx={{ mr: 1 }}>
                Confidence-based
              </Typography>
              <Typography
                variant="caption"
                sx={{ display: "block", color: "text.secondary" }}
              >
                Only run when AI&apos;s confidence exceeds a threshold
              </Typography>
            </MenuItem>
          </Select>
        </FormControl>

        {data?.evalType === "CONFIDENCE" && (
          <FormControl fullWidth sx={{ mt: 2, mb: 1 }}>
            <Typography gutterBottom>
              Confidence Threshold: {data?.confidenceValue || 70}%
            </Typography>
            <Slider
              value={data?.confidenceValue || 70}
              onChange={(_, newValue) =>
                setData({
                  ...(data as ISingleAction),
                  confidenceValue: newValue as number,
                })
              }
              min={0}
              max={100}
              step={1}
              valueLabelDisplay="auto"
              aria-labelledby="confidence-slider"
            />
            <FormHelperText>
              {data?.confidenceValue && data.confidenceValue >= 80
                ? "High confidence threshold - This would prevent the AI from firing this action until it is very certain."
                : data?.confidenceValue && data.confidenceValue >= 50
                ? "Moderate confidence threshold - AI will run this action when reasonably confident."
                : "Low confidence threshold - AI will run this action even with minimal confidence."}
            </FormHelperText>
          </FormControl>
        )}
        {!hidePromptContent && (
          <>
            <EnhancedTextField
              fullWidth
              label="Prompt Content"
              multiline
              rows={4}
              required
              id={`Prompt_content_${crypto.randomUUID()}`}
              value={data?.promptContent}
              onChange={handleChange}
            />
            <div className="flex gap-2">
              <ImportPrompt
                onPromptSelect={(x: string) =>
                  setData({ ...(data as ISingleAction), promptContent: x })
                }
              />
            </div>
            <Stack direction="row" alignItems="center">
              <FormControlLabel
                control={
                  <Checkbox
                    checked={Boolean(
                      data?.includeMainPromptDetails?.includeMainPrompt
                    )}
                    onChange={() =>
                      setData({
                        ...(data as ISingleAction),
                        includeMainPromptDetails: {
                          includeMainPrompt:
                            !data?.includeMainPromptDetails?.includeMainPrompt,
                          mainPromptId: "",
                        },
                      })
                    }
                    color="primary"
                  />
                }
                label="Include main prompt"
              />
              <Tooltip title="The main prompt will be carried over while action evaluation.">
                <InfoOutlined className="cursor-pointer" />
              </Tooltip>
            </Stack>
            <FormControl>
              <Stack direction="row" alignItems="center">
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={data?.augmentedQueryData ?? false}
                      onChange={() =>
                        setData({
                          ...(data as ISingleAction),
                          augmentedQueryData: !data?.augmentedQueryData,
                        })
                      }
                      color="primary"
                    />
                  }
                  label="Include augmented query data"
                />
                <Tooltip title="Enabling this will include the content from your connected knowledge sources (documents, FAQs, websites) during the evaluation of this action.">
                  <InfoOutlined className="cursor-pointer" />
                </Tooltip>
              </Stack>
            </FormControl>
          </>
        )}
        <FormControlLabel
          control={
            <Switch
              checked={isAdvancedSettings}
              onChange={() => setIsAdvancedSettings((prevState) => !prevState)}
            />
          }
          label="Advanced settings"
        />
        {isAdvancedSettings && (
          <ActionsAdvancedSettingsForm
            advancedSettings={advancedSettings}
            handleAiProviderChange={(x: {
              accId: string;
              accName: string;
              comId: string;
              modName: string;
            }) => {
              setAdvancedSettings((prevSettings) => ({
                ...prevSettings,
                aiProvider: {
                  accountId: x.accId,
                  accountName: x.accName,
                  companyId: x.comId,
                  modelName: x.modName,
                },
              }));
            }}
            handleMaxTokensChange={(x: number) => {
              setAdvancedSettings((prevSettings) => ({
                ...prevSettings,
                maxTokensAllowed: x,
              }));
            }}
          />
        )}
        <div className="text-right mb-2">
          <Button
            variant="text"
            color="error"
            sx={{ mx: 1 }}
            onClick={onCancelClick}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            variant="contained"
            onClick={() => setSubmitted(true)}
            disabled={submitted}
            id="agentSettings_btn"
          >
            Submit
            {submitted && <ButtonSpinner />}
          </Button>
        </div>
      </FormControl>
    </div>
  );
};

export default AddDataSource;
