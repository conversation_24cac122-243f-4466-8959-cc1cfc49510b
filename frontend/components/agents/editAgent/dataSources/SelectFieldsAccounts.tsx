import { RootState } from "@/store";
import {
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  SelectChangeEvent,
} from "@mui/material";
import React, { useEffect } from "react";
import { useSelector } from "react-redux";

interface IProp {
  accountId: string;
  providerName: string;
  onChangeFn: (value: string) => void;
  onAccountNameChangeFn: (value: string) => void;
}

const SelectFieldsAccounts: React.FC<IProp> = ({
  onChangeFn,
  onAccountNameChangeFn,
  accountId,
  providerName,
}) => {
  const aiAccountsList = useSelector(
    (state: RootState) => state.ai.aiAccountsList
  );
  const integrationList = useSelector(
    (state: RootState) => state.integrationList.integrationAccountsList
  );
  useEffect(() => {
    console.log({ integrationList });
  }, [integrationList]);
  const currentAgent = useSelector(
    (state: RootState) => state.agents.currentAgent
  );
  const [account, setAccount] = React.useState("");
  const HandleAccountChange = (event: SelectChangeEvent) => {
    const accountId = event.target.value;
    setAccount(accountId);
    onChangeFn(accountId);
    const accountName = integrationList.find(
      (item) => item.accountId === accountId
    )?.name;
    onAccountNameChangeFn(accountName || "");
  };
  useEffect(() => {
    setAccount(accountId);
    onChangeFn(accountId);
  }, [accountId]);
  return (
    <FormControl fullWidth>
      <InputLabel id="demo-simple-select-standard-label" sx={{}}>
        Account
      </InputLabel>
      <Select
        labelId="demo-simple-select-standard-label"
        id="demo-simple-select-standard"
        value={account}
        onChange={HandleAccountChange}
        label="aiProvider"
        sx={{ width: "250px" }}
      >
        {integrationList
          .filter((integration) => integration.providerName == providerName)
          .map(
            (item: any) =>
              item.providerName === providerName && (
                <MenuItem
                  value={item.accountId}
                  key={"aiProvider_account_" + item.accountId}
                  selected={item.accountId === accountId ? true : false}
                >
                  {item.name}
                </MenuItem>
              )
          )}
      </Select>
    </FormControl>
  );
};

export default SelectFieldsAccounts;
