import {
  FormControl,
  InputLabel,
  TextField,
  RadioGroup,
  FormControlLabel,
  Radio,
} from "@mui/material";
import React from "react";

const SetGhlTag = () => {
  const [selectedValue, setSelectedValue] = React.useState("");

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSelectedValue(event.target.value);
  };

  return (
    <FormControl fullWidth>
      <TextField
        id="outlined-basic-tag-value"
        label="Tag name"
        variant="outlined"
        sx={{ width: "250px", marginBottom: "16px" }}
      />
      <RadioGroup
        aria-label="evaluate"
        value={selectedValue}
        onChange={handleChange}
      >
        <FormControlLabel
          value="everyTurn"
          control={<Radio />}
          label="Evaluate on every turn"
        />
        <FormControlLabel
          value="whenNoTag"
          control={<Radio />}
          label="Evaluate only when contact does not have the tag"
        />
      </RadioGroup>
    </FormControl>
  );
};

export default SetGhlTag;
