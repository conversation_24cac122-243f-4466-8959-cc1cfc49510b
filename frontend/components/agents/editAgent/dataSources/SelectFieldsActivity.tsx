import { RootState } from "@/store";
import {
  FormControl,
  InputLabel,
  ListItemIcon,
  MenuItem,
  Select,
  SelectChangeEvent,
} from "@mui/material";
import React, { ChangeEvent, useEffect, useState } from "react";
import { useSelector } from "react-redux";

interface IProp {
  onChangeFn: (value: string) => void;
  accountId?: string;
  providerName: string;
  activity: string;
}

const SelectFieldsActivity: React.FC<IProp> = ({
  onChangeFn,
  accountId,
  providerName,
  activity,
}) => {
  const [actionActivity, setActionActivity] = useState("");
  const HandleActivityChange = (event: SelectChangeEvent) => {
    setActionActivity(event.target.value);
    onChangeFn(event.target.value);
  };
  useEffect(() => {
    setActionActivity(activity);
    onChangeFn(activity);
  }, [activity]);
  const activityList: { [key: string]: string[] } = {
    all: ["read", "write", "read-write"],
    ghlCalendar: ["read", "write"],
    googleSheet: ["read", "write"],
    website: ["read"],
  };
  return (
    <FormControl fullWidth>
      <InputLabel id="demo-simple-select-standard-label" sx={{}}>
        Activity
      </InputLabel>
      <Select
        labelId="demo-simple-select-standard-label"
        id="demo-simple-select-standard"
        value={actionActivity}
        onChange={HandleActivityChange}
        label="aiProvider"
        sx={{ width: "250px" }}
      >
        {/* <MenuItem value={"none"} key={"none"} disabled>
          None
        </MenuItem> */}
        {activityList[providerName]?.map((item: any) => {
          if (item === "read") {
            return (
              <MenuItem
                value={item}
                key={item}
                selected={activity == item ? true : false}
              >
                Read only
              </MenuItem>
            );
          } else if (item === "write") {
            return (
              <MenuItem
                value={item}
                key={item}
                selected={activity == item ? true : false}
              >
                Write only
              </MenuItem>
            );
          } else if (item === "read-write") {
            return (
              <MenuItem
                value={item}
                key={item}
                selected={activity == item ? true : false}
              >
                Read and write
              </MenuItem>
            );
          } else {
            return null; // or handle other cases if needed
          }
        })}

        {/* {aiAccountsList.map((item: any) => (
          <MenuItem
            value={item.accountId}
            key={"aiProvider_account_" + item.accountId}
            selected={item.accountId === accountId ? true : false}
          >
            {item.accountId}
          </MenuItem>
        ))} */}
      </Select>
    </FormControl>
  );
};

export default SelectFieldsActivity;
