import React, { useEffect, useState } from "react";
import {
  DEFAULT_MAX_TOKENS_ALLOWED,
  IAdvancedSettings,
} from "../addDataSource";
import {
  Box,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  SelectChangeEvent,
  Slider,
  Typography,
} from "@mui/material";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store";
import { getAiProviders } from "@/requests/aiProvider/getAiProviders";
import { aiActions } from "@/slices/settings/ai-crm/aiList";
import { IActionAdvancedSettings } from "@/slices/agents/agentsSlice";
import SelectFieldsModel from "../../aiProvider/selectFields/SelectFieldsModel";
import SelectFieldsAccounts from "../../aiProvider/selectFields/selectFieldsAccounts";
import { CONSTANTS } from "@/helpers/constants";

interface IProp {
  advancedSettings: IActionAdvancedSettings;
  handleAiProviderChange: (x: {
    accId: string;
    accName: string;
    comId: string;
    modName: string;
  }) => void;
  handleMaxTokensChange: (x: number) => void;
}

const AdvancedSettings: React.FC<IProp> = ({
  advancedSettings,
  handleAiProviderChange,
  handleMaxTokensChange,
}) => {
  const dispatch = useDispatch();
  const aiAccountsList = useSelector(
    (state: RootState) => state.ai.aiAccountsList
  );

  useEffect(() => {
    console.log({ advancedSettings });
  }, [advancedSettings]);
  useEffect(() => {
    const response = getAiProviders(
      {
        orgId: localStorage.getItem("orgId") || "",
      },
      dispatch
    );
    response.then((res) => {
      console.log(res?.data.aiProviderAccounts);

      dispatch(aiActions.aiAccountsListAdded(res?.data.aiProviderAccounts));
    });
  }, [advancedSettings.aiProvider?.companyId]);
  return (
    <Box
      sx={{ minWidth: 120, display: "flex", flexDirection: "column", gap: 2 }}
    >
      <FormControl fullWidth>
        <SelectFieldsModel
          initialModelName={advancedSettings?.aiProvider?.modelName}
          onChangeFn={(value: { companyId: string; modelName: string }) =>
            handleAiProviderChange({
              modName: value?.modelName,
              comId: value?.companyId,
              accId: advancedSettings?.aiProvider?.accountId,
              accName: advancedSettings?.aiProvider?.accountName,
            })
          }
        />
      </FormControl>
      <FormControl fullWidth>
        <SelectFieldsAccounts
          initialAccountId={advancedSettings?.aiProvider?.accountId}
          provider={advancedSettings?.aiProvider?.companyId}
          modelName={advancedSettings?.aiProvider?.modelName}
          onChangeFn={(value: { accountId: string; accountName: string }) =>
            handleAiProviderChange({
              accId: value?.accountId,
              accName: value?.accountName,
              comId: advancedSettings?.aiProvider?.companyId,
              modName: advancedSettings?.aiProvider?.modelName,
            })
          }
        />
      </FormControl>
      <FormControl sx={{ width: "500px" }}>
        <Box>
          <Typography gutterBottom>Maximum tokens allowed</Typography>
          <Slider
            value={
              advancedSettings.maxTokensAllowed || DEFAULT_MAX_TOKENS_ALLOWED
            }
            max={2000}
            min={1}
            step={10}
            aria-label="Default"
            valueLabelDisplay="auto"
            onChange={(event: Event, newValue: number | number[]) => {
              if (typeof newValue === "number") {
                handleMaxTokensChange(newValue);
              }
            }}
          />
        </Box>
      </FormControl>
    </Box>
  );
};

export default AdvancedSettings;
