import React, { useEffect, useState } from "react";
import {
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Stack,
  Typography,
  Tooltip,
  SelectChangeEvent,
} from "@mui/material";
import { useDispatch, useSelector } from "react-redux";
import { useRouter } from "next/router";
import InfoOutlinedIcon from "@mui/icons-material/InfoOutlined";
import { RootState } from "@/store";
import {
  IGoogleCalendarAction,
  agentsAction,
} from "@/slices/agents/agentsSlice";
import { IAdvancedSettings } from "../../addDataSource";
import { AntSwitch } from "../ghlChannelInputs";
import { addAgentAction } from "@/requests/agents/actions/addAgentAction";
import { editAgentAction } from "@/requests/agents/actions/editAgentAction";
import AddDataSourceMenuItem from "../AddDataSourceMenuItem";
import { logos } from "@/helpers/images";
import { capitalizeFirstLetter } from "@/helpers/basic";

interface IProp {
  data: IGoogleCalendarAction;
  onDataChange: (x: IGoogleCalendarAction) => void;
  submitted: boolean;
  setSubmit: () => void;
  type: "add" | "edit";
  onCloseFn: () => void;
  isAdvancedSettings: boolean;
  advancedSettings: IAdvancedSettings;
  actionId?: string;
}

const GoogleCalendarInput: React.FC<IProp> = ({
  data,
  onDataChange,
  submitted,
  setSubmit,
  type,
  onCloseFn,
  isAdvancedSettings,
  advancedSettings,
  actionId,
}) => {
  const dispatch = useDispatch();
  const router = useRouter();
  const [silent, setSilent] = React.useState(data?.silent ?? true);
  const [cancelEvent, setCancelEvent] = useState(
    data.metaData?.googleCalendarMetaData?.cancelEvent ?? false
  );
  const [rescheduleEvent, setRescheduleEvent] = useState(
    data.metaData?.googleCalendarMetaData?.rescheduleEvent ?? false
  );

  const accountList = useSelector(
    (state: RootState) => state.integrationList.integrationAccountsList
  )
    .filter((item) => item.providerName === data.providerName)
    .map((item) => ({ accountId: item.accountId, name: item.name }));

  const activityList = useSelector(
    (state: RootState) => state.integrationList.integrationProvidersList
  ).find((item) => item.providerId === data.providerName)?.activity;

  const { agentId } = router.query as { agentId: string };
  const accountId = data.accountId ?? "";
  const activity = data.activity;

  const handleChangeActivity = (event: SelectChangeEvent) => {
    onDataChange({ ...data, activity: event.target.value as "read" | "write" });
  };

  const handleChangeAccountId = (event: SelectChangeEvent) => {
    onDataChange({ ...data, accountId: event.target.value as string });
  };

  useEffect(() => {
    const accountName =
      accountList.find((item) => item.accountId === accountId)?.name || "";
    const body = {
      agentId,
      action: {
        accountId: data.accountId ?? "",
        providerName: data.providerName,
        promptContent: data.promptContent,
        accountName,
        activity,
        silent,
        isAdvancedSettings,
        advancedSettings,
        includeMainPromptDetails: data.includeMainPromptDetails,
        augmentedQueryData: data.augmentedQueryData,
        metaData: {
          googleCalendarMetaData: {
            cancelEvent,
            rescheduleEvent,
          },
        },
        evalType: data.evalType,
        confidenceValue: data.confidenceValue,
      },
    };

    if (submitted && type === "add") {
      try {
        const response = addAgentAction(body, dispatch);
        response
          .then((resData) => {
            if (resData.status === 201) {
              dispatch(agentsAction.actionAdded(resData.data.action));
              onCloseFn();
            }
          })
          .finally(() => {
            setSubmit();
          });
      } catch (error) {
        console.log(error);
      }
    } else if (submitted && type === "edit") {
      try {
        const bodyForEdit = { ...body, actionId: actionId ?? "" };
        const response = editAgentAction(bodyForEdit, dispatch);
        response
          .then((resData) => {
            if (resData?.status === 200) {
              dispatch(
                agentsAction.actionEdited({
                  ...bodyForEdit.action,
                  actionId: bodyForEdit.actionId,
                })
              );
              onCloseFn();
            }
          })
          .finally(() => {
            setSubmit();
          });
      } catch (error) {
        console.log(error);
      }
    }
  }, [submitted]);

  return (
    <div>
      <FormControl
        sx={{ display: "flex", flexDirection: "column", gap: 2 }}
        fullWidth
      >
        <FormControl fullWidth>
          <InputLabel id="demo-simple-select-label-account" required>
            Account
          </InputLabel>
          <Select
            labelId="demo-simple-select-label-account"
            id="demo-simple-select-account"
            fullWidth
            value={accountId}
            label="Account"
            onChange={handleChangeAccountId}
            required
          >
            {accountList?.map((item) => (
              <MenuItem
                value={item.accountId}
                key={"Select_activity_key" + item.accountId}
              >
                {item.name || "No name"}
              </MenuItem>
            ))}
            <AddDataSourceMenuItem
              providerName={data.providerName}
              onAuthSuccess={() => {}}
            >
              Add {logos[data.providerName]?.name}
            </AddDataSourceMenuItem>
          </Select>
        </FormControl>
        <FormControl fullWidth>
          <InputLabel id="demo-simple-select-label-activity" required>
            Activity
          </InputLabel>
          <Select
            labelId="demo-simple-select-label-activity"
            id="demo-simple-select-activity"
            fullWidth
            required
            value={activity}
            label="Activity"
            onChange={handleChangeActivity}
          >
            {activityList?.map((item) => (
              <MenuItem value={item} key={item}>
                {item === "events"
                  ? "Fetch events"
                  : capitalizeFirstLetter(item)}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
        {activity === "write" && (
          <>
            <Stack direction="row" spacing={1} alignItems="center">
              <AntSwitch
                checked={silent}
                onChange={() => setSilent(!silent)}
                inputProps={{ "aria-label": "silent mode" }}
              />
              <Typography>Silent</Typography>
              <Tooltip title="Book the appointment and don't send any message">
                <InfoOutlinedIcon sx={{ cursor: "pointer" }} />
              </Tooltip>
            </Stack>

            <Stack direction="row" spacing={1} alignItems="center">
              <AntSwitch
                checked={cancelEvent}
                onChange={() => setCancelEvent(!cancelEvent)}
                inputProps={{ "aria-label": "cancel event" }}
              />
              <Typography>Allow Cancel Events</Typography>
              <Tooltip title="Enable the ability to cancel calendar events">
                <InfoOutlinedIcon sx={{ cursor: "pointer" }} />
              </Tooltip>
            </Stack>

            <Stack direction="row" spacing={1} alignItems="center">
              <AntSwitch
                checked={rescheduleEvent}
                onChange={() => setRescheduleEvent(!rescheduleEvent)}
                inputProps={{ "aria-label": "reschedule event" }}
              />
              <Typography>Allow Reschedule Events</Typography>
              <Tooltip title="Enable the ability to reschedule calendar events">
                <InfoOutlinedIcon sx={{ cursor: "pointer" }} />
              </Tooltip>
            </Stack>
          </>
        )}
      </FormControl>
    </div>
  );
};

export default GoogleCalendarInput;
