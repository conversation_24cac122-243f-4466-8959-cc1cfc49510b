// File: components/AddDataSourceMenuItem.tsx
import React, { useState } from "react";
import { Button } from "@mui/material";
import { CONSTANTS } from "@/helpers/constants";
import OAuthProvider from "./providers/OAuthProvider";
import DialogProvider from "./providers/DialogProvider";
import { IoIosAddCircle } from "react-icons/io";
import { blue } from "@mui/material/colors";

interface AddDataSourceMenuItemProps {
  providerName: string;
  children: React.ReactNode;
  onAuthSuccess: (data: any) => void;
}

const AddDataSourceMenuItem: React.FC<AddDataSourceMenuItemProps> = ({
  providerName,
  children,
  onAuthSuccess,
}) => {
  const [isProviderActive, setIsProviderActive] = useState(false);

  const handleClick = () => {
    setIsProviderActive(true);
  };

  const handleProviderClose = () => {
    setIsProviderActive(false);
  };

  const renderProvider = () => {
    if (!isProviderActive) return null;

    if (isOAuthProvider(providerName)) {
      return (
        <OAuthProvider
          providerName={providerName}
          onAuthSuccess={(data) => {
            onAuthSuccess(data);
            handleProviderClose();
          }}
        />
      );
    } else {
      return (
        <DialogProvider
          providerName={providerName}
          onAuthSuccess={(data) => {
            onAuthSuccess(data);
            handleProviderClose();
          }}
          onClose={handleProviderClose}
        />
      );
    }
  };

  return (
    <>
      <Button
        variant="text"
        fullWidth
        startIcon={<IoIosAddCircle />}
        sx={{ color: blue[700] }}
        onClick={handleClick}
      >
        {children}
      </Button>
      {renderProvider()}
    </>
  );
};

// Helper function to determine if a provider uses OAuth
const isOAuthProvider = (providerName: string): boolean => {
  const oAuthProviders = [
    CONSTANTS.PROVIDERS.GHL_CALENDAR,
    CONSTANTS.PROVIDERS.GHL_CHANNEL,
    CONSTANTS.PROVIDERS.SLACK,
  ];
  return oAuthProviders.includes(providerName);
};

export default AddDataSourceMenuItem;
