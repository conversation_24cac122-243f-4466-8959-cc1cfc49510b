import React from "react";
import useHighLevelConnect from "@/hooks/useHighLevelConnect";
import useSlackConnect from "@/hooks/useSlackConnect";
import useHighLevelChannelConnect from "@/hooks/useHighLevelChannelConnect";
import { CONSTANTS } from "@/helpers/constants";

interface OAuthProviderProps {
  providerName: string;
  onAuthSuccess: (data: any) => void;
}

const OAuthProvider: React.FC<OAuthProviderProps> = ({
  providerName,
  onAuthSuccess,
}) => {
  const { handleConnectClick: handleGhlCalendarConnectClick } =
    useHighLevelConnect();
  const { handleConnectClick: handleGhlChannelConnectClick } =
    useHighLevelChannelConnect();
  const { handleConnectClick: handleSlackConnectClick } = useSlackConnect();

  const handleConnect = () => {
    switch (providerName) {
      case CONSTANTS.PROVIDERS.GHL_CALENDAR:
        handleGhlCalendarConnectClick();
        break;
      case CONSTANTS.PROVIDERS.GHL_CHANNEL:
        handleGhlChannelConnectClick();
        break;
      case CONSTANTS.PROVIDERS.SLACK:
        handleSlackConnectClick();
        break;
      default:
        console.error(`Unsupported OAuth provider: ${providerName}`);
    }
  };

  React.useEffect(() => {
    handleConnect();
  }, [providerName]);

  return null; // OAuth providers don't render anything
};

export default OAuthProvider;
