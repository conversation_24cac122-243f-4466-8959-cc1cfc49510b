import React from "react";
import {
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Checkbox,
  FormControlLabel,
} from "@mui/material";
import useWebsiteScrapper from "@/hooks/useWebsiteScrapper";

interface WebScrapingDialogProps {
  onSuccess: (data: any) => void;
  onClose: () => void;
}

const WebScrapingDialog: React.FC<WebScrapingDialogProps> = ({
  onSuccess,
  onClose,
}) => {
  const {
    urls,
    loading,
    currentStep,
    checkedValues,
    parentLink,
    handleChange,
    handleNextStep,
    handleFinish,
    handleGoBack,
    setParentLink,
  } = useWebsiteScrapper();

  return (
    <>
      <DialogTitle>{`Set up Website Scraping - Step ${currentStep}`}</DialogTitle>
      <DialogContent>
        {currentStep === 1 && (
          <TextField
            autoFocus
            margin="dense"
            id="url"
            label="Website URL"
            type="text"
            fullWidth
            variant="outlined"
            value={parentLink}
            onChange={(e) => setParentLink(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === "a" || e.key === "h") {
                e.stopPropagation();
              }
            }}
          />
        )}
        {currentStep === 2 && (
          <div>
            <p>Select the URLs you want to scrape:</p>
            {urls.map((url, index) => (
              <FormControlLabel
                key={index}
                control={
                  <Checkbox
                    checked={checkedValues.includes(url)}
                    onChange={handleChange}
                    name={url}
                  />
                }
                label={url}
              />
            ))}
          </div>
        )}
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
        {currentStep > 1 && (
          <Button onClick={handleGoBack} disabled={loading}>
            Back
          </Button>
        )}
        {currentStep < 2 ? (
          <Button onClick={handleNextStep} disabled={loading || !parentLink}>
            Next
          </Button>
        ) : (
          <Button
            onClick={() => {
              handleFinish();
              onClose();
            }}
            disabled={loading || checkedValues.length === 0}
          >
            Finish
          </Button>
        )}
      </DialogActions>
    </>
  );
};

export default WebScrapingDialog;
