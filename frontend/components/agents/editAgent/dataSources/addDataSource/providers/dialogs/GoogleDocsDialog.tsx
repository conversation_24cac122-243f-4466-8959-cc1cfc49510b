import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  FormControl,
  Stack,
  TextField,
} from "@mui/material";
import ButtonSpinner from "@/components/general/buttons/spinner";
import useGoogleDocIntegration from "@/hooks/useGoogleDocsIntegration";

interface GoogleDocsIntegrationDialogProps {
  onSuccess: (data: any) => void;
  onClose: () => void;
}

const GoogleDocIntegrationDialog: React.FC<
  GoogleDocsIntegrationDialogProps
> = ({ onSuccess, onClose }) => {
  const { loading, docLink, handleDocLinkChange, handleSubmit } =
    useGoogleDocIntegration(onSuccess);

  const handleFormSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    handleSubmit(e);
    onClose();
  };

  return (
    <>
      <DialogTitle>{`Set up Google Docs Integration`}</DialogTitle>
      <DialogContent>
        <div className="options">
          <form onSubmit={handleFormSubmit}>
            <FormControl fullWidth>
              <TextField
                variant="outlined"
                label="Google Doc Link"
                type="url"
                value={docLink}
                onChange={handleDocLinkChange}
                required
                onKeyDown={(e) => {
                  e.stopPropagation();
                }}
                helperText="For Capri to have access to the Google Doc data, you must either set the share settings so that anyone with the link can open it or add the following email as a collaborator: <EMAIL>"
              />
            </FormControl>
            <Stack
              direction="row"
              justifyContent="flex-end"
              gap={2}
              marginTop={2}
            >
              <Button
                variant="outlined"
                color="error"
                onClick={() => onClose()}
              >
                Cancel
              </Button>
              <Button variant="contained" type="submit" disabled={loading}>
                Submit
                {loading && <ButtonSpinner />}
              </Button>
            </Stack>
          </form>
        </div>
      </DialogContent>
    </>
  );
};

export default GoogleDocIntegrationDialog;
