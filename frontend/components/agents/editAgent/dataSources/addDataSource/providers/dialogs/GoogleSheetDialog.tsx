import React, { useState } from "react";
import {
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Stack,
} from "@mui/material";
import useGoogleSheetIntegration from "@/hooks/useGoogleSheetConnect";

interface GoogleSheetIntegrationDialogProps {
  onSuccess: (data: any) => void;
  onClose: () => void;
}

const GoogleSheetIntegrationDialog: React.FC<
  GoogleSheetIntegrationDialogProps
> = ({ onSuccess, onClose }) => {
  const {
    sheet,
    sheetNameList,
    loading,
    currentStep,
    handleSheetIdChange,
    handleSheetNameChange,
    handleSheetHeaderChange,
    handleSubmit,
    handleFormLink,
    isValidRange,
  } = useGoogleSheetIntegration();

  return (
    <>
      <DialogTitle>{`Set up Google Sheet Integration - Step ${currentStep}`}</DialogTitle>
      <DialogContent>
        {currentStep === 1 && (
          <TextField
            autoFocus
            margin="dense"
            id="sheetId"
            label="Sheet Link"
            type="url"
            fullWidth
            variant="outlined"
            value={sheet.sheetId || ""}
            onChange={handleSheetIdChange}
            required
            onKeyDown={(e) => {
              e.stopPropagation();
            }}
            helperText="Ensure the sheet is accessible <NAME_EMAIL> as a collaborator"
          />
        )}
        {currentStep === 2 && (
          <Stack direction="column" spacing={2}>
            <FormControl fullWidth>
              <InputLabel id="sheet-name-label">Sheet name</InputLabel>
              <Select
                labelId="sheet-name-label"
                id="sheet-name"
                value={sheet.sheetName || ""}
                label="Sheet name"
                required
                onChange={handleSheetNameChange}
              >
                {sheetNameList.map((item) => (
                  <MenuItem
                    value={item}
                    key={item}
                    selected={sheetNameList.length === 1}
                  >
                    {item}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
            <TextField
              variant="outlined"
              label="Headers"
              type="text"
              value={sheet.headers || ""}
              onChange={handleSheetHeaderChange}
              error={!isValidRange(sheet.headers || "")}
              helperText={
                !isValidRange(sheet.headers || "")
                  ? "Invalid range format. Must be like A1:B1."
                  : "The rows which contain your data. For QnA sheets, it is generally A1:B1"
              }
              required
            />
          </Stack>
        )}
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
        {currentStep > 1 && (
          <Button onClick={() => handleFormLink()} disabled={loading}>
            Back
          </Button>
        )}
        {currentStep < 2 ? (
          <Button
            onClick={() => handleFormLink()}
            disabled={loading || !sheet.sheetId}
          >
            Next
          </Button>
        ) : (
          <Button
            onClick={async () => {
              await handleSubmit();
              onSuccess(sheet);
              onClose();
            }}
            disabled={
              loading || !sheet.sheetName || !isValidRange(sheet.headers || "")
            }
          >
            Finish
          </Button>
        )}
      </DialogActions>
    </>
  );
};

export default GoogleSheetIntegrationDialog;
