import React from "react";
import {
  DialogTitle,
  DialogContent,
  DialogA<PERSON>,
  Button,
} from "@mui/material";

interface CustomProviderDialogProps {
  providerName: string;
  onSuccess: (data: any) => void;
  onClose: () => void;
}

const CustomProviderDialog: React.FC<CustomProviderDialogProps> = ({
  providerName,
  onSuccess,
  onClose,
}) => {
  const handleFinish = () => {
    onSuccess({ provider: providerName, customData: "Example data" });
  };

  return (
    <>
      <DialogTitle>{`Set up ${providerName}`}</DialogTitle>
      <DialogContent>
        <div>Custom setup for {providerName}</div>
        {/* Add custom fields and logic for this provider */}
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
        <Button onClick={handleFinish}>Finish</Button>
      </DialogActions>
    </>
  );
};

export default CustomProviderDialog;
