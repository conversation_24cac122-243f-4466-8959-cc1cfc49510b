import React from "react";
import {
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  CircularProgress,
  FormControlLabel,
  Checkbox,
  Link,
  Typography,
  ListItem,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  List,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Box,
  Grid,
} from "@mui/material";
import { useGoogleCalendarIntegration } from "@/hooks/useGoogleCalendarIntegration";
import ChooseCalendar, {
  SelectedListItem,
} from "@/components/general/auth/google/ChooseCalendar";
import CalendarAdvancedSettings from "@/components/general/auth/google/CalendarAdvancedSettings";
import CheckIcon from "@mui/icons-material/Check";
import CalendarAdvancedSettingsInput from "@/components/general/auth/google/CalendarAdvancedSettingsInput";

interface GoogleCalendarIntegrationDialogProps {
  onSuccess: (data: any) => void;
  onClose: () => void;
}

const GoogleCalendarIntegrationDialog: React.FC<
  GoogleCalendarIntegrationDialogProps
> = ({ onSuccess, onClose }) => {
  const {
    calendars,
    selectedCalendar,
    calendarAdvancedDetails,
    currentStep,
    checked,
    loading,
    handleGoogleLogin,
    handleCheckboxChange,
    handleCalendarSelect,
    handleAdvancedSettingsChange,
    handleGoToAdvancedSettings,
    handleSubmit,
    resetIntegration,
  } = useGoogleCalendarIntegration();

  const handleClose = () => {
    resetIntegration();
    onClose();
  };

  const handleFinish = () => {
    handleSubmit();
    onSuccess(selectedCalendar);
    onClose();
  };

  return (
    <>
      <DialogTitle>{`Set up Google Calendar Integration - Step ${currentStep}`}</DialogTitle>
      <DialogContent>
        {currentStep === 1 && (
          <>
            <Typography variant="body1" gutterBottom>
              To integrate your Google Calendar, please sign in with your Google
              account and grant the necessary permissions.
            </Typography>
            <FormControlLabel
              control={
                <Checkbox
                  checked={checked}
                  onChange={handleCheckboxChange}
                  color="primary"
                />
              }
              label={
                <>
                  I acknowledge that my free/busy information from Google
                  Calendar will be shared with AI tools. See our{" "}
                  <Link
                    href={`${process.env.NEXT_PUBLIC_FRONTEND_URL}/privacy`}
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    Privacy Policy
                  </Link>{" "}
                  for details. To stop sharing, remove this calendar integration
                  from your organization settings.
                </>
              }
            />
            <Button
              variant="contained"
              color="primary"
              onClick={handleGoogleLogin}
              disabled={loading || !checked}
              startIcon={loading ? <CircularProgress size={20} /> : null}
              fullWidth
              style={{ marginTop: "20px" }}
            >
              {loading ? "Loading..." : "Sign in with Google"}
            </Button>
          </>
        )}
        {currentStep === 2 && (
          //   <FormControl fullWidth>
          //     <InputLabel id="calendar-select-label">Select Calendar</InputLabel>
          //     <Select
          //       labelId="calendar-select-label"
          //       id="calendar-select"
          //       value={selectedCalendar?.id || ""}
          //       label="Select Calendar"
          //       onChange={(e) => handleCalendarSelect(e.target.value)}
          //     >
          //       {calendars.map((calendar) => (
          //         <MenuItem key={calendar.id} value={calendar.id}>
          //           {calendar.summary}
          //         </MenuItem>
          //       ))}
          //     </Select>
          //   </FormControl>
          //   <ChooseCalendar
          //     calendars={calendars}
          //     onCalendarSelect={(id: string) => handleCalendarSelect(id)}
          //   />
          <List>
            {calendars.map((calendar) => {
              const ListItemComponent =
                calendar.id === selectedCalendar?.id
                  ? SelectedListItem
                  : ListItem;
              return (
                <ListItemComponent
                  key={calendar.id}
                  sx={{
                    cursor: "pointer",
                  }}
                  onClick={() => handleCalendarSelect(calendar.id)}
                >
                  <ListItemText
                    primary={calendar.summary}
                    secondary={`Access Role: ${calendar.accessRole}, Timezone: ${calendar.timeZone}`}
                  />
                  {calendar.id === selectedCalendar?.id && (
                    <ListItemSecondaryAction>
                      <IconButton edge="end">
                        <CheckIcon />
                      </IconButton>
                    </ListItemSecondaryAction>
                  )}
                </ListItemComponent>
              );
            })}
          </List>
        )}
        {currentStep === 3 && (
          //   <Typography variant="body1">
          //     Google Calendar successfully integrated. You can now use this
          //     calendar in your application.
          //   </Typography>
          //   <CalendarAdvancedSettings
          //     calendar={selectedCalendar}
          //     details={calendarAdvancedDetails}
          //     onDetailsChange={handleAdvancedSettingsChange}
          //   />
          <Grid container spacing={2} my={1}>
            <CalendarAdvancedSettingsInput
              advancedSettings={calendarAdvancedDetails}
              onAdvancedSettingsChange={handleAdvancedSettingsChange}
            />
          </Grid>
        )}
      </DialogContent>
      <DialogActions>
        <Button onClick={handleClose}>Cancel</Button>
        {currentStep === 2 && (
          <Button
            onClick={handleGoToAdvancedSettings}
            color="primary"
            disabled={!selectedCalendar}
          >
            Next
          </Button>
        )}
        {currentStep === 3 && (
          <Button
            onClick={async () => {
              await handleSubmit();
              onClose();
            }}
            color="primary"
            disabled={loading}
          >
            Finish{loading && "ing..."}
          </Button>
        )}
      </DialogActions>
    </>
  );
};

export default GoogleCalendarIntegrationDialog;
