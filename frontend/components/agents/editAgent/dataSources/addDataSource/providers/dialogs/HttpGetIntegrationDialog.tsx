import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON>ircularProgress,
  Dialog<PERSON>ontent,
  DialogTitle,
  Stack,
} from "@mui/material";
import { ImSpinner8 } from "react-icons/im";
import HttpUrl from "@/components/settings/integrations/http/http/HttpUrl";
import HttpTab from "@/components/settings/integrations/http/http/HttpTab";
import HttpTabContent from "@/components/settings/integrations/http/http/HttpTabContent";
import HttpParams from "@/components/settings/integrations/http/http/HttpParams";
import HttpAuthorization from "@/components/settings/integrations/http/http/HttpAuthorization";
import HttpHeaders from "@/components/settings/integrations/http/http/HttpHeaders";
import HttpResponse from "@/components/settings/integrations/http/http/HttpResponse";
import useHttpGetIntegration from "@/hooks/useHttpGetIntegration";
import HttpName from "@/components/settings/integrations/http/http/HttpName";

interface HttpGetIntegrationDialogProps {
  accountId?: string;
  onClose: () => void;
}

const HttpGetIntegrationDialog: React.FC<HttpGetIntegrationDialogProps> = ({
  accountId,
  onClose,
}) => {
  const {
    httpRequest,
    setHttpRequest,
    currentTab,
    setCurrentTab,
    response,
    loading,
    saveLoading,
    error,
    initialLoading,
    handleSendRequest,
    handleSubmit,
  } = useHttpGetIntegration(accountId);

  if (initialLoading) {
    return (
      <div className="w-full flex justify-center mt-10">
        <CircularProgress />
      </div>
    );
  }

  return (
    <>
      <DialogTitle>{`${
        accountId ? "Edit" : "Create"
      } HTTP GET Integration`}</DialogTitle>
      <DialogContent>
        <div className="w-[900px]">
          <HttpName
            name={httpRequest.name}
            onNameChange={(name: string) =>
              setHttpRequest({ ...httpRequest, name })
            }
          />
          <HttpUrl
            type="GET"
            url={httpRequest.url}
            loading={loading}
            urlOnChange={(url) => setHttpRequest({ ...httpRequest, url })}
            onFire={handleSendRequest}
          />
          <HttpTab
            currentTab={currentTab}
            onTabChange={(tab) => setCurrentTab(tab)}
          />
          <HttpTabContent />
          {currentTab === "Params" && (
            <HttpParams
              url={httpRequest.url}
              queryParams={httpRequest.queryParameters}
              pathVariables={httpRequest.pathVariables}
              onQueryParamChange={(queryParam) =>
                setHttpRequest({ ...httpRequest, queryParameters: queryParam })
              }
              onPathVariableChange={(pathVariable) =>
                setHttpRequest({ ...httpRequest, pathVariables: pathVariable })
              }
            />
          )}
          {currentTab === "Authorization" && (
            <HttpAuthorization
              authorizationType={httpRequest.authorizationType}
              apiKey={httpRequest.apiKey}
              onAuthorizationTypeChange={(authType) =>
                setHttpRequest({ ...httpRequest, authorizationType: authType })
              }
              onApiKeyChange={(newApiKey) =>
                setHttpRequest({ ...httpRequest, apiKey: newApiKey })
              }
            />
          )}
          {currentTab === "Headers" && (
            <HttpHeaders
              headers={httpRequest.headers}
              onHeadersChange={(header) =>
                setHttpRequest({ ...httpRequest, headers: header })
              }
            />
          )}
          {currentTab === "Response" && (
            <HttpResponse
              response={response}
              responseBuilder={httpRequest.responseBuilder}
              setResponseBuilder={(responseBuilder) =>
                setHttpRequest({
                  ...httpRequest,
                  responseBuilder: responseBuilder,
                })
              }
            />
          )}
        </div>
        {error && <div className="text-red-500 mt-2">{error}</div>}
        <Stack direction="row" justifyContent="flex-end" spacing={2} mt={2}>
          <Button variant="outlined" onClick={onClose}>
            Cancel
          </Button>
          <Button
            variant="contained"
            onClick={async () => {
              await handleSubmit();
              onClose();
            }}
            disabled={saveLoading}
          >
            Save {saveLoading && <ImSpinner8 className="animate-spin ml-2" />}
          </Button>
        </Stack>
      </DialogContent>
    </>
  );
};

export default HttpGetIntegrationDialog;
