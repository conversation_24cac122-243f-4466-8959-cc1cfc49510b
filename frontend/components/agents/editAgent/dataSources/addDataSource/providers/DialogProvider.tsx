import React, { useState } from "react";
import { Dialog } from "@mui/material";
import WebScrapingDialog from "./dialogs/WebScrapingDialog";
import CustomProviderDialog from "./dialogs/CustomProviderDialog";
import { CONSTANTS } from "@/helpers/constants";
import GoogleSheetIntegrationDialog from "./dialogs/GoogleSheetDialog";
import GoogleDocIntegrationDialog from "./dialogs/GoogleDocsDialog";
import HttpGetIntegrationDialog from "./dialogs/HttpGetIntegrationDialog";
import HttpPostIntegrationDialog from "./dialogs/HttpPostIntegrationDialog";
import GoogleCalendarIntegrationDialog from "./dialogs/GoogleCalendarDialog";

interface DialogProviderProps {
  providerName: string;
  onAuthSuccess: (data: any) => void;
  onClose: () => void;
}

const DialogProvider: React.FC<DialogProviderProps> = ({
  providerName,
  onAuthSuccess,
  onClose,
}) => {
  const [isOpen, setIsOpen] = useState(true);

  const handleClose = () => {
    setIsOpen(false);
    onClose();
  };

  const handleSuccess = (data: any) => {
    onAuthSuccess(data);
    handleClose();
  };

  const renderDialogContent = () => {
    switch (providerName) {
      case CONSTANTS.PROVIDERS.WEBSITE:
        return (
          <WebScrapingDialog onSuccess={handleSuccess} onClose={handleClose} />
        );
      case CONSTANTS.PROVIDERS.GOOGLE_SHEET:
        return (
          <GoogleSheetIntegrationDialog
            onSuccess={handleSuccess}
            onClose={handleClose}
          />
        );
      case CONSTANTS.PROVIDERS.GOOGLE_DOCS:
        return (
          <GoogleDocIntegrationDialog
            onSuccess={handleSuccess}
            onClose={handleClose}
          />
        );
      case CONSTANTS.PROVIDERS.HTTP_GET:
        return <HttpGetIntegrationDialog onClose={handleClose} />;
      case CONSTANTS.PROVIDERS.HTTP_POST:
        return <HttpPostIntegrationDialog onClose={handleClose} />;
      case CONSTANTS.PROVIDERS.GOOGLE_CALENDAR:
        return (
          <GoogleCalendarIntegrationDialog
            onSuccess={handleSuccess}
            onClose={handleClose}
          />
        );
      // Add more case statements for other dialog-based providers
      default:
        return (
          <CustomProviderDialog
            providerName={providerName}
            onSuccess={handleSuccess}
            onClose={handleClose}
          />
        );
    }
  };

  return (
    <Dialog
      open={isOpen}
      onClose={handleClose}
      disableEnforceFocus
      disableAutoFocus
      maxWidth={[CONSTANTS.PROVIDERS.HTTP_GET, CONSTANTS.PROVIDERS.HTTP_POST].includes(providerName) ? "lg" : "sm"}
    >
      {renderDialogContent()}
    </Dialog>
  );
};

export default DialogProvider;
