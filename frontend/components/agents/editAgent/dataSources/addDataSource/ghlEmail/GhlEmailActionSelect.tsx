import { IGhlEmailAction, agentsAction } from "@/slices/agents/agentsSlice";
import { IAdvancedSettings } from "../../addDataSource";
import React, { useEffect } from "react";
import {
  Box,
  Chip,
  FormControl,
  FormHelperText,
  InputLabel,
  MenuItem,
  Select,
  SelectChangeEvent,
  Stack,
  Tooltip,
  Typography,
} from "@mui/material";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store";
import { CONSTANTS } from "@/helpers/constants";
import { AntSwitch } from "../ghlChannelInputs";
import AddDataSourceMenuItem from "../AddDataSourceMenuItem";
import { logos } from "@/helpers/images";
import InfoOutlinedIcon from "@mui/icons-material/InfoOutlined";
import { addAgentAction } from "@/requests/agents/actions/addAgentAction";
import { useRouter } from "next/router";
import { editAgentAction } from "@/requests/agents/actions/editAgentAction";
import EnhancedTextField from "../../TextField";

interface IProp {
  data: IGhlEmailAction;
  submitted: boolean;
  setSubmit: () => void;
  type: "add" | "edit";
  onCloseFn: () => void;
  isAdvancedSettings: boolean;
  advancedSettings: IAdvancedSettings;
  actionId?: string;
}

type PhoneId = {
  _id: string;
  orgId: string;
  phoneId: string;
  name: string;
  phoneNumber: string;
  provider: string;
};

const GhlEmailActionSelect: React.FC<IProp> = ({
  data,
  submitted,
  setSubmit,
  onCloseFn,
  type,
  isAdvancedSettings,
  advancedSettings,
  actionId,
}) => {
  const dispatch = useDispatch();
  const router = useRouter();
  const accountList =
    useSelector((state: RootState) => state.channelsList.accountsList).filter(
      (item) => item.providerName === CONSTANTS.PROVIDERS.GHL_CHANNEL
    ) || [];
  const { agentId } = router.query as { agentId: string };

  const [accountId, setAccountId] = React.useState(data.accountId);
  const [activity, setActivity] = React.useState("write");
  const [subject, setSubject] = React.useState(data.ghlEmail?.subject || "");
  const [text, setText] = React.useState(data.ghlEmail?.text || "");
  const [emailType, setEmailType] = React.useState(
    data.ghlEmail?.type || "hardcoded"
  );
  const [silent, setSilent] = React.useState(data?.silent ?? true);

  const activityList = ["write"];

  const handleChangeAccountId = (event: SelectChangeEvent) => {
    setAccountId(event.target.value as string);
  };
  const handleChangeActivity = (event: SelectChangeEvent) => {
    setActivity(event.target.value as "write");
  };

  const handleChangeEmailType = (
    event: SelectChangeEvent<IGhlEmailAction["ghlEmail"]["type"]>
  ) => {
    setEmailType(event.target.value as IGhlEmailAction["ghlEmail"]["type"]);
  };

  const handleSubjectChange = (newValue: string) => {
    setSubject(newValue);
  };

  const handleTextChange = (newValue: string) => {
    setText(newValue);
  };

  useEffect(() => {
    if (!submitted) return;
    const action: IGhlEmailAction = {
      activity: "write",
      providerName: CONSTANTS.PROVIDERS.GHL_EMAIL,
      accountId: accountId || "",
      accountName:
        accountList.find((account) => account.accountId === accountId)?.name ??
        "",
      promptContent: data.promptContent ?? "",
      advancedSettings,
      isAdvancedSettings,
      includeMainPromptDetails: { includeMainPrompt: false, mainPromptId: "" },
      augmentedQueryData: data.augmentedQueryData,
      ghlEmail: {
        text,
        type: emailType,
        subject,
      },
      silent,
      evalType: data.evalType,
      confidenceValue: data.confidenceValue,
    };
    if (type === "add") {
      const addGetAction = async () => {
        // @ts-ignore
        const response = await addAgentAction({ agentId, action }, dispatch);
        if (response?.status === 201) {
          dispatch(agentsAction.actionAdded(response.data.action));
        }
        setSubmit();
        onCloseFn();
      };
      addGetAction();
    } else if (type === "edit") {
      const editGetAction = async () => {
        const response = await editAgentAction(
          // @ts-ignore
          { agentId, actionId: actionId as string, action },
          dispatch
        );
        if (response?.status === 200) {
          dispatch(
            agentsAction.actionEdited({
              ...action,
              actionId: actionId as string,
            })
          );
        }
        setSubmit();
        onCloseFn();
      };
      editGetAction();
    }
  }, [submitted]);
  return (
    <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
      <FormControl fullWidth>
        <InputLabel id="demo-simple-select-label-account" required>
          Account
        </InputLabel>
        <Select
          labelId="demo-simple-select-label-account"
          id="demo-simple-select-account"
          fullWidth
          value={accountId}
          label="Account"
          onChange={handleChangeAccountId}
          required
        >
          {accountList?.map((item) => (
            <MenuItem
              value={item.accountId}
              key={"Select_activity_key" + item.accountId}
            >
              {item.name || "No name"}
            </MenuItem>
          ))}
          <AddDataSourceMenuItem
            providerName={data.providerName}
            onAuthSuccess={() => {}}
          >
            Add {logos[data.providerName]?.name}
          </AddDataSourceMenuItem>
        </Select>
      </FormControl>
      <FormControl fullWidth>
        <InputLabel id="demo-simple-select-label-activity" required>
          Activity
        </InputLabel>
        <Select
          labelId="demo-simple-select-label-activity"
          id="demo-simple-select-activity"
          fullWidth
          required
          value={activity}
          label="Activity"
          onChange={handleChangeActivity}
        >
          {activityList?.map((item) => (
            <MenuItem value={item} key={item}>
              {item === "read" ? "Read" : "Write"}
            </MenuItem>
          ))}
        </Select>
      </FormControl>
      <FormControl fullWidth>
        <InputLabel id="email-type" required>
          Email Type
        </InputLabel>
        <Select
          labelId="email-type"
          id="email-type-select"
          fullWidth
          required
          value={emailType}
          label="Email Type"
          onChange={handleChangeEmailType}
        >
          <MenuItem value={"predictive"}>Predictive</MenuItem>
          <MenuItem value={"hardcoded"}>Hardcoded</MenuItem>
        </Select>
      </FormControl>
      {emailType === "hardcoded" && (
        <FormControl>
          <EnhancedTextField
            fullWidth
            label="Subject"
            multiline
            rows={1}
            required
            id={`Prompt_content_${crypto.randomUUID()}`}
            value={subject}
            onChange={handleSubjectChange}
          />
        </FormControl>
      )}
      {emailType === "hardcoded" && (
        <FormControl>
          <EnhancedTextField
            fullWidth
            label="Text"
            multiline
            rows={4}
            required
            id={`Prompt_content_${crypto.randomUUID()}`}
            value={text}
            onChange={handleTextChange}
          />
        </FormControl>
      )}
      <FormControl>
        <Stack direction="row" spacing={1} alignItems="center">
          <AntSwitch
            defaultChecked
            checked={silent}
            onChange={() => setSilent(!silent)}
            inputProps={{ "aria-label": "ant design" }}
          />
          <Typography>Silent</Typography>
          <Tooltip title="Fire the action and don’t send any message">
            <InfoOutlinedIcon sx={{ cursor: "pointer" }} />
          </Tooltip>
        </Stack>
      </FormControl>
    </Box>
  );
};

export default GhlEmailActionSelect;
