import { ISmsAction, agentsAction } from "@/slices/agents/agentsSlice";
import { IAdvancedSettings } from "../../addDataSource";
import React, { useEffect } from "react";
import {
  Box,
  Chip,
  FormControl,
  FormHelperText,
  InputLabel,
  MenuItem,
  Select,
  SelectChangeEvent,
  Stack,
  Tooltip,
  Typography,
} from "@mui/material";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store";
import { CONSTANTS } from "@/helpers/constants";
import { AntSwitch } from "../ghlChannelInputs";
import AddDataSourceMenuItem from "../AddDataSourceMenuItem";
import { logos } from "@/helpers/images";
import InfoOutlinedIcon from "@mui/icons-material/InfoOutlined";
import { addAgentAction } from "@/requests/agents/actions/addAgentAction";
import { useRouter } from "next/router";
import { editAgentAction } from "@/requests/agents/actions/editAgentAction";
import EnhancedTextField from "../../TextField";
import { fetchPhones } from "@/requests/organizations/basic/fetchPhones";
import PhoneDialog from "@/components/agents/voice/PhoneDialog";
import Add from "@mui/icons-material/Add";

interface IProp {
  data: ISmsAction;
  submitted: boolean;
  setSubmit: () => void;
  type: "add" | "edit";
  onCloseFn: () => void;
  isAdvancedSettings: boolean;
  advancedSettings: IAdvancedSettings;
  actionId?: string;
}

type PhoneId = {
  _id: string;
  orgId: string;
  phoneId: string;
  name: string;
  phoneNumber: string;
  provider: string;
};

const SmsActionSelect: React.FC<IProp> = ({
  data,
  submitted,
  setSubmit,
  onCloseFn,
  type,
  isAdvancedSettings,
  advancedSettings,
  actionId,
}) => {
  const dispatch = useDispatch();
  const router = useRouter();
  const accountList =
    useSelector((state: RootState) => state.channelsList.accountsList).filter(
      (item) => item.providerName === CONSTANTS.PROVIDERS.GHL_CHANNEL
    ) || [];
  const { agentId } = router.query as { agentId: string };
  const orgId = localStorage.getItem("orgId") as string;

  const [accountId, setAccountId] = React.useState(data.accountId);
  const [activity, setActivity] = React.useState("write");
  const [phoneIds, setPhoneIds] = React.useState<PhoneId[]>([]);
  const [selectedPhoneId, setSelectedPhoneId] = React.useState(
    data.sms?.phoneId
  );
  const [text, setText] = React.useState(data.sms?.text || "");
  const [smsType, setSmsType] = React.useState(data.sms?.type || "hardcoded");
  const [silent, setSilent] = React.useState(data?.silent ?? true);
  const [isPhoneDialogOpen, setIsPhoneDialogOpen] = React.useState(false);

  const activityList = ["write"];

  const handleChangeAccountId = (event: SelectChangeEvent) => {
    setAccountId(event.target.value as string);
  };
  const handleChangeActivity = (event: SelectChangeEvent) => {
    setActivity(event.target.value as "write");
  };

  const handleChangeSmsType = (
    event: SelectChangeEvent<ISmsAction["sms"]["type"]>
  ) => {
    setSmsType(event.target.value as ISmsAction["sms"]["type"]);
  };

  const handleSelectedPhoneIdChange = (event: SelectChangeEvent<string>) => {
    setSelectedPhoneId(event.target.value);
  };

  const handleTextChange = (newValue: string) => {
    setText(newValue);
  };

  const handleOpenPhoneDialog = () => {
    setIsPhoneDialogOpen(true);
  };

  const handleClosePhoneDialog = () => {
    setIsPhoneDialogOpen(false);
  };

  useEffect(() => {
    const fetchPhoneIds = async () => {
      const response = await fetchPhones({ orgId }, dispatch);
      if (response.status === 200) {
        const data = response.data;
        setPhoneIds(data);
      }
    };
    fetchPhoneIds();
  }, []);

  useEffect(() => {
    if (!submitted) return;
    const action: ISmsAction = {
      activity: "write",
      providerName: CONSTANTS.PROVIDERS.SMS,
      accountId: accountId ?? "",
      accountName:
        accountList.find((account) => account.accountId === accountId)?.name ??
        "",
      promptContent: data.promptContent ?? "",
      advancedSettings,
      isAdvancedSettings,
      includeMainPromptDetails: { includeMainPrompt: false, mainPromptId: "" },
      augmentedQueryData: data.augmentedQueryData,
      sms: {
        phoneId: selectedPhoneId,
        text,
        type: smsType,
      },
      silent,
      evalType: data.evalType,
      confidenceValue: data.confidenceValue,
    };
    if (type === "add") {
      const addGetAction = async () => {
        // @ts-ignore
        const response = await addAgentAction({ agentId, action }, dispatch);
        if (response?.status === 201) {
          dispatch(agentsAction.actionAdded(response.data.action));
        }
        setSubmit();
        onCloseFn();
      };
      addGetAction();
    } else if (type === "edit") {
      const editGetAction = async () => {
        const response = await editAgentAction(
          // @ts-ignore
          { agentId, actionId: actionId as string, action },
          dispatch
        );
        if (response?.status === 200) {
          dispatch(
            agentsAction.actionEdited({
              ...action,
              actionId: actionId as string,
            })
          );
        }
        setSubmit();
        onCloseFn();
      };
      editGetAction();
    }
  }, [submitted]);
  return (
    <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
      <FormControl fullWidth>
        <InputLabel id="demo-simple-select-label-account" required>
          Account
        </InputLabel>
        <Select
          labelId="demo-simple-select-label-account"
          id="demo-simple-select-account"
          fullWidth
          value={accountId}
          label="Account"
          onChange={handleChangeAccountId}
          required
        >
          {accountList?.map((item) => (
            <MenuItem
              value={item.accountId}
              key={"Select_activity_key" + item.accountId}
            >
              {item.name || "No name"}
            </MenuItem>
          ))}
          <AddDataSourceMenuItem
            providerName={data.providerName}
            onAuthSuccess={() => {}}
          >
            Add {logos[data.providerName]?.name}
          </AddDataSourceMenuItem>
        </Select>
      </FormControl>
      <FormControl fullWidth>
        <InputLabel id="demo-simple-select-label-activity" required>
          Activity
        </InputLabel>
        <Select
          labelId="demo-simple-select-label-activity"
          id="demo-simple-select-activity"
          fullWidth
          required
          value={activity}
          label="Activity"
          onChange={handleChangeActivity}
        >
          {activityList?.map((item) => (
            <MenuItem value={item} key={item}>
              {item === "read" ? "Read" : "Write"}
            </MenuItem>
          ))}
        </Select>
      </FormControl>
      <FormControl fullWidth>
        <InputLabel id="sms-type" required>
          SMS Type
        </InputLabel>
        <Select
          labelId="sms-type"
          id="sms-type-select"
          fullWidth
          required
          value={smsType}
          label="SMS Type"
          onChange={handleChangeSmsType}
        >
          <MenuItem value={"predictive"}>Predictive</MenuItem>
          <MenuItem value={"hardcoded"}>Hardcoded</MenuItem>
        </Select>
      </FormControl>
      {smsType === "hardcoded" && (
        <FormControl>
          <EnhancedTextField
            fullWidth
            label="Text"
            multiline
            rows={4}
            required
            id={`Prompt_content_${crypto.randomUUID()}`}
            value={text}
            onChange={handleTextChange}
          />
        </FormControl>
      )}
      <FormControl>
        <InputLabel id="phone-ids-label">Phone IDs</InputLabel>
        <Select
          labelId="phone-ids-label"
          id="phone-ids"
          value={selectedPhoneId}
          label="Phone IDs"
          onChange={handleSelectedPhoneIdChange}
        >
          {phoneIds.map((item) => (
            <MenuItem value={item._id} key={"key_" + item._id}>
              <Stack direction={"row"} alignItems={"center"} gap={2}>
                {item.name && (
                  <Typography variant="body1" fontWeight={500}>
                    {item.name}
                  </Typography>
                )}
                <Typography variant="body2" color="text.secondary">
                  {item.phoneNumber}
                </Typography>
                <Chip size="small" color="primary" label={item.provider}></Chip>
              </Stack>
            </MenuItem>
          ))}
          <MenuItem onClick={handleOpenPhoneDialog}>
            <Stack direction={"row"} alignItems={"center"} gap={1}>
              <Add /> Add Phone Number
            </Stack>
          </MenuItem>
        </Select>
        <FormHelperText>These are the phone IDs</FormHelperText>
      </FormControl>
      <FormControl>
        <Stack direction="row" spacing={1} alignItems="center">
          <AntSwitch
            defaultChecked
            checked={silent}
            onChange={() => setSilent(!silent)}
            inputProps={{ "aria-label": "ant design" }}
          />
          <Typography>Silent</Typography>
          <Tooltip title="Fire the action and don’t send any message">
            <InfoOutlinedIcon sx={{ cursor: "pointer" }} />
          </Tooltip>
        </Stack>
      </FormControl>
      <PhoneDialog
        isOpen={isPhoneDialogOpen}
        onClose={handleClosePhoneDialog}
        editingIndex={null}
        phoneDetails={[]}
        orgId={orgId}
        agentId={agentId}
        assistantId=""
      />
    </Box>
  );
};

export default SmsActionSelect;
