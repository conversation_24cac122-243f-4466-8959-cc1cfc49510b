// components/GHLChannelInputs.tsx
import {
  IPayload,
  addAgentAction,
} from "@/requests/agents/actions/addAgentAction";
import { editAgentAction } from "@/requests/agents/actions/editAgentAction";
import {
  IActionGhlChannel,
  ICustomFieldData,
  ISingleAction,
  ITagData,
  IStandardFieldData,
  agentsAction,
} from "@/slices/agents/agentsSlice";
import { snackbarActions } from "@/slices/general/snackbar";
import {
  TextField,
  Select,
  MenuItem,
  InputLabel,
  SelectChangeEvent,
  FormControl,
  Button,
  Box,
  Stack,
  Typography,
  styled,
  Switch,
  Autocomplete,
  Chip,
} from "@mui/material";
import { useRouter } from "next/router";
import React, { useEffect } from "react";
import { useDispatch } from "react-redux";
import { IAdvancedSettings } from "../addDataSource";

interface IProp {
  data: IActionGhlChannel;
  submitted: boolean;
  setSubmit: () => void;
  type: "add" | "edit";
  onCloseFn: () => void;
  isAdvancedSettings: boolean;
  advancedSettings: IAdvancedSettings;
  actionId?: string;
  hidePromptContent: boolean;
  setHidePromptContent: (hidePromptContent: boolean) => void;
}

export const AntSwitch = styled(Switch)(({ theme }) => ({
  width: 28,
  height: 16,
  padding: 0,
  display: "flex",
  "&:active": {
    "& .MuiSwitch-thumb": {
      width: 15,
    },
    "& .MuiSwitch-switchBase.Mui-checked": {
      transform: "translateX(9px)",
    },
  },
  "& .MuiSwitch-switchBase": {
    padding: 2,
    "&.Mui-checked": {
      transform: "translateX(12px)",
      color: "#fff",
      "& + .MuiSwitch-track": {
        opacity: 1,
        backgroundColor: theme.palette.mode === "dark" ? "#177ddc" : "#1890ff",
      },
    },
  },
  "& .MuiSwitch-thumb": {
    boxShadow: "0 2px 4px 0 rgb(0 35 11 / 20%)",
    width: 12,
    height: 12,
    borderRadius: 6,
    transition: theme.transitions.create(["width"], {
      duration: 200,
    }),
  },
  "& .MuiSwitch-track": {
    borderRadius: 16 / 2,
    opacity: 1,
    backgroundColor:
      theme.palette.mode === "dark"
        ? "rgba(255,255,255,.35)"
        : "rgba(0,0,0,.25)",
    boxSizing: "border-box",
  },
}));

export const standardFields = [
  "name",
  "firstName",
  "lastName",
  "email",
  "timezone",
  "companyName",
  "phone",
  "address1",
  "city",
  "state",
  "country",
  "postalCode",
  "website",
  "dateOfBirth",
  "gender",
];

const GHLChannelInputs: React.FC<IProp> = ({
  data,
  submitted,
  setSubmit,
  type,
  onCloseFn,
  isAdvancedSettings,
  advancedSettings,
  actionId,
  hidePromptContent,
  setHidePromptContent,
}) => {
  const dispatch = useDispatch();
  const router = useRouter();
  const { agentId } = router.query as { agentId: string };
  const [activity, setActivity] = React.useState(data.activity);
  const [tagDataState, setTagDataState] = React.useState<ITagData>();
  const [customFieldDataState, setCustomFieldDataState] =
    React.useState<ICustomFieldData>({
      fieldKey: "",
      evaluateOn: "everyTurn",
      reply: true,
    });
  const [standardFieldDataState, setStandardFieldDataState] =
    React.useState<IStandardFieldData>({
      fieldKeys: data.metaData?.standardFieldData?.fieldKeys || [],
      evaluateOn: "isEmpty",
      reply: true,
    });
  const [tagValue, setTagValue] = React.useState(
    data.metaData?.tagData?.tagValue || ""
  );
  const [evaluateOn, setEvaluateOn] = React.useState(
    data.metaData?.tagData?.evaluateOn || ""
  );
  const [reply, setReply] = React.useState(!data?.silent);

  const handleChangeTagValue = (event: React.ChangeEvent<HTMLInputElement>) => {
    setTagValue(event.target.value as string);
  };
  const handleChangeActivity = (event: SelectChangeEvent) => {
    setActivity(event.target.value as "tag" | "customField" | "standardField");
  };
  const handleChangeEvaluateOn = (event: SelectChangeEvent) => {
    setEvaluateOn(event.target.value as "everyTurn" | "contactNoTag");
  };

  useEffect(() => {
    setHidePromptContent(activity === "standardField");
  }, [activity, setHidePromptContent]);

  useEffect(() => {
    if (activity === "tag") {
      setTagDataState(data.metaData?.tagData);
    } else if (activity === "customField") {
      setCustomFieldDataState(
        data.metaData?.customFieldData as ICustomFieldData
      );
    } else if (activity === "standardField") {
      setStandardFieldDataState(
        data.metaData?.standardFieldData as IStandardFieldData
      );
    }
  }, [activity]);

  useEffect(() => {
    let metaData;
    if (activity === "tag") {
      metaData = {
        tagData: {
          tagValue,
          evaluateOn: evaluateOn as "everyTurn" | "contactNoTag",
          reply,
        },
      };
    } else if (activity === "customField") {
      metaData = {
        customFieldData: {
          fieldKey: customFieldDataState?.fieldKey as string,
          evaluateOn: customFieldDataState?.evaluateOn as
            | "everyTurn"
            | "isEmpty",
          reply,
        },
      };
    } else if (activity === "standardField") {
      metaData = {
        standardFieldData: {
          fieldKeys: standardFieldDataState?.fieldKeys || [],
          evaluateOn: "isEmpty",
          reply,
        },
      };
    }

    const body = {
      agentId,
      action: {
        providerName: data.providerName,
        promptContent: activity === "standardField" ? "" : data.promptContent,
        activity,
        accountId:
          activity === "standardField"
            ? standardFieldDataState?.fieldKeys.join(",")
            : tagValue || customFieldDataState?.fieldKey,
        accountName:
          activity === "standardField"
            ? standardFieldDataState?.fieldKeys.join(",")
            : tagValue || customFieldDataState?.fieldKey,
        silent: !reply,
        metaData,
        isAdvancedSettings,
        advancedSettings,
        includeMainPromptDetails:
          activity === "standardField"
            ? undefined
            : data.includeMainPromptDetails,
        evalType: data.evalType,
        confidenceValue: data.confidenceValue,
        augmentedQueryData: data.augmentedQueryData,
      },
    };

    const customFieldRegex = /^{{contact\.[a-zA-Z_][a-zA-Z0-9_]*}}$/;
    if (submitted && type === "add") {
      if (
        activity === "customField" &&
        !!!body.action.accountId?.match(customFieldRegex)
      ) {
        dispatch(
          snackbarActions.setSnackbar({
            message: "Please enter valid custom field format",
            type: "error",
          })
        );
        setSubmit();
        return;
      } else if (
        (activity === "tag" &&
          (!!!body.action.metaData?.tagData?.tagValue ||
            !!!body.action.metaData?.tagData?.evaluateOn)) ||
        (activity === "customField" &&
          (!!!body.action.metaData?.customFieldData?.fieldKey ||
            !!!body.action.metaData?.customFieldData?.evaluateOn)) ||
        (activity === "standardField" &&
          standardFieldDataState?.fieldKeys.length === 0)
      ) {
        dispatch(
          snackbarActions.setSnackbar({
            message: "Please fill in details",
            type: "error",
          })
        );
        setSubmit();
        return;
      } else if (!!!body.action.promptContent && activity !== "standardField") {
        dispatch(
          snackbarActions.setSnackbar({
            message: "Please enter a prompt",
            type: "error",
          })
        );
        setSubmit();
        return;
      }

      const response = addAgentAction(body as IPayload, dispatch);
      response
        .then((resData) => {
          if (resData?.status === 201) {
            dispatch(agentsAction.actionAdded(resData.data.action));
          }
        })
        .catch((error: any) => {
          console.log(error.message);
        })
        .finally(() => {
          console.log({ body });
          setSubmit();
          onCloseFn();
        });
    } else if (submitted && type === "edit") {
      const bodyForEdit = { ...body, actionId: actionId as string };
      if (activity === "customField") {
        const matches = body.action.accountId.match(customFieldRegex);
        if (!matches) {
          dispatch(
            snackbarActions.setSnackbar({
              message: "Please enter valid custom field format",
              type: "error",
            })
          );
          setSubmit();
          return;
        }
      }

      const response = editAgentAction(bodyForEdit, dispatch);
      response
        .then((resData) => {
          if (resData?.status === 200) {
            dispatch(
              agentsAction.actionEdited({
                ...bodyForEdit.action,
                actionId: actionId as string,
              } as ISingleAction)
            );
          }
        })
        .catch((error) => {
          console.log(error.message);
        })
        .finally(() => {
          console.log({ body });
          setSubmit();
          onCloseFn();
        });
    }
  }, [submitted]);

  return (
    <React.Fragment>
      <Box sx={{ display: "flex", gap: 2 }}>
        <FormControl fullWidth>
          <InputLabel id="demo-simple-select-label-activity" required>
            Activity
          </InputLabel>
          <Select
            labelId="demo-simple-select-label-activity"
            id="demo-simple-select-activity"
            fullWidth
            value={activity}
            required
            label="Activity"
            onChange={handleChangeActivity}
          >
            <MenuItem value={"tag"}>Add Contact Tag</MenuItem>
            <MenuItem value={"customField"}>Custom field</MenuItem>
            <MenuItem value={"standardField"}>Standard Fields</MenuItem>
          </Select>
        </FormControl>

        <FormControl fullWidth>
          {activity === "tag" ? (
            <TextField
              id="outlined-basic-tag-value"
              label="Tag value"
              variant="outlined"
              value={tagValue}
              onChange={handleChangeTagValue}
            />
          ) : null}
          {activity === "customField" ? (
            <TextField
              id="outlined-basic-custom-field-value"
              label="Custom field value"
              variant="outlined"
              value={customFieldDataState?.fieldKey}
              helperText="Please enter values in this format {{contact.field_name}}. Do not leave any white spaces."
              onChange={(e) => {
                setCustomFieldDataState({
                  ...customFieldDataState,
                  fieldKey: e.target.value,
                } as ICustomFieldData);
              }}
            />
          ) : null}
          {activity === "standardField" ? (
            <Autocomplete
              multiple
              id="standard-fields-select"
              options={standardFields}
              value={standardFieldDataState?.fieldKeys || []}
              onChange={(_, newValue) => {
                setStandardFieldDataState({
                  ...standardFieldDataState,
                  fieldKeys: newValue,
                } as IStandardFieldData);
              }}
              renderInput={(params) => (
                <TextField
                  {...params}
                  variant="outlined"
                  label="Standard Fields"
                  placeholder="Select fields"
                />
              )}
            />
          ) : null}
        </FormControl>
      </Box>

      <Box sx={{ display: "flex", gap: 2 }}>
        {activity === "tag" && (
          <FormControl fullWidth>
            <InputLabel id="demo-simple-select-label-evaluate-on" required>
              Evaluate on
            </InputLabel>
            <Select
              labelId="demo-simple-select-label-evaluate-on"
              id="demo-simple-select-label-evaluate-on"
              fullWidth
              value={evaluateOn}
              required
              label="Evaluate on"
              onChange={handleChangeEvaluateOn}
            >
              <MenuItem value={"everyTurn"}>On every turn</MenuItem>
              <MenuItem value={"contactNoTag"}>
                Only when contact does not have tag
              </MenuItem>
            </Select>
          </FormControl>
        )}
        {activity === "customField" && (
          <FormControl fullWidth>
            <InputLabel id="demo-simple-select-label-evaluate-on" required>
              Evaluate on
            </InputLabel>
            <Select
              labelId="demo-simple-select-label-evaluate-on-custom-field"
              id="demo-simple-select-label-evaluate-on-custom-field"
              fullWidth
              value={customFieldDataState?.evaluateOn}
              required
              label="Evaluate on"
              onChange={(e) => {
                setCustomFieldDataState({
                  ...customFieldDataState,
                  evaluateOn: e.target.value as "everyTurn" | "isEmpty",
                } as ICustomFieldData);
              }}
            >
              <MenuItem value={"everyTurn"}>On every turn</MenuItem>
              <MenuItem value={"isEmpty"}>Only when field is empty</MenuItem>
            </Select>
          </FormControl>
        )}
        {activity === "standardField" && (
          <FormControl fullWidth>
            <InputLabel id="demo-simple-select-label-evaluate-on" required>
              Evaluate on
            </InputLabel>
            <Select
              labelId="demo-simple-select-label-evaluate-on-standard-field"
              id="demo-simple-select-label-evaluate-on-standard-field"
              fullWidth
              value="isEmpty"
              required
              label="Evaluate on"
              disabled
            >
              <MenuItem value={"isEmpty"}>Only when field is empty</MenuItem>
            </Select>
          </FormControl>
        )}
      </Box>
      <Box sx={{ display: "flex", gap: 2 }}>
        <Stack direction="row" spacing={1} alignItems="center">
          <AntSwitch
            defaultChecked
            checked={!reply}
            onChange={() => setReply(!reply)}
            inputProps={{ "aria-label": "ant design" }}
          />
          <Typography>Silent</Typography>
        </Stack>
      </Box>
    </React.Fragment>
  );
};

export default GHLChannelInputs;
