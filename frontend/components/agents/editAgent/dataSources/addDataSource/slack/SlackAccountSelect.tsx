import React, { useState, useEffect } from "react";
import {
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Box,
  SelectChangeEvent,
  FormHelperText,
  Button,
  Typography,
  Tooltip,
  Stack,
} from "@mui/material";
import { IAdvancedSettings } from "../../addDataSource";
import { ISlackAction, agentsAction } from "@/slices/agents/agentsSlice";
import {
  ISlackAgentAction,
  addAgentAction,
} from "@/requests/agents/actions/addAgentAction";
import { CONSTANTS } from "@/helpers/constants";
import { useDispatch, useSelector } from "react-redux";
import { useRouter } from "next/router";
import { editAgentAction } from "@/requests/agents/actions/editAgentAction";
import { IoIosAddCircle } from "react-icons/io";
import { blue } from "@mui/material/colors";
import { RootState } from "@/store";
import { AntSwitch } from "../ghlChannelInputs";
import InfoOutlinedIcon from "@mui/icons-material/InfoOutlined";
import AddDataSourceMenuItem from "../AddDataSourceMenuItem";
import { logos } from "@/helpers/images";

interface IProp {
  data: ISlackAction;
  submitted: boolean;
  setSubmit: () => void;
  type: "add" | "edit";
  onCloseFn: () => void;
  isAdvancedSettings: boolean;
  advancedSettings: IAdvancedSettings;
  actionId?: string;
}

const SlackAccountSelect: React.FC<IProp> = ({
  data,
  actionId,
  submitted,
  type,
  advancedSettings,
  isAdvancedSettings,
  onCloseFn,
  setSubmit,
}) => {
  const dispatch = useDispatch();
  const router = useRouter();
  const channelAccountList = useSelector(
    (state: RootState) => state.channelsList.accountsList
  );
  const accountList = channelAccountList
    .filter((item: any) => item.providerName === data.providerName)
    .map((item: any) => ({ accountId: item.accountId, name: item.name }));
  useEffect(() => {
    console.log({
      accountList,
      channelAccountList,
    });
  }, [channelAccountList, accountList]);
  const activityList = ["write"];
  const [activity, setActivity] = React.useState(data.activity);
  const [accountId, setAccountId] = React.useState(data.accountId);
  const [silent, setSilent] = React.useState(data?.silent ?? true);
  const { agentId } = router.query as { agentId: string };
  const [responseType, setResponseType] = useState<"hardcoded" | "predictive">(
    data.slackDetails?.type || "predictive"
  );
  const [responseText, setResponseText] = useState(
    data.slackDetails?.text || ""
  );
  const handleChangeAccountId = (event: SelectChangeEvent) => {
    setAccountId(event.target.value as string);
  };
  const handleChangeActivity = (event: SelectChangeEvent) => {
    setActivity(event.target.value as "write");
  };

  useEffect(() => {
    if (!submitted) return;
    const action: ISlackAgentAction = {
      activity: "write",
      providerName: CONSTANTS.PROVIDERS.SLACK,
      accountId: accountId ?? "",
      accountName:
        accountList.find((account) => account.accountId === accountId)?.name ??
        "",
      promptContent: data.promptContent ?? "",
      advancedSettings,
      isAdvancedSettings,
      includeMainPromptDetails: { includeMainPrompt: false, mainPromptId: "" },
      augmentedQueryData: data.augmentedQueryData,
      slackDetails: {
        type: responseType,
        text: responseText,
      },
      silent,
      evalType: data.evalType,
      confidenceValue: data.confidenceValue,
    };
    if (type === "add") {
      const addGetAction = async () => {
        const response = await addAgentAction({ agentId, action }, dispatch);
        if (response?.status === 201) {
          dispatch(agentsAction.actionAdded(response.data.action));
        }
        setSubmit();
        onCloseFn();
      };
      addGetAction();
    } else if (type === "edit") {
      const editGetAction = async () => {
        const response = await editAgentAction(
          { agentId, actionId: actionId as string, action },
          dispatch
        );
        if (response?.status === 200) {
          dispatch(
            agentsAction.actionEdited({
              ...action,
              actionId: actionId as string,
            })
          );
        }
        setSubmit();
        onCloseFn();
      };
      editGetAction();
    }
  }, [submitted]);

  const handleTypeChange = (
    event: SelectChangeEvent<"hardcoded" | "predictive">
  ) => {
    setResponseType(event.target.value as "hardcoded" | "predictive");
  };

  const handleTextChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setResponseText(event.target.value);
  };

  return (
    <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
      <FormControl fullWidth>
        <InputLabel id="demo-simple-select-label-account" required>
          Account
        </InputLabel>
        <Select
          labelId="demo-simple-select-label-account"
          id="demo-simple-select-account"
          fullWidth
          value={accountId}
          label="Account"
          onChange={handleChangeAccountId}
          required
        >
          {accountList?.map((item) => (
            <MenuItem
              value={item.accountId}
              key={"Select_activity_key" + item.accountId}
            >
              {item.name || "No name"}
            </MenuItem>
          ))}
          <AddDataSourceMenuItem
            providerName={data.providerName}
            onAuthSuccess={() => {}}
          >
            Add {logos[data.providerName]?.name}
          </AddDataSourceMenuItem>
        </Select>
      </FormControl>
      <FormControl fullWidth>
        <InputLabel id="demo-simple-select-label-activity" required>
          Activity
        </InputLabel>
        <Select
          labelId="demo-simple-select-label-activity"
          id="demo-simple-select-activity"
          fullWidth
          required
          value={activity}
          label="Activity"
          onChange={handleChangeActivity}
        >
          {activityList?.map((item) => (
            <MenuItem value={item} key={item}>
              {item === "read" ? "Read" : "Write"}
            </MenuItem>
          ))}
        </Select>
      </FormControl>
      <FormControl>
        <InputLabel id="response-type-label">Response Type</InputLabel>
        <Select
          labelId="response-type-label"
          id="response-type"
          value={responseType}
          label="Response Type"
          onChange={handleTypeChange}
        >
          <MenuItem value="hardcoded">Hardcoded</MenuItem>
          <MenuItem value="predictive">Predictive</MenuItem>
        </Select>
        <FormHelperText>
          {responseType === "hardcoded"
            ? "A fixed response that will always be sent"
            : "An AI-generated response based on the context"}
        </FormHelperText>
      </FormControl>
      <FormControl>
        <Stack direction="row" spacing={1} alignItems="center">
          <AntSwitch
            defaultChecked
            checked={silent}
            onChange={() => setSilent(!silent)}
            inputProps={{ "aria-label": "ant design" }}
          />
          <Typography>Silent</Typography>
          <Tooltip title="Fire the action and don’t send any message">
            <InfoOutlinedIcon sx={{ cursor: "pointer" }} />
          </Tooltip>
        </Stack>
      </FormControl>

      {responseType === "hardcoded" && (
        <TextField
          fullWidth
          id="response-text"
          label="Response Text"
          variant="outlined"
          value={responseText}
          onChange={handleTextChange}
          multiline
          rows={4}
        />
      )}
    </Box>
  );
};

export default SlackAccountSelect;
