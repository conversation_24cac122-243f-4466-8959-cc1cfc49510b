// components/AccountInputs.tsx
import {
  IAddAgentActionPayload,
  addAgentAction,
} from "@/requests/agents/actions/addAgentAction";
import { editAgentAction } from "@/requests/agents/actions/editAgentAction";
import {
  IAccountAction,
  IActionGhlChannel,
  ISingleAction,
  agentsAction,
} from "@/slices/agents/agentsSlice";
import { IIntegrationProvider } from "@/slices/settings/integrations/integrationList";
import { RootState } from "@/store";
import {
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  SelectChangeEvent,
  Button,
  Stack,
  Typography,
  Tooltip,
} from "@mui/material";
import { useRouter } from "next/router";
import React, { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { IAdvancedSettings } from "../addDataSource";
import { IoIosAddCircle } from "react-icons/io";
import { blue, grey } from "@mui/material/colors";
import { settingsUrl } from "@/slices/subOptions/subOptionSlice";
import { AntSwitch } from "./ghlChannelInputs";
import InfoOutlinedIcon from "@mui/icons-material/InfoOutlined";
import { capitalizeFirstLetter } from "@/helpers/basic";
import AddDataSourceMenuItem from "./AddDataSourceMenuItem";
import { logos } from "@/helpers/images";

interface IProp {
  data: IAccountAction;
  onDataChange: (x: IAccountAction) => void;
  submitted: boolean;
  setSubmit: () => void;
  onCloseFn: () => void;
  type: "add" | "edit";
  isAdvancedSettings: boolean;
  advancedSettings: IAdvancedSettings;
  actionId?: string;
}

const AccountInputs: React.FC<IProp> = ({
  data,
  onDataChange,
  submitted,
  setSubmit,
  onCloseFn,
  type,
  isAdvancedSettings,
  advancedSettings,
  actionId,
}) => {
  const dispatch = useDispatch();
  const router = useRouter();
  const [silent, setSilent] = React.useState(data?.silent ?? true);
  const { agentId } = router.query as { agentId: string };
  const integrationList = useSelector(
    (state: RootState) => state.integrationList.integrationAccountsList
  );
  const integrationProvidersList = useSelector(
    (state: RootState) => state.integrationList.integrationProvidersList
  );
  const accountId = data.accountId ?? "";
  const activity = data.activity;
  const accountList =
    integrationList
      .filter((item) => item.providerName === data.providerName)
      .map((item) => ({ accountId: item.accountId, name: item.name })) || [];

  useEffect(() => {
    console.log({ integrationList });
  }, [integrationList]);
  const [activityList, setActivityList] = React.useState<string[]>([]);
  const handleChangeActivity = (event: SelectChangeEvent) => {
    onDataChange({ ...data, activity: event.target.value as "read" | "write" });
  };
  const handleChangeAccountId = (event: SelectChangeEvent) => {
    onDataChange({ ...data, accountId: event.target.value as string });
  };
  useEffect(() => {
    setActivityList(
      (
        integrationProvidersList.find(
          (item) => item.providerId === data.providerName
        ) as IIntegrationProvider
      )?.activity
    );
  }, [data]);
  useEffect(() => {
    const accountName =
      accountList.find((item) => item.accountId === accountId)?.name || "";
    if (submitted && type === "add") {
      let action: IAddAgentActionPayload = {
        accountId,
        providerName: data.providerName,
        promptContent: data.promptContent,
        accountName,
        activity,
        isAdvancedSettings,
        advancedSettings,
        includeMainPromptDetails: data.includeMainPromptDetails,
        augmentedQueryData: data.augmentedQueryData,
        evalType: data.evalType,
        confidenceValue: data.confidenceValue,
      };

      if (activity === "write") {
        action.silent = silent;
      }

      const body = {
        agentId,
        action,
      };
      console.log({ body });

      try {
        const response = addAgentAction(body, dispatch);
        response
          .then((resData) => {
            if (
              resData?.status &&
              resData?.status >= 200 &&
              resData.status < 300
            ) {
              dispatch(agentsAction.actionAdded(resData.data.action));
              onCloseFn();
              console.log("This should close now");
            }
          })
          .catch((error) => {
            console.log(error.message);
          })
          .finally(() => {
            setSubmit();
          });
      } catch (error: any) {
        console.log(error.message);
      }
    } else if (submitted && type === "edit") {
      const action: IAddAgentActionPayload = {
        accountId,
        providerName: data.providerName,
        promptContent: data.promptContent,
        accountName,
        activity,
        isAdvancedSettings,
        advancedSettings,
        includeMainPromptDetails: data.includeMainPromptDetails,
        augmentedQueryData: data.augmentedQueryData,
        evalType: data.evalType,
        confidenceValue: data.confidenceValue,
      };

      if (activity === "write") {
        action.silent = silent;
      }

      const body = {
        agentId,
        actionId: actionId as string,
        action,
      };
      const reducerPayload: IAccountAction = {
        providerName: body.action.providerName as
          | "ghlCalendar"
          // | "googleCalendar"
          | "googleSheet"
          | "website",
        accountId: body.action.accountId,
        activity: body.action.activity as "read" | "write",
        promptContent: body.action.promptContent,
        actionId: body.actionId,
        accountName: body.action.accountName,
        silent: body.action?.silent,
        isAdvancedSettings: body.action.isAdvancedSettings as boolean,
        advancedSettings: body.action.advancedSettings,
        includeMainPromptDetails: body.action.includeMainPromptDetails,
        evalType: body.action.evalType,
        confidenceValue: body.action.confidenceValue,
        augmentedQueryData: body.action.augmentedQueryData,
      };
      console.log({ body });
      try {
        const response = editAgentAction(body, dispatch);
        response
          .then((resData) => {
            if (
              resData?.status &&
              resData.status >= 200 &&
              resData.status < 300
            ) {
              dispatch(agentsAction.actionEdited(reducerPayload));
              onCloseFn();
              console.log("This should close now");
            }
          })
          .finally(() => {
            setSubmit();
          });
      } catch (error: any) {
        console.log(error.message);
      }
    }
  }, [submitted]);
  return (
    <React.Fragment>
      <FormControl fullWidth>
        <InputLabel id="demo-simple-select-label-account" required>
          Account
        </InputLabel>
        <Select
          labelId="demo-simple-select-label-account"
          id="demo-simple-select-account"
          fullWidth
          value={accountId}
          label="Account"
          onChange={handleChangeAccountId}
          required
        >
          {accountList?.map((item) => (
            <MenuItem
              value={item.accountId}
              key={"Select_activity_key" + item.accountId}
            >
              {item.name || "No name"}
            </MenuItem>
          ))}
          <AddDataSourceMenuItem
            providerName={data.providerName}
            onAuthSuccess={() => {}}
          >
            Add {logos[data.providerName]?.name}
          </AddDataSourceMenuItem>
        </Select>
      </FormControl>
      <FormControl fullWidth>
        <InputLabel id="demo-simple-select-label-activity" required>
          Activity
        </InputLabel>
        <Select
          labelId="demo-simple-select-label-activity"
          id="demo-simple-select-activity"
          fullWidth
          required
          value={activity}
          label="Activity"
          onChange={handleChangeActivity}
        >
          {activityList?.map((item) => (
            <MenuItem value={item} key={item}>
              {capitalizeFirstLetter(item === "events" ? "Fetch events" : item)}
            </MenuItem>
          ))}
        </Select>
      </FormControl>
      {activity === "write" && (
        <Stack direction="row" spacing={1} alignItems="center">
          {/* <Typography>Add tag only</Typography> */}
          <AntSwitch
            defaultChecked
            checked={silent}
            onChange={() => setSilent(!silent)}
            inputProps={{ "aria-label": "ant design" }}
          />
          <Typography>Silent</Typography>
          <Tooltip title="Fire the action and don’t send any message">
            <InfoOutlinedIcon sx={{ cursor: "pointer" }} />
          </Tooltip>
        </Stack>
      )}
    </React.Fragment>
  );
};

export default AccountInputs;
