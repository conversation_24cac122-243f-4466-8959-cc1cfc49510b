import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  <PERSON>complete,
  TextField,
  createTheme,
  ThemeProvider,
  CircularProgress,
  Stack,
  InputAdornment,
  Button,
} from "@mui/material";
import { CONSTANTS } from "@/helpers/constants";
import { RootState } from "@/store";
import HttpPostInput from "./HttpPostInput";
import { IHttpPostAction, agentsAction } from "@/slices/agents/agentsSlice";
import { IAdvancedSettings } from "../../../addDataSource";
import { IHttpPostRequest } from "@/components/settings/integrations/http/httpPost/HttpPostComponent";
import { fetchPostRequest } from "@/requests/integrations/http/post/fetchPostRequest";
import {
  IHttpPostRequestAction,
  addAgentAction,
} from "@/requests/agents/actions/addAgentAction";
import { useRouter } from "next/router";
import { editAgentAction } from "@/requests/agents/actions/editAgentAction";
import { logos } from "@/helpers/images";
import AddDataSourceMenuItem from "../../AddDataSourceMenuItem";

interface IProp {
  data: IHttpPostAction;
  submitted: boolean;
  setSubmit: () => void;
  type: "add" | "edit";
  onCloseFn: () => void;
  isAdvancedSettings: boolean;
  advancedSettings: IAdvancedSettings;
  actionId?: string;
}

interface Account {
  accountId: string;
  name: string;
}

const theme = createTheme(); // You can customize this theme as needed

const HttpPostAccountSelect: React.FC<IProp> = ({
  data,
  actionId,
  submitted,
  type,
  advancedSettings,
  isAdvancedSettings,
  onCloseFn,
  setSubmit,
}) => {
  const dispatch = useDispatch();
  const router = useRouter();
  const { agentId } = router.query as { agentId: string };
  const [selectedAccount, setSelectedAccount] = useState<Account | null>(null);
  const [httpPostObject, setHttpPostObject] = useState<IHttpPostRequest | null>(null);
  const [loadingHttpPostObj, setLoadingHttpPostObj] = useState(false);
  const orgId = localStorage.getItem("orgId") as string;
  const integrationList = useSelector(
    (state: RootState) => state.integrationList.integrationAccountsList
  );

  const accounts: Account[] = integrationList
    .filter((account) => account.providerName === CONSTANTS.PROVIDERS.HTTP_POST)
    .map((account) => ({
      accountId: account.accountId,
      name: account.name ?? "No name",
    }));

  useEffect(() => {
    if (actionId) {
      setHttpPostObject(data.httpRequestDetails);
      const account = accounts.find(
        (account) => account.accountId === data.accountId
      );
      if (account) {
        handleAccountSelect(account);
      }
    } else if (data.accountId) {
      // Fetch the http post details from the integration using the `data.accountId`
    } else if (selectedAccount?.accountId) {
      fetchHttpPostRequestFromIntegration();
    }
  }, [actionId, data.accountId, selectedAccount?.accountId]);

  useEffect(() => {
    if (!submitted) return;

    const action: IHttpPostRequestAction = {
      httpRequestDetails: httpPostObject as IHttpPostRequest,
      activity: "read",
      providerName: CONSTANTS.PROVIDERS.HTTP_POST,
      accountName: httpPostObject?.name ?? "",
      promptContent: data?.promptContent ?? "",
      accountId: selectedAccount?.accountId ?? "",
      advancedSettings,
      isAdvancedSettings,
      includeMainPromptDetails: { includeMainPrompt: false, mainPromptId: "" },
      augmentedQueryData: data.augmentedQueryData,
      evalType: data.evalType,
      confidenceValue: data.confidenceValue,
    };

    const handleAction = async () => {
      let response;
      if (type === "add") {
        response = await addAgentAction({ agentId, action }, dispatch);
        if (response?.status === 201) {
          dispatch(agentsAction.actionAdded(response.data.action));
        }
      } else if (type === "edit") {
        response = await editAgentAction(
          { agentId, actionId: actionId as string, action },
          dispatch
        );
        if (response?.status === 200) {
          dispatch(
            agentsAction.actionEdited({
              ...action,
              actionId: actionId as string,
            })
          );
        }
      }
      setSubmit();
      onCloseFn();
    };

    handleAction();
  }, [submitted]);

  const handleAccountSelect = (account: Account | null) => {
    setSelectedAccount(account);
    console.log("Selected account:", account);
  };

  const fetchHttpPostRequestFromIntegration = async () => {
    setLoadingHttpPostObj(true);
    try {
      const response = await fetchPostRequest({
        body: {
          accountId: selectedAccount?.accountId as string,
          orgId,
        },
        dispatch,
      });
      if (response?.status === 200) {
        setHttpPostObject(response?.data.data);
      }
    } catch (error) {
      console.error("Error fetching HTTP POST request:", error);
    } finally {
      setLoadingHttpPostObj(false);
    }
  };

  return (
    <ThemeProvider theme={theme}>
      <Autocomplete
        options={accounts}
        getOptionLabel={(option) => option.name}
        renderInput={(params) => (
          <TextField
            {...params}
            label="Account"
            variant="outlined"
            InputProps={{
              ...params.InputProps,
              endAdornment: (
                <InputAdornment position="end">
                  <AddDataSourceMenuItem
                    providerName={data.providerName}
                    onAuthSuccess={() => {}}
                  >
                    Add {logos[data.providerName]?.name}
                  </AddDataSourceMenuItem>
                  {params.InputProps.endAdornment}
                </InputAdornment>
              ),
            }}
          />
        )}
        value={selectedAccount}
        onChange={(event, newValue) => handleAccountSelect(newValue)}
        renderOption={(props, option) => (
          <li {...props} key={option.accountId}>
            {option.name}
          </li>
        )}
        isOptionEqualToValue={(option, value) =>
          option.accountId === value.accountId
        }
      />
      {loadingHttpPostObj && (
        <Stack direction="row" justifyContent="center">
          <CircularProgress />
        </Stack>
      )}
      {!!httpPostObject && !loadingHttpPostObj && (
        <HttpPostInput
          httpRequestObj={httpPostObject}
          handleHttpRequestObjectChange={(obj: IHttpPostRequest) =>
            setHttpPostObject(obj)
          }
        />
      )}
    </ThemeProvider>
  );
};

export default HttpPostAccountSelect; 