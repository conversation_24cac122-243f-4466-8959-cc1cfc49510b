import HttpAuthorization from "@/components/settings/integrations/http/http/HttpAuthorization";
import {
  IHttpRequest,
  IHttpResponse,
} from "@/components/settings/integrations/http/http/HttpGetComponent";
import HttpHeaders from "@/components/settings/integrations/http/http/HttpHeaders";
import HttpParams from "@/components/settings/integrations/http/http/HttpParams";
import HttpResponse from "@/components/settings/integrations/http/http/HttpResponse";
import HttpUrl from "@/components/settings/integrations/http/http/HttpUrl";
import { sendRequest } from "@/components/settings/integrations/http/http/send-request";
import { Alert, Paper, PaperProps } from "@mui/material";
import { blue } from "@mui/material/colors";
import React, { useEffect, useState } from "react";
import { useDispatch } from "react-redux";

const StandardPaper: React.FC<PaperProps> = (props) => (
  <Paper elevation={3} sx={{ p: 2, bgcolor: blue[50] }} {...props} />
);

interface IProp {
  httpRequestObj: IHttpRequest;
  handleHttpRequestObjectChange: (obj: IHttpRequest) => void;
}

const defaultState: IHttpRequest = {
  name: "",
  url: "",
  queryParameters: [],
  pathVariables: [],
  headers: [],
  authorizationType: "No Auth",
  responseBuilder: "",
};

const HttpGetInput: React.FC<IProp> = ({
  httpRequestObj,
  handleHttpRequestObjectChange,
}) => {
  const dispatch = useDispatch();
  const [httpRequest, setHttpRequest] = useState<IHttpRequest>(
    httpRequestObj ?? defaultState
  );
  const [response, setResponse] = useState<IHttpResponse | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  useEffect(() => {
    console.log({ httpRequest });

    handleHttpRequestObjectChange(httpRequest);
  }, [httpRequest]);
  const handleSendRequest = async () => {
    setLoading(true);
    setError(null);
    try {
      console.log({ httpRequest });

      const result = await sendRequest(httpRequest, dispatch);
      setResponse(result as IHttpResponse | null);
    } catch (err) {
      setError(
        err instanceof Error ? err.message : "An unknown error occurred"
      );
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <Alert severity="warning">
        In case any value is left empty, the bot will try to fill that in.
      </Alert>
      <StandardPaper>
        <HttpUrl
          type="GET"
          url={httpRequest.url}
          loading={loading}
          urlOnChange={(url) => {
            setHttpRequest({ ...httpRequest, url });
          }}
          onFire={handleSendRequest}
        />
      </StandardPaper>
      <StandardPaper>
        <HttpParams
          url={httpRequest.url}
          queryParams={httpRequest.queryParameters}
          pathVariables={httpRequest.pathVariables}
          onQueryParamChange={(queryParam: IHttpRequest["queryParameters"]) =>
            setHttpRequest({ ...httpRequest, queryParameters: queryParam })
          }
          onPathVariableChange={(pathVariable: IHttpRequest["pathVariables"]) =>
            setHttpRequest({ ...httpRequest, pathVariables: pathVariable })
          }
        />
      </StandardPaper>
      <StandardPaper>
        <HttpAuthorization
          authorizationType={httpRequest.authorizationType}
          apiKey={httpRequest.apiKey}
          onAuthorizationTypeChange={(
            authType: IHttpRequest["authorizationType"]
          ) => setHttpRequest({ ...httpRequest, authorizationType: authType })}
          onApiKeyChange={(newApiKey: IHttpRequest["apiKey"]) =>
            setHttpRequest({ ...httpRequest, apiKey: newApiKey })
          }
        />
      </StandardPaper>
      <StandardPaper>
        <HttpHeaders
          headers={httpRequest.headers}
          onHeadersChange={(header: IHttpRequest["headers"]) =>
            setHttpRequest({ ...httpRequest, headers: header })
          }
        />
      </StandardPaper>
      <StandardPaper>
        <HttpResponse
          response={response}
          responseBuilder={httpRequest.responseBuilder}
          setResponseBuilder={(responseBuilder) =>
            setHttpRequest({
              ...httpRequest,
              responseBuilder: responseBuilder,
            })
          }
        />
      </StandardPaper>
    </>
  );
};

export default HttpGetInput;
