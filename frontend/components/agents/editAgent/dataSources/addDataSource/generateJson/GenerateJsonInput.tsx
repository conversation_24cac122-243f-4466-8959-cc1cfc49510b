import {
  Button,
  FormControl,
  FormControlLabel,
  FormLabel,
  Radio,
  RadioGroup,
  SwipeableDrawer,
  TextField,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import Json<PERSON>eyValue from "./JsonKeyValue";
import AddIcon from "@mui/icons-material/Add";
import <PERSON>sonViewer from "@/components/JsonViewer";
import { IAdvancedSettings } from "../../addDataSource";
import {
  IGenerateJsonPayload,
  addAgentAction,
} from "@/requests/agents/actions/addAgentAction";
import { useDispatch } from "react-redux";
import { useRouter } from "next/router";
import { IGenerateJsonAction, agentsAction } from "@/slices/agents/agentsSlice";
import { editAgentAction } from "@/requests/agents/actions/editAgentAction";

export type TJsonProperty = "string" | "number" | "boolean" | "date";

export interface IJsonKeyValueProps {
  name: string;
  description: string;
  type: TJsonProperty;
  id: string;
}

export interface IJsonStructure {
  name: string; //This will be the name of the JSON object
  generateCondition: "all" | "any";
  properties: {
    name: string;
    description: string;
    type: TJsonProperty;
    id: string;
  }[];
}

type Anchor = "top" | "left" | "bottom" | "right";

interface IProp {
  data: IGenerateJsonAction;
  submitted: boolean;
  setSubmit: () => void;
  type: "add" | "edit";
  onCloseFn: () => void;
  isAdvancedSettings: boolean;
  advancedSettings: IAdvancedSettings;
  actionId?: string;
}

const GenerateJsonInput: React.FC<IProp> = ({
  data,
  submitted,
  setSubmit,
  type,
  onCloseFn,
  isAdvancedSettings,
  advancedSettings,
  actionId,
}) => {
  const dispatch = useDispatch();
  const router = useRouter();
  const { agentId } = router.query as { agentId: string };
  const [state, setState] = React.useState({
    top: false,
    left: false,
    bottom: false,
    right: false,
  });

  const toggleDrawer =
    (anchor: Anchor, open: boolean) =>
    (event: React.KeyboardEvent | React.MouseEvent) => {
      if (
        event &&
        event.type === "keydown" &&
        ((event as React.KeyboardEvent).key === "Tab" ||
          (event as React.KeyboardEvent).key === "Shift")
      ) {
        return;
      }

      setState({ ...state, [anchor]: open });
    };
  const [jsonDetails, setJsonDetails] = useState<IJsonKeyValueProps[]>(
    data?.jsonObjects?.properties || [
      {
        name: "",
        description: "",
        type: "string",
        id: crypto.randomUUID(),
      },
    ]
  );
  useEffect(() => {
    console.log({ jsonDetails });
  }, [jsonDetails]);
  const [name, setName] = useState(data?.jsonObjects?.name || "");
  const [jsonGeneratorCondition, setJsonGeneratorCondition] = useState<
    "all" | "any"
  >(data?.jsonObjects?.generateCondition || "all");
  const addMoreJsonObj = () => {
    setJsonDetails((prev) => [
      ...prev,
      {
        name: "",
        description: "",
        type: "string",
        id: crypto.randomUUID(),
      },
    ]);
  };
  useEffect(() => {
    const body: IGenerateJsonPayload = {
      agentId,
      action: {
        activity: "read",
        providerName: "generateJson",
        accountName: name,
        promptContent: data?.promptContent || "",
        jsonObjects: {
          name,
          generateCondition: jsonGeneratorCondition,
          properties: jsonDetails,
        },
        isAdvancedSettings,
        advancedSettings,
        includeMainPromptDetails: data.includeMainPromptDetails,
        augmentedQueryData: data.augmentedQueryData,
        evalType: data.evalType,
        confidenceValue: data.confidenceValue,
      },
    };
    if (submitted && type === "add") {
      const response = addAgentAction(body, dispatch);
      response
        .then((resData) => {
          if (resData?.status === 201) {
            dispatch(agentsAction.actionAdded(resData.data.action));
          }
        })
        .catch((error: any) => {
          console.log(error.message);
        })
        .finally(() => {
          console.log({ body });
          setSubmit();
          onCloseFn();
        });
    } else if (submitted && type === "edit") {
      const bodyForEdit = { ...body, actionId: actionId as string };
      console.log({ bodyForEdit });

      const response = editAgentAction(bodyForEdit, dispatch);
      response
        .then((resData) => {
          if (resData?.status === 200) {
            dispatch(
              agentsAction.actionEdited({
                ...bodyForEdit.action,
                actionId: actionId as string,
              } as IGenerateJsonAction)
            );
          }
        })
        .catch((error) => {
          console.log(error.message);
        })
        .finally(() => {
          console.log({ body });
          setSubmit();
          onCloseFn();
        });
    }
  }, [submitted]);
  const removeJsonObj = (id: string) => {
    setJsonDetails((prev) =>
      prev.filter((x) => (x as IJsonKeyValueProps).id !== id)
    );
  };
  return (
    <div>
      <div className="flex flex-col gap-2">
        <TextField
          variant="outlined"
          label="JSON name"
          id="generateJSON_name"
          value={name}
          onChange={(e) => setName(e.target.value)}
          fullWidth
        />
        {jsonDetails?.map((item) => (
          <JsonKeyValue
            key={item?.id}
            details={item as IJsonKeyValueProps}
            lastField={jsonDetails.length === 1}
            onJsonChange={(x: IJsonKeyValueProps) =>
              setJsonDetails((prev) =>
                prev.map((y) => ((y as IJsonKeyValueProps).id === x.id ? x : y))
              )
            }
            onJsonDelete={(id: string) => removeJsonObj(id)}
          />
        ))}
      </div>
      <div className="mt-2 flex gap-2">
        <Button
          variant="outlined"
          startIcon={<AddIcon />}
          onClick={() => addMoreJsonObj()}
        >
          Add more
        </Button>
        <Button onClick={toggleDrawer("right", true)}>Preview</Button>
      </div>
      <div className="mt-2">
        <FormControl>
          <FormLabel id="json-generator-freq">
            JSON generator condition
          </FormLabel>
          <RadioGroup
            aria-labelledby="demo-radio-buttons-group-label"
            value={jsonGeneratorCondition}
            onChange={(e) =>
              setJsonGeneratorCondition(e.target.value as "all" | "any")
            }
            name="radio-buttons-group"
          >
            <FormControlLabel
              value="all"
              control={<Radio />}
              label="All required"
            />
            <FormControlLabel
              value="any"
              control={<Radio />}
              label="Any one required"
            />
          </RadioGroup>
        </FormControl>
      </div>
      <SwipeableDrawer
        anchor={"right"}
        open={state["right"]}
        onClose={toggleDrawer("right", false)}
        onOpen={toggleDrawer("right", true)}
      >
        <JsonViewer
          details={(jsonDetails as IJsonKeyValueProps[]).map(
            ({ id, ...rest }) => rest
          )}
        />
      </SwipeableDrawer>
    </div>
  );
};

export default GenerateJsonInput;
