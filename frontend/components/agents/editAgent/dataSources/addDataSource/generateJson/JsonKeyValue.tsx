import {
  FormControl,
  FormControlLabel,
  IconButton,
  InputLabel,
  MenuItem,
  Select,
  Switch,
  TextField,
} from "@mui/material";
import React, { useState } from "react";
import DeleteIcon from "@mui/icons-material/Delete";
import { IJsonKeyValueProps, TJsonProperty } from "./GenerateJsonInput";
import { useDispatch } from "react-redux";
import { snackbarActions } from "@/slices/general/snackbar";

interface IProp {
  details: IJsonKeyValueProps;
  lastField: boolean;
  onJsonChange: (x: IJsonKeyValueProps) => void;
  onJsonDelete: (id: string) => void;
}

const JsonKeyValue: React.FC<IProp> = ({
  details,
  lastField,
  onJsonChange,
  onJsonDelete,
}) => {
  const dispatch = useDispatch();
  return (
    <div className="flex gap-2">
      <div className="w-5/12">
        <TextField
          variant="outlined"
          label="Name"
          value={details.name}
          fullWidth
          required
          onChange={(e) => {
            onJsonChange({
              ...details,
              name: e.target.value,
            });
          }}
        />
      </div>
      <div className="w-5/12">
        <TextField
          variant="outlined"
          label="Description"
          value={details.description}
          fullWidth
          required
          onChange={(e) => {
            onJsonChange({
              ...details,
              description: e.target.value,
            });
          }}
        />
      </div>
      <div className="2/12 mx-auto">
        <div className="flex">
          <FormControl fullWidth>
            <InputLabel id="select-json-generator-type-label">Type</InputLabel>
            <Select
              labelId="select-json-generator-type-label"
              id="select-json-generator-type-id"
              value={details.type}
              label="Type"
              sx={{ width: "120px" }}
              onChange={(e) => {
                onJsonChange({
                  ...details,
                  type: e.target.value as TJsonProperty,
                });
              }}
            >
              <MenuItem value={"string"}>String</MenuItem>
              <MenuItem value={"number"}>Number</MenuItem>
              <MenuItem value={"boolean"}>Boolean</MenuItem>
              <MenuItem value={"date"}>Date</MenuItem>
            </Select>
          </FormControl>
          <IconButton
            aria-label="delete"
            size="large"
            onClick={() => {
              if (lastField) {
                dispatch(
                  snackbarActions.setSnackbar({
                    type: "info",
                    message: "Atleast one field is required",
                  })
                );
                return;
              }
              onJsonDelete(details.id);
            }}
          >
            <DeleteIcon fontSize="inherit" color="error" />
          </IconButton>
        </div>
      </div>
    </div>
  );
};

export default JsonKeyValue;
