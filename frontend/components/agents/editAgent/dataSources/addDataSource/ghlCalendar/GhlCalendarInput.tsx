import React, { useEffect, useState } from "react";
import { IGhlCalendarAction, agentsAction } from "@/slices/agents/agentsSlice";
import { IAdvancedSettings } from "../../addDataSource";
import {
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  SelectChangeEvent,
  Button,
  Stack,
  Typography,
  Tooltip,
  TextField,
  InputAdornment,
} from "@mui/material";
import { useRouter } from "next/router";
import { IoIosAddCircle } from "react-icons/io";
import { blue } from "@mui/material/colors";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store";
import { settingsUrl } from "@/slices/subOptions/subOptionSlice";
import { CONSTANTS } from "@/helpers/constants";
import { AntSwitch } from "../ghlChannelInputs";
import InfoOutlinedIcon from "@mui/icons-material/InfoOutlined";
import { addAgentAction } from "@/requests/agents/actions/addAgentAction";
import { editAgentAction } from "@/requests/agents/actions/editAgentAction";
import AddDataSourceMenuItem from "../AddDataSourceMenuItem";
import { logos } from "@/helpers/images";

interface IProp {
  data: IGhlCalendarAction;
  onDataChange: (x: IGhlCalendarAction) => void;
  submitted: boolean;
  setSubmit: () => void;
  type: "add" | "edit";
  onCloseFn: () => void;
  isAdvancedSettings: boolean;
  advancedSettings: IAdvancedSettings;
  actionId?: string;
}

const GhlCalendarInput: React.FC<IProp> = ({
  data,
  onDataChange,
  submitted,
  setSubmit,
  type,
  onCloseFn,
  isAdvancedSettings,
  advancedSettings,
  actionId,
}) => {
  const dispatch = useDispatch();
  const router = useRouter();
  const accountList = useSelector(
    (state: RootState) => state.integrationList.integrationAccountsList
  )
    .filter((item) => item.providerName === data.providerName)
    .map((item) => ({ accountId: item.accountId, name: item.name }));

  const activityList = useSelector(
    (state: RootState) => state.integrationList.integrationProvidersList
  ).find(
    (item) => item.providerId === CONSTANTS.PROVIDERS.GHL_CALENDAR
  )?.activity;

  const { agentId } = router.query as { agentId: string };
  const accountId = data.accountId ?? "";
  const activity = data.activity;
  const [silent, setSilent] = React.useState(data?.silent ?? true);
  const [evaluateOn, setEvaluateOn] = useState<"everyTurn" | "isEmpty">(
    data.metaData?.ghlCalendarMetaData?.evaluateOn ?? "everyTurn"
  );
  const [dayRange, setDayRange] = useState(
    data.metaData?.ghlCalendarMetaData?.dayRange ?? 60
  );
  const [maxRange, setMaxRange] = useState(
    data.metaData?.ghlCalendarMetaData?.maxRange ?? 60
  );
  const [cancelEvent, setCancelEvent] = useState(
    data.metaData?.ghlCalendarMetaData?.cancelEvent ?? false
  );
  const [rescheduleEvent, setRescheduleEvent] = useState(
    data.metaData?.ghlCalendarMetaData?.rescheduleEvent ?? false
  );
  const handleEvaluateOnChange = (event: SelectChangeEvent) => {
    setEvaluateOn(event.target.value as "everyTurn" | "isEmpty");
  };
  const handleDayRangeChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    setDayRange(parseInt(value));
  };
  const handleMaxRangeChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    setMaxRange(parseInt(value));
  };

  const handleChangeActivity = (event: SelectChangeEvent) => {
    // setActivity(event.target.value as "read" | "write");
    onDataChange({ ...data, activity: event.target.value as "read" | "write" });
  };
  const handleChangeAccountId = (event: SelectChangeEvent) => {
    // setAccountId(event.target.value as string);
    onDataChange({ ...data, accountId: event.target.value as string });
  };

  useEffect(() => {
    console.log({ data });
  }, [data]);

  useEffect(() => {
    const accountName =
      accountList.find((item) => item.accountId === accountId)?.name || "";
    const body = {
      agentId,
      action: {
        accountId: data.accountId ?? "",
        providerName: data.providerName,
        promptContent: data.promptContent,
        accountName,
        activity,
        silent,
        isAdvancedSettings,
        advancedSettings,
        includeMainPromptDetails: data.includeMainPromptDetails,
        augmentedQueryData: data.augmentedQueryData,
        metaData: {
          ghlCalendarMetaData: {
            evaluateOn,
            dayRange,
            maxRange,
            cancelEvent,
            rescheduleEvent,
          },
        },
        evalType: data.evalType,
        confidenceValue: data.confidenceValue,
      },
    };
    console.log({ body });

    if (submitted && type === "add") {
      try {
        const response = addAgentAction(body, dispatch);
        response
          .then((resData) => {
            if (resData.status === 201) {
              dispatch(agentsAction.actionAdded(resData.data.action));
              onCloseFn();
            }
          })
          .finally(() => {
            setSubmit();
          });
      } catch (error) {
        console.log(error);
      }
    } else if (submitted && type === "edit") {
      try {
        const bodyForEdit = { ...body, actionId: actionId ?? "" };
        const response = editAgentAction(bodyForEdit, dispatch);
        response
          .then((resData) => {
            if (resData?.status === 200) {
              dispatch(
                agentsAction.actionEdited({
                  ...bodyForEdit.action,
                  actionId: bodyForEdit.actionId,
                })
              );
              onCloseFn();
            }
          })
          .finally(() => {
            setSubmit();
          });
      } catch (error) {
        console.log(error);
      }
    }
  }, [submitted]);

  return (
    <div>
      <FormControl
        sx={{ display: "flex", flexDirection: "column", gap: 2 }}
        fullWidth
      >
        <InputLabel id="evaluate-on-label">Evaluate On</InputLabel>
        <Select
          labelId="evaluate-on-label"
          id="evaluate-on-select"
          value={evaluateOn}
          label="Evaluate On"
          onChange={handleEvaluateOnChange}
        >
          <MenuItem value="everyTurn">Every Turn</MenuItem>
          <MenuItem value="isEmpty">No Events Scheduled</MenuItem>
        </Select>
        <TextField
          label="Day Range"
          type="number"
          inputProps={{ min: 1 }}
          value={dayRange}
          fullWidth
          helperText="Specify the number of future days to include in the calendar lookup for calendar events."
          onChange={handleDayRangeChange}
          InputProps={{
            endAdornment: <InputAdornment position="end">days</InputAdornment>,
          }}
        />
        <TextField
          label="Slot Lookup Range"
          type="number"
          inputProps={{ min: 1 }}
          value={maxRange}
          fullWidth
          helperText="Specify the maximum number of days to include in the calendar lookup for available slots."
          onChange={handleMaxRangeChange}
          InputProps={{
            endAdornment: <InputAdornment position="end">days</InputAdornment>,
          }}
        />
        <FormControl fullWidth>
          <InputLabel id="demo-simple-select-label-account" required>
            Account
          </InputLabel>
          <Select
            labelId="demo-simple-select-label-account"
            id="demo-simple-select-account"
            fullWidth
            value={accountId}
            label="Account"
            onChange={handleChangeAccountId}
            required
          >
            {accountList?.map((item) => (
              <MenuItem
                value={item.accountId}
                key={"Select_activity_key" + item.accountId}
              >
                {item.name || "No name"}
              </MenuItem>
            ))}
            <AddDataSourceMenuItem
              providerName={data.providerName}
              onAuthSuccess={() => {}}
            >
              Add {logos[data.providerName]?.name}
            </AddDataSourceMenuItem>
          </Select>
        </FormControl>
        <FormControl fullWidth>
          <InputLabel id="demo-simple-select-label-activity" required>
            Activity
          </InputLabel>
          <Select
            labelId="demo-simple-select-label-activity"
            id="demo-simple-select-activity"
            fullWidth
            required
            value={activity}
            label="Activity"
            onChange={handleChangeActivity}
          >
            {activityList?.map((item) => (
              <MenuItem value={item} key={item}>
                {item === "read" ? "Read" : "Write"}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
        {activity === "write" && (
          <>
            <Stack direction="row" spacing={1} alignItems="center">
              <AntSwitch
                checked={silent}
                onChange={() => setSilent(!silent)}
                inputProps={{ "aria-label": "silent mode" }}
              />
              <Typography>Silent</Typography>
              <Tooltip title="Book the appointment and don't send any message">
                <InfoOutlinedIcon sx={{ cursor: "pointer" }} />
              </Tooltip>
            </Stack>

            <Stack direction="row" spacing={1} alignItems="center">
              <AntSwitch
                checked={cancelEvent}
                onChange={() => setCancelEvent(!cancelEvent)}
                inputProps={{ "aria-label": "cancel event" }}
              />
              <Typography>Allow Cancel Events</Typography>
              <Tooltip title="Enable the ability to cancel calendar events">
                <InfoOutlinedIcon sx={{ cursor: "pointer" }} />
              </Tooltip>
            </Stack>

            <Stack direction="row" spacing={1} alignItems="center">
              <AntSwitch
                checked={rescheduleEvent}
                onChange={() => setRescheduleEvent(!rescheduleEvent)}
                inputProps={{ "aria-label": "reschedule event" }}
              />
              <Typography>Allow Reschedule Events</Typography>
              <Tooltip title="Enable the ability to reschedule calendar events">
                <InfoOutlinedIcon sx={{ cursor: "pointer" }} />
              </Tooltip>
            </Stack>
          </>
        )}
      </FormControl>
    </div>
  );
};

export default GhlCalendarInput;
