import * as React from "react";
import Button from "@mui/material/Button";
import TextField from "@mui/material/TextField";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogContentText from "@mui/material/DialogContentText";
import DialogTitle from "@mui/material/DialogTitle";
import AutoFixHighIcon from "@mui/icons-material/AutoFixHigh";

export default function SmartGenerate() {
  const [open, setOpen] = React.useState(false);

  const handleClickOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  return (
    <React.Fragment>
      <Button
        startIcon={<AutoFixHighIcon />}
        sx={{ width: "fit-content" }}
        onClick={handleClickOpen}
      >
        Smart Generate
      </Button>
      <Dialog open={open} onClose={handleClose}>
        <DialogTitle>Smart Generate</DialogTitle>
        <DialogContent>
          <DialogContentText>
            This will auto generate a prompt for you. You can edit it after it
            is done
          </DialogContentText>
          <TextField
            autoFocus
            required
            margin="dense"
            id="name"
            name="prompt"
            label="Prompt"
            type="text"
            fullWidth
            variant="outlined"
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClose}>Cancel</Button>
          <Button type="submit">Use</Button>
        </DialogActions>
      </Dialog>
    </React.Fragment>
  );
}
