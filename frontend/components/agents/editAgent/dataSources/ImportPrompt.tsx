import * as React from "react";
import Button from "@mui/material/Button";
import TextField from "@mui/material/TextField";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogContentText from "@mui/material/DialogContentText";
import DialogTitle from "@mui/material/DialogTitle";
import DownloadIcon from "@mui/icons-material/Download";
import {
  Chip,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  SelectChangeEvent,
  Stack,
} from "@mui/material";
import { getOrgAgents } from "@/requests/agents/basic/getAgent";
import { useDispatch } from "react-redux";
import { FaSpinner } from "react-icons/fa6";
import { getActionPrompts } from "@/requests/agents/basic/getActionPrompts";

type ActionDetails = {
  actionId: string;
  accountId: string;
  accountName: string;
  providerName: string;
  activity: string;
  promptContent: string;
};

export default function ImportPrompt({
  onPromptSelect,
}: {
  onPromptSelect: (prompt: string) => void;
}) {
  const dispatch = useDispatch();
  const [open, setOpen] = React.useState(false);
  const [loading, setLoading] = React.useState(false);
  const [loading2, setLoading2] = React.useState(false);
  const [agentList, setAgentList] = React.useState<
    { agentName: string; _id: string }[]
  >([]);
  const [agentId, setAgentId] = React.useState("");
  const [actionDetails, setActionDetails] =
    React.useState<Partial<ActionDetails>>();
  const [promptVal, setPromptVal] = React.useState("");
  const [actionDetailsList, setActionDetailsList] = React.useState<
    ActionDetails[]
  >([]);
  React.useEffect(() => {
    const fetchAgents = async () => {
      const orgId = localStorage.getItem("orgId");
      if (!orgId) {
        return;
      }
      
      setLoading(true);
      try {
        const response = await getOrgAgents(
          { orgId },
          dispatch
        );
        setLoading(false);
        const agents = response.data.data;
        setAgentList(agents);
      } catch (error) {
        setLoading(false);
        console.error('Failed to load agents:', error);
      }
    };
    fetchAgents();
  }, []);

  const handleClickOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  const handleChangeAgentId = async (event: SelectChangeEvent) => {
    setActionDetails({ actionId: "" });
    setActionDetailsList([]);
    setPromptVal("");
    setAgentId(event.target.value as string);
    setLoading2(true);
    const response = await getActionPrompts(
      { agentId: event.target.value as string },
      dispatch
    );
    setLoading2(false);
    if (response?.status === 200) {
      setActionDetailsList(response.data);
    }
  };

  const handleSelectAction = (event: SelectChangeEvent) => {
    setActionDetails(
      actionDetailsList.find((action) => action.actionId === event.target.value)
    );
    const promptContent = actionDetailsList.find(
      (action) => action.actionId === event.target.value
    )?.promptContent;
    setPromptVal(promptContent || "");
  };

  const handleSubmit = () => {
    onPromptSelect(promptVal);
    setOpen(false);
  };

  return (
    <React.Fragment>
      <Button
        variant="outlined"
        startIcon={<DownloadIcon />}
        sx={{ width: "fit-content" }}
        onClick={handleClickOpen}
      >
        Import prompt
      </Button>
      <Dialog open={open} onClose={handleClose} fullWidth>
        <DialogTitle>Import prompt</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Import a prompt from any of your agents.
          </DialogContentText>
          <Stack gap={2} mt={2}>
            <FormControl fullWidth>
              <InputLabel id="demo-simple-select-agent">Agent</InputLabel>
              <Select
                labelId="demo-simple-select-label-agent"
                id="demo-simple-select-label-agent"
                value={agentId}
                label="Agent"
                onChange={handleChangeAgentId}
              >
                {loading && agentList.length === 0 && (
                  <MenuItem className="flex justify-center" disabled>
                    <FaSpinner className="animate-spin" />
                  </MenuItem>
                )}
                {agentList?.map((agent) => (
                  <MenuItem value={agent?._id} key={"Agent" + agent?._id}>
                    {agent?.agentName || "No name"}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
            <FormControl fullWidth>
              <InputLabel id="demo-simple-select-action-details">
                Actions
              </InputLabel>
              <Select
                labelId="demo-simple-select-label-action-details"
                id="demo-simple-select-action-details"
                value={actionDetails?.actionId}
                label="Actions"
                onChange={handleSelectAction}
              >
                {loading2 && (
                  <MenuItem className="flex justify-center" disabled>
                    <FaSpinner className="animate-spin" />
                  </MenuItem>
                )}
                {actionDetailsList?.map((actionDetail) => (
                  <MenuItem
                    value={actionDetail?.actionId}
                    className="flex gap-2"
                    key={"Action" + actionDetail?.actionId}
                  >
                    {actionDetail?.accountName || "No name"}{" "}
                    <Chip label={actionDetail.providerName} />{" "}
                    <Chip label={actionDetail.activity} />
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
            <TextField
              autoFocus
              required
              margin="dense"
              id="name"
              name="prompt"
              label="Prompt"
              type="text"
              fullWidth
              variant="outlined"
              multiline
              minRows={4}
              maxRows={8}
              value={promptVal}
              onChange={(e) => setPromptVal(e.target.value)}
            />
          </Stack>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClose}>Cancel</Button>
          <Button onClick={handleSubmit}>Use</Button>
        </DialogActions>
      </Dialog>
    </React.Fragment>
  );
}
