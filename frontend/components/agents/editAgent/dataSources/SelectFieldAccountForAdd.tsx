import { logos } from "@/helpers/images";
import { getIntegrationAccounts } from "@/requests/integrations/getIntegrationAccounts";
import {
  ISingleIntegration,
  integrationModalActions,
} from "@/slices/settings/integrations/integrationList";
import { RootState } from "@/store";
import {
  FormControl,
  InputLabel,
  Select,
  SelectChangeEvent,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";

interface IProp {
  onAccountChangeFn: (value: string) => void;
  onAccountNameChangeFn: (value: string) => void;
  onProviderChangeFn: (value: string) => void;
}

const SelectFieldAccountForAdd: React.FC<IProp> = ({
  onAccountChangeFn,
  onAccountNameChangeFn,
  onProviderChangeFn,
}) => {
  const dispatch = useDispatch();
  const integrationList = useSelector(
    (state: RootState) => state.integrationList.integrationAccountsList
  );
  useEffect(() => {
    console.log({ integrationList });
  }, [integrationList]);
  const [account, setAccount] = useState("");
  const [groupedAccounts, setGroupedAccounts] = useState<{
    [key: string]: ISingleIntegration[];
  }>({
    ghlTag: [
      {
        name: "Set tag",
        accountId: "ghlTag",
        providerName: "ghlTag",
        credentialId: "",
      },
    ],
  });

  const findProvider = (acc: string) => {
    for (let providerName in groupedAccounts) {
      for (let provider of groupedAccounts[providerName]) {
        if (provider.accountId === acc) {
          return { providerName, accountName: provider.name };
        }
      }
    }
  };

  const HandleAccountChange = (event: SelectChangeEvent) => {
    const acc = event.target.value;
    setAccount(acc);
    onAccountChangeFn(acc);
    const { providerName, accountName } = findProvider(acc) as {
      providerName: string;
      accountName: string;
    };
    onAccountNameChangeFn(accountName);
    onProviderChangeFn(providerName || "");
  };
  useEffect(() => {
    const groupedAccountsFiltered = integrationList?.reduce(
      (groups, account) => {
        if (!groups[account.providerName]) {
          groups[account.providerName] = [];
        }
        groups[account.providerName].push(account);
        return groups;
      },
      {} as { [key: string]: ISingleIntegration[] }
    );
    setGroupedAccounts((prevState) => ({
      ...prevState,
      ...groupedAccountsFiltered,
    }));

    console.log({ groupedAccountsFiltered });
  }, [integrationList]);

  return (
    <FormControl sx={{ width: 250 }}>
      <InputLabel id="demo-simple-select-standard-label" sx={{}}>
        Account
      </InputLabel>
      <Select
        native
        defaultValue=""
        id="grouped-native-select"
        label="Grouping"
        onChange={HandleAccountChange}
      >
        <option aria-label="None" value="" disabled />
        {Object.keys(groupedAccounts).map((providerName) => {
          return (
            <optgroup label={logos[providerName].name} key={providerName}>
              {groupedAccounts[providerName].map((item) => (
                <option value={item.accountId} key={"key" + item.accountId}>
                  {item.name}
                </option>
              ))}
            </optgroup>
          );
        })}
      </Select>
    </FormControl>
  );
};

export default SelectFieldAccountForAdd;
