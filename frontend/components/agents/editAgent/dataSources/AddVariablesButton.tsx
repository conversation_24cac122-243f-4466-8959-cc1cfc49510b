import React, { useState } from "react";
import {
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  TextField,
  List,
  ListItem,
  ListItemText,
  ListSubheader,
  Typography,
  IconButton,
} from "@mui/material";
import LocalOfferIcon from "@mui/icons-material/LocalOffer";

interface Variable {
  name: string;
  group: "Native" | "User Defined";
}

const variables: Variable[] = [
  { name: "var1", group: "Native" },
  { name: "var2", group: "Native" },
  { name: "userVar1", group: "User Defined" },
  { name: "userVar2", group: "User Defined" },
];

interface AddVariablesButtonProps {
  onVariableSelect: (variableName: string) => void;
}

const AddVariablesButton: React.FC<AddVariablesButtonProps> = ({
  onVariableSelect,
}) => {
  const [open, setOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");

  const handleOpen = () => setOpen(true);
  const handleClose = () => setOpen(false);

  const filteredVariables = variables.filter((variable) =>
    variable.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const groupedVariables = filteredVariables.reduce((acc, variable) => {
    if (!acc[variable.group]) {
      acc[variable.group] = [];
    }
    acc[variable.group].push(variable);
    return acc;
  }, {} as Record<string, Variable[]>);

  const handleVariableClick = (variableName: string) => {
    onVariableSelect(variableName);
    handleClose();
  };

  return (
    <>
      <IconButton aria-label="tag" color="primary" onClick={handleOpen}>
        <LocalOfferIcon />
      </IconButton>
      <Dialog open={open} onClose={handleClose} fullWidth maxWidth="xs">
        <DialogTitle>Add Variables</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Search variables"
            type="text"
            fullWidth
            variant="outlined"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
          {Object.entries(groupedVariables).map(([group, vars]) => (
            <List
              key={group}
              subheader={
                <ListSubheader component="div">{group} variables</ListSubheader>
              }
            >
              {vars.map((variable) => (
                <ListItem
                  key={variable.name}
                  button
                  onClick={() => handleVariableClick(variable.name)}
                >
                  <ListItemText primary={variable.name} />
                </ListItem>
              ))}
            </List>
          ))}
          {filteredVariables.length === 0 && (
            <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
              No variables found.
            </Typography>
          )}
        </DialogContent>
      </Dialog>
    </>
  );
};

export default AddVariablesButton;
