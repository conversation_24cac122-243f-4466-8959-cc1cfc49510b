import Line from "@/components/general/shapes/horizontalLine";
import SubHeading from "@/components/settings/crm_ai/subHeading";
import React, { useState } from "react";
import { useSelector } from "react-redux";
import { RootState } from "@/store";
import SinglePrompt from "./singlePrompt";
import AddPrompt from "./addPrompt";
import { Link, Typography } from "@mui/material";

const Prompts = () => {
  const prompts = useSelector(
    (state: RootState) => state.agents.currentAgent?.prompts?.prompt
  );
  const activePrompt = useSelector(
    (state: RootState) => state.agents.currentAgent?.prompts?.currentActive
  );

  const [add, setAdd] = useState(false);
  const handleAdd = () => {
    setAdd(!add);
  };
  return (
    <div className="border-[1px] border-x-slate-300 rounded-md bg-fourth my-5">
      <SubHeading
        value="Prompts"
        addBtn={add}
        addBtnFn={handleAdd}
        additionalDescription={
          <Typography variant="caption">
            Prompts are used to generate responses for the agent. Read more{" "}
            <Link
              href="https://help.capriai.us/prompt-design/creating-prompts"
              target="_blank"
            >
              here
            </Link>
          </Typography>
        }
      />
      <Line />
      {add && <AddPrompt addBtnFn={handleAdd} />}
      {prompts?.map((prompt, index) => (
        <div className="" key={"Single_prompt_div" + prompt.promptId}>
          <SinglePrompt prompt={prompt} activePrompt={activePrompt} />
          {index !== prompts.length - 1 && <Line />}
        </div>
      ))}
    </div>
  );
};

export default Prompts;
