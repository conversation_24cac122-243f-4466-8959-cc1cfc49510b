import React from "react";
import { Text<PERSON><PERSON>, Stack, Typography } from "@mui/material";
import { TemplateVariable } from "@/helpers/agent-templates";

interface VariableInputProps {
  variable: TemplateVariable;
  onChange: (name: string, value: string) => void;
}

export const VariableInput: React.FC<VariableInputProps> = ({
  variable,
  onChange,
}) => {
  return (
    <Stack spacing={1}>
      <Typography variant="subtitle2">{variable.name}</Typography>
      <Typography variant="caption" color="text.secondary">
        {variable.description}
      </Typography>
      <TextField
        fullWidth
        size="small"
        value={variable.value}
        onChange={(e) => onChange(variable.name, e.target.value)}
        placeholder={`Enter ${variable.name}`}
      />
    </Stack>
  );
};
