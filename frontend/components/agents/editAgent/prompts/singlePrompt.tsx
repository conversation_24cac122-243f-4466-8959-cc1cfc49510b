import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>witch,
  <PERSON><PERSON><PERSON>,
  Too<PERSON><PERSON>,
} from "@mui/material";
import React, { ChangeEvent, useEffect, useState } from "react";
import { AiFillEdit } from "react-icons/ai";
import SinglePromptInner from "./singlePromptInner";
import { BsFillTrashFill } from "react-icons/bs";
import GeneralModal from "@/components/general/modal";
import GeneralModalContent from "@/components/general/generalModalContent";
import { deletePrompt } from "@/requests/agents/prompt/deletePrompt";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store";
import { ISinglePrompt, agentsAction } from "@/slices/agents/agentsSlice";
import { setActivePrompt } from "@/requests/agents/prompt/setActivePrompt";
import { snackbarActions } from "@/slices/general/snackbar";
import ContentCopyIcon from "@mui/icons-material/ContentCopy";

interface IProp {
  prompt: ISinglePrompt;
  activePrompt: string;
}

const SinglePrompt: React.FC<IProp> = ({ prompt, activePrompt }) => {
  const dispatch = useDispatch();
  const currentAgent = useSelector(
    (state: RootState) => state.agents.currentAgent
  );
  const [edit, setEdit] = useState(false);
  const [headerValue, setHeaderValue] = useState("");
  const [open, setOpen] = useState(false); //for the warning modal
  const [loading, setLoading] = useState(false); //for the warning modal
  const [isCopied, setIsCopied] = useState(false);
  useEffect(() => {
    setHeaderValue(prompt?.name);
  }, [prompt]);
  const toggleEdit = () => {
    setEdit(!edit);
  };
  const handleHeaderValue = (event: ChangeEvent<HTMLInputElement>) => {
    setHeaderValue(event.target.value);
  };
  const handleClose = () => {
    setOpen(false);
  };
  const handleDelete = async () => {
    setLoading(true);
    //perform the axios request for the delete prompt
    try {
      const response = await deletePrompt(
        {
          agentId: currentAgent?._id,
          promptId: prompt.promptId,
        },
        dispatch
      );
      if (response?.status == 200) {
        dispatch(agentsAction.promptDeleted({ promptId: prompt.promptId }));
      }
    } catch (error: any) {
      dispatch(
        snackbarActions.setSnackbar({
          message:
            error.response?.data?.message ||
            "An unexpected error occurred. Please try again.",
          type: "error",
        })
      );
      console.log(error?.message);
    }
    setLoading(false);
  };
  const handleSetPrompt = async () => {
    const body = {
      agentId: currentAgent?._id,
      promptId: prompt.promptId,
    };
    try {
      const response = await setActivePrompt(body, dispatch);
      if (response?.status == 200) {
        dispatch(
          agentsAction.promptActiveStatusChanged({ promptId: body.promptId })
        );
      }
    } catch (error: any) {
      console.log(error?.message);
    }
  };

  const handleAgentCopy = async () => {
    const content = prompt?.promptId;
    try {
      // Using navigator.clipboard.writeText for modern browsers
      await navigator.clipboard.writeText(content);
      setIsCopied(true);
    } catch (err) {
      // Fallback to document.execCommand for older browsers
      const textArea = document.createElement("textarea");
      textArea.value = content;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand("copy");
      document.body.removeChild(textArea);
      setIsCopied(true);
    }
    dispatch(
      snackbarActions.setSnackbar({
        message: "Prompt ID copied",
        type: "success",
      })
    );
  };
  return (
    <>
      <GeneralModal open={open} onClose={handleClose}>
        <GeneralModalContent
          header="Warning"
          content="Are you sure you want to delete this prompt?"
          rightBtn="Delete"
          isLoading={loading}
          onClose={handleClose}
          customFn={handleDelete}
        />
      </GeneralModal>
      <div className="flex items-center justify-between  text-[18px] py-3 px-4">
        {!edit && (
          <>
            <div className="w-2/12  font-semibold flex items-center line-clamp-2">
              {!edit && <>{prompt?.name ? prompt.name : "No name"}</>}
            </div>
          </>
        )}
        {edit && (
          <>
            <div className="w-full flex items-center justify-between">
              <TextField
                type="text"
                value={headerValue}
                name=""
                id="prompt-name"
                variant="outlined"
                label="Prompt name"
                className="bg-transparent"
                onChange={handleHeaderValue}
              />
              <IconButton
                aria-label="delete"
                color="error"
                onClick={() => {
                  setOpen(true);
                }}
              >
                <BsFillTrashFill />
              </IconButton>
            </div>
          </>
        )}
        {!edit && (
          <>
            <div className="w-7/12 gap-2 line-clamp-4">
              {prompt?.promptContent ? prompt.promptContent : "No prompt"}
            </div>
            <div className="w-3/12 flex items-center justify-end gap-3">
              <div className="">
                {prompt.promptId === activePrompt && (
                  <Chip label="Active" color="success" />
                )}
                {prompt.promptId !== activePrompt && (
                  <Chip
                    label="Set active"
                    variant="outlined"
                    onClick={handleSetPrompt}
                  />
                )}
              </div>
              <div className="">
                <Tooltip title="Copy prompt ID">
                  <IconButton
                    aria-label="delete"
                    size="large"
                    onClick={handleAgentCopy}
                  >
                    <ContentCopyIcon />
                  </IconButton>
                </Tooltip>
              </div>
              <Button
                variant="outlined"
                startIcon={<AiFillEdit />}
                onClick={toggleEdit}
              >
                Edit
              </Button>
            </div>
          </>
        )}
      </div>
      {edit && (
        <SinglePromptInner
          headerValue={headerValue}
          prompt={prompt}
          editFn={toggleEdit}
        />
      )}
    </>
  );
};

export default SinglePrompt;
