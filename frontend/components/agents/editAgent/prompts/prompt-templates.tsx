import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  DialogTitle,
  DialogContent,
  DialogActions,
  Stack,
  Typography,
  Stepper,
  Step,
  StepLabel,
} from "@mui/material";
import DescriptionIcon from "@mui/icons-material/Description";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import SearchBar from "@/components/organization-tab/general/SearchBar";
import HandleOverrideDialog from "./handle-override-dialog";
import {
  Template,
  TemplateVariable,
  VariableDetail,
  extractVariables,
  replaceVariables,
} from "@/helpers/agent-templates";
import { VariableForm } from "./VariableForm";
import { TemplatesList } from "./TemplateList";

interface PromptTemplatesProps {
  currentPrompt: string;
  onPromptChange: (prompt: string) => void;
  templates: Template[];
  variableDetails: VariableDetail[];
}

const steps = ["Select Template", "Fill Values"];

const PromptTemplates: React.FC<PromptTemplatesProps> = ({
  currentPrompt,
  onPromptChange,
  templates,
  variableDetails,
}) => {
  const [dialogOpen, setDialogOpen] = useState(false);
  const [searchText, setSearchText] = useState("");
  const [openWarningDialog, setOpenWarningDialog] = useState(false);
  const [activeStep, setActiveStep] = useState(0);
  const [selectedTemplate, setSelectedTemplate] = useState<Template | null>(
    null
  );
  const [expanded, setExpanded] = useState<number | null>(null);
  const [templateVariables, setTemplateVariables] = useState<
    TemplateVariable[]
  >([]);

  const handleExpandClick = (i: number) => {
    setExpanded(expanded === i ? null : i);
  };

  const handleSelectTemplate = (template: Template) => {
    // Extract unique variables using Array.from instead of spread operator
    const variables = Array.from(new Set(extractVariables(template.prompt)));

    // Create template variables array with unique variables only
    const templateVars = variables.map((varName) => {
      const details = variableDetails.find((v) => v.name === varName);
      return {
        name: varName,
        value: "",
        description: details?.description || "No description available",
        occurrences: template.prompt.split(varName).length - 1,
      };
    });

    setSelectedTemplate(template);
    setTemplateVariables(templateVars);

    if (templateVars.length > 0) {
      setActiveStep(1);
    } else if (currentPrompt) {
      setOpenWarningDialog(true);
    } else {
      applyTemplate(template.prompt, {});
    }
  };

  const handleVariableChange = (name: string, value: string) => {
    setTemplateVariables((prev) =>
      prev.map((v) => (v.name === name ? { ...v, value } : v))
    );
  };

  const handleBack = () => {
    setActiveStep(0);
    setTemplateVariables([]);
    setSelectedTemplate(null);
  };

  const applyTemplate = (
    templatePrompt: string,
    variables: Record<string, string>
  ) => {
    const finalPrompt = replaceVariables(templatePrompt, variables);
    onPromptChange(finalPrompt);
    handleCloseAll();
  };

  const handleApplyTemplate = () => {
    if (!selectedTemplate) return;

    const variables = templateVariables.reduce(
      (acc, curr) => ({ ...acc, [curr.name]: curr.value }),
      {}
    );

    if (currentPrompt) {
      setOpenWarningDialog(true);
    } else {
      applyTemplate(selectedTemplate.prompt, variables);
    }
  };

  const handleOverride = () => {
    if (!selectedTemplate) return;

    const variables = templateVariables.reduce(
      (acc, curr) => ({ ...acc, [curr.name]: curr.value }),
      {}
    );

    applyTemplate(selectedTemplate.prompt, variables);
  };

  const handleCloseAll = () => {
    setDialogOpen(false);
    setOpenWarningDialog(false);
    setActiveStep(0);
    setSelectedTemplate(null);
    setTemplateVariables([]);
    setSearchText("");
  };

  const isVariablesComplete = templateVariables.every(
    (v) => v.value.trim() !== ""
  );

  return (
    <>
      <Button
        startIcon={<DescriptionIcon />}
        variant="outlined"
        onClick={() => setDialogOpen(true)}
      >
        Templates
      </Button>

      <Dialog
        onClose={handleCloseAll}
        open={dialogOpen}
        fullWidth
        maxWidth="md"
      >
        <div className="bg-gray-100">
          <DialogTitle>
            <Stack spacing={2}>
              <Stack
                direction="row"
                spacing={1}
                justifyContent="space-between"
                alignItems="center"
              >
                {activeStep === 1 && (
                  <Button
                    startIcon={<ArrowBackIcon />}
                    variant="text"
                    onClick={handleBack}
                    size="small"
                  >
                    Back to Templates
                  </Button>
                )}
                <Typography variant="h6">Prompt Templates</Typography>
                {activeStep === 0 && (
                  <SearchBar
                    val={searchText}
                    onchangeFn={setSearchText}
                    searchText="Search Templates..."
                  />
                )}
              </Stack>
              <Stepper
                activeStep={activeStep}
                sx={{ width: "450px", alignSelf: "center" }}
              >
                {steps.map((label) => (
                  <Step key={label}>
                    <StepLabel>{label}</StepLabel>
                  </Step>
                ))}
              </Stepper>
            </Stack>
          </DialogTitle>

          <DialogContent>
            {activeStep === 0 ? (
              <TemplatesList
                templates={templates}
                expanded={expanded}
                searchText={searchText}
                onExpandClick={handleExpandClick}
                onSelectTemplate={handleSelectTemplate}
              />
            ) : (
              selectedTemplate && (
                <VariableForm
                  variables={templateVariables}
                  selectedTemplate={selectedTemplate}
                  onChange={handleVariableChange}
                />
              )
            )}
          </DialogContent>

          {activeStep === 1 && (
            <DialogActions>
              <Button variant="text" color="error" onClick={handleCloseAll}>
                Cancel
              </Button>
              <Button
                onClick={handleApplyTemplate}
                disabled={!isVariablesComplete}
                variant="contained"
                color="primary"
              >
                Apply Template
              </Button>
            </DialogActions>
          )}
        </div>
      </Dialog>

      <HandleOverrideDialog
        open={openWarningDialog}
        onOverride={handleOverride}
        onClose={() => setOpenWarningDialog(false)}
      />
    </>
  );
};

export default PromptTemplates;
