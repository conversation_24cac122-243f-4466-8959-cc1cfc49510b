import Line from "@/components/general/shapes/horizontalLine";
import { addNewPrompt } from "@/requests/agents/prompt/addNewPrompt";
import { agentsAction } from "@/slices/agents/agentsSlice";
import { organizationAction } from "@/slices/organization/organizationSlice";
import { RootState } from "@/store";
import { Button, TextField } from "@mui/material";
import React, { ChangeEvent, useState } from "react";
import { AiFillSave } from "react-icons/ai";
import { useDispatch, useSelector } from "react-redux";
import PromptTemplates from "./prompt-templates";
import EnhancedTextField from "../dataSources/TextField";
import { templates, variableDetails } from "@/helpers/agent-templates";

interface IProp {
  addBtnFn: () => void;
}

const AddPrompt: React.FC<IProp> = ({ addBtnFn }) => {
  const dispatch = useDispatch();
  const currentAgent = useSelector(
    (state: RootState) => state.agents.currentAgent
  );
  const setupStatus = useSelector(
    (state: RootState) => state.organization.setupStatus
  );

  const [loading, setLoading] = useState(false);
  const [promptHeaderValue, setPromptHeaderValue] = useState("");
  const [promptValue, setPromptValue] = useState("");

  const handlePromptChange = (val: string) => {
    setPromptValue(val);
  };
  const handlePromptHeaderChange = (event: ChangeEvent<HTMLInputElement>) => {
    setPromptHeaderValue(event.target.value);
  };

  const handleSubmit = async () => {
    const body = {
      agentId: currentAgent?._id,
      prompt: {
        name: promptHeaderValue,
        promptContent: promptValue,
      },
    };
    setLoading(true);
    const response = await addNewPrompt(body, dispatch);
    setLoading(false);
    if (response?.status == 200) {
      dispatch(agentsAction.newPromptAdded(response.data));
      addBtnFn();
      if (!!currentAgent?.aiProvider?.accountId) {
        dispatch(
          organizationAction.setupStatusChanged({
            ...setupStatus,
            agentSetup: true,
          })
        );
      }
    }
    console.log({ body });
  };
  return (
    <>
      <div className="px-4 py-2 pb-3 flex flex-col gap-2">
        <div className="">
          <TextField
            type="text"
            value={promptHeaderValue}
            name=""
            id="prompt-name"
            variant="outlined"
            label="Prompt name"
            className="bg-transparent"
            onChange={handlePromptHeaderChange}
            fullWidth
          />
        </div>
        <div className="">
          <EnhancedTextField
            className="my-2 py-1 w-full rounded-sm text-primary outline-none"
            multiline
            minRows={4}
            maxRows={8}
            label={"Prompt"}
            value={promptValue}
            variant="outlined"
            onChange={handlePromptChange}
          />
          <PromptTemplates
            currentPrompt={promptValue}
            onPromptChange={setPromptValue}
            templates={templates}
            variableDetails={variableDetails}
          />
        </div>
        <div className="flex justify-end gap-5">
          <Button variant="text" onClick={addBtnFn}>
            Cancel
          </Button>
          <Button
            variant="contained"
            startIcon={<AiFillSave />}
            onClick={handleSubmit}
            disabled={loading}
            className="saveMainPromptBtn"
            id="saveMainPromptBtn"
          >
            Save
          </Button>
        </div>
      </div>
      <Line />
    </>
  );
};

export default AddPrompt;
