import { TextField } from "@mui/material";
import React from "react";
import { IAdvancedSettings } from "../singlePromptInner";

interface IProp {
  advancedSettings: IAdvancedSettings;
  handleFallbackPromptChange: (x: string) => void;
}

const AdvancedSettings: React.FC<IProp> = ({
  advancedSettings,
  handleFallbackPromptChange,
}) => {
  return (
    <div className="my-2">
      <TextField
        variant="outlined"
        label="Custom fallback prompt"
        value={advancedSettings.customFallbackPrompt}
        onChange={(e) => handleFallbackPromptChange(e.target.value)}
        fullWidth
        multiline
        maxRows={8}
        minRows={4}
      />
    </div>
  );
};

export default AdvancedSettings;
