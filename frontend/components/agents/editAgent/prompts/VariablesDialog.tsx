import React from "react";
import {
  <PERSON><PERSON>,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Stack,
} from "@mui/material";
import { TemplateVariable } from "@/helpers/agent-templates";
import { VariableInput } from "./VariablesInput";

interface VariablesDialogProps {
  open: boolean;
  variables: TemplateVariable[];
  onClose: () => void;
  onSubmit: () => void;
  onChange: (name: string, value: string) => void;
}

export const VariablesDialog: React.FC<VariablesDialogProps> = ({
  open,
  variables,
  onClose,
  onSubmit,
  onChange,
}) => {
  const isComplete = variables.every((v) => v.value.trim() !== "");

  return (
    <Dialog open={open} onClose={onClose} fullWidth maxWidth="sm">
      <DialogTitle>Fill in Template Variables</DialogTitle>
      <DialogContent>
        <Stack spacing={3} sx={{ mt: 2 }}>
          {variables.map((variable) => (
            <VariableInput
              key={variable.name}
              variable={variable}
              onChange={onChange}
            />
          ))}
        </Stack>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
        <Button
          onClick={onSubmit}
          disabled={!isComplete}
          variant="contained"
          color="primary"
        >
          Apply Template
        </Button>
      </DialogActions>
    </Dialog>
  );
};
