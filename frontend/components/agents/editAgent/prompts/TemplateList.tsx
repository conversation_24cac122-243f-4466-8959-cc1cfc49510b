import React from "react";
import {
  <PERSON><PERSON>,
  Card,
  CardContent,
  Card<PERSON>ctions,
  Button,
  ListItemText,
  Chip,
  Stack,
  IconButton,
  Box,
} from "@mui/material";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import ExpandLessIcon from "@mui/icons-material/ExpandLess";
import { Template } from "@/helpers/agent-templates";

interface TemplatesListProps {
  templates: Template[];
  expanded: number | null;
  searchText: string;
  onExpandClick: (index: number) => void;
  onSelectTemplate: (template: Template) => void;
}

export const TemplatesList: React.FC<TemplatesListProps> = ({
  templates,
  expanded,
  searchText,
  onExpandClick,
  onSelectTemplate,
}) => {
  const filteredTemplates = templates.filter(
    (template) =>
      template.name.toLowerCase().includes(searchText.toLowerCase()) ||
      template.tags.some((tag) =>
        tag.toLowerCase().includes(searchText.toLowerCase())
      )
  );

  return (
    <Grid container spacing={2}>
      {filteredTemplates.map((template, index) => (
        <Grid item xs={12} key={index}>
          <Card>
            <CardContent>
              <ListItemText
                primary={template.name}
                secondary={
                  <>
                    <Box
                      component="div"
                      sx={{
                        display: "-webkit-box",
                        WebkitLineClamp: expanded === index ? undefined : 4,
                        WebkitBoxOrient: "vertical",
                        overflow: "hidden",
                      }}
                    >
                      {template.prompt}
                    </Box>
                    <Stack direction="row" justifyContent="center">
                      <IconButton
                        onClick={() => onExpandClick(index)}
                        aria-expanded={expanded === index}
                        aria-label="show more"
                      >
                        {expanded === index ? (
                          <ExpandLessIcon />
                        ) : (
                          <ExpandMoreIcon />
                        )}
                      </IconButton>
                    </Stack>
                  </>
                }
              />
              <Stack direction="row" spacing={1}>
                {template.tags.map((tag, index) => (
                  <Chip key={index} label={tag} variant="filled" />
                ))}
              </Stack>
            </CardContent>
            <CardActions>
              <Button
                size="small"
                color="primary"
                variant="text"
                onClick={() => onSelectTemplate(template)}
              >
                Use Template
              </Button>
            </CardActions>
          </Card>
        </Grid>
      ))}
    </Grid>
  );
};
