import React from "react";
import { <PERSON><PERSON>, Typo<PERSON>, <PERSON><PERSON>ield, <PERSON><PERSON>, Box } from "@mui/material";
import { Template, TemplateVariable } from "@/helpers/agent-templates";

interface VariableFormProps {
  variables: TemplateVariable[];
  selectedTemplate: Template;
  onChange: (name: string, value: string) => void;
}

export const VariableForm: React.FC<VariableFormProps> = ({
  variables,
  selectedTemplate,
  onChange,
}) => {
  return (
    <Stack spacing={3}>
      <Alert severity="info">
        Fill in the variables for template:{" "}
        <strong>{selectedTemplate.name}</strong>
      </Alert>

      <Typography variant="body2" color="text.secondary">
        Template Preview:
        <Box
          sx={{
            mt: 1,
            p: 2,
            bgcolor: "grey.50",
            borderRadius: 1,
            fontFamily: "monospace",
          }}
        >
          {selectedTemplate.prompt}
        </Box>
      </Typography>

      {variables.map((variable) => (
        <Stack key={variable.name} spacing={1}>
          <Typography variant="subtitle2">{variable.name}</Typography>
          <Typography variant="caption" color="text.secondary">
            {variable.description}
          </Typography>
          <TextField
            fullWidth
            size="small"
            value={variable.value}
            onChange={(e) => onChange(variable.name, e.target.value)}
            placeholder={`Enter ${variable.name}`}
          />
        </Stack>
      ))}
    </Stack>
  );
};
