import { editPrompt } from "@/requests/agents/prompt/editPrompt";
import { ISinglePrompt, agentsAction } from "@/slices/agents/agentsSlice";
import { RootState } from "@/store";
import { <PERSON>ton, FormControlLabel, Switch, TextField } from "@mui/material";
import React, { ChangeEvent, useEffect, useState } from "react";
import { AiFillEdit, AiFillSave } from "react-icons/ai";
import { useDispatch, useSelector } from "react-redux";
import AdvancedSettings from "./advancedSettings/AdvancedSettings";
import PromptTemplates from "./prompt-templates";
import EnhancedTextField from "../dataSources/TextField";
import { templates, variableDetails } from "@/helpers/agent-templates";

interface IProp {
  headerValue: string;
  prompt: ISinglePrompt;
  editFn: () => void;
}

export interface IAdvancedSettings {
  customFallbackPrompt: string;
}

const SinglePromptInner: React.FC<IProp> = ({
  headerValue,
  prompt,
  editFn,
}) => {
  const dispatch = useDispatch();
  const currentAgent = useSelector(
    (state: RootState) => state.agents.currentAgent
  );
  const [loading, setLoading] = useState(false);
  const [isAdvancedSettings, setIsAdvancedSettings] = useState(
    prompt.isFallbackPrompt ?? false
  );
  const [advancedSettings, setAdvancedSettings] = useState<IAdvancedSettings>({
    customFallbackPrompt: prompt.customFallbackPrompt || "",
  });
  const [promptValue, setPromptValue] = useState(prompt.promptContent);
  const handleChange = (value: string) => {
    setPromptValue(value);
  };
  const handleSubmit = async () => {
    console.log({
      customFallbackPrompt:
        (isAdvancedSettings && advancedSettings.customFallbackPrompt) || "",
    });

    const body = {
      agentId: currentAgent._id,
      promptId: prompt.promptId,
      prompt: {
        name: headerValue,
        promptContent: promptValue,
        isFallbackPrompt: isAdvancedSettings,
        customFallbackPrompt: advancedSettings.customFallbackPrompt,
      },
    };
    setLoading(true);
    const response = await editPrompt(body, dispatch);
    setLoading(false);
    const { agentId, ...dispatchBody } = body;
    if (response?.status == 200) {
      dispatch(agentsAction.promptEdited(dispatchBody));
      editFn();
    }
    console.log({ dispatchBody });
  };
  return (
    <div className="px-4 pb-3">
      <div className="mb-2">
        <EnhancedTextField
          className="my-2 py-1 w-full rounded-sm text-primary outline-none"
          multiline
          maxRows={8}
          label={"Prompt"}
          name={prompt.promptId}
          id={prompt.promptId}
          value={promptValue}
          variant="outlined"
          onChange={handleChange}
          minRows={4}
        />
        <PromptTemplates
          currentPrompt={promptValue}
          onPromptChange={setPromptValue}
          templates={templates}
          variableDetails={variableDetails}
        />
      </div>
      <FormControlLabel
        control={
          <Switch
            checked={isAdvancedSettings}
            onChange={() => setIsAdvancedSettings((prevState) => !prevState)}
          />
        }
        label="Advanced settings"
      />
      {isAdvancedSettings && (
        <AdvancedSettings
          advancedSettings={advancedSettings}
          handleFallbackPromptChange={(x: string) =>
            setAdvancedSettings({
              ...advancedSettings,
              customFallbackPrompt: x,
            })
          }
        />
      )}
      <div className="flex justify-end gap-5">
        <Button variant="text" onClick={editFn}>
          Cancel
        </Button>
        <Button
          variant="contained"
          className="bg-blue-500 saveMainPromptBtn"
          startIcon={<AiFillSave />}
          onClick={handleSubmit}
          disabled={loading}
          id="saveMainPromptBtn"
        >
          Save
        </Button>
      </div>
    </div>
  );
};

export default SinglePromptInner;
