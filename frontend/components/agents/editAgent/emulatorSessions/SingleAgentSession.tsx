import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "@mui/material";
import { useRouter } from "next/router";
import React from "react";
import { AiFillDelete } from "react-icons/ai";
import { BiLinkExternal } from "react-icons/bi";
import { BsFillTrashFill } from "react-icons/bs";
import { MdDeleteOutline } from "react-icons/md";

interface IProp {
  agentId: string;
}

const SingleAgentSession: React.FC<IProp> = ({ agentId }) => {
  const router = useRouter();
  const currentAgentId = router.query.agentId as string;
  // const currentAgentId = router.query.agentId?.toString();
  return (
    <>
      <div
        className={`flex px-4 py-3 ${
          currentAgentId === agentId && "dynamic-border"
        }`}
      >
        <div className="w-2/12 flex items-center text-[18px]  font-semibold">
          {currentAgentId === agentId ? "Current Agent" : "Unknown"}
        </div>
        <div className="w-7/12"></div>
        <div className="w-3/12 flex justify-end gap-4">
          <Tooltip title="Go to the emulator sessions">
            <IconButton aria-label="delete" color="primary" size="large">
              <BiLinkExternal fontSize="inherit" />
            </IconButton>
          </Tooltip>
          <IconButton color="error" aria-label="delete">
            <BsFillTrashFill />
          </IconButton>
        </div>
      </div>

      <style jsx>{`
        @keyframes gradient {
          0% {
            background-position: 0% 50%;
          }
          50% {
            background-position: 100% 50%;
          }
          100% {
            background-position: 0% 50%;
          }
        }

        .dynamic-border {
          border: 4px solid;
          border-image-source: linear-gradient(
            270deg,
            red,
            yellow,
            lime,
            aqua,
            blue,
            magenta,
            red
          );
          border-image-slice: 1;
          animation: gradient 3s ease infinite;
        }
      `}</style>
    </>
  );
};

export default SingleAgentSession;
