import Line from "@/components/general/shapes/horizontalLine";
import SubHeading from "@/components/settings/crm_ai/subHeading";
import React, { useEffect, useState } from "react";
import AddEmulatorSession from "./AddEmulatorSession";
import SingleAgentSession from "./SingleAgentSession";

const EmulatorSessions = () => {
  const [add, setAdd] = useState(false);
  const [sessions, setSessions] = useState<
    { agentId: string; agentName: string }[]
  >([]);
  const handleAdd = () => {
    setAdd(!add);
  };
  useEffect(() => {
    setSessions([
      { agentId: "64c8e9f5d98d1390ef9f4939", agentName: "Agent 1" },
      { agentId: "02", agentName: "Agent 2" },
    ]);
  }, []);
  return (
    <div className="border-[1px] border-x-slate-300 rounded-md bg-fourth my-5">
      <SubHeading value="Training sessions" addBtn={add} addBtnFn={handleAdd} />
      <Line />
      {add && <AddEmulatorSession editFn={handleAdd} />}
      {sessions.map((session, index) => (
        <>
          <div className="" key={"Single_prompt_div" + session.agentId}>
            <SingleAgentSession
              agentId={session.agentId}
              key={"Single_prompt_div" + session.agentId}
            />
            {index !== sessions.length - 1 && <Line />}
          </div>
        </>
      ))}
    </div>
  );
};

export default EmulatorSessions;
