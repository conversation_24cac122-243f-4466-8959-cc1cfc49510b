import React, { ChangeEvent, useEffect, useState } from "react";
import {
  Button,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  SelectChangeEvent,
} from "@mui/material";
import { AiFillSave } from "react-icons/ai";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store";
import { addAgentAction } from "@/requests/agents/actions/addAgentAction";
import { IAgent, agentsAction } from "@/slices/agents/agentsSlice";
import { getOrgAgents } from "@/requests/agents/basic/getAgent";
import { BiImport } from "react-icons/bi";

interface IProp {
  editFn: () => void;
}

const AddEmulatorSession: React.FC<IProp> = ({ editFn }) => {
  const dispatch = useDispatch();
  const [agents, setAgents] = useState<IAgent[]>([]);
  const [selectedAgent, setSelectedAgent] = useState<string>("");

  const handleSubmit = async () => {
    const body = {};
    try {
    } catch (error: any) {
      console.log(error.message);
    }
  };
  const handleAgentChange = (event: SelectChangeEvent) => {
    setSelectedAgent(event.target.value);
  };
  useEffect(() => {
    // setAgents([
    //   { agentId: "1", name: "Agent 1" },
    //   { agentId: "2", name: "Agent 2" },
    // ]);
    const response = getOrgAgents(
      {
        orgId: localStorage.getItem("orgId") || "",
      },
      dispatch
    );
    response.then((res) => setAgents(res?.data.data));
    console.log({ agents });
  }, []);
  return (
    <div className="px-4 py-4 flex flex-col gap-4">
      <FormControl sx={{ width: 250 }}>
        <InputLabel id="demo-simple-select-standard-label" sx={{}}>
          Agents
        </InputLabel>
        <Select
          value={selectedAgent}
          id="grouped-native-select"
          label="Agents"
          onChange={handleAgentChange}
        >
          {agents.map((agent) => (
            <MenuItem value={agent._id} key={"Agent_Name_" + agent._id}>
              {agent.agentName || "No name"}
            </MenuItem>
          ))}
        </Select>
      </FormControl>
      <div className="flex justify-end gap-5">
        <Button variant="text" onClick={editFn}>
          Cancel
        </Button>
        <Button
          variant="contained"
          className="bg-blue-500"
          startIcon={<BiImport />}
          onClick={handleSubmit}
        >
          Import
        </Button>
      </div>
    </div>
  );
};

export default AddEmulatorSession;
