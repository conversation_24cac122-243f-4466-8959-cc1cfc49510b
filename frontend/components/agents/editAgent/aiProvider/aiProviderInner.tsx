import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  FormControl,
  Link,
  Stack,
} from "@mui/material";
import React, { useState } from "react";
import { settingsUrl } from "@/slices/subOptions/subOptionSlice";
import { AiFillSave } from "react-icons/ai";
import {
  <PERSON><PERSON>iP<PERSON>ider,
  IFailsafeAIProvider,
  agentsAction,
} from "@/slices/agents/agentsSlice";
import FailsafeAICard from "./FailsafeAICard";
import AiSettingsForm from "./AiSettingForm";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store";
import { editAiProvider } from "@/requests/agents/aiProvider/editAiProvider";
import { useRouter } from "next/router";
import { organizationAction } from "@/slices/organization/organizationSlice";
import { useCurrentPlan } from "@/hooks/useCurrentPlan";

interface IProp {
  editFn: () => void;
}

const DUMMY = {
  accountId: "8197a479-e932-4f0f-9963-09cd642b9b87",
  accountName: "Sobebar Gemini AI Test",
  companyId: "gemini",
  modelName: "gemini-1.5-flash",
  advancedSettings: {
    frequencyPenalty: 0.4,
    maxLength: 200,
    optimize: "accuracy",
    temperature: 1,
  },
  isAdvancedSettings: true,
};

export interface IAdvSettings {
  temperature: number;
  maxLength: number;
  frequencyPenalty: number;
}

const AiProviderInner: React.FC<IProp> = ({ editFn }) => {
  const dispatch = useDispatch();
  const router = useRouter();
  const { agentId } = router.query as { agentId: string };
  const aiAccountsList =
    useSelector((state: RootState) => state.ai.aiAccountsList) || [];
  const { aiProviderList } = useCurrentPlan();
  const currentAgent = useSelector(
    (state: RootState) => state.agents.currentAgent
  );
  const {
    aiProvider: currentAiProvider,
    failsafeAiProvider: currentFailsafeAiProvider,
  } = currentAgent;
  const setupStatus = useSelector(
    (state: RootState) => state.organization.setupStatus
  );
  const [editedAiProviderState, setEditedAiProviderState] =
    useState(currentAiProvider);

  const [editedFailsafeAiProviderState, setEditedFailsafeAiProviderState] =
    useState(currentFailsafeAiProvider);
  const [loading, setLoading] = useState(false);

  const handleSubmit = async () => {
    const payload = {
      agentId,
      aiProvider: {
        companyId: editedAiProviderState.companyId,
        modelName: editedAiProviderState.modelName,
        accountId: editedAiProviderState.accountId,
        accountName: editedAiProviderState.accountName,
      },
      isAdvancedSettings: editedAiProviderState.isAdvancedSettings,
      advancedSettings: editedAiProviderState.advancedSettings,
      failsafeAiProvider: editedFailsafeAiProviderState,
    };
    console.log({ payload });
    setLoading(true);
    const response = await editAiProvider(payload, dispatch);
    setLoading(false);
    if (response?.status == 201) {
      dispatch(agentsAction.aiProviderEdited(editedAiProviderState));
      dispatch(
        agentsAction.failSafeAIProviderUpdated(editedFailsafeAiProviderState)
      );
      editFn();
      if (!!currentAgent?.prompts?.prompt?.length) {
        dispatch(
          organizationAction.setupStatusChanged({
            ...setupStatus,
            agentSetup: true,
          })
        );
      }
    }
  };
  const handleAiProviderChange = (values: IAiProvider) => {
    console.log(values);
    setEditedAiProviderState(values);
  };
  const handleFailsafeAiProviderChange = (values: IFailsafeAIProvider) => {
    console.log(values);
    setEditedFailsafeAiProviderState(values);
  };

  return (
    <div className="text-[18px] w-full px-4 py-3">
      <div className="infoMsg">
        This will be the language model that will power your conversations
      </div>
      <div className="mt-2">
        <Alert severity="info" sx={{ width: "fit-content" }}>
          You don&apos;t have to setup an AI provider if you use Capri&apos;s
          hosted LLMs. If you want to use a different AI provider, then you can
          setup the key{" "}
          <Link href={settingsUrl}>
            <b>here</b>{" "}
          </Link>
        </Alert>
      </div>
      <div className="my-3">
        <FormControl
          variant="standard"
          fullWidth
          sx={{
            m: 1,
            rowGap: 2,
          }}
        >
          <Stack direction={"row"} sx={{ width: "100%", gap: 2 }}>
            <Stack direction={"column"} sx={{ width: "50%" }}>
              <AiSettingsForm
                aiProviderList={aiProviderList}
                aiAccountsList={aiAccountsList}
                onChange={handleAiProviderChange}
                initialValues={currentAiProvider}
              />
            </Stack>
            <Stack direction={"column"} sx={{ width: "50%" }}>
              <FailsafeAICard onChange={handleFailsafeAiProviderChange} />
            </Stack>
          </Stack>
        </FormControl>
      </div>

      <div className="flex justify-end gap-5">
        <Button variant="text" onClick={editFn}>
          Cancel
        </Button>
        <Button
          variant="contained"
          className="bg-blue-500"
          disabled={
            loading ||
            !editedAiProviderState.accountId ||
            !editedFailsafeAiProviderState.accountId
          }
          startIcon={<AiFillSave />}
          onClick={handleSubmit}
          id="agentAi_btn"
        >
          Save
          {loading && (
            <CircularProgress size={20} thickness={5} sx={{ ml: 0.5 }} />
          )}
        </Button>
      </div>
    </div>
  );
};

export default AiProviderInner;
