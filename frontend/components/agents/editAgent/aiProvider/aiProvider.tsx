import Line from "@/components/general/shapes/horizontalLine";
import SubHeading from "@/components/settings/crm_ai/subHeading";
import { RootState } from "@/store";
import { Button } from "@mui/material";
import Image from "next/image";
import React, { useState } from "react";
import { AiFillEdit } from "react-icons/ai";
import { useSelector } from "react-redux";
import AiProviderInner from "./aiProviderInner";
import { logos } from "@/helpers/images";
import { CONSTANTS } from "@/helpers/constants";
import { CAPRI_HOSTED_COMPANY_IDS } from "@/slices/settings/ai-crm/aiList";

const AiProvider = () => {
  const [edit, setEdit] = useState(false);
  const currentAgent = useSelector(
    (state: RootState) => state.agents.currentAgent
  );

  const CAPRI_HOSTED_NAME = "Capri Hosted LLM";

  const currentAgentAiProviderName = CAPRI_HOSTED_COMPANY_IDS.includes(
    currentAgent?.aiProvider?.companyId
  )
    ? CAPRI_HOSTED_NAME
    : currentAgent?.aiProvider?.modelName;

  const companyId = currentAgent?.aiProvider?.companyId;
  console.log({ companyId });

  const toggleEdit = () => {
    setEdit(!edit);
  };
  return (
    <div className="border-[1px] border-x-slate-300 rounded-md bg-fourth my-5">
      <SubHeading value="AI Provider" />
      <Line />
      <div className="flex items-center justify-between  text-[18px] py-3 px-4">
        <div className="w-2/12 font-semibold line-clamp-2">
          {currentAgentAiProviderName}
        </div>
        {!edit && (
          <>
            <div className="w-8/12 font-semibold flex items-center gap-2">
              {!!!companyId && <div>Not set</div>}
              {!!companyId && (
                <Image
                  alt="AI-provider-logo"
                  src={logos[companyId]?.logo || logos["capriHostedLlm"].logo}
                  height={30}
                  width={30}
                />
              )}
            </div>
            <div className="w-2/12 flex justify-end">
              <Button
                variant="outlined"
                startIcon={<AiFillEdit />}
                onClick={toggleEdit}
              >
                Edit
              </Button>
            </div>
          </>
        )}
      </div>
      {edit && <AiProviderInner editFn={toggleEdit} />}
    </div>
  );
};

export default AiProvider;
