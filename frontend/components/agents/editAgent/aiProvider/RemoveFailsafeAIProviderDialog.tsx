import React, { useState } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  <PERSON>alogActions,
  Button,
  IconButton,
  Toolt<PERSON>,
} from "@mui/material";
import DeleteIcon from "@mui/icons-material/Delete";

interface RemoveFailsafeAIProviderDialogProps {
  onConfirm: () => void;
}

const RemoveFailsafeAIProviderDialog: React.FC<
  RemoveFailsafeAIProviderDialogProps
> = ({ onConfirm }) => {
  const [isOpen, setIsOpen] = useState(false);

  const handleOpen = () => setIsOpen(true);
  const handleClose = () => setIsOpen(false);

  const handleConfirm = () => {
    onConfirm();
    handleClose();
  };

  return (
    <>
      <Tooltip title="Remove Failsafe AI Provider">
        <IconButton
          aria-label="remove failsafe AI provider"
          onClick={handleOpen}
          color="error"
        >
          <DeleteIcon />
        </IconButton>
      </Tooltip>
      <Dialog
        open={isOpen}
        onClose={handleClose}
        aria-labelledby="remove-failsafe-dialog-title"
        aria-describedby="remove-failsafe-dialog-description"
      >
        <DialogTitle id="remove-failsafe-dialog-title">
          Disable Failsafe AI Provider
        </DialogTitle>
        <DialogContent>
          <DialogContentText id="remove-failsafe-dialog-description">
            Are you sure you want to disable the failsafe AI provider? This
            action will remove the backup AI service for your agent.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClose} color="primary">
            Cancel
          </Button>
          <Button onClick={handleConfirm} color="error" variant="contained">
            Disable Failsafe
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default RemoveFailsafeAIProviderDialog;
