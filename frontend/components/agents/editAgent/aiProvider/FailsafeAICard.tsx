import React, { useEffect, useState, useMemo } from "react";
import {
  Card,
  CardContent,
  Typography,
  Button,
  Box,
  Stack,
  Alert,
  Autocomplete,
  FormControl,
  TextField,
} from "@mui/material";
import { Warning as WarningIcon } from "@mui/icons-material";
import { IFailsafeAIProvider } from "@/slices/agents/agentsSlice";
import RemoveFailsafeAIProviderDialog from "./RemoveFailsafeAIProviderDialog";
import { useSelector } from "react-redux";
import { RootState } from "@/store";
import {
  singleAiModelState,
  AiProviderCompany,
  CAPRI_HOSTED_COMPANY_IDS,
} from "@/slices/settings/ai-crm/aiList";
import { useAiProviderGrouping } from "@/hooks/useAiProviderGrouping";

interface GroupedModel extends singleAiModelState {
  companyId: string;
  companyName: string;
  accountId: string;
  accountName: string;
  isCapriHosted?: boolean;
}

interface FailsafeAICardProps {
  onChange: (x: IFailsafeAIProvider) => void;
}

interface IFailSafeNotSetup {
  handleOpen: () => void;
}

export const FailSafeNotSetup: React.FC<IFailSafeNotSetup> = ({
  handleOpen,
}) => {
  return (
    <Card sx={{ maxWidth: 400, margin: "auto" }}>
      <CardContent>
        <Box display="flex" alignItems="center" mb={2}>
          <WarningIcon color="warning" sx={{ mr: 1 }} />
          <Typography variant="h6" component="div">
            Failsafe AI Provider Not Set
          </Typography>
        </Box>
        <Typography variant="body2" color="text.secondary" mb={2}>
          {`You don't have a failsafe AI provider set. Configure one to ensure
          continuous AI service.`}
        </Typography>
        <Button
          variant="contained"
          color="primary"
          onClick={handleOpen}
          fullWidth
        >
          Configure AI Provider
        </Button>
      </CardContent>
    </Card>
  );
};

const FailsafeAICard: React.FC<FailsafeAICardProps> = ({ onChange }) => {
  const failsafeAiProvider = useSelector(
    (state: RootState) => state.agents.currentAgent.failsafeAiProvider
  );
  const aiAccountsList =
    useSelector((state: RootState) => state.ai.aiAccountsList) || [];
  const { groupedModels: aiProviderList = [] } = useAiProviderGrouping();
  const [currentFailsafeConfig, setCurrentFailsafeConfig] =
    useState(failsafeAiProvider);
  const [showAccountDropdown, setShowAccountDropdown] = useState(false);

  useEffect(() => {
    const isCapriHosted = aiProviderList.find(
      (item) => item.companyId === failsafeAiProvider.companyId
    )?.isCapriHosted;
    if (!isCapriHosted) {
      setShowAccountDropdown(true);
    } else {
      setShowAccountDropdown(false);
    }
  }, [failsafeAiProvider, aiProviderList]);

  const groupedModels = useMemo(() => {
    const capriHostedModels: GroupedModel[] = [];
    const otherModels: GroupedModel[] = [];

    aiProviderList?.forEach((company) => {
      const companyModels = company.models.map((model) => ({
        ...model,
        companyId: company.companyId,
        companyName: company.name,
        accountName: model.modelName,
        accountId: model.id,
        isCapriHosted: company.isCapriHosted,
      }));

      if (company.isCapriHosted) {
        capriHostedModels.push(...companyModels);
      } else {
        otherModels.push(...companyModels);
      }
    });

    return [...capriHostedModels, ...otherModels];
  }, [aiProviderList]);

  const selectedModel = useMemo(
    () =>
      groupedModels.find(
        (model) =>
          model.companyId === currentFailsafeConfig?.companyId &&
          model.modelName === currentFailsafeConfig?.modelName
      ),
    [groupedModels, currentFailsafeConfig]
  );
  const getAvailableAccounts = (companyId: AiProviderCompany) => {
    return (
      aiAccountsList.find((company) => company.companyId === companyId)
        ?.accounts || []
    );
  };

  const selectedAccount = useMemo(
    () =>
      getAvailableAccounts(currentFailsafeConfig?.companyId || "").find(
        (account) => account.accountId === currentFailsafeConfig?.accountId
      ),
    [aiAccountsList, currentFailsafeConfig]
  );

  const handleDisableFailsafe = () => {
    setCurrentFailsafeConfig((prevState) => ({
      ...prevState,
      failsafe: false,
    }));
  };

  const handleModelChange = (newValue: GroupedModel | null) => {
    if (newValue) {
      if (newValue.isCapriHosted) {
        setCurrentFailsafeConfig((prevState) => ({
          ...prevState,
          companyId: newValue.companyId,
          modelName: newValue.modelName,
          accountId: newValue.accountId,
          accountName: newValue.accountName,
        }));
        setShowAccountDropdown(false);
      } else {
        setCurrentFailsafeConfig((prevState) => ({
          ...prevState,
          companyId: newValue.companyId,
          modelName: newValue.modelName,
          accountId: "",
          accountName: "",
        }));
        setShowAccountDropdown(true);
      }
    }
  };

  const handleAccountChange = (newValue: any) => {
    if (newValue) {
      setCurrentFailsafeConfig((prevState) => ({
        ...prevState,
        accountId: newValue.accountId,
        accountName: newValue.name,
      }));
    }
  };

  useEffect(() => {
    onChange(currentFailsafeConfig);
  }, [currentFailsafeConfig]);

  useEffect(() => {
    console.log({ aiProviderList, groupedModels });
  }, [aiProviderList, groupedModels]);

  if (currentFailsafeConfig.failsafe) {
    return (
      <>
        <Stack direction={"row"}>
          <Stack direction={"column"} gap={2} flexGrow={1}>
            <Typography variant="caption">Failsafe AI provider</Typography>

            <FormControl fullWidth>
              <Autocomplete
                value={selectedModel || null}
                options={groupedModels}
                groupBy={(option) =>
                  CAPRI_HOSTED_COMPANY_IDS.includes(option.companyId)
                    ? "Capri AI hosted models"
                    : option.companyName
                }
                getOptionLabel={(option) => option.name}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label="Model"
                    placeholder="Select a model"
                  />
                )}
                renderGroup={(params) => (
                  <Box key={params.key}>
                    <Typography
                      variant="subtitle2"
                      sx={{
                        px: 2,
                        py: 1,
                        backgroundColor: "grey.100",
                        fontWeight: "bold",
                      }}
                    >
                      {params.group}
                    </Typography>
                    {params.children}
                  </Box>
                )}
                onChange={(_, newValue) => handleModelChange(newValue)}
                isOptionEqualToValue={(option, value) =>
                  option.companyId === value?.companyId &&
                  option.modelName === value?.modelName
                }
              />
            </FormControl>

            {!selectedModel?.isCapriHosted && showAccountDropdown && (
              <FormControl fullWidth>
                <Autocomplete
                  value={selectedAccount || null}
                  options={getAvailableAccounts(
                    currentFailsafeConfig?.companyId || ""
                  )}
                  getOptionLabel={(option) => option.name}
                  renderInput={(params) => (
                    <TextField {...params} label="Account" />
                  )}
                  onChange={(_, newValue) => handleAccountChange(newValue)}
                />
              </FormControl>
            )}

            <Alert severity="info">
              {
                "The failsafe AI provider will be triggered only when your primary AI provider fails to respond. For the best experience we recommend choosing a different provider then your primary."
              }
            </Alert>
          </Stack>
          <Box mt={5}>
            <RemoveFailsafeAIProviderDialog onConfirm={handleDisableFailsafe} />
          </Box>
        </Stack>
      </>
    );
  }

  if (!currentFailsafeConfig || !currentFailsafeConfig.failsafe) {
    return (
      <FailSafeNotSetup
        handleOpen={() => {
          setCurrentFailsafeConfig((prevState) => ({
            ...prevState,
            failsafe: true,
          }));
        }}
      />
    );
  }

  return <></>;
};

export default FailsafeAICard;
