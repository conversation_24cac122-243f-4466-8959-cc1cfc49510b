import React, { useEffect, useMemo, useState } from "react";
import {
  Autocomplete,
  Box,
  FormControl,
  FormControlLabel,
  Switch,
  Slider,
  TextField,
  Typography,
  Paper,
  Stack,
} from "@mui/material";
import {
  IAiProvider,
  OpenAiAdvancedSettings,
  TokenOptimize,
} from "@/slices/agents/agentsSlice";
import {
  AiProviderCompany,
  CAPRI_HOSTED_COMPANY_IDS,
  singleAiAccountState,
  singleAiCompanyState,
  singleAiModelState,
} from "@/slices/settings/ai-crm/aiList";
import { hasProviderProperty } from "@/helpers/basic";
import { CONSTANTS } from "@/helpers/constants";

// Types
interface GroupedModel extends singleAiModelState {
  companyId: string;
  companyName: string;
  accountId: string;
  accountName: string;
  isCapriHosted?: boolean;
}

// Constants
const TEMPERATURE_MIN = 0.1;
const TEMPERATURE_MAX = 1;
const MAX_LENGTH_MIN = 1;
const MAX_LENGTH_MAX = 4000;
const OPTIMIZE_OPTIONS: TokenOptimize[] = ["cost", "accuracy"];

const DEFAULT_ADVANCED_SETTINGS: Record<string, any> = {
  [CONSTANTS.PROVIDERS.OPENROUTER]: {
    temperature: 0.3,
    maxLength: 180,
    frequencyPenalty: 0,
    optimize: "accuracy" as TokenOptimize,
  },
  "openai-hosted": {
    temperature: 0.3,
    maxLength: 180,
    frequencyPenalty: 0,
    optimize: "accuracy" as TokenOptimize,
  },
  fireworks: {
    temperature: 0.3,
    maxLength: 180,
    frequencyPenalty: 0,
    optimize: "accuracy" as TokenOptimize,
  },
  openai: {
    temperature: 0.3,
    maxLength: 180,
    frequencyPenalty: 0,
    optimize: "accuracy" as TokenOptimize,
  },
  claude: {
    temperature: 0.3,
    maxLength: 180,
    optimize: "accuracy" as TokenOptimize,
  },
  groq: {
    temperature: 0.3,
    maxLength: 180,
    optimize: "accuracy" as TokenOptimize,
  },
  gemini: {
    temperature: 0.3,
    maxLength: 180,
    optimize: "accuracy" as TokenOptimize,
  },
};

// const SettingsSection = styled(Box)(({ theme }) => ({
//   marginBottom: theme.spacing(3),
// }));

interface AiSettingsFormProps {
  aiProviderList: singleAiCompanyState[];
  aiAccountsList: singleAiAccountState[];
  initialValues?: IAiProvider;
  onChange?: (values: IAiProvider) => void;
}

const AiSettingsForm: React.FC<AiSettingsFormProps> = ({
  aiProviderList = [],
  aiAccountsList,
  initialValues,
  onChange,
}) => {
  const [provider, setProvider] = useState<IAiProvider>({
    companyId: "",
    modelName: "",
    accountId: "",
    accountName: "",
    isAdvancedSettings: false,
    advancedSettings: undefined,
    ...initialValues,
  });

  const groupedModels = useMemo(() => {
    const capriHostedModels: GroupedModel[] = [];
    const otherModels: GroupedModel[] = [];

    aiProviderList?.forEach((company) => {
      const companyModels = company.models.map((model) => ({
        ...model,
        companyId: company.companyId,
        companyName: company.name,
        accountName: model.modelName,
        accountId: model.id,
        isCapriHosted: company.isCapriHosted,
      }));

      if (company.isCapriHosted) {
        capriHostedModels.push(...companyModels);
      } else {
        otherModels.push(...companyModels);
      }
    });

    return [...capriHostedModels, ...otherModels];
  }, [aiProviderList]);

  const selectedModel = useMemo(
    () =>
      groupedModels.find(
        (model) =>
          model.companyId === provider.companyId &&
          model.modelName === provider.modelName
      ),
    [groupedModels, provider.companyId, provider.modelName]
  );

  useEffect(() => {
    console.log({ selectedModel });
  }, [selectedModel]);

  const handleProviderChange = (updates: Partial<IAiProvider>) => {
    const newState = { ...provider, ...updates };

    if (updates.companyId && updates.companyId !== provider.companyId) {
      newState.advancedSettings = DEFAULT_ADVANCED_SETTINGS[updates.companyId];
      // Only reset account info if not provided in updates
      if (!updates.accountId) {
        newState.accountId = "";
        newState.accountName = "";
      }
    }

    setProvider(newState);
    onChange?.(newState);
  };

  const getAvailableAccounts = (companyId: AiProviderCompany) => {
    return (
      aiAccountsList.find((company) => company.companyId === companyId)
        ?.accounts || []
    );
  };

  const renderAdvancedSettings = () => {
    if (!provider.isAdvancedSettings || !provider.advancedSettings) return null;

    return (
      <Box sx={{ mt: 2 }}>
        {hasProviderProperty(
          DEFAULT_ADVANCED_SETTINGS,
          provider.companyId,
          "temperature"
        ) && (
          <>
            <Typography gutterBottom>Temperature</Typography>
            <Slider
              value={provider.advancedSettings.temperature}
              min={TEMPERATURE_MIN}
              max={TEMPERATURE_MAX}
              step={0.1}
              onChange={(_, value) =>
                handleProviderChange({
                  //@ts-ignore
                  advancedSettings: {
                    ...provider.advancedSettings,
                    temperature: value as number,
                  },
                })
              }
              valueLabelDisplay="auto"
            />
          </>
        )}

        {hasProviderProperty(
          DEFAULT_ADVANCED_SETTINGS,
          provider.companyId,
          "maxLength"
        ) && (
          <>
            <Typography gutterBottom sx={{ mt: 2 }}>
              Maximum Length
            </Typography>
            <Slider
              value={provider.advancedSettings.maxLength}
              min={MAX_LENGTH_MIN}
              max={MAX_LENGTH_MAX}
              onChange={(_, value) =>
                handleProviderChange({
                  //@ts-ignore
                  advancedSettings: {
                    ...provider.advancedSettings,
                    maxLength: value as number,
                  },
                })
              }
              valueLabelDisplay="auto"
            />
          </>
        )}

        {hasProviderProperty(
          DEFAULT_ADVANCED_SETTINGS,
          provider.companyId,
          "frequencyPenalty"
        ) && (
          <>
            <Typography gutterBottom sx={{ mt: 2 }}>
              Frequency Penalty
            </Typography>
            <Slider
              value={
                (provider.advancedSettings as OpenAiAdvancedSettings)
                  .frequencyPenalty
              }
              min={0}
              max={2}
              step={0.1}
              onChange={(_, value) =>
                handleProviderChange({
                  advancedSettings: {
                    ...provider.advancedSettings,
                    frequencyPenalty: value as number,
                  } as OpenAiAdvancedSettings,
                })
              }
              valueLabelDisplay="auto"
            />
          </>
        )}

        {hasProviderProperty(
          DEFAULT_ADVANCED_SETTINGS,
          provider.companyId,
          "optimize"
        ) && (
          <FormControl fullWidth sx={{ mt: 2 }}>
            <Autocomplete
              value={provider.advancedSettings.optimize}
              options={OPTIMIZE_OPTIONS}
              renderInput={(params) => (
                <TextField {...params} label="Optimize" />
              )}
              onChange={(_, newValue) => {
                if (newValue) {
                  handleProviderChange({
                    //@ts-ignore
                    advancedSettings: {
                      ...provider.advancedSettings,
                      optimize: newValue,
                    },
                  });
                }
              }}
            />
          </FormControl>
        )}
      </Box>
    );
  };

  return (
    <>
      <Stack direction={"row"}>
        <Stack direction={"column"} gap={2} flexGrow={1}>
          <Typography variant="caption">Primary AI provider</Typography>
          <Box>
            <FormControl fullWidth>
              <Autocomplete
                value={selectedModel || null}
                options={groupedModels}
                groupBy={(option) =>
                  CAPRI_HOSTED_COMPANY_IDS.includes(option.companyId)
                    ? "Capri AI hosted models"
                    : option.companyName
                }
                getOptionLabel={(option) => option.name}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label="Model"
                    placeholder="Select a model"
                  />
                )}
                renderGroup={(params) => (
                  <Box key={params.key}>
                    <Typography
                      variant="subtitle2"
                      sx={{
                        px: 2,
                        py: 1,
                        backgroundColor: "grey.100",
                        fontWeight: "bold",
                      }}
                    >
                      {params.group}
                    </Typography>
                    {params.children}
                  </Box>
                )}
                onChange={(_, newValue) => {
                  if (newValue) {
                    if (newValue.isCapriHosted) {
                      handleProviderChange({
                        companyId: newValue.companyId,
                        modelName: newValue.modelName,
                        accountId: newValue.accountId,
                        accountName: newValue.accountName,
                      });
                    } else {
                      handleProviderChange({
                        companyId: newValue.companyId,
                        modelName: newValue.modelName,
                        accountId: "",
                        accountName: "",
                      });
                    }
                  }
                }}
                isOptionEqualToValue={(option, value) =>
                  option.companyId === value?.companyId &&
                  option.modelName === value?.modelName
                }
              />
            </FormControl>
          </Box>

          {!selectedModel?.isCapriHosted && (
            <Box>
              <FormControl fullWidth>
                <Autocomplete
                  value={
                    getAvailableAccounts(provider.companyId).find(
                      (account) => account.accountId === provider.accountId
                    ) || null
                  }
                  options={getAvailableAccounts(provider.companyId)}
                  getOptionLabel={(option) => option.name}
                  renderInput={(params) => (
                    <TextField {...params} label="Account" />
                  )}
                  onChange={(_, newValue) => {
                    if (newValue) {
                      handleProviderChange({
                        accountId: newValue.accountId,
                        accountName: newValue.name,
                      });
                    }
                  }}
                />
              </FormControl>
            </Box>
          )}

          <Box>
            <FormControlLabel
              control={
                <Switch
                  checked={provider.isAdvancedSettings}
                  onChange={(e) => {
                    handleProviderChange({
                      isAdvancedSettings: e.target.checked,
                      //   advancedSettings: e.target.checked
                      //     ? DEFAULT_ADVANCED_SETTINGS[provider.companyId]
                      //     : undefined,
                    });
                  }}
                />
              }
              label="Advanced settings"
            />

            {renderAdvancedSettings()}
          </Box>
        </Stack>
      </Stack>
    </>
  );
};

export default AiSettingsForm;
