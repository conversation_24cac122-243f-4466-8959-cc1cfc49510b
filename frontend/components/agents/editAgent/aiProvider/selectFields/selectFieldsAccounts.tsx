import { CONSTANTS } from "@/helpers/constants";
import { useCurrentPlan } from "@/hooks/useCurrentPlan";
import { getAiProviders } from "@/requests/aiProvider/getAiProviders";
import {
  ISingleAiAccountState,
  aiActions,
} from "@/slices/settings/ai-crm/aiList";
import { RootState } from "@/store";
import { Autocomplete, TextField } from "@mui/material";
import React, { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";

interface IProp {
  initialAccountId?: string;
  modelName?: string;
  provider: string;
  onChangeFn: (value: { accountId: string; accountName: string }) => void;
}

const SelectFieldsAccounts: React.FC<IProp> = ({
  initialAccountId,
  modelName,
  provider,
  onChangeFn,
}) => {
  const dispatch = useDispatch();
  const { disableExternalAiProviders } = useCurrentPlan();
  const aiAccountsList = useSelector(
    (state: RootState) => state.ai.aiAccountsList
  );
  const aiProviderList = useSelector(
    (state: RootState) => state.ai.aiProviderList
  );
  const currentAgent = useSelector(
    (state: RootState) => state.agents.currentAgent
  );
  const [account, setAccount] = React.useState<ISingleAiAccountState | null>(
    null
  );

  const HandleAccountChange = (event: any, value: any) => {
    if (value) {
      setAccount(value);
      onChangeFn({
        accountId: value.accountId,
        accountName: value.name,
      });
    } else {
      setAccount(null);
      onChangeFn({
        accountId: "",
        accountName: "",
      });
    }
  };

  useEffect(() => {
    if (
      provider === CONSTANTS.PROVIDERS.FIREWORKS ||
      provider === CONSTANTS.PROVIDERS.CAPRI_HOSTED_OPENAI
    ) {
      const accountId =
        aiProviderList
          .find((aiProvider) => aiProvider.companyId === provider)
          ?.models.find((model) => model.modelName === modelName)?.id || "";
      const accountName =
        aiProviderList.find((aiProvider) => aiProvider.companyId === provider)
          ?.name || "";
      onChangeFn({
        accountId,
        accountName: "Capri Hosted LLM",
      });
      return;
    }
    const currentAccount = aiAccountsList
      .find((item) => item.companyId === provider)
      ?.accounts.find((account) => account.accountId === initialAccountId);
    setAccount(currentAccount ?? null);
  }, [currentAgent, aiAccountsList, provider]);

  useEffect(() => {
    const response = getAiProviders(
      {
        orgId: localStorage.getItem("orgId") || "",
      },
      dispatch
    );
    response.then((res) => {
      dispatch(aiActions.aiAccountsListAdded(res?.data?.aiProviderAccounts));
    });
  }, []);

  const options =
    aiAccountsList.find((item) => item.companyId === provider)?.accounts || [];

  if (
    provider === CONSTANTS.PROVIDERS.FIREWORKS ||
    provider === CONSTANTS.PROVIDERS.CAPRI_HOSTED_OPENAI ||
    disableExternalAiProviders
  )
    return null;

  return (
    <Autocomplete
      options={options}
      getOptionLabel={(option) => option.name}
      value={account}
      onChange={HandleAccountChange}
      renderInput={(params) => <TextField {...params} label="Account" />}
    />
  );
};

export default SelectFieldsAccounts;
