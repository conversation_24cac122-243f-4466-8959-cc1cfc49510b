import { CONSTANTS } from "@/helpers/constants";
import { useCurrentPlan } from "@/hooks/useCurrentPlan";
import {
  singleAiCompanyState,
  singleAiModelState,
} from "@/slices/settings/ai-crm/aiList";
import { RootState } from "@/store";
import { Autocomplete, TextField } from "@mui/material";
import React, { useEffect, useState } from "react";
import { useSelector } from "react-redux";

interface IProp {
  initialModelName?: string;
  onChangeFn: (value: { companyId: string; modelName: string }) => void;
  resetAccount?: () => void;
}

interface OptionType extends singleAiModelState {
  companyId: string;
  groupName: string;
}

const SelectFieldsModel: React.FC<IProp> = ({
  initialModelName,
  onChangeFn,
  resetAccount,
}) => {
  const { disableExternalAiProviders } = useCurrentPlan();
  const aiProviderList = useSelector(
    (state: RootState) => state.ai.aiProviderList
  );
  const currentAgent = useSelector(
    (state: RootState) => state.agents.currentAgent
  );
  const [provider, setProvider] = React.useState<OptionType | null>(null);
  const [options, setOptions] = React.useState<OptionType[]>([]);

  useEffect(() => {
    const newOptions = aiProviderList.reduce<OptionType[]>((acc, item) => {
      const models = item.models.map((model) => ({
        ...model,
        companyId: item.companyId,
        groupName: item.name,
      }));
      return [...acc, ...models];
    }, []);
    setOptions(newOptions);
    console.log({ initialModelName });

    const initialProvider = newOptions.find(
      (option) => option.modelName === initialModelName
    );
    setProvider(initialProvider || null);

    // onChangeFn({
    //   companyId: currentAgent?.aiProvider?.companyId || "",
    //   modelName: currentAgent?.aiProvider?.modelName || "",
    // });
  }, [aiProviderList, currentAgent]);

  const handleChange = (event: any, value: OptionType | null) => {
    if (value) {
      const oldCompanyId = provider?.companyId;
      const newCompanyId = value?.companyId;
      setProvider(value);
      onChangeFn({ companyId: value.companyId, modelName: value.modelName });
      if (oldCompanyId !== newCompanyId) {
        //reset the selected account.
        console.log("Resetting old values");
        resetAccount?.();
      }
    } else {
      setProvider(null);
      onChangeFn({ companyId: "", modelName: "" });
    }
  };

  return (
    <Autocomplete
      options={options
        .filter((item) => {
          if (disableExternalAiProviders) {
            return item.companyId === CONSTANTS.PROVIDERS.FIREWORKS;
          }
          return true;
        })
        .sort((a, b) => -b.groupName.localeCompare(a.groupName))}
      groupBy={(option) => option.groupName}
      getOptionLabel={(option) => option.modelName}
      value={provider}
      onChange={handleChange}
      renderInput={(params) => <TextField {...params} label="Model" />}
    />
  );
};

export default SelectFieldsModel;
