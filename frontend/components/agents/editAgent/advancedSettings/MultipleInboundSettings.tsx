import LoomDialog from "@/components/general/LoomDialog";
import { handleMultipleInbound } from "@/requests/agents/advancedSettings/handleMultipleInbound";
import { agentsAction } from "@/slices/agents/agentsSlice";
import { RootState } from "@/store";
import {
  <PERSON><PERSON>,
  Chip,
  FormControlLabel,
  InputAdornment,
  Link,
  Stack,
  Switch,
  TextField,
  Typography,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import { AiFillEdit } from "react-icons/ai";
import { ImSpinner8 } from "react-icons/im";
import { useDispatch, useSelector } from "react-redux";

export interface MultipleInboundSettingsProps {
  multipleInbound: boolean;
  initialWait: number;
  maxWait: number;
  incrementBy: number;
}

const MultipleInboundSettings = () => {
  const dispatch = useDispatch();
  const currentAgent = useSelector(
    (state: RootState) => state.agents.currentAgent
  );
  const [loading, setLoading] = useState(false);
  const multipleInbound = currentAgent?.multipleInbound;
  const multipleInboundConfig = currentAgent?.multipleInboundConfig;
  const [settings, setSettings] = useState<MultipleInboundSettingsProps | null>(
    null
  );
  const [edit, setEdit] = useState(false);
  useEffect(() => {
    if (multipleInboundConfig === undefined) return;
    setSettings({
      multipleInbound,
      initialWait: multipleInboundConfig.initialWait,
      maxWait: multipleInboundConfig.maxWait,
      incrementBy: multipleInboundConfig.incrementBy,
    });
  }, [multipleInbound, multipleInboundConfig, edit]);

  const toggleEdit = () => {
    setEdit(!edit);
  };
  const handleSave = async () => {
    if (settings === null) return;
    setLoading(true);
    const response = await handleMultipleInbound(
      {
        agentId: currentAgent?._id,
        body: settings,
      },
      dispatch
    );
    setLoading(false);
    if (response.status === 200) {
      dispatch(agentsAction.setMultipleInboundChange(settings.multipleInbound));
      dispatch(agentsAction.setMultipleInboundConfigChange(settings));
      toggleEdit();
    }
  };
  if (settings === null) return null;
  return (
    <>
      <div className="flex items-center justify-between  text-[18px] py-3 px-4">
        <div className="w-2/12 font-semibold line-clamp-2">
          Multiple inbound
        </div>
        {!edit && (
          <>
            <div className="w-8/12 flex items-center gap-2">
              {multipleInbound ? (
                <Chip label="Enabled" color="success" />
              ) : (
                <Chip label="Disabled" color="default" />
              )}
            </div>
            <div className="w-2/12 flex justify-end">
              <Button
                variant="outlined"
                startIcon={<AiFillEdit />}
                onClick={toggleEdit}
              >
                Edit
              </Button>
            </div>
          </>
        )}
      </div>
      {edit && (
        <>
          <div className="px-4 pb-3">
            <FormControlLabel
              control={
                <Switch
                  checked={settings.multipleInbound}
                  onChange={() => {
                    setSettings({
                      ...settings,
                      multipleInbound: !settings.multipleInbound,
                    });
                  }}
                />
              }
              label="Multiple Inbound"
            />
            <Typography variant="body2">
              Enable this feature to combine multiple inbound messages received
              within a short time span into a single request, streamlining
              responses and reducing redundancy. If disabled, each inbound
              message will be processed and responded to individually. For
              reference, check{" "}
              <LoomDialog
                triggerComponent={<Link>here</Link>}
                title="Multiple inbound feature"
                videoUrl="https://www.loom.com/share/0979f178167b487586d01720dfacf22a?sid=7f2c1535-19d2-441f-996f-600d211728b7"
              />
            </Typography>
            {settings.multipleInbound && (
              <Stack direction={"column"} gap={2} mt={2}>
                <TextField
                  label="Initial Wait"
                  type="number"
                  inputProps={{ min: 0 }}
                  value={settings?.initialWait}
                  fullWidth
                  onChange={(e) => {
                    setSettings({
                      ...settings,
                      initialWait: Number(e.target.value),
                    });
                  }}
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">seconds</InputAdornment>
                    ),
                  }}
                />
                <TextField
                  label="Max Wait"
                  type="number"
                  inputProps={{ min: 0 }}
                  value={settings?.maxWait}
                  fullWidth
                  onChange={(e) => {
                    setSettings({
                      ...settings,
                      maxWait: Number(e.target.value),
                    });
                  }}
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">seconds</InputAdornment>
                    ),
                  }}
                />
                <TextField
                  label="Increment By"
                  type="number"
                  inputProps={{ min: 0 }}
                  value={settings.incrementBy}
                  fullWidth
                  onChange={(e) => {
                    setSettings({
                      ...settings,
                      incrementBy: Number(e.target.value),
                    });
                  }}
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">%</InputAdornment>
                    ),
                  }}
                />
              </Stack>
            )}
          </div>
          <Stack direction={"row"} justifyContent={"end"} gap={2} p={2}>
            <Button variant="text" color="error" onClick={toggleEdit}>
              Cancel
            </Button>
            <Button variant="contained" disabled={loading} onClick={handleSave}>
              Save {loading && <ImSpinner8 className="animate-spin ml-2" />}
            </Button>
          </Stack>
        </>
      )}
    </>
  );
};

export default MultipleInboundSettings;
