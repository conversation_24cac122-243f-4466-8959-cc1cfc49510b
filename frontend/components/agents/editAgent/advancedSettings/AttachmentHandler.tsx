import React, { useEffect, useState, useCallback } from "react";
import AdvancedSettingsTemplate from "./AdvancedSettingsTemplate";
import {
  Chip,
  Stack,
  Switch,
  TextField,
  FormControlLabel,
  Box,
  Select,
  MenuItem,
  InputLabel,
  FormControl,
  SelectChangeEvent,
  Typography,
  Link,
} from "@mui/material";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store";
import { IAttachmentConfig, agentsAction } from "@/slices/agents/agentsSlice";
import { stringToBoolean } from "@/helpers/basic";
import { handleAttachments } from "@/requests/agents/advancedSettings/attachmentHandler";
import { useRouter } from "next/router";
import LoomDialog from "@/components/general/LoomDialog";

const defaultConfig: IAttachmentConfig = {
  withoutMessage: {
    enabled: false,
    tagToAdd: "",
    setSilent: false,
  },
  withMessage: {
    enabled: false,
    tagToAdd: "",
    setSilent: false,
  },
};

const AttachmentHandler: React.FC = React.memo(function AttachmentHandler() {
  const dispatch = useDispatch();
  const router = useRouter();
  const { agentId } = router.query as { agentId: string };
  const currentAgent = useSelector(
    (state: RootState) => state.agents.currentAgent
  );

  const [loading, setLoading] = useState(false);
  const isSavedState = useState(false);
  const [isSaved, setIsSaved] = isSavedState;
  const [attachmentHandler, setAttachmentHandler] = useState<IAttachmentConfig>(
    currentAgent.attachmentConfig || defaultConfig
  );

  const handleToggleChange = useCallback(
    (type: "withoutMessage" | "withMessage") =>
      (event: React.ChangeEvent<HTMLInputElement>) => {
        setAttachmentHandler((prev) => ({
          ...prev,
          [type]: {
            ...prev[type],
            enabled: event.target.checked,
          },
        }));
      },
    []
  );

  const handleTextFieldChange = useCallback(
    (type: "withoutMessage" | "withMessage") =>
      (event: React.ChangeEvent<HTMLInputElement>) => {
        setAttachmentHandler((prev) => ({
          ...prev,
          [type]: {
            ...prev[type],
            tagToAdd: event.target.value,
          },
        }));
      },
    []
  );

  const handleSelectChange = useCallback(
    (type: "withoutMessage" | "withMessage") =>
      (event: SelectChangeEvent<string>) => {
        setAttachmentHandler((prev) => ({
          ...prev,
          [type]: {
            ...prev[type],
            setSilent: stringToBoolean(event.target.value),
          },
        }));
      },
    []
  );

  const handleSave = useCallback(async () => {
    const payload = {
      agentId,
      attachmentConfig: attachmentHandler,
    };
    setLoading(true);
    const response = await handleAttachments(payload, dispatch);
    setLoading(false);
    setIsSaved(true);
    if (response.status === 200) {
      dispatch(agentsAction.setAttachmentConfigChanged(attachmentHandler));
    }
  }, [agentId, attachmentHandler, dispatch, setIsSaved]);

  const ChipComponent: React.FC<{ label: string; enabled: boolean }> =
    React.memo(function ChipComponent({ label, enabled }) {
      return (
        <Chip
          label={label}
          color={enabled ? "success" : "default"}
          variant="filled"
        />
      );
    });

  const ToggleWithInputs: React.FC<{
    type: "withoutMessage" | "withMessage";
    label: string;
    helperText: string;
    dialogTitle: string;
    videoUrl: string;
  }> = React.memo(function ToggleWithInputs({
    type,
    label,
    helperText,
    dialogTitle,
    videoUrl,
  }) {
    const [localTagToAdd, setLocalTagToAdd] = useState(
      attachmentHandler[type].tagToAdd
    );

    const handleLocalTextFieldChange = (
      event: React.ChangeEvent<HTMLInputElement>
    ) => {
      setLocalTagToAdd(event.target.value);
    };

    const handleBlur = () => {
      handleTextFieldChange(type)({
        target: { value: localTagToAdd },
      } as React.ChangeEvent<HTMLInputElement>);
    };
    return (
      <Box sx={{ mb: 2 }}>
        <Stack direction={"column"}>
          <FormControlLabel
            control={
              <Switch
                checked={attachmentHandler[type].enabled}
                onChange={handleToggleChange(type)}
              />
            }
            label={label}
          />
          <Typography variant="caption">
            {helperText}. For more reference, check{" "}
            <LoomDialog
              triggerComponent={<Link>here</Link>}
              title={dialogTitle}
              videoUrl={videoUrl}
            />
          </Typography>
        </Stack>
        {attachmentHandler[type].enabled && (
          <Box sx={{ ml: 4, mt: 1, display: "flex", gap: 2 }}>
            <TextField
              label="Tag to Add"
              value={localTagToAdd}
              onChange={handleLocalTextFieldChange}
              onBlur={handleBlur}
              fullWidth
              margin="dense"
            />
            <FormControl fullWidth margin="dense">
              <InputLabel id={`${type}-set-silent-label`}>
                Set Silent
              </InputLabel>
              <Select
                labelId={`${type}-set-silent-label`}
                value={String(attachmentHandler[type].setSilent)}
                onChange={handleSelectChange(type)}
                label="Set Silent"
              >
                <MenuItem value="true">True</MenuItem>
                <MenuItem value="false">False</MenuItem>
              </Select>
            </FormControl>
          </Box>
        )}
      </Box>
    );
  });

  const valueComponent = (
    <Stack direction="row" spacing={2}>
      <ChipComponent
        label="Without Message"
        enabled={attachmentHandler.withoutMessage.enabled}
      />
      <ChipComponent
        label="With Message"
        enabled={attachmentHandler.withMessage.enabled}
      />
    </Stack>
  );

  const innerComponent = (
    <Box>
      <ToggleWithInputs
        type="withoutMessage"
        label="Without Message"
        helperText="This is the action we will take if we receive a message with both text and a file attached such as an image or document"
        dialogTitle="Without Message feature"
        videoUrl="https://www.loom.com/share/3e5dfcc8658b49a38d58788b424fbc54?sid=774c1de3-f194-4056-9abf-06c269ea8970"
      />
      <ToggleWithInputs
        type="withMessage"
        label="With Message"
        helperText="This is the action we will take if we receive a message with a file attached and no text"
        dialogTitle="With Message feature"
        videoUrl="https://www.loom.com/share/3e5dfcc8658b49a38d58788b424fbc54?t=112&sid=592557e8-33d1-4f5c-97e3-f829170f8f57"
      />
    </Box>
  );

  if (!attachmentHandler) return null;
  return (
    <AdvancedSettingsTemplate
      title="Attachment Handler"
      valueComponent={valueComponent}
      innerComponent={innerComponent}
      loading={loading}
      onSave={handleSave}
      isSavedState={isSavedState}
    />
  );
});

export default AttachmentHandler;
