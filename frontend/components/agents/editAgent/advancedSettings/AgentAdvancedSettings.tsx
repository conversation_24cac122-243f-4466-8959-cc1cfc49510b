import Line from "@/components/general/shapes/horizontalLine";
import SubHeading from "@/components/settings/crm_ai/subHeading";
import { Button } from "@mui/material";
import React, { useState } from "react";
import { AiFillEdit } from "react-icons/ai";
import MultipleInboundSettings from "./MultipleInboundSettings";
import UserManualResponds from "./UserManualResponds";
import AttachmentHandler from "./AttachmentHandler";
import EmptyMessageHandler from "./EmptyMessageHandler";
import AdvancedFallbackHandler from "./AdvancedFallbackHandler";
import ContextLengthInAugmentedQueryHandler from "./ContextLengthInAugmentedQueryHandler";
import UnreadHandler from "./UnreadHandler";

const AgentAdvancedSettings = () => {
  const [edit, setEdit] = useState(false);
  const toggleEdit = () => {
    setEdit(!edit);
  };
  return (
    <div className="border-[1px] border-x-slate-300 rounded-md bg-fourth my-5">
      <SubHeading value="Advanced Settings" />
      <Line />
      <MultipleInboundSettings />
      <Line />
      <UserManualResponds />
      <Line />
      <AttachmentHandler />
      <Line />
      <EmptyMessageHandler />
      <Line />
      <AdvancedFallbackHandler />
      <Line />
      <ContextLengthInAugmentedQueryHandler />
      <Line />
      <UnreadHandler />
      {/* {edit && <AiProviderInner editFn={toggleEdit} />} */}
    </div>
  );
};

export default AgentAdvancedSettings;
