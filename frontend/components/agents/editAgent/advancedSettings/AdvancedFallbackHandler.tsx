import React, { useEffect, useState, useCallback } from "react";
import AdvancedSettingsTemplate from "./AdvancedSettingsTemplate";
import {
  Chip,
  Stack,
  Switch,
  TextField,
  FormControlLabel,
  Box,
  Select,
  MenuItem,
  InputLabel,
  FormControl,
  SelectChangeEvent,
  Typography,
  Link,
} from "@mui/material";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store";
import { IEmptyMessageConfig, agentsAction } from "@/slices/agents/agentsSlice";
import { stringToBoolean } from "@/helpers/basic";
import { useRouter } from "next/router";
import { handleEmptyMessageConfig } from "@/requests/agents/advancedSettings/handleEmptyMessageConfig";
import { handleAdvancedFallbackConfig } from "@/requests/agents/advancedSettings/handleAdvancedFallbackConfig";
import LoomDialog from "@/components/general/LoomDialog";

const defaultConfig: IEmptyMessageConfig = {
  enabled: false,
  tagToAdd: "",
  setSilent: false,
};

const AdvancedFallbackHandler: React.FC = React.memo(
  function EmptyMessageHandler() {
    const dispatch = useDispatch();
    const router = useRouter();
    const { agentId } = router.query as { agentId: string };
    const currentAgent = useSelector(
      (state: RootState) => state.agents.currentAgent
    );

    const [loading, setLoading] = useState(false);
    const isSavedState = useState(false);
    const [isSaved, setIsSaved] = isSavedState;
    const [fallbackConfig, setFallbackConfig] = useState<IEmptyMessageConfig>(
      currentAgent.fallbackConfig || defaultConfig
    );

    const handleToggleChange = useCallback(
      (event: React.ChangeEvent<HTMLInputElement>) => {
        setFallbackConfig((prev) => ({
          ...prev,
          enabled: event.target.checked,
        }));
      },
      []
    );

    const handleTextFieldChange = useCallback(
      (event: React.ChangeEvent<HTMLInputElement>) => {
        setFallbackConfig((prev) => ({
          ...prev,
          tagToAdd: event.target.value,
        }));
      },
      []
    );

    const handleSelectChange = useCallback(
      (event: SelectChangeEvent<string>) => {
        setFallbackConfig((prev) => ({
          ...prev,
          setSilent: stringToBoolean(event.target.value),
        }));
      },
      []
    );

    const handleSave = useCallback(async () => {
      const payload = {
        agentId,
        fallbackConfig: fallbackConfig,
      };
      setLoading(true);
      const response = await handleAdvancedFallbackConfig(payload, dispatch);
      setLoading(false);
      setIsSaved(true);
      if (response.status === 200) {
        dispatch(agentsAction.setEmptyMessageConfigChanged(fallbackConfig));
      }
    }, [agentId, fallbackConfig, dispatch, setIsSaved]);

    const ChipComponent: React.FC<{ label: string; enabled: boolean }> =
      React.memo(function ChipComponent({ label, enabled }) {
        return (
          <Chip
            label={label}
            color={enabled ? "success" : "default"}
            variant="filled"
          />
        );
      });

    const ToggleWithInputs: React.FC = React.memo(function ToggleWithInputs() {
      const [localTagToAdd, setLocalTagToAdd] = useState(
        fallbackConfig.tagToAdd || ""
      );

      const handleLocalTextFieldChange = (
        event: React.ChangeEvent<HTMLInputElement>
      ) => {
        setLocalTagToAdd(event.target.value);
      };

      const handleBlur = () => {
        handleTextFieldChange({
          target: { value: localTagToAdd },
        } as React.ChangeEvent<HTMLInputElement>);
      };

      return (
        <Box sx={{ mb: 2 }}>
          <Stack direction="column">
            <FormControlLabel
              control={
                <Switch
                  checked={fallbackConfig.enabled}
                  onChange={handleToggleChange}
                />
              }
              label="Handle Custom Fallback"
            />
            <Typography variant="caption">
              These steps will be taken when a fallback is triggered by the
              agent.For reference, check{" "}
              <LoomDialog
                triggerComponent={<Link>here</Link>}
                title="Custom fallback feature"
                videoUrl="https://www.loom.com/share/f0173450c9544923b1bb1be0067e290b?sid=a40e095c-36e1-4dfc-8062-2b9990f41d6f"
              />
            </Typography>
          </Stack>
          {fallbackConfig.enabled && (
            <Box sx={{ ml: 4, mt: 1, display: "flex", gap: 2 }}>
              <TextField
                label="Tag to Add"
                value={localTagToAdd}
                onChange={handleLocalTextFieldChange}
                onBlur={handleBlur}
                fullWidth
                margin="dense"
              />
              <FormControl fullWidth margin="dense">
                <InputLabel id="empty-message-set-silent-label">
                  Set Silent
                </InputLabel>
                <Select
                  labelId="empty-message-set-silent-label"
                  value={String(fallbackConfig.setSilent)}
                  onChange={handleSelectChange}
                  label="Set Silent"
                >
                  <MenuItem value="true">True</MenuItem>
                  <MenuItem value="false">False</MenuItem>
                </Select>
              </FormControl>
            </Box>
          )}
        </Box>
      );
    });

    const valueComponent = (
      <ChipComponent label="Enabled" enabled={fallbackConfig.enabled} />
    );

    const innerComponent = <ToggleWithInputs />;

    if (!fallbackConfig) return null;

    return (
      <AdvancedSettingsTemplate
        title="Custom Fallback"
        valueComponent={valueComponent}
        innerComponent={innerComponent}
        loading={loading}
        onSave={handleSave}
        isSavedState={isSavedState}
      />
    );
  }
);

export default AdvancedFallbackHandler;
