import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store";
import {
  Button,
  Chip,
  FormControlLabel,
  Switch,
  Typography,
  Stack,
} from "@mui/material";
import { AiFillEdit } from "react-icons/ai";
import { ImSpinner8 } from "react-icons/im";
import { fetchKeepConversationsUnread } from "@/requests/agents/advancedSettings/keepConversationsUnread";
import { agentsAction } from "@/slices/agents/agentsSlice";

interface AgentWithMarkUnread {
  markUnreadAfterReply?: boolean;
  _id: string;
}

const UnreadHandler = () => {
  const dispatch = useDispatch();
  const currentAgent = useSelector(
    (state: RootState) => state.agents.currentAgent
  ) as AgentWithMarkUnread;
  const [loading, setLoading] = useState(false);
  const markUnreadAfterReply = currentAgent?.markUnreadAfterReply || false;
  const [isUnreadEnabled, setIsUnreadEnabled] = useState(markUnreadAfterReply);
  const [edit, setEdit] = useState(false);

  useEffect(() => {
    setIsUnreadEnabled(markUnreadAfterReply);
  }, [markUnreadAfterReply]);

  const toggleEdit = () => {
    setEdit(!edit);
    setIsUnreadEnabled(markUnreadAfterReply);
  };

  const handleSave = async () => {
    if (!currentAgent?._id) return;

    setLoading(true);
    const response = await fetchKeepConversationsUnread(
      {
        agentId: currentAgent._id,
        markUnreadAfterReply: isUnreadEnabled,
      },
      dispatch
    );
    setLoading(false);

    if (response.status === 202) {
      dispatch(agentsAction.setMarkUnreadAfterReply(isUnreadEnabled));
      toggleEdit();
    }
  };

  return (
    <>
      <div className="flex items-center justify-between text-[18px] py-3 px-4">
        <div className="w-2/12 font-semibold line-clamp-2">
          Keep conversations unread
        </div>
        {!edit && (
          <>
            <div className="w-8/12 flex items-center gap-2">
              {markUnreadAfterReply ? (
                <Chip label="Enabled" color="success" />
              ) : (
                <Chip label="Disabled" color="default" />
              )}
            </div>
            <div className="w-2/12 flex justify-end">
              <Button
                variant="outlined"
                startIcon={<AiFillEdit />}
                onClick={toggleEdit}
              >
                Edit
              </Button>
            </div>
          </>
        )}
      </div>
      {edit && (
        <>
          <div className="px-4 pb-3">
            <FormControlLabel
              control={
                <Switch
                  checked={isUnreadEnabled}
                  onChange={() => setIsUnreadEnabled(!isUnreadEnabled)}
                />
              }
              label="Enable"
            />
            <Typography variant="body2">
              Enabling this will keep your HighLevel conversations unread after
              the bot sends a reply.
            </Typography>
          </div>
          <Stack direction={"row"} justifyContent={"end"} gap={2} p={2}>
            <Button variant="text" color="error" onClick={toggleEdit}>
              Cancel
            </Button>
            <Button variant="contained" disabled={loading} onClick={handleSave}>
              Save {loading && <ImSpinner8 className="animate-spin ml-2" />}
            </Button>
          </Stack>
        </>
      )}
    </>
  );
};

export default UnreadHandler;
