import { But<PERSON>, <PERSON>ack } from "@mui/material";
import React, { Dispatch, SetStateAction, useEffect, useState } from "react";
import { AiFillEdit } from "react-icons/ai";
import { ImSpinner8 } from "react-icons/im";

interface IProp {
  title: string;
  valueComponent: React.ReactNode;
  innerComponent: React.ReactNode;
  loading: boolean;
  onSave: () => void;
  isSavedState: [boolean, Dispatch<SetStateAction<boolean>>];
}

const AdvancedSettingsTemplate: React.FC<IProp> = ({
  title,
  valueComponent,
  innerComponent,
  loading,
  onSave,
  isSavedState,
}) => {
  const [isSaved, setIsSaved] = isSavedState;
  const [edit, setEdit] = useState(false);
  const toggleEdit = () => {
    setEdit(!edit);
  };
  useEffect(() => {
    if (isSaved === true) {
      setEdit(false);
      setIsSaved(false);
    }
  }, [isSaved]);
  return (
    <>
      <div className="flex items-center justify-between  text-[18px] py-3 px-4">
        <div className="w-2/12 font-semibold line-clamp-2">{title}</div>
        {!edit && (
          <>
            <div className="w-8/12 flex items-center gap-2">
              {valueComponent}
            </div>
            <div className="w-2/12 flex justify-end">
              <Button
                variant="outlined"
                startIcon={<AiFillEdit />}
                onClick={toggleEdit}
              >
                Edit
              </Button>
            </div>
          </>
        )}
      </div>
      {edit && (
        <>
          <div className="px-4 pt-2 flex flex-col gap-3">{innerComponent}</div>
          <Stack direction={"row"} justifyContent={"end"} gap={2} p={2}>
            <Button variant="text" color="error" onClick={toggleEdit}>
              Cancel
            </Button>
            <Button variant="contained" disabled={loading} onClick={onSave}>
              Save {loading && <ImSpinner8 className="animate-spin ml-2" />}
            </Button>
          </Stack>
        </>
      )}
    </>
  );
};

export default AdvancedSettingsTemplate;
