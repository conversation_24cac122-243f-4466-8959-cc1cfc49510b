import React, { useEffect, useState, useCallback } from "react";
import AdvancedSettingsTemplate from "./AdvancedSettingsTemplate";
import {
  Chip,
  Stack,
  Switch,
  TextField,
  FormControlLabel,
  Box,
  Select,
  MenuItem,
  InputLabel,
  FormControl,
  SelectChangeEvent,
  Typography,
  Link,
} from "@mui/material";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store";
import { IEmptyMessageConfig, agentsAction } from "@/slices/agents/agentsSlice";
import { stringToBoolean } from "@/helpers/basic";
import { useRouter } from "next/router";
import { handleEmptyMessageConfig } from "@/requests/agents/advancedSettings/handleEmptyMessageConfig";
import LoomDialog from "@/components/general/LoomDialog";

const defaultConfig: IEmptyMessageConfig = {
  enabled: false,
  tagToAdd: "",
  setSilent: false,
};

const EmptyMessageHandler: React.FC = React.memo(
  function EmptyMessageHandler() {
    const dispatch = useDispatch();
    const router = useRouter();
    const { agentId } = router.query as { agentId: string };
    const currentAgent = useSelector(
      (state: RootState) => state.agents.currentAgent
    );

    const [loading, setLoading] = useState(false);
    const isSavedState = useState(false);
    const [isSaved, setIsSaved] = isSavedState;
    const [emptyMessageConfig, setEmptyMessageConfig] =
      useState<IEmptyMessageConfig>(
        currentAgent.emptyMessageConfig || defaultConfig
      );

    const handleToggleChange = useCallback(
      (event: React.ChangeEvent<HTMLInputElement>) => {
        setEmptyMessageConfig((prev) => ({
          ...prev,
          enabled: event.target.checked,
        }));
      },
      []
    );

    const handleTextFieldChange = useCallback(
      (event: React.ChangeEvent<HTMLInputElement>) => {
        setEmptyMessageConfig((prev) => ({
          ...prev,
          tagToAdd: event.target.value,
        }));
      },
      []
    );

    const handleSelectChange = useCallback(
      (event: SelectChangeEvent<string>) => {
        setEmptyMessageConfig((prev) => ({
          ...prev,
          setSilent: stringToBoolean(event.target.value),
        }));
      },
      []
    );

    const handleSave = useCallback(async () => {
      const payload = {
        agentId,
        emptyMessageConfig,
      };
      setLoading(true);
      const response = await handleEmptyMessageConfig(payload, dispatch);
      setLoading(false);
      setIsSaved(true);
      if (response.status === 200) {
        dispatch(agentsAction.setEmptyMessageConfigChanged(emptyMessageConfig));
      }
    }, [agentId, emptyMessageConfig, dispatch, setIsSaved]);

    const ChipComponent: React.FC<{ label: string; enabled: boolean }> =
      React.memo(function ChipComponent({ label, enabled }) {
        return (
          <Chip
            label={label}
            color={enabled ? "success" : "default"}
            variant="filled"
          />
        );
      });

    const ToggleWithInputs: React.FC = React.memo(function ToggleWithInputs() {
      const [localTagToAdd, setLocalTagToAdd] = useState(
        emptyMessageConfig.tagToAdd || ""
      );

      const handleLocalTextFieldChange = (
        event: React.ChangeEvent<HTMLInputElement>
      ) => {
        setLocalTagToAdd(event.target.value);
      };

      const handleBlur = () => {
        handleTextFieldChange({
          target: { value: localTagToAdd },
        } as React.ChangeEvent<HTMLInputElement>);
      };

      return (
        <Box sx={{ mb: 2 }}>
          <Stack direction="column">
            <FormControlLabel
              control={
                <Switch
                  checked={emptyMessageConfig.enabled}
                  onChange={handleToggleChange}
                />
              }
              label="Empty Message Handler"
            />
            <Typography variant="caption">
              This is the action we will take if we receive an empty message.
              For reference, check{" "}
              <LoomDialog
                triggerComponent={<Link>here</Link>}
                title="Empty message handler feature"
                videoUrl="https://www.loom.com/share/a7bc409886254d6b9cd60e0c1b4b6d55?sid=4f29a3e0-e3d2-4030-9f8c-c244c49335f7"
              />
            </Typography>
          </Stack>
          {emptyMessageConfig.enabled && (
            <Box sx={{ ml: 4, mt: 1, display: "flex", gap: 2 }}>
              <TextField
                label="Tag to Add"
                value={localTagToAdd}
                onChange={handleLocalTextFieldChange}
                onBlur={handleBlur}
                fullWidth
                margin="dense"
              />
              <FormControl fullWidth margin="dense">
                <InputLabel id="empty-message-set-silent-label">
                  Set Silent
                </InputLabel>
                <Select
                  labelId="empty-message-set-silent-label"
                  value={String(emptyMessageConfig.setSilent)}
                  onChange={handleSelectChange}
                  label="Set Silent"
                >
                  <MenuItem value="true">True</MenuItem>
                  <MenuItem value="false">False</MenuItem>
                </Select>
              </FormControl>
            </Box>
          )}
        </Box>
      );
    });

    const valueComponent = (
      <ChipComponent
        label={emptyMessageConfig.enabled ? "Enabled" : "Disabled"}
        enabled={emptyMessageConfig.enabled}
      />
    );

    const innerComponent = <ToggleWithInputs />;

    if (!emptyMessageConfig) return null;

    return (
      <AdvancedSettingsTemplate
        title="Empty Message Handler"
        valueComponent={valueComponent}
        innerComponent={innerComponent}
        loading={loading}
        onSave={handleSave}
        isSavedState={isSavedState}
      />
    );
  }
);

export default EmptyMessageHandler;
