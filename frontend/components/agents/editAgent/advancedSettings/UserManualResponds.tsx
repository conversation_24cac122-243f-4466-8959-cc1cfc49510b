import LoomDialog from "@/components/general/LoomDialog";
import { fetchHumanTakeover } from "@/requests/agents/advancedSettings/humanTakeover";
import { IHumanTakeover, agentsAction } from "@/slices/agents/agentsSlice";
import { RootState } from "@/store";
import {
  Button,
  Chip,
  FormControl,
  InputAdornment,
  InputLabel,
  Link,
  MenuItem,
  Select,
  Stack,
  TextField,
  Typography,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import { AiFillEdit } from "react-icons/ai";
import { ImSpinner8 } from "react-icons/im";
import { useDispatch, useSelector } from "react-redux";

const UserManualResponds = () => {
  const dispatch = useDispatch();
  const currentAgent = useSelector(
    (state: RootState) => state.agents.currentAgent
  );
  const humanTakeover = currentAgent.humanTakeover;
  const [humanTakeoverState, setHumanTakeoverState] =
    useState<IHumanTakeover | null>(null);
  const [loading, setLoading] = useState(false);
  const handleSave = async () => {
    let body;
    if (humanTakeoverState?.takeoverType === "None") {
      body = {
        takeoverType: humanTakeoverState?.takeoverType,
      };
    } else if (humanTakeoverState?.takeoverType === "Tag") {
      body = {
        takeoverType: humanTakeoverState?.takeoverType,
        tagToAdd: humanTakeoverState?.tagToAdd,
      };
    } else if (humanTakeoverState?.takeoverType === "Wait") {
      body = {
        takeoverType: humanTakeoverState?.takeoverType,
        timeToWait: humanTakeoverState?.timeToWait,
      };
    }

    setLoading(true);
    const response = await fetchHumanTakeover(
      {
        agentId: currentAgent._id,
        body: body as IHumanTakeover,
      },
      dispatch
    );
    setLoading(false);
    if (response?.status === 200) {
      dispatch(
        agentsAction.setHumanTakeover(humanTakeoverState as IHumanTakeover)
      );
      toggleEdit();
    }
  };

  useEffect(() => {
    setHumanTakeoverState(humanTakeover);
  }, [humanTakeover]);

  const [edit, setEdit] = useState(false);
  const toggleEdit = () => {
    setEdit(!edit);
  };

  if (!humanTakeoverState || !humanTakeover) return null;

  return (
    <>
      <div className="flex items-center justify-between  text-[18px] py-3 px-4">
        <div className="w-2/12 font-semibold line-clamp-2">Human Takeover</div>
        {!edit && (
          <>
            <div className="w-8/12 flex items-center gap-2">
              {humanTakeover.takeoverType === "None" ? (
                <Chip label="None" color="default" />
              ) : (
                <Chip label={humanTakeover.takeoverType} color="success" />
              )}
            </div>
            <div className="w-2/12 flex justify-end">
              <Button
                variant="outlined"
                startIcon={<AiFillEdit />}
                onClick={toggleEdit}
              >
                Edit
              </Button>
            </div>
          </>
        )}
      </div>
      {edit && (
        <>
          <div className="px-4 pt-2 flex flex-col gap-3">
            <FormControl fullWidth>
              <InputLabel id="demo-simple-select-label">
                Takeover type
              </InputLabel>
              <Select
                value={humanTakeoverState.takeoverType}
                label="Takeover type"
                onChange={(e) =>
                  setHumanTakeoverState({
                    ...humanTakeoverState,
                    takeoverType: e.target
                      .value as IHumanTakeover["takeoverType"],
                  })
                }
              >
                <MenuItem value="None">None</MenuItem>
                <MenuItem value="Tag">Tag</MenuItem>
                <MenuItem value="Wait">Wait</MenuItem>
              </Select>
            </FormControl>
            {humanTakeoverState.takeoverType === "Tag" && (
              <TextField
                label="Tag to Add"
                value={humanTakeoverState.tagToAdd}
                onChange={(e) => {
                  setHumanTakeoverState({
                    ...humanTakeoverState,
                    tagToAdd: e.target.value,
                  });
                }}
              />
            )}
            {humanTakeoverState.takeoverType === "Wait" && (
              <TextField
                label="Time to Wait (seconds)"
                type="number"
                inputProps={{ min: 0 }}
                value={humanTakeoverState.timeToWait || ""}
                onChange={(e) => {
                  setHumanTakeoverState({
                    ...humanTakeoverState,
                    timeToWait: Number(e.target.value),
                  });
                }}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">seconds</InputAdornment>
                  ),
                }}
              />
            )}
            <Typography variant="body2">
              This setting determines what Capri will do if a human takes over
              the conversation manually from the CRM. When activated, Capri will
              stop responding to a contact that a human has manually sent a
              message to. For more reference, check{" "}
              <LoomDialog
                triggerComponent={<Link>here</Link>}
                title="Human takeover feature"
                videoUrl="https://www.loom.com/share/9daffd883a584d3dadad49621bd70768?sid=fe9a30db-19f3-440b-9cec-75a1c2278bd2"
              />
            </Typography>
          </div>
          <Stack direction={"row"} justifyContent={"end"} gap={2} p={2}>
            <Button variant="text" color="error" onClick={toggleEdit}>
              Cancel
            </Button>
            <Button variant="contained" disabled={loading} onClick={handleSave}>
              Save {loading && <ImSpinner8 className="animate-spin ml-2" />}
            </Button>
          </Stack>
        </>
      )}
    </>
  );
};

export default UserManualResponds;
