import { handleContextLength } from "@/requests/agents/advancedSettings/handleContextLength";
import { agentsAction } from "@/slices/agents/agentsSlice";
import { RootState } from "@/store";
import { <PERSON><PERSON>, <PERSON>, Slider, Stack, Typography } from "@mui/material";
import React, { useEffect, useState } from "react";
import { AiFillEdit } from "react-icons/ai";
import { ImSpinner8 } from "react-icons/im";
import { useDispatch, useSelector } from "react-redux";

const MIN_CONTEXT_LENGTH = 200;
const MAX_CONTEXT_LENGTH = 4000;
const STEP_SIZE = 200;
const DEFAULT_VALUE = 1000;

const ContextLengthInAugmentedQueryHandler = () => {
  const dispatch = useDispatch();
  const currentAgent = useSelector(
    (state: RootState) => state.agents.currentAgent
  );
  const [loading, setLoading] = useState(false);
  const contextLength = currentAgent?.contextLength || DEFAULT_VALUE;
  const [settings, setSettings] = useState<number | null>(null);
  const [edit, setEdit] = useState(false);

  useEffect(() => {
    setSettings(contextLength);
  }, [contextLength, edit]);

  const toggleEdit = () => {
    setEdit(!edit);
  };

  const handleSave = async () => {
    if (settings === null) return;
    setLoading(true);
    const response = await handleContextLength(
      {
        agentId: currentAgent?._id,
        contextLength: settings,
      },
      dispatch
    );
    setLoading(false);
    if (response.status === 200) {
      dispatch(agentsAction.setContextLength(settings));
      toggleEdit();
    }
  };

  if (settings === null) return null;

  return (
    <>
      <div className="flex items-center justify-between text-[18px] py-3 px-4">
        <div className="w-2/12 font-semibold line-clamp-2">
          Augmented Query Context Length
        </div>
        {!edit && (
          <>
            <div className="w-8/12 flex items-center gap-2">
              <Chip label={`${settings} tokens`} />
            </div>
            <div className="w-2/12 flex justify-end">
              <Button
                variant="outlined"
                startIcon={<AiFillEdit />}
                onClick={toggleEdit}
              >
                Edit
              </Button>
            </div>
          </>
        )}
      </div>
      {edit && (
        <>
          <div className="px-4 pb-3">
            <Typography variant="body2" sx={{ mb: 2 }}>
              Control the number of tokens that is read from your knowledge
              sources. For common use cases keep it between 800-1200. If your use
              case requires a large amount of data to be read, consider keeping
              it between 2000-3000.
            </Typography>
            <Slider
              value={settings}
              min={MIN_CONTEXT_LENGTH}
              max={MAX_CONTEXT_LENGTH}
              step={STEP_SIZE}
              marks
              valueLabelDisplay="auto"
              onChange={(_, value) => setSettings(value as number)}
            />
          </div>
          <Stack direction={"row"} justifyContent={"end"} gap={2} p={2}>
            <Button variant="text" color="error" onClick={toggleEdit}>
              Cancel
            </Button>
            <Button variant="contained" disabled={loading} onClick={handleSave}>
              Save {loading && <ImSpinner8 className="animate-spin ml-2" />}
            </Button>
          </Stack>
        </>
      )}
    </>
  );
};

export default ContextLengthInAugmentedQueryHandler;
