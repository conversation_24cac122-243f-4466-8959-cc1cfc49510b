import React, { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>le,
  <PERSON>alogContent,
  <PERSON>alogA<PERSON>,
  Button,
  Autocomplete,
  TextField,
  CircularProgress,
  Box,
  Typography,
} from "@mui/material";
import { useDispatch } from "react-redux";
import { getOrgSnapshot } from "@/requests/agent-snapshots/get-org-snapshot";
import { linkExistingAgentToSnapshot } from "@/requests/agent-snapshots/link-existing-agent-to-snapshot";
import { agentsAction } from "@/slices/agents/agentsSlice";

interface ISnapshot {
  _id: string;
  name: string;
  description: string;
}

interface Props {
  open: boolean;
  onClose: () => void;
  agentId: string;
  orgId: string;
}

export const LinkAgentToSnapshotDialog = ({
  open,
  onClose,
  agentId,
  orgId,
}: Props) => {
  const dispatch = useDispatch();
  const [snapshots, setSnapshots] = useState<ISnapshot[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedSnapshot, setSelectedSnapshot] = useState<ISnapshot | null>(
    null
  );
  const [linking, setLinking] = useState(false);

  useEffect(() => {
    const fetchSnapshots = async () => {
      setLoading(true);
      try {
        const response = await getOrgSnapshot(
          { organizationId: orgId },
          dispatch
        );
        if (response?.status === 200) {
          setSnapshots(response.data);
        }
      } finally {
        setLoading(false);
      }
    };

    if (open) {
      fetchSnapshots();
    }
  }, [open, orgId, dispatch]);

  const handleLink = async () => {
    if (!selectedSnapshot) return;

    setLinking(true);
    try {
      const response = await linkExistingAgentToSnapshot(
        {
          agentId,
          snapshotId: selectedSnapshot._id,
        },
        dispatch
      );

      if (response?.status === 201) {
        dispatch(agentsAction.setCurrentAgentSnapshot(selectedSnapshot._id));
        onClose();
      }
    } finally {
      setLinking(false);
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle>Link Agent to Snapshot</DialogTitle>
      <DialogContent>
        {loading ? (
          <Box display="flex" justifyContent="center" my={3}>
            <CircularProgress />
          </Box>
        ) : (
          <Autocomplete
            options={snapshots}
            getOptionLabel={(option) => option.name}
            renderOption={(props, option) => (
              <li {...props}>
                <Box>
                  <Typography variant="body1">{option.name}</Typography>
                  <Typography variant="body2" color="text.secondary">
                    {option.description}
                  </Typography>
                </Box>
              </li>
            )}
            renderInput={(params) => (
              <TextField
                {...params}
                label="Select Snapshot"
                fullWidth
                margin="normal"
              />
            )}
            value={selectedSnapshot}
            onChange={(_, newValue) => setSelectedSnapshot(newValue)}
          />
        )}
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
        <Button
          onClick={handleLink}
          variant="contained"
          disabled={!selectedSnapshot || linking}
          startIcon={linking && <CircularProgress size={20} />}
        >
          Link
        </Button>
      </DialogActions>
    </Dialog>
  );
};
