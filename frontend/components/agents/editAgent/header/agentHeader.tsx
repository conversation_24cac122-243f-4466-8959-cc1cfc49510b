import BackArrow from "@/components/general/backArrow";
import React, { useEffect, useState } from "react";
import { AiOutlineCloseCircle, AiOutlineEdit } from "react-icons/ai";
import SensorsIcon from "@mui/icons-material/Sensors";
import { MdOutlineDone } from "react-icons/md";
import Button from "@mui/material/Button";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store";
import GeneralModal from "@/components/general/modal";
import { useRouter } from "next/router";
import { BsFillTrashFill } from "react-icons/bs";
import GeneralModalContent from "@/components/general/generalModalContent";
import { deleteAgent } from "@/requests/agents/basic/deleteAgent";
import { IAgent, agentsAction } from "@/slices/agents/agentsSlice";
import { editAgentName } from "@/requests/agents/name/editAgentName";
import { changeAgentStatus } from "@/requests/agents/basic/changeAgentStatus";
import {
  Box,
  FormControlLabel,
  IconButton,
  Stack,
  Switch,
  TextField,
  ToggleButton,
  ToggleButtonGroup,
  Tooltip,
  CircularProgress,
} from "@mui/material";
import { grey } from "@mui/material/colors";
import { TrainingStatusIndicator } from "../../TrainingStatusIndicator";
import { CreateSnapshotDialog } from "@/components/agents/editAgent/header/create-snapshot-dialog";
import { getSnapshotsByAgentId } from "@/requests/agent-snapshots/get-snapshots-by-agentId";
import MoreVertIcon from "@mui/icons-material/MoreVert";
import Menu from "@mui/material/Menu";
import MenuItem from "@mui/material/MenuItem";
import ListItemIcon from "@mui/material/ListItemIcon";
import ListItemText from "@mui/material/ListItemText";
import SaveAltIcon from "@mui/icons-material/SaveAlt";
import DeleteIcon from "@mui/icons-material/Delete";
import { LinkAgentToSnapshotDialog } from "./link-agent-to-snapshot-dialog";
import LinkIcon from "@mui/icons-material/Link";

const AgentHeader = () => {
  const dispatch = useDispatch();
  const router = useRouter();
  const path = router.asPath;
  const header = useSelector(
    (state: RootState) => state.agents.currentAgent?.agentName
  );
  const currentAgent: IAgent = useSelector(
    (state: RootState) => state.agents.currentAgent
  );

  const [open, setOpen] = useState(false);
  const [editing, setEditing] = useState(false);
  const [loading, setLoading] = useState(false); //for the warning modal
  const [inputValue, setInputValue] = useState("");
  const [disabled, setDisabled] = useState(currentAgent?.disabled);
  const [snapshots, setSnapshots] = useState([]);
  const [snapshotDialogOpen, setSnapshotDialogOpen] = useState(false);
  const [isLoadingSnapshots, setIsLoadingSnapshots] = useState(true);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const menuOpen = Boolean(anchorEl);
  const [linkDialogOpen, setLinkDialogOpen] = useState(false);

  // First, define fetchSnapshots outside of useEffect
  const fetchSnapshots = async () => {
    const response = await getSnapshotsByAgentId(
      { agentId: currentAgent._id },
      dispatch
    );
    if (response?.status === 200) {
      setSnapshots(response.data);
    }
  };

  useEffect(() => {
    setDisabled(
      currentAgent && "disabled" in currentAgent ? currentAgent.disabled : false
    );
  }, [currentAgent]);
  useEffect(() => {
    setInputValue(header);
  }, [header]);
  useEffect(() => {
    const loadSnapshots = async () => {
      setIsLoadingSnapshots(true);
      if (currentAgent?._id) {
        await fetchSnapshots();
      }
      setIsLoadingSnapshots(false);
    };
    loadSnapshots();
  }, [currentAgent?._id, dispatch]);

  const handleClose = () => {
    setOpen(false);
  };
  const handleDelete = async () => {
    setLoading(true);
    //Write the axios request for agent delete
    const response = await deleteAgent(
      { agentId: currentAgent._id as string },
      dispatch
    );
    router.push("/home/<USER>");
    setLoading(false);
  };
  const handleHeaderChange = async () => {
    //Write the axios req for handling the Header change
    const body = {
      agentId: currentAgent?._id,
      agentName: inputValue,
    };
    const response = await editAgentName(body, dispatch);
    if (response?.status == 201) {
      dispatch(agentsAction.agentNameChanged(body.agentName));
    }
    setEditing(false);
  };
  const handleStatusChange = async (
    event: React.MouseEvent<HTMLElement>,
    newStatus: boolean
  ) => {
    if (newStatus != null) {
      const response = await changeAgentStatus(
        {
          agentId: currentAgent?._id,
          disabled: newStatus,
        },
        dispatch
      );
      if (response?.status == 200) {
        setDisabled(newStatus);
      }
    }
  };

  const handleMenuClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  return (
    <>
      {" "}
      <GeneralModal open={open} onClose={handleClose}>
        <GeneralModalContent
          header="Warning"
          content="Are you sure you want to delete this agent?"
          rightBtn="Delete"
          isLoading={loading}
          onClose={handleClose}
          customFn={handleDelete}
        />
      </GeneralModal>
      <CreateSnapshotDialog
        open={snapshotDialogOpen}
        onClose={() => setSnapshotDialogOpen(false)}
        agentId={currentAgent?._id}
        orgId={currentAgent?.orgId}
        onSuccess={fetchSnapshots}
      />
      <LinkAgentToSnapshotDialog
        open={linkDialogOpen}
        onClose={() => setLinkDialogOpen(false)}
        agentId={currentAgent?._id}
        orgId={currentAgent?.orgId}
      />
      <div className="flex text-fourth items-center justify-between">
        <Box display="flex" alignItems="center">
          <BackArrow />
          {!editing && (
            <>
              <div className="text-heading2 font-semibold mx-3 my-4 cursor-pointer max-w-[400px] truncate">
                {header ? header : "Not set"}
              </div>
              <AiOutlineEdit
                className="text-heading2 cursor-pointer"
                onClick={() => {
                  setEditing(true);
                }}
              />
            </>
          )}
          {editing && (
            <Box mx={3} my={4} position="relative">
              <Stack direction={"row"}>
                <TextField
                  name="agentName"
                  value={inputValue}
                  fullWidth
                  variant="outlined"
                  // label="Agent name"
                  className="cursor-text"
                  sx={{ background: grey[200] }}
                  InputProps={{
                    endAdornment: (
                      <IconButton edge="end" onClick={handleHeaderChange}>
                        <MdOutlineDone fontSize="inherit" />
                      </IconButton>
                    ),
                  }}
                  onChange={(event: React.ChangeEvent<HTMLInputElement>) =>
                    setInputValue(event.target.value)
                  }
                />
                <IconButton
                  aria-label="delete"
                  color="error"
                  size="large"
                  onClick={() => {
                    setEditing(false);
                  }}
                >
                  <AiOutlineCloseCircle fontSize="inherit" />
                </IconButton>
              </Stack>
            </Box>
          )}
        </Box>
        <div className="right flex gap-3">
          {isLoadingSnapshots ? (
            <Box display="flex" alignItems="center">
              <CircularProgress size={24} />
            </Box>
          ) : (
            <>
              {currentAgent.snapshotId && (
                <Tooltip title="This agent is synced with an external source. Please contact your admin for more details.">
                  <Box
                    display="flex"
                    alignItems="center"
                    sx={{ color: "warning.main" }}
                  >
                    <SensorsIcon className="mr-2" />
                    External Source
                  </Box>
                </Tooltip>
              )}
              {snapshots.length > 0 && (
                <Box
                  display="flex"
                  alignItems="center"
                  sx={{ color: "info.main" }}
                >
                  <SaveAltIcon className="mr-2" />
                  Snapshot Created
                </Box>
              )}
              <TrainingStatusIndicator
                agent={{
                  _id: currentAgent._id,
                  name: currentAgent.agentName,
                  isTraining: currentAgent.processingJobs?.length > 0,
                  activeTraining: currentAgent.processingJobs?.map((job) => ({
                    startedAt: new Date(),
                    status: "in_progress",
                    type: job.jobType,
                    details: `${job.fileName} is being processed.`,
                  })),
                }}
                onRetry={() => {}}
              />
              <ToggleButtonGroup
                value={disabled}
                exclusive
                onChange={handleStatusChange}
                aria-label="text alignment"
                sx={{ background: "gray" }}
              >
                <ToggleButton
                  value={true}
                  aria-label="Draft button"
                  color="warning"
                  sx={{
                    backgroundColor: disabled ? "warning.main" : "gray",
                    "&:hover": {
                      backgroundColor: "warning.main",
                    },
                    "&.Mui-selected": {
                      backgroundColor: "warning.main",
                      color: "white",
                    },
                  }}
                >
                  Draft
                </ToggleButton>
                <ToggleButton
                  value={false}
                  aria-label="Draft"
                  color="success"
                  sx={{
                    backgroundColor: !disabled ? "success.main" : "gray",
                    "&:hover": {
                      backgroundColor: "success.main",
                    },
                    "&.Mui-selected": {
                      backgroundColor: "success.main",
                      color: "white",
                    },
                  }}
                  id="agentSetActive_btn"
                >
                  Active
                </ToggleButton>
              </ToggleButtonGroup>
              <IconButton
                onClick={handleMenuClick}
                size="small"
                sx={{ color: "white" }}
                aria-controls={menuOpen ? "more-menu" : undefined}
                aria-haspopup="true"
                aria-expanded={menuOpen ? "true" : undefined}
              >
                <MoreVertIcon />
              </IconButton>
              <Menu
                anchorEl={anchorEl}
                id="more-menu"
                open={menuOpen}
                onClose={handleMenuClose}
                onClick={handleMenuClose}
                transformOrigin={{ horizontal: "right", vertical: "top" }}
                anchorOrigin={{ horizontal: "right", vertical: "bottom" }}
              >
                <MenuItem
                  onClick={() => setSnapshotDialogOpen(true)}
                  disabled={snapshots.length > 0}
                >
                  <ListItemIcon>
                    <SaveAltIcon fontSize="small" />
                  </ListItemIcon>
                  <ListItemText>Create Snapshot</ListItemText>
                </MenuItem>
                <MenuItem
                  onClick={() => setLinkDialogOpen(true)}
                  disabled={Boolean(currentAgent.snapshotId)}
                >
                  <ListItemIcon>
                    <LinkIcon fontSize="small" />
                  </ListItemIcon>
                  <ListItemText>Link to Snapshot</ListItemText>
                </MenuItem>
                <MenuItem onClick={() => setOpen(true)}>
                  <ListItemIcon>
                    <DeleteIcon fontSize="small" color="error" />
                  </ListItemIcon>
                  <ListItemText>Delete</ListItemText>
                </MenuItem>
              </Menu>
            </>
          )}
        </div>
      </div>
    </>
  );
};

export default AgentHeader;
