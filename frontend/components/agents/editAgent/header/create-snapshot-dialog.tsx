import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>alog<PERSON><PERSON>,
  <PERSON>alogContent,
  DialogTitle,
  TextField,
  Stack,
  CircularProgress,
} from "@mui/material";
import { useState } from "react";
import { useDispatch } from "react-redux";
import { createSnapshotFromAgent } from "@/requests/agent-snapshots/create-snapshot-from-agent";
import { useCheckSnapshotName } from "@/hooks/useCheckSnapshotName";
import NameValidation from "@/components/agents/agent-builder/NameValidation";

interface CreateSnapshotDialogProps {
  open: boolean;
  onClose: () => void;
  agentId: string;
  orgId: string;
  onSuccess: () => void;
}

export const CreateSnapshotDialog = ({
  open,
  onClose,
  agentId,
  orgId,
  onSuccess,
}: CreateSnapshotDialogProps) => {
  const dispatch = useDispatch();
  const [name, setName] = useState("");
  const [description, setDescription] = useState("");
  const [loading, setLoading] = useState(false);
  const { isChecking: isCheckingName, validationResult: nameValidationResult } =
    useCheckSnapshotName(name);

  const handleCreate = async () => {
    setLoading(true);
    try {
      const response = await createSnapshotFromAgent(
        {
          agentId,
          orgId,
          name,
          description,
        },
        dispatch
      );

      if (response?.status === 200 || response?.status === 201) {
        onSuccess();
        onClose();
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle>Create Agent Snapshot</DialogTitle>
      <DialogContent>
        <Stack spacing={2} sx={{ mt: 2 }}>
          <TextField
            label="Snapshot Name"
            fullWidth
            value={name}
            onChange={(e) => setName(e.target.value)}
            disabled={loading}
            error={
              name.length >= 3 && nameValidationResult.isNameUnique === false
            }
            helperText={
              name.length > 0 && name.length < 3
                ? "Snapshot name must be at least 3 characters long"
                : ""
            }
          />
          <NameValidation
            name={name}
            isChecking={isCheckingName}
            validationResult={nameValidationResult}
            customSuccessMessage="This snapshot name is available!"
            customErrorMessage="This snapshot name is already taken. Please choose another."
          />
          <TextField
            label="Description"
            fullWidth
            multiline
            rows={4}
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            disabled={loading}
          />
        </Stack>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} disabled={loading}>
          Cancel
        </Button>
        <Button
          onClick={handleCreate}
          variant="contained"
          disabled={
            !name ||
            loading ||
            isCheckingName ||
            name.length < 3 ||
            nameValidationResult.isNameUnique === false
          }
          startIcon={
            loading ? <CircularProgress size={20} color="inherit" /> : null
          }
        >
          {loading ? "Creating..." : "Create Snapshot"}
        </Button>
      </DialogActions>
    </Dialog>
  );
};
