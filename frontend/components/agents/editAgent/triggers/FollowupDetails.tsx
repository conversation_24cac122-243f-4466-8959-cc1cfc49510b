import React, { useEffect, useState } from "react";
import {
  Grid,
  TextField,
  Autocomplete,
  InputAdornment,
  Switch,
  FormControlLabel,
  Typography,
  Paper,
  Box,
  Button,
} from "@mui/material";
import { useSelector } from "react-redux";
import { RootState } from "@/store";
import { IFollowupDetails } from "@/slices/agents/agentsSlice";
import { Add as AddIcon } from "@mui/icons-material";
import { TagConditionBlock } from "./TagConditionBlock";
import EnhancedTextField from "@/components/agents/editAgent/dataSources/TextField";
import FollowupSchedulePicker from "./FollowupSchedulePicker";
import { googleTimeZones } from "@/helpers/text/googleTimeZones";

const DEFAULT_DETAILS: IFollowupDetails = {
  isFollowupEnabled: false,
  duration: 15,
  maxAttempts: 2,
  promptId: "",
  conditionPrompt: "",
  tagConditions: [],
  timezone: "America/New_York", // Default timezone
  schedule: {
    monday: [],
    tuesday: [],
    wednesday: [],
    thursday: [],
    friday: [],
    saturday: [],
    sunday: [],
  },
};

interface IProp {
  followupDetails?: IFollowupDetails;
  onFollowupChange: (details: IFollowupDetails) => void;
}

const FollowupDetails: React.FC<IProp> = ({
  followupDetails: followupDetailsFromParent = DEFAULT_DETAILS,
  onFollowupChange,
}) => {
  const {
    prompts: { currentActive: currentActivePromptId, prompt: promptsList },
  } = useSelector((state: RootState) => state.agents.currentAgent);
  const [followupDetails, setFollowupDetails] = useState<IFollowupDetails>(
    followupDetailsFromParent
  );

  const handleChange = (field: keyof IFollowupDetails, value: any) => {
    const updatedDetails = {
      ...followupDetails,
      [field]: value,
    };
    setFollowupDetails(updatedDetails);
    onFollowupChange(updatedDetails);
  };

  const handleAddTagCondition = () => {
    const updatedDetails = {
      ...followupDetails,
      tagConditions: [
        ...(followupDetails.tagConditions || []),
        { tagOption: "", tagValue: "" },
      ],
    };
    setFollowupDetails(updatedDetails);
    onFollowupChange(updatedDetails);
  };

  const handleUpdateTagCondition =
    (index: number) => (updatedCondition: any) => {
      const newTagConditions = [...(followupDetails.tagConditions || [])];
      newTagConditions[index] = updatedCondition;
      const updatedDetails = {
        ...followupDetails,
        tagConditions: newTagConditions,
      };
      setFollowupDetails(updatedDetails);
      onFollowupChange(updatedDetails);
    };

  const handleDeleteTagCondition = (index: number) => () => {
    const newTagConditions = followupDetails.tagConditions?.filter(
      (_, i) => i !== index
    );
    const updatedDetails = {
      ...followupDetails,
      tagConditions: newTagConditions,
    };
    setFollowupDetails(updatedDetails);
    onFollowupChange(updatedDetails);
  };

  const handleScheduleChange = (schedule: IFollowupDetails["schedule"]) => {
    handleChange("schedule", schedule);
  };

  useEffect(() => {
    console.log({ followupDetails });
  }, [followupDetails]);

  const truncateText = (text: string, maxLines: number = 2) => {
    const lines = text.split("\n").slice(0, maxLines);
    const truncated = lines.join("\n");
    return lines.length < text.split("\n").length
      ? `${truncated}...`
      : truncated;
  };

  return (
    <Grid container spacing={2}>
      <Grid item xs={12}>
        <FormControlLabel
          control={
            <Switch
              checked={followupDetails.isFollowupEnabled}
              onChange={(e) =>
                handleChange("isFollowupEnabled", e.target.checked)
              }
            />
          }
          label="Enable Followup"
        />
      </Grid>

      {followupDetails.isFollowupEnabled && (
        <Grid item xs={12}>
          <Paper sx={{ p: 2, bgcolor: "#fafafa" }}>
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <Typography variant="h6" gutterBottom>
                  Follow-up Details
                </Typography>
              </Grid>
              <Grid item xs={6}>
                <TextField
                  fullWidth
                  label="Duration"
                  type="number"
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">Minutes</InputAdornment>
                    ),
                    inputProps: { min: 0 },
                  }}
                  value={followupDetails.duration}
                  onChange={(e) =>
                    handleChange("duration", Number(e.target.value))
                  }
                />
              </Grid>
              <Grid item xs={6}>
                <TextField
                  fullWidth
                  label="Maximum Attempts"
                  type="number"
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">Attempts</InputAdornment>
                    ),
                    inputProps: { min: 0 },
                  }}
                  value={followupDetails.maxAttempts}
                  onChange={(e) =>
                    handleChange("maxAttempts", Number(e.target.value))
                  }
                />
              </Grid>
              <Grid item xs={12}>
                <Autocomplete
                  options={promptsList}
                  getOptionLabel={(option) => option.name}
                  value={
                    promptsList.find(
                      (p) => p.promptId === followupDetails.promptId
                    ) || null
                  }
                  onChange={(_, newValue) => {
                    handleChange("promptId", newValue?.promptId || "");
                  }}
                  renderOption={(props, option) => (
                    <Box component="li" {...props}>
                      <Box>
                        <Typography variant="subtitle1">
                          {option.name}
                        </Typography>
                        <Typography
                          variant="body2"
                          color="text.secondary"
                          sx={{
                            whiteSpace: "pre-line",
                            overflow: "hidden",
                            textOverflow: "ellipsis",
                            display: "-webkit-box",
                            WebkitLineClamp: 2,
                            WebkitBoxOrient: "vertical",
                          }}
                        >
                          {truncateText(option.promptContent)}
                        </Typography>
                      </Box>
                    </Box>
                  )}
                  renderInput={(params) => (
                    <TextField {...params} label="Select Prompt" fullWidth />
                  )}
                />
              </Grid>
              <Grid item xs={12}>
                <Typography variant="subtitle1" gutterBottom>
                  Condition Prompt
                </Typography>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Specify conditions that determine when the follow-up should be
                  triggered. This prompt will be evaluated to decide if a
                  follow-up action is needed.
                </Typography>
                <EnhancedTextField
                  fullWidth
                  label="Condition Prompt"
                  multiline
                  rows={4}
                  value={followupDetails.conditionPrompt}
                  onChange={(value: string) =>
                    handleChange("conditionPrompt", value)
                  }
                  placeholder="Enter the condition prompt for follow-up..."
                  variant="outlined"
                  margin="normal"
                  helperText="You can use tags and variables to create dynamic conditions for your follow-up logic."
                />
              </Grid>
              <Grid item xs={12}>
                <Typography variant="subtitle1" gutterBottom>
                  Tag Conditions (AND)
                </Typography>
                <Grid container spacing={2}>
                  {followupDetails.tagConditions?.map((condition, index) => (
                    <Grid item xs={12} key={index}>
                      <TagConditionBlock
                        condition={condition}
                        onUpdate={handleUpdateTagCondition(index)}
                        onDelete={handleDeleteTagCondition(index)}
                        showDelete={followupDetails.tagConditions?.length > 1}
                      />
                      {index <
                        (followupDetails.tagConditions?.length || 0) - 1 && (
                        <Typography
                          align="left"
                          color="textSecondary"
                          variant="h6"
                          sx={{ mt: 2, mb: 1 }}
                        >
                          AND
                        </Typography>
                      )}
                    </Grid>
                  ))}
                  <Grid item xs={12}>
                    <Button
                      startIcon={<AddIcon />}
                      onClick={handleAddTagCondition}
                      variant="outlined"
                      size="small"
                    >
                      Add Tag Condition
                    </Button>
                  </Grid>
                </Grid>
              </Grid>

              {/* Timezone Picker */}
              <Grid item xs={12} sx={{ mt: 2 }}>
                <Typography variant="subtitle1" gutterBottom>
                  Follow-up Time Settings
                </Typography>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Configure when follow-ups can be sent based on timezone and
                  schedule.
                </Typography>
              </Grid>
              <Grid item xs={12}>
                <Autocomplete
                  options={googleTimeZones}
                  value={followupDetails.timezone}
                  onChange={(_, newValue) => {
                    handleChange("timezone", newValue || "UTC");
                  }}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      label="Timezone"
                      fullWidth
                      helperText="Select the timezone for the follow-up schedule"
                    />
                  )}
                />
              </Grid>

              {/* Schedule Picker */}
              <Grid item xs={12} sx={{ mt: 2 }}>
                <FollowupSchedulePicker
                  schedule={followupDetails.schedule}
                  onScheduleChange={handleScheduleChange}
                />
              </Grid>
            </Grid>
          </Paper>
        </Grid>
      )}
    </Grid>
  );
};

export default FollowupDetails;
