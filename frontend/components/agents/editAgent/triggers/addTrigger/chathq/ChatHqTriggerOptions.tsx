import { IChatHqTrigger } from "@/slices/agents/agentsSlice";
import { FormControl, Grid, InputLabel, MenuItem, Select } from "@mui/material";
import React from "react";

interface IProp {
  data: IChatHqTrigger;
  setData: (data: IChatHqTrigger) => void;
}

const ChatHqTriggerOptions: React.FC<IProp> = ({ data, setData }) => {
  return (
    <>
      <Grid container spacing={2}>
        <Grid item xs={6}>
          <FormControl fullWidth required>
            <InputLabel id="liveChatWidget">Live chat widget</InputLabel>
            <Select
              labelId="liveChatWidget"
              id="liveChatWidget"
              value={String(data.liveChatWidget || "")}
              label="Live chat widget"
              onChange={(e) => {
                setData({
                  ...data,
                  liveChatWidget: String(e.target.value),
                });
              }}
            >
              {[
                { name: "Chat widget 1", value: "chatwidget1" },
                { name: "Chat widget 2", value: "chatwidget2" },
              ].map((item) => (
                <MenuItem
                  value={item.value}
                  key={"liveChatWidget" + item.value}
                >
                  {item.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>
      </Grid>
    </>
  );
};

export default ChatHqTriggerOptions;
