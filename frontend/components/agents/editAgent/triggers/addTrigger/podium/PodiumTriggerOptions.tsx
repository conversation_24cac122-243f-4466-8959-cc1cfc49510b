import { IPodiumTrigger } from "@/slices/agents/agentsSlice";
import React, { useEffect, useState } from "react";
import {
  FormControl,
  Grid,
  InputLabel,
  MenuItem,
  Select,
  TextField,
} from "@mui/material";
import { useDispatch } from "react-redux";
import { getChannelAccounts } from "@/requests/channels/getChannelAccounts";

interface IProp {
  data: IPodiumTrigger;
  setData: (data: IPodiumTrigger) => void;
}

const channelList = [{ name: "SMS", value: "phone" }];

const PodiumTriggerOptions: React.FC<IProp> = ({ data, setData }) => {
  const dispatch = useDispatch();
  const [accountList, setAccountList] = useState<
    {
      accountId: string;
      name: string;
      providerName: string;
    }[]
  >([]);
  useEffect(() => {
    const response = getChannelAccounts(
      {
        orgId: localStorage.getItem("orgId") as string,
        provider: "podium",
      },
      dispatch
    );
    response.then((resData) => {
      if (resData?.status === 200) {
        setAccountList(resData.data.channels);
      }
    });
  }, []);

  return (
    <Grid container spacing={2}>
      <Grid item xs={6}>
        <FormControl fullWidth required>
          <InputLabel id="subaccount">Subaccount</InputLabel>
          <Select
            labelId="subaccount"
            id="subaccount"
            value={String(data.subaccount || "")}
            label="Subaccount"
            onChange={(e) => {
              setData({
                ...data,
                subaccount: String(e.target.value),
              });
            }}
          >
            {accountList.map((item) => (
              <MenuItem
                value={item.accountId}
                key={"subaccount" + item.accountId}
              >
                {item.name}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Grid>
      <Grid item xs={6}>
        <FormControl fullWidth required>
          <InputLabel id="channel">Channel</InputLabel>
          <Select
            labelId="channel"
            id="channel"
            value={String(data.channel || "")}
            label="Channel"
            onChange={(e) => {
              setData({
                ...data,
                channel: String(e.target.value),
              });
            }}
          >
            {channelList.map((item) => (
              <MenuItem value={item.value} key={"channel" + item.value}>
                {item.name}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Grid>
      <Grid item xs={6}>
        <FormControl fullWidth required>
          <InputLabel id="task">Task type</InputLabel>
          <Select
            labelId="taskOption"
            id="taskOption"
            value={String(data.task || "")}
            label="Task type"
            onChange={(e) => {
              setData({
                ...data,
                task: String(e.target.value),
              });
            }}
          >
            {[
              { name: "Response", value: "respond" },
              { name: "Outreach", value: "outreach" },
              { name: "Evaluate", value: "evaluate" },
            ].map((item) => (
              <MenuItem value={item.value} key={"taskOption" + item.value}>
                {item.name}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Grid>
      <Grid item xs={6}>
        <FormControl fullWidth required>
          <InputLabel id="channel">Tag type</InputLabel>
          <Select
            labelId="tagOption"
            id="tagOption"
            value={String(data.tagOption || "")}
            label="Tag type"
            onChange={(e) => {
              setData({
                ...data,
                tagOption: String(e.target.value),
              });
            }}
          >
            {[
              { name: "HAS TAG", value: "hasTag" },
              { name: "DOES NOT HAVE TAG", value: "doesntHaveTag" },
            ].map((item) => (
              <MenuItem value={item.value} key={"tagOption" + item.value}>
                {item.name}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Grid>
      {data.tagOption && (
        <Grid item xs={6}>
          <TextField
            id="tagValue"
            label="Tag value"
            variant="outlined"
            value={String(data.tagValue || "")}
            fullWidth
            required
            onChange={(e) => {
              setData({
                ...data,
                tagValue: String(e.target.value),
              });
            }}
          />
        </Grid>
      )}
    </Grid>
  );
};

export default PodiumTriggerOptions;
