import Line from "@/components/general/shapes/horizontalLine";
import { RootState } from "@/store";
import {
  Button,
  FormControl,
  Grid,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  Typography,
} from "@mui/material";
import React, { FormEvent, useEffect, useState } from "react";
import { AiFillSave } from "react-icons/ai";
import { useDispatch, useSelector } from "react-redux";
import GhlTriggerOptions from "./ghl/GhlTriggerOptions";
import ChatHqTriggerOptions from "./chathq/ChatHqTriggerOptions";
import {
  IChatHqTrigger,
  IFollowupDetails,
  IGhlTrigger,
  IHubspotTrigger,
  IPodiumTrigger,
  ITrigger,
  agentsAction,
} from "@/slices/agents/agentsSlice";
import { useRouter } from "next/router";
import {
  ITriggerBasicProp,
  addTrigger,
} from "@/requests/agents/trigger/addTrigger";
import { editTrigger } from "@/requests/agents/trigger/editTrigger";
import { snackbarActions } from "@/slices/general/snackbar";
import ButtonSpinner from "@/components/general/buttons/spinner";
import { CONSTANTS } from "@/helpers/constants";
import PodiumTriggerOptions from "./podium/PodiumTriggerOptions";
import HubspotTriggerOptions from "./hubspot/HubspotTriggerOptions";

interface IProp {
  addBtnFn: () => void;
  data?: ITrigger;
  type: "add" | "edit";
}

const AddTrigger: React.FC<IProp> = ({ addBtnFn, data, type }) => {
  const dispatch = useDispatch();
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const { agentId } = router.query as { agentId: string };
  const channelsList = useSelector(
    (state: RootState) => state.channelsList.channelList
  );
  const [triggerDetails, setTriggerDetails] = useState<ITrigger>({
    triggerId: "",
    triggerName: "",
    providerName: "",
    active: false,
    data: {
      tagConditions: [],
      task: "respond",
    },
  });
  useEffect(() => {
    if (data) {
      setTriggerDetails(data);
    }
  }, [data]);
  const getBody = () => {
    const bodyBasic = {
      agentId,
      triggerName: triggerDetails.triggerName,
      providerName: triggerDetails.providerName,
      active: triggerDetails.active,
    } as ITriggerBasicProp;
    console.log({ bodyBasic });

    if (triggerDetails.providerName === CONSTANTS.PROVIDERS.GHL_CHANNEL) {
      const ghlData = triggerDetails.data as IGhlTrigger;
      return {
        ...bodyBasic,
        data: ghlData,
        followUp: triggerDetails.followUp,
      };
    } else if (triggerDetails.providerName === CONSTANTS.PROVIDERS.PODIUM) {
      const podiumData = triggerDetails.data as IPodiumTrigger;
      return {
        ...bodyBasic,
        data: podiumData,
      };
    } else if (triggerDetails.providerName === CONSTANTS.PROVIDERS.HUBSPOT) {
      const hubspotData = triggerDetails.data as IHubspotTrigger;
      return {
        ...bodyBasic,
        data: hubspotData,
      };
    }
  };
  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    let body = getBody();
    console.log({ body });
    if (body === undefined) {
      return;
    }
    if (type === "add") {
      try {
        setLoading(true);
        const response = await addTrigger(body, dispatch);
        setLoading(false);
        addBtnFn();
        if (response?.status === 201) {
          dispatch(
            agentsAction.ghlTriggerAdded({ response: response.data.trigger })
          );
          console.log("res ", response.data.trigger);
        }
      } catch (error: any) {
        dispatch(
          snackbarActions.setSnackbar({
            message:
              error.response?.data?.message ||
              "An unexpected error occurred. Please try again.",
            type: "error",
          })
        );
      }
    } else if (type === "edit") {
      try {
        const { triggerId, ...restOfData } = triggerDetails as ITrigger;
        const payload = {
          agentId,
          triggerId,
          data: restOfData,
          // followUp: triggerDetails.followUp,
        };
        setLoading(true);
        const response = await editTrigger(payload, dispatch);
        setLoading(false);
        addBtnFn();
        if (response?.status === 200) {
          dispatch(
            agentsAction.ghlTriggerEdited({ response: response.data.trigger })
          );
          console.log("res ", response.data.trigger);
        }
      } catch (error: any) {
        console.log(error);
      }
    }
  };
  return (
    <>
      <form onSubmit={handleSubmit}>
        <div className="px-4 py-5 pb-3 flex flex-col gap-5">
          <Grid container spacing={2}>
            <Grid item xs={6}>
              <TextField
                id="triggerName"
                label="Trigger name"
                variant="outlined"
                value={triggerDetails.triggerName || ""}
                fullWidth
                required
                onChange={(e) => {
                  setTriggerDetails({
                    ...triggerDetails,
                    triggerName: e.target.value,
                  });
                }}
              />
            </Grid>
            <Grid item xs={6}>
              <FormControl required fullWidth>
                <InputLabel id="triggerProvider">Provider</InputLabel>
                <Select
                  labelId="triggerProvider"
                  id="triggerProvider"
                  value={triggerDetails.providerName}
                  label="Provider"
                  onChange={(e) => {
                    setTriggerDetails({
                      ...triggerDetails,
                      providerName: e.target.value,
                    });
                  }}
                >
                  {channelsList
                    .filter((item) => item.value != CONSTANTS.PROVIDERS.SLACK)
                    .map((item) => (
                      <MenuItem
                        value={item.value}
                        key={"Channel_Option_" + item.value}
                        disabled={item.comingSoon}
                      >
                        {item.name}{" "}
                        {item.comingSoon && (
                          <Typography
                            variant="body1"
                            paddingX={2}
                            fontStyle={"italic"}
                          >
                            Comming soon!
                          </Typography>
                        )}
                      </MenuItem>
                    ))}
                </Select>
              </FormControl>
            </Grid>
          </Grid>
          {triggerDetails.providerName === CONSTANTS.PROVIDERS.GHL_CHANNEL && (
            <GhlTriggerOptions
              data={triggerDetails.data as IGhlTrigger}
              setData={(data: IGhlTrigger) =>
                setTriggerDetails({ ...triggerDetails, data })
              }
              followUpDetails={triggerDetails.followUp}
              setFollowUpDetails={(data: IFollowupDetails) =>
                setTriggerDetails({ ...triggerDetails, followUp: data })
              }
            />
          )}
          {triggerDetails.providerName ===
            CONSTANTS.PROVIDERS.CHATHQ_CHANNEL && (
            <ChatHqTriggerOptions
              data={triggerDetails.data as IChatHqTrigger}
              setData={(data: IChatHqTrigger) =>
                setTriggerDetails({ ...triggerDetails, data })
              }
            />
          )}
          {triggerDetails.providerName === CONSTANTS.PROVIDERS.PODIUM && (
            <PodiumTriggerOptions
              data={triggerDetails.data as IPodiumTrigger}
              setData={(data: IPodiumTrigger) =>
                setTriggerDetails({ ...triggerDetails, data })
              }
            />
          )}
          {triggerDetails.providerName === CONSTANTS.PROVIDERS.HUBSPOT && (
            <HubspotTriggerOptions
              data={triggerDetails.data as IHubspotTrigger}
              setData={(data: IHubspotTrigger) =>
                setTriggerDetails({ ...triggerDetails, data })
              }
            />
          )}
          <div className="flex justify-end gap-5">
            <Button variant="text" onClick={addBtnFn}>
              Cancel
            </Button>
            <Button
              variant="contained"
              startIcon={<AiFillSave />}
              type="submit"
              disabled={loading}
            >
              Save {loading && <ButtonSpinner />}
            </Button>
          </div>
        </div>
        <Line />
      </form>
    </>
  );
};

export default AddTrigger;
