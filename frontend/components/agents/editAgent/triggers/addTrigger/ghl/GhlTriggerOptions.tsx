import React, { useEffect, useState } from "react";
import {
  FormControl,
  Grid,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  Collapse,
  Button,
  InputAdornment,
  Autocomplete,
  SelectChangeEvent,
  Typography,
  Chip,
  Box,
} from "@mui/material";
import {
  Add as AddIcon,
  InsertDriveFile as InsertDriveFileIcon,
} from "@mui/icons-material";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store";
import { getChannelAccounts } from "@/requests/channels/getChannelAccounts";
import {
  IFollowupDetails,
  IGhlTrigger,
  ITagCondition,
  ISingleAction,
  ProcessedFile,
} from "@/slices/agents/agentsSlice";
import { TagConditionBlock } from "../../TagConditionBlock";
import FollowupDetails from "../../FollowupDetails";
import { logos } from "@/helpers/images";
import { getFileTypeInfo } from "../../../knowledge/FileStatus";

interface IProp {
  data: IGhlTrigger;
  setData: (data: IGhlTrigger) => void;
  followUpDetails?: IFollowupDetails;
  setFollowUpDetails: (data: IFollowupDetails) => void;
}

interface IPrompt {
  promptId: string;
  name: string;
  promptContent: string;
}

const GhlTriggerOptions: React.FC<IProp> = ({
  data,
  setData,
  followUpDetails,
  setFollowUpDetails,
}) => {
  const dispatch = useDispatch();
  const {
    currentAgent: {
      prompts: { prompt },
      actions,
      processedFiles: files,
    },
  } = useSelector((state: RootState) => state.agents);
  const [showMore, setShowMore] = useState(false);
  const [accountList, setAccountList] = useState<
    {
      accountId: string;
      name: string;
      providerName: string;
    }[]
  >([]);
  const [prompts, setPrompts] = useState<IPrompt[]>(prompt || []);
  const [selectedIncludeActions, setSelectedIncludeActions] = useState<
    ISingleAction[]
  >([]);
  const [selectedExcludeActions, setSelectedExcludeActions] = useState<
    ISingleAction[]
  >([]);
  const [selectedIncludeKnowledge, setSelectedIncludeKnowledge] = useState<
    ProcessedFile[]
  >([]);
  const [selectedExcludeKnowledge, setSelectedExcludeKnowledge] = useState<
    ProcessedFile[]
  >([]);

  useEffect(() => {
    const fetchData = async () => {
      const response = await getChannelAccounts(
        {
          orgId: localStorage.getItem("orgId") as string,
          provider: "ghl",
        },
        dispatch
      );
      if (response?.status === 200) {
        setAccountList(response.data.channels);
      }
    };
    fetchData();
  }, []);

  // Initialize selected actions from comma-separated IDs
  useEffect(() => {
    if (data.include && actions.length > 0) {
      const includeIds = data.include.split(",").filter((id) => id);
      const includedActions = actions.filter(
        (action) => action.actionId && includeIds.includes(action.actionId)
      );
      setSelectedIncludeActions(includedActions);
    }

    if (data.exclude && actions.length > 0) {
      const excludeIds = data.exclude.split(",").filter((id) => id);
      const excludedActions = actions.filter(
        (action) => action.actionId && excludeIds.includes(action.actionId)
      );
      setSelectedExcludeActions(excludedActions);
    }

    if (data.include_knowledge && files.length > 0) {
      const includeIds = data.include_knowledge.split(",").filter((id) => id);
      const includedFiles = files.filter(
        (file) => file.accountId && includeIds.includes(file.accountId)
      );
      setSelectedIncludeKnowledge(includedFiles);
    }

    if (data.exclude_knowledge && files.length > 0) {
      const excludeIds = data.exclude_knowledge.split(",").filter((id) => id);
      const excludedFiles = files.filter(
        (file) => file.accountId && excludeIds.includes(file.accountId)
      );
      setSelectedExcludeKnowledge(excludedFiles);
    }
  }, [
    data.include,
    data.exclude,
    data.include_knowledge,
    data.exclude_knowledge,
    actions,
    files,
  ]);

  const handleGetDatasourceImgSrc = (providerName: string) => {
    if (!providerName) return "";
    return "/squareLogos" + logos[providerName]?.logo || "";
  };

  const handleSelectChange =
    (field: keyof Omit<IGhlTrigger, "tagConditions">) =>
    (event: SelectChangeEvent) => {
      setData({
        ...data,
        [field]: event.target.value,
      });
    };

  const handleTextChange =
    (field: keyof Omit<IGhlTrigger, "tagConditions">) =>
    (event: React.ChangeEvent<HTMLInputElement>) => {
      // For comma-separated ID fields, remove any whitespace
      if (
        field === "include" ||
        field === "exclude" ||
        field === "include_knowledge" ||
        field === "exclude_knowledge"
      ) {
        setData({
          ...data,
          [field]: event.target.value
            .split(",")
            .map((item) => item.trim())
            .join(","),
        });
      } else {
        setData({
          ...data,
          [field]: event.target.value,
        });
      }
    };

  const handleAddTagCondition = () => {
    setData({
      ...data,
      tagConditions: [...data.tagConditions, { tagOption: "", tagValue: "" }],
    });
  };

  const handleUpdateTagCondition =
    (index: number) => (updatedCondition: ITagCondition) => {
      const newTagConditions = [...data.tagConditions];
      newTagConditions[index] = updatedCondition;
      setData({
        ...data,
        tagConditions: newTagConditions,
      });
    };

  const handleDeleteTagCondition = (index: number) => () => {
    const newTagConditions = data.tagConditions.filter((_, i) => i !== index);
    setData({
      ...data,
      tagConditions: newTagConditions,
    });
  };

  const handleFollowupDetailsChange = (updatedDetails: IFollowupDetails) => {
    setFollowUpDetails(updatedDetails);
  };

  // Handle action selection changes
  const handleIncludeActionsChange = (
    event: React.SyntheticEvent,
    newValue: ISingleAction[]
  ) => {
    setSelectedIncludeActions(newValue);
    const actionIds = newValue
      .map((action) => action.actionId)
      .filter((id) => id !== undefined) // Filter out undefined
      .join(",");
    setData({
      ...data,
      include: actionIds,
    });
  };

  const handleExcludeActionsChange = (
    event: React.SyntheticEvent,
    newValue: ISingleAction[]
  ) => {
    setSelectedExcludeActions(newValue);
    const actionIds = newValue
      .map((action) => action.actionId)
      .filter((id) => id !== undefined) // Filter out undefined
      .join(",");
    setData({
      ...data,
      exclude: actionIds,
    });
  };

  // Handle knowledge selection changes
  const handleIncludeKnowledgeChange = (
    event: React.SyntheticEvent,
    newValue: ProcessedFile[]
  ) => {
    setSelectedIncludeKnowledge(newValue);
    const knowledgeIds = newValue
      .map((file) => file.accountId)
      .filter((id) => id !== undefined) // Filter out undefined
      .join(",");
    setData({
      ...data,
      include_knowledge: knowledgeIds,
    });
  };

  const handleExcludeKnowledgeChange = (
    event: React.SyntheticEvent,
    newValue: ProcessedFile[]
  ) => {
    setSelectedExcludeKnowledge(newValue);
    const knowledgeIds = newValue
      .map((file) => file.accountId)
      .filter((id) => id !== undefined) // Filter out undefined
      .join(",");
    setData({
      ...data,
      exclude_knowledge: knowledgeIds,
    });
  };

  // Get display text for an action
  const getActionDisplayText = (action: ISingleAction) => {
    const providerName = action.providerName || "Unknown Provider";
    const accountName = action.accountName
      ? `${action.accountName}`
      : "No name";
    const activityName = action.activity || "";

    return `${providerName} - ${accountName} - ${activityName}`;
  };

  // Format file size in a readable way
  const formatCharacterCount = (count: number) => {
    if (count < 1000) return `${count} chars`;
    if (count < 1000000) return `${(count / 1000).toFixed(1)}K chars`;
    return `${(count / 1000000).toFixed(1)}M chars`;
  };

  return (
    <Grid container spacing={2}>
      <Grid item xs={6}>
        <FormControl fullWidth required>
          <InputLabel>Subaccount</InputLabel>
          <Select
            value={String(data.subaccount || "")}
            label="Subaccount"
            onChange={handleSelectChange("subaccount")}
          >
            {accountList.map((item) => (
              <MenuItem
                value={item.accountId}
                key={`subaccount${item.accountId}`}
              >
                {item.name}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Grid>
      <Grid item xs={6}>
        <FormControl fullWidth required>
          <InputLabel>Channel</InputLabel>
          <Select
            value={String(data.channel || "")}
            label="Channel"
            onChange={handleSelectChange("channel")}
          >
            {[
              { name: "SMS", value: "SMS" },
              { name: "Google My Business", value: "GMB" },
              { name: "Facebook", value: "FB" },
              { name: "Instagram", value: "IG" },
              { name: "Live Chat", value: "Live_Chat" },
              { name: "WhatsApp", value: "WhatsApp" },
            ].map((item) => (
              <MenuItem value={item.value} key={`channel${item.value}`}>
                {item.name}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Grid>
      <Grid item xs={6}>
        <Autocomplete
          options={prompts}
          getOptionLabel={(option) => option.name}
          value={prompts.find((p) => p.promptId === data.prompt) || null}
          onChange={(_, newValue) => {
            setData({
              ...data,
              prompt: newValue?.promptId || "",
            });
          }}
          renderInput={(params) => (
            <TextField {...params} label="Select Prompt" fullWidth />
          )}
        />
      </Grid>

      <Grid item xs={12}>
        <Typography variant="subtitle1" gutterBottom>
          Tag Conditions (AND)
        </Typography>
        <Grid container spacing={2}>
          {data.tagConditions?.map((condition, index) => (
            <Grid item xs={12} key={index}>
              <TagConditionBlock
                condition={condition}
                onUpdate={handleUpdateTagCondition(index)}
                onDelete={handleDeleteTagCondition(index)}
                showDelete={data.tagConditions.length > 1}
              />
              {index < data.tagConditions.length - 1 && (
                <Typography
                  align="left"
                  color="textSecondary"
                  variant="h6"
                  sx={{ mt: 2, mb: 1 }}
                >
                  AND
                </Typography>
              )}
            </Grid>
          ))}
          <Grid item xs={12}>
            <Button
              startIcon={<AddIcon />}
              onClick={handleAddTagCondition}
              variant="outlined"
              size="small"
            >
              Add Tag Condition
            </Button>
          </Grid>
        </Grid>
      </Grid>

      <Grid item xs={12}>
        <FollowupDetails
          followupDetails={followUpDetails}
          onFollowupChange={handleFollowupDetailsChange}
        />
      </Grid>

      <Grid item xs={12}>
        <Button onClick={() => setShowMore(!showMore)}>
          {showMore ? "Show less" : "Show more"}
        </Button>
      </Grid>
      <Grid item xs={12}>
        <Collapse in={showMore}>
          <Grid container spacing={2}>
            <Grid item xs={6}>
              <FormControl fullWidth required>
                <InputLabel>Task type</InputLabel>
                <Select
                  value={String(data.task || "")}
                  label="Task type"
                  onChange={handleSelectChange("task")}
                >
                  {[
                    { name: "Response", value: "respond" },
                    // { name: "Outreach", value: "outreach" },
                    { name: "Evaluate", value: "evaluate" },
                  ].map((item) => (
                    <MenuItem
                      value={item.value}
                      key={`taskOption${item.value}`}
                    >
                      {item.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={6}>
              <TextField
                label="History"
                type="number"
                value={data.history || ""}
                onChange={handleTextChange("history")}
                fullWidth
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">hours</InputAdornment>
                  ),
                }}
                helperText="Number of hours in history to fetch data from"
              />
            </Grid>
            <Grid item xs={6}>
              <Autocomplete
                multiple
                options={actions}
                value={selectedIncludeActions}
                onChange={handleIncludeActionsChange}
                getOptionLabel={(option) => getActionDisplayText(option)}
                isOptionEqualToValue={(option, value) =>
                  option.actionId === value.actionId
                }
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label="Include Actions"
                    fullWidth
                    helperText="Select actions to include"
                  />
                )}
                renderOption={(props, option) => (
                  <li {...props}>
                    <Grid container direction="column" spacing={1}>
                      <Grid item container alignItems="center" spacing={1}>
                        <Grid item>
                          {option.providerName && (
                            <Box
                              component="img"
                              src={handleGetDatasourceImgSrc(
                                option.providerName
                              )}
                              alt={option.providerName}
                              sx={{
                                width: 24,
                                height: 24,
                                borderRadius: "4px",
                                objectFit: "contain",
                                mr: 1,
                                backgroundColor: "background.paper",
                                border: "1px solid",
                                borderColor: "divider",
                              }}
                              onError={(e) => {
                                (e.target as HTMLImageElement).style.display =
                                  "none";
                              }}
                            />
                          )}
                        </Grid>
                        <Grid item xs>
                          <Typography
                            variant="body1"
                            noWrap
                            sx={{
                              overflow: "hidden",
                              textOverflow: "ellipsis",
                              maxWidth: "100%",
                            }}
                          >
                            {option.providerName || "Unknown Provider"}
                            {option.accountName
                              ? ` - ${option.accountName}`
                              : " - No name"}
                          </Typography>
                        </Grid>
                        <Grid item>
                          <Chip
                            label={option.activity || "No activity"}
                            size="small"
                            color="primary"
                            variant="outlined"
                            sx={{ fontWeight: "medium" }}
                          />
                        </Grid>
                      </Grid>
                      <Grid item>
                        <Typography variant="caption" color="text.secondary">
                          ID: {option.actionId || "No ID"}
                        </Typography>
                      </Grid>
                      <Grid item>
                        <Typography
                          variant="body2"
                          color="text.secondary"
                          sx={{
                            display: "-webkit-box",
                            WebkitLineClamp: 2,
                            WebkitBoxOrient: "vertical",
                            overflow: "hidden",
                            textOverflow: "ellipsis",
                            borderLeft: "2px solid",
                            borderColor: "divider",
                            pl: 1,
                          }}
                        >
                          {option.promptContent || "No prompt content"}
                        </Typography>
                      </Grid>
                    </Grid>
                  </li>
                )}
                renderTags={(tagValue, getTagProps) =>
                  tagValue.map((option, index) => (
                    <Chip
                      avatar={
                        option.providerName ? (
                          <Box
                            component="img"
                            src={handleGetDatasourceImgSrc(option.providerName)}
                            alt={option.providerName}
                            sx={{
                              width: 24,
                              height: 24,
                              borderRadius: "4px",
                              objectFit: "contain",
                            }}
                            onError={(e) => {
                              (e.target as HTMLImageElement).style.display =
                                "none";
                            }}
                          />
                        ) : undefined
                      }
                      label={`${
                        option.accountName ? `${option.accountName}` : ""
                      } ${option.activity ? `- ${option.activity}` : ""}`}
                      {...getTagProps({ index })}
                      key={option.actionId}
                      sx={{
                        "& .MuiChip-avatar": {
                          width: 24,
                          height: 24,
                          ml: "5px",
                        },
                        "& .MuiChip-label": {
                          overflow: "hidden",
                          textOverflow: "ellipsis",
                          maxWidth: { xs: "120px", sm: "160px", md: "200px" },
                        },
                      }}
                    />
                  ))
                }
              />
            </Grid>
            <Grid item xs={6}>
              <Autocomplete
                multiple
                options={actions}
                value={selectedExcludeActions}
                onChange={handleExcludeActionsChange}
                getOptionLabel={(option) => getActionDisplayText(option)}
                isOptionEqualToValue={(option, value) =>
                  option.actionId === value.actionId
                }
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label="Exclude Actions"
                    fullWidth
                    helperText="Select actions to exclude"
                  />
                )}
                renderOption={(props, option) => (
                  <li {...props}>
                    <Grid container direction="column" spacing={1}>
                      <Grid item container alignItems="center" spacing={1}>
                        <Grid item>
                          {option.providerName && (
                            <Box
                              component="img"
                              src={handleGetDatasourceImgSrc(
                                option.providerName
                              )}
                              alt={option.providerName}
                              sx={{
                                width: 24,
                                height: 24,
                                borderRadius: "4px",
                                objectFit: "contain",
                                mr: 1,
                                backgroundColor: "background.paper",
                                border: "1px solid",
                                borderColor: "divider",
                              }}
                              onError={(e) => {
                                (e.target as HTMLImageElement).style.display =
                                  "none";
                              }}
                            />
                          )}
                        </Grid>
                        <Grid item xs>
                          <Typography
                            variant="body1"
                            noWrap
                            sx={{
                              overflow: "hidden",
                              textOverflow: "ellipsis",
                              maxWidth: "100%",
                            }}
                          >
                            {option.providerName || "Unknown Provider"}
                            {option.accountName
                              ? ` - ${option.accountName}`
                              : " - No name"}
                          </Typography>
                        </Grid>
                        <Grid item>
                          <Chip
                            label={option.activity || "No activity"}
                            size="small"
                            color="primary"
                            variant="outlined"
                            sx={{ fontWeight: "medium" }}
                          />
                        </Grid>
                      </Grid>
                      <Grid item>
                        <Typography variant="caption" color="text.secondary">
                          ID: {option.actionId || "No ID"}
                        </Typography>
                      </Grid>
                      <Grid item>
                        <Typography
                          variant="body2"
                          color="text.secondary"
                          sx={{
                            display: "-webkit-box",
                            WebkitLineClamp: 2,
                            WebkitBoxOrient: "vertical",
                            overflow: "hidden",
                            textOverflow: "ellipsis",
                            borderLeft: "2px solid",
                            borderColor: "divider",
                            pl: 1,
                          }}
                        >
                          {option.promptContent || "No prompt content"}
                        </Typography>
                      </Grid>
                    </Grid>
                  </li>
                )}
                renderTags={(tagValue, getTagProps) =>
                  tagValue.map((option, index) => (
                    <Chip
                      avatar={
                        option.providerName ? (
                          <Box
                            component="img"
                            src={handleGetDatasourceImgSrc(option.providerName)}
                            alt={option.providerName}
                            sx={{
                              width: 24,
                              height: 24,
                              borderRadius: "4px",
                              objectFit: "contain",
                            }}
                            onError={(e) => {
                              (e.target as HTMLImageElement).style.display =
                                "none";
                            }}
                          />
                        ) : undefined
                      }
                      label={`${
                        option.accountName ? `${option.accountName}` : ""
                      } ${option.activity ? `- ${option.activity}` : ""}`}
                      {...getTagProps({ index })}
                      key={option.actionId}
                      sx={{
                        "& .MuiChip-avatar": {
                          width: 24,
                          height: 24,
                          ml: "5px",
                        },
                        "& .MuiChip-label": {
                          overflow: "hidden",
                          textOverflow: "ellipsis",
                          maxWidth: { xs: "120px", sm: "160px", md: "200px" },
                        },
                      }}
                    />
                  ))
                }
              />
            </Grid>
            <Grid item xs={6}>
              <Autocomplete
                multiple
                options={files || []}
                value={selectedIncludeKnowledge}
                onChange={handleIncludeKnowledgeChange}
                getOptionLabel={(option) => option.fileName || "Unnamed file"}
                isOptionEqualToValue={(option, value) =>
                  option.accountId === value.accountId
                }
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label="Include Knowledge"
                    fullWidth
                    helperText="Select knowledge files to include"
                  />
                )}
                renderOption={(props, option) => {
                  const fileTypeInfo = option.mimeType
                    ? getFileTypeInfo(option.mimeType)
                    : { label: "File", icon: <InsertDriveFileIcon /> };

                  return (
                    <li {...props}>
                      <Grid container direction="column" spacing={1}>
                        <Grid item container alignItems="center" spacing={1}>
                          <Grid item>
                            <Box
                              component="div"
                              sx={{
                                display: "flex",
                                alignItems: "center",
                                backgroundColor: "action.hover",
                                padding: "4px 8px",
                                borderRadius: 1,
                                gap: 1,
                              }}
                            >
                              {React.cloneElement(fileTypeInfo.icon, {
                                fontSize: "small",
                                sx: { color: "primary.main" },
                              })}
                              <Typography
                                variant="caption"
                                color="text.primary"
                                fontWeight="medium"
                              >
                                {formatCharacterCount(
                                  option.characterCount || 0
                                )}
                              </Typography>
                            </Box>
                          </Grid>
                          <Grid item xs>
                            <Typography
                              variant="body1"
                              noWrap
                              sx={{
                                overflow: "hidden",
                                textOverflow: "ellipsis",
                                maxWidth: "100%",
                              }}
                            >
                              {option.fileName || "Unnamed file"}
                              <Typography
                                component="span"
                                variant="caption"
                                color="text.secondary"
                              >
                                {option.mimeType
                                  ? ` (${fileTypeInfo.label})`
                                  : ""}
                              </Typography>
                            </Typography>
                          </Grid>
                        </Grid>
                        <Grid item>
                          <Typography variant="caption" color="text.secondary">
                            ID: {option.accountId || "No ID"}
                          </Typography>
                        </Grid>
                      </Grid>
                    </li>
                  );
                }}
                renderTags={(tagValue, getTagProps) =>
                  tagValue.map((option, index) => {
                    const fileTypeInfo = option.mimeType
                      ? getFileTypeInfo(option.mimeType)
                      : { label: "File", icon: <InsertDriveFileIcon /> };

                    return (
                      <Chip
                        icon={
                          <Box
                            sx={{
                              display: "flex",
                              alignItems: "center",
                              ml: "5px",
                            }}
                          >
                            {React.cloneElement(fileTypeInfo.icon, {
                              fontSize: "small",
                            })}
                          </Box>
                        }
                        label={`${option.fileName || "Unnamed file"}`}
                        {...getTagProps({ index })}
                        key={option.accountId}
                        sx={{
                          "& .MuiChip-icon": {
                            color: "primary.main",
                          },
                          "& .MuiChip-label": {
                            overflow: "hidden",
                            textOverflow: "ellipsis",
                            maxWidth: { xs: "120px", sm: "160px", md: "200px" },
                          },
                        }}
                      />
                    );
                  })
                }
              />
            </Grid>
            <Grid item xs={6}>
              <Autocomplete
                multiple
                options={files || []}
                value={selectedExcludeKnowledge}
                onChange={handleExcludeKnowledgeChange}
                getOptionLabel={(option) => option.fileName || "Unnamed file"}
                isOptionEqualToValue={(option, value) =>
                  option.accountId === value.accountId
                }
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label="Exclude Knowledge"
                    fullWidth
                    helperText="Select knowledge files to exclude"
                  />
                )}
                renderOption={(props, option) => {
                  const fileTypeInfo = option.mimeType
                    ? getFileTypeInfo(option.mimeType)
                    : { label: "File", icon: <InsertDriveFileIcon /> };

                  return (
                    <li {...props}>
                      <Grid container direction="column" spacing={1}>
                        <Grid item container alignItems="center" spacing={1}>
                          <Grid item>
                            <Box
                              component="div"
                              sx={{
                                display: "flex",
                                alignItems: "center",
                                backgroundColor: "action.hover",
                                padding: "4px 8px",
                                borderRadius: 1,
                                gap: 1,
                              }}
                            >
                              {React.cloneElement(fileTypeInfo.icon, {
                                fontSize: "small",
                                sx: { color: "primary.main" },
                              })}
                              <Typography
                                variant="caption"
                                color="text.primary"
                                fontWeight="medium"
                              >
                                {formatCharacterCount(
                                  option.characterCount || 0
                                )}
                              </Typography>
                            </Box>
                          </Grid>
                          <Grid item xs>
                            <Typography
                              variant="body1"
                              noWrap
                              sx={{
                                overflow: "hidden",
                                textOverflow: "ellipsis",
                                maxWidth: "100%",
                              }}
                            >
                              {option.fileName || "Unnamed file"}
                              <Typography
                                component="span"
                                variant="caption"
                                color="text.secondary"
                              >
                                {option.mimeType
                                  ? ` (${fileTypeInfo.label})`
                                  : ""}
                              </Typography>
                            </Typography>
                          </Grid>
                        </Grid>
                        <Grid item>
                          <Typography variant="caption" color="text.secondary">
                            ID: {option.accountId || "No ID"}
                          </Typography>
                        </Grid>
                      </Grid>
                    </li>
                  );
                }}
                renderTags={(tagValue, getTagProps) =>
                  tagValue.map((option, index) => {
                    const fileTypeInfo = option.mimeType
                      ? getFileTypeInfo(option.mimeType)
                      : { label: "File", icon: <InsertDriveFileIcon /> };

                    return (
                      <Chip
                        icon={
                          <Box
                            sx={{
                              display: "flex",
                              alignItems: "center",
                              ml: "5px",
                            }}
                          >
                            {React.cloneElement(fileTypeInfo.icon, {
                              fontSize: "small",
                            })}
                          </Box>
                        }
                        label={`${option.fileName || "Unnamed file"}`}
                        {...getTagProps({ index })}
                        key={option.accountId}
                        sx={{
                          "& .MuiChip-icon": {
                            color: "primary.main",
                          },
                          "& .MuiChip-label": {
                            overflow: "hidden",
                            textOverflow: "ellipsis",
                            maxWidth: { xs: "120px", sm: "160px", md: "200px" },
                          },
                        }}
                      />
                    );
                  })
                }
              />
            </Grid>
          </Grid>
        </Collapse>
      </Grid>
    </Grid>
  );
};

export default GhlTriggerOptions;
