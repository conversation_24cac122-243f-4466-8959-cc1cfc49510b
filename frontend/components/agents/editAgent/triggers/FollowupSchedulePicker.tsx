import {
  Box,
  Checkbox,
  FormControl,
  FormControlLabel,
  IconButton,
  <PERSON>ack,
  Typo<PERSON>,
  styled,
  Button,
} from "@mui/material";
import React from "react";
import DeleteIcon from "@mui/icons-material/Delete";
import AddIcon from "@mui/icons-material/Add";
import { capitalizeFirstLetter } from "@/helpers/basic";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { TimePicker } from "@mui/x-date-pickers/TimePicker";
import dayjs from "dayjs";
import { Schedule, TimeRange } from "@/slices/agents/agentsSlice";

interface FollowupSchedulePickerProps {
  schedule: Schedule;
  onScheduleChange: (schedule: Schedule) => void;
}

interface TimeSlot {
  start: string;
  end: string;
}

const HighlightedBox = styled(Box)(({ theme }) => ({
  backgroundColor: theme.palette.action.selected,
  borderRadius: theme.shape.borderRadius,
}));

const defaultSchedule: Schedule = {
  monday: [],
  tuesday: [],
  wednesday: [],
  thursday: [],
  friday: [],
  saturday: [],
  sunday: [],
};

const FollowupSchedulePicker: React.FC<FollowupSchedulePickerProps> = ({
  schedule = defaultSchedule,
  onScheduleChange,
}) => {
  const defaultTimeSlot: TimeRange = {
    start: "09:00",
    end: "17:00",
  };

  const daysOfWeek = [
    "monday",
    "tuesday",
    "wednesday",
    "thursday",
    "friday",
    "saturday",
    "sunday",
  ] as const;

  const handleCheckboxChange = (
    event: React.ChangeEvent<HTMLInputElement>,
    day: keyof Schedule
  ) => {
    if (event.target.checked) {
      const updatedSchedule = {
        ...schedule,
        [day]: [{ ...defaultTimeSlot }],
      };
      onScheduleChange(updatedSchedule);
    } else {
      const updatedSchedule = {
        ...schedule,
        [day]: [],
      };
      onScheduleChange(updatedSchedule);
    }
  };

  const addTimeSlot = (day: keyof Schedule) => {
    const updatedSchedule = {
      ...schedule,
      [day]: [...schedule[day], { ...defaultTimeSlot }],
    };
    onScheduleChange(updatedSchedule);
  };

  const removeTimeSlot = (day: keyof Schedule, slotIndex: number) => {
    const updatedSchedule = {
      ...schedule,
      [day]: schedule[day].filter((_, index) => index !== slotIndex),
    };
    onScheduleChange(updatedSchedule);
  };

  const updateTimeSlot = (
    day: keyof Schedule,
    slotIndex: number,
    field: keyof TimeRange,
    value: string
  ) => {
    const updatedSchedule = {
      ...schedule,
      [day]: schedule[day].map((slot, idx) =>
        idx === slotIndex ? { ...slot, [field]: value } : slot
      ),
    };
    onScheduleChange(updatedSchedule);
  };

  return (
    <HighlightedBox sx={{ p: 2 }}>
      <Typography variant="h6" gutterBottom>
        Follow-up Schedule
      </Typography>
      <Typography variant="body2" color="text.secondary" gutterBottom>
        Set the times when follow-ups can be sent. Follow-ups will only be sent
        during these time windows.
      </Typography>
      <Stack direction="column" spacing={2} sx={{ mt: 2 }}>
        {daysOfWeek.map((day) => {
          const daySchedule = schedule[day];
          const isChecked = daySchedule.length > 0;

          return (
            <Stack
              direction="column"
              spacing={2}
              key={day}
              sx={{ borderBottom: 1, borderColor: "divider", pb: 2 }}
            >
              <Stack
                direction="row"
                alignItems="center"
                justifyContent="space-between"
              >
                <FormControl sx={{ width: "120px" }}>
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={isChecked}
                        onChange={(e) => handleCheckboxChange(e, day)}
                      />
                    }
                    label={capitalizeFirstLetter(day).slice(0, 3).toUpperCase()}
                  />
                </FormControl>
                {isChecked && (
                  <Button
                    startIcon={<AddIcon />}
                    size="small"
                    onClick={() => addTimeSlot(day)}
                    variant="outlined"
                  >
                    Add Time Slot
                  </Button>
                )}
              </Stack>

              {!isChecked && (
                <Typography variant="body2" color="text.secondary">
                  No follow-ups on this day
                </Typography>
              )}

              {daySchedule.map((timeSlot, slotIndex) => (
                <Stack
                  key={slotIndex}
                  direction="row"
                  alignItems="center"
                  spacing={2}
                  sx={{ ml: 4 }}
                >
                  <LocalizationProvider dateAdapter={AdapterDayjs}>
                    <TimePicker
                      value={dayjs(`2022-04-17T${timeSlot.start}`)}
                      onChange={(newValue) =>
                        updateTimeSlot(
                          day,
                          slotIndex,
                          "start",
                          newValue?.format("HH:mm") || timeSlot.start
                        )
                      }
                      slotProps={{ textField: { size: "small" } }}
                    />
                    <Typography>to</Typography>
                    <TimePicker
                      value={dayjs(`2022-04-17T${timeSlot.end}`)}
                      onChange={(newValue) =>
                        updateTimeSlot(
                          day,
                          slotIndex,
                          "end",
                          newValue?.format("HH:mm") || timeSlot.end
                        )
                      }
                      slotProps={{ textField: { size: "small" } }}
                    />
                  </LocalizationProvider>
                  <IconButton
                    size="small"
                    onClick={() => removeTimeSlot(day, slotIndex)}
                    sx={{
                      visibility: daySchedule.length > 1 ? "visible" : "hidden",
                    }}
                  >
                    <DeleteIcon fontSize="small" />
                  </IconButton>
                </Stack>
              ))}
            </Stack>
          );
        })}
      </Stack>
    </HighlightedBox>
  );
};

export default FollowupSchedulePicker;
