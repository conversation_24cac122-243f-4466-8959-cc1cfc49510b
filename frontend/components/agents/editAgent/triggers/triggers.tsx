import Line from "@/components/general/shapes/horizontalLine";
import SubHeading from "@/components/settings/crm_ai/subHeading";
import React, { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { RootState } from "@/store";
import AddTrigger from "./addTrigger/AddTrigger";
import SingleTrigger from "./SingleTrigger";
import { ITrigger } from "@/slices/agents/agentsSlice";
import { Alert, AlertTitle, Link, Typography } from "@mui/material";
import LoomDialog from "@/components/general/LoomDialog";
const Triggers = () => {
  const triggers = useSelector(
    (state: RootState) => state.agents.currentAgent?.triggers
  ) as ITrigger[];

  const [add, setAdd] = useState(false);
  const handleAdd = () => {
    setAdd(!add);
  };
  return (
    <>
      {triggers &&
        triggers.filter((trigger) => trigger.active).length === 0 && (
          <Alert severity="warning">
            <AlertTitle>No active trigger</AlertTitle>
            Triggers are used to generate responses for the agent. Read more{" "}
            <Link
              href="https://help.capriai.us/trigger-design/creating-triggers"
              target="_blank"
            >
              here
            </Link>
          </Alert>
        )}
      <div className="border-[1px] border-x-slate-300 rounded-md bg-gray-100 my-5">
        <SubHeading
          value="Triggers"
          addBtn={add}
          addBtnFn={handleAdd}
          additionalDescription={
            <Typography variant="caption">
              Triggers allow you to connect your agent to an external CRM. Know
              more{" "}
              <LoomDialog
                triggerComponent={<Link>here</Link>}
                title="Triggers"
                videoUrl="https://www.loom.com/share/2a8dacd9069f473c8672f2de2f49c3f6"
              />
            </Typography>
          }
        />
        <Line />
        {add && <AddTrigger addBtnFn={handleAdd} type="add" />}
        {triggers?.map((trigger, index) => (
          <div className="" key={"Single_prompt_div" + trigger.triggerId}>
            <SingleTrigger data={trigger} />
            {index !== triggers.length - 1 && <Line />}
          </div>
        ))}
      </div>
    </>
  );
};

export default Triggers;
