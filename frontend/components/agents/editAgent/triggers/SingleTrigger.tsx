import { logos } from "@/helpers/images";
import { ITrigger, agentsAction } from "@/slices/agents/agentsSlice";
import {
  <PERSON><PERSON>,
  <PERSON>,
  FormControlLabel,
  IconButton,
  Stack,
  Switch,
  SwitchProps,
} from "@mui/material";
import Image from "next/image";
import React, { useEffect, useState } from "react";
import { AiFillEdit } from "react-icons/ai";
import AddTrigger from "./addTrigger/AddTrigger";
import { BsFillTrashFill } from "react-icons/bs";
import GeneralModal from "@/components/general/modal";
import GeneralModalContent from "@/components/general/generalModalContent";
import { useRouter } from "next/router";
import { styled } from "@mui/material/styles";
import { deleteTrigger } from "@/requests/agents/trigger/deleteTrigger";
import { useDispatch } from "react-redux";

interface IProp {
  data: ITrigger;
}

const IOSSwitch = styled((props: SwitchProps) => (
  <Switch focusVisibleClassName=".Mui-focusVisible" disableRipple {...props} />
))(({ theme }) => ({
  width: 42,
  height: 26,
  padding: 0,
  "& .MuiSwitch-switchBase": {
    padding: 0,
    margin: 2,
    transitionDuration: "300ms",
    "&.Mui-checked": {
      transform: "translateX(16px)",
      color: "#fff",
      "& + .MuiSwitch-track": {
        backgroundColor: theme.palette.mode === "dark" ? "#2ECA45" : "#65C466",
        opacity: 1,
        border: 0,
      },
      "&.Mui-disabled + .MuiSwitch-track": {
        opacity: 0.5,
      },
    },
    "&.Mui-focusVisible .MuiSwitch-thumb": {
      color: "#33cf4d",
      border: "6px solid #fff",
    },
    "&.Mui-disabled .MuiSwitch-thumb": {
      color:
        theme.palette.mode === "light"
          ? theme.palette.grey[100]
          : theme.palette.grey[600],
    },
    "&.Mui-disabled + .MuiSwitch-track": {
      opacity: theme.palette.mode === "light" ? 0.7 : 0.3,
    },
  },
  "& .MuiSwitch-thumb": {
    boxSizing: "border-box",
    width: 22,
    height: 22,
  },
  "& .MuiSwitch-track": {
    borderRadius: 26 / 2,
    backgroundColor: theme.palette.mode === "light" ? "#E9E9EA" : "#39393D",
    opacity: 1,
    transition: theme.transitions.create(["background-color"], {
      duration: 500,
    }),
  },
}));

const SingleTrigger: React.FC<IProp> = ({ data }) => {
  const dispatch = useDispatch();
  const router = useRouter();
  const { agentId } = router.query as { agentId: string };
  const [edit, setEdit] = useState(false);
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [activeStatus, setActiveStatus] = useState(Boolean(data?.active));
  useEffect(() => {
    console.log({ activeStatus });
  }, [activeStatus]);
  const toggleEdit = () => {
    setEdit(!edit);
  };
  const handleClose = () => {
    setOpen(false);
  };
  const handleDelete = async () => {
    setLoading(true);
    const body = {
      triggerId: data.triggerId,
      agentId,
    };
    const response = await deleteTrigger(body, dispatch);
    if (response?.status === 200) {
      dispatch(agentsAction.triggerDeleted({ triggerId: body.triggerId }));
    }
    console.log({ body });
    setLoading(false);
  };
  return (
    <>
      <GeneralModal open={open} onClose={handleClose}>
        <GeneralModalContent
          header="Warning"
          content="Are you sure you want to delete this trigger?"
          rightBtn="Delete"
          isLoading={loading}
          onClose={handleClose}
          customFn={handleDelete}
        />
      </GeneralModal>
      <div className="flex items-center justify-between  text-[18px] py-3 px-4">
        <div className="w-2/12 font-semibold">
          {data.triggerName || "Not set"}
        </div>
        {edit && (
          <>
            <Stack direction={"row"} gap={2}>
              <IconButton
                aria-label="delete"
                color="error"
                onClick={() => {
                  setOpen(true);
                }}
              >
                <BsFillTrashFill />
              </IconButton>
              <FormControlLabel
                control={<IOSSwitch sx={{ m: 1 }} />}
                label="Activate"
                checked={activeStatus}
                onChange={() => setActiveStatus(!activeStatus)}
              />
            </Stack>
          </>
        )}
        {!edit && (
          <>
            <div className="w-6/12 font-semibold flex items-center gap-2">
              {data.providerName ? (
                <Image
                  alt="Trigger_logo"
                  src={logos[data.providerName].logo}
                  height={100}
                  width={100}
                  className="h-[25px] w-auto"
                />
              ) : (
                "No provider name"
              )}
            </div>
            <div className="w-2/12 flex justify-end">
              {activeStatus ? (
                <Chip label="Active" variant="filled" color="success" />
              ) : (
                <Chip label="Inactive" />
              )}
            </div>
            <div className="w-2/12 flex justify-end">
              <Button
                variant="outlined"
                startIcon={<AiFillEdit />}
                onClick={toggleEdit}
              >
                Edit
              </Button>
            </div>
          </>
        )}
      </div>
      {edit && (
        <AddTrigger
          addBtnFn={toggleEdit}
          data={{ ...data, active: activeStatus }}
          type="edit"
        />
      )}
    </>
  );
};

export default SingleTrigger;
