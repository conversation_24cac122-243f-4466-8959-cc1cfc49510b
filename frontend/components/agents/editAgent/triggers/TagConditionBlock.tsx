import React from "react";
import {
  Grid,
  FormControl,
  InputLabel,
  Select,
  TextField,
  IconButton,
  MenuItem,
  SelectChangeEvent,
  Paper,
} from "@mui/material";
import { Delete as DeleteIcon } from "@mui/icons-material";
import { ITagCondition } from "@/slices/agents/agentsSlice";

interface ITagConditionBlockProps {
  condition: ITagCondition;
  onUpdate: (updatedCondition: ITagCondition) => void;
  onDelete: () => void;
  showDelete: boolean;
}

export const TagConditionBlock: React.FC<ITagConditionBlockProps> = ({
  condition,
  onUpdate,
  onDelete,
  showDelete,
}) => {
  const handleSelectChange = (event: SelectChangeEvent) => {
    onUpdate({
      ...condition,
      tagOption: event.target.value,
    });
  };

  const handleTextChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    onUpdate({
      ...condition,
      tagValue: event.target.value,
    });
  };

  return (
    <Paper elevation={2} sx={{ p: 2 }}>
      <Grid container spacing={2} alignItems="center">
        <Grid item xs={showDelete ? 5.5 : 6}>
          <FormControl fullWidth required>
            <InputLabel>Tag type</InputLabel>
            <Select
              value={condition.tagOption}
              label="Tag type"
              onChange={handleSelectChange}
            >
              {[
                { name: "HAS TAG", value: "hasTag" },
                { name: "DOES NOT HAVE TAG", value: "doesntHaveTag" },
              ].map((item) => (
                <MenuItem value={item.value} key={`tagOption${item.value}`}>
                  {item.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>
        <Grid item xs={showDelete ? 5.5 : 6}>
          <TextField
            label="Tag value"
            value={condition.tagValue}
            onChange={handleTextChange}
            fullWidth
            required
          />
        </Grid>
        {showDelete && (
          <Grid item xs="auto">
            <IconButton onClick={onDelete} color="error">
              <DeleteIcon />
            </IconButton>
          </Grid>
        )}
      </Grid>
    </Paper>
  );
};
