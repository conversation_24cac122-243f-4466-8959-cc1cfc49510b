import { CONSTANTS } from "@/helpers/constants";

export type ProviderName =
  | typeof CONSTANTS.PROVIDERS.GHL_CHANNEL
  | "ghlCalendar"
  | "googleCalendar"
  | "googleSheet"
  | "website";

interface IActionBasic {
  actionId?: string;
  promptContent: string;
  providerName: ProviderName; // This is our discriminator
  advancedSettings?: boolean;
  isAdvancedSettings?: any;
}

export interface IActionGhlChannel extends IActionBasic {
  providerName: typeof CONSTANTS.PROVIDERS.GHL_CHANNEL; // <-- Notice the specificity here
  activity: "tag" | "customField";
  metaData: {
    tagData?: {
      tagType: "has tag" | "does not have tag";
      tagValue: string;
      evaluateOn: "everyTurn" | "contactNoTag";
      reply: boolean;
    };
    customFieldData?: {
      fieldKey: string;
      type: "String" | "number" | "list";
      evaluateOn: "everyTurn" | "isEmpty";
    };
  };
}

export interface IAccountAction extends IActionBasic {
  providerName: "ghlCalendar" | "googleCalendar" | "googleSheet" | "website";
  accountName: string;
  activity: "read" | "write";
  silent?: boolean;
  accountId: string;
}

export type TAction = IAccountAction | IActionGhlChannel;
