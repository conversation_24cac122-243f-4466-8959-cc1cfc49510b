import React, { useState } from "react";
import { useRouter } from "next/router";
import {
  <PERSON>ton,
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Typography,
  TextField,
  Box,
  Autocomplete,
  Step,
  Stepper,
  StepLabel,
  Card,
  CardContent,
  CardMedia,
  Grid,
  Paper,
  Alert,
  useTheme,
  ListItem,
  ListItemIcon,
  ListItemText,
  Stack,
  IconButton,
  CircularProgress,
} from "@mui/material";
import { styled } from "@mui/material/styles";
import AddIcon from "@mui/icons-material/Add";
import { logos } from "@/helpers/images";
import StatusBanner from "./StatusBanner";
import useHighLevelChannelConnect from "@/hooks/useHighLevelChannelConnect";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store";
import { addTrigger } from "@/requests/agents/trigger/addTrigger";
import { agentsAction } from "@/slices/agents/agentsSlice";
import CloseIcon from "@mui/icons-material/Close";
import { toggleAgentBanner } from "@/requests/agents/basic/toggleAgentBanner";

// TypeScript interfaces
interface Provider {
  providerName: string;
  name: string;
}

interface Account {
  accountId: string;
  name: string;
  providerName: string;
}

interface AccountOption extends Account {
  isAddNew?: boolean;
}

const StyledDialogContent = styled(DialogContent)(({ theme }) => ({
  minHeight: "400px",
  padding: theme.spacing(3),
}));

const AgentNotificationBanners: React.FC = () => {
  const dispatch = useDispatch();
  const router = useRouter();
  const { agentId } = router.query as { agentId: string };
  const theme = useTheme();
  const { handleConnectClick, dataFromChild } = useHighLevelChannelConnect();
  const [loading, setLoading] = useState(false);

  // data
  const providers: Provider[] = [
    { providerName: "ghl", name: "Highlevel" },
    { providerName: "hubspot", name: "Hubspot" },
    { providerName: "podium", name: "Podium" },
  ];

  const accountList: Account[] =
    useSelector((state: RootState) => state.channelsList.accountsList)?.map(
      (item) => ({
        providerName: item.providerName,
        accountId: item.accountId,
        name: item.name,
      })
    ) || [];

  const triggerList =
    useSelector((state: RootState) => state.agents.currentAgent.triggers) || [];

  // State
  const {
    showNotifications: { connectAgentToTrigger = false },
  } = useSelector((state: RootState) => state.agents.currentAgent);
  const [openDialog, setOpenDialog] = useState(false);
  const [activeStep, setActiveStep] = useState(0);
  const [selectedProvider, setSelectedProvider] = useState<string>("");
  const [triggerName, setTriggerName] = useState<string>("");
  const [selectedAccount, setSelectedAccount] = useState<AccountOption | null>(
    null
  );
  const [showAddAccount, setShowAddAccount] = useState(false);

  // Create account options including "Add new account"
  const accountOptions: AccountOption[] = [
    ...accountList,
    {
      accountId: "new",
      name: "Add a new account",
      providerName: "ghl",
      isAddNew: true,
    },
  ];

  // Step labels
  const steps = ["Select Provider", "Configure Settings"];

  // Handlers
  const handleOpenDialog = () => setOpenDialog(true);

  const handleDismissBanner = () => {
    dispatch(
      agentsAction.toggleNotification({
        propertyName: "connectAgentToTrigger",
        value: false,
      })
    );
  };

  const handleCallDismissBannerApi = async () => {
    handleDismissBanner();
    await toggleAgentBanner(
      { agentId, enabled: false, notificationType: "connectAgentToTrigger" },
      dispatch
    );
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    resetForm();
  };

  const resetForm = () => {
    setActiveStep(0);
    setSelectedProvider("");
    setTriggerName("");
    setSelectedAccount(null);
    setShowAddAccount(false);
  };

  const handleNext = () => {
    setActiveStep((prevStep) => prevStep + 1);
  };

  const handleBack = () => {
    setActiveStep((prevStep) => prevStep - 1);
  };

  const handleProviderSelect = (providerName: string) => {
    if (providerName === "ghl") {
      setSelectedProvider(providerName);
    }
  };

  const handleConnect = async () => {
    setLoading(true);
    const response = await addTrigger(
      {
        active: false,
        agentId,
        providerName: selectedAccount?.providerName as string,
        triggerName,
        data: {
          channel: "SMS",
          task: "respond",
          tagOption: "hasTag",
          tagValue: "capri",
          subaccount: selectedAccount?.accountId as string,
        },
      },
      dispatch
    );
    setLoading(false);
    if (response.status === 201) {
      dispatch(
        agentsAction.ghlTriggerAdded({ response: response.data.trigger })
      );
      handleCallDismissBannerApi();
      handleCloseDialog();
      router.push({
        pathname: router.pathname,
        query: { ...router.query, view: "triggers" },
      });
    }
  };

  const handleSignIn = (provider: string) => {
    if (provider === "ghl") {
      handleConnectClick();
    }
  };

  const handleAccountChange = (
    event: React.SyntheticEvent,
    newValue: AccountOption | null
  ) => {
    setSelectedAccount(newValue);
    if (newValue?.isAddNew) {
      setShowAddAccount(true);
    }
  };

  // Effect to handle successful HighLevel connection
  React.useEffect(() => {
    if (dataFromChild?.providerName) {
      // handleConnect();
      const { accountId, name, providerName } = dataFromChild;
      setSelectedAccount({
        accountId: accountId || "",
        name: name || "",
        providerName,
      });
      setShowAddAccount(false);
    }
  }, [dataFromChild]);

  // Step content
  const getStepContent = (step: number) => {
    switch (step) {
      case 0:
        return (
          <Grid container spacing={3}>
            {providers.map((provider) => (
              <Grid item xs={12} sm={4} key={provider.providerName}>
                <Card
                  onClick={() => handleProviderSelect(provider.providerName)}
                  sx={{
                    cursor: provider.providerName !== "ghl" ? "" : "pointer",
                    height: "100%",
                    transition: "all 0.3s ease",
                    border:
                      selectedProvider === provider.providerName
                        ? `2px solid ${theme.palette.primary.main}`
                        : "2px solid transparent",
                    opacity: provider.providerName !== "ghl" ? 0.5 : 1,
                  }}
                >
                  <CardMedia
                    component="img"
                    height="140"
                    image={logos[provider.providerName]?.logo}
                    alt={provider.name}
                    sx={{ objectFit: "contain", p: 2 }}
                  />
                  <CardContent>
                    <Typography variant="h6" align="center">
                      {provider.name}
                    </Typography>
                    {provider.providerName !== "ghl" && (
                      <Typography
                        variant="caption"
                        color="text.secondary"
                        align="center"
                        display="block"
                      >
                        Coming soon
                      </Typography>
                    )}
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        );
      case 1:
        return (
          <Box sx={{ display: "flex", flexDirection: "column", gap: 3 }}>
            <TextField
              fullWidth
              label="Trigger Name"
              value={triggerName}
              onChange={(e) => setTriggerName(e.target.value)}
              placeholder="Enter trigger name"
              variant="outlined"
            />

            {accountList.length > 0 && !showAddAccount ? (
              <Autocomplete
                options={accountOptions.filter(
                  (item) => item.providerName === selectedProvider
                )}
                getOptionLabel={(option) => option.name}
                value={selectedAccount}
                onChange={handleAccountChange}
                renderOption={(props, option) => (
                  <ListItem {...props}>
                    {option.isAddNew ? (
                      <>
                        <ListItemIcon>
                          <AddIcon />
                        </ListItemIcon>
                        <ListItemText primary={option.name} />
                      </>
                    ) : (
                      <ListItemText primary={option.name} />
                    )}
                  </ListItem>
                )}
                renderInput={(params) => (
                  <TextField {...params} label="Select Subaccount" />
                )}
              />
            ) : (
              <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
                <Alert severity="info">
                  Please sign in to add a new account:
                </Alert>
                <Stack direction={"row"}>
                  <Button
                    variant="outlined"
                    fullWidth
                    onClick={() => handleSignIn(selectedProvider)}
                  >
                    Sign in with{" "}
                    {
                      providers.find((p) => p.providerName === selectedProvider)
                        ?.name
                    }
                  </Button>
                  <IconButton
                    onClick={() => {
                      setSelectedAccount(null);
                      setShowAddAccount(false);
                    }}
                    size="small"
                    title="Close Login button"
                  >
                    <CloseIcon />
                  </IconButton>
                </Stack>
              </Box>
            )}
          </Box>
        );
      default:
        return null;
    }
  };

  return (
    <>
      {connectAgentToTrigger && (
        <Box sx={{ p: 3 }}>
          <StatusBanner
            isConnected={!connectAgentToTrigger}
            onConnect={handleOpenDialog}
            onBannerDismiss={handleCallDismissBannerApi}
          />
        </Box>
      )}

      <Dialog
        open={openDialog}
        onClose={handleCloseDialog}
        maxWidth="md"
        fullWidth
        PaperProps={{
          sx: { bgcolor: theme.palette.background.default },
        }}
      >
        <DialogTitle>
          <Typography variant="h5" component="div">
            Connect Chatbot
          </Typography>
        </DialogTitle>
        <StyledDialogContent>
          <Box sx={{ mb: 4 }}>
            <Stepper activeStep={activeStep}>
              {steps.map((label) => (
                <Step key={label}>
                  <StepLabel>{label}</StepLabel>
                </Step>
              ))}
            </Stepper>
          </Box>
          {getStepContent(activeStep)}
        </StyledDialogContent>
        <DialogActions sx={{ p: 3 }}>
          <Button onClick={handleCloseDialog} color="inherit">
            Cancel
          </Button>
          {activeStep > 0 && (
            <Button onClick={handleBack} color="inherit">
              Back
            </Button>
          )}
          {activeStep === steps.length - 1 ? (
            <Button
              onClick={handleConnect}
              disabled={
                !triggerName || loading || (!selectedAccount && !showAddAccount)
              }
              variant="contained"
            >
              Connect {loading && <CircularProgress size={24} sx={{ ml: 1 }} />}
            </Button>
          ) : (
            <Button
              onClick={handleNext}
              disabled={!selectedProvider}
              variant="contained"
            >
              Next
            </Button>
          )}
        </DialogActions>
      </Dialog>
    </>
  );
};

export default AgentNotificationBanners;
