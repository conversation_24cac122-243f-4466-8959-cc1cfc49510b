import { Box, Paper } from "@mui/material";
import React from "react";
import {
  <PERSON><PERSON><PERSON>,
  Line,
  <PERSON>Axis,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Brush,
} from "recharts";

interface LineGraphProps {
  data: {
    date: number;
    numOfTokensUsed: number;
    cost: number;
    earned: number;
  }[];
}

function CustomTooltip({ active, payload, label }: any) {
  console.log(active, payload, label);

  if (active && payload && payload.length) {
    return (
      <div className="custom-tooltip bg-gray-50 shadow p-4">
        <p className="intro">
          {payload.map((item: any) => {
            return (
              <>
                <p className="label text-xs">
                  {new Date(item.payload.date).toLocaleString("en-US", {
                    month: "short",
                    day: "2-digit",
                    hour: "2-digit",
                    minute: "2-digit",
                    hour12: true,
                  })}
                </p>
                <p key={item.dataKey}>
                  {item.dataKey === "numOfTokensUsed"
                    ? `${item.value} tokens`
                    : `$${item.value}`}
                </p>
              </>
            );
          })}
        </p>
      </div>
    );
  }

  return null;
}

const LineGraph: React.FC<LineGraphProps> = ({ data }) => {
  return (
    <div style={{ width: "100%" }}>
      <Box
        component={Paper}
        padding={2}
        sx={{ display: "flex", flexDirection: "column", gap: 2 }}
      >
        <p className="font-extrabold">Tokens</p>
        <ResponsiveContainer width="100%" height={200}>
          <LineChart
            width={500}
            height={200}
            data={data}
            syncId="anyId"
            margin={{
              top: 10,
              right: 30,
              left: 0,
              bottom: 0,
            }}
          >
            <CartesianGrid strokeDasharray="3 3" />
            {/* <XAxis
              dataKey={"date"}
              tickFormatter={(unixTime) => formatDate(new Date(unixTime))}
            /> */}
            <YAxis />
            <Tooltip
              content={<CustomTooltip />}
              cursor={{ fill: "transparent" }}
            />
            <Line
              type="monotone"
              dataKey={"numOfTokensUsed"}
              stroke="#8884d8"
              fill="#8884d8"
            />
          </LineChart>
        </ResponsiveContainer>
        <p className="font-extrabold">Earned</p>
        <ResponsiveContainer width="100%" height={200}>
          <LineChart
            width={500}
            height={200}
            data={data.map((item) => ({
              ...item,
              earned: item.earned / 100,
            }))}
            syncId="anyId"
            margin={{
              top: 10,
              right: 30,
              left: 0,
              bottom: 0,
            }}
          >
            <CartesianGrid strokeDasharray="3 3" />
            {/* <XAxis
              dataKey={"date"}
              tickFormatter={(unixTime) => formatDate(new Date(unixTime))}
            /> */}
            <YAxis />
            <Tooltip
              content={<CustomTooltip />}
              cursor={{ fill: "transparent" }}
            />
            <Line
              type="monotone"
              dataKey={"earned"}
              stroke="#8884d8"
              fill="#8884d8"
            />
          </LineChart>
        </ResponsiveContainer>
        <p className="font-extrabold">Cost</p>
        <ResponsiveContainer width="100%" height={200}>
          <LineChart
            width={500}
            height={200}
            data={data}
            syncId="anyId"
            margin={{
              top: 10,
              right: 30,
              left: 0,
              bottom: 0,
            }}
          >
            <CartesianGrid strokeDasharray="3 3" />
            {/* <XAxis
              dataKey={"date"}
              tickFormatter={(unixTime) => formatDate(new Date(unixTime))}
            /> */}
            <YAxis />
            <Tooltip
              content={<CustomTooltip />}
              cursor={{ fill: "transparent" }}
            />
            <Line
              type="monotone"
              dataKey={"cost"}
              stroke="#8884d8"
              fill="#8884d8"
            />
          </LineChart>
        </ResponsiveContainer>
      </Box>
    </div>
  );
};

export default LineGraph;
