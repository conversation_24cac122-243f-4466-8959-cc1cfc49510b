import { Breadcrumbs, Typography } from "@mui/material";
import React from "react";
import NavigateNextIcon from "@mui/icons-material/NavigateNext";
import FolderIcon from "@mui/icons-material/Folder";
import { FolderPath } from "@/slices/agents/agentsSlice";
import Link from "next/link";

interface IProp {
  path: FolderPath[];
}

const FolderBreadcrumb: React.FC<IProp> = ({ path }) => {
  return (
    <Breadcrumbs
      sx={{
        mb: 1,
      }}
      separator={<NavigateNextIcon sx={{ color: "white" }} fontSize="small" />}
    >
      <FolderIcon sx={{ color: "white", mt: 1 }} />
      {path?.map((folder, index) => {
        if (index === path.length - 1) {
          return (
            <Typography color="white" key={folder._id}>
              {folder.name}
            </Typography>
          );
        }
        return (
          <Link
            className="text-white underline"
            href={`?folder=${folder._id}`}
            key={folder._id}
          >
            {folder.name}
          </Link>
        );
      })}
    </Breadcrumbs>
  );
};

export default FolderBreadcrumb;
