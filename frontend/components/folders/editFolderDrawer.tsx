import React, { useEffect, useState } from "react";
import {
  <PERSON>er,
  TextField,
  FormLabel,
  Button,
  Box,
  FormControl,
  InputLabel,
  Input,
  DialogTitle,
} from "@mui/material";
import EditRoundedIcon from "@mui/icons-material/EditRounded";
import IconButton from "@mui/material/IconButton";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store";
import { snackbarActions } from "@/slices/general/snackbar";
import { editFolder } from "@/requests/folders/editFolder";
import { agentsAction } from "@/slices/agents/agentsSlice";

const EditFolderDrawer = () => {
  const dispatch = useDispatch();
  const [isOpen, setIsOpen] = useState(false);
  const currentFolderPath = useSelector(
    (state: RootState) => state.agents.currentFolderPath
  );

  const [name, setName] = useState("");
  const [folderId, setFolderId] = useState("");
  const [loading, setLoading] = useState(false);
  useEffect(() => {
    setName(currentFolderPath[currentFolderPath.length - 1].name);
    setFolderId(currentFolderPath[currentFolderPath.length - 1]._id);
  }, [currentFolderPath]);

  const handleOpen = () => setIsOpen(true);
  const handleClose = () => setIsOpen(false);

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setName(event.target.value);
  };

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    if (!!!name) {
      dispatch(
        snackbarActions.setSnackbar({
          message: "Folder name cannot be empty",
          type: "error",
        })
      );
    } else if (!!!folderId) {
      dispatch(
        snackbarActions.setSnackbar({
          message: "Folder Id cannot be empty",
          type: "error",
        })
      );
    }
    // Handle form submission here
    const data = {
      name,
      folderId,
    };
    console.log({ data });
    setLoading(true);
    const response = await editFolder(data, dispatch);
    setLoading(false);
    if (response?.status === 200) {
      dispatch(agentsAction.folderEdited(response.data));
      let newFolderPath = [...currentFolderPath];
      newFolderPath[newFolderPath.length - 1] = {
        ...newFolderPath[newFolderPath.length - 1],
        name: name,
      };
      dispatch(agentsAction.currentFolderPathChanged(newFolderPath));
    }
  };

  return (
    <div>
      <IconButton onClick={handleOpen} color="primary">
        <EditRoundedIcon />
      </IconButton>
      <Drawer anchor="right" open={isOpen} onClose={handleClose}>
        <DialogTitle sx={{ pb: 1 }}>Edit Folder</DialogTitle>
        <Box px={3} width={450}>
          <form onSubmit={handleSubmit} className="flex flex-col gap-2">
            <FormControl variant="standard" required>
              <InputLabel htmlFor="id-nickname">Folder name</InputLabel>
              <Input
                id="id-folder-name"
                name="Folder name"
                value={name}
                onChange={handleChange}
              />
            </FormControl>
            <div className="flex justify-end">
              <Button onClick={handleClose} color="primary">
                Cancel
              </Button>
              <Button
                type="submit"
                variant="contained"
                color="primary"
                disabled={loading}
              >
                Submit{loading && "ting..."}
              </Button>
            </div>
          </form>
        </Box>
      </Drawer>
    </div>
  );
};

export default EditFolderDrawer;
