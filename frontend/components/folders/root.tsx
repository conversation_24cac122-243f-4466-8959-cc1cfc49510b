import React, { useEffect } from "react";
import FolderStructure from "./folderStructure";
import { Grid, Skeleton, Typography } from "@mui/material";
import FileStructure from "./files/fileStructure";
import {
  Folder,
  IAgent,
  IAgentDemo,
  agentsAction,
} from "@/slices/agents/agentsSlice";
import { useRouter } from "next/router";
import { getRootFolder } from "@/requests/folders/getRootFolder";
import { local } from "d3";
import { useDispatch, useSelector } from "react-redux";
import { getFolderById } from "@/requests/folders/getFolderById";
import { RootState } from "@/store";

export type Agent = IAgentDemo;

export type Resource = Agent;

export type FolderType = "agents" | "integrations";

interface IProp {
  searchQuery?: string;
  orgId: string;
  type: FolderType;
}

const Root: React.FC<IProp> = ({ orgId, type, searchQuery = "" }) => {
  const dispatch = useDispatch();
  const router = useRouter();
  const currentOrgId = useSelector(
    (state: RootState) => state.organization.currentOrganizationId
  );

  const [loading, setLoading] = React.useState(true);
  const { folder } = router.query as { folder: string };

  const resources = useSelector((state: RootState) => state.agents.agentsList);
  const folders = useSelector((state: RootState) => state.agents.folders);

  const [filteredResources, setFilteredResources] = React.useState<Resource[]>(
    []
  );
  const [filteredFolders, setFilteredFolders] = React.useState<Folder[]>([]);

  useEffect(() => {
    const fetchFoldersWithOrg = async () => {
      setLoading(true);
      const response = await getRootFolder({ orgId, type }, dispatch);
      setLoading(false);
      if (response.status === 200) {
        dispatch(agentsAction.agentsListAdded(response.data.resources));
        dispatch(agentsAction.folderListAdded(response.data.folders));
      }
    };
    const fetchFolderWithId = async () => {
      setLoading(true);
      const response = await getFolderById({ folderId: folder }, dispatch);
      setLoading(false);
      if (response.status === 200) {
        console.log({
          resources: response.data.resources,
          folders: response.data.folders,
        });

        dispatch(agentsAction.agentsListAdded(response.data.resources));
        dispatch(agentsAction.folderListAdded(response.data.folders));
      }
    };

    if (!!!folder) {
      //search by orgId
      fetchFoldersWithOrg();
    } else {
      //search by folderId
      fetchFolderWithId();
    }
  }, [folder, currentOrgId]);

  useEffect(() => {
    if (searchQuery === "") {
      setFilteredResources(resources);
      setFilteredFolders(folders);
    } else {
      const filteredResources = resources.filter((resource) =>
        resource.agentName?.toLowerCase().includes(searchQuery.toLowerCase())
      );
      const filteredFolders = folders.filter((folder) =>
        folder.name?.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setFilteredResources(filteredResources);
      setFilteredFolders(filteredFolders);
    }
  }, [searchQuery, resources, folders]);
  if (!orgId) {
    return <Typography variant="h6">No organization id provided</Typography>;
  }
  if (loading) {
    return (
      <Grid container spacing={2}>
        {Array(8)
          .fill([0])
          .map((_, index) => (
            <Grid
              item
              sm={12}
              md={6}
              lg={4}
              key={"SkeletonKey_AgentCard_" + index}
            >
              <Skeleton
                variant="rectangular"
                height={210}
                sx={{ bgcolor: "grey.700", borderRadius: "10px" }}
              />
            </Grid>
          ))}
      </Grid>
    );
  }
  if (folders.length === 0 && resources.length === 0) {
    return <Typography variant="h6">No {type} found</Typography>;
  }
  return (
    <Grid container spacing={2}>
      <FolderStructure folders={filteredFolders} />
      <FileStructure files={filteredResources} type={type} />
    </Grid>
  );
};

export default Root;
