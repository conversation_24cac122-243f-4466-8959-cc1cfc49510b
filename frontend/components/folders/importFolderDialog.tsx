import React, { FormEvent, useEffect, useState } from "react";
import { FolderType } from "./root";
import { useDispatch, useSelector } from "react-redux";
import { useRouter } from "next/router";
import {
  <PERSON><PERSON>,
  Chip,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Typography,
} from "@mui/material";
import { ImSpinner8 } from "react-icons/im";
import { SimpleTreeView } from "@mui/x-tree-view/SimpleTreeView";
import { TreeItem } from "@mui/x-tree-view/TreeItem";
import { getFoldersNested } from "@/requests/folders/getFoldersNested";
import { transferFolder } from "@/requests/folders/transfer-folder";
import { Folder, FolderPath, agentsAction } from "@/slices/agents/agentsSlice";
import FolderIcon from "@mui/icons-material/Folder";
import { Box } from "@mui/material";
import { getOrgAgents } from "@/requests/agents/basic/getAgent";
import InsertDriveFileIcon from "@mui/icons-material/InsertDriveFile";
import { transferFile } from "@/requests/folders/transfer-file";
import { RootState } from "@/store";
import FolderBreadcrumb from "./folderBreadcrumb";

interface IProp {
  type: FolderType;
}

type Agent = {
  _id: string;
  agentName: string;
};

type NestedFolder = Folder & {
  resourceIds: string[];
  children: NestedFolder[];
};

const FileItem = ({ file }: { file: Agent }) => (
  <TreeItem
    itemId={file._id}
    label={
      <Box display="flex" alignItems="center">
        <InsertDriveFileIcon />
        <Box ml={1}>
          {file.agentName || "No name"} <Chip label={file._id} size="small" />
        </Box>
      </Box>
    }
  />
);

const FolderItem = ({
  folder,
  agentList,
}: {
  folder: NestedFolder;
  agentList: Agent[];
}) => {
  const folderAgents = agentList.filter((agent) =>
    folder.resourceIds.includes(agent._id)
  );

  return (
    <TreeItem
      itemId={folder._id}
      label={
        <Box display="flex" alignItems="center">
          <FolderIcon />
          <Box ml={1}>{folder.name}</Box>
        </Box>
      }
    >
      {folder.children &&
        folder.children.map((child: NestedFolder) => (
          <FolderItem key={child._id} folder={child} agentList={agentList} />
        ))}
      {folderAgents.map((agent) => (
        <FileItem key={agent._id} file={agent} />
      ))}
    </TreeItem>
  );
};

const ImportFolderDialog: React.FC<IProp> = ({ type }) => {
  const dispatch = useDispatch();
  const router = useRouter();
  const currentFolderPath = useSelector(
    (state: RootState) => state.agents.currentFolderPath
  );

  const open = router.query.action === "import-folder";
  const orgId = localStorage.getItem("orgId") as string;
  const { folder: parent } = router.query as { folder: string };

  const [loading, setLoading] = React.useState(false);
  const [loading2, setLoading2] = React.useState(false);
  const [loading3, setLoading3] = React.useState(false);

  const [agentList, setAgentList] = useState<Agent[]>([]);
  const [selectedItems, setSelectedItems] = React.useState<string | null>(null);
  const [nestedFolders, setNestedFolders] = React.useState<any[]>([]);
  const handleSelectedItemsChange = (
    event: React.SyntheticEvent,
    itemId: string | null
  ) => {
    console.log({ itemId });

    if (itemId === null) {
      setSelectedItems(null);
    } else {
      setSelectedItems(itemId);
    }
  };
  useEffect(() => {
    const fetchFoldersNested = async () => {
      setLoading(true);
      const response = await getFoldersNested({ orgId, type }, dispatch);
      if (response?.status === 200) {
        setNestedFolders(response.data);
      }
      setLoading(false);
    };
    const fetchAgents = async () => {
      setLoading3(true);
      const response = await getOrgAgents({ orgId, onlyName: true }, dispatch);
      setLoading3(false);
      if (response?.status === 200) {
        setAgentList(response.data.data);
      }
    };
    fetchAgents();
    fetchFoldersNested();
  }, [parent]);

  function findFolderPath(folders: any[], id: string): FolderPath[] {
    const folder = folders.find((folder) => folder._id === id);
    if (!folder) {
      return [];
    }
    const parentPath = folder.parent
      ? findFolderPath(folders, folder.parent)
      : [];
    return [...parentPath, { name: folder.name, _id: folder._id }];
  }

  function flattenFolders(folders: any[]): any[] {
    return folders.reduce((flat, folder) => {
      return [...flat, folder, ...flattenFolders(folder.children)];
    }, []);
  }

  useEffect(() => {
    const flatFolders = flattenFolders(nestedFolders);
    let path: FolderPath[] = [];
    if (!!!parent) {
      path.push({
        _id: "#",
        name: "root",
      });
    } else {
      path = findFolderPath(flatFolders, parent);
    }
    console.log({ path });

    dispatch(agentsAction.currentFolderPathChanged(path));
  }, [parent, nestedFolders]);

  const handleClose = () => {
    const newQuery = { ...router.query };
    delete newQuery.action;

    router.push({
      pathname: router.pathname,
      query: newQuery,
    });
  };
  const folderTransfer = async () => {
    console.log({
      selectedItems,
    });
    const body = {
      sourceFolderId: selectedItems,
      destinationFolderId: parent,
      resourceType: type,
      orgId,
    };
    setLoading2(true);
    const response = await transferFolder(body, dispatch);
    setLoading2(false);
    if (response?.status === 201) {
      dispatch(agentsAction.newFolderAdded(response.data.sourceFolder));
      handleClose();
    }
  };

  const fileTransfer = async () => {
    const body = {
      resourceId: selectedItems as string,
      folderId: parent,
      resourceType: type,
      orgId,
    };
    setLoading2(true);
    const response = await transferFile(body, dispatch);
    setLoading2(false);
    if (response?.status === 201) {
      dispatch(agentsAction.newAgentsAdded(response.data));
      handleClose();
    }
  };
  const onSubmit = async (e: FormEvent) => {
    e.preventDefault();
    // Check if the selected item is a folder or a file
    const isAgent = agentList.find((agent) => agent._id === selectedItems);
    console.log({
      cond: isAgent,
      selectedItems,
      nestedFolders,
      agentList,
    });

    if (!!!isAgent) {
      // folder is selected
      folderTransfer();
    } else if (isAgent) {
      // file is selected
      fileTransfer();
    }
  };
  return (
    <Dialog open={open} onClose={handleClose} fullWidth>
      <form noValidate autoComplete="off" onSubmit={onSubmit}>
        <DialogTitle>Import Folder</DialogTitle>
        <DialogContent>
          {!!loading ||
            (!!loading3 && (
              <div className="w-full flex items-center gap-2 justify-center">
                <ImSpinner8 className="animate-spin" />
                <Typography>
                  Loading {loading ? "folders" : "agents"}...
                </Typography>
              </div>
            ))}
          {!!!loading && (
            <>
              <SimpleTreeView
                defaultExpandedItems={currentFolderPath?.map(
                  (folder) => folder._id
                )}
                selectedItems={parent}
                onSelectedItemsChange={handleSelectedItemsChange}
              >
                {nestedFolders.map((folder) => (
                  <FolderItem
                    key={folder._id}
                    folder={folder}
                    agentList={agentList}
                  />
                ))}
              </SimpleTreeView>
            </>
          )}
          {/* {currentFolderPath ? (
            <div className="bg-gray-400 px-2">
              <FolderBreadcrumb path={currentFolderPath} />
            </div>
          ) : null} */}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClose}>Cancel</Button>
          <Button type="submit" color="primary" disabled={loading2}>
            Import{loading2 && "ing..."}
          </Button>
        </DialogActions>
      </form>
    </Dialog>
  );
};

export default ImportFolderDialog;
