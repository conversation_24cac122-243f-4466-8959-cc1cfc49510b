import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  DialogT<PERSON>le,
  <PERSON>alogContent,
  <PERSON>alog<PERSON><PERSON>,
  <PERSON><PERSON>,
  Alert,
} from "@mui/material";
import DeleteOutlineRoundedIcon from "@mui/icons-material/DeleteOutlineRounded";
import IconButton from "@mui/material/IconButton";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store";
import { deleteFolder } from "@/requests/folders/deleteFolder";
import { agentsAction } from "@/slices/agents/agentsSlice";
import { useRouter } from "next/router";
import { snackbarActions } from "@/slices/general/snackbar";
import { ImSpinner8 } from "react-icons/im";

interface IProp {
  folderId: string;
}

const DeleteFolderDialog: React.FC<IProp> = ({ folderId }) => {
  const dispatch = useDispatch();
  const router = useRouter();
  const folders = useSelector((state: RootState) => state.agents.folders);
  const agentsList = useSelector((state: RootState) => state.agents.agentsList);
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = React.useState(false);

  const cantDelete = folders.length > 0 || agentsList.length > 0;

  const handleClickOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  const handleDelete = async () => {
    // Handle delete action here
    if (!!!folderId) {
      dispatch(
        snackbarActions.setSnackbar({
          message: "Cannot delete the root folder",
          type: "error",
        })
      );
      return;
    }
    setLoading(true);
    const response = await deleteFolder({ folderId }, dispatch);
    setLoading(false);
    if (response.status === 200) {
      dispatch(agentsAction.folderDeleted({ folderId }));
      router.push({
        pathname: router.pathname,
        query: {
          ...router.query,
          folder: response.data.newParent,
        },
      });
    }
    handleClose();
  };

  return (
    <div>
      <IconButton color="error" onClick={handleClickOpen}>
        <DeleteOutlineRoundedIcon />
      </IconButton>
      <Dialog open={open} onClose={handleClose}>
        <DialogTitle>
          {"Are you sure you want to delete this folder?"}
        </DialogTitle>
        <DialogContent>
          {/* You can add more content here if needed */}
          {cantDelete && (
            <Alert severity="error">
              Can&apos;t delete folder with subfolders or files
            </Alert>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClose} color="primary">
            Cancel
          </Button>
          <Button
            onClick={handleDelete}
            color="error"
            variant="contained"
            autoFocus
            disabled={cantDelete || loading}
          >
            Delete{loading && <ImSpinner8 className="animate-spin ml-2" />}
          </Button>
        </DialogActions>
      </Dialog>
    </div>
  );
};

export default DeleteFolderDialog;
