import React, { useRef } from "react";
import { useRouter } from "next/router";
import Button from "@mui/material/Button";
import ButtonGroup from "@mui/material/ButtonGroup";
import Menu from "@mui/material/Menu";
import MenuItem from "@mui/material/MenuItem";
import ArrowDropDownIcon from "@mui/icons-material/ArrowDropDown";
import AddIcon from "@mui/icons-material/Add";
import FolderIcon from "@mui/icons-material/Folder";
import ImportExportIcon from "@mui/icons-material/ImportExport";
import { ListItemIcon } from "@mui/material";
import ChatbotCreationFlow from "../agents/agent-builder/AgentBuilderDrawer";
import { Add } from "@mui/icons-material";

const AddNew = () => {
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const router = useRouter();
  const buttonGroupRef = useRef<HTMLDivElement>(null);

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleAction = (action: string) => {
    router.push({
      pathname: router.pathname,
      query: { ...router.query, action },
    });
    handleClose();
  };

  const trigger = (
    <Button variant="contained" startIcon={<Add />}>
      Add Chatbot
    </Button>
  );

  return (
    <div>
      <ButtonGroup
        variant="contained"
        aria-label="split button"
        ref={buttonGroupRef}
      >
        {/* <Button
          onClick={() => handleAction("add-agent")}
          startIcon={<AddIcon />}
        >
          Add Agent
        </Button> */}
        <ChatbotCreationFlow trigger={trigger} />
        <Button
          size="small"
          aria-controls="split-button-menu"
          aria-expanded={Boolean(anchorEl) ? "true" : undefined}
          aria-haspopup="menu"
          onClick={handleClick}
        >
          <ArrowDropDownIcon />
        </Button>
      </ButtonGroup>
      <Menu
        id="split-button-menu"
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleClose}
        sx={{ marginTop: "2px" }}
        anchorOrigin={{ vertical: "bottom", horizontal: "right" }}
        transformOrigin={{ vertical: "top", horizontal: "right" }}
        PaperProps={{
          style: {
            width: buttonGroupRef?.current?.clientWidth,
          },
        }}
      >
        <MenuItem onClick={() => handleAction("add-folder")}>
          <ListItemIcon>
            <FolderIcon fontSize="small" />
          </ListItemIcon>
          Add Folder
        </MenuItem>
        <MenuItem onClick={() => handleAction("import-folder")}>
          <ListItemIcon>
            <ImportExportIcon fontSize="small" />
          </ListItemIcon>
          Import
        </MenuItem>
      </Menu>
    </div>
  );
};

export default AddNew;
