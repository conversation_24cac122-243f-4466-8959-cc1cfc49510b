import React, { FormEvent } from "react";
import { useRouter } from "next/router";
import Dialog from "@mui/material/Dialog";
import DialogTitle from "@mui/material/DialogTitle";
import DialogContent from "@mui/material/DialogContent";
import DialogActions from "@mui/material/DialogActions";
import TextField from "@mui/material/TextField";
import Button from "@mui/material/Button";
import { FolderType } from "./root";
import { createFolder } from "@/requests/folders/createFolder";
import { useDispatch } from "react-redux";
import { agentsAction } from "@/slices/agents/agentsSlice";

interface IProp {
  type: FolderType;
}

const AddNewFolderDialog: React.FC<IProp> = ({ type }) => {
  const dispatch = useDispatch();
  const router = useRouter();
  const open = router.query.action === "add-folder";
  const { folder: parent } = router.query as { folder: string };
  const [name, setName] = React.useState("");
  const [loading, setLoading] = React.useState(false);

  const handleClose = () => {
    const newQuery = { ...router.query };
    delete newQuery.action;
  
    router.push({
      pathname: router.pathname,
      query: newQuery,
    });
  };

  const onSubmit = async (e: FormEvent) => {
    e.preventDefault();
    const body = {
      parent,
      organization: localStorage.getItem("orgId") as string,
      name,
      type,
    };
    setLoading(true);
    const response = await createFolder(body, dispatch);
    setLoading(false);
    if (response?.status === 201) {
      dispatch(agentsAction.newFolderAdded(response.data));
      setName("");
    }
    handleClose();
  };

  return (
    <Dialog open={open} onClose={handleClose} fullWidth>
      <form noValidate autoComplete="off" onSubmit={onSubmit}>
        <DialogTitle>Add Folder</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            id="name"
            label="Folder Name"
            value={name}
            type="text"
            fullWidth
            required
            onChange={(e) => setName(e.target.value)}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClose}>Cancel</Button>
          <Button type="submit" color="primary" disabled={loading}>
            Add{loading && "ing..."}
          </Button>
        </DialogActions>
      </form>
    </Dialog>
  );
};

export default AddNewFolderDialog;
