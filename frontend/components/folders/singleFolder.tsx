import { Grid } from "@mui/material";
import React from "react";
import FolderIcon from "@mui/icons-material/Folder";
import Link from "next/link";
import { useRouter } from "next/router";
import { Folder } from "@/slices/agents/agentsSlice";

interface IProp {
  folder: Folder;
  lastFolder?: boolean;
}

const SingleFolder: React.FC<IProp> = ({ folder }) => {
  const router = useRouter();
  const loading = false;
  const goToFolder = (folderId: string) => {
    const newPath = {
      pathname: router.pathname,
      query: { ...router.query, folder: folderId },
    };
    const newUrl = `${router.asPath.split("?")[0]}?${new URLSearchParams(
      newPath.query
    ).toString()}`;
    return newUrl;
  };
  return (
    <>
      <Grid item sm={12} md={6} lg={4}>
        <Link href={goToFolder(folder._id)}>
          <div
            className={`h-[210px] border-[1px] border-slate-600 bg-gradient-to-tr from-slate-300 to-slate-400 rounded-2xl text-center flex items-center justify-center cursor-pointer hover:scale-[1.05] transition-all hover:bg-gradient-to-tl ${
              loading ? "opacity-50 pointer-events-none" : ""
            }`}
          >
            <div>
              <div>
                <FolderIcon className="text-[50px] mx-auto text-gray-800" />
              </div>
              <div className="text-[24px] text-gray-800 font-medium">
                {folder.name}
              </div>
            </div>
          </div>
        </Link>
      </Grid>
    </>
  );
};

export default SingleFolder;
