import React from "react";
import { useRouter } from "next/router";
import Link from "next/link";
import Avatar from "@mui/material/Avatar";
import AvatarGroup from "@mui/material/AvatarGroup";
import { IAgent } from "@/slices/agents/agentsSlice";
import { logos } from "@/helpers/images";
import { Chip, Grid, IconButton, Tooltip, Typography } from "@mui/material";
import { grey } from "@mui/material/colors";
import ContentCopyIcon from "@mui/icons-material/ContentCopy";
import { useDispatch } from "react-redux";
import { snackbarActions } from "@/slices/general/snackbar";
import { Agent } from "../../root";
import { CONSTANTS } from "@/helpers/constants";

interface CardsProps {
  data: Agent;
}
const Cards: React.FC<CardsProps> = ({ data }) => {
  const dispatch = useDispatch();
  const router = useRouter();
  const parentId = router.pathname;
  const companyId = data.aiProvider?.companyId;
  console.log({ companyId });

  const copyToClipboard = (e: React.MouseEvent) => {
    e.preventDefault();
    navigator.clipboard.writeText(data._id);
    dispatch(
      snackbarActions.setSnackbar({
        message: "Agent ID copied to clipboard",
        type: "success",
      })
    );
  };
  if (!!!data?._id) return null;
  return (
    <Grid item sm={12} md={6} lg={4}>
      <div className="px-5 text-gray-700 py-2 min-h-[210px] border-[1px] border-slate-600 bg-gradient-to-tr from-slate-300 to-slate-400 rounded-2xl text-center hover:scale-[1.05] cursor-pointer transition-all hover:bg-gradient-to-tl">
        <Link href={parentId + "/" + data._id}>
          <div className="font-semibold flex justify-center items-center gap-1">
            <div className="text-[24px] truncate">
              {data?.agentName || "No name"}
            </div>
            <div
              style={{
                padding: "5px",
                width: "10px",
                height: "10px",
                borderRadius: "50%",
                backgroundColor:
                  "disabled" in data && data.disabled === true
                    ? "red"
                    : "green",
              }}
            />
            <Tooltip title="Copy to clipboard">
              <IconButton onClick={copyToClipboard} color="primary">
                <ContentCopyIcon />
              </IconButton>
            </Tooltip>
          </div>
          <div className="flex flex-col mt-1">
            <div className="flex my-2 h-[30px]">
              <div className="w-4/12 text-start font-extrabold">
                AI provider
              </div>
              <div className="w-8/12 flex">
                <Avatar
                  src={logos[companyId]?.logo || ""}
                  alt={companyId}
                  sx={{
                    padding: "5px",
                    background: "linear-gradient(to right, #fff, #dcdcdc)",
                    width: "35px",
                    height: "35px",
                    border: 0,
                    boxShadow: 2,
                  }}
                />
              </div>
            </div>
            <div className="flex my-2 h-[30px]">
              <div className="w-4/12 text-start font-extrabold">Prompt</div>
              <div className="w-8/12 text-left truncate">
                {data.mainPrompt || "Not set"}
              </div>
            </div>
            <div className="flex my-2 h-[30px]">
              <div className="w-4/12 text-start font-extrabold">Actions</div>
              {!!!data.actions?.length && <Typography>Not set</Typography>}
              <AvatarGroup
                max={4}
                sx={{
                  "& .MuiAvatar-colorDefault": {
                    padding: "5px",
                    width: "25px",
                    height: "25px",
                    border: 1,
                    background: "linear-gradient(to right, #fff, #dcdcdc)",
                    boxShadow: 2,
                    color: grey[800],
                  },
                }}
              >
                {data.actions?.map((item, index) => (
                  <Avatar
                    alt={"icon_" + item}
                    src={"/squareLogos" + logos[item]?.logo || ""}
                    key={"action_" + index}
                    sx={{
                      padding: "5px",
                      background: "linear-gradient(to right, #fff, #dcdcdc)",
                      width: "25px",
                      height: "25px",
                      border: 0,
                      boxShadow: 2,
                    }}
                  />
                ))}
              </AvatarGroup>
            </div>
          </div>
        </Link>
      </div>
    </Grid>
  );
};

export default Cards;
