import React, { useEffect, useState } from "react";
import { useRouter } from "next/router";
import Dialog from "@mui/material/Dialog";
import DialogTitle from "@mui/material/DialogTitle";
import DialogContent from "@mui/material/DialogContent";
import DialogActions from "@mui/material/DialogActions";
import TextField from "@mui/material/TextField";
import Button from "@mui/material/Button";
import Autocomplete from "@mui/material/Autocomplete";
import Checkbox from "@mui/material/Checkbox";
import FormControlLabel from "@mui/material/FormControlLabel";
import SystemUpdateAltIcon from "@mui/icons-material/SystemUpdateAlt";
import { Box, Chip, Stack, Typography } from "@mui/material";

import { getOrgAgents } from "@/requests/agents/basic/getAgent";
import { useDispatch, useSelector } from "react-redux";
import {
  createAgent,
  createAgentExtract,
} from "@/requests/agents/basic/createAgent";
import { agentsUrl } from "@/slices/subOptions/subOptionSlice";
import { agentsAction } from "@/slices/agents/agentsSlice";
import CloseIcon from "@mui/icons-material/Close";
import CustomAutocomplete from "@/components/customerAutocomplete";
import { organizationAction } from "@/slices/organization/organizationSlice";
import { RootState } from "@/store";

interface Agent {
  _id: string;
  agentName: string;
}

const agents: Agent[] = [
  // Populate with actual data
];

type ImportOptions = {
  aiProviders: boolean;
  prompts: boolean;
  dataSources: boolean;
  savedSessions: boolean;
};

const AddAgentDialog = () => {
  const dispatch = useDispatch();
  const router = useRouter();
  const open = router.query.action === "add-agent";
  const { folder } = router.query as { folder: string };
  const setupStatus = useSelector(
    (state: RootState) => state.organization.setupStatus
  );
  const [name, setName] = useState("");
  const [loading, setLoading] = useState(false);
  const [agentListLoading, setAgentListLoading] = useState(false);
  const [showAgentList, setShowAgentList] = useState(false);
  const orgId = localStorage.getItem("orgId") as string;
  const [selectedAgent, setSelectedAgent] = useState<Agent | null>(null);
  const [orgAgentList, setOrgAgentList] = useState<Agent[]>([]);

  useEffect(() => {
    if (!!!showAgentList) {
      setSelectedAgent(null);
    }
  }, [showAgentList]);

  const [importOptions, setImportOptions] = useState<ImportOptions>({
    aiProviders: false,
    prompts: false,
    dataSources: false,
    savedSessions: false,
  });

  const handleClose = () => {
    const newQuery = { ...router.query };
    delete newQuery.action;
    router.push({
      pathname: router.pathname,
      query: newQuery,
    });
  };
  const onSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    // Submit logic
    let body: {
      agentName: string;
      agentId?: string;
      orgId: string;
      agentImports?: ImportOptions;
      folder?: string;
    } = {
      agentName: name,
      agentId: selectedAgent?._id,
      orgId,
      folder,
    };
    if (showAgentList) {
      body.agentImports = importOptions;
    }
    console.log({ body });
    let response: any;
    setLoading(true);
    if (showAgentList) {
      //extract agent
      response = await createAgentExtract(body, dispatch);
    } else {
      //create new agent
      response = await createAgent(body, dispatch);
    }
    setLoading(false);
    handleClose();
    if (response?.status === 201) {
      console.log({ newAGent: response.data.data });
      await router.push(agentsUrl + "/" + response.data.data._id);
      dispatch(agentsAction.newAgentsAdded(response.data.data));
      dispatch(
        organizationAction.setupStatusChanged({ ...setupStatus, agent: true })
      );
    }
  };

  useEffect(() => {
    const getOrgAgentList = async () => {
      setAgentListLoading(true);
      const response = await getOrgAgents({ orgId, onlyName: true }, dispatch);
      setAgentListLoading(false);
      if (response?.status === 200) {
        setOrgAgentList(response.data.data);
      }
    };
    getOrgAgentList();
  }, []);

  return (
    <Dialog open={open} onClose={handleClose} fullWidth>
      <form noValidate autoComplete="off" onSubmit={onSubmit}>
        <DialogTitle>Add Agent</DialogTitle>
        <DialogContent
          sx={{
            display: "flex",
            flexDirection: "column",
            gap: 2,
          }}
        >
          <TextField
            autoFocus
            margin="dense"
            id="name"
            label="Agent Name"
            value={name}
            type="text"
            fullWidth
            required
            onChange={(e) => setName(e.target.value)}
          />
          <Stack direction="row" alignItems={"center"} spacing={2}>
            <Button
              variant={showAgentList ? "contained" : "outlined"}
              color="primary"
              startIcon={<SystemUpdateAltIcon />}
              onClick={() => {
                setShowAgentList((prev) => !prev);
              }}
            >
              Import Existing Agent
            </Button>
            {showAgentList && (
              <Chip
                label="Close"
                variant="outlined"
                color="warning"
                icon={<CloseIcon />}
                onClick={() => {
                  setShowAgentList((prev) => !prev);
                }}
              />
            )}
          </Stack>
          {showAgentList && (
            <CustomAutocomplete
              options={orgAgentList}
              selectedOption={selectedAgent}
              setSelectedOption={setSelectedAgent}
              loading={agentListLoading}
              getOptionLabel={(option) => option.agentName || "No name"}
              renderOption={(props, option, { selected }) => (
                <li {...props} key={option._id}>
                  <Box>{option.agentName || "No name"}</Box>
                </li>
              )}
            />
            // <Autocomplete
            //   options={orgAgentList}
            //   getOptionLabel={(option) => option.agentName || "No name"}
            //   onChange={(event, newValue) => {
            //     setSelectedAgent(newValue);
            //   }}
            //   renderInput={(params) => (
            //     <TextField {...params} label="Agents" variant="outlined" />
            //   )}
            //   fullWidth={true}
            // />
          )}
          {selectedAgent && (
            <>
              <Stack direction={"column"}>
                <Typography variant="body1">Import Options</Typography>
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={importOptions.aiProviders}
                      onChange={() =>
                        setImportOptions((prev) => ({
                          ...prev,
                          aiProviders: !prev.aiProviders,
                        }))
                      }
                    />
                  }
                  label="AI Providers"
                />
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={importOptions.prompts}
                      onChange={() =>
                        setImportOptions((prev) => ({
                          ...prev,
                          prompts: !prev.prompts,
                        }))
                      }
                    />
                  }
                  label="Prompts"
                />
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={importOptions.dataSources}
                      onChange={() =>
                        setImportOptions((prev) => ({
                          ...prev,
                          dataSources: !prev.dataSources,
                        }))
                      }
                    />
                  }
                  label="Actions"
                />
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={importOptions.savedSessions}
                      onChange={() =>
                        setImportOptions((prev) => ({
                          ...prev,
                          savedSessions: !prev.savedSessions,
                        }))
                      }
                    />
                  }
                  label="Saved Sessions"
                />
              </Stack>
            </>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClose}>Cancel</Button>
          <Button type="submit" color="primary" disabled={loading}>
            Add{loading && "ing..."}
          </Button>
        </DialogActions>
      </form>
    </Dialog>
  );
};

export default AddAgentDialog;
