import React from "react";
import { FolderType, Resource } from "../root";
import Cards from "./agent/card";

interface IProp {
  files: Resource[];
  type: FolderType;
}

const FileStructure: React.FC<IProp> = ({ files, type }) => {
  if (type === "agents") {
    return (
      <React.Fragment>
        {files.map((file, index) => (
          <Cards key={file._id} data={file} />
        ))}
      </React.Fragment>
    );
  }
  return <div></div>;
};

export default FileStructure;
