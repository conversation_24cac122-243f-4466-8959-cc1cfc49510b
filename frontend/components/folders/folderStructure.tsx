import { List, Paper } from "@mui/material";
import React from "react";
import SingleFolder from "./singleFolder";
import { Folder } from "@/slices/agents/agentsSlice";

interface IProp {
  folders: Folder[];
}

const FolderStructure: React.FC<IProp> = ({ folders }) => {
  return (
    <>
      {folders.map((folder, index) => (
        <SingleFolder folder={folder} key={folder._id} />
      ))}
    </>
  );
};

export default FolderStructure;
