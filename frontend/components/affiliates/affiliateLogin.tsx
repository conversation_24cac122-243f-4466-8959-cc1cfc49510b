import { affiliateActions } from "@/slices/affiliates/affiliateSlice";
import {
  Button,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  SelectChangeEvent,
  TextField,
} from "@mui/material";
import React from "react";
import { BiArrowBack } from "react-icons/bi";
import { useDispatch } from "react-redux";

const AffiliateLogin = () => {
  const dispatch = useDispatch();
  const [age, setAge] = React.useState("");

  const handleChange = (event: SelectChangeEvent) => {
    setAge(event.target.value as string);
  };

  return (
    <div className="mt-20">
      <div className="form bg-slate-200 rounded-md shadow-sm shadow-gray-500 w-[500px] px-5 py-3">
        <div className="mb-3">
          <div className="relative">
            <div className="absolute h-full flex items-center">
              <BiArrowBack
                className="text-[24px] cursor-pointer hover:bg-slate-300 rounded-full"
                onClick={() => {
                  dispatch(affiliateActions.setupInProgressChanged(false));
                }}
              />
            </div>
            <div className="text-center text-[28px] font-bold text-gray-700">
              Affiliate details
            </div>
          </div>
        </div>
        <div className="flex gap-2 justify-between mb-3">
          <TextField
            required
            id="outlined-required"
            label="First name"
            defaultValue=""
            variant="filled"
          />
          <TextField
            required
            id="outlined-required"
            label="Last name"
            defaultValue=""
            variant="filled"
          />
        </div>
        <div className="mb-3">
          <TextField
            required
            id="outlined-required"
            label="Email"
            defaultValue=""
            variant="filled"
            className="w-full"
          />
        </div>
        <div className="mb-3">
          <FormControl
            variant="filled"
            sx={{ minWidth: 120 }}
            className="w-full"
          >
            <InputLabel id="demo-simple-select-filled-label">
              Payment provider
            </InputLabel>
            <Select
              labelId="demo-simple-select-filled-label"
              id="demo-simple-select-filled"
              value={age}
              onChange={handleChange}
            >
              <MenuItem value={"paypal"}>Paypal</MenuItem>
              <MenuItem value={"ach"}>ACH</MenuItem>
              <MenuItem value={"wise"}>Wise</MenuItem>
            </Select>
          </FormControl>
        </div>
        <div className="flex justify-center">
          <Button variant="contained" className="bg-blue-700">
            Save
          </Button>
        </div>
      </div>
    </div>
  );
};

export default AffiliateLogin;
