import React from "react";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
} from "chart.js";
import { Line } from "react-chartjs-2";

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
);

const LineChart = () => {
  const userData = [
    { id: 1, year: 2016, userGain: 120, userLost: 7 },
    { id: 2, year: 2017, userGain: 176, userLost: 23 },
    { id: 3, year: 2018, userGain: 279, userLost: 93 },
    { id: 4, year: 2019, userGain: 522, userLost: 193 },
    { id: 5, year: 2020, userGain: 1030, userLost: 823 },
  ];
  const options = {
    responsive: true,
    plugins: {
      legend: {
        position: "top",
      },
      title: {
        display: true,
        text: "Chart.js Line Chart",
      },
    },
  };

  const data = {
    labels: userData.map((item) => item.year),
    datasets: [
      {
        label: "User Gain",
        data: userData.map((item) => item.userGain),
        borderColor: "rgb(255, 99, 132)",
        backgroundColor: "rgba(255, 99, 132, 0.5)",
      },
      {
        label: "User Lost",
        data: userData.map((item) => item.userLost),
        borderColor: "rgb(53, 162, 235)",
        backgroundColor: "rgba(53, 162, 235, 0.5)",
      },
    ],
  };

  return (
    <>
      <Line data={data} />
    </>
  );
};

export default LineChart;
