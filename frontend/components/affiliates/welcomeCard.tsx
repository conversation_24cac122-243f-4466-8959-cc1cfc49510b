import { But<PERSON> } from "@mui/material";
import React, { useState } from "react";
import Image from "next/image";
import Gift from "public/images/affiliates/gifts.svg";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store";
import { affiliateActions } from "@/slices/affiliates/affiliateSlice";

const WelcomeCard = () => {
  const data = useSelector((state: RootState) => state.affiliate);
  const dispatch = useDispatch();
  return (
    <div className="mt-10">
      <div className=" mx-auto h-fit bg-slate-200 px-7 py-5 rounded-md shadow-sm shadow-slate-500">
        <div className="flex justify-between pt-3 pb-5 px-3">
          <div className="font-bold flex items-center">
            Invite your friends and get 30% commision! Join the referrel program
          </div>
          <div className="">
            <Button
              variant="contained"
              className="bg-green-700 hover:bg-green-600"
              onClick={() => {
                dispatch(affiliateActions.setupInProgressChanged(true));
              }}
            >
              Apply
            </Button>
          </div>
        </div>
        <hr className="bg-white h-[2px]" />
        <div className="flex justify-between px-3 py-5 items-center">
          <div className=" text-[16px]">
            <div className="">
              We are trying to grow up Capri as fast as possible to offer you
              the best platform!<br></br> Help us to achieve this goal by
              inviting friends.
            </div>
            <div className="mt-5">
              We offer 30% commision every-time you refer a friend that join us!
            </div>
          </div>
          <div className="">
            <Image
              src={Gift}
              alt={"gift"}
              className="m-auto sm:h-[120px] h-[110px] w-fit"
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default WelcomeCard;
