import { useState, useEffect } from "react";
import { useDispatch } from "react-redux";
import { useRouter } from "next/router";
import { useUser } from "@auth0/nextjs-auth0/client";
import { integrationModalActions } from "@/slices/settings/integrations/integrationList";
import { closeModal } from "@/helpers/modals/closeModal";
import { fetchPostRequest } from "@/requests/integrations/http/post/fetchPostRequest";
import { createPostRequest } from "@/requests/integrations/http/post/createPostRequest";
import { updatePostRequest } from "@/requests/integrations/http/post/updatePostRequest";
import {
  IHttpPostRequest,
  IHttpResponse,
  Tab,
} from "@/components/settings/integrations/http/httpPost/HttpPostComponent";
import { sendPostRequest } from "@/components/settings/integrations/http/http/send-post-request";

const defaultState: IHttpPostRequest = {
  name: "User Creation API",
  url: "https://jsonplaceholder.typicode.com/users",
  queryParameters: [
    {
      name: "format",
      value: "json",
      description: "Response format"
    }
  ],
  pathVariables: [],
  headers: [
    {
      name: "Accept",
      value: "application/json",
      description: "Accept JSON responses"
    }
  ],
  bodyParameters: [
    {
      name: "name",
      value: "John Doe",
      description: "User's full name"
    },
    {
      name: "email",
      value: "<EMAIL>",
      description: "User's email address"
    },
    {
      name: "phone",
      value: "555-0123",
      description: "User's phone number"
    }
  ],
  bodyType: "application/json",
  authorizationType: "No Auth",
  responseBuilder: "User created successfully: {name} with email {email}",
};

const useHttpPostIntegration = (accountId?: string) => {
  const dispatch = useDispatch();
  const router = useRouter();
  const user = useUser();
  const [httpRequest, setHttpRequest] = useState<IHttpPostRequest>(defaultState);
  const [currentTab, setCurrentTab] = useState<Tab>("Params");
  const [response, setResponse] = useState<IHttpResponse | null>(null);
  const [loading, setLoading] = useState(false);
  const [saveLoading, setSaveLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [initialLoading, setInitialLoading] = useState(!!accountId);
  const orgId = localStorage.getItem("orgId") as string;

  useEffect(() => {
    const loadInitialData = async () => {
      if (accountId) {
        try {
          const data = await fetchPostRequest({
            body: { orgId, accountId },
            dispatch,
          });
          setHttpRequest(data.data.data);
        } catch (err) {
          console.error("Failed to load initial data:", err);
          setError("Failed to load initial request data. Using default state.");
        } finally {
          setInitialLoading(false);
        }
      }
    };

    loadInitialData();
  }, [accountId, dispatch, orgId]);

  const handleSendRequest = async () => {
    setLoading(true);
    setError(null);
    try {
      const result = await sendPostRequest(httpRequest, dispatch);
      setResponse(result as IHttpResponse | null);
      setCurrentTab("Response");
    } catch (err) {
      setError(
        err instanceof Error ? err.message : "An unknown error occurred"
      );
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async () => {
    setSaveLoading(true);
    try {
      if (accountId && orgId) {
        const response = await updatePostRequest({
          accountId,
          body: { ...httpRequest, orgId },
          dispatch,
        });
        if (response?.status === 200) {
          dispatch(
            integrationModalActions.integrationUpdated({
              accountId,
              integration: response.data,
            })
          );
          closeModal(router);
        }
      } else {
        const response = await createPostRequest({
          body: {
            ...httpRequest,
            author: user.user?.name ?? "No author",
            name: httpRequest.name ?? httpRequest.url,
            orgId,
          },
          dispatch,
        });
        if (response?.status === 201) {
          dispatch(
            integrationModalActions.integrationAccountAdded(response?.data.data)
          );
          closeModal(router);
        }
      }
    } catch (err) {
      setError(
        err instanceof Error ? err.message : "An unknown error occurred"
      );
    } finally {
      setSaveLoading(false);
    }
  };

  return {
    httpRequest,
    setHttpRequest,
    currentTab,
    setCurrentTab,
    response,
    loading,
    saveLoading,
    error,
    initialLoading,
    handleSendRequest,
    handleSubmit,
  };
};

export default useHttpPostIntegration; 