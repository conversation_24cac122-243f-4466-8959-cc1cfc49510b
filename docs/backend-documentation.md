# Backend Structure Documentation

## Public response, conversations Public API 
- backend-nest/src/organization/services/public/v1.service.ts -
  contains the code for save conversation, get conversation, and message response public API

### agent public api
- agent/public-api/ - contains endpoints for external/public API access to agent functionality

## GHL API, N<PERSON>s, <PERSON>wilio, Vapi, Hubspot, Chat HQ, Slack API services
These files contain the methods that defines the request schema for API calls to external services.

- api-client/api-client.module.ts - Main module that provides various API client services (GHL, Podium, ChatHQ etc.) as global providers
- api-client/lib/ - contains shared library code used by multiple API clients
- api-client/services/ - Contains individual service implementations for different API integrations
  - services/apiclient.service.ts - Base API client service with common functionality
  - services/ghl-apis/ghl-apis.service.ts - Service for GoHighLevel API integration
  - services/podium/podium-api.service.ts - Service for Podium API integration
  - services/chat-hq/chat-hq.service.ts - Service for ChatHQ API integration
  - services/v2migration/v2migration.service.ts - Service handling API migration from v2 to v3
  - services/hubspot/hubspot-api.service.ts - Service for HubSpot API integration
  - services/slack-api/slack-api.service.ts - Service for Slack API integration
  - services/vapi/vapi-api.service.ts - Service for VAPI (Voice API) integration
  - backend-nest/src/api-client/services/nylas/nylas-api.ts - Service for Nylas Outlook API integration
  - backend-nest/src/api-client/services/twilio/twilio-api.service.ts : Service for twilio API integration

## Outlook Integration

backend-nest/src/connections/outlook/outlook.controller.ts :
contains implementation of outlook integration

## Agent Module Structure

- agent/agent.controller.ts - Main controller handling agent CRUD operations, prompts, triggers, and other agent management endpoints
- agent/agent.module.ts - NestJS module that organizes and provides agent-related services and controllers
- agent/agent.service.ts - Core service implementing agent business logic and data operations
- agent/dto/ - Contains Data Transfer Objects for agent operations (get, create, update, delete etc.)
- agent/file-processor.ts - Handles processing of uploaded files (PDF, Word, Excel etc.) for agent knowledge
- agent/file-upload.service.ts - Service for managing file uploads and processing queues
- agent/qnaMap.ts - Utility for mapping Q&A data structures for quick create agent

## Voice Module Structure

- voice/voice.controller.ts - Main controller handling voice API endpoints like activation, calls, and assistant management
- voice/voice.service.ts - Core service implementing voice functionality and business logic
- backend-nest/src/voice/webhooks/webhooks.controller.ts - Handles Voice Webhooks for handling end of the call report, assistant request.
- voice/vapi/ - Contains integration code for VAPI including assistants, tools, and phone services
- voice/webhooks/ -  handles webhook callbacks and events from voice services

## Externals Module Structure

This module contains the Uphex integration code.

- controllers/application/v1-application.controller.ts - handles uphex 
- externals/dtos/ -  contains Data Transfer Objects for external API requests/responses
- services/application/v1-application.service.ts -  This function is used to process live events from a Uphex server, to build uphex context taking the Ad campaign data into account.

## Vector Module Structure

Contains the code to use the pinecone vector db and tiktoken.

- services/vector.service.ts - Main service for vector operations and business logic
- services/pinecone/pinecone.service.ts - Service for Pinecone vector database integration
- services/tiktoken.service.ts - Service for token counting/processing using Tiktoken


## Migration Module Structure

- migration/migration.module.ts - Main module for handling data migrations between different versions or systems, integrating with agent, organization and vector services
- migration/controllers/ - Contains API controllers for migration operations
  - controllers/migration/migration.controller.ts -  handles migration API endpoints and triggers
- migration/dto/ -  contains Data Transfer Objects for migration requests and responses
- services/migration.service.ts - Main service handling logic for embedding storage, embedding creation, etc

## Organization Module Structure

This module contains the core logic to handle organization resources like AI provider, channels, integrations, static data, GHL webhook, knowledge sources.

  - organization/controllers/ - Contains all organization-related API controllers
  - controllers/organization.controller.ts - Main organization management controller
  - controllers/conversations/v1-conversations.controller.ts - Contains the processes that serve the conversations page inside an agent.
  - organization/services/ - Contains business logic services for organization functionality

### Ai providers

  - controllers/aiProvider/aiProvider.controller.ts - handles AI provider integration endpoints
  - services/aiProviders/aiProvider.service.ts -  implements AI provider integration logic

### Channels

  - controllers/calendar-availablity/org-availablity.controller.ts - manages organization calendar availability settings
  - controllers/channels/ - Handles various communication channel integrations
  - channels/channels.controller.ts - Contains the code to connect Slack channel, get all the organization's channel, to connect a GHL channel, to delete a channel, to get a mapped agent for channel, to get a channel list, to edit an agent's channel.
  - channels/chat-hq/chat-hq.controller.ts - manages ChatHQ integration
  - channels/ghl/ghl.controller.ts - Contains the code to process GHL webhook to update contact custom fields to search conversations to get messages by conversation id.
  - channels/hubspot/hubspot.controller.ts - manages HubSpot CRM integration
  - channels/podium/podium.controller.ts - handles Podium review platform integration
  - controllers/webhook/webhook.controller.ts -  handles webhook-related endpoints


- services/calendar-availablity/calendar-availablity.service.ts -  manages calendar availability logic
  - services/channels/ - Implements channel integration services
    - channels/channels.service.ts - Main channel service
    - channels/chat-hq/chat-hq.service.ts -  handles ChatHQ integration logic
    - channels/ghl/ghl.service.ts -  implements GoHighLevel CRM logic
    - channels/ghl/ghl-helpers.service.ts -  provides helper functions for GHL
    - channels/ghl/ghl-webhook-helpers.service.ts -  handles GHL webhook processing
    - channels/ghl/ghl-webhook-conversation.service.ts -  manages GHL conversation webhooks
    - channels/hubspot/hubspot.service.ts -  implements HubSpot CRM logic
    - channels/podium/podium.service.ts -  handles Podium review platform logic
    - channels/slack/slack.service.ts -  manages Slack integration


### Static data

  - controllers/data/ - Manages static data resources. Holds function like get all static data, delete a static data, get a static data, edit a data source.
    - data/QnA/QnA.controller.ts - handles Q&A knowledge base endpoints
    - data/google-docs/google-docs.controller.ts - manages Google Docs integration
    - data/pdf/pdf.controller.ts - handles PDF processing endpoints
    - data/site/site.controller.ts - manages website data operations

- services/data/ - Implements data-related services
    - data/QnA/QnA.service.ts -  handles Q&A knowledge base logic
    - data/data.service.ts - Main data operations service
    - data/google-docs/goole-docs.service.ts -  manages Google Docs integration
    - data/pdf/pdf.service.ts -  implements PDF processing
    - data/site/site.service.ts -  handles website data operations

  - controllers/ghl_data.controller.ts - handles GoHighLevel data operations, GHL webhook processing

### Integrations

  - integrations/acutiy/acuity.controller.ts - handles Acuity scheduling integration
  - integrations/calendly/calendly.controller.ts - manages Calendly scheduling
  - integrations/ghl-calendar/ghl-calendar.controller.ts -  handles GoHighLevel calendar
  - integrations/google-calendar/google-calendar.controller.ts -  manages Google Calendar
  - integrations/google-sheet/google-sheet.controller.ts -  handles Google Sheets
  - integrations/http-get/http-get.controller.ts - manages HTTP GET API integrations
  - integrations/integrations.controller.ts - Main integrations controller


- services/integrations/ - Manages third-party integration services
    - integrations/acuity/acuity.service.ts -  handles Acuity scheduling logic
    - integrations/calendly/calendly.service.ts -  implements Calendly integration
    - integrations/ghl-calendar/ghl-calendar.service.ts -  manages GoHighLevel calendar
    - integrations/google-calendar/google-calendar.service.ts -  handles Google Calendar
    - integrations/http-get/http-get.service.ts -  implements HTTP GET API logic
    - integrations/integration.service.ts - Main integrations service
    - integrations/sheet/sheet.service.ts -  manages spreadsheet integrations
  - services/organization.service.ts - Main organization management service

## Multiple inbound

- backend-nest/src/organization/services/channels/ghl/ghl.service.ts :
contains the processing logic.

- backend-nest/src/utility/services/scheduler.service.ts:
contains the functions to handle multiple inbound features - create cloud tasks, which agents to process and which to schedule for multiple inbound.

## Follow up

- backend-nest/src/utility/services/scheduler.service.ts:
`scheduleFollowUp` function helps in checking if the current message should be scheduled for follow up or not.

- backend-nest/src/organization/services/channels/ghl/ghl-webhook-helpers.service.ts:
contains `processFollowUpOnInbound` which checks if the current follow up request should be allowed for processing to generate a response or should it be aborted given the followup conditions.

- backend-nest/src/session/session.service.ts:
  `evaluateFollowUp` evaluates whether a followup prompt evaluates to true or false

## Uphex

- backend-nest/src/mongo/service/contextData/contextData.service.ts:
`getUphexContextData` retrieves uphex context for injection into response generation prompt in ghl.service.ts


## Webhook for hubspot, podium, outlook

- backend-nest/src/webhook-v2/v2-webhooks.controller.ts:  This module contains the code to handle the webhook for HubSpot, Podium and Outlook and implements the processes that process the queries coming in from these channels and responds to them.
 * Note. GHL webhook is not implemented here. It is implemented inside ghl.controller.ts.
- /Users/<USER>/Desktop/work/capri-new-website/backend-nest/src/webhook-v2/outlook/outlook-webhooks.service.ts: Contains the code to process the Outlook Outbound Webbook.