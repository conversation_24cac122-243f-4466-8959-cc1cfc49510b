{"testRequests": [{"name": "JSONPlaceholder Create Post", "description": "Test creating a new post using JSONPlaceholder API", "url": "https://jsonplaceholder.typicode.com/posts", "bodyParameters": [{"name": "title", "value": "Sample Post Title", "description": "The title of the post"}, {"name": "body", "value": "This is the content of the sample post created for testing purposes.", "description": "The content/body of the post"}, {"name": "userId", "value": "1", "description": "The ID of the user creating the post"}], "headers": [{"name": "Content-Type", "value": "application/json"}], "queryParameters": [], "bodyType": "application/json"}, {"name": "HTTPBin Echo Test", "description": "Test POST request that echoes back the sent data", "url": "https://httpbin.org/post", "bodyParameters": [{"name": "name", "value": "Test User", "description": "User name"}, {"name": "email", "value": "<EMAIL>", "description": "User email"}, {"name": "message", "value": "Hello from Capri POST test!", "description": "Test message"}], "headers": [{"name": "Content-Type", "value": "application/json"}, {"name": "User-Agent", "value": "Capri-Test/1.0"}], "queryParameters": [{"name": "test", "value": "true", "description": "Test parameter"}], "bodyType": "application/json"}, {"name": "Form Data Test", "description": "Test POST request with form-encoded data", "url": "https://httpbin.org/post", "bodyParameters": [{"name": "username", "value": "testuser", "description": "Username field"}, {"name": "password", "value": "testpass123", "description": "Password field"}, {"name": "remember", "value": "true", "description": "Remember me checkbox"}], "headers": [{"name": "Content-Type", "value": "application/x-www-form-urlencoded"}], "queryParameters": [], "bodyType": "application/x-www-form-urlencoded"}], "curlExamples": [{"name": "Test JSONPlaceholder", "curl": "curl -X POST http://localhost:3000/session/test/httpPost \\\n  -H \"Content-Type: application/json\" \\\n  -d '{\n    \"url\": \"https://jsonplaceholder.typicode.com/posts\",\n    \"bodyParameters\": [\n      {\"name\": \"title\", \"value\": \"Sample Post\", \"description\": \"Post title\"},\n      {\"name\": \"body\", \"value\": \"Test content\", \"description\": \"Post content\"},\n      {\"name\": \"userId\", \"value\": \"1\", \"description\": \"User ID\"}\n    ],\n    \"headers\": [\n      {\"name\": \"Content-Type\", \"value\": \"application/json\"}\n    ]\n  }'"}, {"name": "Test HTTPBin Echo", "curl": "curl -X POST http://localhost:3000/session/test/httpPost \\\n  -H \"Content-Type: application/json\" \\\n  -d '{\n    \"url\": \"https://httpbin.org/post\",\n    \"bodyParameters\": [\n      {\"name\": \"message\", \"value\": \"Hello World\", \"description\": \"Test message\"}\n    ],\n    \"headers\": [\n      {\"name\": \"Content-Type\", \"value\": \"application/json\"}\n    ]\n  }'"}], "instructions": {"setup": ["1. Ensure your backend server is running on port 3000", "2. The test endpoint is available at /session/test/httpPost", "3. Send POST requests with the sample data above"], "testing": ["1. <PERSON><PERSON> one of the curl examples above", "2. Run it in your terminal", "3. Check the response for success/failure", "4. Look at the server logs for detailed request information"], "debugging": ["1. Check server logs for detailed request/response information", "2. Verify the target URL is accessible", "3. Ensure proper JSON formatting in request body", "4. Check for any network connectivity issues"]}}