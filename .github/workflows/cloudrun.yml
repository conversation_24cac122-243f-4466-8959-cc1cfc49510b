name: Deploy to Cloud Run

env:
  SERVICE_NAME: backend-nest
  PROJECT_ID: dwy-master
  DOCKER_IMAGE_URL: us-central1-docker.pkg.dev/dwy-master/cloud-run-source-deploy/backend-nest

on:
  push:
    branches:
      - beta
      - feat/docker
  pull_request:
    branches:
      - beta
      - feat/docker

jobs:
  dockerize-and-deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v2

      - name: Google Cloud Auth
        uses: "google-github-actions/auth@v2"
        with:
          credentials_json: "${{ secrets.GCP_SA_KEY }}"
          project_id: ${{ env.PROJECT_ID }}

      - name: Set up Cloud SDK
        uses: "google-github-actions/setup-gcloud@v2"

      - name: Configure Docker
        run: |
          gcloud auth configure-docker us-central1-docker.pkg.dev

      - name: Build and Push Docker Image
        run: |
          docker build -t ${{ env.DOCKER_IMAGE_URL }}:latest -f ./backend-nest/Dockerfile ./backend-nest
          docker push ${{ env.DOCKER_IMAGE_URL }}:latest

      - name: Deploy to Cloud Run
        run: |
          echo SERVICE_NAME $SERVICE_NAME
          gcloud run deploy $SERVICE_NAME \
            --image ${{ env.DOCKER_IMAGE_URL }}:latest \
            --platform managed \
            --region us-central1 \
            --allow-unauthenticated
