const { MongoClient, ObjectId } = require('mongodb');

async function run() {
  const url = process.env.MONGO_URI;
  const client = new MongoClient(url);

  try {
    // Connect to the MongoDB cluster
    await client.connect();

    // Specify the database to use
    const db = client.db('test');

    // Specify the collection to use
    const collection = db.collection('organization');
    const result = await collection.findOne({
      _id: new ObjectId('663b3e7a174c7a542b18b2c6'),
    });
    
  } catch (e) {
    
  } finally {
    // Close the connection to the MongoDB cluster
    await client.close();
  }
}

run().catch(console.dir);
