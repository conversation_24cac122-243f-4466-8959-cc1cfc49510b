import { Response } from 'express';
import { HttpException, HttpStatus } from '@nestjs/common';

export function handleException(response: Response, error: HttpException) {
  
  if (error instanceof HttpException) {
    const res = error.getResponse();
    if (typeof res === 'string') {
      return response.status(error.getStatus()).json({ message: res });
    } else {
      response.status(error.getStatus()).json(res);
    }
  } else {
    response
      .status(HttpStatus.INTERNAL_SERVER_ERROR)
      .json({ error: 'Internal Server Error' });
  }

  // throw new HttpException(
  //   (error.getResponse() as string) || 'Internal server error',
  //   error.getStatus() || 500,
  // );
}
