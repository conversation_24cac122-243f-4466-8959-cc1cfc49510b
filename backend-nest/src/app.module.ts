import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { OrganizationModule } from './organization/organization.module';
import { MongooseModule } from '@nestjs/mongoose';
import { UserModule } from './user/user.module';
import { ApiClientModule } from './api-client/api-client.module';
import { CredentialsModule } from './credentials/credentials.module';
import { AuthModule } from './auth/auth.module';
import * as dotenv from 'dotenv';
import { MongoModule } from './mongo/mongo.module';
import { AgentModule } from './agent/agent.module';
import { SessionModule } from './session/session.module';
import { VectorModule } from './vector/vector.module';
import { GcpModule } from './gcp/gcp.module';
import { MigrationModule } from './migration/migration.module';
import { BillingModule } from './billing/billing.module';
import { FallbackModule } from './fallback/fallback.module';
import { ConfigModule } from '@nestjs/config';
import { TokensModule } from './tokens/tokens.module';
import { AuthenticationMiddleware } from './middleware/authentication.middleware';
import { NotificationModule } from './notification/notification.module';
import { RequestService } from './request.service';
import { MyLogger } from './utils/logger';
import { ThrottlerModule } from '@nestjs/throttler';
import { CacheModule } from '@nestjs/cache-manager';
import { IframeGhlModule } from './iframe-ghl/iframe-ghl.module';
import { ScheduleModule } from '@nestjs/schedule';
import { FallbackService } from './fallback/fallback.service';
import { LoggerModule } from './logger/logger.module';
import { LlamaModule } from './llama/llama.module';
import { ExternalsModule } from './externals/externals.module';
import { CronJobsModule } from './cron-jobs/cron-jobs.module';
import { AppUpdatesModule } from './app-updates/app-updates.module';
import { RebillingModule } from './rebilling/rebilling.module';
import { GroqModule } from './groq/groq.module';
import { AiHistoryModule } from './ai_History_Record/aiHistory_module';
// import { AiHistoryService } from './ai_History_Record/aiHistory_service';
// import { MongoAiHistoryService } from './ai_History_Record/aiHistory_mongo_service';
import { FoldersModule } from './folders/folders.module';
import { AdminModule } from './admin/admin.module';
import { RedisOptions } from './config/redis';
import { UtilityModule } from './utility/utility.module';
import { HttpRequestModule } from './http-request/http-request.module';
import { VoiceModule } from './voice/voice.module';
import { FeatureFlagsModule } from './feature-flags/feature-flags.module';
import { BullModule } from '@nestjs/bull';
import { ConnectionsModule } from './connections/connections.module';
import { WebhookV2Module } from './webhook-v2/webhook-v2.module';
import { AgentTemplatesModule } from './agent-templates/agent-templates.module';
import { AgentSnapshotsModule } from './agent-snapshots/agent-snapshots.module';
import { AiModelsModule } from './ai-models/ai-models.module';
import { AuditLogModule } from './audit-log/audit-log.module';

dotenv.config();
@Module({
  imports: [
    OrganizationModule,
    MongooseModule.forRoot(process.env.MONGO_URI),
    UserModule,
    ApiClientModule,
    CredentialsModule,
    AuthModule,
    MongoModule,
    AgentModule,
    SessionModule,
    VectorModule,
    GcpModule,
    MigrationModule,
    BillingModule,
    FallbackModule,
    TokensModule,
    NotificationModule,
    ThrottlerModule.forRoot([
      {
        ttl: 1000,
        limit: 10,
      },
    ]),
    CacheModule.registerAsync(RedisOptions),
    ConfigModule.forRoot({ isGlobal: true }),
    IframeGhlModule,
    ScheduleModule.forRoot(),
    LoggerModule,
    LlamaModule,
    ExternalsModule,
    CronJobsModule,
    AppUpdatesModule,
    RebillingModule,
    GroqModule,
    AiHistoryModule,
    FoldersModule,
    AdminModule,
    UtilityModule,
    HttpRequestModule,
    VoiceModule,
    FeatureFlagsModule,
    BullModule.forRoot({
      redis: {
        host: process.env.REDIS_HOST,
        port: Number(process.env.REDIS_PORT),
        password: process.env.REDIS_PASSWORD,
      },
    }),
    ConnectionsModule,
    WebhookV2Module,
    AgentTemplatesModule,
    AgentSnapshotsModule,
    AiModelsModule,
    AuditLogModule,
  ],
  providers: [RequestService, MyLogger, FallbackService],
  exports: [],
  controllers: [],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(AuthenticationMiddleware).forRoutes('*');
  }
}
