import { Injectable } from '@nestjs/common';
import {
  BedrockRuntimeClient,
  InvokeModelCommand,
} from '@aws-sdk/client-bedrock-runtime';


@Injectable()
export class LlamaService {
  private extractYesOrNo(responseText: string): string {
    const yesMatch = responseText.match(/\byes\b/i);
    const noMatch = responseText.match(/\bno\b/i);

    if (yesMatch) {
      return 'yes';
    } else if (noMatch) {
      return 'no';
    } else {
      return 'no';
    }
  }

  async yesNoGenerateLlamaResponse(messages: any) {
    const userMessage = messages.find(
      (message) => message.role === 'user',
    ).content;
    const conversationText = userMessage
      .split('\n\n')
      .slice(1)
      .join('\n')
      .trim();
    const conversationFormatted = {
      conversation: conversationText,
    };

    // Extract knowledge source description from system messages
    const systemMessage = messages.find(
      (message) => message.role === 'system',
    ).content;
    const knowledgeSourceDescription = systemMessage
      .split('\n')
      .slice(3)
      .join('\n')
      .trim();

    const conversationString = JSON.stringify(conversationFormatted);
    const finalPromptContent = `
Question: Based on the user's conversation and the instruction to use the knowledge source, should the knowledge source be applied?
Conversation: ${conversationString}
Knowledge Source Description: ${knowledgeSourceDescription}

Please output 'yes' or 'no'. If 'no', then explain your answer:
`;

    
    

    const client = new BedrockRuntimeClient({
      region: 'us-east-1',
      credentials: {
        accessKeyId: '********************',
        secretAccessKey: '9m4x9qSmQWrc8iYpmm0Wm+yHv/QlB5RHNbBjN6bQ',
      },
    });
    const modelId = 'meta.llama2-70b-chat-v1';
    const payload = {
      prompt: finalPromptContent,
      temperature: 0.4,
      top_p: 0.9,
      max_gen_len: 500,
    };
    const command = new InvokeModelCommand({
      body: JSON.stringify(payload),
      contentType: 'application/json',
      accept: 'application/json',
      modelId,
    });

    try {
      const response = await client.send(command);
      const decodedResponseBody = new TextDecoder().decode(response.body);
      const responseBody = JSON.parse(decodedResponseBody);

      let result = responseBody.generation;

      

      const answer = this.extractYesOrNo(result);
      return answer;
    } catch (err) {
      
    }
  }
}
