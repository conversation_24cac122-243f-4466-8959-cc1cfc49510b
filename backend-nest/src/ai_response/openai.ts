// openai.service.ts
import Anthropic from '@anthropic-ai/sdk';
import { Injectable } from '@nestjs/common';
import Groq from 'groq-sdk';
import { Configuration, OpenAIApi } from 'openai';
import { CUSTOM_LLM, PROVIDERS } from 'src/lib/constant';
import { structure_aiHistory_action } from "../session/aiHistory_structure"
const { GoogleGenerativeAI } = require('@google/generative-ai');
import { MyLogger } from 'src/logger/logger.service';
import axios from 'axios';
interface IAiProviderAdvSettings {
  temperature: number;
  maxLength: number;
  frequencyPenalty: number;
}

@Injectable()
export class OpenaiService {
  constructor(
    private readonly myLogger: MyLogger,
  ){}
  async validateMessage(validatedMessages: any) {
    
    const rearrangedMessages = [];
    let lastRole = "user";
    let concatenatedMessage = '';

    validatedMessages.forEach((message, index) => {
      if (message.role !== lastRole) {
        if (concatenatedMessage !== '') {
          rearrangedMessages.push({ role: lastRole, content: concatenatedMessage.trim() });
          concatenatedMessage = '';
        }
        lastRole = message.role;
      }
      concatenatedMessage += message.content + ' ';

      // Check if it's the last message
      if (index === validatedMessages.length - 1) {
        rearrangedMessages.push({ role: message.role, content: concatenatedMessage.trim() });
      }
    });
     
    return rearrangedMessages;
  }

  async generateBotResponse(action_eventId, credentialId, action, agentData, messages, maxTokens, modelObj, aiProvAdvSett, retryCount=3): Promise<any> {
    let companyId = modelObj.companyId;

    try {
      let response;

      let advAiProvSett = {
        temperature: 0.4,
        maxLength: maxTokens,
        frequencyPenalty: 0,
      };

      if (aiProvAdvSett) {
        advAiProvSett.temperature = aiProvAdvSett.temperature;
        advAiProvSett.maxLength = aiProvAdvSett.maxLength;
        advAiProvSett.frequencyPenalty = aiProvAdvSett.frequencyPenalty;
      }

      if (
        companyId === PROVIDERS.OPENAI_PROVIDER ||
        companyId === PROVIDERS.OPENAI_HOSTED
      ) {
        const configuration = new Configuration({
          apiKey: modelObj.key,
        });
        const openai = new OpenAIApi(configuration);
        response = await openai.createChatCompletion({
          model: modelObj.model_name,
          messages: messages,
          max_tokens: advAiProvSett.maxLength || maxTokens,
          temperature:  advAiProvSett.temperature || 1,
          frequency_penalty: advAiProvSett.frequencyPenalty,
        });
      } else if (companyId === PROVIDERS.CLAUDE_PROVIDER) {
        let prompt = messages.shift();
        const anthropic = new Anthropic({
          apiKey: modelObj.key,
        });
        const validatedMessages = messages.filter(
          (message) => message.content.trim() !== '',
        );
        while (
          validatedMessages.length > 0 &&
          validatedMessages[0].role === 'assistant'
        ) {
          prompt.content += validatedMessages.shift().content;
        }
        let finalMessage = await this.validateMessage(validatedMessages);
        response = await anthropic.messages.create({
          model: modelObj.model_name,
          max_tokens: advAiProvSett.maxLength || maxTokens,
          messages: finalMessage,
          temperature: advAiProvSett.temperature,
          system: prompt.content,
        });
      } else if (companyId === PROVIDERS.GROQ_PROVIDER || companyId === PROVIDERS.GROQ_HOSTED) {
        const groq = new Groq({
          apiKey: modelObj.key,
        });

        messages = messages.filter(
          (message) =>
            !(
              message.role === 'user' &&
              (!message.content || message.content.trim() === '')
            ),
        );

        response = await groq.chat.completions.create({
          model: modelObj.model_name,
          max_tokens: advAiProvSett.maxLength || maxTokens,
          temperature: advAiProvSett.temperature,
          frequency_penalty: advAiProvSett.frequencyPenalty,
          messages: messages,
          stop: null,
          stream: false,
        });
      } else if (companyId === PROVIDERS.FIREWORKS_PROVIDER) {
        const url = 'https://api.fireworks.ai/inference/v1/chat/completions';
        const body = {
          model: modelObj.model_name,
          max_tokens: advAiProvSett.maxLength || maxTokens,
          temperature: advAiProvSett.temperature,
          frequency_penalty: advAiProvSett.frequencyPenalty,
          messages: messages.map((message) => ({
            role: message.role,
            content: message.content,
          })),
        };

        const headers = {
          Authorization: `Bearer ${CUSTOM_LLM.API_KEY}`,
          'Content-Type': 'application/json',
        };

        try {
          response = await axios.post(url, body, { headers });
        } catch (error) {
          throw error;
        }
      } else if (companyId === PROVIDERS.GOOGLE_GEMINI) {
        const generationConfig = {
          maxOutputTokens: advAiProvSett.maxLength || maxTokens,
          temperature: advAiProvSett.temperature,
          frequencyPenalty: advAiProvSett.frequencyPenalty,
        };

        const genAI = new GoogleGenerativeAI(modelObj.key);
        const model = genAI.getGenerativeModel({
          model: modelObj.model_name,
          generationConfig,
        });

        // Ensure the first message has the role 'user'
        const history = [];
        if (messages.length > 0) {
          history.push({
            role: 'user',
            parts: [{ text: messages[0].content }],
          });
        }

        history.push(
          ...messages.map((message, index) => ({
            role:
              index === 0 ? 'user' : message.role === 'user' ? 'user' : 'model',
            parts: [{ text: message.content }],
          })),
        );
        const chat = model.startChat({
          history,
          generationConfig,
        });

        const result = await chat.sendMessage(
          messages[messages.length - 1].content,
        );
        response = await result.response;
      } else if (companyId === PROVIDERS.OPENROUTER_PROVIDER) {
        const url = 'https://openrouter.ai/api/v1/chat/completions';

        const body = {
          model: modelObj.model_name,
          messages: messages,
          max_tokens: advAiProvSett.maxLength,
          temperature: advAiProvSett.temperature,
          frequency_penalty: advAiProvSett.frequencyPenalty,
        };

        const headers = {
          Authorization: `Bearer ${modelObj.key}`,
          'Content-Type': 'application/json',
        };

        try {
          response = await axios.post(url, body, { headers });
        } catch (error) {
          throw error;
        }
      } else {
        
      }

      let action_aiHistory_structure = null;

      if (action) {
        action_aiHistory_structure = await structure_aiHistory_action(
          agentData,
          modelObj,
          response,
          messages,
          action,
          credentialId,
          action_eventId,
        );
      }

      return { response, action_aiHistory_structure };
    } catch (error) {
      if (PROVIDERS.OPENAI_PROVIDER === companyId) {
        this.myLogger.error({
          message: `Status: ${error.response.status}`,
          context: 'SessionService.generateBotResponse',
        });
        this.myLogger.error({
          message: `${error.response.data.error.message}`,
          context: 'SessionService.generateBotResponse',
        });
      } else if (PROVIDERS.FIREWORKS_PROVIDER === companyId) {
        this.myLogger.error({
          message: `Status: ${error.response.status}`,
          context: 'SessionService.generateBotResponse',
        });
        this.myLogger.error({
          message: `${error.response.data.error}`,
          context: 'SessionService.generateBotResponse',
        });
      } else if (PROVIDERS.CLAUDE_PROVIDER === companyId) {
        this.myLogger.error({
          message: `Status: ${error.status}`,
          context: 'SessionService.generateBotResponse',
        });
        this.myLogger.error({
          message: `${error.error.error.message}`,
          context: 'SessionService.generateBotResponse',
        });
      } else if (PROVIDERS.GROQ_PROVIDER === companyId) {
        this.myLogger.error({
          message: `Status: ${error.status}`,
          context: 'SessionService.generateBotResponse',
        });
        this.myLogger.error({
          message: `${error.error.error.message}`,
          context: 'SessionService.generateBotResponse',
        });
      } else if (PROVIDERS.GOOGLE_GEMINI === companyId) {
        this.myLogger.error({
          message: `Status: ${error.status}`,
          context: 'SessionService.generateBotResponse',
        });
        this.myLogger.error({
          message: `${error.errorDetails[0].reason}`,
          context: 'SessionService.generateBotResponse',
        });
      } else if (PROVIDERS.OPENROUTER_PROVIDER === companyId) {
        this.myLogger.error({
          message: `Status: ${error.response?.status}`,
          context: 'SessionService.yesNoGenerateBotResponse',
        });
        this.myLogger.error({
          message: `${error.response?.data?.error || error.message}`,
          context: 'SessionService.yesNoGenerateBotResponse',
        });
      }
      if (
        error &&
        (error.response.status !== 401 || error.status !== 401) &&
        retryCount > 0
      ) {
        this.myLogger.error({
          message: `#45238409578230: Error code: ${error.response.status || error.status
            })... AND Error: ${JSON.stringify(error)}`,
          context: 'SessionService',
        });
        await new Promise((resolve) => setTimeout(resolve, 3000));
        return this.generateBotResponse(
          action_eventId,
          credentialId,
          action,
          agentData,
          messages,
          maxTokens,
          modelObj,
          aiProvAdvSett,
          retryCount - 1,
        );
      } else {
        this.myLogger.error({
          message: `#7850293475023: Error code: ${error.response.status || error.status
            })... AND Error:${JSON.stringify(error)}`,
          context: 'SessionService',
        });
        throw error;
      }
    }
  }

  async failsafeAiProvider(action_eventId, credentialId, action, agentData, messages, maxTokens, modelObj, aiProvAdvSett, maxRetries = 2, initialDelay = 1000) {
    let retryCount = 0;
    let delay = initialDelay;
   await this.myLogger.log({message: "Implementing failsafe service.", context: "FailSafe"})
   while (retryCount < maxRetries) {
     try {
       let response;
       let advAiProvSett = {
         temperature: 0.4,
         maxLength: maxTokens,
         frequencyPenalty: 0,
       };
       if (aiProvAdvSett) {
         advAiProvSett.temperature = aiProvAdvSett.temperature;
         advAiProvSett.maxLength = aiProvAdvSett.maxLength;
         advAiProvSett.frequencyPenalty = aiProvAdvSett.frequencyPenalty;
       }
       let companyId = modelObj.companyId;
       if (companyId === PROVIDERS.OPENAI_PROVIDER || companyId === PROVIDERS.OPENAI_HOSTED) {
         const configuration = new Configuration({
           apiKey: modelObj.key,
         });
         const openai = new OpenAIApi(configuration);
         response = await openai.createChatCompletion({
           model: modelObj.model_name,
           messages: messages,
           max_tokens: advAiProvSett.maxLength,
           temperature: advAiProvSett.temperature,
           frequency_penalty: advAiProvSett.frequencyPenalty,
         });
       } else if (companyId === PROVIDERS.CLAUDE_PROVIDER) {
         const anthropic = new Anthropic({
           apiKey: modelObj.key,
         });

         let prompt = messages.shift();

         const validatedMessages = messages.filter(
           (message) => message.content.trim() !== '',
         );

         while (
           validatedMessages.length > 0 &&
           validatedMessages[0].role === 'assistant'
         ) {
           prompt.content += validatedMessages.shift().content;
         }

         response = await anthropic.messages.create({
           model: modelObj.model_name,
           max_tokens: advAiProvSett.maxLength,
           temperature: advAiProvSett.temperature,
           messages: messages,
           system: prompt.content,
         });
       } else if (companyId === PROVIDERS.GROQ_PROVIDER || companyId === PROVIDERS.GROQ_HOSTED) {
         const groq = new Groq({
           apiKey: modelObj.key,
         });

         messages = messages.filter(
           (message) =>
             !(
               message.role === 'user' &&
               (!message.content || message.content.trim() === '')
             ),
         );

       response = await groq.chat.completions.create({
         model: modelObj.model_name,
         max_tokens: 2,
         temperature: advAiProvSett.temperature,
         messages: messages,
         stop: null,
         stream: false,
       });
      } else if (companyId === PROVIDERS.FIREWORKS_PROVIDER) {
        const url = 'https://api.fireworks.ai/inference/v1/chat/completions';
        const body = {
          model: CUSTOM_LLM.MODEL,
          messages: messages.map((message) => ({
            role: message.role,
            content: message.content,
          })),
        };

        const headers = {
          Authorization: `Bearer ${CUSTOM_LLM.API_KEY}`,
          'Content-Type': 'application/json',
        };

        response = await fetch(url, {
          method: 'POST',
          headers: headers,
          body: JSON.stringify(body),
        }).then((res) => res.json());
      } else if (companyId === PROVIDERS.GOOGLE_GEMINI) {
          const generationConfig = {
            maxOutputTokens: advAiProvSett.maxLength || maxTokens,
            temperature: advAiProvSett.temperature,
          };

          const genAI = new GoogleGenerativeAI(modelObj.key);
          const model = genAI.getGenerativeModel({
            model: modelObj.model_name,
            generationConfig,
          });

        // Ensure the first message has the role 'user'
          const history = [];
          if (messages.length > 0) {
            history.push({
              role: 'user',
              parts: [{ text: messages[0].content }],
            });
          }

          history.push(
            ...messages.map((message, index) => ({
              role:
                index === 0
                  ? 'user'
                  : message.role === 'user'
                  ? 'user'
                  : 'model',
              parts: [{ text: message.content }],
            })),
          );
        const chat = model.startChat({
          history,
          generationConfig: {
            maxOutputTokens: advAiProvSett.maxLength,
          },
        });

        const result = await chat.sendMessage(
          messages[messages.length - 1].content,
        );
        response = await result.response;
      } else if (companyId === PROVIDERS.OPENROUTER_PROVIDER) {
        const url = 'https://openrouter.ai/api/v1/chat/completions';
        const body = {
          model: modelObj.model_name,
          messages: messages,
          max_tokens: advAiProvSett.maxLength,
          temperature: advAiProvSett.temperature,
          frequency_penalty: advAiProvSett.frequencyPenalty,
        };

        const headers = {
          Authorization: `Bearer ${modelObj.key}`,
          'Content-Type': 'application/json',
        };

        response = await axios.post(url, body, { headers });
      } else {
        
      }
     const message_aiHistory_structure = await structure_aiHistory_action(agentData, modelObj, response, messages, action, credentialId, action_eventId);
     return {response, message_aiHistory_structure};
   } catch (error) {

     this.myLogger.error({
       message: `#78965934297542 error: ${error}`,
       context: 'FailSafe',
     });

     if (error.response) {
       if (error.response.status === 401) {
         this.myLogger.error({
           message: "Incorrect API key provided for AI.",
           context: 'SessionService',
         });
         this.myLogger.error({
           message: `Error code: ${
             error.response.status || error.status
           })... ${JSON.stringify(error.response)}`,
           context: 'SessionService',
         });
         break;
       } else if (error.response.status === 429) {
         this.myLogger.error({
           message: "Token length exceeded.",
           context: 'SessionService',
         });
         this.myLogger.error({
           message: `Error code: ${
             error.response.status || error.status
           })... ${JSON.stringify(error.response)}`,
           context: 'SessionService',
         });
         break;
       }
     } else if (error.status) {
       if (error.status === 401) {
         this.myLogger.error({
           message: "Incorrect API key provided for AI.",
           context: 'SessionService',
         });
         break;
       } else if (error.status === 429) {
         this.myLogger.error({
           message: "Token length exceeded.",
           context: 'SessionService',
         });
         break;
       }
     }

     retryCount++;

     if (retryCount < maxRetries) {
       this.myLogger.error({
         message: `Retrying after ${delay}ms. Retry attempt: ${retryCount}`,
         context: 'FailSafe',
       });
       await new Promise((resolve) => setTimeout(resolve, delay));
       delay *= 2; // Double the delay for the next retry
     } else {
       //implement failsafe method.
       this.myLogger.error({
         message: `Error generating action yes no response after maximum retries: ${JSON.stringify(error.response)}`,
         context: 'FailSafe',
       });
       throw error;
       }
     }
   }
 }
}
