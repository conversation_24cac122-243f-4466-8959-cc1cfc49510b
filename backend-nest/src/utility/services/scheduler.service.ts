/* 
contains the functions to handle multiple inbound features.
*/

import { Injectable } from '@nestjs/common';
import { TaskService } from 'src/gcp/services/task.service';
import { MyLogger } from 'src/logger/logger.service';
import { CacheService } from './cache.service';
import { CacheTTL } from '@nestjs/cache-manager';
import { FollowUpSchema } from 'src/mongo/schemas/agents/agents.schema';
const DEFAULT_MIN_WAIT = 2;
const DEFAULT_INCREMENT_PRODUCT = 30;
const DEFAULT_MAX_TIME_OUT = 10;


type FollowUpParam = {
  followUpConfig: FollowUpSchema;
  agentId: string;
  contactFollowUpInfo: {
    lastAttemptAt: Date;
    attemptCount: number;
    followUpTaskId?: string;
    contactId: string;
    resourceId: string;
  };
  payload: any;
}

@Injectable()
export class SchedulerService {
  constructor(
    private readonly logger: MyLogger,
    private readonly taskService: TaskService,
    private readonly cacheService: CacheService,
  ) {}

  async scheduleFollowUp({ followUpConfig, contactFollowUpInfo, payload, agentId }: FollowUpParam) {
    try {
      if (contactFollowUpInfo?.lastAttemptAt){
        /*
        if the current inbound event happened before the last scheduled follow-up is sent to the user. 
        Then delete the existing scheduled follow-up payload from Google Cloud task.
        */
        const lastFollowUpAt = new Date(contactFollowUpInfo.lastAttemptAt.getTime() + followUpConfig.duration * 60 * 1000);
        if (new Date() <= lastFollowUpAt && contactFollowUpInfo?.followUpTaskId) {
          this.logger.log({
            message: `Follow up already scheduled | contactId: ${contactFollowUpInfo?.contactId} | taskId ${contactFollowUpInfo.followUpTaskId} | deleting old follow up`,
            context: `Follow up event handling`
          });

          await this.taskService.deleteFollowUpTask(contactFollowUpInfo.followUpTaskId);
        }
      }

      // schedule a check for t+k min, where t = now, k = duration
      const newFollowUpTime = followUpConfig.duration * 60;// in seconds
      const taskUrl = `${process.env.CAPRI_OPERATIONS_ROUTING_FUNC}/request`;
      // Create a Google Cloud task with payload type as "followUp" which is used to identify it as a follow up request.
      // and the 'task' as 'outreach'. We will set the direction as inbound so that it is received back by our server and processed for response generation.
      const { correlationId, task } =
        await this.taskService.createWebhookMessageTask(
          taskUrl,
          newFollowUpTime,
          {
            url: process.env.BACKEND_URL + (payload.agent ? `/leadconnector/workflow/webhook` : `/leadconnector/webhook`),
            failsafeUrl: process.env.BACKEND_BACKUP_URL + (payload.agent ? `/leadconnector/workflow/webhook` : `/leadconnector/webhook`),
            payload: { ...payload, type: 'followUp', task: 'outreach', body: '', direction: 'inbound', prompt_id: (followUpConfig?.promptId ?? ((followUpConfig as any)?.prompt_id)), followupAgent: agentId },
          }
        );
      this.logger.log({
        message: `Follow up scheduled with correlationId : ${correlationId} | contactId: ${contactFollowUpInfo?.contactId} | task name ${task?.name}`,
        context: `Follow up event handling`
      });
      return {
        lastAttemptAt: new Date(),
        followUpTaskId: (task?.name ?? "").split('/').pop(),
      }
    } catch (error) {
      this.logger.error({
        message: `Error in scheduling follow up | err message - ${error?.message}`,
        context: `Follow up event handling`
      });
      return { error: error?.message }
    }
  }


  /*
  This function is used to create the multiple inbound task in Google Cloud task with the correct values for wait time
  */
  async handleWebhookEvents(agent, contactId, payload, existingRecord, minWait, incrementProduct, maxTimeOut) {
    try {
      let MIN_WAIT = minWait || DEFAULT_MIN_WAIT;
      let INCREMENT_PRODUCT = (incrementProduct || DEFAULT_INCREMENT_PRODUCT)/100 ;
      let MAX_TIME_OUT = maxTimeOut || DEFAULT_MAX_TIME_OUT;
      MIN_WAIT = Math.min(MIN_WAIT, MAX_TIME_OUT);
      
      const taskUrl = `${process.env.CAPRI_OPERATIONS_ROUTING_FUNC}/request`;

      this.logger.log({
        message: `Using Min wait time :${MIN_WAIT} , Max time out: ${MAX_TIME_OUT}, increment product: ${INCREMENT_PRODUCT}`,
        context: `${contactId}`
      })

      if (existingRecord) {
        // existing record
        let newDelay =
          MIN_WAIT + INCREMENT_PRODUCT * (existingRecord.delay || MIN_WAIT);
        newDelay = Math.min(newDelay, MAX_TIME_OUT);

        const { correlationId, task } =
          await this.taskService.createWebhookMessageTask(
            taskUrl,
            newDelay,
            {
              url: process.env.BACKEND_URL +  (payload.agent ? `/leadconnector/workflow/webhook`: `/leadconnector/webhook`),
              failsafeUrl: process.env.BACKEND_BACKUP_URL +  (payload.agent ? `/leadconnector/workflow/webhook`: `/leadconnector/webhook`),
              payload: { ...payload, delay: newDelay, },
              // "headers": {}
            }
          );

        await this.cacheService.saveWebhookMessagesInCache(
          contactId,
          agent._id.toString(),
          {
            correlationId,
            task: task.name,
            message: payload.body,
            delay: newDelay,
          },
        );
        this.logger.log({
          message: `WAIT TIME INCREMENTED, old delay ${existingRecord.delay} | new delay ${newDelay} | CORRELATION ID : ${correlationId}`,
        });
      } else {
        // new record
        const { correlationId, task } =
          await this.taskService.createWebhookMessageTask(
            taskUrl,
            MIN_WAIT,
            {
              url: process.env.BACKEND_URL + (payload.agent ? `/leadconnector/workflow/webhook`: `/leadconnector/webhook`),
              failsafeUrl: process.env.BACKEND_BACKUP_URL + (payload.agent ? `/leadconnector/workflow/webhook`: `/leadconnector/webhook`),
              payload: payload
              // "headers": {}
            },
          );

        await this.cacheService.saveWebhookMessagesInCache(
          contactId,
          agent._id.toString(),
          { correlationId, task: task.name, message: payload.body, delay: MIN_WAIT },
        );
        this.logger.log({
          message: `NEW RECORD CREATED, delay ${MIN_WAIT} | CORRELATION ID : ${correlationId} | TASK NAME : ${task.name}`,
          context: `${contactId}`
        });
      }
    } catch (error) {
      this.logger.error({
        message: `MULTIPLE INBOUND ERROR in evaluating the delay : ${error?.message} for AGENT ID : ${agent?._id?.toString()}`,
        context: `${contactId}`
      })
      return agent
    }
  }


  /**
   * This function determines which agents should be scheduled for multiple inbounds and which one should be processed for this turn, for the current process.
   */
  async handleMultipleInboundTask(contactId: string, agents, payload, triggerOverrides) {
    try {
      this.logger.log({
        message: `Starting handleMultipleInboundTask | ContactId: ${contactId} | Total agents: ${agents?.length || 0} | Payload task: ${payload?.task} | Has correlationId: ${!!payload?.correlationId}`,
        context: `${contactId}`
      });

      // agents to be processed, agents to be scheduled for multiple inbound
      const [processAgents, scheduleAgents] = (agents ?? []).reduce(
        (acc, curr) => {
          curr?.multipleInbound ? acc[1].push(curr) : acc[0].push(curr);
          return acc;
        },
        [[], []],
      );

      this.logger.log({
        message: `Agent categorization complete | ProcessAgents: ${processAgents.length} | ScheduleAgents: ${scheduleAgents.length}`,
        context: `${contactId}`
      });

      let scheduleAgentsCount = 0;
      
      this.logger.log({
        message: `Starting to process ${scheduleAgents.length} schedule agents`,
        context: `${contactId}`
      });

      for (const agent of scheduleAgents) {
        const agentId = (agent?._id ?? "").toString();
        this.logger.log({
          message: `Processing agent | AgentId: ${agentId} | Agent name: ${agent?.name || 'Unknown'}`,
          context: `${contactId}`
        });

        let triggerTask = triggerOverrides?.[agent?._id?.toString()]?.task;
        
        this.logger.log({
          message: `Trigger task analysis | TriggerTask: ${triggerTask} | PayloadTask: ${payload?.task}`,
          context: `${contactId}`
        });

        if (['outreach', 'evaluate'].includes(triggerTask) || ['outreach', 'evaluate'].includes(payload?.task)) {
          // if task type is custom we don't schedule it for inbound and just give it a pass
          this.logger.log({
            message: `Custom task type detected -> skipping multiple inbound scheduling | AgentId: ${agentId}`,
            context: `${contactId}`
          });
          processAgents.push(agent);
          continue
        }

        this.logger.log({
          message: `Fetching cached data for agent | AgentId: ${agentId}`,
          context: `${contactId}`
        });

        const cachedData = await this.cacheService.getWebhookMessagesFromCache(
          contactId,
          agentId,
        );

        this.logger.log({
          message: `Cached data retrieved | AgentId: ${agentId} | HasCachedData: ${!!cachedData} | CachedData: ${JSON.stringify(cachedData)}`,
          context: `${contactId}`
        });
  
        if (payload?.correlationId === undefined) {
          this.logger.log({
            message: `Processing as message webhook | AgentId: ${agentId}`,
            context: `${contactId}`
          });

          // message webhook
          let minWait = agent?.multipleInboundConfig.initialWait,
            incrementProduct = agent?.multipleInboundConfig.incrementBy,
            maxTimeOut = agent?.multipleInboundConfig.maxWait;

          this.logger.log({
            message: `Multiple inbound config values | AgentId: ${agentId} | minWait: ${minWait} | incrementProduct: ${incrementProduct} | maxTimeOut: ${maxTimeOut}`,
            context: `${contactId}`
          });

          this.logger.log({
            message: `Calling handleWebhookEvents | AgentId: ${agentId}`,
            context: `${contactId}`
          });

          let extraAgent = await this.handleWebhookEvents(
            agent,
            contactId,
            payload,
            cachedData,
            minWait,
            incrementProduct,
            maxTimeOut
          );

          if (extraAgent) {
            processAgents.push(extraAgent); 
            this.logger.log({
              message: `Agent moved to processAgents due to handleWebhookEvents failure | AgentId: ${agentId}`,
              context: `${contactId}`
            });
          } else {
            scheduleAgentsCount++;
            this.logger.log({
              message: `Agent successfully scheduled for multiple inbound | AgentId: ${agentId} | ScheduledCount: ${scheduleAgentsCount}`,
              context: `${contactId}`
            });
          }
        } else {
          this.logger.log({
            message: `Processing as task webhook | AgentId: ${agentId} | PayloadCorrelationId: ${payload?.correlationId}`,
            context: `${contactId}`
          });

          // task webhook
          if (cachedData) {
            this.logger.log({
              message: `Comparing correlation IDs | AgentId: ${agentId} | PayloadCorrelationId: ${payload?.correlationId} | CachedCorrelationId: ${cachedData.correlationId}`,
              context: `${contactId}`
            });

            // assuming only 1 instance of cached data exists for the (contactId, agentId)
            if (payload?.correlationId === cachedData.correlationId) {
              this.logger.log({
                message: `Correlation IDs match - processing agent | AgentId: ${agentId}`,
                context: `${contactId}`
              });

              processAgents.push(agent);
              
              this.logger.log({
                message: `Deleting cached webhook messages | AgentId: ${agentId}`,
                context: `${contactId}`
              });

              await this.cacheService.deleteCachedWebhookMessages(
                contactId,
                agentId,
              );

              this.logger.log({
                message: `Cached webhook messages deleted successfully | AgentId: ${agentId}`,
                context: `${contactId}`
              });
            } else {
              this.logger.log({
                message: `Correlation IDs do not match - skipping agent | AgentId: ${agentId} | Expected: ${cachedData.correlationId} | Received: ${payload?.correlationId}`,
                context: `${contactId}`
              });
            }
          } else {
            this.logger.log({
              message: `No cached data found for task webhook| AgentId: ${agentId}`,
              context: `${contactId}`
            });
          }

          this.logger.log({
            message: `Multiple inbound webhook processed | ContactId: ${contactId} | AgentId: ${agentId} | cached data exists: ${!!cachedData} | payload correlationId: ${
              payload?.correlationId
            }`,
          });
        }
        
        this.logger.log({
          message: `Completed processing agent | AgentId: ${agentId} | CurrentProcessAgentsCount: ${processAgents.length}`,
          context: `${contactId}`
        });
      }
  
      this.logger.log({
        message: `Completed processing all schedule agents | ContactId: ${contactId}`,
        context: `${contactId}`
      });

      this.logger.log({
        message: `Final summary - Total agents received: ${
          agents.length
        } | Initial processAgents: ${
          agents.length - scheduleAgents.length
        } | Schedule agents processed: ${
          scheduleAgents.length
        } | Final processAgents count: ${
          processAgents.length
        } | Successfully scheduled agents: ${scheduleAgentsCount} | Agents moved to process: ${
          processAgents.length - (agents.length - scheduleAgents.length)
        }`,
        context: `${contactId}`,
      });

      this.logger.log({
        message: `handleMultipleInboundTask completed successfully | ContactId: ${contactId} | Returning ${processAgents.length} agents for processing`,
        context: `${contactId}`
      });
  
      return processAgents;
    } catch (error) {
      this.logger.error({
        message: `MULTIPLE INBOUND ERROR in determining whether it's a new webhook or a scheduled task webhook | ContactId: ${contactId} | Error: ${error?.message} | Stack: ${error?.stack}`,
        context: `${contactId}`
      })
      
      this.logger.log({
        message: `Returning original agents array due to error | ContactId: ${contactId} | AgentCount: ${agents?.length || 0}`,
        context: `${contactId}`
      });
      
      return agents
    }
  }
}
