import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';
import { Inject, Injectable } from '@nestjs/common';

@Injectable()
export class CacheService {
  constructor(@Inject(CACHE_MANAGER) private cacheManager: Cache) {}
  async getData<T = any>(key: string): Promise<T> {
    const value = await this.cacheManager.get<T>(key);
    return value;
  }

  async postData(key: string, createData) {
    const { value, ttl = 300000  } = createData;
    await this.cacheManager.set(key, value, ttl);
  }

  async deleteData(key: string) {
    await this.cacheManager.del(key);
  }

  async saveWebhookMessagesInCache(
    contactId: string,
    agentId: string,
    payload,
  ) {
    const key = `webhook_${agentId}_${contactId}`;
    await this.cacheManager.set(key, JSON.stringify(payload));
  }

  async deleteCachedWebhookMessages(contactId: string, agentId: string) {
    const key = `webhook_${agentId}_${contactId}`;
    await this.cacheManager.del(key);
  }

  async getWebhookMessagesFromCache(contactId: string, agentId: string) {
    const key = `webhook_${agentId}_${contactId}`;
    const value = await this.cacheManager.get<string>(key);
    return value ? JSON.parse(value) : value;
  }
}
