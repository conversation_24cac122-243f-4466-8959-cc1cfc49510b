/**
 * Service for managing file uploads and processing queues
 */
import { InjectQueue } from '@nestjs/bull';
import { Injectable, Logger } from '@nestjs/common';
import { Queue } from 'bull';
import { MyLogger } from 'src/logger/logger.service';
import { AdditionalMetadata } from 'src/vector_db/types';

export type WebsiteActionType = 'website-scrap' | 'website-crawl';

export interface WebsiteProcessingJob {
  website: string; // The website URL
  actionType: WebsiteActionType;
  maxDepth?: number;
}

export interface WebsiteProcessingResult {
  id: string;
  website: string;
  actionType: WebsiteActionType;
}

@Injectable()
export class AgentFileUpload {
  private readonly serviceName = 'AgentFileUpload';

  constructor(
    private readonly myLogger: MyLogger,
    @InjectQueue('file-processing') private fileProcessingQueue: Queue,
    @InjectQueue('website-processing') private websiteProcessingQueue: Queue,
  ) {}

  onModuleInit() {
    this.myLogger.log({
      message: 'AgentFileUpload initialized',
      context: this.serviceName,
    });
    this.myLogger.log({
      message: `Queue name: ${this.fileProcessingQueue.name}`,
      context: this.serviceName,
    });

    this.fileProcessingQueue.on('completed', (job) => {
      this.myLogger.log({
        message: `Job ${job.id} has completed`,
        context: this.serviceName,
      });
    });

    this.fileProcessingQueue.on('failed', (job, err) => {
      this.myLogger.error({
        message: `Job ${job.id} has failed with error ${err.message}`,
        context: this.serviceName,
      });
    });

    this.fileProcessingQueue.on('error', (error) => {
      this.myLogger.error({
        message: `Queue error: ${error.message}`,
        context: this.serviceName,
      });
    });

    this.fileProcessingQueue.on('stalled', (job) => {
      this.myLogger.warn({
        message: `Job ${job.id} has stalled`,
        context: this.serviceName,
      });
    });
  }

  async processFiles(
    context: AdditionalMetadata,
    files: Express.Multer.File[],
  ) {
    try {
      this.myLogger.log({
        message: `Processing ${files.length} files`,
        context: this.serviceName,
      });

      const jobs = await Promise.all(
        files.map(async (file, index) => {
          try {
            this.myLogger.log({
              message: `Adding job for file ${index + 1}: ${file.originalname}`,
              context: this.serviceName,
            });

            const job = await this.fileProcessingQueue.add(
              'process-file',
              {
                context,
                filename: file.originalname,
                buffer: file.buffer.toString('base64'),
                mimetype: file.mimetype,
              },
              {
                removeOnComplete: true,
                removeOnFail: false,
                attempts: 5,
                backoff: {
                  type: 'exponential',
                  delay: 1000,
                },
              },
            );

            this.myLogger.log({
              message: `Job added to queue: ${job.id}`,
              context: this.serviceName,
            });

            // Log the current state of the queue
            const jobCounts = await this.fileProcessingQueue.getJobCounts();
            this.myLogger.log({
              message: `Current queue state: ${JSON.stringify(jobCounts)}`,
              context: this.serviceName,
            });

            return {
              id: job.id,
              fileName: file.originalname,
              mimeType: file.mimetype,
            };
          } catch (error) {
            this.myLogger.error({
              message: `Failed to add job to queue for file ${file.originalname}: ${error.message}`,
              context: this.serviceName,
            });
            throw error;
          }
        }),
      );

      this.myLogger.log({
        message: `All jobs added. Total jobs: ${jobs.length}`,
        context: this.serviceName,
      });

      return jobs;
    } catch (error) {
      this.myLogger.error({
        message: error?.message || 'Error adding the files for queuing',
        context: this.serviceName,
      });
      throw error;
    }
  }

  async processWebsite(
    context: AdditionalMetadata,
    websiteJob: WebsiteProcessingJob,
  ) {
    try {
      this.myLogger.log({
        message: `Adding job for website: ${websiteJob.website} with action: ${websiteJob.actionType}`,
        context: this.serviceName,
      });

      const job = await this.websiteProcessingQueue.add(
        'process-website',
        {
          context,
          ...websiteJob,
        },
        {
          removeOnComplete: true,
          removeOnFail: false,
          attempts: 3,
          backoff: {
            type: 'exponential',
            delay: 2000,
          },
          timeout: 300000,
          priority: websiteJob.actionType === 'website-crawl' ? 1 : 2,
        },
      );

      this.myLogger.log({
        message: `Website processing job added to queue: ${job.id}`,
        context: this.serviceName,
      });

      // Log queue state
      const jobCounts = await this.websiteProcessingQueue.getJobCounts();
      this.myLogger.log({
        message: `Current queue state: ${JSON.stringify(jobCounts)}`,
        context: this.serviceName,
      });

      return {
        id: job.id,
        website: websiteJob.website,
        actionType: websiteJob.actionType,
      };
    } catch (error) {
      this.myLogger.error({
        message: `Failed to add website processing job for ${websiteJob.website}: ${error.message}`,
        context: this.serviceName,
      });
      throw error;
    }
  }

  async getQueueHealth() {
    try {
      const jobCounts = await this.fileProcessingQueue.getJobCounts();
      const workers = await this.fileProcessingQueue.getWorkers();
      const isPaused = await this.fileProcessingQueue.isPaused();

      return {
        jobCounts,
        workerCount: workers.length,
        isPaused,
      };
    } catch (error) {
      this.myLogger.error({
        message: `Failed to get queue health: ${error.message}`,
        context: this.serviceName,
      });
      throw error;
    }
  }
}
