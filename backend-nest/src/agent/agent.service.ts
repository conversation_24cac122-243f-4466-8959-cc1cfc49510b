/**
 * Core service implementing agent business logic and data operations
 */
import {
  Injectable,
  Res,
  HttpStatus,
  Inject,
  forwardRef,
  BadRequestException,
  NotFoundException,
} from '@nestjs/common';
import { CreateAgentDto } from './dto/create-agent.dto';
import { Response } from 'express';
import { MongoAgentService } from 'src/mongo/service/agent/mongo-agent.service';
import { MongoOrganizationService } from 'src/mongo/service/organization/mongo-organization.service';
import { PushNewPromptDto } from './dto/push-prompt-dto';
import { EditPromptDto } from './dto/edit-prompt-dto';
import { EditActivePromptDto } from './dto/active-prompt-dto';
import { EditStatusDto } from './dto/edit-status-dto';
import { AddDatasourceDto } from './dto/add-datasource-dto';
import { EditDatasourceDto, GetDatasourceDto } from './dto/edit-action-dto';
import { SheetService } from 'src/organization/services/integrations/sheet/sheet.service';
import mongoose, { Types } from 'mongoose';
import { ObjectId } from 'mongodb';
import { AddTriggerDto, EditTriggerDto } from './dto/add-trigger-dto';
import { DeleteTriggerDto } from './dto/delete-trigger.dto';
import { UpdateStatusTriggerDto } from './dto/update-trigger-status.dto';
import { AiProviderService } from 'src/organization/services/aiProviders/aiProvider.service';
import { v4 as uuidv4 } from 'uuid';
import { CUSTOM_LLM, KINDS, PROVIDERS, modelMaps } from 'src/lib/constant';
import { ICreatePromptDto } from 'src/lib/global-interfaces';
import { ApiclientService } from 'src/api-client/services/apiclient.service';
import { qnaMap } from './qnaMap';
import { IQuickActionProp } from './dto/quickAgent.dto';
import { SiteService } from 'src/organization/services/data/site/site.service';
import { MongoCredentialsService } from 'src/mongo/service/credentials/mongo-credentials.service';
import { ExistingAgentDataDto } from 'src/mongo/dto/agent/create_duplicate_dto';
import { handleException } from 'helpers/handleException';
import { MongoConversationService } from 'src/mongo/service/conversations/mongo-conversations.service';
import {
  BedrockRuntimeClient,
  InvokeModelCommand,
} from '@aws-sdk/client-bedrock-runtime';
import { FoldersService } from 'src/folders/folders.service';
import { IHumanTakeover } from './dto/human-takeover.dto';
import { EditMainPromptInjectionDto } from './dto/mainPrompt_dto';
import { UpdateFailsafeAiProviderDto } from 'src/mongo/dto/agent/failsafe_dto';
import { AssistantsService } from 'src/voice/vapi/assistants/assistants.service';
import { ToolsService } from 'src/voice/vapi/tools/tools.service';
import { AgentFileUpload, WebsiteProcessingJob } from './file-upload.service';
import { getAugmentedQueryDto } from './dto/get-augmented-query.dto';
import { PineconeService } from 'src/vector_db/pinecone/pinecone.service';
import { openai } from 'src/vector_db/utils';
import OpenAI from 'openai-v4';
import { MyLogger } from 'src/logger/logger.service';
import { GhlApisService } from 'src/api-client/services/ghl-apis/ghl-apis.service';
import { MongoVoiceNumbersService } from 'src/mongo/service/voiceNumbers/voiceNumbers.service';
import { KnowledgeService } from 'src/voice/vapi/knowledge/knowledge.service';
import {
  AgentDocument,
  FAQ,
  JobType,
  NotificationSettings,
} from 'src/mongo/schemas/agents/agents.schema';
import { HandleResourceDto, MessageStatus } from './dto/handle-resourc.dto';
import axios from 'axios';
import { ChannelDTO } from './dto/agent-config.dto';
import { WebsiteCrawlDirect } from './agent';
import { UpdateContextLengthPayload } from './dto/update-context-length-dto';
import { FollowUpBodyDto } from './dto/follow-up.dto';
@Injectable()
export class AgentService {
  constructor(
    private readonly mongoAgentService: MongoAgentService,
    private readonly mongoOrganizationService: MongoOrganizationService,
    private readonly aiProviderService: AiProviderService,
    private readonly apiClientService: ApiclientService,
    private readonly siteService: SiteService,
    private readonly mongoCredentialService: MongoCredentialsService,
    private readonly mongoConversationService: MongoConversationService,
    private readonly foldersService: FoldersService,
    private readonly assistantService: AssistantsService,
    private readonly toolsService: ToolsService,
    private readonly fileUploadService: AgentFileUpload,
    private readonly pineconeService: PineconeService,
    private readonly myLogger: MyLogger,
    private readonly ghlApisService: GhlApisService,
    private readonly mongoVoiceNumberService: MongoVoiceNumbersService,
    private readonly knowledgeService: KnowledgeService,
  ) {}

  async handleNewFaq(agentId: string, faqs: FAQ[] = []) {
    try {
      if (!agentId || !faqs) {
        throw new Error('Agent ID and FAQs are required');
      }

      // Step 1: Retrieve agent data
      const agentData = await this.mongoAgentService.getAgent({ _id: agentId });
      if (!agentData) {
        throw new Error('Agent not found');
      }

      const { faqs: oldFaqs = [], orgId } = agentData;

      // Step 2: Delete old FAQs from Pinecone
      let idsToDelete: string[] = [];
      if (Array.isArray(oldFaqs) && oldFaqs.length > 0) {
        idsToDelete = [...new Set(oldFaqs.map((faq) => faq.id))];
        

        const deleteResult = await this.pineconeService.deleteWithMetadata(
          idsToDelete,
        );
        if (!deleteResult.success) {
          throw new Error('Failed to delete old FAQs from Pinecone');
        }
      }

      // Step 3: Add new records to Pinecone
      const upsertPromises = faqs.map((faq) => {
        const { id, ...faqWithoutId } = faq;
        return this.pineconeService.upsert(
          {
            orgId,
            accountId: id,
            agentId,
          },
          JSON.stringify(faqWithoutId),
        );
      });

      // Wait for all upserts to complete
      const upsertedDocuments = await Promise.all(upsertPromises);

      // Step 4: Update agent with new FAQs
      await this.mongoAgentService.updateAgent({ _id: agentId }, { faqs });

      return {
        success: true,
        faqs,
      };
    } catch (error) {
      
      throw new Error(
        error instanceof Error
          ? error.message
          : 'An unexpected error occurred while updating FAQs',
      );
    }
  }
  async createAgent(
    userId,
    createAgentDto: CreateAgentDto,
    @Res() response?: Response,
  ) {
    const { faqs = [], website, ...restOfCreateAgentDto } = createAgentDto;
    const actions = [];
    if (restOfCreateAgentDto.calendarDetails) {
      const { calendarDetails } = restOfCreateAgentDto;
      

      actions.push({
        actionId: uuidv4(),
        accountId: calendarDetails.accountId,
        providerName: calendarDetails.providerName,
        promptContent:
          'This calendar contains real-time availability for appointments. Read it to determine what dates and times are available for an appointment.',
        activity: 'read',
        accountName: calendarDetails.name,
        silent: false,
        isAdvancedSettings: false,
        advancedSettings: {
          maxTokensAllowed: 2000,
        },
        includeMainPromptDetails: {
          includeMainPrompt: false,
          mainPromptId: '',
        },
      });
      actions.push({
        actionId: uuidv4(),
        accountId: calendarDetails.accountId,
        providerName: calendarDetails.providerName,
        promptContent:
          'Add appointments to this calendar once the day and time for the appointment have been clearly established and confirmed.',
        activity: 'write',
        accountName: calendarDetails.name,
        silent: false,
        isAdvancedSettings: false,
        advancedSettings: {
          maxTokensAllowed: 2000,
        },
        includeMainPromptDetails: {
          includeMainPrompt: false,
          mainPromptId: '',
        },
      });
    }
    const newAgent = await this.mongoAgentService.createAgent(userId, {
      ...restOfCreateAgentDto,
      actions,
    });
    const agentId = newAgent._id?.toString();
    if (faqs) {
      await this.handleNewFaq(agentId, faqs);
    }
    if (website) {
      // await this.processWebsites(agentId, { website, actionType: JobType.WEBSITE_SCRAP, maxDepth: 0 })
      this.processWebsitesUsingApiCall(agentId, {
        website,
        actionType: JobType.WEBSITE_SCRAP,
        maxDepth: 3,
      });
    }
    // Get the organization that created the agent
    const organizationId = createAgentDto.orgId;

    // Update the organization by pushing the agent ID into the agent[] array
    await this.mongoOrganizationService.updateOrganization(
      { _id: organizationId },
      { $push: { agents: newAgent._id.toString() } },
    );
    //Add the agent to the folder
    await this.foldersService.addResourceToFolder({
      resourceId: newAgent._id?.toString(),
      type: 'agents',
      folderId: createAgentDto?.folder,
      organization: createAgentDto.orgId,
    });
    if (response) {
      return response.status(HttpStatus.CREATED).json({
        message: 'Agent Created Successfully',
        data: newAgent,
      });
    } else {
      return newAgent;
    }
  }

  async createDuplicateAgent(
    userId,
    id,
    createAgentDto: ExistingAgentDataDto,
    response: Response,
  ) {
    const dupAgent = await this.mongoAgentService.createDuplicateAgent(
      userId,
      id,
      createAgentDto,
    );
    if (dupAgent) {
      // Get the organization that created the agent
      const organizationId = dupAgent.orgId;

      // Update the organization by pushing the agent ID into the agent[] array
      await this.mongoOrganizationService.updateOrganization(
        { _id: organizationId },
        { $push: { agents: dupAgent._id } },
      );

      //Add the agent to the folder
      await this.foldersService.addResourceToFolder({
        resourceId: dupAgent._id?.toString(),
        type: 'agents',
        folderId: createAgentDto?.folder,
        organization: createAgentDto.orgId,
      });

      return response.status(201).json({
        message: 'Duplicate Agent Created Successfully.',
        data: dupAgent,
      });
    }
  }

  async getAgent(agentId: string, @Res() response: Response) {
    try {
      const agent = await this.mongoAgentService.getAgent({ _id: agentId });
      if (agent?.voiceConfig?.assistantId && agent?.voiceConfig?.enabled) {
        const assistant = await this.assistantService.getVapiAssistant({
          agent,
        });
        agent['voiceConfig']['fillerInjectionEnabled'] =
          assistant?.voice?.fillerInjectionEnabled;
      }
      return response.json({
        message: 'Agent fetched successfully',
        data: agent,
      });
    } catch (error) {
      
      return response.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        error: 'Internal Server Error',
      });
    }
  }

  async handleResourceStatus(agentId: string, payload: HandleResourceDto) {
    try {
      const {
        jobId,
        accountId,
        characterCount,
        status,
        fileName,
        jobType,
        textContent,
        failedReason,
        maxDepth = 1,
      } = payload;
      const agent = await this.mongoAgentService.getAgent({ _id: agentId });
      if (!agent) {
        throw new NotFoundException(`Agent with ID ${agentId} not found`);
      }
      const { orgId } = agent;
      if (status === 'success') {
        // upsert data to pinecone
        await this.pineconeService.upsert(
          { accountId, agentId, orgId },
          textContent,
        );
        // upload to vapi
        let voiceFile = await this.knowledgeService.uploadVapiFile({
          agentId,
          fileContent: textContent,
          fileName,
        });
        // put the job from processing to processed
        await this.mongoAgentService.updateAgent(
          { _id: agentId },
          {
            $pull: { processingJobs: { jobId } },
            $push: {
              processedFiles: {
                accountId,
                fileName,
                characterCount,
                jobType,
                voiceFileId: voiceFile.vapiFileId,
                maxDepth,
              },
            },
          },
        );
      } else if (status === 'failed') {
        //TODO : Handle for when the resouce processing returns a failed status
      }
      return {
        status: 'success',
      };
    } catch (error) {
      this.myLogger.log({
        message: error.message || 'Failed to handle resource status',
        context: 'handleResourceStatus',
      });
    }
  }
  async getAllAgentByOrganization(
    orgId: string,
    @Res() response: Response,
    onlyName: boolean = false,
  ) {
    try {
      const organization = await this.mongoOrganizationService.getOrganization({
        _id: orgId,
      });
      if (!organization) {
        throw response
          .status(HttpStatus.NOT_FOUND)
          .json({ error: 'Organization not found' });
      }

      const agentIds = organization.agents;

      const agents = (
        await Promise.all(
          agentIds.map((agentId) =>
            this.mongoAgentService.getAgent({ _id: new ObjectId(agentId) }),
          ),
        )
      ).filter((agent) => agent !== null); // Filter out null values

      let namedAgents: { _id: ObjectId; agentName: string }[];

      if (!!onlyName) {
        namedAgents = agents.map((agent) => {
          return {
            _id: agent._id,
            agentName: agent.agentName,
          };
        });
      }

      return response.json({
        message:
          'All Agents belonging to this organization fetched successfully',
        data: !!onlyName ? namedAgents : agents,
      });
    } catch (error) {
      
      return response.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        error: 'Internal Server Error',
      });
    }
  }

  async getPrompts(agentId: string) {
    const agent = await this.mongoAgentService.getAgent({ _id: agentId });
    const actions = agent.actions;
    return actions.map((action) => {
      return {
        actionId: action.actionId,
        accountId: action.accountId,
        accountName: action?.accountName,
        providerName: action.providerName,
        activity: action.activity,
        promptContent: action.promptContent,
      };
    });
  }

  async updateAgentName(
    agentId: string,
    agentName: string,
    @Res() response?: Response,
  ) {
    try {
      const updatedAgent = await this.mongoAgentService.updateAgentName(
        agentId,
        agentName,
      );
      if (updatedAgent?.voiceConfig?.assistantId) {
        await this.assistantService.updateVapiAssistant({
          agentId,
          assistantId: updatedAgent?.voiceConfig.assistantId,
          name: agentName,
        });
      }
      if (response) {
        return response.json({
          message: 'Agent name updated successfully',
        });
      }
    } catch (error) {
      
      if (response) {
        return response.status(500).json({ error: 'Internal Server Error' });
      } else throw new Error(error?.message || 'Error updating agent name');
    }
  }

  async updateAgentAIProvider(updateAiProviderDto, response: Response) {
    try {
      const updatedAgent = await this.mongoAgentService.updateAgentAIProvider(
        updateAiProviderDto,
      );
      return response.json({
        message: "Agent's AI Provider updated successfully",
      });
    } catch (error) {
      return response.status(500).json({ error: error });
    }
  }

  async updateFailSafeAgentAIProvider(
    updateFailSafeAiProviderDto: UpdateFailsafeAiProviderDto,
    response: Response,
  ) {
    try {
      const updatedAgent =
        await this.mongoAgentService.updateFailsafeAiProvider(
          updateFailSafeAiProviderDto.agentId,
          updateFailSafeAiProviderDto,
        );
      return response.json({
        message: "Agent's Failsafe AI Provider updated successfully",
      });
    } catch (error) {
      return response.status(500).json({ error: error });
    }
  }

  async pushNewPrompt(
    pushNewPromptDto: PushNewPromptDto,
    @Res() response?: Response,
  ) {
    try {
      const agent = await this.mongoAgentService.pushNewPrompt(
        pushNewPromptDto,
      );
      const lastIndex = agent.prompts.prompt.length;
      const currentActive = agent.prompts.currentActive;
      if (agent?.voiceConfig?.assistantId && agent?.voiceConfig?.enabled) {
        const { actions, toolIds, tools, vapiPrompt } =
          await this.assistantService.createVapiSystemPrompt(agent);
        await this.assistantService.updateVapiAssistant({
          agentId: agent._id.toString(),
          assistantId: agent?.voiceConfig.assistantId,
          mainPrompt: vapiPrompt,
          model: {
            model: agent?.voiceConfig?.model?.model,
            provider: agent?.voiceConfig?.model?.provider,
          },
          toolIds,
        });
      }

      if (response) {
        return response.status(200).json({
          message: 'New Prompt Pushed Successfully',
          currentActive: currentActive,
          prompt: agent.prompts.prompt[lastIndex - 1],
        });
      }

      return;
    } catch (error) {
      
      return response.status(500).json({ error: 'Internal Server Error' });
    }
  }

  async handleVariables(accountId: string) {
    this.myLogger.log({
      message: `All custom values from accountId: ${accountId} should be fetched`,
      context: accountId,
    });

    let organization = await this.mongoOrganizationService.getOrganization({
      'connections.channels.accountId': accountId,
    });
    if (!organization) {
      throw new NotFoundException(
        `Organization with accountId: ${accountId} not found`,
      );
    }

    let locationId = organization.connections.channels.find(
      (channel) => channel.accountId === accountId,
    )?.keyId;

    if (!locationId) {
      throw new NotFoundException(
        `LocationId for accountId: ${accountId} not found`,
      );
    }

    const cred = await this.mongoCredentialService.getCredential({
      keyId: locationId,
      kind: KINDS.GHL_CREDENTIAL,
    });
    if (cred?.creds?.accessToken == undefined) {
      this.myLogger.log({
        message: `Your leadconnector account with locationId: ${locationId} couldn't be found in our database`,
        context: `${accountId}`,
      });

      return;
    }

    let variables = await this.ghlApisService.getVariables({
      locationId: locationId,
      tokens: {
        access_token: cred.creds.accessToken,
        refresh_token: cred.creds.refreshToken,
      },
    });

    let transformedVariables: Array<{
      name: string;
      type: 'static' | 'dynamic';
      value?: string;
      providerName?: string;
      fieldKey?: string;
    }> = variables.map((variable) => ({
      name: variable.name,
      type: 'dynamic',
      value: variable.value,
      providerName: 'ghl',
      fieldKey: variable.fieldKey,
    }));

    const processedVariables = transformedVariables.map((variable) => {
      if (variable.type === 'dynamic' && variable.fieldKey) {
        variable.fieldKey = `{{agent.${variable.fieldKey}}}`;
      }
      return variable;
    });

    return processedVariables;
  }

  async handleImportAllAgentVariables(agentId: string, accountId: string) {
    // Get all the variables from the `accountId` and then save it as variables within the agent having ID `agentId`
    this.myLogger.log({
      message: `All custom values from accountId: ${accountId} should be inserted to agentId: ${agentId}`,
      context: 'handleImportAllAgentVariables',
    });

    let organization = await this.mongoOrganizationService.getOrganization({
      'connections.channels.accountId': accountId,
    });
    if (!organization) {
      throw new NotFoundException(
        `Organization with accountId: ${accountId} not found`,
      );
    }

    let locationId = organization.connections.channels.find(
      (channel) => channel.accountId === accountId,
    )?.keyId;

    if (!locationId) {
      throw new NotFoundException(
        `LocationId for accountId: ${accountId} not found`,
      );
    }

    const cred = await this.mongoCredentialService.getCredential({
      keyId: locationId,
      kind: KINDS.GHL_CREDENTIAL,
    });
    if (cred?.creds?.accessToken == undefined) {
      this.myLogger.log({
        message: `Your leadconnector account with locationId: ${locationId} couldn't be found in our database`,
        context: `${agentId}`,
      });

      return;
    }

    let variables = await this.ghlApisService.getVariables({
      locationId: locationId,
      tokens: {
        access_token: cred.creds.accessToken,
        refresh_token: cred.creds.refreshToken,
      },
    });

    let transformedVariables: Array<{
      name: string;
      type: 'static' | 'dynamic';
      value?: string;
      providerName?: string;
      fieldKey?: string;
    }> = variables.map((variable) => ({
      name: variable.name,
      type: 'dynamic',
      value: variable.value,
      providerName: 'ghl',
      fieldKey: variable.fieldKey,
    }));

    const processedVariables = transformedVariables.map((variable) => {
      if (variable.type === 'dynamic' && variable.fieldKey) {
        variable.fieldKey = `{{agent.${variable.fieldKey}}}`;
      }
      return variable;
    });

    let updatedAgent = await this.mongoAgentService.updateAgent(
      { _id: agentId },
      { $set: { variables: processedVariables } },
    );

    this.myLogger.log({
      message: `All custom values from accountId: ${accountId} inserted to agentId: ${agentId}`,
      context: 'handleImportAllAgentVariables',
    });

    return updatedAgent;
  }

  async addChannelDetails(agentId: string, channelDetails: ChannelDTO) {
    if (!channelDetails || !channelDetails?.account) return;
    const { account, isCreateFirstTrigger, isImportVariables } = channelDetails;
    if (isCreateFirstTrigger) {
      // Add the first trigger
      await this.addTriggers({
        active: false,
        agentId,
        data: {
          channel: 'SMS',
          task: 'respond',
          tagOption: 'hasTag',
          tagValue: 'capri',
          subaccount: account.accountId as string,
        },
        providerName: 'ghl',
        triggerName: 'First trigger',
      });
    }
    if (isImportVariables) {
      // Import all the variables from the channel
      await this.handleImportAllAgentVariables(
        agentId,
        channelDetails.account?.accountId,
      );
    }
  }

  async editPrompt(editPromptDto: EditPromptDto, @Res() response: Response) {
    try {
      const updatedAgent = await this.mongoAgentService.editPrompt(
        editPromptDto,
      );
      if (
        editPromptDto.promptId === updatedAgent?.prompts?.currentActive &&
        updatedAgent?.voiceConfig?.assistantId
      ) {
        const { actions, toolIds, tools, vapiPrompt } =
          await this.assistantService.createVapiSystemPrompt(updatedAgent);

        await this.assistantService.updateVapiAssistant({
          agentId: updatedAgent._id.toString(),
          assistantId: updatedAgent?.voiceConfig.assistantId,
          mainPrompt: vapiPrompt,
          toolIds,
          model: {
            model: updatedAgent?.voiceConfig?.model?.model,
            provider: updatedAgent?.voiceConfig?.model?.provider,
          },
        });
      }
      return response.status(200).json({
        message: "Agent's prompt updated successfully",
      });
    } catch (error) {
      
      return response.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        error: 'Internal Server Error',
      });
    }
  }

  async deletePrompt(
    agentId: string,
    promptId: string,
    @Res() response: Response,
  ) {
    const agentDetails = await this.mongoAgentService.getAgent({
      _id: agentId,
    });
    if (agentDetails.prompts.currentActive == promptId) {
      return response.status(400).json({
        message:
          'Cannot delete active prompt. Please select any other available prompts as active.',
      });
    } else {
      const deletedPrompt = await this.mongoAgentService.deletePrompt(
        agentId,
        promptId,
      );
      return response.status(200).json({
        message: 'Prompt deleted successfully',
      });
    }
  }

  async editActivePrompt(
    editActivePromptDto: EditActivePromptDto,
    @Res() response?: Response,
  ) {
    const updatedAgent = await this.mongoAgentService.editActivePrompt(
      editActivePromptDto,
    );
    if (updatedAgent?.voiceConfig?.assistantId) {
      const { actions, toolIds, tools, vapiPrompt } =
        await this.assistantService.createVapiSystemPrompt(updatedAgent);

      await this.assistantService.updateVapiAssistant({
        agentId: updatedAgent?._id?.toString(),
        assistantId: updatedAgent?.voiceConfig.assistantId,
        mainPrompt: vapiPrompt,
        toolIds,
        model: {
          model: updatedAgent?.voiceConfig?.model?.model,
          provider: updatedAgent?.voiceConfig?.model?.provider,
        },
      });
    }
    if (Response) {
      return response.json({
        message: 'Active Prompt updated successfully',
      });
    }
  }

  async addDatasources(
    addDatasourceDto: AddDatasourceDto,
    @Res() response?: Response,
  ) {
    // check for duplicate datasources
    const { agentId, action } = addDatasourceDto;

    const agentData = await this.mongoAgentService.getAgent({ _id: agentId });

    if (agentData) {
      if (
        [PROVIDERS.SLACK, PROVIDERS.GHL_EMAIL, PROVIDERS.SMS, PROVIDERS.GET, PROVIDERS.POST].includes(
          action.providerName,
        )
      ) {
        const agent = await this.mongoAgentService.addDatasources(
          addDatasourceDto,
        );

        const lastIndex = agent.actions.length;
        if (response) {
          return response.json({
            message: 'Action created successfully',
            action: agent.actions[lastIndex - 1],
          });
        } else {
          return {
            action: agent.actions[lastIndex - 1],
          };
        }
      }

      if (action.hasOwnProperty('jsonObjects')) {
        const agent = await this.mongoAgentService.addDatasources(
          addDatasourceDto,
        );

        const lastIndex = agent.actions.length;
        if (response) {
          return response.json({
            message: 'Action created successfully',
            action: agent.actions[lastIndex - 1],
          });
        } else
          return {
            action: agent.actions[lastIndex - 1],
          };
      } else {
        // reject adding duplicate data source except tag
        const actionElement = agentData.actions.filter(
          (actionItem) =>
            actionItem.accountId === action.accountId &&
            actionItem.activity === action.activity,
        );

        if (actionElement?.[0]) {
          if (response) {
            return response.status(HttpStatus.BAD_REQUEST).json({
              message: `Cannot add duplicate data source with same action`,
            });
          } else return `Cannot add duplicate data source with same action`;
        }

        const agent = await this.mongoAgentService.addDatasources(
          addDatasourceDto,
        );

        if (agent?.voiceConfig?.assistantId && agent?.voiceConfig?.enabled) {
          const { actions, toolIds, tools, vapiPrompt } =
            await this.assistantService.createVapiSystemPrompt(agent);

          await this.assistantService.updateVapiAssistant({
            assistantId: agent?.voiceConfig?.assistantId,
            agentId: agentId,
            mainPrompt: vapiPrompt,
            toolIds,
            model: {
              model: agent?.voiceConfig?.model?.model,
              provider: agent?.voiceConfig?.model?.provider,
            },
          });
        }

        const lastIndex = agent.actions.length;
        if (response) {
          return response.json({
            message: 'Action created successfully',
            action: agent.actions[lastIndex - 1],
          });
        } else
          return {
            action: agent.actions[lastIndex - 1],
          };
      }
    } else {
      
    }
  }

  async editDatasources(
    editDatasourceDto: EditDatasourceDto,
    @Res() response: Response,
  ) {
    try {
      const agent = await this.mongoAgentService.editDatasources(
        editDatasourceDto,
      );
      const updatedAction = agent.actions.find(
        (action) => action.actionId === editDatasourceDto.actionId,
      );

      if (
        updatedAction?.voice?.vapiToolId &&
        editDatasourceDto?.action?.promptContent
      ) {
        const { actions, toolIds, tools, vapiPrompt } =
          await this.assistantService.createVapiSystemPrompt(agent);
        await this.assistantService.updateVapiAssistant({
          agentId: editDatasourceDto.agentId,
          assistantId: agent.voiceConfig.assistantId,
          mainPrompt: vapiPrompt,
          toolIds,
          model: {
            model: agent?.voiceConfig?.model?.model,
            provider: agent?.voiceConfig?.model?.provider,
          },
        });
        await this.toolsService.updateTool({
          toolId: updatedAction?.voice?.vapiToolId,
          body: {
            function: {
              name: editDatasourceDto?.actionId,
              description: editDatasourceDto?.action.promptContent,
            },
          },
        });
      }

      return response.status(200).json({
        message: 'Action edited successfully',
      });
    } catch (error) {
      
      handleException(response, error);
    }
  }

  async getDatasources(
    getDatasourceDto: GetDatasourceDto,
    @Res() response: Response,
  ) {
    try {
      const agent = await this.mongoAgentService.getDatasources(
        getDatasourceDto,
      );

      return response.status(200).json({
        message: 'Action fetched successfully',
        data: agent,
      });
    } catch (error) {
      
      return response.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        error: 'Internal Server Error',
      });
    }
  }

  async isPrerequisiteSatisfied(agentDetails) {
    const aiProvider = agentDetails.aiProvider;
    if (!aiProvider) {
      return false; // aiProvider is not defined
    }

    // Check if any of the properties in aiProvider are empty
    const {
      companyId,
      modelName,
      accountName,
      accountId,
      isAdvancedSettings,
      advancedSettings,
    } = aiProvider;

    let isHostedLLM = (accountId ?? '').startsWith('capriHostedLlm');
    if (!companyId || !modelName || !accountId) {
      return false; // aiProvider is empty
    }

    //check if active prompt is empty
    // if (
    //   agentDetails['prompts'] == undefined ||
    //   agentDetails.prompts['currentActive'] == undefined ||
    //   agentDetails.prompts.currentActive == ''
    // ) {
    //   return false;
    // }

    if (!isHostedLLM) {
      //check if secret key is missing
      const aiProviderAccountId = agentDetails.aiProvider.accountId;
      const orgid = agentDetails.orgId;
      const orgDetails = await this.mongoOrganizationService.getOrganization({
        _id: orgid,
      });
      for (const aiProviderInOrg of orgDetails.connections.aiProvider) {
        if (aiProviderInOrg.accountId == aiProviderAccountId) {
          const credId = aiProviderInOrg.credentialId;
          const credData = await this.mongoCredentialService.getCredential({
            _id: credId,
          });
          if (!credData['creds'].secret) {
            return false;
          }
        }
      }
    }

    return true; // session is ready to be started
  }

  async addTriggers(addTriggerDto: AddTriggerDto, response?: Response) {
    try {
      const { agentId } = addTriggerDto;
      const agentData = await this.mongoAgentService.getAgent({ _id: agentId });
      const isDuplicateQueryParams = {
        'triggers.data.subaccount': addTriggerDto.data.subaccount,
        'triggers.data.tagOption': addTriggerDto.data.tagOption,
        'triggers.data.tagValue': addTriggerDto.data.tagValue,
        'trigger.data.channel': addTriggerDto.data.channel,
      };
      const duplicateTrigger = await this.mongoAgentService.checkAgentExists(
        isDuplicateQueryParams,
      );
      if (duplicateTrigger)
        throw new BadRequestException(
          `A trigger with the same subaccount, tag option and tag value already exists.`,
        );

      const isSatisfied = await this.isPrerequisiteSatisfied(agentData);
      if (isSatisfied) {
        const triggerResponse = await this.mongoAgentService.addTrigger(
          addTriggerDto,
        );

        const length = triggerResponse.triggers.length;

        if (response) {
          return response.status(201).json({
            message: 'Triggers Added Successfully',
            trigger: triggerResponse.triggers[length - 1],
          });
        } else {
          return triggerResponse?.triggers[length - 1];
        }
      } else {
        if (response) {
          return response.status(400).json({
            message:
              'Please set AI Provider and an active prompt before creating a trigger',
          });
        }
      }
    } catch (error) {
      
      if (response) {
        return response.status(500).json({ error: 'Internal server Error' });
      }
    }
  }

  async editTrigger(body: EditTriggerDto) {
    const { agentId, triggerId, data } = body;
    const triggerUpdate = await this.mongoAgentService.updateAgent(
      { _id: agentId, 'triggers.triggerId': triggerId },
      { $set: { 'triggers.$': { ...data, triggerId } } },
    );
    const triggerObj = await this.mongoAgentService.getAgent(
      { _id: agentId, 'triggers.triggerId': triggerId },
      { 'triggers.$': 1 },
    );
    const response = triggerObj?.triggers?.[0];
    return response;
  }

  async updateTriggerStatus(
    updateStatusTriggerDto: UpdateStatusTriggerDto,
    response,
  ) {
    await this.mongoAgentService.updateTriggerStatus(updateStatusTriggerDto);
    return response.status(200).json({
      message: 'Successfully Updated The Status Of Trigger.',
    });
  }

  async deleteTrigger(agentId, triggerId, response: Response) {
    await this.mongoAgentService.deleteTrigger(agentId, triggerId, response);
    return response.status(200).json({
      message: 'Trigger Deleted Successfully.',
    });
  }

  async deleteAction(
    agentId: string,
    actionId: string,
    @Res() response: Response,
    isTool?: boolean,
  ) {
    try {
      // const agentWithAction = await this.mongoAgentService.getAgent({
      //   _id: agentId,
      // });
      const agent = await this.mongoAgentService.deleteAction(
        agentId,
        actionId,
      );
      if (agent?.voiceConfig?.assistantId && agent?.voiceConfig?.enabled) {
        // let {} = await this.assistantService.createVapiSystemPrompt(agentWithAction);
        let { actions, toolIds, tools, vapiPrompt } =
          await this.assistantService.createVapiSystemPrompt(agent);
        const toolId = agent.voiceConfig.tools.find(
          (t) => t.actionId === actionId,
        )?.toolId;
        if (toolId) {
          const d = await this.mongoAgentService.deleteTool(agentId, toolId);
          toolIds = toolIds.filter((t) => t !== toolId);
          const r = await this.toolsService.deleteTool({ toolId });
          const a = await this.assistantService.updateVapiAssistant({
            agentId,
            assistantId: agent?.voiceConfig?.assistantId,
            toolIds,
            model: {
              model: agent?.voiceConfig?.model?.model,
              provider: agent?.voiceConfig?.model?.provider,
            },
            mainPrompt: vapiPrompt,
          });
          
        }
      }
      return response.status(200).json({
        message: 'Action deleted successfully',
      });
    } catch (error) {
      handleException(response, error);
    }
  }

  async deleteAgent(agentId: string, @Res() response: Response) {
    try {
      const agentInfo = await this.mongoAgentService.getAgent({ _id: agentId });
      const organizationId = agentInfo.orgId;
      const deletedAgent = await this.mongoAgentService.deleteAgent({
        _id: agentId,
      });
      const toolIds = deletedAgent.actions.map(
        (action) => action?.voice?.vapiToolId,
      );
      if (deletedAgent?.voiceConfig?.assistantId) {
        await this.assistantService.deleteVapiAssistant({
          assistantId: deletedAgent?.voiceConfig?.assistantId,
          toolIds,
        });
      } else {
        
      }
      // Update the organization by removing the agent ID from the agents[] array
      await this.mongoOrganizationService.updateOrganization(
        { _id: organizationId },
        { $pull: { agents: agentId } },
      );
      // Remove the agent from the folder
      await this.foldersService.removeResourceId({
        resourceId: agentId,
        type: 'agents',
        organization: organizationId,
      });
      return response.status(200).json({
        message: 'Agent deleted successfully',
      });
    } catch (error) {
      
      return response.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        error: 'Internal Server Error',
      });
    }
  }

  async toggleAgent(
    agentId: string,
    toggle: Boolean,
    @Res() response?: Response,
  ) {
    try {
      const agentData = await this.mongoAgentService.getAgent({ _id: agentId });
      if (!agentData || agentData == undefined) {
        if (response) {
          return response.status(200).json({
            message: 'Agent not found.',
          });
        }
      }

      if (
        (agentData['prompts'] == undefined ||
          agentData.prompts['currentActive'] == undefined ||
          agentData.prompts.currentActive == '') &&
        !toggle
      ) {
        if (response) {
          return response.status(400).json({
            error: 'Please set an active prompt before activating the agent.',
          });
        }
      }

      const orgId = agentData.orgId;
      const orgData = await this.mongoOrganizationService.getOrganization({
        _id: orgId,
      });
      const maxAgents = orgData.billing.allowedAgents;
      // const activeAgents = orgData.agents.length;
      const activeAgentsArr = await this.mongoAgentService.getAgents({
        orgId,
        disabled: false,
      });
      const activeAgents = activeAgentsArr.length;
      

      if (!toggle) {
        if (orgData.billing.status === 'suspended') {
          if (response) {
            return response.status(HttpStatus.PAYMENT_REQUIRED).json({
              message:
                'Your organization has been suspended due to non payment. Please update your plan to enable this agent.',
            });
          } else {
            throw new BadRequestException('Your organization has been suspended due to non payment. Please update your plan to enable this agent.');
          }
        }
        if (activeAgents >= maxAgents) {
          if (response) {
            return response.status(HttpStatus.PAYMENT_REQUIRED).json({
              message:
                'You have reached the maximum number of active agents in your organization.',
            });
          } else {
            throw new BadRequestException('Max agents reached');
          }
        }
      }
      const agentInfo = await this.mongoAgentService.toggleAgent(
        agentId,
        toggle,
        response,
      );
      // const organizationId = agentInfo.orgId;
      // const totalAgents = organizationI
      // const deletedAgent = await this.mongoAgentService.deleteAgent({ _id: agentId });

      // agentInfo.disabled = true;
      // await agentInfo.save();
      // Update the organization by removing the agent ID from the agents[] array
      // await this.mongoOrganizationService.updateOrganization(
      //   { _id: organizationId },
      //   { $pull: { agents: agentId } },
      // );
      if (response) {
        return response.status(200).json({
          message: 'Agent status toggled successfully.',
        });
      }
    } catch (error) {
      
      if (response) {
        return response.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
          error: error,
        });
      }
    }
  }

  async createPromptUsingLlama(prompt) {
    const payload = {
      prompt,
      temperature: 0.5,
      top_p: 0.9,
    };

    const url = 'https://api.fireworks.ai/inference/v1/chat/completions';

    const body = {
      model: 'accounts/fireworks/models/llama-v3p1-70b-instruct',
      messages: [
        {
          role: 'system',
          content: prompt,
        },
      ],
    };

    const headers = {
      Authorization: `Bearer ${CUSTOM_LLM.API_KEY}`,
      'Content-Type': 'application/json',
    };

    try {
      let response = await axios.post(url, body, { headers });
      return response.data.choices[0].message.content;
    } catch (error) {
      throw error;
    }
  }

  async createQuickAgent(
    body: IQuickActionProp,
    res?: Response,
    userId?: string,
  ) {
    try {
      const { orgId } = body;
      const qna = qnaMap(body);

      

      let mainPrompt = `<s>[INST] <<SYS>>
You are the Prompt Generator, Your role is to assist by generating prompts based on the user's defined format.
You can use this information to generate a prompt that follows the required template format plus anything else you want. Don't add any Note and generated prompt should be usefull enough to work.

The business you are generating a prompt for has given you the following information:
${qna.map(({ question, answer }) => `\n${question}\n${answer}`).join('\n')}
<</SYS>>
This is a required template to generate a prompt. The prompt you generate must follow the following format in json format:

{
I want you to act  ${
        qna.find((q) => q.question.includes('assistant name'))?.answer ||
        'an assistant'
      }, the friendly assistant for ${
        qna.find((q) => q.question.includes('businessName'))?.answer ||
        '(company name)'
      }. As the assistant, it is your responsibility to follow guidance provided to you by the business you are representing, and only answer questions asked directly by the contact. You should only answer questions that are relevant to the business or offerings that the business has. If the contact asks you any questions or says anything that is not relevant to the business, you should respond with "Sorry, I don't think I'm the best person to answer that for you". 

 ${
   qna.find((q) => q.question.includes('company name'))?.answer ||
   '(Company name)'
 } is a ${
        (qna.find((q) => q.question.includes('company description')) || {})
          .answer || '(company description)'
      } based in ${
        (qna.find((q) => q.question.includes('Where')) || {}).answer ||
        '(Company description - e.g., a local Dental office in (city))'
      }. We specialize in ${
        qna.find((q) => q.question.includes('services the company offers'))
          ?.answer || '(services the company offers)'
      }. Your job is to answer any questions the contact may have about ${
        qna.find((q) => q.question.includes('company name'))?.answer ||
        '(company name)'
      }, and try to schedule an appointment for the contact to meet with a member of our team. 
 We offer a free initial consultation to discuss business goals and provide personalized recommendations for improvement. During this meeting, we'll learn more about the business and suggest ways to enhance the online presence. 
 Scheduling the contact for a consultation is your primary goal. If the contact seems interested, you should try to coordinate a day and time for the meeting. 
 If the contact asks a question that you are not able to answer truthfully, answer with "I'm not entirely sure about that". 
 Do not answer any questions you were not directly asked by the contact. 
 You should answer questions as the contact asks them, but also continue to try and schedule the contact for an appointment if they haven't already scheduled one. 
 Do not answer questions that are not relevant to the services we provide. 
 Do not repeat the same question several times in a row. 
 You should only ask the same question one time concurrently, to prevent your questions from being repetitive. 
}
 [/INST]
`;

      // 

      let finalPrompt = await this.createPromptUsingLlama(mainPrompt);
      // 

      function extractContent(inputString) {
        try {
          // Find content between curly braces
          const match = inputString.match(/\{([\s\S]*)\}/);

          if (!match) return null;

          // Get the content and clean it up
          let content = match[1]
            // Remove the initial quote if it exists
            .replace(/^\s*"/, '')
            // Clean up escaped characters
            .replace(/\\n/g, '\n')
            .replace(/\\'/g, "'")
            .replace(/\\"/g, '"')
            // Remove trailing quote if it exists
            .replace(/"\s*$/, '')
            .trim();

          return content;
        } catch (error) {
          
          return null;
        }
      }

      const extractedContent = extractContent(finalPrompt);

      // At this point, finalPrompt should be a parsed JSON object
      

      const action = [];
      if (body.calendarDetails) {
        // const org = await this.mongoOrganizationService.getOrganization({_id: body.orgId});
        // const calendarConnection = (org?.connections?.dataSources ?? []).find(c => c.accountId === body.calendarDetails.accountId);
        action.push({
          actionId: uuidv4(),
          accountId: body.calendarDetails.accountId,
          providerName: body.calendarDetails.providerName,
          promptContent:
            'This calendar contains real-time availability for appointments. Read it to determine what dates and times are available for an appointment.',
          activity: 'read',
          accountName: body.calendarDetails.name,
          silent: false,
          isAdvancedSettings: false,
          advancedSettings: {
            maxTokensAllowed: 2000,
          },
          includeMainPromptDetails: {
            includeMainPrompt: false,
            mainPromptId: '',
          },
        });
        action.push({
          actionId: uuidv4(),
          accountId: body.calendarDetails.accountId,
          providerName: body.calendarDetails.providerName,
          promptContent:
            'Add appointments to this calendar once the day and time for the appointment have been clearly established and confirmed.',
          activity: 'write',
          accountName: body.calendarDetails.name,
          silent: false,
          isAdvancedSettings: false,
          advancedSettings: {
            maxTokensAllowed: 2000,
          },
          includeMainPromptDetails: {
            includeMainPrompt: false,
            mainPromptId: '',
          },
        });
      }

      let agent;
      // if (aiConn?.accountId) {
      let uuid = uuidv4();
      let promptName = body?.companyInfo.agentName || 'agent';
      agent = await this.createAgent(userId, {
        agentName:
          (body?.companyInfo.agentName || `Quick Agent`) + ' ' + body.niche,
        aiProvider: {
          companyId: 'openai-hosted',
          modelName: 'gpt-4o',
          accountName: 'gpt-4o',
          accountId: '67aa3370ed7970b32883ba6f',
          isAdvancedSettings: false,
          advancedSettings: {
            temperature: 0.3,
            maxLength: 1000,
            frequencyPenalty: 0,
            optimize: 'accuracy',
          },
        },
        prompts: {
          currentActive: uuid,
          prompt: [
            {
              promptId: uuid,
              name: `${
                promptName.charAt(0).toUpperCase() + promptName.slice(1)
              } prompt`,
              promptContent:
                // `I want you to act as a virtual assistant for a business. As the assistant, it is your responsibility to follow guidance provided to you by the business you are representing, and only answer questions asked directly by the contact. You should only answer questions that are relevant to the business or offerings that the business has. If the contact asks you any questions or says anything that is not relevant to the business, you should respond with "Sorry, I don\'t think I\'m the best person to answer that for you".\n\n` +
                extractedContent,
            },
          ],
        },
        orgId,
        actions: action,
        disabled: true,
      });
      // }

      //scrape website
      try {
        if (body.companyInfo?.scapeWebsite) {
          this.addQuickAgentSite(
            orgId,
            body?.companyInfo?.website,
            body.companyInfo.businessName,
            body?.companyInfo?.website,
            agent._id.toString(),
          );
        }
      } catch (err) {
        
      }
      res.status(HttpStatus.CREATED).send({
        agentId: agent._id.toString(),
        finalPrompt,
      });
    } catch (err) {
      
      handleException(res, err);
    }
  }

  async addQuickAgentSite(
    orgId: string,
    urls: string,
    userName: string,
    domain: string,
    agentId: string,
    schedule?: boolean,
  ) {
    try {
      if (schedule) {
        // const queue = "v3-migration-tasks";
        // const project = "dwy-master";
        // const location = "us-central1";
        // let timeInSeconds;
        // await this.taskService.createHttpTask(project, location, queue, `${process.env.BACKEND_URL}/migration/location/${orgId}`, { locationId, usid }, timeInSeconds)
      } else {
        const siteConn = await this.siteService.scrapeSites(
          orgId,
          urls,
          userName,
          domain,
        );
        await this.addDatasources({
          agentId,
          action: {
            actionId: uuidv4(),
            accountId: siteConn.accountId,
            accountName: siteConn.name,
            providerName: siteConn.providerName,
            promptContent: 'Use this resource to look for any information',
            activity: 'read',
            deleted: false,
            silent: true,
            isAdvancedSettings: false,
          },
        });
      }
      
    } catch (err) {
      
    }
  }

  async getAllAgentHistory(
    reqId: string,
    agentId: string,
    query: GetAllHistoryQueryParams,
  ) {
    let { page = 1, limit = 10, searchContactId = '' } = query;
    page = parseInt(page.toString());
    limit = parseInt(limit.toString());
    if (ObjectId.isValid(agentId) === false) {
      throw new BadRequestException('Invalid Agent ID');
    }
    let queryBody = {
      agentId: agentId,
    };
    if (searchContactId !== '') {
      queryBody['contactId'] = searchContactId;
    }
    const agentHistory =
      await this.mongoConversationService.getPaginatedConversations(
        {
          query: queryBody,
        },
        {
          page,
          limit,
        },
      );
    const result = [];
    agentHistory.forEach((item) => {
      result.push({
        channel: item?.channel,
        lastMessage:
          item.conversation[(item.conversation || []).length - 1]?.body,
        lastMessageTime:
          item.conversation[(item.conversation || []).length - 1]?.dateAdded,
        totalTokens: item?.totalTokens || 0,
        numberOfMessages: (item.conversation || []).length,
        contactId: item?.contactId,
        resourceId: item?.resourceId,
        conversationId: item?._id.toString(),
      });
    });
    const totalConversations =
      await this.mongoConversationService.getConversationsCount({
        agentId: agentId,
      });
    const totalPage = Math.ceil(totalConversations / limit);
    return {
      totalPage,
      currentPage: page,
      history: result,
    };
  }

  async getDetailedHistory(
    reqId: string,
    contactId: string,
    query: GetAllHistoryQueryParams,
  ) {
    let { limit = 10, page = 1 } = query;
    page = parseInt(page.toString());
    limit = parseInt(limit.toString());
    let skip = (page - 1) * limit;
    const detailedHistory =
      await this.mongoConversationService.getConversations({
        query: {
          contactId,
        },
      });
    const result = [];
    const conversation = detailedHistory[0].conversation;
    let j = skip + 1;
    if (skip == 0) j = 0;
    let typeMap = {
      email: 'Email',
      sms: 'SMS',
      facebook: 'FB',
      whatsapp: 'whatsApp',
      webchat: 'Live_Chat',
      instagram: 'IG',
    };
    for (let i = j; i < conversation.length && i < skip + limit + 1; i++) {
      if (
        query?.messageType &&
        conversation[i]?.messageType !== typeMap[query.messageType]
      )
        continue;
      if (conversation?.[i]) {
        result.push({
          aiProvider: conversation[i]?.aiProvider,
          message: conversation[i]?.body || '',
          dateAdded: conversation[i].dateAdded,
          tokens: conversation[i]?.tokens || 0,
          direction: conversation[i].direction || '',
          status: conversation[i].status || '',
          actions: conversation[i].actions || [],
          messageType: conversation[i]?.messageType || '',
        });
      }
    }
    return {
      conversations: result,
      totalPage: Math.ceil(conversation.length / limit),
      currentPage: page,
    };
  }

  async getGhlCalendarsAction(agentId: string) {
    const agent = await this.mongoAgentService.getAgent(
      { _id: agentId },
      { actions: 1 },
    );
    const calendarActions = agent.actions.filter(
      (action) => action.providerName === PROVIDERS.GHL_CALENDAR,
    );
    return calendarActions;
  }

  async getTagsAction(agentId: string) {
    const agent = await this.mongoAgentService.getAgent(
      { _id: agentId },
      { actions: 1 },
    );
    const tagActions = agent.actions.filter(
      (action) => action.providerName === PROVIDERS.GHL_CHANNEL,
    );
    return tagActions;
  }

  async getAgents(query: Object, projection: Object) {
    const agents = await this.mongoAgentService.getAgents(query, projection);
    return agents;
  }

  async generateQuickAgentPrompt(
    questions,
    secretKey: string,
  ): Promise<string> {
    async function createPrompt(mainPrompt: any, secretKey: string) {
      let reqBody = {
        method: 'POST',
        url: 'https://api.openai.com/v1/chat/completions',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${secretKey}`,
        },
        data: {
          max_tokens: 750,
          temperature: 0.4,
          frequency_penalty: 0.5,
          model: 'gpt-4-turbo-preview',
          messages: [{ role: 'system', content: mainPrompt.prompt }],
        },
      };
      return reqBody;
    }

    try {
      let mainPrompt = {
        prompt:
          `You are responsible for building a natural language prompt for a business. The prompt you generate must follow the following template: \n\n` +
          `You are (assistant name), the friendly assistant for (company name). (Company name) is a (company description - i.e. 'a local Dental office in (city)'. \n ` +
          `(Company name) is able to help with things like (services the company offers). Your job is to answer any questions the contact may have about (company name), and try to schedule an appointment for the contact to meet with a member of our team. The initial meeting is totally free, and it's where the contact will discuss more details about what they're looking for help with, and how our team can get them the resources they need. Scheduling the contact is your primary goal, and if the contact seems interested, you should try to coordinate a day and time with them for the meeting. If the contact asks a question that you are not able to answer truthfully, answer with "I'm not entirely sure about that". Do not answer any questions you were not directly asked by the contact. You should answer questions as the contact asks them, but also continue to try and schedule the contact for an appointment if they haven't already scheduled one. Do not answer questions that are not relevant to the services we provide. Do not repeat the same question several times in a row. You should only ask the same question one time concurrently, to prevent your questions from being repetitive. Wait for the contact to answer your current question before asking the next question. \n\n` +
          `The business you are generating a prompt for has given you the following information:`,
      };
      for (let q = 0; q < questions.length; q++) {
        mainPrompt.prompt += `\n\n${questions[q].question}\n${questions[q].answer}`;
      }
      mainPrompt.prompt += `\n\nUsing the information above, generate a prompt that follows the required template format. \n\n`;

      let promptRequestBody = await createPrompt(mainPrompt, secretKey);
      let promptResponse;
      try {
        promptResponse = await this.apiClientService.rawApiRequest(
          promptRequestBody,
        );
      } catch (error) {
        promptRequestBody.data.model = 'gpt-3.5-turbo';
        promptResponse = await this.apiClientService.rawApiRequest(
          promptRequestBody,
        );
      }
      return promptResponse?.choices?.[0]?.message?.content || '';
    } catch (error) {
      
      return '';
    }
  }

  async handleMultipleInboundConfigs(
    agentId: string,
    payload: {
      multipleInbound: boolean;
      initialWait?: number;
      maxWait?: number;
      incrementBy?: number;
    },
  ) {
    const configData = {};

    if (payload?.multipleInbound) {
      configData['multipleInbound'] = payload?.multipleInbound;
      if (payload?.initialWait) {
        configData['multipleInboundConfig.initialWait'] = payload?.initialWait;
      }
      if (payload?.maxWait) {
        configData['multipleInboundConfig.maxWait'] = payload?.maxWait;
      }
      if (payload?.incrementBy) {
        configData['multipleInboundConfig.incrementBy'] = payload?.incrementBy;
      }
    } else {
      configData['multipleInbound'] = false;
    }
    const agent = await this.mongoAgentService.updateAgent(
      {
        _id: agentId,
      },
      {
        $set: configData,
      },
    );
  }

  async addVariable(
    agentId: string,
    variable: {
      name: string;
      type: 'static' | 'dynamic';
      value?: string;
      providerName?: string;
      fieldKey?: string;
    },
  ) {
    // Validate the variable
    if (variable.type === 'static' && (!variable.fieldKey || !variable.value)) {
      throw new Error(
        `Static variable '${variable.name}' must have a value and field key`,
      );
    }
    if (
      variable.type === 'dynamic' &&
      (!variable.providerName || !variable.fieldKey)
    ) {
      throw new Error(
        `Dynamic variable '${variable.name}' must have providerName and fieldKey`,
      );
    }

    const agent = await this.mongoAgentService.updateAgent(
      { _id: agentId },
      {
        $push: { variables: variable },
      },
    );
    return agent.variables;
  }

  async addVariables(
    agentId: string,
    variables: Array<{
      name: string;
      type: 'static' | 'dynamic';
      value?: string;
      providerName?: string;
      fieldKey?: string;
    }>,
  ) {
    if (!Array.isArray(variables)) {
      throw new TypeError('Variables must be an array');
    }

    for (const variable of variables) {
      if (variable.type === 'static' && !variable.value) {
        throw new Error(`Static variable '${variable.name}' must have a value`);
      }
      if (
        variable.type === 'dynamic' &&
        (!variable.providerName || !variable.fieldKey)
      ) {
        throw new Error(
          `Dynamic variable '${variable.name}' must have providerName and fieldKey`,
        );
      }
    }

    // Update agent with new variables
    const agent = await this.mongoAgentService.updateAgent(
      { _id: agentId },
      {
        $push: { variables: { $each: variables } },
      },
    );

    return agent.variables;
  }

  async getVariables(agentId: string) {
    const agent = await this.mongoAgentService.getAgent({ _id: agentId });
    return agent.variables || [];
  }

  async locationVariables(locationId: string) {
    try {
      const cred = await this.mongoCredentialService.getCredential({
        keyId: locationId,
        kind: KINDS.GHL_CREDENTIAL,
      });
      if (cred?.creds?.accessToken == undefined) {
        this.myLogger.log({
          message: `Your leadconnector account with locationId: ${locationId} couldn't be found in our database`,
          context: `${locationId}`,
        });
        return;
      }
      const tokens = {
        access_token: cred.creds.accessToken,
        refresh_token: cred.creds.refreshToken,
      };

      let variables = this.ghlApisService.getVariables({
        tokens,
        locationId,
      });

      return variables;
    } catch (error) {
      this.myLogger.log({
        message: `Couldn't get variables from leadconnector account with locationId: ${locationId}, error: ${error}`,
        context: `${locationId}`,
      });
    }
  }

  async getGHLSubaccountList(orgId: string) {
    try {
      const organization = await this.mongoOrganizationService.getOrganization({
        _id: orgId,
      });

      // If no organization is found, handle the error
      if (!organization) {
        this.myLogger.log({
          message: `Couldn't find organization with orgId: ${orgId}`,
          context: `${orgId}`,
        });
        return { success: false, message: 'Organization not found' };
      }

      // Filter out the GHL subaccounts from the organization's connections
      const ghlSubaccounts = organization.connections.channels
        .filter((channel) => channel.providerName === 'ghl')
        .map((channel) => ({
          name: channel.name,
          providerName: channel.providerName,
          keyId: channel.keyId,
          accountId: channel.accountId,
        }));

      return ghlSubaccounts;
    } catch (error) {
      this.myLogger.log({
        message: `Error fetching GHL subaccounts for orgId: ${orgId}, error: ${error}`,
        context: `${orgId}`,
      });
    }
  }

  async deleteVariable(agentId: string, variableId: string) {
    const agent = await this.mongoAgentService.updateAgent(
      { _id: agentId },
      {
        $pull: { variables: { _id: variableId } },
      },
    );
    return agent;
  }

  // 4. Update a specific variable by name
  async updateVariable(
    agentId: string,
    variableId: string,
    updateData: {
      type?: 'static' | 'dynamic';
      value?: string;
      providerName?: string;
      fieldKey?: string;
    },
  ) {
    // Find the agent and the specific variable
    const agent = await this.mongoAgentService.getAgent({ _id: agentId });
    const variableIndex = agent.variables.findIndex(
      (v) => v._id.toString() === variableId,
    );

    if (variableIndex === -1) {
      throw new Error(`Variable '${variableId}' not found`);
    }

    // Update the variable
    Object.assign(agent.variables[variableIndex], updateData);

    // Save the agent
    await this.mongoAgentService.updateAgent(
      { _id: agentId },
      { $set: { variables: agent.variables } },
    );

    return agent.variables;
  }

  async addHumanTakeover(
    agentId: string,
    payload: IHumanTakeover,
    response: Response,
  ) {
    const { tagToAdd, takeoverType, timeToWait } = payload;
    if (takeoverType === 'Tag' && !tagToAdd)
      throw new BadRequestException('tagToAdd is required');
    if (takeoverType === 'Wait' && !timeToWait)
      throw new BadRequestException('timeToWait is required');
    if (!agentId) throw new BadRequestException('agentId is required');

    const updateBody = {
      'humanTakeover.takeoverType': takeoverType,
    };
    if (takeoverType === 'Tag') {
      updateBody['humanTakeover.tagToAdd'] = tagToAdd;
    } else if (takeoverType === 'Wait') {
      updateBody['humanTakeover.timeToWait'] = timeToWait;
    }
    const agent = await this.mongoAgentService.updateAgent(
      {
        _id: agentId,
      },
      {
        $set: updateBody,
      },
    );
    response.status(HttpStatus.OK).send({ message: 'Successfully added' });
  }

  async updateMainPromptToggle(editMainPromptDto: EditMainPromptInjectionDto) {
    const updatedAgent = await this.mongoAgentService.updateMainPromptToggle(
      editMainPromptDto,
    );
    return updatedAgent;
  }

  async handleAttachmentConfig({ agentId, attachmentConfig = null }) {
    const updateBody = {
      attachmentConfig: {},
    };
    if (attachmentConfig?.withoutMessage) {
      updateBody['attachmentConfig']['withoutMessage'] = {
        enabled: attachmentConfig?.withoutMessage?.enabled,
        tagToAdd: attachmentConfig?.withoutMessage?.tagToAdd,
        setSilent: attachmentConfig?.withoutMessage?.setSilent,
      };
    }
    if (attachmentConfig?.withMessage) {
      updateBody['attachmentConfig']['withMessage'] = {
        enabled: attachmentConfig?.withMessage?.enabled,
        tagToAdd: attachmentConfig?.withMessage?.tagToAdd,
        setSilent: attachmentConfig?.withMessage?.setSilent,
      };
    }
    const updatedAgent = await this.mongoAgentService.updateAgent(
      {
        _id: agentId,
      },
      {
        $set: updateBody,
      },
    );
    return updatedAgent;
  }

  async handleEmptyMessage({ agentId, emptyMessageConfig = null }) {
    const updateBody = {
      emptyMessageConfig: {
        enabled: !!emptyMessageConfig?.enabled,
      },
    };
    if (emptyMessageConfig?.tagToAdd)
      updateBody['emptyMessageConfig']['tagToAdd'] =
        emptyMessageConfig?.tagToAdd;
    if (emptyMessageConfig?.setSilent)
      updateBody['emptyMessageConfig']['setSilent'] =
        emptyMessageConfig?.setSilent;

    const updatedAgent = await this.mongoAgentService.updateAgent(
      {
        _id: agentId,
      },
      {
        $set: updateBody,
      },
    );
    return updatedAgent;
  }

  async handleFallbackConfig({ agentId, fallbackConfig = null }) {
    const updateBody = {
      fallbackConfig: {},
    };
    if (fallbackConfig?.enabled) {
      updateBody['fallbackConfig']['enabled'] = fallbackConfig?.enabled;
    }
    if (fallbackConfig?.tagToAdd) {
      updateBody['fallbackConfig']['tagToAdd'] = fallbackConfig?.tagToAdd;
    }
    if (fallbackConfig?.setSilent !== undefined) {
      updateBody['fallbackConfig']['setSilent'] = fallbackConfig?.setSilent;
    }
    const updatedAgent = await this.mongoAgentService.updateAgent(
      {
        _id: agentId,
      },
      {
        $set: updateBody,
      },
    );
    return updatedAgent;
  }

  async handleUpdateContextLength({agentId, contextLength} : UpdateContextLengthPayload) {
    await this.mongoAgentService.updateAgent({_id: agentId}, {contextLength});
    return;
  }

  async toggleAgentToolsSync({ agentId, isSynced }) {
    if (agentId && isSynced) {
      const agent = await this.mongoAgentService.getAgent({ _id: agentId });
      const actions = agent?.actions || [];
      let vapiTools = agent?.voiceConfig?.tools || [];
      const agentToolMap = new Map();
      vapiTools.forEach((v) => {
        if (!agentToolMap.has(v.actionId)) {
          agentToolMap.set(v.actionId, v.toolId);
        }
      });
      const tools = actions.map((action) => {
        return {
          actionId: action.actionId,
          accountId: action.accountId,
          accountName: action?.accountName,
          providerName: action.providerName,
          activity: action.activity,
          promptContent: action.promptContent,
          silent: action.silent,
          toolId: agentToolMap.get(action.actionId),
        };
      });
      const updatedAgent = await this.mongoAgentService.updateAgent(
        {
          _id: agentId,
        },
        {
          $set: {
            tools: tools,
            isSynced: isSynced,
          },
        },
      );
    } else if (agentId && !isSynced) {
      await this.mongoAgentService.updateAgent(
        {
          _id: agentId,
        },
        {
          $set: {
            isSynced: isSynced,
          },
        },
      );
    }
  }

  async updateVapiToolStatus({
    toolId,
    agentId,
    active,
  }: {
    toolId: string;
    agentId: string;
    active: boolean;
  }) {
    const agent = await this.mongoAgentService.updateAgent(
      {
        _id: agentId,
        'voiceConfig.tools.toolId': toolId,
      },
      {
        $set: {
          'voiceConfig.tools.$.active': active,
        },
      },
    );
    return agent;
  }

  private async callTheScrapeUrl(websiteUrl: string, maxDepth = 3) {
    try {
      const WEBSCRAPER_URL = `${process.env.WEBSCRAPER_URL}/crawl`;
      const body = { start_url: websiteUrl, max_depth: maxDepth };

      this.myLogger.log({
        message: `Starting website scraping. ${WEBSCRAPER_URL} ${JSON.stringify(
          body,
        )}`,
        context: 'callTheScrapeUrl',
      });

      const response = await axios.post(WEBSCRAPER_URL, body, {
        headers: {
          'Content-Type': 'application/json',
        },
      });

      this.myLogger.log({
        message: `Website scraping completed ${JSON.stringify(response.data)}`,
        context: 'callTheScrapeUrl',
      });
      const contentArray = response.data?.results?.map(
        (item: WebsiteCrawlDirect) => ({ ...item.content }),
      ); // Returns {url: string, content: string}[]
      return contentArray;
    } catch (error) {
      this.myLogger.error({
        message:
          error instanceof Error ? error.message : 'Failed to scrape website',
        context: 'callTheScrapeUrl',
      });
      throw error;
    }
  }

  async processWebsitesUsingApiCall(
    agentId: string,
    website: WebsiteProcessingJob,
  ) {
    try {
      const HARDCODED_JOBID = 'manual-processing-website';
      const processingJob = {
        jobId: HARDCODED_JOBID,
        accountId: uuidv4(),
        fileName: website.website,
        jobType: website.actionType,
        maxDepth: website?.maxDepth || 0,
      };
      const { accountId, fileName, jobId, jobType, maxDepth } = processingJob;

      await this.mongoAgentService.updateAgent(
        { _id: agentId },
        { $push: { processingJobs: processingJob } },
      );

      const scrapedResults = await this.callTheScrapeUrl(
        website.website,
        website.maxDepth,
      );
      this.myLogger.log({
        message: `Scraped content is ${JSON.stringify(scrapedResults)}`,
        context: 'processWebsitesUsingApiCall',
      });
      

      if (scrapedResults && scrapedResults.length > 0) {
        // Process each scraped result
        for (const result of scrapedResults) {
          const { url, content } = result;
          await this.handleResourceStatus(agentId, {
            accountId,
            characterCount: content.length,
            fileName: url,
            jobId: HARDCODED_JOBID,
            jobType: JobType.WEBSITE_SCRAP,
            status: MessageStatus.SUCCESS,
            textContent: content,
            maxDepth,
          });
        }
      } else {
        // Handle case where no results were returned
        await this.handleResourceStatus(agentId, {
          accountId,
          characterCount: 0,
          fileName: website?.website,
          jobId: HARDCODED_JOBID,
          jobType: JobType.WEBSITE_SCRAP,
          status: MessageStatus.FAILED,
          textContent: '',
          failedReason: `Failed to scrape website ${website.website}`,
        });
      }
    } catch (error) {
      this.myLogger.error({
        message: error?.message || 'Error processing website',
        context: 'processWebsitesUsingApiCall',
      });
    }
  }

  async processWebsites(agentId: string, website: WebsiteProcessingJob) {
    const { orgId } = await this.mongoAgentService.getAgent(
      { _id: agentId },
      { orgId: 1 },
    );
    const accountId = uuidv4();
    const jobDetails = await this.fileUploadService.processWebsite(
      { agentId, orgId, accountId },
      website,
    );
    const processingJob = {
      jobId: jobDetails.id,
      accountId,
      fileName: website.website,
      jobType: website.actionType,
      maxDepth: website?.maxDepth || 0,
    };
    await this.mongoAgentService.updateAgent(
      { _id: agentId },
      { $push: { processingJobs: processingJob } },
    );
    return { message: 'Website queued for processing', processingJob };
  }

  async processFiles(agentId: string, files: Express.Multer.File[]) {
    if (files.length === 0) return;
    const { orgId } = await this.mongoAgentService.getAgent(
      { _id: agentId },
      { orgId: 1 },
    );
    const accountId = uuidv4();
    const jobDetails = await this.fileUploadService.processFiles(
      { agentId, orgId, accountId },
      files,
    );

    const processingJobs = jobDetails.map((job) => ({
      jobId: job.id,
      accountId,
      fileName: job.fileName,
      mimeType: job.mimeType,
      jobType: JobType.FILE_UPLOAD,
    }));

    await this.mongoAgentService.updateAgent(
      { _id: agentId },
      { $push: { processingJobs: { $each: processingJobs } } },
    );

    this.myLogger.log({
      message: 'Jobs queued. ProcessingJobs: ' + JSON.stringify(processingJobs),
      context: 'processFiles',
    });

    return { message: 'Files queued for processing', processingJobs };
  }

  async getAllFileStatus(agentId: string) {
    const agent = await this.mongoAgentService.getAgent({ _id: agentId });
    const { processedFiles, processingJobs } = agent;
    return { processedFiles, processingJobs };
  }

  async getAugmentedQuery(agentId: string, payload: getAugmentedQueryDto) {
    const { message } = payload;
    const agent = await this.mongoAgentService.getAgent({ _id: agentId });
    const { processedFiles, prompts } = agent;
    const currentPromptText =
      prompts?.prompt.find(
        (prompt) => prompt.promptId === prompts.currentActive,
      )?.promptContent ?? '';
    const accountIdList = processedFiles.map((file) => file.accountId);
    const augmentedQuery = await this.pineconeService.query(
      { accountId: { $in: accountIdList } },
      message,
    );

    const response = await openai.chat.completions.create({
      messages: [
        {
          role: 'system',
          content: `${currentPromptText}
          Below is an augmented query for the message received.
          ===AUGMENTED QUERY STARTS===
          ${augmentedQuery}
          ===AUGMENTED QUERY ENDS===
          Generate a reply.`,
        },
        { role: 'user', content: message },
      ],
      model: 'gpt-4',
    });

    const responseText = response.choices[0].message.content;
    const tokenUsage = response.usage;

    // Generate HTML content
    const htmlContent = this.generateHtmlContent(
      responseText,
      augmentedQuery.toString(),
      tokenUsage,
    );

    return htmlContent;
  }

  async getAugmentedQueryResponse(
    agentId: string,
    payload: getAugmentedQueryDto,
  ) {
    const { message, excludedIds, includedIds } = payload;
    const agent = await this.mongoAgentService.getAgent({ _id: agentId });
    const { processedFiles, faqs = [] } = agent;
    const faqAccountIds = [...new Set(faqs?.map((faq) => faq.id))];
    const textContentKnowledgeSourceId =
      agent.textContentKnowledgeSource?.accountId;
    const textContentKnowledgeSourceIdArr = textContentKnowledgeSourceId
      ? [textContentKnowledgeSourceId]
      : [];
    let accountIdList: string[] = [
      ...faqAccountIds,
      ...textContentKnowledgeSourceIdArr,
    ];
    let processAccountIdList: string[] = [];

    if (excludedIds && excludedIds.length > 0) {
      processAccountIdList = processedFiles
        .filter((file) => !excludedIds.includes(file.accountId))
        .map((file) => file.accountId);
    } else if (includedIds && includedIds.length > 0) {
      processAccountIdList = processedFiles
        .filter((file) => includedIds.includes(file.accountId))
        .map((file) => file.accountId);
    } else {
      processAccountIdList = processedFiles.map((file) => file.accountId);
    }

    accountIdList = accountIdList.concat(processAccountIdList);

    const filesInAccountIdList = processedFiles.filter((file) =>
      accountIdList.includes(file.accountId),
    );

    filesInAccountIdList.forEach(async (file) => {
      this.myLogger.log({
        message: `File referenced in augmented query: ${file.fileName}`,
        context: agentId,
      });
    });
    const topK = agent.contextLength/200
    const augmentedQuery = await this.pineconeService.query(
      { accountId: { $in: accountIdList } },
      message,
      topK || 5
    );

    return augmentedQuery;
  }

  async removeKnowledgeSourceProcessedFiles(
    agentId: string,
    accountId: string,
  ) {
    await this.pineconeService.deleteWithMetadata([accountId]);
    const agent = await this.mongoAgentService.updateAgent(
      { _id: agentId },
      {
        $pull: {
          processedFiles: { accountId },
        },
      },
      { new: false },
    );
    const file = agent?.processedFiles?.find(
      (file) => file.accountId === accountId,
    );
    this.myLogger.log({
      message: `File found in processed files: ${JSON.stringify(file)}`,
      context: 'DELETE KNOWLEDGE',
    });
    if (agent?.voiceConfig?.assistantId && file?.voiceFileId) {
      this.myLogger.log({
        message: `Deleting voice file: ${file.voiceFileId} from agent: ${agentId}`,
        context: 'VOICE | DELETE VOICE FILE',
      });
      await this.knowledgeService.deleteVapiFile(file.voiceFileId, agentId);
      await this.mongoAgentService.updateAgent(
        { _id: agentId },
        {
          'voiceConfig.files': {
            $pull: {
              providerFileId: file.voiceFileId,
            },
          },
        },
      );
    }
    if (!agent) {
      throw new NotFoundException('Agent does not exist.');
    }
    return;
  }

  async removeKnowledgeSourceProcessingJobs(
    agentId: string,
    accountId: string,
  ) {
    const agent = await this.mongoAgentService.updateAgent(
      { _id: agentId },
      {
        $pull: {
          processingJobs: { accountId },
        },
      },
    );
    if (!agent) {
      throw new NotFoundException('Agent does not exist.');
    }
    return;
  }

  private generateHtmlContent(
    responseText: string,
    augmentedQuery: string,
    tokenUsage: OpenAI.Chat.Completions.CompletionUsage,
  ): string {
    return `
      <!DOCTYPE html>
      <html lang="en">
      <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Augmented Query Response</title>
          <style>
              body {
                  font-family: Arial, sans-serif;
                  line-height: 1.6;
                  padding: 20px;
                  max-width: 800px;
                  margin: 0 auto;
                  color: #333;
              }
              h1 {
                  color: #2c3e50;
                  border-bottom: 2px solid #2c3e50;
                  padding-bottom: 10px;
              }
              h2 {
                  color: #34495e;
                  margin-top: 20px;
              }
              pre {
                  background-color: #f8f8f8;
                  border: 1px solid #ddd;
                  border-radius: 4px;
                  padding: 15px;
                  white-space: pre-wrap;
                  word-wrap: break-word;
              }
              .token-usage {
                  background-color: #e8f4f8;
                  border: 1px solid #b8d4e6;
                  border-radius: 4px;
                  padding: 10px;
                  margin-top: 20px;
              }
              .token-usage p {
                  margin: 5px 0;
              }
          </style>
      </head>
      <body>
          <h1>Augmented Query Response</h1>
          
          <h2>Response Text</h2>
          <pre>${this.escapeHtml(responseText)}</pre>
          
          <h2>Augmented Query</h2>
          <pre>${this.escapeHtml(augmentedQuery)}</pre>
          
          <div class="token-usage">
              <h2>Token Usage</h2>
              <p>Prompt Tokens: ${tokenUsage.prompt_tokens}</p>
              <p>Completion Tokens: ${tokenUsage.completion_tokens}</p>
              <p>Total Tokens: ${tokenUsage.total_tokens}</p>
          </div>
      </body>
      </html>
    `;
  }

  private escapeHtml(unsafe: string): string {
    return unsafe
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#039;');
  }

  async checkAgentNameUniqueness(orgId: string, agentName: string) {
    const agent = await this.mongoAgentService.getAgent({
      agentName,
      orgId,
    });
    if (agent) {
      return {
        isNameUnique: false,
        agentId: agent._id?.toString(),
      };
    }
    return {
      isNameUnique: true,
      agentId: null,
    };
  }

  async updateNotificationSetting(
    agentId: string,
    notificationType: keyof NotificationSettings,
    enabled: boolean,
  ): Promise<AgentDocument> {
    const updateQuery = {
      [`showNotifications.${notificationType}`]: enabled,
    };

    const updatedAgent = await this.mongoAgentService.updateAgent(
      { _id: agentId },
      { $set: updateQuery },
      { new: true, runValidators: true },
    );

    if (!updatedAgent) {
      throw new Error('Agent not found');
    }

    return updatedAgent;
  }

  async fetchAllLinks(agentId: string, website: string) {
    const endpoint = `${process.env.WEBSCRAPER_URL}/extract-links`;
    const body = { url: website };
    
    try {
      const response = await axios.post(endpoint, body);
      if (response.status === 200) {
        const links = response.data?.links;
        return {
          links: response.data?.links,
          failReason: null,
        };
      }
    } catch (error) {
      this.myLogger.log({ message: error?.message, context: 'fetchAllLinks' });
      throw error?.message || 'Fail to retrieve links';
    }
  }

  private async processMultipleWebsites(
    scraping_id: string,
    agentId: string,
    orgId: string,
    urls: string[],
    processingJobs: any[],
  ) {
    try {
      this.myLogger.log({
        message: `Starting webscraping for scraping_id: ${scraping_id}`,
        context: scraping_id,
      });

      const response = await axios.post(
        `${process.env.WEBSCRAPER_URL}/scrape-urls`,
        { scraping_id, urls },
      );

      this.myLogger.log({
        message: `Webscraping completed for scraping_id: ${scraping_id}`,
        context: scraping_id,
      });

      /*

      {
        "url": "https://capriai.us/blog",
        "content": {
            "url": "https://beta.capriai.us/404",
            "content": "404: This page could not be found 🍪 Cookie Notice We use cookies to ensure that we give you the best experience on our website.   Read cookies policies (https://beta.capriai.us/privacy) . Accept 404 This page could not be found  ."
        }
    },

      */

      const scrapedResults = response.data.map((item: any) => ({
        accountId: uuidv4(),
        url: item.url,
        content: item.content.content,
      }));

      await Promise.all([
        ...scrapedResults.map(({ accountId, ...rest }) =>
          this.pineconeService.upsert(
            { orgId, accountId, agentId },
            JSON.stringify(rest),
          ),
        ),
        ...processingJobs.map((job, index) =>
          this.handleResourceStatus(agentId, {
            ...job,
            characterCount: scrapedResults[index].content?.length ?? 0,
            textContent: scrapedResults[index].content ?? "",
            status: MessageStatus.SUCCESS,
          }),
        ),
      ]);

      this.myLogger.log({
        message: `Successfully processed the website scraping for scraping_id: ${scraping_id}`,
        context: scraping_id,
      });

    } catch (error) {
      this.myLogger.error({
        message: error?.message || 'Error processing URLs',
        context: scraping_id,
      });

      await Promise.all(
        processingJobs.map((job) =>
          this.handleResourceStatus(agentId, {
            ...job,
            characterCount: 0,
            textContent: '',
            status: MessageStatus.FAILED,
            failedReason: `Failed to scrape website ${job.fileName}`,
          }),
        ),
      );
    }
  }

  async scrapeUrls(agentId: string, urls: string[]) {
    const agentData = await this.mongoAgentService.getAgent({ _id: agentId });
    const { orgId } = agentData;

    let scraping_id = uuidv4();

    this.myLogger.log({
      message: `Scraping URLs: ${JSON.stringify(
        urls,
      )} with scraping_id: ${scraping_id}`,
      context: scraping_id,
    });

    const processingJobs = urls.map((url) => ({
      jobId: 'manual-processing-website',
      accountId: uuidv4(),
      fileName: url,
      jobType: JobType.WEBSITE_SCRAP,
      maxDepth: 0,
      scraping_id
    }));

    try {
      await this.mongoAgentService.updateAgent(
        { _id: agentId },
        { $push: { processingJobs: { $each: processingJobs } } },
      );

      // Return early response indicating processing started
      this.processMultipleWebsites(
        scraping_id,
        agentId,
        orgId,
        urls,
        processingJobs,
      );

      return {
        success: true,
        message: 'Website processing started',
        processingJobs,
      };
    } catch (error) {
      this.myLogger.error({
        message: error?.message || 'Error initiating URL processing',
        context: scraping_id,
      });
      throw error?.message || `Failed to initiate processing for ${urls}`;
    }
  }

  async addRawTextKnowledgeSource(agentId: string, rawTextContent: string) {
    const { textContentKnowledgeSource, orgId } =
      await this.mongoAgentService.getAgent({ _id: agentId });
    let body: { accountId: string; textContent: string } | null = null;
    if (!textContentKnowledgeSource) {
      body = {
        accountId: uuidv4(),
        textContent: rawTextContent,
      };
    } else {
      body = {
        accountId: textContentKnowledgeSource.accountId,
        textContent: rawTextContent,
      };
      await this.pineconeService.deleteWithMetadata([
        textContentKnowledgeSource.accountId,
      ]);
    }
    

    await this.pineconeService.upsert(
      {
        orgId,
        accountId: body.accountId,
        agentId,
      },
      JSON.stringify(rawTextContent),
    );
    await this.mongoAgentService.updateAgent(
      { _id: agentId },
      { textContentKnowledgeSource: body },
    );
    return body;
  }

  async updateFollowUpConfig(agentId: string, params: FollowUpBodyDto) {
    const triggerId = params.triggerId;
    delete params['triggerId'];

    const updateResponse = await this.mongoAgentService.updateAgent({
      _id: agentId,
      'triggers.triggerId': triggerId
    }, {
      '$set': {
        'triggers.$.followUp': { ...params }
      }
    });
  }


  async markUnreadAfterReply(agentId: string, markUnreadAfterReply: boolean, response: Response) {
    try {
      await this.mongoAgentService.updateAgent({ _id: agentId }, {
        $set: {
          markUnreadAfterReply
        }
      });
      this.myLogger.log({
        message: `Agent updated successfully | Mark Unread after reply - ${markUnreadAfterReply} | ${agentId}`,
        context: `Mark Unread after reply | ${agentId}`
      })
      return response.status(HttpStatus.ACCEPTED).json({ message: `Agent updated successfully` });
    } catch (error) {
      this.myLogger.error({
        message: `Failed to update agent ${agentId} : error ${error?.message}`,
        context: `Mark Unread after reply | ${agentId}`,
      });
      handleException(response, error);
    }
  }
}
