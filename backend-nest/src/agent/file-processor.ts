/**
 * Handles processing of uploaded files (PDF, Word, Excel etc.) for agent knowledge
 */
import { Process, Processor } from '@nestjs/bull';
import { Job } from 'bull';
import { extname } from 'path';
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as pdf from 'pdf-parse';
import * as mammoth from 'mammoth';
import * as XLSX from 'xlsx';
import { PineconeService } from 'src/vector_db/pinecone/pinecone.service';
import { Document } from '@pinecone-database/doc-splitter';
import { MongoAgentService } from 'src/mongo/service/agent/mongo-agent.service';
import { KnowledgeService } from 'src/voice/vapi/knowledge/knowledge.service';
import { Readable } from 'stream';
import { JobType } from 'src/mongo/schemas/agents/agents.schema';
import { MyLogger } from 'src/logger/logger.service';

interface File {
  new (fileBits: BlobPart[], fileName: string, options?: FilePropertyBag): File;
}

interface AcceptedFileTypes {
  [key: string]: string[];
}

@Injectable()
@Processor('file-processing')
export class FileProcessor {
  private readonly ACCEPTED_FILE_TYPES: AcceptedFileTypes;
  private readonly MAX_FILE_SIZE: number;

  constructor(
    private readonly myLogger: MyLogger,
    private readonly mongoAgentService: MongoAgentService,
    private configService: ConfigService,
    private readonly pineconeService: PineconeService,
    private readonly knowledgeService: KnowledgeService,
  ) {
    this.ACCEPTED_FILE_TYPES = {
      'application/pdf': ['.pdf'],
      'text/plain': ['.txt'],
      'application/msword': ['.doc'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
        ['.docx'],
      'application/vnd.ms-excel': ['.xls'],
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': [
        '.xlsx',
      ],
    };
    this.MAX_FILE_SIZE = this.configService.get<number>(
      'MAX_FILE_SIZE',
      10 * 1024 * 1024,
    ); // 10MB default
  }

  private validateFile(
    filename: string,
    mimetype: string,
    size: number,
  ): boolean {
    if (size > this.MAX_FILE_SIZE) {
      this.myLogger.error({
        message: `File ${filename} exceeds maximum size of ${this.MAX_FILE_SIZE} bytes`,
        context: 'validateFile',
      });
      return false;
    }

    const ext = extname(filename).toLowerCase();
    if (!Object.values(this.ACCEPTED_FILE_TYPES).flat().includes(ext)) {
      this.myLogger.error({
        message: `File ${filename} has an invalid extension`,
        context: 'validateFile',
      });
      return false;
    }

    if (!Object.keys(this.ACCEPTED_FILE_TYPES).includes(mimetype)) {
      this.myLogger.error({
        message: `File ${filename} has an invalid mime type`,
        context: 'validateFile',
      });
      return false;
    }

    if (!this.ACCEPTED_FILE_TYPES[mimetype].includes(ext)) {
      this.myLogger.error({
        message: `File ${filename} extension does not match its mime type`,
        context: 'validateFile',
      });
      return false;
    }

    return true;
  }

  @Process('process-file')
  async handleFileProcessing(job: Job): Promise<{
    success: boolean;
    message: string;
    content?: string;
    result?: Document;
  }> {
    const { context, filename, buffer, mimetype } = job.data;
    const { agentId, orgId, accountId } = context;
    try {
      const fileBuffer = Buffer.from(buffer, 'base64');

      if (!this.validateFile(filename, mimetype, fileBuffer.length)) {
        throw new Error(`Invalid file: ${filename}`);
      }

      this.myLogger.log({
        message: `Processing file ${filename} for agent ${agentId} which belongs to ${orgId}. AccountId is ${accountId}`,
        context: 'handleFileProcessing',
      });
      this.myLogger.log({
        message: `File size: ${fileBuffer.length} bytes`,
        context: 'handleFileProcessing',
      });
      this.myLogger.log({
        message: `File type: ${mimetype}`,
        context: 'handleFileProcessing',
      });

      const content = await this.processFileByType(
        mimetype,
        fileBuffer,
        filename,
      );
      

      let voiceFile = await this.knowledgeService.uploadVapiFile({
        agentId,
        fileContent: content,
        fileName: filename,
      });

      const result = await this.pineconeService.upsert(context, content);

      const characterCount = content.length;

      await this.mongoAgentService.updateAgent(
        { _id: job.data.context.agentId },
        {
          $pull: { processingJobs: { jobId: job.id } },
          $push: {
            processedFiles: {
              accountId,
              fileName: job.data.filename,
              mimeType: mimetype,
              characterCount,
              jobType: JobType.FILE_UPLOAD,
              voiceFileId: voiceFile.vapiFileId,
            },
          },
        },
      );

      return {
        success: true,
        message: `File ${filename} processed successfully`,
        content,
        result,
      };
    } catch (error) {
      this.myLogger.error({
        message: `Error processing file ${filename}:`,
        context: 'handleFileProcessing',
      });
      throw error;
    }
  }

  private async processFileByType(
    mimetype: string,
    fileBuffer: Buffer,
    filename: string,
  ): Promise<string> {
    switch (mimetype) {
      case 'application/pdf':
        return await this.processPDF(fileBuffer);
      case 'text/plain':
        return await this.processTextFile(fileBuffer);
      case 'application/msword':
      case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
        return await this.processWordDocument(fileBuffer);
      case 'application/vnd.ms-excel':
      case 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet':
        return await this.processExcelFile(fileBuffer);
      default:
        throw new Error(`Unsupported file type: ${mimetype}`);
    }
  }

  private async processPDF(fileBuffer: Buffer): Promise<string> {
    try {
      const data = await pdf(fileBuffer);
      return data.text;
    } catch (error) {
      this.myLogger.error({
        message: 'Error processing PDF: ' + error,
        context: 'processPDF',
      });
      throw new Error('Failed to process PDF file');
    }
  }

  private async processTextFile(fileBuffer: Buffer): Promise<string> {
    return fileBuffer.toString('utf-8');
  }

  private async processWordDocument(fileBuffer: Buffer): Promise<string> {
    try {
      const result = await mammoth.extractRawText({ buffer: fileBuffer });
      return result.value;
    } catch (error) {
      this.myLogger.error({
        message: 'Error processing Word document:' + error,
        context: 'processWordDocument',
      });
      throw new Error('Failed to process Word document');
    }
  }

  private async processExcelFile(fileBuffer: Buffer): Promise<string> {
    try {
      const workbook = XLSX.read(fileBuffer, { type: 'buffer' });
      let result = '';
      workbook.SheetNames.forEach((sheetName) => {
        const worksheet = workbook.Sheets[sheetName];
        const json = XLSX.utils.sheet_to_json(worksheet, {
          defval: null, // Set default value for empty cells to null
          raw: false, // This ensures dates and numbers are parsed
        });
        result += `Sheet: ${sheetName}\n${JSON.stringify(json, null, 2)}\n\n`;
      });
      return result;
    } catch (error) {
      this.myLogger.error({
        message: 'Error processing Excel file:' + error,
        context: 'processExcelFile',
      });
      throw new Error('Failed to process Excel file');
    }
  }
}
