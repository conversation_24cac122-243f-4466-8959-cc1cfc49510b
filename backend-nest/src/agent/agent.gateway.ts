import {
  WebSocketGateway,
  WebSocketServer,
  SubscribeMessage,
  MessageBody,
  OnGatewayConnection,
  OnGatewayDisconnect,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { v4 as uuidv4 } from 'uuid';

interface FAQ {
  question: string;
  answer: string;
}

interface AgentConfig {
  name: string;
  description: string;
  website: string;
  faqs: FAQ[];
}

@WebSocketGateway(Number(process.env.WS_PORT) || 3334, {
  cors: {
    origin: process.env.FRONTEND_URL || 'http://localhost:3000',
    credentials: true,
  },
})
export class AgentGateway implements OnGatewayConnection, OnGatewayDisconnect {
  @WebSocketServer()
  server: Server;

  // private readonly logger = new Logger('AgentGateway');
  // afterInit(server: Server) {
  //   this.logger.log('WebSocket Gateway Initialized');
  // }

  handleConnection(client: Socket) {
    // this.logger.log(`Client connected: ${client.id}`);
  }

  handleDisconnect(client: Socket) {
    // this.logger.log(`Client disconnected: ${client.id}`);
  }

  @SubscribeMessage('initializeAgent')
  async handleAgentInitialization(
    @MessageBody()
    data: {
      usid: string;
      orgId: string;
      agentConfig: AgentConfig;
    },
  ) {
    

    try {
      const { orgId, agentConfig, usid } = data;
      // Step 1: Create Agent
      this.server.emit('agentStatus', {
        status: 'creating',
        message: 'Creating new agent...',
      });
      const { agentId } = await this.createAgent(usid, orgId, agentConfig);
      // Step 2: Create Chat Session
      this.server.emit('agentStatus', {
        status: 'creating_session',
        message: 'Creating chat session...',
      });
      const { sessionId } = await this.createChatSession(usid, orgId, agentId);
      // Complete
      this.server.emit('agentStatus', {
        status: 'ready',
        message: 'Agent setup complete!',
        agentId,
        sessionId,
      });
    } catch (error) {
      // this.logger.error('Agent initialization failed:', error);
      this.server.emit('agentStatus', {
        status: 'error',
        message: `Failed to initialize agent. Error: ${
          error.message ?? 'Some error IDK'
        }`,
      });
    }
  }

  private async createAgent(
    usid: string,
    orgId: string,
    agentConfig: AgentConfig,
  ): Promise<{ agentId: string }> {
    const headers = {
      Cookie: `usid=${usid}; orgId=${orgId}`,
      'Content-Type': 'application/json',
    };  
    const faqWithId = agentConfig.faqs.map((faq) => ({
      id: uuidv4(),
      question: faq.question,
      answer: faq.answer
    }))

    const requestBodyForAgent = {
      orgId,
      actions: [],
      agentName: agentConfig.name,
      faqs: faqWithId,
      // aiProvider: null,
      disabled: true,
    };

    const responseForAgent = await fetch(
      `${process.env.BACKEND_URL}/agent/create`,
      {
        method: 'POST',
        headers,
        body: JSON.stringify(requestBodyForAgent),
      },
    );

    if (!responseForAgent.ok) {
      throw new Error(`Failed to create agent: ${responseForAgent.statusText}`);
    }

    const agent = await responseForAgent.json();
    const agentId = agent.data._id?.toString();

    const requestBodyForPrompt = {
      agentId,
      prompt: {
        name: 'First prompt',
        promptContent: agentConfig.description,
        isFallbackPrompt: false,
        customFallbackPrompt: '',
      },
    };

    const responseForPrompt = await fetch(
      `${process.env.BACKEND_URL}/agent/add/prompt`,
      {
        method: 'POST',
        headers,
        body: JSON.stringify(requestBodyForPrompt),
      },
    );

    if (!responseForPrompt.ok) {
      throw new Error(
        `Failed to create agent: ${responseForPrompt.statusText}`,
      );
    }
    return { agentId };
  }

  private async createChatSession(
    usid: string,
    orgId: string,
    agentId: string,
  ): Promise<{ sessionId: string }> {
    const headers = {
      Cookie: `usid=${usid}; orgId=${orgId}`,
      'Content-Type': 'application/json',
    };
    

    const requestBodyForSession = {
      orgId,
      agentId,
    };

    const responseForSession = await fetch(
      `${process.env.BACKEND_URL}/session/start`,
      {
        method: 'POST',
        headers,
        body: JSON.stringify(requestBodyForSession),
      },
    );

    if (!responseForSession.ok) {
      throw new Error(
        `Failed to create session: ${responseForSession.statusText}`,
      );
    }

    const session = await responseForSession.json();
    return { sessionId: session.data._id };
  }
}
