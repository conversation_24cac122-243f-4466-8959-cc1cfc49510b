/**
 * Main controller handling agent CRUD operations, prompts, triggers, and other core agent management endpoints
 */
import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Res,
  Delete,
  UseGuards,
  Req,
  Patch,
  Query,
  BadRequestException,
  Put,
  HttpStatus,
  UseInterceptors,
  UploadedFiles,
} from '@nestjs/common';
import { AgentService } from './agent.service';
import { OrganizationService } from 'src/organization/services/organization.service';
import { Response } from 'express';
import { CreateAgentDto } from 'src/mongo/dto/agent/create.dto';
import { GetAgentDto } from './dto/get.dto';
import { DeleteAgentDto } from './dto/delete.dto';
import { UpdateAgentNameDto } from './dto/update-agent-name-dto';
import { UpdateAiProviderDto } from './dto/update-agent-aiProvider-dto';
import { PushNewPromptDto } from './dto/push-prompt-dto';
import { EditPromptDto } from './dto/edit-prompt-dto';
import { EditActivePromptDto } from './dto/active-prompt-dto';
import { EditStatusDto } from './dto/edit-status-dto';
import { AddDatasourceDto } from './dto/add-datasource-dto';
import { EditDatasourceDto, GetDatasourceDto } from './dto/edit-action-dto';
import { AuthGuard } from 'src/auth/auth.guard';
import { RequestWithUser } from '../auth/auth.interface';
import { AddTriggerDto, EditTriggerDto } from './dto/add-trigger-dto';
import { DeleteTriggerDto } from './dto/delete-trigger.dto';
import { UpdateStatusTriggerDto } from './dto/update-trigger-status.dto';
import { ToggleStatusAgentDto } from './dto/toggle-status-dto';
import { ExistingAgentDataDto } from 'src/mongo/dto/agent/create_duplicate_dto';
import { handleException } from 'helpers/handleException';
import { ObjectId } from 'mongodb';
import { MyLogger } from 'src/logger/logger.service';
import { IHumanTakeover } from './dto/human-takeover.dto';
import { EditMainPromptInjectionDto } from './dto/mainPrompt_dto';
import { UpdateFailsafeAiProviderDto } from 'src/mongo/dto/agent/failsafe_dto';
import { FilesInterceptor } from '@nestjs/platform-express';
import { getAugmentedQueryDto } from './dto/get-augmented-query.dto';
import { UpdateFaqDto } from 'src/mongo/dto/agent/update-faq.dto';
import { CreateAgentConfigDTO } from './dto/agent-config.dto';
import { v4 as uuidv4 } from 'uuid';
import {
  FAQ,
  NotificationSettings,
} from 'src/mongo/schemas/agents/agents.schema';
import { HandleResourceDto } from './dto/handle-resourc.dto';
import { UpdateContextLengthPayload } from './dto/update-context-length-dto';
import { FollowUpBodyDto } from './dto/follow-up.dto';

@Controller('agent')
@UseGuards(AuthGuard)
export class AgentController {
  constructor(
    private readonly agentService: AgentService,
    // private readonly sessionService: SessionService,
    private readonly myLogger: MyLogger,
  ) {}

  @Patch('/:agentId/follow-up')
  async updateFollowUpConfig(@Param('agentId') agentId: string, @Body() body: FollowUpBodyDto, @Req() request: RequestWithUser, @Res() response: Response) {
    try {
      await this.agentService.updateFollowUpConfig(agentId, body);
      this.myLogger.log({
        message: `Follow up configured successfully for agent ${agentId} | trigger id - ${body.triggerId}`,
        context: `AGENT | SAVE FOLLOW UP`
      })
      return response.status(HttpStatus.OK).json({
        message: `Follow up configured successfully`
      })
    } catch (error) {
      this.myLogger.error({
        message: `Error saving follow up for agent ${agentId} | trigger id - ${body.triggerId} | error - ${error?.message}`,
        context: `AGENT | SAVE FOLLOW UP`
      });
      handleException(response, error);
    }
  }

  @Post('/create')
  async createAgent(
    @Req() request: RequestWithUser,
    @Body() createAgentDto: CreateAgentDto,
    @Res() response: Response,
  ) {
    const userId = request.userId;
    const createdAgent = await this.agentService.createAgent(
      userId,
      createAgentDto,
      response,
    );
  }

  @Post('/build/flow')
  async createAgentBuiltWithFlow(
    @Req() request: RequestWithUser,
    @Body() agentConfig: CreateAgentConfigDTO,
  ) {
    const userId = request.userId;
    const {
      faqs,
      name,
      orgId,
      folder,
      website,
      channel,
      description,
      calendarDetails,
    } = agentConfig;
    const faqWithId = faqs.map((faq) => ({
      id: uuidv4(),
      question: faq.question,
      answer: faq.answer,
    }));
    // Step 1: Create an agent
    const newAgent = await this.agentService.createAgent(userId, {
      actions: [],
      folder,
      agentName: name,
      faqs: faqWithId,
      disabled: true,
      aiProvider: null,
      orgId: orgId,
      website,
      calendarDetails,
    });
    //@ts-ignore
    const agentId = newAgent._id.toString();
    // Step 2: Setup the prompt
    await this.agentService.pushNewPrompt({
      agentId,
      prompt: {
        name: 'First prompt',
        promptContent: description,
        isFallbackPrompt: false,
        customFallbackPrompt: '',
      },
    });
    // Step 3: Add the channel details if any
    await this.agentService.addChannelDetails(agentId, channel);
    return { agentId };
  }

  @Post(':agentId/importVariables/:accountId')
  async importAllAgentVariables(
    @Param('agentId') agentId: string,
    @Param('accountId') accountId: string,
  ) {
    return this.agentService.handleImportAllAgentVariables(agentId, accountId);
  }

  @Get('get-variables/:accountId')
  async getVariablesUsingAccountId(@Param('accountId') accountId: string) {
    return this.agentService.handleVariables(accountId);
  }

  @Patch('/:agentId/resource-status')
  async handleResourceStatus(
    @Param('agentId') agentId: string,
    @Body() payload: HandleResourceDto,
  ) {
    return await this.agentService.handleResourceStatus(agentId, payload);
  }

  @Patch('/:agentId/update-faq')
  async updateAgentFaqs(
    @Param('agentId') agentId: string,
    @Body() updateFaq: UpdateFaqDto,
  ) {
    const { faqs } = updateFaq;
    const faqsWithIds: FAQ[] = faqs.map((faq) => ({
      id: uuidv4(),
      question: faq.question,
      answer: faq.answer,
    }));
    return await this.agentService.handleNewFaq(agentId, faqsWithIds);
  }

  @Post('/create/extract/:id')
  async createDuplicateAgent(
    @Req() request: RequestWithUser,
    @Param('id') id: string,
    @Body() createAgentDto: ExistingAgentDataDto,
    @Res() response: Response,
  ) {
    const userId = request.userId;
    const createdAgent = await this.agentService.createDuplicateAgent(
      userId,
      id,
      createAgentDto,
      response,
    );
  }

  // @Post('/edit/status')
  // async editAgentStatus(
  //   @Body() editStatusDto: EditStatusDto,
  //   @Res() response: Response,
  // ) {
  //   const editedStatus = await this.agentService.editAgentStatus(
  //     editStatusDto,
  //     response,
  //   );
  //   return editedStatus;
  // }

  @Patch('fallback-config')
  async fallbackConfig(@Body() b, @Res() response: Response) {
    try {
      const updatedAgent = await this.agentService.handleFallbackConfig(b);
      response
        .status(HttpStatus.OK)
        .json({ message: 'Fallback configuration updated successfully!' });
    } catch (error) {
      this.myLogger.error({
        message: `error encountered while updating fallback configuration : ${
          error.message
        } | ${JSON.stringify(error.stack)}`,
      });
      handleException(response, error);
    }
  }

  @Patch('context-length')
  async updateContextLength(@Body() body: UpdateContextLengthPayload, @Res() response: Response) {
    try {
      await this.agentService.handleUpdateContextLength(body);
      response
      .status(HttpStatus.OK)
      .json({ message: 'Context Length updated successfully!' });
    } catch (error) {
      this.myLogger.error({
        message: `error encountered while updating context length : ${
          error.message
        } | ${JSON.stringify(error.stack)}`,
      });
      handleException(response, error);
    }
  }

  @Get('/org/:orgId')
  async getOrganization(
    @Param('orgId') orgId: string,
    @Req() request: RequestWithUser,
    @Query('only-name') onlyName: string,
    @Res() response: Response,
  ) {
    try {
      // Use orgId from cookies if URL param is invalid, otherwise use URL param for backwards compatibility
      const finalOrgId = (!orgId || orgId === 'null' || orgId === 'undefined' || !ObjectId.isValid(orgId)) 
        ? request.orgId 
        : orgId;
      
      if (!finalOrgId || !ObjectId.isValid(finalOrgId)) {
        return response.status(HttpStatus.BAD_REQUEST).json({
          message: 'Invalid organization ID provided',
        });
      }
      
      const allAgents = await this.agentService.getAllAgentByOrganization(
        finalOrgId,
        response,
        onlyName === 'true',
      );
      return allAgents;
    } catch (error) {
      this.myLogger.error({
        message: error?.message,
        context: 'AgentController.getOrganization',
      });
      return response.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        message: 'An error occurred while retrieving organization agents',
      });
    }
  }

  @Get(':agentId/action-prompts')
  async getPrompts(@Param('agentId') agentId: string) {
    try {
      const prompts = await this.agentService.getPrompts(agentId);
      return prompts;
    } catch (error) {
      this.myLogger.error({
        message: error?.message,
        context: 'AgentController.getOrganization',
      });
    }
  }

  @Post('/edit/name')
  async updateAgentName(
    @Body() updateAgentNameDto: UpdateAgentNameDto,
    @Req() req: RequestWithUser,
    @Res() response: Response,
  ) {
    const updateAgentName = await this.agentService.updateAgentName(
      updateAgentNameDto.agentId,
      updateAgentNameDto.agentName,
      response,
    );
    return updateAgentName;
  }

  @Patch(':agentId/notifications')
  async updateNotificationSetting(
    @Param('agentId') agentId: string,
    @Body()
    body: { notificationType: keyof NotificationSettings; enabled: boolean },
  ) {
    return this.agentService.updateNotificationSetting(
      agentId,
      body.notificationType,
      body.enabled,
    );
  }

  @Post('/edit/aiprovider')
  async updateAgentAIProvider(
    @Body() updateAiProviderDto: UpdateAiProviderDto,
    @Res() response: Response,
  ) {
    const result = await this.agentService.updateAgentAIProvider(
      updateAiProviderDto,
      response,
    );
    return result;
  }

  @Post('/edit/failsafeaiprovider')
  async updateFailSafeAgentAIProvider(
    @Body() updateFailSafeAiProviderDto: UpdateFailsafeAiProviderDto,
    @Res() response: Response,
  ) {
    const result = await this.agentService.updateFailSafeAgentAIProvider(
      updateFailSafeAiProviderDto,
      response,
    );
    return result;
  }

  @Get('/:agentId/website/links/fetch-all')
  async fetchAllLinks(
    @Query() query: { website: string },
    @Param() agentId: string,
  ) {
    return await this.agentService.fetchAllLinks(agentId, query?.website);
  }

  @Post('/:agentId/website/scrape')
  async scrapeUrls(
    @Param() param: { agentId: string },
    @Body() body: { urls: string[] },
  ) {
    return await this.agentService.scrapeUrls(param.agentId, body.urls);
  }

  @Post('/:agentId/knowledge-sources/knowledge/raw-text')
  async addRawTextKnowledgeSource(
    @Param() param: { agentId: string },
    @Body() body: { textContent: string },
  ) {
    return await this.agentService.addRawTextKnowledgeSource(
      param.agentId,
      body.textContent,
    );
  }

  @Post('/add/prompt')
  async pushNewPrompt(
    @Body() pushNewPromptDto: PushNewPromptDto,
    @Res() response: Response,
  ) {
    const addedPrompt = await this.agentService.pushNewPrompt(
      pushNewPromptDto,
      response,
    );
    return addedPrompt;
  }

  @Post('/edit/prompt')
  async editPrompt(
    @Body() editPromptDto: EditPromptDto,
    @Res() response: Response,
  ) {
    return this.agentService.editPrompt(editPromptDto, response);
  }

  @Delete('prompt/:agentId/:promptId')
  async deletePrompt(
    @Param('agentId') agentId: string,
    @Param('promptId') promptId: string,
    @Res() response: Response,
  ) {
    await this.agentService.deletePrompt(agentId, promptId, response);
  }

  @Post('/edit/prompt/active')
  async editActivePrompt(
    @Body() editActivePromptDto: EditActivePromptDto,
    @Res() response: Response,
  ) {
    const activePrompt = await this.agentService.editActivePrompt(
      editActivePromptDto,
      response,
    );
    return activePrompt;
  }

  @Post('/add/action')
  async addDatasources(
    @Body() addDatasourceDto: AddDatasourceDto,
    @Res() response: Response,
  ) {
    const agent = await this.agentService.addDatasources(
      addDatasourceDto,
      response,
    );
    return agent;
  }

  @Post('/edit/action')
  async editDatasources(
    @Body() editDatasourceDto: EditDatasourceDto,
    @Res() response: Response,
  ) {
    const agent = await this.agentService.editDatasources(
      editDatasourceDto,
      response,
    );
    return agent;
  }

  @Get('/get/action')
  async getDatasources(
    @Body() getDatasourceDto: GetDatasourceDto,
    @Res() response: Response,
  ) {
    const agent = await this.agentService.getDatasources(
      getDatasourceDto,
      response,
    );
    return agent;
  }

  @Post('/add/trigger')
  async addTrigger(
    @Body() addTriggerDto: AddTriggerDto,
    @Res() response: Response,
  ) {
    try {
      const trigger = await this.agentService.addTriggers(
        addTriggerDto,
        response,
      );
      return trigger;
    } catch (error) {
      handleException(response, error);
    }
  }

  @Post('/edit/trigger')
  async editTrigger(@Body() body: EditTriggerDto, @Res() response: Response) {
    try {
      const trigger = await this.agentService.editTrigger(body);
      return response
        .status(200)
        .json({ message: 'Trigger Edited Successfully.', trigger });
    } catch (error) {
      handleException(response, error);
    }
  }

  @Post('/trigger/status')
  async updateTriggerStatus(
    @Body() updateStatusTriggerDto: UpdateStatusTriggerDto,
    @Res() response: Response,
  ) {
    try {
      await this.agentService.updateTriggerStatus(
        updateStatusTriggerDto,
        response,
      );
    } catch (error) {
      handleException(response, error);
    }
  }

  @Delete('trigger/:agentId/:triggerId')
  async deleteTrigger(
    @Param('agentId') agentId: string,
    @Param('triggerId') triggerId: string,
    @Res() response: Response,
  ) {
    try {
      await this.agentService.deleteTrigger(agentId, triggerId, response);
    } catch (error) {
      handleException(response, error);
    }
  }

  @Delete('action/:agentId/:actionId')
  async deleteAction(
    @Param('agentId') agentId: string,
    @Param('actionId') actionId: string,
    @Res() response: Response,
  ) {
    await this.agentService.deleteAction(agentId, actionId, response);
  }

  //delete an agent
  @Delete('/:id')
  async deleteAgent(@Param() param: DeleteAgentDto, @Res() response: Response) {
    const agent = await this.agentService.deleteAgent(param.id, response);
  }

  //disable an agent
  @Patch('/status/:id')
  async toggleAgent(
    @Param() param: DeleteAgentDto,
    @Body() toggleDto: ToggleStatusAgentDto,
    @Res() response: Response,
  ) {
    const agent = await this.agentService.toggleAgent(
      param.id,
      toggleDto.disabled,
      response,
    );
  }

  @Post('create/quick')
  @UseGuards(AuthGuard)
  async createQuickAgent(
    @Req() req: RequestWithUser,
    @Body() createQuickAgentDto: any,
    @Res() response: Response,
  ) {
    await this.agentService.createQuickAgent(
      createQuickAgentDto,
      response,
      req.userId,
    );
  }

  @Get('history/all/:agentId')
  async getAllAgentHistory(
    @Req() req: RequestWithUser,
    @Param('agentId') agentId: string,
    @Query() query: GetAllHistoryQueryParams,
    @Res() response: Response,
  ) {
    try {
      const agentHistory = await this.agentService.getAllAgentHistory(
        req.uniqueCode,
        agentId,
        query,
      );
      return response.status(200).json(agentHistory);
    } catch (err) {
      handleException(response, err);
    }
  }

  @Get('history/:contactId')
  async getDetailedHistory(
    @Req() req: RequestWithUser,
    @Query() query: GetAllHistoryQueryParams,
    @Param('contactId') contactId: string,
    @Res() response: Response,
  ) {
    try {
      const agentHistory = await this.agentService.getDetailedHistory(
        req.uniqueCode,
        contactId,
        query,
      );
      return response.status(200).json(agentHistory);
    } catch (err) {
      handleException(response, err);
    }
  }

  @Get('list/ghl-calendars')
  async getGhlCalendars(
    @Req() req: RequestWithUser,
    @Query('agentId') agentId: string,
    @Res() res: Response,
  ) {
    try {
      if (!agentId || !ObjectId.isValid(agentId))
        throw new BadRequestException('agentId invalid');
      const calendars = await this.agentService.getGhlCalendarsAction(agentId);
      res.status(200).json(calendars);
    } catch (err) {
      handleException(res, err);
    }
  }

  @Get('list/tags')
  async getTags(
    @Req() req: RequestWithUser,
    @Query('agentId') agentId: string,
    @Res() res: Response,
  ) {
    try {
      if (!agentId || !ObjectId.isValid(agentId))
        throw new BadRequestException('agentId invalid');
      const tags = await this.agentService.getTagsAction(agentId);
      res.status(200).json(tags);
    } catch (err) {
      handleException(res, err);
    }
  }

  @Post(':agentId/add/human-takeover')
  async addHumanTakeover(
    @Req() req: RequestWithUser,
    @Param('agentId') agentId: string,
    @Body() payload: IHumanTakeover,
    @Res() response: Response,
  ) {
    await this.agentService.addHumanTakeover(agentId, payload, response);
  }

  @Patch('/:agentId/multiple-inbound')
  async handleMultipleInboundConfigs(
    @Param('agentId') agentId: string,
    @Body()
    payload: {
      multipleInbound: boolean;
      initialWait?: number;
      maxWait?: number;
      incrementBy?: number;
    },
    @Res() response: Response,
  ) {
    await this.agentService.handleMultipleInboundConfigs(agentId, payload);
    response.status(HttpStatus.OK).send({ message: 'Successfully updated' });
  }

  @Post(':agentId/variables')
  async addVariable(
    @Param('agentId') agentId: string,
    @Body()
    variable: {
      name: string;
      type: 'static' | 'dynamic';
      value?: string;
      providerName?: string;
      fieldKey?: string;
    },
  ) {
    try {
      const updatedAgent = await this.agentService.addVariable(
        agentId,
        variable,
      );
      return { success: true, data: updatedAgent };
    } catch (error) {
      throw error;
    }
  }

  @Post(':agentId/variables/bulk')
  async addVariables(
    @Param('agentId') agentId: string,
    @Body('variables')
    variables: Array<{
      name: string;
      type: 'static' | 'dynamic';
      value?: string;
      providerName?: string;
      fieldKey?: string;
    }>,
  ) {
    try {
      if (!Array.isArray(variables)) {
        throw new TypeError('Variables must be an array');
      }

      const processedVariables = variables.map((variable) => {
        if (variable.type === 'dynamic' && variable.fieldKey) {
          variable.fieldKey = `{{agent.${variable.fieldKey}}}`;
        }
        return variable;
      });

      const updatedAgent = await this.agentService.addVariables(
        agentId,
        processedVariables,
      );
      return { success: true, data: updatedAgent };
    } catch (error) {
      
      throw error;
    }
  }

  @Get(':agentId/variables')
  async getVariables(@Param('agentId') agentId: string) {
    try {
      const variables = await this.agentService.getVariables(agentId);
      return { success: true, data: variables };
    } catch (error) {
      throw error;
    }
  }

  // 3. Delete a variable
  @Delete(':agentId/variables/:variableId')
  async deleteVariable(
    @Param('agentId') agentId: string,
    @Param('variableId') variableId: string,
  ) {
    try {
      const updatedAgent = await this.agentService.deleteVariable(
        agentId,
        variableId,
      );
      return { success: true, data: updatedAgent };
    } catch (error) {
      throw error;
    }
  }

  // 4. Update a specific variable
  @Patch(':agentId/variables/:variableId')
  async updateVariable(
    @Param('agentId') agentId: string,
    @Param('variableId') variableId: string,
    @Body()
    updateData: {
      type?: 'static' | 'dynamic';
      value?: string;
      providerName?: string;
      fieldKey?: string;
    },
  ) {
    try {
      const updatedAgent = await this.agentService.updateVariable(
        agentId,
        variableId,
        updateData,
      );
      return { success: true, data: updatedAgent };
    } catch (error) {
      throw error;
    }
  }

  @Get('variables/location/:locationId')
  async getVariablesfromLocation(@Param('locationId') locationId: string) {
    try {
      const variables = await this.agentService.locationVariables(locationId);

      const standardFields = [
        'name',
        'first_name',
        'last_name',
        'email',
        'phone',
        'date_of_birth',
        'source',
        'type',
        'linkedin_profile',
        'industry',
        'company_name',
        'address1',
        'city',
        'country',
        'state',
        'postal_code',
        'website',
        'timezone',
        'credit_score',
      ];

      for (const field of standardFields) {
        variables.push({
          id: '',
          value: '',
          name: field,
          model: 'standard',
          fieldKey: `contact.${field}`,
        });
      }

      return { success: true, data: variables };
    } catch (error) {
      throw error;
    }
  }

  @Get('variables/:orgId/ghl-subaccounts')
  async listGhlSubaccounts(@Param('orgId') orgId: string) {
    try {
      const accounts = await this.agentService.getGHLSubaccountList(orgId);
      return { success: true, data: accounts };
    } catch (error) {
      throw error;
    }
  }

  @Post('action/mainPrompt')
  async updateMainPromptToggle(
    @Body() editMainPromptInjectionDto: EditMainPromptInjectionDto,
    @Res() response: Response,
  ) {
    const updatedAgent = await this.agentService.updateMainPromptToggle(
      editMainPromptInjectionDto,
    );
    response
      .status(HttpStatus.OK)
      .json({ message: 'Action updated successfully!' });
  }

  @Patch('attachment/config')
  async handleAttachmentConfig(@Body() b, @Res() response: Response) {
    try {
      const updatedAgent = await this.agentService.handleAttachmentConfig(b);
      response
        .status(HttpStatus.OK)
        .json({ message: 'Action updated successfully!' });
    } catch (error) {
      this.myLogger.error({
        message: `error encountered while adding attachment tag : ${
          error.message
        } | ${JSON.stringify(error.stack)}`,
      });
      handleException(response, error);
    }
  }

  @Patch('empty-message/config')
  async handleEmptyMessageConfig(@Body() b, @Res() response: Response) {
    try {
      const updatedAgent = await this.agentService.handleEmptyMessage(b);
      response
        .status(HttpStatus.OK)
        .json({ message: 'Action updated successfully!' });
    } catch (error) {
      this.myLogger.error({
        message: `error encountered while updating empty message handler : ${
          error.message
        } | ${JSON.stringify(error.stack)}`,
      });
      handleException(response, error);
    }
  }

  @Post(':agentId/file-upload')
  @UseInterceptors(FilesInterceptor('files'))
  async uploadFiles(
    @Param('agentId') agentId: string,
    @UploadedFiles() files: Express.Multer.File[],
  ) {
    const queuedFiles = await this.agentService.processFiles(agentId, files);
    // return this.fileUploadService.processFiles(agentId, files);
    return queuedFiles;
  }

  @Get(':agentId/files/status')
  async getAllFileStatus(@Param('agentId') agentId: string) {
    return await this.agentService.getAllFileStatus(agentId);
  }

  @Post(':agentId/augmented-query')
  async getAugmentedQuery(
    @Body() getAugmentedQueryDto: getAugmentedQueryDto,
    @Param('agentId') agentId: string,
  ) {
    return await this.agentService.getAugmentedQuery(
      agentId,
      getAugmentedQueryDto,
    );
  }

  @Post(':agentId/augmented-query-response')
  async getAugmentedQueryResponse(
    @Body() getAugmentedQueryDto: getAugmentedQueryDto,
    @Param('agentId') agentId: string,
  ) {
    return await this.agentService.getAugmentedQueryResponse(
      agentId,
      getAugmentedQueryDto,
    );
  }

  @Delete(':agentId/knowledge-sources/processed-files/:accountId')
  async removeKnowledgeSourceProcessedFiles(
    @Param('agentId') agentId: string,
    @Param('accountId') accountId: string,
  ) {
    await this.agentService.removeKnowledgeSourceProcessedFiles(
      agentId,
      accountId,
    );
    return { message: 'Knowledge source removed successfully.' };
  }
  @Delete(':agentId/knowledge-sources/processing-jobs/:accountId')
  async removeKnowledgeSourceProcessingJobs(
    @Param('agentId') agentId: string,
    @Param('accountId') accountId: string,
  ) {
    await this.agentService.removeKnowledgeSourceProcessingJobs(
      agentId,
      accountId,
    );
    return { message: 'Knowledge source removed successfully.' };
  }

  @Get('/check-name')
  async checkAgentNameUniqueness(
    @Query('agentname') agentname: string,
    @Req() req: RequestWithUser,
  ) {
    const orgId = req.cookies['orgId'];
    return await this.agentService.checkAgentNameUniqueness(orgId, agentname);
  }

  //get an agent
  @Get('/:id')
  async getAgent(@Param() param: GetAgentDto, @Res() response: Response) {
    const agent = await this.agentService.getAgent(param.id, response);
  }

  @Patch('/:agentId/mark-unread')
  async markUnreadAfterReply(
    @Param('agentId') agentId: string,
    @Body('markUnreadAfterReply') markUnreadAfterReply: boolean,
    @Res() response: Response,
  ) {
    await this.agentService.markUnreadAfterReply(agentId, markUnreadAfterReply, response);
  }
}
