import { Module, forwardRef } from '@nestjs/common';
import { AgentController } from './agent.controller';
import { AgentService } from './agent.service';
import { UserService } from 'src/user/user.service';
import { OrganizationModule } from 'src/organization/organization.module';
import { UserModule } from 'src/user/user.module';
import { FoldersModule } from 'src/folders/folders.module';
import { VoiceModule } from 'src/voice/voice.module';
import { AgentFileUpload } from './file-upload.service';
import { FileProcessor } from './file-processor';
import { BullModule } from '@nestjs/bull';
import { VectorDbModule } from 'src/vector_db/vector_db.module';
import { PineconeService } from 'src/vector_db/pinecone/pinecone.service';
import { LoggerModule } from 'src/logger/logger.module';
import { MongoVoiceUsageService } from 'src/mongo/service/voiceUsage/voice-usage.service';
import { AgentGateway } from './agent.gateway';
import { PublicApiController } from './public-api/public-api.controller';
import { TokensModule } from 'src/tokens/tokens.module';
import { PublicApiService } from './public-api/public-api.service';
import { IframeGhlModule } from 'src/iframe-ghl/iframe-ghl.module';

@Module({
  imports: [
    forwardRef(() => OrganizationModule),
    UserModule,
    FoldersModule,
    forwardRef(() => VoiceModule),
    BullModule.registerQueue({
      name: 'file-processing',
    }),
    BullModule.registerQueue({
      name: 'website-processing',
    }),
    VectorDbModule,
    LoggerModule,
    TokensModule,
    forwardRef(() => IframeGhlModule),
  ],
  controllers: [AgentController, PublicApiController],
  providers: [
    AgentService,
    AgentFileUpload,
    FileProcessor,
    PineconeService,
    MongoVoiceUsageService,
    AgentGateway,
    UserService,
    PublicApiService,
  ],
  exports: [AgentService],
})
export class AgentModule {}
