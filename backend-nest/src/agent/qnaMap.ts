/***
 * Utility for mapping Q&A data structures for quick create agent 
 */
export const qnaMap = (payload) => {
    let qna = []
    if (payload?.companyInfo?.businessName) {
        qna.push({
            question: `What is the name of your business ?`,
            answer: payload?.companyInfo?.businessName
        })
    }
    if (payload?.companyInfo?.companyLocation) {
        qna.push({
            question: `Where are your operations located ?`,
            answer: payload?.companyInfo?.companyLocation
        })
    }
    if (payload?.companyInfo?.website) {
        qna.push({
            question: `Can you provied me your website ?`,
            answer: payload?.companyInfo?.website
        })
    }
    if (payload?.companyInfo?.agentName) {
        qna.push({
            question: `What is your assistant name ?`,
            answer: payload?.companyInfo?.agentName
        })
    }

    if (payload?.additionalInfo?.areaServed) {
        qna.push({
            question: `What is your area of service ?`,
            answer: payload?.additionalInfo?.areaServed
        })
    }
    if (payload?.additionalInfo?.areaOfPractice) {
        qna.push({
            question: `What is your area of practice ?`,
            answer: payload?.additionalInfo?.areaOfPractice
        })
    }
    if (payload?.additionalInfo?.primaryService) {
        qna.push({
            question: `What is your primary service ?`,
            answer: payload?.additionalInfo?.primaryService
        })
    }
    if (payload?.faq) {
        qna.push(...(payload.faq || []))
    }
    return qna;
}