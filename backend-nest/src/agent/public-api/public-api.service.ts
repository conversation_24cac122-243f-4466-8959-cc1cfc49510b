import { BadRequestException, Injectable } from '@nestjs/common';
import { AgentService } from '../agent.service';
import { MyLogger } from 'src/logger/logger.service';
import { ConnectGhlLocationDto } from '../dto/public-api.dto';
import { MongoOrganizationService } from 'src/mongo/service/organization/mongo-organization.service';

@Injectable()
export class PublicApiService {
    constructor(
        private readonly agentService: AgentService,
        private readonly logger: MyLogger,
        private readonly mongoOrganizationService: MongoOrganizationService,
    ) { }

    async checkLocationExistsAsChannel(orgId: string, params: ConnectGhlLocationDto) {
        // check if the location is present as a channel
        const org = (await this.mongoOrganizationService.getOrganization({
            _id: orgId,
            'connections.channels.keyId': params.locationId
        }, {
            'connections.channels': 1
        }))

        const channel = org?.connections?.channels?.find((channel) => channel.keyId === params.locationId);

        // if not then throw an error
        if (!channel) throw new BadRequestException('No channel found with the given locationId under your Capri organization');

        return channel;
    }

}
