/* 
contains endpoints for external/public API access to agent functionality
*/
import {
    Body,
    Controller,
    Get,
    Param,
    Post,
    Res,
    Delete,
    UseGuards,
    Req,
    Patch,
    Query,
    BadRequestException,
    Put,
    HttpStatus,
    UseInterceptors,
    UploadedFiles,
    UploadedFile,
    UnauthorizedException,
} from '@nestjs/common';
import { MyLogger } from 'src/logger/logger.service';
import { AgentService } from '../agent.service';
import { CreateAgentDto, PublicApiCreateAgentDto } from '../dto/create-agent.dto';
import { Response } from 'express';
import { v4 as uuidv4 } from 'uuid';
import {
    AgentDocument,
    FAQ,
    NotificationSettings,
} from 'src/mongo/schemas/agents/agents.schema';
import { UpdateFaqDto } from 'src/mongo/dto/agent/update-faq.dto';
import { UpdateAgentNameDto } from '../dto/update-agent-name-dto';
import { FilesInterceptor } from '@nestjs/platform-express';
import { EditActivePromptDto } from '../dto/active-prompt-dto';
import { EditPromptDto } from '../dto/edit-prompt-dto';
import { GetAgentDto } from '../dto/get.dto';
import { PushNewPromptDto } from '../dto/push-prompt-dto';
import { TokensService } from 'src/tokens/tokens.service';
import { ExistingAgentDataDto } from 'src/mongo/dto/agent/create_duplicate_dto';
import { DeleteAgentDto } from '../dto/delete.dto';
import { ToggleStatusAgentDto } from '../dto/toggle-status-dto';
import { handleException } from 'helpers/handleException';
import { AssignAgentToLocation, ConnectGhlLocationDto, IPublicApiAddKnowledgeDto } from '../dto/public-api.dto';
import { PublicApiService } from './public-api.service';
import { AddFaqDto } from 'src/iframe-ghl/dto/faq.dto';
import { IframeHelperService } from 'src/iframe-ghl/iframe-helper.service';
import { MongoAgentService } from 'src/mongo/service/agent/mongo-agent.service';
import { FoldersService } from 'src/folders/folders.service';
import { FolderType } from 'src/mongo/schemas/folders/folders.schema';
import { ChannelsService } from 'src/organization/services/channels/channels.service';


@Controller('v1/agent')
export class PublicApiController {

    constructor(
        private readonly agentService: AgentService,
        private readonly tokenService: TokensService,
        private readonly logger: MyLogger,
        private readonly publicApiService: PublicApiService,
        private readonly iframeHelperService: IframeHelperService,
        private readonly mongoAgentService: MongoAgentService,
        private readonly foldersService: FoldersService,
        private readonly channelService: ChannelsService,
    ) { }

    async validateToken(req: Request) {
        let authorization = (req.headers['authorization'] || '') as any;
        authorization = authorization.split(' ');

        const capriToken =
            authorization.length > 1 ? authorization[1] : undefined;

        if (!capriToken) throw new UnauthorizedException('Capri token not found');
        const token = await this.tokenService.getTokenByToken({
            token: capriToken,
        });

        if (!token?.token) throw new UnauthorizedException('Invalid token');
        const orgId = token?.orgId;
        this.logger.log({
            message: `Request for orgId - ${orgId}`,
            context: 'PUBLIC API VALIDATE TOKEN',
        })
        return orgId;
    }

    /*
    create an agent
    */
    @Post('/create')
    async createAgent(
        @Req() req: Request,
        @Body() createAgentDto: PublicApiCreateAgentDto,
        @Res() response: Response,
    ) {
        try {
            const orgId = await this.validateToken(req);
            const activePromptId = uuidv4();
            const prompts = createAgentDto.prompts ? {
                currentActive: activePromptId,
                prompt: [{
                    promptId: activePromptId,
                    name: createAgentDto?.prompts?.name,
                    promptContent: createAgentDto?.prompts?.promptContent,
                    customFallbackPrompt: createAgentDto?.prompts?.customFallbackPrompt,
                    isFallbackPrompt: false
                }]
            } : undefined;

            const createdAgent = await this.agentService.createAgent(
                '',
                { ...createAgentDto, prompts, orgId, disabled: false, aiProvider: undefined, actions: [] },
            );

            if (createdAgent) {
                response.status(HttpStatus.CREATED).json(createdAgent);
            } else {
                throw new BadRequestException('Failed to create agent');
            }
        } catch (error) {
            this.logger.error({
                message: error instanceof Error ? error.message : 'Failed to create agent',
                context: 'PUBLIC API CREATE AGENT',
            });
            handleException(response, error);
        }
    }

    /*
    update agent faq
    */
    @Post('/:agentId/update-faq')
    async updateAgentFaqs(@Req() req: Request,
        @Param('agentId') agentId: string,
        @Body() updateFaq: UpdateFaqDto,
        @Res() response: Response,
    ) {
        try {
            const orgId = await this.validateToken(req);

            const agentData: AgentDocument = await this.mongoAgentService.getAgent({ _id: agentId });
            if (!agentData) throw new BadRequestException('Agent not found');

            const newFaqs = updateFaq.faqs.map((faq) => ({
                id: uuidv4(),
                question: faq.question,
                answer: faq.answer,
            }));
            const updatedFaqs = [...agentData.faqs, ...newFaqs];
            const r = await this.iframeHelperService.handleNewFaq(agentId, updatedFaqs);

            response.status(HttpStatus.OK).json(r);
        } catch (error) {
            this.logger.error({
                message: `Error in updating agent faqs : ` + error.message,
                context: 'PUBLIC API UPDATE AGENT FAQS',
            });
            handleException(response, error);
        }
    }


    /*
    Update an agent's name
    */
    @Post('/edit/name')
    async updateAgentName(
        @Body() updateAgentNameDto: UpdateAgentNameDto,
        @Req() req: Request,
        @Res() response: Response,
    ) {
        try {
            const orgId = await this.validateToken(req);

            const updateAgentName = await this.agentService.updateAgentName(
                updateAgentNameDto.agentId,
                updateAgentNameDto.agentName,
                response,
            );
        } catch (error) {
            this.logger.error({
                message: `Error in updating agent name : ` + error.message,
                context: 'PUBLIC API UPDATE AGENT NAME',
            });
            handleException(response, error);
        }
    }

    /*
    scrape a website and add to the agent
    */
    @Post('/:agentId/website/scrape')
    async scrapeUrls(@Req() req: Request,
        @Param() param: { agentId: string },
        @Body() body: { urls: string[] },
        @Res() response: Response,
    ) {
        try {
            const orgId = await this.validateToken(req);

            const result = await this.agentService.scrapeUrls(param.agentId, body.urls);

            return response.status(HttpStatus.OK).json(result);
        } catch (error) {
            this.logger.error({
                message: `Error in scraping urls : ` + error.message,
                context: 'PUBLIC API SCRAPE URLS',
            });
            handleException(response, error);
        }
    }

    /*
    Add a raw text as an all-est source to the agent.
    */
    @Post('/:agentId/knowledge-sources/knowledge/raw-text')
    async addRawTextKnowledgeSource(@Req() req: Request,
        @Param() param: { agentId: string },
        @Body() body: { textContent: string },
        @Res() response: Response,
    ) {
        try {
            const orgId = await this.validateToken(req);

            const result = await this.agentService.addRawTextKnowledgeSource(
                param.agentId,
                body.textContent,
            );

            return response.status(HttpStatus.OK).json(result);
        } catch (error) {
            this.logger.error({
                message: `Error in adding raw text knowledge source : ` + error.message,
                context: 'PUBLIC API ADD RAW TEXT KNOWLEDGE SOURCE',
            })
            handleException(response, error);
        }
    }

    /*
    
    */
    @Post('/add/prompt')
    async pushNewPrompt(@Req() req: Request,
        @Body() pushNewPromptDto: PushNewPromptDto,
        @Res() response: Response,
    ) {
        try {
            const orgId = await this.validateToken(req);

            const addedPrompt = await this.agentService.pushNewPrompt(
                pushNewPromptDto,
                response,
            );
        } catch (error) {
            this.logger.error({
                message: `Error in pushing new prompt : ` + error.message,
                context: 'PUBLIC API PUSH NEW PROMPT',
            })
            handleException(response, error);
        }
    }

    /*
    Edit the prompt for an agent.
    */
    @Post('/edit/prompt')
    async editPrompt(@Req() req: Request,
        @Body() editPromptDto: EditPromptDto,
        @Res() response: Response,
    ) {
        try {
            const orgId = await this.validateToken(req);

            return this.agentService.editPrompt(editPromptDto, response);
        } catch (error) {
            this.logger.error({
                message: `Error in editing prompt : ` + error.message,
                context: 'PUBLIC API EDIT PROMPT',
            })
            handleException(response, error);
        }
    }

    /*
    Delete a prompt from an agent.
    */
    @Delete('prompt/:agentId/:promptId')
    async deletePrompt(@Req() req: Request,
        @Param('agentId') agentId: string,
        @Param('promptId') promptId: string,
        @Res() response: Response,
    ) {
        try {
            const orgId = await this.validateToken(req);

            const result = await this.agentService.deletePrompt(agentId, promptId, response);
        } catch (error) {
            this.logger.error({
                message: `Error in deleting prompt : ` + error.message,
                context: 'PUBLIC API DELETE PROMPT',
            })
            handleException(response, error);
        }
    }

    /*
    update the active count.
    */
    @Post('/edit/prompt/active')
    async editActivePrompt(@Req() req: Request,
        @Body() editActivePromptDto: EditActivePromptDto,
        @Res() response: Response,
    ) {
        try {
            const orgId = await this.validateToken(req);

            const activePrompt = await this.agentService.editActivePrompt(
                editActivePromptDto,
                response,
            );
        } catch (error) {
            this.logger.error({
                message: `Error in editing active prompt : ` + error.message,
                context: 'PUBLIC API EDIT ACTIVE PROMPT',
            })
            handleException(response, error);
        }
    }

    /*
    Upload a file and add it as a knowledge source to the agent.
    */
    @Post(':agentId/file-upload')
    @UseInterceptors(FilesInterceptor('files'))
    async uploadFiles(@Req() req: Request,
        @Param('agentId') agentId: string,
        @UploadedFiles() files: Express.Multer.File[],
        @Res() response: Response,
    ) {
        try {
            const orgId = await this.validateToken(req);

            
            const queuedFiles = await this.agentService.processFiles(agentId, files);
            // return this.fileUploadService.processFiles(agentId, files);

            return response.status(HttpStatus.OK).json(queuedFiles);
        } catch (error) {
            this.logger.error({
                message: `Error in uploading files : ` + error.message,
                context: 'PUBLIC API UPLOAD FILES',
            })
            handleException(response, error);
        }
    }

    /*
    Get uploaded files status. Not yet implemented as a public API in make module.
    */
    @Get(':agentId/files/status')
    async getAllFileStatus(@Req() req: Request, @Param('agentId') agentId: string, @Res() response: Response,) {
        try {
            const orgId = await this.validateToken(req);

            const fileStatus = await this.agentService.getAllFileStatus(agentId);

            return response.status(HttpStatus.OK).json(fileStatus);
        } catch (error) {
            this.logger.error({
                message: `Error in getting file status : ` + error.message,
                context: 'PUBLIC API GET ALL FILE STATUS',
            })
            handleException(response, error);
        }
    }

    /*
    Remove knowledge source from an agent.
    */
    @Delete(':agentId/knowledge-sources/processed-files/:accountId')
    async removeKnowledgeSourceProcessedFiles(@Req() req: Request,
        @Param('agentId') agentId: string,
        @Param('accountId') accountId: string,
        @Res() response: Response,
    ) {
        try {
            const orgId = await this.validateToken(req);

            await this.agentService.removeKnowledgeSourceProcessedFiles(
                agentId,
                accountId,
            );
            return response.status(HttpStatus.OK).json({ message: 'Knowledge source removed successfully.' });
        } catch (error) {
            this.logger.error({
                message: `Error in removing knowledge source processed files : ` + error.message,
                context: 'PUBLIC API REMOVE KNOWLEDGE SOURCE PROCESSED FILES',
            })
            handleException(response, error);
        }
    }

    /*
    Remove knowledge source processing jobs not imported as a public API and also not implemented as a Make module.
    */
    @Delete(':agentId/knowledge-sources/processing-jobs/:accountId')
    async removeKnowledgeSourceProcessingJobs(@Req() req: Request,
        @Param('agentId') agentId: string,
        @Param('accountId') accountId: string,
        @Res() response: Response,
    ) {
        try {
            const orgId = await this.validateToken(req);

            await this.agentService.removeKnowledgeSourceProcessingJobs(
                agentId,
                accountId,
            );
            return response.status(HttpStatus.OK).json({ message: 'Knowledge source removed successfully.' });
        } catch (error) {
            this.logger.error({
                message: `Error in removing knowledge source processing jobs : ` + error.message,
                context: 'PUBLIC API REMOVE KNOWLEDGE SOURCE PROCESSING JOBS',
            })
            handleException(response, error);
        }
    }

    /*
    Create a duplicate agent from an existing agent.
    */
    @Post('/create/extract/:id')
    async createDuplicateAgent(
        @Req() req: Request,
        @Param('id') id: string,
        @Body() createAgentDto: ExistingAgentDataDto,
        @Res() response: Response,
    ) {
        try {
            const orgId = await this.validateToken(req);

            const createdAgent = await this.agentService.createDuplicateAgent(
                '',
                id,
                { ...createAgentDto, orgId },
                response,
            );
        } catch (error) {
            this.logger.error({
                message: `Error in creating duplicate agent : ` + error.message,
                context: 'PUBLIC API CREATE DUPLICATE AGENT',
            })
            handleException(response, error);
        }
    }


    //disable an agent
    @Post('/status/:id')
    async toggleAgent(
        @Req() req: Request,
        @Param() param: DeleteAgentDto,
        @Body() toggleDto: ToggleStatusAgentDto,
        @Res() response: Response,
    ) {
        try {
            const orgId = await this.validateToken(req);

            const agent = await this.agentService.toggleAgent(
                param.id,
                toggleDto.disabled,
                response,
            );
        } catch (error) {
            this.logger.error({
                message: `Error in toggling agent : ` + error.message,
                context: 'PUBLIC API TOGGLE AGENT',
            })
            handleException(response, error);
        }
    }

    /*
    Upload a knowledge source. Can be of type - files, URLs, FAQs or text content.
    */
    @Post(':agentId/knowledge-source')
    @UseInterceptors(FilesInterceptor('files'))
    async uploadKnowledgeSource(@Req() req: Request,
        @Param('agentId') agentId: string,
        @Res() response: Response,
        @Body() body: IPublicApiAddKnowledgeDto,
        @UploadedFiles() files?: Express.Multer.File[] | undefined
    ) {
        try {
            this.logger.log({
                message: `Uploading knowledge source | ${JSON.stringify(body)}`,
                context: 'PUBLIC API UPLOAD KNOWLEDGE SOURCE',
            });

            const orgId = await this.validateToken(req);

            let r = {};
            if (files?.length > 0) {
                if (!files) throw new BadRequestException('No files found');

                r['files'] = await this.agentService.processFiles(agentId, files);
            } else {
                this.logger.error({
                    message: `No files found`,
                    context: 'PUBLIC API UPLOAD KNOWLEDGE SOURCE',
                })
            }

            if (body?.urls) {
                if (!body.urls) throw new BadRequestException('No urls found');
                const urls = JSON.parse(body.urls);
                r['urls'] = await this.agentService.scrapeUrls(agentId, urls);
            }
            if (body?.faqs) {
                if (!body.faqs) throw new BadRequestException('No faqs found');

                const { faqs } = body;
                const faqsWithIds: FAQ[] = JSON.parse(faqs).map((faq) => ({
                    id: uuidv4(),
                    question: faq.question,
                    answer: faq.answer,
                }));
                const agentData: AgentDocument = await this.mongoAgentService.getAgent({ _id: agentId });
                if (!agentData) throw new BadRequestException('Agent not found');

                const updatedFaqs = [...agentData.faqs, ...faqsWithIds];
                r['faqs'] = await this.iframeHelperService.handleNewFaq(agentId, updatedFaqs);
            }
            if (body?.textContent) {
                if (!body.textContent) throw new BadRequestException('No text content found');

                r['textContent'] = await this.agentService.addRawTextKnowledgeSource(
                    agentId,
                    body.textContent,
                );
            }
            // return this.fileUploadService.processFiles(agentId, files);

            return response.status(HttpStatus.OK).json({ response: r });
        } catch (error) {
            this.logger.error({
                message: `Error in uploading knowledge source : ` + error.message,
                context: 'PUBLIC API UPLOAD KNOWLEDGE SOURCE',
            })
            handleException(response, error);
        }
    }

    /*
    Add a trigger to the agent.
    */
    @Post('/add/trigger')
    async addTrigger(
        @Req() req: Request,
        @Body() body: ConnectGhlLocationDto,
        @Res() response: Response,
    ) {
        try {
            const orgId = await this.validateToken(req);

            const channel = await this.publicApiService.checkLocationExistsAsChannel(orgId, body);

            const a = await this.agentService.addTriggers({
                active: true,
                agentId: body.agentId,
                data: {
                    channel: body.messageType,
                    task: 'respond',
                    tagConditions: [],
                    subaccount: channel?.accountId
                },
                providerName: 'ghl',
                triggerName: 'Default by Public API',
            })

            return response.status(HttpStatus.OK).json(a);
        } catch (error) {
            this.logger.error({
                message: `Error in toggling agent : ` + error.message,
                context: 'PUBLIC API TOGGLE AGENT',
            })
            handleException(response, error);
        }
    }

    /*
    Assign a GHL location to the agent.
    */
    @Post('/assign/location')
    async assignLocation(
        @Req() req: Request,
        @Body() body: AssignAgentToLocation,
        @Res() response: Response,
    ) {
        try {
            const orgId = await this.validateToken(req);

            const { channels } = await this.channelService.getAllChannels(orgId);
            const channel = channels.find(channel => channel.keyId === body.locationId);
            const agentIds = body.agentIds ?? [];

            if (!channel) throw new BadRequestException('Channel not found');

            const result = await this.channelService.mapChannelToAgents(
                orgId,
                channel?.accountId,
                agentIds,
            );

            return response.status(HttpStatus.OK).json(result);
        } catch (error) {
            this.logger.error({
                message: `Error in assigning location to an agent : ` + error.message,
                context: 'PUBLIC API ASSIGN LOCATION',
            })
            handleException(response, error);
        }
    }

    /*
    Update an agent. Can be used to update the FAQ's agent name, prompt id and whether the agent is active or not.
    */
    @Post('update/:agentId')
    async updateAgent(
        @Req() req: Request,
        @Param('agentId') agentId: string,
        @Body() updateAgentDto: Partial<UpdateFaqDto & UpdateAgentNameDto & EditActivePromptDto & ToggleStatusAgentDto>,
        @Res() response: Response,
    ) {
        try {
            const orgId = await this.validateToken(req);

            if ((updateAgentDto?.faqs ?? []).length > 0) {
                const faqsWithIds: FAQ[] = updateAgentDto.faqs.map((faq) => ({
                    id: uuidv4(),
                    question: faq.question,
                    answer: faq.answer,
                }));
                await this.agentService.handleNewFaq(agentId, faqsWithIds);
            }

            if (updateAgentDto.agentName) {
                await this.agentService.updateAgentName(
                    agentId,
                    updateAgentDto.agentName,
                );
            }

            if (updateAgentDto.promptId) {
                await this.agentService.editActivePrompt(
                    {
                        agentId,
                        promptId: updateAgentDto.promptId,
                    }
                );
            }

            if (updateAgentDto.disabled !== undefined) {
                await this.agentService.toggleAgent(
                    agentId,
                    updateAgentDto.disabled,
                );
            }

            response.status(HttpStatus.OK).json({ message: 'Agent updated successfully' });
        } catch (error) {
            this.logger.error({
                message: error instanceof Error ? error.message : 'Failed to update agent',
                context: 'PUBLIC API UPDATE AGENT',
            });
            handleException(response, error);
        }
    }

    /*
    List folders.
    */
    @Get('/list/folders')
    async listFolders(
        @Req() req: Request, @Res() response: Response
    ) {
        try {
            const orgId = await this.validateToken(req);

            this.logger.log({
                message: `Request for listing folders | orgId ${orgId}`,
                context: 'PUBLIC API LIST FOLDERS',
            })
            const f = await this.foldersService.findAll({
                orgId,
                type: FolderType.Agents,
            });
            const foldersSchema = (f?.folders ?? []).map((folder) => {
                return {
                    name: folder.name,
                    id: folder?._id?.toString() ?? "",
                };
            });

            return response.status(HttpStatus.OK).json({ folders: foldersSchema });
        } catch (error) {
            this.logger.error({
                message: `Error in listing folders : ` + error.message,
                context: 'PUBLIC API LIST FOLDERS',
            });
            handleException(response, error);
        }
    }

    //get an agent
    @Get('/:id')
    async getAgent(@Req() req: Request, @Param() param: GetAgentDto, @Res() response: Response) {
        try {
            const orgId = await this.validateToken(req);

            const agent = await this.agentService.getAgent(param.id, response);
        } catch (error) {
            this.logger.error({
                message: `Error in getting agent : ` + error.message,
                context: 'PUBLIC API GET AGENT',
            })
            handleException(response, error);
        }
    }

    /*
    Delete an agent.
    */
    @Delete('/:id')
    async deleteAgent(@Req() req: Request, @Param() param: DeleteAgentDto, @Res() response: Response) {
        try {
            const orgId = await this.validateToken(req);

            const agent = await this.agentService.deleteAgent(param.id, response);
        } catch (error) {
            this.logger.error({
                message: `Error in deleting agent : ` + error.message,
                context: 'PUBLIC API DELETE AGENT',
            })
            handleException(response, error);
        }
    }
}
