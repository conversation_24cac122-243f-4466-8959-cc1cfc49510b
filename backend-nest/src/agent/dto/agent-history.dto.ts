// top view
// POST  /agent/history/all/:agentId

interface GetAllHistoryQueryParams {
  page: number;
  limit: number;
  messageType?: string;
  searchContactId?: string;
}

interface GetAllHistoryPathParams {
    agentId: string;
}

interface GetAllHistoryResponse {
    totalPage: number;
    currentPage: number;
    history: {
        channel: string;
        lastMessage: string;
        lastMessageTime: Date;
        totalTokens: number;
        numberOfMessages: number;
    }[];
};

// message level
// POST /agent/history/:contactId

interface GetDetailedHistoryPathParams {
    contactId: string;
}

interface GetDetailedHistoryQueryParams {
    page: number;
    limit: number;
}

interface GetDetailedHistoryResponse {
    totalPage: number;
    currentPage: number;
    history: {
        aiProvider: string;
        message: string;
        time: Date;
        tokens: number;
    }[]
};