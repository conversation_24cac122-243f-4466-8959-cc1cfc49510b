import {
  IsString,
  IsNotEmpty,
  IsIn,
  IsOptional,
  IsObject,
  IsBoolean,
  ValidateNested,
  IsNumber,
  IsEnum,
  IsArray,
  ValidateIf,
  <PERSON>,
  <PERSON>,
} from 'class-validator';
import { Type } from 'class-transformer';

class JsonObjects {
  @IsNotEmpty()
  @IsString()
  name: string;

  @IsNotEmpty()
  @IsIn(['all', 'any'])
  generateCondition: 'all' | 'any';

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => JsonObjectProperty)
  properties: JsonObjectProperty[];
}

class JsonObjectProperty {
  @IsString()
  @IsNotEmpty()
  id: string;

  @IsString()
  @IsNotEmpty()
  name: string;

  @IsString()
  @IsNotEmpty()
  description: string;

  @IsEnum(['string', 'number', 'boolean', 'date'])
  type: 'string' | 'number' | 'boolean' | 'date';
}

class AIProvider {
  @IsString()
  @IsNotEmpty()
  companyId: string;

  @IsString()
  @IsNotEmpty()
  modelName: string;

  @IsString()
  // @IsNotEmpty()
  accountName: string;

  @IsString()
  // @IsNotEmpty()
  accountId: string;
}

class AdvancedSettings {
  @IsNumber()
  @IsNotEmpty()
  maxTokensAllowed: number;

  @ValidateNested()
  @Type(() => AIProvider)
  aiProvider: AIProvider;
}

class TagData {
  @IsOptional()
  @IsString()
  @IsIn(['has tag', 'does not have tag'])
  tagType: 'has tag' | 'does not have tag';

  @IsNotEmpty()
  @IsString()
  tagValue: string;

  @IsNotEmpty()
  @IsString()
  @IsIn(['everyTurn', 'contactNoTag'])
  evaluateOn: 'everyTurn' | 'contactNoTag';

  @IsNotEmpty()
  @IsBoolean()
  reply: boolean;
}

class CustomFieldData {
  @IsOptional()
  @IsString()
  fieldKey: string;

  @IsOptional()
  @IsString()
  @IsIn(['String', 'number', 'list'])
  type: 'String' | 'number' | 'list';

  @IsOptional()
  @IsString()
  @IsIn(['everyTurn', 'isEmpty'])
  evaluateOn: 'everyTurn' | 'isEmpty';

  @IsOptional()
  @IsBoolean()
  reply?: boolean;
}

class GhlCalendarMetaData {
  @IsNotEmpty()
  @IsString()
  @IsIn(['everyTurn', 'isEmpty'])
  evaluateOn: 'everyTurn' | 'isEmpty';

  @IsOptional()
  @IsNumber()
  dayRange: number;

  @IsOptional()
  @IsNumber()
  maxRange: number;

  @IsOptional()
  @IsBoolean()
  cancelEvent: boolean;

  @IsOptional()
  @IsBoolean()
  rescheduleEvent: boolean;
}

class GoogleCalendarMetaData {
  @IsOptional()
  @IsBoolean()
  cancelEvent: boolean;

  @IsOptional()
  @IsBoolean()
  rescheduleEvent: boolean;
}

class StandardFieldData {
  @IsNotEmpty()
  @IsArray()
  fieldKeys: string[];

  @IsNotEmpty()
  @IsBoolean()
  reply: boolean;

  @IsNotEmpty()
  @IsString()
  @IsIn(['isEmpty'])
  evaluateOn: 'isEmpty';
}

class MetaData {
  @IsOptional()
  @ValidateNested()
  @Type(() => TagData)
  tagData?: TagData;

  @IsOptional()
  @ValidateNested()
  @Type(() => CustomFieldData)
  customFieldData?: CustomFieldData;

  @IsOptional()
  @Type(() => GhlCalendarMetaData)
  ghlCalendarMetaData?: GhlCalendarMetaData;

  @IsOptional()
  @Type(() => GoogleCalendarMetaData)
  googleCalendarMetaData?: GoogleCalendarMetaData;

  @IsOptional()
  @Type(() => StandardFieldData)
  standardFieldData?: StandardFieldData;
}

class ApiKey {
  @IsString()
  @IsNotEmpty()
  key: string;

  @IsString()
  @IsNotEmpty()
  value: string;

  @IsEnum(['Header', 'Query'])
  addTo: 'Header' | 'Query';
}

class HttpGetParameter {
  @IsOptional()
  @IsString()
  name: string;

  @IsOptional()
  @IsString()
  value: string;

  @IsOptional()
  @IsString()
  description: string;
}

class HttpGet {
  @IsOptional()
  @IsString()
  @ValidateIf((o) => o.name !== undefined && o.name !== null && o.name !== '')
  @IsNotEmpty()
  name: string;

  @IsOptional()
  @IsString()
  @ValidateIf((o) => o.url !== undefined && o.url !== null && o.url !== '')
  @IsNotEmpty()
  url: string;

  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => HttpGetParameter)
  queryParameters: HttpGetParameter[];

  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => HttpGetParameter)
  pathVariables: HttpGetParameter[];

  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => HttpGetParameter)
  headers: HttpGetParameter[];

  @IsOptional()
  @IsEnum(['No Auth', 'API Key', 'OAuth 2.0'])
  authorizationType: 'No Auth' | 'API Key' | 'OAuth 2.0';

  @IsOptional()
  @ValidateNested()
  @Type(() => ApiKey)
  apiKey?: ApiKey;

  @IsOptional()
  @IsString()
  responseBuilder: string;
}

class HttpRequestParameter {
  @IsOptional()
  @IsString()
  @ValidateIf((o) => o.name !== undefined && o.name !== null && o.name !== '')
  @IsNotEmpty()
  name: string;

  @IsOptional()
  @IsString()
  @ValidateIf((o) => o.value !== undefined && o.value !== null && o.value !== '')
  @IsNotEmpty()
  value: string;

  @IsOptional()
  @IsString()
  @ValidateIf((o) => o.description !== undefined && o.description !== null && o.description !== '')
  @IsNotEmpty()
  description: string;
}

class HttpRequest {
  @IsOptional()
  @IsString()
  @ValidateIf((o) => o.name !== undefined && o.name !== null && o.name !== '')
  @IsNotEmpty()
  name: string;

  @IsOptional()
  @IsString()
  @ValidateIf((o) => o.url !== undefined && o.url !== null && o.url !== '')
  @IsNotEmpty()
  url: string;

  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => HttpRequestParameter)
  queryParameters: HttpRequestParameter[];

  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => HttpRequestParameter)
  pathVariables: HttpRequestParameter[];

  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => HttpRequestParameter)
  headers: HttpRequestParameter[];

  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => HttpRequestParameter)
  bodyParameters: HttpRequestParameter[];

  @IsOptional()
  @IsEnum(['application/json', 'application/x-www-form-urlencoded'])
  bodyType: 'application/json' | 'application/x-www-form-urlencoded';

  @IsOptional()
  @IsEnum(['No Auth', 'API Key', 'OAuth 2.0'])
  authorizationType: 'No Auth' | 'API Key' | 'OAuth 2.0';

  @IsOptional()
  @ValidateNested()
  @Type(() => ApiKey)
  apiKey?: ApiKey;

  @IsOptional()
  @IsString()
  @IsNotEmpty()
  responseBuilder: string;
}

class IncludeMainPromptDetails {
  @IsNotEmpty()
  @IsBoolean()
  includeMainPrompt: Boolean;

  @IsNotEmpty()
  @IsString()
  mainPromptId: string;
}

class SlackDetails {
  @IsString()
  @IsNotEmpty()
  type: string;

  @IsString()
  @IsOptional()
  text?: string;

  @IsString()
  @IsNotEmpty()
  channel_id: string;
}

class Action {
  @IsOptional()
  @IsString()
  accountId: string;

  @IsOptional()
  @IsString()
  accountName: string;

  @IsOptional()
  @IsString()
  providerName: string;

  @IsOptional()
  @IsString()
  @ValidateIf(o => o.activity !== 'standardField')
  @IsNotEmpty({ message: 'promptContent is required except for standardField activity' })
  promptContent: string;

  @IsOptional()
  @IsEnum(['read', 'write', 'tag', 'customField', 'events', 'standardField'])
  activity: 'read' | 'write' | 'tag' | 'customField' | 'events' | 'standardField';

  @IsBoolean()
  isAdvancedSettings: boolean;

  @IsBoolean()
  @IsOptional()
  silent?: boolean;

  @IsOptional()
  @IsEnum(['DEFAULT', 'ALWAYS', 'CONFIDENCE'])
  evalType?: 'DEFAULT' | 'ALWAYS' | 'CONFIDENCE';

  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(100)
  confidenceValue?: number;

  @IsOptional()
  @IsBoolean()
  augmentedQueryData?: boolean;

  @IsOptional()
  @ValidateNested()
  @Type(() => MetaData)
  metaData?: MetaData;

  @IsOptional()
  @ValidateNested()
  @Type(() => JsonObjects)
  jsonObjects?: JsonObjects;

  @IsOptional()
  @ValidateNested()
  @Type(() => AdvancedSettings)
  advancedSettings?: AdvancedSettings;

  @IsOptional()
  @ValidateNested()
  @Type(() => HttpGet)
  httpGetRequestDetails?: HttpGet;

  @IsOptional()
  @ValidateNested()
  @Type(() => HttpRequest)
  httpRequestDetails?: HttpRequest;

  @IsOptional()
  @IsObject()
  @Type(() => IncludeMainPromptDetails)
  includeMainPromptDetails?: IncludeMainPromptDetails;

  @IsOptional()
  @IsObject()
  slackDetails?: SlackDetails;

  @IsOptional()
  @IsObject()
  sms?: {
    type: string;
    text?: string;
    phoneId?: string;
  };

  @IsOptional()
  @IsObject()
  ghlEmail?: {
    type: string;
    text?: string;
    subject?: string;
  };
}

export class EditDatasourceDto {
  @IsNotEmpty()
  @IsString()
  agentId: string;

  @IsOptional()
  @IsString()
  actionId: string;

  @IsNotEmpty()
  @ValidateNested()
  @Type(() => Action)
  action: Action;

  @IsOptional()
  isTool: boolean;
}

export class GetDatasourceDto {
  @IsNotEmpty()
  @IsString()
  agentId: string;

  @IsOptional()
  @IsNotEmpty()
  @IsString()
  actionId: string;

  @IsOptional()
  @IsNotEmpty()
  @IsString()
  accountId: string;
}
