import { MessagingTypes } from "src/lib/constant";

export interface IPublicApiAddKnowledgeDto {
    faqs?: string; // JSONified - { question: string; answer: string }[];
    urls?: string; // JSONified - string[];
    textContent?: string;
}

export interface ConnectGhlLocationDto {
    agentId: string,
    locationId: string,
    messageType: MessagingTypes
}

export interface AssignAgentToLocation {
    agentIds: string[],
    locationId: string,
}