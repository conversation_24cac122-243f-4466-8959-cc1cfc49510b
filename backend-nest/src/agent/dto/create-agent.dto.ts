import { Type } from 'class-transformer';
import {
  IsString,
  IsNotEmpty,
  IsUrl,
  ValidateNested,
  IsOptional,
  IsObject,
  IsBoolean,
  IsArray,
} from 'class-validator';
import { FAQDto } from 'src/mongo/dto/agent/create.dto';

export class CalendarDetailsDTO {
  @IsString()
  @IsOptional()
  credentialId: string;

  @IsString()
  @IsNotEmpty()
  author: string;

  @IsString()
  @IsNotEmpty()
  providerName: string;

  @IsString()
  @IsNotEmpty()
  accountId: string;

  @IsString()
  @IsNotEmpty()
  name: string;

  @IsString()
  @IsOptional()
  message?: string;

  @IsString()
  @IsOptional()
  userName?: string;

  @IsString()
  @IsOptional()
  userId?: string;

  @IsString()
  @IsOptional()
  orgId?: string;

  @IsString()
  @IsOptional()
  keyId?: string;

  @IsString()
  @IsOptional()
  calendarId?: string;

  @IsString()
  @IsOptional()
  timezone?: string;

  @IsString()
  @IsOptional()
  usid?: string;
}

export class CreateAgentDto {
  // @IsString()
  readonly agentName: string;

  @IsNotEmpty()
  readonly aiProvider: Object;

  @IsOptional()
  @IsString()
  readonly folder?: string;

  @IsOptional()
  readonly prompts?: Object;

  @IsNotEmpty()
  readonly actions: Object[];

  @IsString()
  readonly orgId: string;

  @IsBoolean()
  readonly disabled: boolean;

  @IsOptional()
  @IsUrl()
  readonly website?: string;

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => FAQDto)
  readonly faqs?: FAQDto[];

  @ValidateNested()
  @Type(() => CalendarDetailsDTO)
  @IsOptional()
  calendarDetails?: CalendarDetailsDTO;

  // @IsString()
  // @IsOptional()
  // readonly discriminatorTrigger: string;
}

export class PublicApiCreateAgentDto {
  readonly agentName: string;

  @IsOptional()
  @IsString()
  readonly folder?: string;

  @IsOptional()
  readonly prompts?: {
    name: string;
    promptContent: string;
    customFallbackPrompt?: string;
    isFallbackPrompt?: boolean;
  };

  @IsOptional()
  @IsUrl()
  readonly website?: string;
}