export interface IQuickActionProp {
    orgId: string;
    model_type?: string;
    // aiProvider: {
    //     accountId?: string,
    //     secretKey?: string,
    //     keyName?: string,
    // },
    niche: TNiche,
    companyInfo: {
        businessName: string,
        agentName: string,
        companyLocation: string,
        website: string,
        scapeWebsite: boolean;
    },
    additionalInfo?: {
        areaServed?: string,
        areaOfPractice?: string,
        primaryService?: string,
    },
    calendarDetails: {
        credentialId: string,
        author: string,
        providerName: string,
        accountId: string,
        name: string,
        message?: string,
        userName?: string,
        userId?: string,
        orgId?: string,
        keyId?: string,
        calendarId?: string,
        timezone?: string,
        usid?: string,

    },
    faq: { question: string; answer: string }[];
}

export type TNiche = 'Real estate' | 'Dental' | 'Marketing agency' | 'Custom';