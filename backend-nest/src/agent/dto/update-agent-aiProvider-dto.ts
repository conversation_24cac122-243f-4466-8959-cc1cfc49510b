import { IsString, IsNotEmpty, ValidateNested, IsN<PERSON>ber, IsBoolean, IsOptional } from 'class-validator';

class AIProvider {
    @IsString()
    @IsNotEmpty()
    companyId: string;

    @IsString()
    @IsNotEmpty()
    modelName: string;
  
    @IsString()
    @IsNotEmpty()
    accountName: string;
  
    @IsString()
    @IsNotEmpty()
    accountId: string;
  }

  class FailSafeDetails {

    @IsBoolean()
    failsafe: boolean;

    @IsString()
    companyId: string;
  
    @IsString()
    modelName: string;
  
    @IsString()
    accountName: string;
  
    @IsString()
    accountId: string;
  }

  class AdvancedSettings {
    @IsNumber()
    @IsNotEmpty()
    temperature: Number;

    @IsNumber()
    @IsNotEmpty()
    maxLength: Number;
  
    @IsNumber()
    @IsNotEmpty()
    frequencyPenalty: Number;

    @IsString()
    @IsNotEmpty()
    optimize: string;
  }
  
  export class UpdateAiProviderDto {
    @IsString()
    @IsNotEmpty()
    agentId: string;
  
    @ValidateNested()
    aiProvider: AIProvider;

    @IsOptional()
    @IsBoolean()
    isAdvancedSettings?: Boolean;

    @IsOptional()
    @ValidateNested()
    advancedSettings?: AdvancedSettings;

    @IsOptional()
    @ValidateNested()
    failsafeAiProvider?: FailSafeDetails;
  }