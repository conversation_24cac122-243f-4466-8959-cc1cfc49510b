import { Type } from 'class-transformer';
import {
  IsArray,
  IsBoolean,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUrl,
  ValidateNested,
} from 'class-validator';
import { CalendarDetailsDTO } from './create-agent.dto';

export class FAQDTO {
  @IsString()
  @IsNotEmpty()
  question: string;

  @IsString()
  @IsNotEmpty()
  answer: string;
}

export class AccountDTO {
  @IsString()
  @IsNotEmpty()
  accountId: string;

  @IsString()
  @IsNotEmpty()
  name: string;

  @IsString()
  @IsNotEmpty()
  providerName: string;
}

export class ChannelDTO {
  @ValidateNested()
  @Type(() => AccountDTO)
  @IsOptional()
  account?: AccountDTO;

  @IsBoolean()
  @IsOptional()
  isCreateFirstTrigger?: boolean;

  @IsBoolean()
  @IsOptional()
  isImportVariables?: boolean;
}

export class CreateAgentConfigDTO {
  @IsString()
  @IsNotEmpty()
  orgId: string;

  @IsString()
  @IsOptional()
  folder: string;

  @IsString()
  @IsNotEmpty()
  name: string;

  @IsString()
  @IsOptional()
  description?: string;

  @IsOptional()
  @IsUrl()
  website?: string;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => FAQDTO)
  faqs: FAQDTO[];

  @IsOptional()
  @ValidateNested({ each: true })
  channel: ChannelDTO;

  @ValidateNested()
  @Type(() => CalendarDetailsDTO)
  @IsOptional()
  calendarDetails?: CalendarDetailsDTO;
}

export class AgentConfigResponseDTO {
  @IsString()
  orgId: string;

  @IsString()
  name: string;

  @IsString()
  @IsOptional()
  description?: string;

  @IsUrl()
  website: string;

  @ValidateNested({ each: true })
  @Type(() => FAQDTO)
  faqs: FAQDTO[];

  @IsString()
  id: string;

  @IsString()
  createdAt: string;

  @IsString()
  updatedAt: string;
}

export class UpdateAgentConfigDTO {
  @IsString()
  @IsOptional()
  orgId?: string;

  @IsString()
  @IsOptional()
  name?: string;

  @IsString()
  @IsOptional()
  description?: string;

  @IsUrl()
  @IsOptional()
  website?: string;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => FAQDTO)
  @IsOptional()
  faqs?: FAQDTO[];
}