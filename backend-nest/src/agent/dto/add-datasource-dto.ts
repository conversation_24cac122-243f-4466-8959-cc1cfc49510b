import {
  IsString,
  IsNotEmpty,
  IsIn,
  IsOptional,
  IsObject,
  IsBoolean,
  ValidateNested,
  IsNumber,
  IsEnum,
  IsArray,
  ValidateIf,
  <PERSON>,
  <PERSON>,
} from 'class-validator';
import { Type } from 'class-transformer';

class JsonObjects {
  @IsNotEmpty()
  @IsString()
  name: string;

  @IsNotEmpty()
  @IsIn(['all', 'any'])
  generateCondition: 'all' | 'any';

  @IsNotEmpty()
  @IsObject()
  properties: {
    name: string;
    description: string;
    type: 'string' | 'number' | 'boolean' | 'date';
  }[];
}

class AIProvider {
  @IsString()
  @IsNotEmpty()
  companyId: string;

  @IsString()
  @IsNotEmpty()
  modelName: string;

  @IsString()
  @IsNotEmpty()
  accountName: string;

  @IsString()
  @IsNotEmpty()
  accountId: string;
}

class AdvancedSettings {
  @IsNumber()
  @IsNotEmpty()
  maxTokensAllowed: number;

  @ValidateNested()
  aiProvider: AIProvider;
}

class TagData {
  @IsOptional()
  @IsString()
  @IsIn(['has tag', 'does not have tag'])
  tagType: 'has tag' | 'does not have tag';

  @IsNotEmpty()
  @IsString()
  tagValue: string;

  @IsNotEmpty()
  @IsString()
  @IsIn(['everyTurn', 'contactNoTag'])
  evaluateOn: 'everyTurn' | 'contactNoTag';

  @IsNotEmpty()
  reply: boolean;
}

class CustomFieldData {
  @IsNotEmpty()
  @IsString()
  fieldKey: string;

  @IsNotEmpty()
  @IsOptional()
  reply: Boolean;

  @IsNotEmpty()
  @IsString()
  @IsIn(['everyTurn', 'isEmpty'])
  evaluateOn: 'everyTurn' | 'isEmpty';
}

class GhlCalendarMetaData {
  @IsNotEmpty()
  @IsString()
  @IsIn(["everyTurn", "isEmpty"])
  evaluateOn: "everyTurn" | "isEmpty";

  @IsOptional()
  @IsNumber()
  dayRange: number;

  @IsOptional()
  @IsNumber()
  maxRange: number;
}

class GoogleCalendarMetaData {
  @IsOptional()
  @IsBoolean()
  cancelEvent: boolean;

  @IsOptional()
  @IsBoolean()
  rescheduleEvent: boolean;
}

class StandardFieldData {
  @IsNotEmpty()
  @IsArray()
  fieldKeys: string[];

  @IsNotEmpty()
  @IsBoolean()
  reply: boolean;

  @IsNotEmpty()
  @IsString()
  @IsIn(['isEmpty'])
  evaluateOn: 'isEmpty';
}

class MetaData {
  @IsOptional()
  @Type(() => TagData)
  tagData?: TagData;

  @IsOptional()
  @Type(() => CustomFieldData)
  customFieldData?: CustomFieldData;

  @IsOptional()
  @Type(() => GhlCalendarMetaData)
  ghlCalendarMetaData?: GhlCalendarMetaData;

  @IsOptional()
  @Type(() => GoogleCalendarMetaData)
  googleCalendarMetaData?: GoogleCalendarMetaData;

  @IsOptional()
  @Type(() => StandardFieldData)
  standardFieldData?: StandardFieldData;
}

class ApiKey {
  @IsOptional()
  @IsString()
  @ValidateIf((o) => o.key !== undefined && o.key !== null && o.key !== '')
  @IsNotEmpty()
  key: string;

  @IsOptional()
  @IsString()
  @ValidateIf((o) => o.value !== undefined && o.value !== null && o.value !== '')
  @IsNotEmpty()
  value: string;

  @IsOptional()
  @IsEnum(['Header', 'Query'])
  addTo: 'Header' | 'Query';
}

class HttpGetParameter {
  @IsOptional()
  @IsString()
  @ValidateIf((o) => o.name !== undefined && o.name !== null && o.name !== '')
  @IsNotEmpty()
  name: string;

  @IsOptional()
  @IsString()
  @ValidateIf((o) => o.value !== undefined && o.value !== null && o.value !== '')
  @IsNotEmpty()
  value: string;

  @IsOptional()
  @IsString()
  @ValidateIf((o) => o.description !== undefined && o.description !== null && o.description !== '')
  @IsNotEmpty()
  description: string;
}

class HttpGet {
  @IsOptional()
  @IsString()
  @ValidateIf((o) => o.name !== undefined && o.name !== null && o.name !== '')
  @IsNotEmpty()
  name: string;

  @IsOptional()
  @IsString()
  @ValidateIf((o) => o.url !== undefined && o.url !== null && o.url !== '')
  @IsNotEmpty()
  url: string;

  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => HttpGetParameter)
  queryParameters: HttpGetParameter[];

  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => HttpGetParameter)
  pathVariables: HttpGetParameter[];

  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => HttpGetParameter)
  headers: HttpGetParameter[];

  @IsOptional()
  @IsEnum(['No Auth', 'API Key', 'OAuth 2.0'])
  authorizationType: 'No Auth' | 'API Key' | 'OAuth 2.0';

  @IsOptional()
  @ValidateNested()
  @Type(() => ApiKey)
  apiKey?: ApiKey;

  @IsOptional()
  @IsString()
  @ValidateIf((o) => o.responseBuilder !== undefined && o.responseBuilder !== null && o.responseBuilder !== '')
  @IsNotEmpty()
  responseBuilder: string;
}

class HttpRequestParameter {
  @IsOptional()
  @IsString()
  @ValidateIf((o) => o.name !== undefined && o.name !== null && o.name !== '')
  @IsNotEmpty()
  name: string;

  @IsOptional()
  @IsString()
  @ValidateIf((o) => o.value !== undefined && o.value !== null && o.value !== '')
  @IsNotEmpty()
  value: string;

  @IsOptional()
  @IsString()
  @ValidateIf((o) => o.description !== undefined && o.description !== null && o.description !== '')
  @IsNotEmpty()
  description: string;
}

class HttpRequest {
  @IsOptional()
  @IsString()
  @ValidateIf((o) => o.name !== undefined && o.name !== null && o.name !== '')
  @IsNotEmpty()
  name: string;

  @IsOptional()
  @IsString()
  @ValidateIf((o) => o.url !== undefined && o.url !== null && o.url !== '')
  @IsNotEmpty()
  url: string;

  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => HttpRequestParameter)
  queryParameters: HttpRequestParameter[];

  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => HttpRequestParameter)
  pathVariables: HttpRequestParameter[];

  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => HttpRequestParameter)
  headers: HttpRequestParameter[];

  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => HttpRequestParameter)
  bodyParameters: HttpRequestParameter[];

  @IsOptional()
  @IsEnum(['application/json', 'application/x-www-form-urlencoded'])
  bodyType: 'application/json' | 'application/x-www-form-urlencoded';

  @IsOptional()
  @IsEnum(['No Auth', 'API Key', 'OAuth 2.0'])
  authorizationType: 'No Auth' | 'API Key' | 'OAuth 2.0';

  @IsOptional()
  @ValidateNested()
  @Type(() => ApiKey)
  apiKey?: ApiKey;

  @IsOptional()
  @IsString()
  @IsNotEmpty()
  responseBuilder: string;
}

class IncludeMainPromptDetails {
  @IsNotEmpty()
  @IsBoolean()
  includeMainPrompt: Boolean;

  @IsNotEmpty()
  @IsString()
  mainPromptId: string;
}

class Action {
  @IsString()
  @IsOptional()
  actionId?: string;

  @IsBoolean()
  @IsOptional()
  deleted: boolean;

  @IsString()
  @IsOptional()
  accountId: string;

  @IsString()
  @IsNotEmpty()
  accountName: string;

  @IsString()
  @IsNotEmpty()
  providerName: string;

  @IsOptional()
  @IsString()
  @ValidateIf(o => o.activity !== 'standardField')
  @IsNotEmpty({ message: 'promptContent is required except for standardField activity' })
  promptContent: string;

  @IsOptional()
  @IsEnum(['read', 'write', 'tag', 'customField', 'events', 'standardField'])
  activity: 'read' | 'write' | 'tag' | 'customField' | 'events' | 'standardField';

  @IsBoolean()
  isAdvancedSettings: boolean;

  @IsBoolean()
  @IsOptional()
  silent?: boolean;

  @IsOptional()
  @IsEnum(['DEFAULT', 'ALWAYS', 'CONFIDENCE'])
  evalType?: 'DEFAULT' | 'ALWAYS' | 'CONFIDENCE';

  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(100)
  confidenceValue?: number;

  @IsOptional()
  @IsBoolean()
  augmentedQueryData?: boolean;

  @IsOptional()
  @IsObject()
  @Type(() => MetaData)
  metaData?: MetaData;

  @IsOptional()
  @IsObject()
  @Type(() => JsonObjects)
  jsonObjects?: JsonObjects;

  @IsOptional()
  @IsObject()
  @Type(() => AdvancedSettings)
  advancedSettings?: AdvancedSettings;

  @IsOptional()
  @ValidateNested()
  @Type(() => HttpRequest)
  httpRequestDetails?: HttpRequest;

  @IsOptional()
  @ValidateNested()
  @Type(() => HttpGet)
  httpGetRequestDetails?: HttpGet;

  @IsOptional()
  @IsObject()
  @Type(() => IncludeMainPromptDetails)
  includeMainPromptDetails?: IncludeMainPromptDetails;

  @IsOptional()
  @IsObject()
  slackDetails?: {
    type: string;
    text?: string;
    channel_id?: string;
  };

  @IsOptional()
  @IsObject()
  sms?: {
    type: string;
    text?: string;
    phoneId?: string;
  };

  @IsOptional()
  @IsObject()
  ghlEmail?: {
    type: string;
    text?: string;
    subject?: string;
  };
}

export class AddDatasourceDto {
  @IsNotEmpty()
  @IsString()
  agentId: string;

  @IsNotEmpty()
  @IsObject()
  action: Action;

  @IsOptional()
  isTool?: boolean;
}
