import { IsNotEmpty, IsString, IsBoolean, IsN<PERSON>ber, IsOptional } from 'class-validator';

export class FollowUpBodyDto {
    @IsOptional()
    @IsNumber()
    duration: number; // minutes

    @IsOptional()
    @IsNumber()
    maxAttempts: number;

    @IsOptional()
    @IsString()
    promptId: string;

    @IsOptional()
    @IsString()
    triggerId: string;

    @IsOptional()
    @IsString()
    conditionPrompt: string;


    @IsNotEmpty()
    @IsBoolean()
    isFollowupEnabled: boolean;
}