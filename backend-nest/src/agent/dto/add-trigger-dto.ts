import {
  IsString,
  IsNotEmpty,
  IsIn,
  IsObject,
  IsOptional,
  Validate,
  ValidatorConstraint,
  ValidatorConstraintInterface,
  ValidationArguments,
  IsBoolean,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';
import { Schedule } from 'src/lib/types';

export interface IGhlTrigger {
  subaccount: string;
  channel: string; // SMS | Email | GMB | IG | FB | Live Chat
  tagOption?: string; //tagType
  tagValue?: string;
  task?: string;
  sourceIdentifier?: string;
  prompt?: string;
  history?: number;
  include?: string;
  exclude?: string;
  include_knowledge?: string;
  exclude_knowledge?: string;
  tagConditions?: Array<{
    tagOption: 'hasTag' | 'doesntHaveTag';
    tagValue: string;
  }>;
}


// export interface IChatHqTrigger {
//   liveChatWidget: string;
//   // timeOfDay: number; //UNIX
// }

@ValidatorConstraint({ async: false })
export class IsValidGhlTrigger implements ValidatorConstraintInterface {
  validate(triggerData: any, args: ValidationArguments) {
    const parentObject: AddTriggerDto = args.object as AddTriggerDto;
    if (parentObject.providerName !== 'ghl') {
      return true; // Skip validation for other types
    }
    return (
      typeof triggerData.subaccount === 'string' &&
      typeof triggerData.channel === 'string'
    );
  }

  defaultMessage(args: ValidationArguments) {
    return 'Invalid GHL Trigger Data!';
  }
}

@ValidatorConstraint({ async: false })
export class IsValidChatHqTrigger implements ValidatorConstraintInterface {
  validate(triggerData: any, args: ValidationArguments) {
    const parentObject: AddTriggerDto = args.object as AddTriggerDto;
    if (parentObject.providerName !== 'chathq') {
      return true; // Skip validation for other types
    }
    return typeof triggerData.liveChatWidget === 'string';
  }

  defaultMessage(args: ValidationArguments) {
    return 'Invalid ChatHQ Trigger Data!';
  }
}

class BasicDto {
  @IsNotEmpty()
  @IsString()
  triggerName: string;

  @IsNotEmpty()
  @IsString()
  @IsIn(['ghl', 'chathq', 'podium', 'hubspot'])
  providerName: string;

  @IsNotEmpty()
  @IsObject()
  @Validate(IsValidGhlTrigger)
  @Validate(IsValidChatHqTrigger)
  @Type(() => Object)
  data: IGhlTrigger;

  @IsNotEmpty()
  @IsBoolean()
  active: Boolean;
}

class FollowUpConfigDto {
  duration?: number;
  maxAttempts?: number;
  promptId?: string;
  isFollowupEnabled: boolean;
  conditionPrompt?: string;
  timezone?: string;
  schedule?: Schedule;
  tagConditions?: Array<{
    tagOption: 'hasTag' | 'doesntHaveTag';
    tagValue: string;
  }>;
}

export class AddTriggerDto extends BasicDto {
  @IsNotEmpty()
  @IsString()
  agentId: string;

  @IsOptional()
  @Type(() => FollowUpConfigDto)
  followUp?: FollowUpConfigDto
}

export class EditTriggerDto {
  @IsNotEmpty()
  @IsString()
  triggerId: string;

  @IsNotEmpty()
  @IsString()
  agentId: string;

  @IsNotEmpty()
  @ValidateNested()
  @Type(() => BasicDto)
  data: BasicDto

  @IsOptional()
  @Type(() => FollowUpConfigDto)
  followUp?: FollowUpConfigDto
}