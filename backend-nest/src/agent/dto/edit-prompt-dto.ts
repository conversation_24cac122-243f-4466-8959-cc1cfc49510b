import { Is<PERSON><PERSON>, <PERSON><PERSON>otEmpty, Is<PERSON><PERSON>D, IsObject, IsBoolean, IsOptional } from 'class-validator';

class Prompt{
  @IsNotEmpty()
  @IsString()
  name: string;

  @IsNotEmpty()
  @IsString()
  promptContent: string;

  @IsOptional()
  @IsBoolean()
  isFallbackPrompt: boolean;

  @IsOptional()
  @IsString()
  customFallbackPrompt?: string;
}

export class EditPromptDto {
  @IsNotEmpty()
  @IsString()
  agentId: string;

  @IsNotEmpty()
  @IsUUID()
  promptId: string;

  @IsNotEmpty()
  @IsObject()
  prompt: Prompt;

}