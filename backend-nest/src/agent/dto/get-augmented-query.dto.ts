import { IsNotEmpty, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>y } from "class-validator";

export class getAugmentedQueryDto {
    @IsString()
    @IsNotEmpty()
    @MaxLength(50000, { message: "Message must not exceed 25000 characters" }) // max allowed is 25k characters
    message: string;

    @IsOptional()
    @IsArray()
    @IsString({ each: true })
    includedIds?: string[];

    @IsOptional()
    @IsArray()
    @IsString({ each: true })
    excludedIds?: string[];
}