import { IsNotEmpty, IsString, IsOptional, IsObject, IsBoolean } from 'class-validator';

class Prompt{
  @IsOptional()
  @IsString()
  promptId?: string;

  @IsNotEmpty()
  @IsString()
  name: string;

  @IsNotEmpty()
  @IsString()
  promptContent: string;
  
  @IsOptional()
  @IsBoolean()
  isFallbackPrompt: Boolean;

  @IsOptional()
  @IsString()
  customFallbackPrompt: string;
}

export class PushNewPromptDto {
  @IsNotEmpty()
  @IsString()
  agentId: string;

  @IsNotEmpty()
  @IsObject()
  prompt: Prompt;
}