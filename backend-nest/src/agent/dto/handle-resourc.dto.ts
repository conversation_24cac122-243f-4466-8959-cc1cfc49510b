import { IsString, IsEnum, IsOptional, IsNumber } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { JobType } from 'src/mongo/schemas/agents/agents.schema';

export enum MessageStatus {
  FAILED = 'failed',
  SUCCESS = 'success',
}

export class HandleResourceDto {
  @IsString()
  jobId: string;

  @IsString()
  accountId: string;

  @IsString()
  fileName: string;

  @IsNumber()
  characterCount: number;

  @IsEnum(MessageStatus)
  status: MessageStatus;

  @IsEnum(JobType)
  jobType: JobType;

  @IsOptional()
  @IsNumber()
  maxDepth?: number;

  @IsString()
  textContent: string;

  @IsOptional()
  @IsString()
  failedReason?: string;
}