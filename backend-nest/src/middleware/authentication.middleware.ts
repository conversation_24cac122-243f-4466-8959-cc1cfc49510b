import {
  Injectable,
  Logger,
  NestMiddleware,
  UnauthorizedException,
} from '@nestjs/common';
import { randomUUID } from 'crypto';
import { NextFunction, Response } from 'express';
import { RequestWithUser } from 'src/auth/auth.interface';
import { OrganizationService } from 'src/organization/services/organization.service';
// import { RequestService } from 'src/request.service';
import { UserService } from 'src/user/user.service';

@Injectable()
export class AuthenticationMiddleware implements NestMiddleware {
  private readonly logger = new Logger(AuthenticationMiddleware.name);
  constructor(
    private readonly userService: UserService,
    private readonly organizationService: OrganizationService, // private readonly requestService: RequestService,
  ) {}
  async use(req: RequestWithUser, res: Response, next: NextFunction) {
    this.logger.log(AuthenticationMiddleware.name);
    const uuid = randomUUID();
    req.uniqueCode = uuid;
 
    const sessionId = req.cookies['usid'];
    const orgId = req.cookies['orgId'];
    
    //exclude the user creation route from the check
    if (
      req.originalUrl.includes('admin') ||
      req.originalUrl === '/user/id' ||
      req.originalUrl.includes('/user/sessionid') ||
      req.originalUrl.includes('/leadconnector') ||
      req.originalUrl.includes('/migration') ||
      req.originalUrl === '/google-sheet/batch' ||
      req.originalUrl === '/leadconnector/preferences' ||
      req.originalUrl.includes('/google-calendar/callback') ||
      req.originalUrl.includes('/google-sheet/webhook') ||
      req.originalUrl.startsWith('/v1') ||
      req.originalUrl === '/integrations/vector/delete' ||
      req.originalUrl.includes('/webhook') ||
      req.originalUrl.includes('/podium') ||
      req.originalUrl.includes('/hubspot/connect') ||
      req.originalUrl.includes('/tokens') ||
      req.originalUrl.includes('/create/uphex') ||
      //iframe stuffs start
      req.originalUrl.includes('iframe') ||
      req.originalUrl.includes('/hubspot/update/channel') ||
      req.originalUrl.includes('/channels/ghl/') ||
      req.originalUrl.includes('/agent/list/ghl-calendars') ||
      req.originalUrl.includes('/agent/list/tags') ||
      req.originalUrl.includes('/channels/slack/connect') ||
      req.originalUrl.includes('/augmented-query-response') ||
      req.originalUrl.includes('/ghl-tool') ||
      req.originalUrl.includes('/google-calendar/settings') ||
      req.originalUrl.includes('/google-calendar-tool') ||
      req.originalUrl.includes('/resource-status') ||
      req.originalUrl.includes('/channel/outlook/connect') ||
      req.originalUrl === '/agent-templates/all' || 
      req.originalUrl.includes('history/train') ||
      req.originalUrl.includes('/ai-models')
    ) {
      next();
    } else {
      if (!sessionId) {
        throw new UnauthorizedException('Missing session ID');
      }
      if (sessionId) {
        const user = await this.userService.findUserBySessionId(sessionId);
        if (!user) {
          throw new UnauthorizedException('Invalid session');
        }
        //   this.requestService.setUser({
        //     userId: (user as any)._id.toString(),
        //     email: user.email,
        //     userName: user.name,
        //     organizationSetup: user.organizationSetup,
        //   });
        req.userId = (user as any)._id.toString();
        req.username = (user as any)?.name;
        req.email = (user as any)?.email;
        req.orgId = orgId;
      }
      if (
        req.originalUrl === '/user/org/getall' ||
        req.originalUrl === '/user/org/requests' ||
        req.originalUrl === '/user/org/handlereq' ||
        req.originalUrl.includes('/organization') ||
        req.originalUrl.includes('/leadconnector') ||
        req.originalUrl.includes('/migration') ||
        req.originalUrl === '/google-sheet/batch' ||
        req.originalUrl === '/leadconnector/preferences' ||
        req.originalUrl.includes('/google-calendar') ||
        req.originalUrl.includes('/google-sheet/webhook') ||
        req.originalUrl.includes('/calendly') ||
        req.originalUrl.includes('/acuity') ||
        req.originalUrl.includes('/v1') ||
        req.originalUrl.includes('/google-docs') ||
        req.originalUrl === '/app-updates/latest' ||
        req.originalUrl.includes('/tokens') ||
        req.originalUrl.includes('/create/uphex') ||
        req.originalUrl === '/agent-templates/all' ||
        req.originalUrl.includes('iframe')
      ) {
        next();
      } else {
        if (!orgId) {
          throw new UnauthorizedException('Missing organization ID');
        }
        if (orgId) {
          const org = await this.organizationService.getOrganization({
            _id: orgId,
          });
          if (!org) {
            throw new UnauthorizedException('Organization not found');
          }
          // this.requestService.setBillingDetails(org.billing);
          // this.requestService.setOrgDetails({orgId, billing: org.billing});
        }

        next();
      }
    }
  }
}
