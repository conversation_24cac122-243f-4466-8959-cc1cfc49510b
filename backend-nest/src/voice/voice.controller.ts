/**
 * Main controller handling voice API endpoints like activation, calls, and assistant management
 */
import {
  Controller,
  Get,
  Post,
  Req,
  Res,
  Body,
  HttpStatus,
  Render,
  Delete,
  Param,
  Query,
  UseGuards,
  Patch,
} from '@nestjs/common';
import { Request, Response } from 'express';
import {
  ApiOperation,
  ApiQuery,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiParam,
} from '@nestjs/swagger';
import { VoiceService } from './voice.service';
import { MyLogger } from 'src/logger/logger.service';
import { voiceActivationDto } from './vapi/assistants/assistants.dto';
import { handleException } from 'helpers/handleException';
import { RequestWithUser } from 'src/auth/auth.interface';
import { RequireFeature } from 'src/decorators/feature-access.decorator';
import { FeatureAccessGuard } from 'src/guards/feature-access.guard';
import { VoiceUsageService } from 'src/organization/services/usage/voice-usage/voice-usage.service';

@Controller('voice/v1/')
export class VoiceController {
  constructor(
    private readonly voiceService: VoiceService,
    private readonly logger: MyLogger,
    private readonly voiceUsageService: VoiceUsageService,
  ) {}

  @Post('activate')
  async handleVoiceActivation(
    @Body() body: voiceActivationDto,
    @Res() response: Response,
  ) {
    try {
      // 
      const data = await this.voiceService.handleVoiceActivation(body);
      return response
        .status(201)
        .json({ message: 'Voice activated successfully', data});
    } catch (error) {
      handleException(response, error);
    }
  }

  @Post('toggle')
  async toggleAssistantService(@Body() body, @Res() response: Response) {
    await this.voiceService.toggleAssistantService(body);
    return response
      .status(200)
      .json({ message: 'status changed successfully' });
  }

  @Post('server/calls/:agentId')
  async logServerCalls(@Body() body, @Param('agentId') agentId, @Res() response: Response) {
    this.logger.log({
      message: `Received voice events for agent ${agentId} - \n\n` + JSON.stringify(body),
    })
    return response.sendStatus(200);
  }

  @Patch('update/channel/:agentId')
  async handleChannelAssistantMapping(
    @Req () req: RequestWithUser,
    @Param('agentId') agentId,
    @Body() body,
    @Res() response: Response,
  ){
    try {
      await this.voiceService.handleChannelAssistantMapping({
        agentId,
        orgId: body.orgId,
        accountId: body.accountId,
      })
      return response.status(200).json({ message: 'Channel for voice updated successfully' });
    } catch (error) {
      this.logger.error({
        message: error.message,
        context: 'VOICE'
      })
      handleException(response, error);
    }
  }

  @Patch('update/assistant/:agentId')
  async updateAssistant(
    @Req () req: RequestWithUser,
    @Param('agentId') agentId: string,
    @Body() body,
    @Res() response: Response,
  ){
    try {
      const data = await this.voiceService.updateAssistant({
        agentId,
        body
      })
      response.status(200).json(data);
    } catch (error) {
      this.logger.error({
        message: error.message,
        context: 'UPDATE ASSISTANT | VOICE'
      })
      handleException(response, error);
    }
  }

  @Post('revert/:agentId')
  async revertVoiceAssistant(
    @Req () req: RequestWithUser,
    @Param('agentId') agentId: string,
    @Body() body,
    @Res() response: Response,
  ){
    try {
      const data = await this.voiceService.revertVoiceAssistant(agentId);
      response.status(200).json(data);
    } catch (error) {
      this.logger.error({
        message: error.message,
        context: 'REVERT ASSISTANT | VOICE'
      })
      handleException(response, error);
    }
  }

  @Get('assistant/:agentId')
  async getVoiceAssistantConfigurations(
    @Req () req: RequestWithUser,
    @Param('agentId') agentId,
    @Res() response: Response,
  ){
    try {
      const data = await this.voiceService.getVapiAssistant({
        agentId,
      })
      response.status(200).json(data);
    } catch (error) {
      this.logger.error({
        message: error.message,
        context: 'VOICE GET ASSISTANT | VOICE'
      })
      handleException(response, error);
    }
  }

  @Get('calls/:agentId')
  async getVoiceCalls(
    @Req () req: RequestWithUser,
    @Param('agentId') agentId,
    @Query() query: {
      limit?: number,
      createdAtGe?: string,
      createdAtLe?: string
    },
    @Res() response: Response,
  ){
    try {
      const data = await this.voiceService.getVoiceCallHistory(agentId, query.limit, query.createdAtGe, query.createdAtLe);
      return response.status(200).json(data);
    } catch (error) {
      this.logger.error({
        message: error.message,
        context: 'VOICE GET CALL HISTORY | VOICE'
      })
      handleException(response, error);
    }
  }

  @Get('call/:callId')
  async getVoiceCall(
    @Req () req: RequestWithUser,
    @Param('callId') callId,
    @Res() response: Response,
  ){
    try {
      const data = await this.voiceService.getCall(callId);
      return response.status(200).json(data);
    } catch (error) {
      this.logger.error({
        message: error.message,
        context: 'VOICE GET CALL HISTORY | VOICE'
      })
      handleException(response, error);
    }
  }

  @Get('predict/usage/:agentId')
  async predictVoiceUsage(
    @Req () req: RequestWithUser,
    @Param('agentId') agentId,
    @Res() response: Response,
  ){
    try {
      const data = await this.voiceUsageService.predictVoiceUsageCost(agentId);
      return response.status(200).json(data);
    } catch (error) {
      this.logger.error({
        message: `Error in predicting voice usage - ${error.message}`,
        context: 'VOICE GET CALL HISTORY | VOICE'
      })
      handleException(response, error);
    }
  }


  @Patch('transfer-call')
  async handleTransferCall(
    @Req () req: RequestWithUser,
    @Body() body,
    @Res() response: Response,
  ){
    try {
      await this.voiceService.upsertTransfercallType(body);
      return response.status(200).json({ message: 'Transfer call type updated successfully' });
    } catch (error) {
      this.logger.error({
        message: `Error in updating transfer call configurations : ${error.message}`,
        context: 'VOICE | UPDATE TRANSFER CALL'
      })
      handleException(response, error);
    }
  }
}
