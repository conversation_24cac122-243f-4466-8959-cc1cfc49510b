import { Module, forwardRef } from '@nestjs/common';
import { GhlToolService } from './vapi/tools/ghl/ghl-tools.service';
import { PhoneService } from './vapi/phone/phone.service';
import { AssistantsService } from './vapi/assistants/assistants.service';
import { GoogleSheetToolService } from './vapi/tools/google/google-sheet-tool.service';
import { ToolsService } from './vapi/tools/tools.service';
import { VoiceController } from './voice.controller';
import { AgentModule } from 'src/agent/agent.module';
import { VoiceService } from './voice.service';
import { PhoneController } from './vapi/phone/phone.controller';
import { ToolsController } from './vapi/tools/tools.controller';
import { SessionModule } from 'src/session/session.module';
import { SessionService } from 'src/session/session.service';
import { UtilityService } from './vapi/tools/utility.service';
import { GhlController } from './vapi/tools/ghl/ghl-tools.controller';
import { GoogleCalendarToolsController } from './vapi/tools/google/google-calendar-tool.controller';
import { GoogleDocsToolsController } from './vapi/tools/google/google-docs-tool.controller';
import { GoogleSheetToolsController } from './vapi/tools/google/google-sheet-tool.controller';
import { SiteController } from './vapi/tools/site/site-tools.controller';
import { HttpController } from './vapi/tools/http/http-tools.controller';
import { SiteToolService } from './vapi/tools/site/site-tool.service';
import { GoogleCalendarToolService } from './vapi/tools/google/google-calendar-tool.service';
import { GoogleDocsToolService } from './vapi/tools/google/google-docs-tool.service';
import { HttpToolService } from './vapi/tools/http/http-get-tool.service';
import { FeatureFlagsModule } from 'src/feature-flags/feature-flags.module';
import { WebhooksController } from './webhooks/webhooks.controller';
import { WebhooksService } from './webhooks/webhooks.service';
import { OrganizationModule } from 'src/organization/organization.module';
import { KnowledgeService } from './vapi/knowledge/knowledge.service';
import { KnowledgeController } from './vapi/knowledge/knowledge.controller';

@Module({
  imports: [
    forwardRef(() => SessionModule),
    forwardRef(() => AgentModule),
    FeatureFlagsModule,
    forwardRef(() => OrganizationModule),
  ],
  providers: [
    GhlToolService,
    PhoneService,
    AssistantsService,
    GoogleSheetToolService,
    SiteToolService,
    GoogleCalendarToolService,
    GoogleDocsToolService,
    ToolsService,
    VoiceService,
    UtilityService,
    HttpToolService,
    WebhooksService,
    KnowledgeService,
  ],
  exports: [VoiceService, AssistantsService, ToolsService, KnowledgeService],
  controllers: [
    VoiceController,
    PhoneController,
    ToolsController,
    GhlController,
    GoogleCalendarToolsController,
    GoogleDocsToolsController,
    GoogleSheetToolsController,
    SiteController,
    HttpController,
    WebhooksController,
    KnowledgeController,
  ],
})
export class VoiceModule {}
