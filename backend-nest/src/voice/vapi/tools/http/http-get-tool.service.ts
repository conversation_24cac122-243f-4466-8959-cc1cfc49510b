import { Injectable } from '@nestjs/common';
import axios from 'axios';
import { ApiclientService } from 'src/api-client/services/apiclient.service';
import { PROVIDERS } from 'src/lib/constant';
import { MyLogger } from 'src/logger/logger.service';
import { MongoAgentService } from 'src/mongo/service/agent/mongo-agent.service';
import { MongoCredentialsService } from 'src/mongo/service/credentials/mongo-credentials.service';
import { MongoOrganizationService } from 'src/mongo/service/organization/mongo-organization.service';
import { SessionService } from 'src/session/session.service';
import { ToolsService } from '../tools.service';

interface Entity {
  name: string;
  value: string;
}

@Injectable()
export class HttpToolService {
  constructor(
    private readonly mongoCredentialService: MongoCredentialsService,
    private readonly logger: MyLogger,
    private readonly sessionService: SessionService,
    private readonly mongoAgentService: MongoAgentService,
    private readonly mongoOrganizationService: MongoOrganizationService,
    private readonly apiClientService: ApiclientService,
    private readonly toolsService: ToolsService,
  ) {}

  async handleHttpCall(
    {assistantId, toolId, query, path, headers}:
    {assistantId: string, toolId: string, query: Entity[], path: Entity[], headers: Entity[],}
  ) {
    try {
      const agent = await this.mongoAgentService.getAgent({
        'voiceConfig.assistantId': assistantId,
        'voiceConfig.provider': 'vapi',
      });
      const action = agent.actions.find(
        (action) => action.voice.vapiToolId === toolId,
      );
      const { abort } = await this.toolsService.preCheckToolCall(agent);
      if (abort) {
        return;
      }
      const { activity, accountName, accountId, actionId, providerName } =
        action;
      if (providerName === 'get') {
        let requestUrl = action.httpGetRequestDetails.url;
        const {
          queryParameters,
          pathVariables,
          headers: headersArray,
          authorizationType,
          responseBuilder,
        } = action.httpGetRequestDetails;

        //   const res = await this.apiClientService.axiosRequest({
        //     method: 'GET',
        //     headers:
        //   })

        // get request - session servie - line 9876-10617
      }
    } catch (error) {
      this.logger.error({
        message: 'Error in google sheet event tool service ' + error.message,
        context: 'SiteService.handleSiteAction',
      });
    }
  }
}
