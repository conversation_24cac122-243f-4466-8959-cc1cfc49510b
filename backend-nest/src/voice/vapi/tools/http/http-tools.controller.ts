import {
    Controller, Get, Post, Req, Res, Body, HttpStatus, Render, Delete, Param, Query, UseGuards, Patch,
  } from '@nestjs/common';
import { Request, Response } from 'express';
import { handleException } from 'helpers/handleException';
import { HttpToolService } from './http-get-tool.service';

@Controller('http-get-tool')
export class HttpController {
    constructor(
        private readonly httpToolService: HttpToolService,
    ) {}

    @Post('request/:agentId')
    async makeRequest(@Param('agentId') agentId: string, @Req() req: Request, @Res() res: Response, @Body() body: any) {
        try {
            // return await this.ghlToolService.handleGhlCalendarAction()
        } catch (error) {
            handleException(res, error) 
        }
    }
}
