import { Injectable } from '@nestjs/common';
import { GhlApisService } from 'src/api-client/services/ghl-apis/ghl-apis.service';
import { vapiApiService } from 'src/api-client/services/vapi/vapi-api.service';
import { CUSTOM_LLM, CUSTOM_LLM_2, MAP_PROVIDER_TO_KINDS, CUSTOM_LLM_3, CUSTOM_LLM_4, CUSTOM_LLM_5, CUSTOM_LLM_6, PROVIDERS, llmModelMap, isCustomLlmProvider } from 'src/lib/constant';
import { MyLogger } from 'src/logger/logger.service';
import { MongoAgentService } from 'src/mongo/service/agent/mongo-agent.service';
import { AiProviderMaps } from './tools.helper';
import { MongoCredentialsService } from 'src/mongo/service/credentials/mongo-credentials.service';
import { MongoOrganizationService } from 'src/mongo/service/organization/mongo-organization.service';
import { AgentDocument } from 'src/mongo/schemas/agents/agents.schema';
import { getApiKeyAndProvider } from 'src/session/helpers/getApiKeyAndEmbeddingProvider';
import { VectorService } from 'src/vector/services/vector.service';
import { getCredentials } from 'src/session/helpers/getCredentials';

@Injectable()
export class ToolsService {
  constructor(
    private readonly vapiApiService: vapiApiService,
    private readonly logger: MyLogger,
    private readonly ghlApiService: GhlApisService,
    private readonly mongoCredentialService: MongoCredentialsService,
    private readonly mongoOrganizationService: MongoOrganizationService,
    private readonly vectorService: VectorService,
  ) {}

  async createTool({
    type,
    name,
    description,
    properties,
    required,
    serverUrl,
    async
  }: {
    type: 'endCall' | 'transferCall' | 'function';
    name: string;
    description?: string;
    properties?: any;
    required?: string[];
    serverUrl?: string;
    async?: boolean;
  }) {
    try {
      let body = {
        type,
        function: {
          name,
        },
        server: {
          url: serverUrl ?? undefined,
        },
        async,
      };
      if (type === 'function') {
        body['function']['description'] = description ?? undefined;
        body['function']['parameters'] = {
          type: 'object',
          properties,
          required,
        };
      }
      const newTool = await this.vapiApiService.createTool(process.env.VAPI_TOKEN, {
        body,
      });

      return newTool;
    } catch (error) {
      this.logger.error({
        message: `Error in createTool - `+error.message,
        context: 'vapiApiService.createTool',
      });
      return { error: error.message };
    }
  }

  async deleteTool({ toolId }: { toolId: string }) {
    try {
      return await this.vapiApiService.deleteTool(process.env.VAPI_TOKEN, {
        toolId,
      });
    } catch (error) {
      this.logger.error({
        message: `Error deleting toolId ${toolId} : ` + (error?.response?.data?.message ?? error?.message),
        context: 'VAPI API | DELETE TOOL',
      });
      return { error: error.message };
    }
  }

  async updateTool({ toolId, body }: { toolId: string; body: any }) {
    try {
      return await this.vapiApiService.updateTool(process.env.VAPI_TOKEN, {
        toolId,
        body,
      });
    } catch (error) {
      this.logger.error({
        message: error.message,
        context: 'vapiApiService.updateTool',
      });
      return { error: error.message };
    }
  }

  async getToolDescription(){
    try {
      // todo: get tool description
    } catch (error) {
      this.logger.error({
        message: error.message,
        context: 'vapiApiService.getToolDescription',
      });
      return { error: error.message };
    }
  }

  async extractToolProperties(agent: AgentDocument, b: any) {
    let toolId = b?.message?.call?.id, vapiOrgId = b?.message?.call?.orgId;
    let callType = b?.message?.call?.type;
    let toolCallId = b?.message?.tool_calls?.[0]?.id ??  b?.message?.toolCalls?.[0]?.id;
    let assistantId =
      b?.message?.call?.assistant_id  ?? b?.message?.call?.assistantId ?? b?.message?.assistant?.id;
    let contactNumber = b?.message?.customer?.number;
    let contactName = b?.message?.customer?.name;
    // let f = {
    //   name: b?.message?.toolCalls?.[0]?.toolCall?.function?.name,
    //   actionId: b?.message?.toolCalls?.[0]?.toolCall?.function?.name,
    //   arguments: b?.message?.toolCalls?.[0]?.toolCall?.function?.arguments,
    //   toolCallId: b?.message?.toolCalls?.[0]?.toolCall?.id,
    // }
    let conversations = ''

    let prevDirection = '';
    let messages = b?.message?.artifact?.messages ?? [];
    for (let m of messages) {
      if (m.role === 'user') {
        if (prevDirection === 'user') conversations += '\n';
        conversations += `user: ` + m.message + '\n';
        prevDirection = 'user';
      } else if (m.role === 'bot') {
        conversations += `assistant: ` + m.message + '\n\n';
        prevDirection = 'bot';
      }
    }
    if (prevDirection === 'user') conversations += '\n';
    let query;
    let messagesLength = messages.length;
    while (messagesLength > 0) {
      if (messages[messagesLength - 1].role === 'user') {
        query = messages[messagesLength - 1].message;
        break;
      }
      messagesLength--;
    }

    let contact;
    if (callType !== 'webCall') {
      const call = await this.vapiApiService.getCall(
        process.env.VAPI_TOKEN,
        b?.message?.call?.id,
      );
      let c = await this.getCustomerContact(agent, call, { phone: contactNumber, name: contactName });
      contact = c?.contact;
    }
    const org = await this.mongoOrganizationService.getOrganization({ _id: agent.orgId}, {'connections': 1});

    return {
      isWebCall: (callType === 'webCall' || callType === 'web_call'),
      assistantId,
      conversations,
      query,
      contactId: contact?.id ?? "",
      contactDetails: contact,
      toolCallId, 
      org,
    };
  }
  
  async preCheckToolCall(agent:any){
    if (agent?.voiceConfig?.enabled){
      this.logger.log({
        message: `Voice is disabled for the agent, skipping the request`,
        context: `VOICE | TOOL CALL PRECHECK`,
      })
      return {abort: true}
    }
    return {abort: false}
  }

  async getAiProviderConfigs(agent, credentialId, query?: string) {
    let companyId = agent?.aiProvider?.companyId;
    const secretKey = await getCredentials(companyId, credentialId, this.mongoCredentialService, this.logger, null);
    let apiKey, embeddingProvider;
    if (companyId === PROVIDERS.OPENAI_PROVIDER) {
      apiKey = secretKey;
      embeddingProvider = PROVIDERS.OPENAI_PROVIDER;
    } else {
      ({ apiKey, embeddingProvider } = getApiKeyAndProvider({
        companyId: companyId,
      }));
}
    let aiProvAdvSett = {
      temperature: 0.4,
      maxLength: 1000,
      frequencyPenalty: 0,
    };

    if (
      agent.aiProvider && agent.aiProvider.isAdvancedSettings
    ) {
      aiProvAdvSett.temperature = agent.aiProvider.advancedSettings.temperature;
      aiProvAdvSett.maxLength = agent.aiProvider.advancedSettings.maxLength;
      aiProvAdvSett.frequencyPenalty = agent.aiProvider.advancedSettings.frequencyPenalty;
    }

    const maxTokensGeneral = 150;

    const aiProviderAccountId = agent?.aiProvider?.accountId;
    const aiProvider = agent.aiProvider;

    const modelObj = {
      companyId: companyId,
      model_name: agent?.aiProvider?.modelName,
      key: secretKey,
    };

    let modelType = modelObj.model_name;

    //TODO: unnessary mapping for custom llm providers, fix required from frontend
    if(isCustomLlmProvider(companyId)){
      llmModelMap.forEach((llm) => {
        if (modelObj.model_name === llm.frontendName) {
          modelObj.model_name = llm.modelName;
        }
      });
    } 


    const embeddings = await this.vectorService.createEmbedding(
      embeddingProvider,
      apiKey,
      query,
    );

    const embedding = embeddings?.[0]?.embedding;

    return {
      modelObj,
      modelType,
      apiKey,
      embeddingProvider,
      aiProvAdvSett,
      maxTokensGeneral,
      aiProviderAccountId,
      aiProvider,
      embedding,
    };
  }

  async createToolResponse({result, toolCallId, funcName, errorMessage=null} : {result:string, toolCallId: string, funcName: string, errorMessage?: string}){
    if (!errorMessage){
      return {
        results: [{
          toolCallId: toolCallId,
          result,
        }]
      }
    } else {
      return {
        'error': errorMessage,
        'results': []
      }
    }
    // if (errorMessage){
    //   return {
    //     error: errorMessage,
    //     results: []
    //   }
    // } else {
      // return {
      //   results:[{
      //     message:
      //       {
      //         type: "request-complete",
      //         role: "assistant",
      //         content: result,
      //       },
      //     toolCallId: toolcallId,
      //     name: funcName,
      //   }]
      // }
    // }
  }

  async getCustomerContact(agent: AgentDocument, call: any, overrides: { phone: string, name: string } = undefined) {
    try {
      const locationId = agent?.voiceConfig?.channel?.keyId;
      const kind = MAP_PROVIDER_TO_KINDS?.[agent?.voiceConfig?.channel?.providerName];
      if (!locationId) throw new Error('Location Id not found');
      if (!kind) throw new Error(`No default subaccount added to the voice assistant's configuration`);
      const cred = await this.mongoCredentialService.getCredential({
        kind,
        keyId: locationId
      });
      const tokens = {
        access_token: cred.creds?.accessToken,
        refresh_token: cred.creds?.refreshToken,
      };

      const data = {
        phone: call?.customer?.number ?? overrides?.phone,
        name: call?.customer?.name ?? overrides?.name,
        locationId
      }

      const contact = await this.ghlApiService.createContact(
        tokens,
        locationId,
        data,
        undefined,
      );
      return {
        contact: contact?.id ? contact : contact.contact,
        locationId,
        tokens
      };
    } catch (error) {
     this.logger.error({
       message: error.message,
       context: 'VOICE TOOLS | GET CUSTOMER CONTACT',
     }) 
    }
  }

  async createTransferCallTool({transferCall, agentId}: {
    transferCall: {
      transferCallNumber: string;
      transferMessage?: string;
      transferCallPrompt: string;
    };
    agentId: string;
  }){
    try {
      const newTransferCallTool = await this.vapiApiService.createTool(
        process.env.VAPI_TOKEN,
        {
          body: {
            "type": "transferCall",
            "destinations": [
              {
                "type": "number",
                "number": transferCall.transferCallNumber,
                "message": transferCall.transferMessage
              }
            ],
            "function": {
              "name": `transferCall for ${agentId}`,
              "description": transferCall.transferCallPrompt,
              "parameters": {
                "type": "object",
                "properties": {
                  "destination": {
                    "type": "string",
                    "enum": [transferCall.transferCallNumber],
                    "description": "The destination to transfer the call to."
                  }
                },
                "required": ["destination"]
              }
            },
            "messages": [
              {
                "type": "request-start",
                "content": transferCall.transferMessage,
                "conditions": [
                  {
                    "param": "destination",
                    "operator": "eq",
                    "value": transferCall.transferCallNumber
                  }
                ]
              }
            ]
          }
        });

        return newTransferCallTool;
    } catch (error) {
      this.logger.error({
        message: `Failed to create a transfer call tool - ${error.message}`,
        context: 'VOICE TOOLS | CREATE TRANSFER CALL TOOL'
      })
    }
  }

  async getTool({toolId}){ 
    try {
      return await this.vapiApiService.getTool(process.env.VAPI_TOKEN,toolId);
    } catch (error) {
      this.logger.error({
        message: `Failed to get tool - ${error.message}`,
        context: 'VOICE TOOLS | GET TOOL'
      })
    }
  }
}
