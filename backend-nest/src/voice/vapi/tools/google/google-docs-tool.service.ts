import { Injectable } from '@nestjs/common';
import axios from 'axios';
import { PROVIDERS } from 'src/lib/constant';
import { MyLogger } from 'src/logger/logger.service';
import { MongoAgentService } from 'src/mongo/service/agent/mongo-agent.service';
import { MongoCredentialsService } from 'src/mongo/service/credentials/mongo-credentials.service';
import { MongoOrganizationService } from 'src/mongo/service/organization/mongo-organization.service';
import { SessionService } from 'src/session/session.service';
import { UtilityService } from '../utility.service';
import { ToolsService } from '../tools.service';

@Injectable()
export class GoogleDocsToolService {
  constructor(
    private readonly mongoCredentialService: MongoCredentialsService,
    private readonly logger: MyLogger,
    private readonly sessionService: SessionService,
    private readonly mongoAgentService: MongoAgentService,
    private readonly mongoOrganizationService: MongoOrganizationService,
    private readonly toolUtilityService: UtilityService,
    private readonly toolsService: ToolsService,
  ) {}

  async handleGoogleDocsAction(
    {assistantId, toolId, query}:
    {assistantId: string,toolId: string,query: string,}
  ) {
    try {
      const agent = await this.mongoAgentService.getAgent({
        'voiceConfig.assistantId': assistantId,
        'voiceConfig.provider': 'vapi',
      });
      const action = agent.actions.find(
        (action) => action.voice.vapiToolId === toolId,
      );
      const {abort} = await this.toolsService.preCheckToolCall(agent);
      if (abort) {
        return;
      }
      const { activity, accountName, accountId, actionId } = action;
      const org = await this.mongoOrganizationService.getOrganization({
        _id: agent.orgId,
      });
      //   const sheetEntity = (org?.connections?.dataSources ?? []).find((ds) => ds.accountId === accountId);
      let credentialId;
      org.connections.aiProvider.forEach((a) => {
        if (a.accountId == accountId) {
          credentialId = a.credentialId;
        }
      });
      const aiProvider = agent.aiProvider;
      const modelType = agent.aiProvider.modelName;
      let companyId = agent.aiProvider.companyId;

      const embedding = await this.toolUtilityService.createEmbedding(
        companyId,
        modelType,
        credentialId,
        query,
      );
      await this.sessionService.getSessionSamples(
        agent.id,
        agent.orgId,
        embedding,
        PROVIDERS.GOOGLE_DOCS,
        accountId,
      );
    } catch (error) {
      this.logger.error({
        message: 'Error in google docs event tool service ' + error.message,
        context: 'GoogleDocsService.handleGoogleDocsAction',
      });
    }
  }
}
