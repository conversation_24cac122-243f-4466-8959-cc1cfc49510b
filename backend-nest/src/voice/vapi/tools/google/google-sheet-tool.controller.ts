import {
    Controller, Get, Post, Req, Res, Body, HttpStatus, Render, Delete, Param, Query, UseGuards, Patch,
  } from '@nestjs/common';
import { Request, Response } from 'express';
import { handleException } from 'helpers/handleException';
import { GoogleSheetToolService } from './google-sheet-tool.service';

@Controller('google-sheet-tool')
export class GoogleSheetToolsController {
    constructor(
        private readonly googleSheetToolService: GoogleSheetToolService
    ) {}

    @Post('query/:agentId')
    async query(@Param('agentId') agentId: string, @Req() req: Request, @Res() res: Response, @Body() body: any) {
        try {
            return await this.googleSheetToolService.handleGoogleSheetAction(body)
        } catch (error) {
            handleException(res, error) 
        }
    }
}
