import {
    Controller, Get, Post, Req, Res, Body, HttpStatus, Render, Delete, Param, Query, UseGuards, Patch,
  } from '@nestjs/common';
import { Request, Response } from 'express';
import { handleException } from 'helpers/handleException';
import { GoogleCalendarToolService } from './google-calendar-tool.service';
import { MongoAgentService } from 'src/mongo/service/agent/mongo-agent.service';
import { ToolsService } from '../tools.service';
import { MyLogger } from 'src/logger/logger.service';

@Controller('google-calendar-tool')
export class GoogleCalendarToolsController {

  constructor(
    private readonly googleCalendarToolService: GoogleCalendarToolService,
    private readonly logger: MyLogger,
    private readonly toolService: ToolsService,
    private readonly mongoAgentService: MongoAgentService,
  ) {}

  @Post('availability/:agentId/:actionId')
  async getAvailability(@Param('agentId') agentId: string, @Param('actionId') actionId: string, @Req() req: Request, @Res() res: Response, @Body() body: any) {
    try {
      const agent = await this.mongoAgentService.getAgent({_id: agentId});
      
      const tool = (agent?.voiceConfig?.tools || []).find(
        (tool) => tool.actionId === actionId
      );
      if (!tool.active) {
        throw new Error('Tool not active');
      }

      const {isWebCall, query, assistantId, conversations, contactDetails, contactId, toolCallId, org} = await this.toolService.extractToolProperties(agent, body)
      const result = await this.googleCalendarToolService.handleGoogleCalendarAction({
        assistantId,
        conversations, 
        query,
        contactDetails,
        contactId,
        isWebCall,
        toolBody: body,
        org,
        actionId,
        agent
      });
      const response = await this.toolService.createToolResponse({
        result, toolCallId, funcName: actionId
      });
      this.logger.log({
        message: `Availability success | data - ${result}`,
        context: `VOICE TOOL | GOOGLE CALENDAR READ`,
      })
      res.status(HttpStatus.CREATED).json(response)
    } catch (error) {
      this.logger.error({
        message: error?.response?.data?.message ?? error.message,
        context: `VOICE TOOL | GOOGLE CALENDAR READ`,
      })
      const response = await this.toolService.createToolResponse({
        result: '', toolCallId: '', funcName:'', errorMessage: 'Sorry your request could not be processed. Please try again later.'
      });
      res.status(HttpStatus.CREATED).json(response);
    }
  }

  @Post('appointment/create/:agentId/:actionId')
  async createAppointment(@Param('agentId') agentId: string, @Param('actionId') actionId: string, @Req() req: Request, @Res() res: Response, @Body() body: any) {
    try {
      const agent = await this.mongoAgentService.getAgent({_id: agentId});
      
      const tool = (agent?.voiceConfig?.tools || []).find(
        (tool) => tool.actionId === actionId
      );
      if (!tool.active) {
        throw new Error('Tool not active');
      }

      const {
        isWebCall,
        query,
        assistantId,
        conversations,
        contactDetails,
        contactId,
        toolCallId,
        org,
      } = await this.toolService.extractToolProperties(agent, body);

      const r = await this.googleCalendarToolService.handleGoogleCalendarAction({...body, assistantId, isWebCall, org, actionId, query, conversations, contactDetails, contactId, agent});

      const response = await this.toolService.createToolResponse({
        result: r, toolCallId , funcName: actionId
      });
      return res.status(HttpStatus.CREATED).json(response);
    } catch (error) {
      const response = await this.toolService.createToolResponse({
        result: '', toolCallId: '', funcName:'', errorMessage: 'Sorry your request could not be processed. Please try again later.'
      });
      this.logger.error({
        message: error?.response?.data ?? error?.message,
        context: `VOICE | CREATE APPOINTMENT`,
      })
      res.status(HttpStatus.CREATED).json(response);
    }
  }

  @Post('events/:agentId/:actionId')
  async getEvents(@Param('agentId') agentId: string, @Param('actionId') actionId: string, @Req() req: Request, @Res() res: Response, @Body() body: any) {
    try {
      const agent = await this.mongoAgentService.getAgent({_id: agentId});
      
      const tool = (agent?.voiceConfig?.tools || []).find(
        (tool) => tool.actionId === actionId
      );
      if (!tool.active) {
        throw new Error('Tool not active');
      }

      const {isWebCall, query, assistantId, conversations, contactDetails, contactId, toolCallId, org} = await this.toolService.extractToolProperties(agent, body)
      const result = await this.googleCalendarToolService.handleGoogleCalendarAction({
        assistantId,
        conversations, 
        query,
        contactDetails,
        contactId,
        isWebCall,
        toolBody: body,
        org,
        actionId,
        agent
      });
      const response = await this.toolService.createToolResponse({
        result, toolCallId, funcName: actionId
      });
      this.logger.log({
        message: `Get events success | data - ${result}`,
        context: `VOICE TOOL | GOOGLE CALENDAR GET EVENTS`,
      })
      res.status(HttpStatus.CREATED).json(response)
    } catch (error) {
      this.logger.error({
        message: error?.response?.data?.message ?? error.message,
        context: `VOICE TOOL | GOOGLE CALENDAR GET EVENTS`,
      })
      const response = await this.toolService.createToolResponse({
        result: '', toolCallId: '', funcName:'', errorMessage: 'Sorry your request could not be processed. Please try again later.'
      });
      res.status(HttpStatus.CREATED).json(response);
    }
  }  
}
