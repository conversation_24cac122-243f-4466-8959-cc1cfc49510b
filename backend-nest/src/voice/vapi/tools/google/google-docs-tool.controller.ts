import {
    Controller, Get, Post, Req, Res, Body, HttpStatus, Render, Delete, Param, Query, UseGuards, Patch,
  } from '@nestjs/common';
import { Request, Response } from 'express';
import { handleException } from 'helpers/handleException';
import { GoogleDocsToolService } from './google-docs-tool.service';

@Controller('google-docs-tool')
export class GoogleDocsToolsController {
    constructor(
        private readonly googleDocsToolService: GoogleDocsToolService
    ) {}

    @Post('query/:agentId')
    async query(@Param('agentId') agentId: string, @Req() req: Request, @Res() res: Response, @Body() body: any) {
        try {
            return await this.googleDocsToolService.handleGoogleDocsAction(body)
        } catch (error) {
            handleException(res, error) 
        }
    }
}
