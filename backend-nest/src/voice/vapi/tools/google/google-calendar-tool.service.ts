import { BadRequestException, Injectable } from '@nestjs/common';
import axios from 'axios';
import { MyLogger } from 'src/logger/logger.service';
import { MongoAgentService } from 'src/mongo/service/agent/mongo-agent.service';
import { MongoCredentialsService } from 'src/mongo/service/credentials/mongo-credentials.service';
import { MongoOrganizationService } from 'src/mongo/service/organization/mongo-organization.service';
import { MongoUserService } from 'src/mongo/service/user/mongo-user.service';
import { ToolsService } from '../tools.service';
import { SessionService } from 'src/session/session.service';
import { AgentDocument } from 'src/mongo/schemas/agents/agents.schema';
import { OrganizationDocument } from 'src/mongo/schemas/organization/organization.schema';
import { OPTIMIZEOPTIONS } from 'src/lib/constant';
import { isIsoDate } from 'src/lib/utils';

@Injectable()
export class GoogleCalendarToolService {
  constructor(
    private readonly mongoCredentialService: MongoCredentialsService,
    private readonly logger: MyLogger,
    private readonly mongoOrganizationService: MongoOrganizationService,
    private readonly mongoUserService: MongoUserService,
    private readonly toolsService: ToolsService,
    private readonly sessionService: SessionService,
  ) {}

  async handleGoogleCalendarAction(
    { assistantId, actionId, contactId, conversations, query, contactDetails, agent, toolBody, org, isWebCall} :
    {   
        assistantId: string,
        actionId: string,
        contactId?: string,
        conversations: string,
        query: string,
        contactDetails?: any,
        isWebCall?:boolean,
        agent: AgentDocument,
        toolBody: any,
        org: OrganizationDocument
    }
  ) {
      const action = agent.actions.find((action) => action.actionId === actionId);
      const { activity, accountName, accountId  } = action;

      const aiProvider = org?.connections?.aiProvider.find(
          (ap) => ap.accountId === agent?.aiProvider?.accountId,
        );
      const {modelObj, embedding, modelType} = await this.toolsService.getAiProviderConfigs(agent, aiProvider?.credentialId);
      const calendarEntity = (org?.connections?.dataSources ?? []).find((ds) => ds.accountId === accountId);
      if (!calendarEntity) {
        this.logger.error({
            message: `Calendar not connected: ${accountId}, orgId: ${agent.orgId}`,
        });
        throw new BadRequestException('Calendar not connected');
      }

      let resultAvailabilty;
      const sysPrompt =
        (agent?.prompts?.prompt ?? []).find(
          (p) => p.promptId === agent?.prompts?.currentActive,
        )?.promptContent ?? '';
      
      if (activity === 'write') {
        if (!isWebCall){
          const botResponse = await this.sessionService.writeActions(
            modelObj, contactId, agent._id.toString(), agent, conversations, org, sysPrompt, query, modelType, embedding, org._id.toString(),
            undefined, // aiProvAdvSett,
            true, //webhook
            OPTIMIZEOPTIONS.ACCURACY, // optimize
            [], // actionIdArray,
            contactDetails,
            false, undefined,
            [actionId],//includeIds
            [], // excludeIds,
          );
          const botResponses = botResponse?.botResponseForEachDataSource ?? [];
          for (const botResponse of botResponses) {
            let { startDate = '', endDate = '' } = botResponse.eventData;
            const formattedTime =  isIsoDate(startDate) ?  `${startDate.getDate()} ${startDate.toLocaleString('default', { month: 'long' })}, ${startDate.getFullYear()} at ${startDate.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' })}`: startDate;
              this.logger.log({
                  message: "detected Web call -> skipping appointment creation. Result of the call: " + JSON.stringify({startDate, endDate}),
                  context: "VOICE TOOL | GOOGLE CALENDAR | WRITE",
              })
              return "Your appointment has been booked successfully at " + formattedTime;
          }
        }
        return `your appointment is booked successfully`;
      } else {
        resultAvailabilty = await this.sessionService.readActions(
          modelObj,
          contactId,
          agent._id.toString(),
          agent,
          conversations,
          org?._id?.toString(),
          org,
          embedding,
          sysPrompt,
          query,
          modelType,
          undefined, // aiProvAdvSett,
          true, //webhook
          OPTIMIZEOPTIONS.ACCURACY, // optimize
          [], // actionIdArray,
          contactDetails,
          [],
          [actionId],//includeIds
          [], // excludeIds,
        );
        this.logger.log({
          message: `Google calendar read tool : ${resultAvailabilty?.systemPrompt}`,
          context: 'VOICE TOOL | GOOGLE CALENDAR READ',
        });
        return resultAvailabilty?.systemPrompt ?? 'Could not find availability. Please try again';
      } 
  }
}
