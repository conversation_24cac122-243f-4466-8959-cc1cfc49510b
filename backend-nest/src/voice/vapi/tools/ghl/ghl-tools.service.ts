import { BadRequestException, Injectable } from '@nestjs/common';
import { GhlApisService } from 'src/api-client/services/ghl-apis/ghl-apis.service';
import { KINDS, OPTIMIZEOPTIONS, PROVIDERS } from 'src/lib/constant';
import { MyLogger } from 'src/logger/logger.service';
import { MongoAgentService } from 'src/mongo/service/agent/mongo-agent.service';
import { MongoCredentialsService } from 'src/mongo/service/credentials/mongo-credentials.service';
import { MongoOrganizationService } from 'src/mongo/service/organization/mongo-organization.service';
import { ToolsService } from '../tools.service';
import { SessionService } from 'src/session/session.service';
import { AgentDocument } from 'src/mongo/schemas/agents/agents.schema';
import { isIsoDate } from 'src/lib/utils';
import { OrganizationDocument } from 'src/mongo/schemas/organization/organization.schema';

@Injectable()
export class GhlToolService {
    constructor(
        private readonly ghlApiService: GhlApisService,
        private readonly mongoCredentialService: MongoCredentialsService,
        private readonly logger: MyLogger,
        private readonly mongoAgentService: MongoAgentService,
        private readonly mongoOrganizationService: MongoOrganizationService,
        private readonly toolsService: ToolsService,
        private readonly sessionService: SessionService,
    ) {}

    async addGhlTag(
        { assistantId, actionId, contactId, toolBody, locationId, tag }:
        {assistantId: string, actionId: string, contactId: string, toolBody: any, locationId?: string, tag: string}
    ) {
        if (!locationId) {
            this.logger.error({
                message: 'No mapped subbaccount found to add the tag to',
                context: 'VOICE TOOL | ADD TAG',
            });
            return "No mapped subbaccount found to add the tag to";
        }
        const creds = await this.mongoCredentialService.getCredential({
            kind: KINDS.GHL_CREDENTIAL,
            keyId: locationId,
        })
        const tokens = {
            access_token: creds.creds.accessToken,
            refresh_token: creds.creds.refreshToken,
        }
        this.logger.log({
            message: `add GHL tag for contactId ${contactId} | tag - ${tag}`,
            context: 'VOICE | ADD TAG',
        })
        const r = await this.ghlApiService.addTag(tokens, locationId, contactId, tag);
        return 'ok';
    }

    async evaluateCustomField({
        assistantId, actionId, locationId, contactId, value, agent, conversations, query, toolBody, contactDetails
    }:{
            agent: AgentDocument,
            assistantId: string,
            actionId: string,
            contactId: string,
            locationId: string,
            value: string,
            conversations: string,
            query: string,
            toolBody: any,
            contactDetails?: any,
    }){
        // const creds = await this.mongoCredentialService.getCredential({
        //     kind: KINDS.GHL_CREDENTIAL,
        //     keyId: locationId,
        // })
        // const tokens = {
        //     access_token: creds.creds.accessToken,
        //     refresh_token: creds.creds.refreshToken,
        // }
        const action = agent.actions.find((action) => action.actionId === actionId);
        const { activity, accountName, accountId  } = action;

        const org = await this.mongoOrganizationService.getOrganization({ _id: agent.orgId}, {'connections': 1});
        const aiProvider = org?.connections?.aiProvider.find(
            (ap) => ap.accountId === agent?.aiProvider?.accountId,
          );
        const { modelObj, embedding, modelType } = await this.toolsService.getAiProviderConfigs(agent, aiProvider?.credentialId, query);

        const sysPrompt =
          (agent?.prompts?.prompt ?? []).find(
            (p) => p.promptId === agent?.prompts?.currentActive,
          )?.promptContent ?? '';

        const r = await this.sessionService.readActions(
            modelObj,
            contactId,
            agent._id.toString(),
            agent,
            conversations,
            org?._id?.toString(),
            org,
            embedding,
            sysPrompt,
            query,
            modelType,
            undefined, // aiProvAdvSett,
            true, //webhook
            OPTIMIZEOPTIONS.ACCURACY, // optimize
            [], // actionIdArray,
            contactDetails,
            [],
            [actionId],//includeIds
            [], // excludeIds,
          );
        return "ok";
    }


    async handleGhlCalendarAction(
        { assistantId, actionId, contactId, startTime, conversations, query, contactDetails, agent, toolBody, org, isWebCall} :
        {   
            assistantId: string,
            actionId: string,
            contactId?: string,
            startTime?: string,
            conversations: string,
            query: string,
            contactDetails?: any,
            isWebCall?:boolean,
            agent: AgentDocument,
            toolBody: any,
            org: OrganizationDocument
        }
    ) {
        const action = agent.actions.find((action) => action.actionId === actionId);
        const { activity, accountName, accountId  } = action;

        // get calendar details from organization
        const calendarEntity = (org?.connections?.dataSources ?? []).find((ds) => ds.accountId === accountId);
        if (!calendarEntity) {
            this.logger.error({
                message: `Calendar datasource not found: ${accountId}, orgId: ${agent.orgId}`,
            });
            throw new BadRequestException('Calendar datasource not found: ' + accountId);
        }
        const aiProvider = org?.connections?.aiProvider.find(
          (ap) => ap.accountId === agent?.aiProvider?.accountId,
        );

        const {modelObj, embedding, modelType} = await this.toolsService.getAiProviderConfigs(agent, aiProvider?.credentialId);
        
        const sysPrompt =
          (agent?.prompts?.prompt ?? []).find(
            (p) => p.promptId === agent?.prompts?.currentActive,
          )?.promptContent ?? '';

        // get credentials
        const creds = await this.mongoCredentialService.getCredential({
            _id: calendarEntity?.credentialId,
        });
        const tokens = {
            access_token: creds.creds.accessToken,
            refresh_token: creds.creds.refreshToken,
        }
        let resultAvailabilty;
        if (activity === 'read') {
            resultAvailabilty = await this.sessionService.readActions(
                modelObj,
                contactId,
                agent._id.toString(),
                agent,
                conversations,
                org?._id?.toString(),
                org,
                embedding,
                sysPrompt,
                query,
                modelType,
                undefined, // aiProvAdvSett,
                true, //webhook
                OPTIMIZEOPTIONS.COST, // optimize
                [], // actionIdArray,
                contactDetails,
                [],
                [actionId],//includeIds
                [], // excludeIds,
            );
            this.logger.log({
                message: `GHL read tool : ${JSON.stringify(resultAvailabilty)}`,
                context: 'VOICE TOOL | GHL CALENDAR | READ',
            });
            return resultAvailabilty?.systemPrompt;
            
        } else if (activity === 'write') {
            const botResponse = await this.sessionService.writeActions(
                modelObj, contactId, agent._id.toString(), agent, conversations, org, sysPrompt, query, modelType, embedding, org._id.toString(),
                undefined, // aiProvAdvSett,
                true, //webhook
                OPTIMIZEOPTIONS.COST, // optimize
                [], // actionIdArray,
                contactDetails,
                false, undefined,
                [actionId],//includeIds
                [], // excludeIds,
            );
            const botResponses = botResponse?.botResponseForEachDataSource ?? [];
            for (const botResponse of botResponses) {
                if (botResponse.kind === PROVIDERS.GHL_CALENDAR){                    
                    let { startDate = '', endDate = '' } = botResponse.eventData;
                    const formattedTime =  isIsoDate(startDate) ?  `${startDate.getDate()} ${startDate.toLocaleString('default', { month: 'long' })}, ${startDate.getFullYear()} at ${startDate.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' })}`: startDate;
                    if (isWebCall) {
                        this.logger.log({
                            message: "detected Web call -> skipping appointment creation. Result of the call: " + JSON.stringify({startDate, endDate}),
                            context: "VOICE TOOL | GHL CALENDAR | WRITE",
                        })
                        return "Your appointment has been booked successfully at " + formattedTime;
                    }
                    
                    const result = await this.ghlApiService.makeGhlCalendarAppointment(
                        tokens,
                        calendarEntity.keyId,
                        calendarEntity.calendarId,
                        contactId,
                        startDate,
                        endDate,
                    );
                    if (result.error){
                        return "We failed to book your appointment with error - " + result.error + ". Please try again.";
                    } else {
                        return "Your appointment has been booked successfully";
                    }
                }
            }
            this.logger.log({
                message: `GHL write tool result : ${JSON.stringify(resultAvailabilty)}`,
                context: 'VOICE TOOL | GHL CALENDAR | WRITE',
            });
            return "We couldn't book your appointment. Please try again.";
        }
        return resultAvailabilty;
    }
}
