export const createGHLgetFreeSlots = (daysList)=>{
    let message = '', slotPrompts = [];
    for (var day in daysList) {
        if (day !== 'traceId') {
            //convert day to conversational format
            var year = parseInt(day.split('-')[0], 10);
            var month = parseInt(day.split('-')[1], 10);
            var dayOfMonth = parseInt(day.split('-')[2], 10);
            var date = new Date(year, month - 1, dayOfMonth);
            var longDate = date.toLocaleDateString('en-US', {
            weekday: 'long',
            month: 'long',
            day: 'numeric',
            });

            var daySlots = daysList[day].slots;
            if (
            daySlots !== undefined &&
            daySlots.length > 0 &&
            daySlots.length <= 5
            ) {
            var slotPrompt =
                'On ' +
                longDate +
                ', the following times are available:';

            for (var s = 0; s < daySlots.length; s++) {
                var slot = daySlots[s];
                var timeSlot = slot.split('T')[1];
                var actualTime = timeSlot.split('-')[0];
                var hour = parseInt(actualTime.split(':')[0]);
                var minute = actualTime.split(':')[1];
                var ampm = 'am';
                if (hour > 12) {
                hour -= 12;
                ampm = 'pm';
                } else if (hour === 12) {
                ampm = 'pm';
                } else if (hour === 0) {
                hour = 12;
                }
                slot = hour + ':' + minute + ampm;

                slotPrompt += slot + ', ';
                //if it's the last slot, remove the comma and add a new line
                if (s === daySlots.length - 1) {
                slotPrompt = slotPrompt.slice(0, -2);
                slotPrompt += '\n\n';
                }
            }
            } else if (
            daySlots !== undefined &&
            daySlots.length > 5
            ) {
            var selectedSlots = [];
            var slotPrompt =
                'On ' +
                longDate +
                ', the following times are available:\n';

            while (selectedSlots.length < 5) {
                var randomIndex = Math.floor(
                Math.random() * daySlots.length,
                );
                if (
                selectedSlots.indexOf(daySlots[randomIndex]) === -1
                ) {
                selectedSlots.push(daySlots[randomIndex]);
                }
            }

            for (var s = 0; s < selectedSlots.length; s++) {
                var slot = selectedSlots[s];
                var timeSlot = slot.split('T')[1];
                var actualTime = timeSlot.split('-')[0];
                var hour = parseInt(actualTime.split(':')[0]);
                var minute = actualTime.split(':')[1];
                var ampm = 'am';
                if (hour > 12) {
                hour -= 12;
                ampm = 'pm';
                } else if (hour === 12) {
                ampm = 'pm';
                } else if (hour === 0) {
                hour = 12;
                }
                var formattedSlot = hour + ':' + minute + ampm;

                slotPrompt += formattedSlot + ', ';
                //if it's the last slot, remove the comma and add a new line
                if (s === selectedSlots.length - 1) {
                slotPrompt = slotPrompt.slice(0, -2);
                slotPrompt += '\n\n';
                }
            }
            } else {
            var slotPrompt =
                '\n\nOn ' + day + ', there are no available times.';
            }

            var slotPromptString = '';
            slotPromptString +=
            'After checking the calendar, the following results were returned:\n\n';
            for (var s = 0; s < slotPrompts.length; s++) {
            slotPromptString += slotPrompts[s];
            }
            slotPrompts.push(slotPrompt);
        }
        }
        message +=
                '\n\nAfter checking the calendar, the following available days and times were found:';
                message += slotPrompts.join('');
                message +=
                'You should only refer the listed times above when discussing availability with the contact. Assume that any other days or times not listed above are not confirmed as available.';
    return message;
}