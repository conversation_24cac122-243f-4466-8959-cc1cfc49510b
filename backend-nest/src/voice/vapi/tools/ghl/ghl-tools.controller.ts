import {
    Controller, Get, Post, Req, Res, Body, HttpStatus, Render, Delete, Param, Query, UseGuards, Patch,
  } from '@nestjs/common';
import { Request, Response } from 'express';
import { handleException } from 'helpers/handleException';
import { GhlToolService } from './ghl-tools.service';
import { MongoAgentService } from 'src/mongo/service/agent/mongo-agent.service';
import { ToolsService } from '../tools.service';
import { MyLogger } from 'src/logger/logger.service';


@Controller('ghl-tool')
export class GhlController {

  constructor(
    private readonly ghlToolService: GhlToolService,
    private readonly mongoAgentService: MongoAgentService,
    private readonly toolService: ToolsService,
    private readonly logger: MyLogger
  ) {}

  @Post('availability/:agentId/:actionId')
  async getAvailability(@Param('agentId') agentId: string, @Param('actionId') actionId: string, @Req() req: Request, @Res() res: Response, @Body() body: any) {
    try {
      this.logger.log({
        context: JSON.stringify(body),
        message: `VOICE TOOL | GHL GET AVAILABILITY`,
      })

      const agent = await this.mongoAgentService.getAgent({_id: agentId});
      
      const tool = (agent?.voiceConfig?.tools || []).find(
        (tool) => tool.actionId === actionId
      );
      if (!tool.active) {
        throw new Error('Tool not active');
      }

      const {isWebCall, query, assistantId, conversations, contactDetails, contactId, toolCallId, org} = await this.toolService.extractToolProperties(agent, body)
      const result = await this.ghlToolService.handleGhlCalendarAction({
        assistantId,
        actionId,
        agent,
        conversations,
        query,
        contactDetails,
        contactId,
        isWebCall,
        toolBody: body,
        org
      });
      const response = await this.toolService.createToolResponse({
        result, toolCallId, funcName: actionId
      });
      this.logger.log({
        message: `Availability success | data - ${JSON.stringify(result)}`,
        context: `VOICE TOOL | GHL CALENDAR READ`,
      })
      res.status(HttpStatus.CREATED).json(response)
    } catch (error) {
      this.logger.error({
        message: error?.response?.data?.message ?? error.message,
        context: `VOICE TOOL | GHL CALENDAR READ`,
      })
      const response = await this.toolService.createToolResponse({
        result: '', toolCallId: '', funcName:'', errorMessage: 'Sorry your request could not be processed. Please try again later.'
      });
      res.status(HttpStatus.CREATED).json(response)
    }
  }

  @Post('appointment/create/:agentId/:actionId')
  async createAppointment(@Param('agentId') agentId: string, @Param('actionId') actionId: string, @Req() req: Request, @Res() res: Response, @Body() body: any) {
    try {
      this.logger.log({
        context: JSON.stringify(body),
        message: `VOICE TOOL | GHL CREATE APPOINTMENT`,
      })

      const agent = await this.mongoAgentService.getAgent({_id: agentId});

      const tool = (agent?.voiceConfig?.tools || []).find(
        (tool) => tool.actionId === actionId
      );
      if (!tool.active) {
        throw new Error('Tool not active');
      }
      
      const {
        isWebCall,
        query,
        assistantId,
        conversations,
        contactDetails,
        contactId,
        toolCallId,
        org,
      } = await this.toolService.extractToolProperties(agent, body);
      
      const r = await this.ghlToolService.handleGhlCalendarAction({...body, assistantId, isWebCall, org, actionId, query, conversations, contactDetails, contactId, agent});

      const response = await this.toolService.createToolResponse({
        result: r, toolCallId , funcName: actionId
      });
      res.status(HttpStatus.CREATED).json(response);
    } catch (error) {
      const response = await this.toolService.createToolResponse({
        result: '', toolCallId: '', funcName:'', errorMessage: 'Sorry your request could not be processed. Please try again later.'
      });
      this.logger.error({
        message: error?.response?.data ?? error?.message,
        context: `VOICE | CREATE APPOINTMENT`,
      })
      res.status(HttpStatus.CREATED).json(response)
    }
  }

  @Post('tag/:agentId/:actionId')
  async addTag(@Param('agentId') agentId: string, @Param('actionId') actionId: string, @Req() req: Request, @Res() res: Response, @Body() body: any) {
    try {
      this.logger.log({
        context: JSON.stringify(body),
        message: `VOICE TOOL | GHL ADD TAG`,
      });

      const agent = await this.mongoAgentService.getAgent({_id: agentId});

      const tool = (agent?.voiceConfig?.tools || []).find(
        (tool) => tool.actionId === actionId
      );
      if (!tool.active) {
        throw new Error('Tool not active');
      }

      const {isWebCall, query, assistantId, conversations, contactDetails, contactId, toolCallId} = await this.toolService.extractToolProperties(agent, body);
      let result: string;
      const action = agent?.actions?.find((a) => a.actionId === actionId);
      const tag = action?.metaData?.tagData?.tagValue;
      const locationId = agent?.voiceConfig?.channel?.keyId;
      if (!isWebCall){
        result = await this.ghlToolService.addGhlTag({toolBody: body, assistantId, actionId, contactId, tag, locationId });
        this.logger.error({
          message: `Add tag success | data - ${JSON.stringify(result)}`,
          context: `VOICE TOOL | ADD TAG`,
        })
        const response = await this.toolService.createToolResponse({
          result: 'Tag added', toolCallId, funcName: actionId,
        });
        return res.status(HttpStatus.CREATED).json(response);
      } else {
        this.logger.log({
          message: `Webcall detected -> not performing add tag action | data - ${{tag, locationId}}`,
          context: `VOICE TOOL | GHL ADD TAG`,
        })
        res.status(HttpStatus.CREATED).json('ok');
      }
    } catch (error) {
      const response = await this.toolService.createToolResponse({
        result: '', toolCallId: '', funcName:'', errorMessage: 'Failed to add tag'
      });
      this.logger.error({
        message: error?.response?.data ?? error?.message,
        context: `VOICE TOOL | ADD TAG`,
      })
      res.status(HttpStatus.CREATED).json(response)
    }
  }

  @Post('custom-field/:agentId/:actionId')
  async addCustomField(@Param('agentId') agentId: string, @Param('actionId') actionId: string, @Req() req: Request, @Res() res: Response, @Body() body: any) {
    try {
      this.logger.log({
        context: JSON.stringify(body),
        message: `VOICE TOOL | GHL ADD CUSTOM FIELD`,
      })

      const agent = await this.mongoAgentService.getAgent({_id: agentId});
      
      const tool = (agent?.voiceConfig?.tools || []).find(
        (tool) => tool.actionId === actionId
      );
      if (!tool?.active) {
        throw new Error('Tool not active');
      }

      const {isWebCall, query, assistantId, conversations, contactDetails, contactId, toolCallId} = await this.toolService.extractToolProperties(agent, body)
      if (!isWebCall) {
        const r = await this.ghlToolService.evaluateCustomField({ ...body, assistantId, actionId, agent, contactId, conversations, query, contactDetails });
        const response = await this.toolService.createToolResponse({
          result: 'custom field added', toolCallId, funcName: actionId,
        });
        this.logger.log({
          message: `custom field added | data - ${JSON.stringify(r)}`,
          context: `VOICE TOOL | GHL ADD CUSTOM FIELD`,
        })
        return res.status(HttpStatus.CREATED).json(response);
      } else {
        this.logger.log({
          message: `Webcall detected -> not performing add custom field action | toolcallId - ${toolCallId}`,
          context: `VOICE TOOL | GHL ADD CUSTOM FIELD`,
        })
        res.status(HttpStatus.CREATED).json('ok');
      }
    } catch (error) {
      const response = await this.toolService.createToolResponse({
        result: '', toolCallId: '', funcName:'', errorMessage: 'Failed to add custom field'
      });
      this.logger.error({
        message: error?.response?.data ?? error?.message,
        context: `VOICE TOOL | GHL ADD CUSTOM FIELD`,
      })
      res.status(HttpStatus.CREATED).json(response)
    }
  }
}
