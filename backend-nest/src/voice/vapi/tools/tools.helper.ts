import { KINDS, PROVIDERS } from "src/lib/constant";

export const createToolResponseBody = ({
    error, funcName, toolCallId, systemMessage, assistantMessage, type
}: {
    error?: string;
    funcName: string;
    toolCallId: string;
    systemMessage?: string;
    assistantMessage?: string;
    type?: "request-complete" | "request-failed";
}) => {
    return {
        messageResponse: {
            error,
            results: [{
                name: funcName,
                toolCallId,
                message: systemMessage?? assistantMessage ? [{
                    type,
                    content: systemMessage ?? assistantMessage,
                    role: assistantMessage ? 'assistant': 'system',
                }]: [],
            }],
        }
    }
}

export const AiProviderMaps = {
    [PROVIDERS.OPENAI_PROVIDER] : KINDS.OPENAI_CREDENTIAL,
    [PROVIDERS.CLAUDE_PROVIDER] : KINDS.CLAUDE_CREDENTIAL,
    [PROVIDERS.GROQ_PROVIDER] : KINDS.GROQ_CREDENTIAL,
    [PROVIDERS.FIREWORKS_PROVIDER] : KINDS.FIREWORKS_CREDENTIAL,
    [PROVIDERS.GOOGLE_GEMINI] : KINDS.GEMINI_CREDENTIAL,
}