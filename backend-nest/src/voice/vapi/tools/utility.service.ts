import { BadRequestException, Injectable } from '@nestjs/common';
import { CUSTOM_LLM, KINDS, PROVIDERS } from 'src/lib/constant';
import { MyLogger } from 'src/logger/logger.service';
import { MongoCredentialsService } from 'src/mongo/service/credentials/mongo-credentials.service';
import { getApiKeyAndProvider } from 'src/session/helpers/getApiKeyAndEmbeddingProvider';
import { getCredentials } from 'src/session/helpers/getCredentials';
import { VectorService } from 'src/vector/services/vector.service';

@Injectable()
export class UtilityService {
  constructor(
    private readonly logger: MyLogger,
    private readonly vectorService: VectorService,
    private readonly mongoCredentialService: MongoCredentialsService,
  ) {}

  //   const modelType =   agentData.aiProvider.modelName
  //   let companyId = agentData.aiProvider.companyId
  async createEmbedding(
    companyId: string,
    modelType: string,
    credentialId: string,
    query,
  ) {
    let credentialData;
    
    let secretKey = await getCredentials(companyId, credentialId, this.mongoCredentialService, this.logger, null);
    
    const model_Obj: {
      companyId: string;
      model_name: string;
      key: string;
    } = {
      companyId: companyId,
      model_name: modelType,
      key: secretKey,
    };

    let apiKey, embeddingProvider;
    if (companyId === PROVIDERS.OPENAI_PROVIDER) {
      apiKey = secretKey;
      embeddingProvider = PROVIDERS.OPENAI_PROVIDER;
    } else {
      ({ apiKey, embeddingProvider } = getApiKeyAndProvider({
        companyId: companyId,
      }));
    }
    const e = await this.vectorService.createEmbedding(
      embeddingProvider,
      apiKey,
      query,
    );

    const embedding = e[0].embedding;

    return embedding;
  }
}
