import {
    Controller,
    Get,
    Post,
    Req,
    Res,
    Body,
    HttpStatus,
    Render,
    Delete,
    Param,
    Query,
    UseGuards,
    Patch,
  } from '@nestjs/common';
  import { Request, Response } from 'express';
import { ToolsService } from './tools.service';
import { AgentService } from 'src/agent/agent.service';
import { handleException } from 'helpers/handleException';
import { MyLogger } from 'src/logger/logger.service';


@Controller('tools')
export class ToolsController {

  constructor(
    private readonly toolservice: ToolsService,
    private readonly agentService: AgentService,
    private readonly logger: MyLogger,
  ) {}

  @Post('add')
  async addTool(@Req() req: Request, @Res() res: Response, @Body() body: any) {
    try {
      await this.agentService.addDatasources({
        action: body.action,
        agentId: body.agentId,
        isTool: true
      })
      return res.status(HttpStatus.CREATED).json({
        message: `Tool added successfully`
      })
    } catch (error) {
      handleException(res, error) 
    }
  }

  @Patch('update/:toolId')
  async updateTool(@Param('toolId') toolId: string, @Req() req: Request, @Res() res: Response, @Body() body: any) {
    try {
      await this.agentService.editDatasources({
        action: body.action,
        agentId: body.agentId,
        actionId: toolId,
        isTool: true
      }, res)
      return res.status(HttpStatus.CREATED).json({
        message: `Tool updated successfully`
      })
    } catch (error) {
      handleException(res, error) 
    }
  }

  @Delete('delete/:toolId')
  async deleteTool(@Param('toolId') toolId: string, @Req() req: Request, @Res() res: Response, @Body() body: any) {
    try {
      await this.agentService.deleteAction(
        body.agentId,
        toolId,
        res,
        true
      )
      return res.status(HttpStatus.CREATED).json({
        message: `Tool added successfully`
      })
    } catch (error) {
      handleException(res, error) 
    }
  }

  @Patch('status/:agentId')
  async updateVoiceToolStatus(
    @Param('agentId') agentId: string,
    @Body() b,
    @Res() response: Response
  ){
    try {
      const updatedAgent = await this.agentService.updateVapiToolStatus({
        toolId: b.toolId, agentId, active: b.active
      });
      response.status(HttpStatus.OK).json({message: "Action updated successfully!"});
    } catch (error) {
      this.logger.error({
        message: `VOICE | encountered while updating voice tool status : ${error.message} | ${JSON.stringify(error.stack)}`,
      })
      handleException(response, error);
    }
  }
}