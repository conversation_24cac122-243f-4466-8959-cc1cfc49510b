import {
    Controller, Get, Post, Req, Res, Body, HttpStatus, Render, Delete, Param, Query, UseGuards, Patch,
  } from '@nestjs/common';
import { Request, Response } from 'express';
import { handleException } from 'helpers/handleException';
import { SiteToolService } from './site-tool.service';

@Controller('site-tool')
export class SiteController {
    constructor(
        private readonly siteToolService: SiteToolService,
    ) {}

    @Post('query/:agentId')
    async makeQuery(@Param('agentId') agentId: string, @Req() req: Request, @Res() res: Response, @Body() body: any) {
        try {
            return await this.siteToolService.handleSiteAction(body)
        } catch (error) {
            handleException(res, error) 
        }
    }
}
