import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { MyLogger } from 'src/logger/logger.service';
import { vapiApiService } from 'src/api-client/services/vapi/vapi-api.service';
import { MongoAgentService } from 'src/mongo/service/agent/mongo-agent.service';
import { AssistantsService } from '../assistants/assistants.service';

@Injectable()
export class KnowledgeService {
    constructor(
        private readonly vapiApiService: vapiApiService,
        private readonly logger: MyLogger,
        private readonly mongoAgentService: MongoAgentService,
        private readonly assistantService: AssistantsService,
    ){}

    async uploadVapiFile({
        fileContent, fileName, agentId
    }: {
        fileName: string,
        fileContent: string,
        agentId: string,
    }){
        const agent = await this.mongoAgentService.getAgent({ _id: agentId });
        if (agent?.voiceConfig?.assistantId && agent?.voiceConfig?.enabled) {
            this.logger.log({
              message: `Processing VAPI file for ${agentId} and file name ${fileName}`,
              context: `VOICE | UPLOAD VAPI FILE`,
            });
            const vapiFile = await this.vapiApiService.uploadFile(process.env.VAPI_TOKEN, { fileName, textContent: fileContent });
            const updatedAgent = await this.mongoAgentService.updateAgent({ _id: agentId }, {
                $push: {
                    'voiceConfig.files': {
                        provider: 'vapi',
                        providerFileId: vapiFile.id
                    }
                }
            },{
                new: true
            });
            const { actions, toolIds, tools, vapiPrompt } = await this.assistantService.createVapiSystemPrompt(updatedAgent);
            const fileIds = (updatedAgent?.voiceConfig?.files ??[]).map(file => file?.providerFileId);
    
            await this.assistantService.updateVapiAssistant({
                agentId: updatedAgent?._id?.toString(),
                assistantId: updatedAgent?.voiceConfig.assistantId,
                mainPrompt: vapiPrompt,
                toolIds,
                model: {
                    model: updatedAgent?.voiceConfig?.model?.model,
                    provider: updatedAgent?.voiceConfig?.model?.provider,
                },
                knowledgeBase: {
                    provider: 'canonical',
                    fileIds,
                }
            });
            if (!vapiFile?.id) {
              this.logger.error({
                message: `Error uploading vapi file for ${agentId} and file name ${fileName}`,
                context: `VOICE | UPLOAD VAPI FILE`,
              });
            }
            return {
                vapiFileId: vapiFile.id,
            }
        } else {
            this.logger.error({
              message: `No active voice assistant found for ${agentId} and file name ${fileName}`,
              context: `VOICE | UPLOAD VAPI FILE`,
            });
            return {
                vapiFileId: undefined
            }
        }
    }

    async deleteVapiFile(id: string, agentId: string){
        const f = await this.vapiApiService.deleteFile(process.env.VAPI_TOKEN, id);
        if (f?.id) {
            const a = await this.mongoAgentService.updateAgent({ _id: agentId }, {
                $pull: {
                    'voiceConfig.files': {
                        providerFileId: id
                    }
                }
            },{
                new: true
            });
            this.logger.log({
              message: `VAPI file deleted for ${agentId} and file id ${id}`,
              context: `VOICE | DELETE VAPI FILE`,
            })
            return a;
        } else {
            throw new BadRequestException('File not deleted. Please try again');
        }
    }
}
