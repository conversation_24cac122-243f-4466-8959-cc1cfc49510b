import {
    Controller,
    Get,
    Post,
    Req,
    Res,
    Body,
    HttpStatus,
    Render,
    Delete,
    Param,
    Query,
    UseGuards,
    Patch,
    UploadedFile,
    UseInterceptors,
} from '@nestjs/common';
import { Request, Response } from 'express';
import {
ApiOperation,
ApiQuery,
ApiNotFoundResponse,
ApiOkResponse,
ApiParam,
} from '@nestjs/swagger';
import { MyLogger } from 'src/logger/logger.service';
import { handleException } from 'helpers/handleException';
import { KnowledgeService } from './knowledge.service';
import { vapiApiService } from 'src/api-client/services/vapi/vapi-api.service';
import { FileInterceptor } from '@nestjs/platform-express';

@Controller('voice/knowledge')
export class KnowledgeController {
    constructor(
        private readonly myLogger: MyLogger,
        private readonly knowledgeService: KnowledgeService,
    ){}

    // @Post('upload/:agentId')
    // @UseInterceptors(FileInterceptor('file'))
    // public async uploadKnowledge(@UploadedFile() file, @Param('agentId') agentId: string, @Req() req: Request, @Res() res: Response) {
    //     try {
    //         const a = await this.knowledgeService.uploadVapiFile({agentId, file});
    //         return res.status(HttpStatus.OK).json({
    //             message: 'file uploaded successfully',
    //             data: a
    //         });
    //     } catch (error) {
    //         this.myLogger.error({
    //             message: error?.response?.data?.message ?? error?.message ?? `failed to upload file`,
    //             context: 'VOICE | UPLOAD FILE',
    //         });
    //         handleException(res, error);
    //     }
    // }

    @Delete('file/:agentId/:id')
    async deleteFile(@Param('id') id: string, @Param('agentId') agentId: string, @Req() req: Request, @Res() res: Response) {
        try {
            const a = await this.knowledgeService.deleteVapiFile(id, agentId);
            return res.status(HttpStatus.OK).json({
                message: 'file deleted successfully',
            });
        } catch (error) {
            this.myLogger.error({
                message: error?.message ?? `failed to delete file`,
                context: 'VOICE | DELETE FILE',
            });
            handleException(res, error);
        }
    }
}
