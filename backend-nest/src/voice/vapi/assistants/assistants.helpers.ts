import { PROVIDERS } from 'src/lib/constant';
import { assistantDto } from './assistants.dto';

const getGoogleCalendarSubRoute = (action) => {
  switch (action) {
    case 'read':
      return 'availability';
    case 'write':
      return 'appointment/create';
    case 'events':
      return 'events';
    default:
      return 'events';
  }
}

export const createActionToolProperty = ({
  provider, 
  type,
  agentId,
  prompt = null,
  actionId
}) => {
  if (provider === PROVIDERS.GHL_CALENDAR) {
    return {
      properties: {
        date: {
          type: 'string',
          description:
            'The date and time of the event in ISO 8601 format as determined from the users message, e.g. 2022-01-01T00:00:00Z',
        },
      },
      required: ['date'],
      serverUrl: `${process.env.BACKEND_URL}/ghl-tool/${
        type === 'read' ? 'availability' : 'appointment/create'
      }/${agentId}/${actionId}`,
      additionalPrompt: '',
      async: false,
      funcName: `ghl_calendar_${type === 'read' ? 'get_availability': 'create_appointment'}_tool`,
    };
  } /*  else if (provider === PROVIDERS.GOOGLE_SHEET){
    return {
      properties: {
        query: {
          type: 'string',
          description: 'What info the user is asking about, in the current query',
        }
      },
      required: ['query'],
      serverUrl : `${process.env.BACKEND_URL}/google-sheet-tool/query/${agentId}`,
      additionalPrompt: ''
    }
  } else if (provider === PROVIDERS.GOOGLE_DOCS){
    return {
      properties: {
        query: {
          type: 'string',
          description: 'What info the user is asking about, in the current query',
        }
      },
      required: ['query'],
      serverUrl : `${process.env.BACKEND_URL}/google-docs-tool/query/${agentId}`,
      additionalPrompt: ''
    }
  } else if (provider === PROVIDERS.WEBSITE){
    return {
      properties: {
        query: {
          type: 'string',
          description: 'What info the user is asking about, in the current query',
        }
      },
      required: ['query'],
      serverUrl : `${process.env.BACKEND_URL}/site-tool/query/${agentId}`,
      additionalPrompt: ''
    }
  } */ else if (provider === PROVIDERS.GOOGLE_CALENDAR) {
    return {
      properties: {
        fullDateString: {
          type: 'string',
          description:
            'The start time of the discussed event in ISO 8601 format, e.g. 2022-01-01T00:00:00Z',
        },
        summary: {
          type: 'string',
          description:
            'The summary of the reason behind booking the appointment',
        },
      },
      required: ['summary', 'fullDateString'],
      serverUrl: `${process.env.BACKEND_URL}/google-calendar-tool/${
        getGoogleCalendarSubRoute(type)
      }/${agentId}/${actionId}`,
      additionalPrompt: '',
      async: false,
      funcName: `google_calendar_${type === 'read' ? 'get_availability': 'create_appointment'}_tool`
    };
  } else if (provider === PROVIDERS.GHL_CHANNEL) {
    if (type === 'customField'){
      return {
        properties: {
          value: {
            type: 'string',
            description: `the value of ${prompt} evaluated from the current conversation. Only fill with the value and nothing else` || 'The value of the field that is determined from the conversation',
          }
        },
        required: ['value'],
        serverUrl: `${process.env.BACKEND_URL}/ghl-tool/custom-field/${agentId}/${actionId}`,
        additionalPrompt: '',
        async: true,
        funcName: `ghl_custom_field`
      };
    }
    if (type === 'tag'){
      return {
        properties: {
          value: {
            type: 'string',
            description: `use the value 'tag'`,
          }
        },
        required: ['value'],
        serverUrl: `${process.env.BACKEND_URL}/ghl-tool/tag/${agentId}/${actionId}`,
        additionalPrompt: '',
        async: true,
        funcName: `ghl_tag`
      };
    }
  }
  /* else if (provider === PROVIDERS.GET){
    return {
      properties: {
        query: {
          type: 'string',
          description: 'What info the user is asking about, in the current query',
        }
      },
      required: ['query'],
      serverUrl : `${process.env.BACKEND_URL}/http-get-tool/request/${agentId}`,
      additionalPrompt: ''
    }
  } */
  return {
    properties: null,
    required: null,
    serverUrl: null,
    additionalPrompt: '',
    async: false,
    funcName: ''
  };
}

export const buildCreateAssistantBody = ({
  name,
  model,
  transcriber,
  voice,
  firstMessageMode,
  firstMessage,
  serverMessages = [
    'end-of-call-report',
    'function-call',
    // "hang",
    // "speech-update",
    // "status-update",
    'tool-calls',
    'transfer-destination-request',
  ],
  metadata: { agentId },
  serverUrl,
  backchannelingEnabled,
  backgroundDenoisingEnabled,
  silenceTimeoutSeconds,
  backgroundSound
}: Partial<assistantDto>) => {
  return {
    transcriber: {
      provider: transcriber?.provider ?? 'deepgram',
      model: transcriber?.model ?? 'nova-2',
      language: transcriber?.language ?? 'en',
      smartFormat: true,
      keywords: transcriber?.keywords ?? [],
    },
    model: {
      messages: model?.messages ?? [],
      toolIds: model?.toolIds ?? [],
      provider: model?.provider ?? 'openai',
      model: model?.model ?? 'gpt-4o',
      temperature: 1,
      maxTokens: 525,
      emotionRecognitionEnabled: true,
    },
    voice: {
      inputPreprocessingEnabled: true,
      inputReformattingEnabled: true,
      inputMinCharacters: 30,
      inputPunctuationBoundaries: [
        '。',
        '，',
        '.',
        '!',
        '?',
        ';',
        ')',
        '،',
        '۔',
        '।',
        '॥',
        '|',
        '||',
        ',',
        ':',
      ],
      fillerInjectionEnabled: voice?.fillerInjectionEnabled ?? false,
      provider: voice?.provider ?? '11labs',
      voiceId: voice?.voiceId ?? 'sarah',
      stability: voice?.stability,
      style: voice?.style,
      similarityBoost: voice?.similarityBoost,
      optimizeStreamingLatency: voice?.optimizeStreamingLatency,
      useSpeakerBoost: voice?.useSpeakerBoost,
    },
    firstMessageMode: firstMessageMode,
    serverMessages,
    backgroundDenoisingEnabled,
    backchannelingEnabled,
    silenceTimeoutSeconds,
    backgroundSound,
    modelOutputInMessagesEnabled: true,
    transportConfigurations: [
      // {
      //   provider: 'twilio',
      //   timeout: 60,
      //   record: false, // todo - user input
      //   recordingChannels: 'mono',
      // },
    ],
    name,
    firstMessage,
    metadata: {
      agentId,
    },
    serverUrl,
    // "messagePlan": {
    //     "idleMessages": [
    //     "<string>"// todo - user input
    //     ],
    //     "idleMessageMaxSpokenCount": 5.5,
    //     "idleTimeoutSeconds": 17.5
    // }
  };
};
