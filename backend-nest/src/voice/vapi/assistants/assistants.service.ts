import { BadRequestException, Injectable } from '@nestjs/common';
import { vapiApiService } from 'src/api-client/services/vapi/vapi-api.service';
import { createAssistantDto, updateAssistantDto } from './assistants.dto';
import { MyLogger } from 'src/logger/logger.service';
import { MongoAgentService } from 'src/mongo/service/agent/mongo-agent.service';
import { buildCreateAssistantBody, createActionToolProperty } from './assistants.helpers';
import { ToolsService } from '../tools/tools.service';
import { ObjectId } from 'mongodb';
import { v4 as uuidv4 } from 'uuid';
import { getCustomFieldSubstring } from 'src/lib/utils';

@Injectable()
export class AssistantsService {
    constructor(
      private readonly vapiApiService: vapiApiService, 
      private readonly logger: MyLogger,
      private readonly mongoAgentService: MongoAgentService,
      private readonly toolsService: ToolsService,
    ) {}

    async createVapiAssistant(b:createAssistantDto) {
        const agent = await this.mongoAgentService.getAgent({ _id: new ObjectId(b.agentId) });
        // build system prompt
        const {actions, toolIds, tools, vapiPrompt} = await this.createVapiSystemPrompt(agent);
        this.logger.log({
          message: `Vapi tools created | agentId ${
            b.agentId
          } | toolIds - ${JSON.stringify(toolIds)}`,
          context: 'VOICE | CREATE VAPI ASSISTANT',
        });
        const serverUrl = `${process.env.BACKEND_URL}/voice/webhooks/end-of-call-report/${agent._id.toString()}`;
        // create assistant
        const assistantBody = buildCreateAssistantBody({
          name: agent.agentName,
          firstMessage: b.firstMessage,
          firstMessageMode: b.firstMessageMode,
          metadata: {
            agentId: b.agentId,
          },
          serverUrl,
          model: {
            messages: [{
              content: vapiPrompt,
              role: 'system',
            }],
            toolIds,
            provider: b?.model?.provider ?? 'openai',
            model: b?.model?.model ?? 'gpt-4o',
          },
          voice: b.voice,
          backchannelingEnabled: b.backchannelingEnabled,
          backgroundDenoisingEnabled: b.backgroundDenoisingEnabled,
          silenceTimeoutSeconds: b.silenceTimeoutSeconds,
          backgroundSound: b.backgroundSound ?? "office"// off, office 
        });
        const assistant = await this.vapiApiService.createAssistant(
          process.env.VAPI_TOKEN,
          {
            body: assistantBody,
          },
        );
        this.logger.log({
          message: `Vapi assistant created successfully | assistantId ${assistant?.id} | agentId ${b.agentId}`,
          context: 'VOICE | CREATE VAPI ASSISTANT',
        });
        const newAgent = await this.mongoAgentService.updateAgent({
          _id: b.agentId,
        }, {
          $set: {
            actions: actions,
            voiceConfig: {
              tools,
              assistantId: assistant.id,
              provider: 'vapi',
              firstMessage: b.firstMessage,
              firstMessageMode: b.firstMessageMode,
              enabled: true,
              model: b.model,
            }
          },
        })
        return {
          assistantId: assistant?.id,
          ...b,
          tools,
        }
    }

    async getVapiAssistant({agent, agentId}:{agent?, agentId?: string}) {
      try {
        // * completed
        const a = agent ? agent : await this.mongoAgentService.getAgent({ _id: agentId });
        const assistantId = a?.voiceConfig?.assistantId;
        if (!assistantId) {
          this.logger.log({
            message: `No linked voice assistant found`,
            context: 'UPDATE AGENT | VOICE'
          })
          return;
        }
        const assistant = await this.vapiApiService.getAssistant(process.env.VAPI_TOKEN, { assistantId });
        if (assistant){
          this.logger.log({
            message: `Vapi assistant fetched successfully`,
            context: 'VOICE | GET VAPI ASSISTANT'
          })
        }
        return assistant;
      } catch (error) {
        this.logger.error({
          message: `Error fetching vapi assistant : ${error?.response?.data?.message ?? error?.message}`,
          context: 'VOICE | GET VAPI ASSISTANT'
        })
        return undefined;
      }
    }

    async updateVapiAssistant(updateParams:updateAssistantDto ){
      try {
        // * completed
        const agent = updateParams?.assistantId ? await this.mongoAgentService.getAgent({ _id: updateParams.agentId }): undefined;
        const assistantId = agent?.voiceConfig?.assistantId ?? updateParams?.assistantId;
        if (!assistantId) {
          this.logger.log({
            message: `No linked voice assistant found`,
            context: 'UPDATE AGENT | VOICE'
          })
          return;
        }
        const updateBody = {}
        if (updateParams?.firstMessageMode) updateBody['firstMessageMode'] = updateParams?.firstMessageMode
        if (updateParams?.firstMessage) updateBody['firstMessage'] = updateParams?.firstMessage
        if (updateParams?.name) updateBody['name'] = updateParams?.name
        const modelUpdate = {
          messages: [{
            content: updateParams?.mainPrompt,
            role: 'system',
          }],
          provider: updateParams.model.provider,
          model: updateParams.model.model,
          toolIds: updateParams?.toolIds,
        }
        if (updateParams?.knowledgeBase) modelUpdate['knowledgeBase'] = updateParams?.knowledgeBase;
        if (updateParams?.mainPrompt) updateBody['model'] = modelUpdate;
        if (updateParams.voice) updateBody['voice'] = updateParams?.voice;
        if (updateParams?.backchannelingEnabled) updateBody['backchannelingEnabled'] = updateParams?.backchannelingEnabled;
        if (updateParams?.backgroundDenoisingEnabled) updateBody['backgroundDenoisingEnabled'] = updateParams?.backgroundDenoisingEnabled;
        if (updateParams?.silenceTimeoutSeconds) updateBody['silenceTimeoutSeconds'] = updateParams?.silenceTimeoutSeconds;
        if (updateParams?.backgroundSound) updateBody['backgroundSound'] = updateParams?.backgroundSound;

        if (Object.keys(updateBody).length > 0) {
          const assistant = await this.vapiApiService.updateAssistant(process.env.VAPI_TOKEN, {
            body: updateBody,
            assistantId,
          });
          this.logger.log({
            message: `Vapi assistant updated successfully | assistantId ${assistantId}`,
            context: `VOICE | UPDATE VAPI ASSISTANT`
          })
          return assistant;
        } else {
          this.logger.log({
            message: `No updates to make | assistantId ${assistantId}`,
            context: `VOICE | UPDATE VAPI ASSISTANT`
          })
          return;
        }
      } catch (error) {
        this.logger.error({
          message: `Error updating vapi assistant : ${error?.response?.data?.message ?? error?.message}`,
          context: `VOICE | UPDATE VAPI ASSISTANT`
        })
      }
    }

    async deleteVapiAssistant({assistantId, toolIds}:{assistantId: string, toolIds: string[]}) {
        // * completed
        try {
          const assistant = await this.vapiApiService.deleteAssistant(process.env.VAPI_TOKEN, { assistantId });
          toolIds = toolIds.filter((toolId) => toolId !== undefined);
          const deletePromises = toolIds.map((t)=> this.toolsService.deleteTool({ toolId: t }));
          const r = await Promise.all(deletePromises);
          const toolError = r.map(t => t?.error);
          this.logger.log({
            message: `Vapi assistant deleted successfully | result assistantId ${assistantId} ${toolError.length > 0 && `| tool call errors - ${JSON.stringify(toolError)}`}`,
            context: `VOICE | DELETE VAPI ASSISTANT`
          })
          return assistant;
        } catch (error) {
          this.logger.error({
            message: `Error deleting vapi assistant : ${error?.response?.data?.message ?? error?.message}`,
            context: `VOICE | DELETE VAPI ASSISTANT`
          })
        }
    }

    async updateVapiAssistantPrompt({agentId, assistantId, prompt}){
      const assistant = await this.vapiApiService.getAssistant(process.env.VAPI_TOKEN, { assistantId });
      let sysPrompt = assistant?.model?.messages?.[0]?.content ?? '';
      sysPrompt = sysPrompt.replace(/^.*(?=\n\n--tools--)/, `${prompt} \n`);
      
      await this.updateVapiAssistant({
        assistantId, 
        agentId,
        mainPrompt: sysPrompt
      })
    }

    async constructToolsPrompt({
      agent, agentId
    }){
      let vapiToolsPrompt = '', toolId = '';

      const actions = agent?.actions ?? [];
      let index = 0, toolIds = [], tools= [];
      for (const action of actions) {
        // get the tool properties, serverUrl from actions
        const { properties=null, required=null, serverUrl=null, additionalPrompt='', async=false, funcName='' } = createActionToolProperty({
          provider: action?.providerName,
          type: action.activity,
          agentId: agentId,
          prompt: getCustomFieldSubstring(action?.metaData?.customFieldData?.fieldKey ?? ''),
          actionId: action.actionId
        })
        if (serverUrl && !action?.voice?.vapiToolId) {
          const t = await this.toolsService.createTool({
            type: 'function',
            name: action.actionId,
            description: action.promptContent + '\n' + additionalPrompt,
            properties: properties,
            required: required,
            serverUrl,
            async
          });
          toolId = t?.id;
          actions[index]['voice'] = { vapiToolId: t?.id };
        } else if (serverUrl && action?.voice?.vapiToolId) {
          toolId = action?.voice?.vapiToolId;
          actions[index]['voice'] = { vapiToolId: action?.voice?.vapiToolId };
        }
        if (toolId && serverUrl) {
          vapiToolsPrompt += `\n- ${action.actionId} : \n${action.promptContent}\n\n`;
          toolIds.push(toolId);
          tools.push({
            active: true,
            actionId: action.actionId,
            accountId: action.accountId,
            providerName: action.providerName,
            activity: action.activity,
            accountName: action.accountName,
            toolId: toolId
          });
          toolId = '';
        }
        index++;
      };
      const transferCallPrompt = agent?.voiceConfig?.transferCall?.transferCallPrompt;
      vapiToolsPrompt += `---tools---\n\nPlease note this additional guidelines -\n1. The current date and time is {{now}}`;
      if (agent?.voiceConfig?.transferCall?.transferCallEnabled && transferCallPrompt){
        vapiToolsPrompt += `\n- Please Call the transferCall function with the phone number ${agent?.voiceConfig?.transferCall?.transferCallNumber} if the following condition is satisfied - <transfer_call_condition> ${transferCallPrompt} </transfer_call_condition>`;
        toolIds.push(agent?.voiceConfig?.transferCall?.transferCallToolId);
      }
      return { vapiToolsPrompt, toolIds, tools, actions };
    }

    async createVapiSystemPrompt(agent){
      const currentActivePrompt =
          (agent?.prompts?.prompt ?? []).find(
            (p) => p.promptId === agent?.prompts?.currentActive,
          )?.promptContent ?? '';
        if (currentActivePrompt === '')
          throw new BadRequestException('No active prompt found for the agent');

        let vapiPrompt = `${currentActivePrompt}`;
        
        // create tools
        const { vapiToolsPrompt, toolIds, tools, actions } = await this.constructToolsPrompt({
          agentId: agent._id.toString(),
          agent
        })      

        if (vapiToolsPrompt) vapiPrompt = `${vapiPrompt}\n\n---tools---\n\n${vapiToolsPrompt}`;
        if (toolIds.length > 0){
          await this.mongoAgentService.updateAgent({ _id: agent._id.toString() }, {
            $set: {
              'actions': actions,
              'voiceConfig.tools': tools  
            }
          })
        }
        return { vapiPrompt, toolIds, tools, actions };
    }

    async revertVoiceAssistant(agentId){
      const deletedAgent = await this.mongoAgentService.updateAgent({ _id: agentId }, {
        $unset: {
          'voiceConfig': ""
        }
      }, { new: false});
      const toolIds = deletedAgent.actions.map(
        (action) => action?.voice?.vapiToolId,
      );
      if (deletedAgent?.voiceConfig?.assistantId) {
        await this.deleteVapiAssistant({
          assistantId: deletedAgent?.voiceConfig?.assistantId,
          toolIds,
        });
      }
      // remove a field from the agent.actions[].voice.vapiToolId
      await this.mongoAgentService.updateAgent({ _id: agentId },{ $unset: { "actions.$[].voice.vapiToolId": "" } })

      return deletedAgent
    }
}
