export type UpdateAssistantDto = {
    "transcriber": {
      "provider": "deepgram",
      "model": "nova-2",
      "language": "bg",
      "smartFormat": boolean,
      "keywords": string[]
    },
    "model": {
      "messages"?: {
        "content":string,
        "role": string
      }[],
      "toolIds"?: string[
       
      ],
      "provider": string,
      "model":string,
      "temperature"?: 1,
      "maxTokens"?: 525,
      "emotionRecognitionEnabled"?: boolean
    },
    "voice": {
      "inputPreprocessingEnabled": boolean,
      "inputReformattingEnabled": boolean,
      "inputMinCharacters": 30,
      "inputPunctuationBoundaries": [
        "。",
        "，",
        ".",
        "!",
        "?",
        ";",
        ")",
        "،",
        "۔",
        "।",
        "॥",
        "|",
        "||",
        ",",
        ":"
      ],
      "fillerInjectionEnabled": boolean,
      "provider": "azure",
      "voiceId": "andrew",
      "speed": 1.25
    },
    "firstMessageMode": "assistant-speaks-first" | "assistant-speaks-first-with-model-generated-message" | "assistant-waits-for-user",
    "recordingEnabled": false,
    "serverMessages": [
      "conversation-update",
      "end-of-call-report",
      "function-call",
      "hang",
      "speech-update",
      "status-update",
      "tool-calls",
      "transfer-destination-request",
      "user-interrupted"
    ],
    "silenceTimeoutSeconds": 30,
    "responseDelaySeconds": 0.4,
    "llmRequestDelaySeconds": 0.1,
    "llmRequestNonPunctuatedDelaySeconds": 1.5,
    "numWordsToInterruptAssistant": 5,
    "maxDurationSeconds": 1800,
    "backgroundSound": "office",
    "backchannelingEnabled": boolean,
    "backgroundDenoisingEnabled": boolean,
    "modelOutputInMessagesEnabled": boolean,
    "transportConfigurations": [
      {
        "provider": "twilio",
        "timeout": 60,
        "record": boolean,
        "recordingChannels": "mono"
      }
    ],
    "name":string,
    "firstMessage":string,
    "voicemailDetection": {
      "provider": "twilio",
      "voicemailDetectionTypes": [
        "machine_end_beep",
        "machine_end_silence"
      ],
      "enabled": boolean,
      "machineDetectionTimeout": 31,
      "machineDetectionSpeechThreshold": 3500,
      "machineDetectionSpeechEndThreshold": 2750,
      "machineDetectionSilenceTimeout": 6000
    },
    "voicemailMessage":string,
    "endCallMessage":string,
    "endCallPhrases": [
     string
    ],
    "metadata": {},
    "serverUrl":string,
    "serverUrlSecret":string,
    "analysisPlan": {
      "summaryPrompt":string,
      "summaryRequestTimeoutSeconds": 10.5,
      "structuredDataRequestTimeoutSeconds": 10.5,
      "successEvaluationPrompt":string,
      "successEvaluationRubric": "NumericScale",
      "successEvaluationRequestTimeoutSeconds": 10.5,
      "structuredDataPrompt":string,
      "structuredDataSchema": {
        "type": "string",
        "items": {},
        "properties": {},
        "description":string,
        "required": [
         string
        ]
      }
    },
    "artifactPlan": {
      "videoRecordingEnabled": boolean
    },
    "messagePlan": {
      "idleMessages": [
       string
      ],
      "idleMessageMaxSpokenCount": 5.5,
      "idleTimeoutSeconds": 17.5
    }
}