export interface assistantDto {
    transcriber: {
      provider: string;
      model: string;
      language: string;
      smartFormat: boolean;
      keywords: string[];
    };
    model: {
      messages?: {
        content: string;
        role: string;
      }[];
      tools?: {
        async: boolean;
        messages: {
          type: string;
          content: string;
          conditions: {
            param: string;
            value: string;
            operator: string;
          }[];
        }[];
        type: string;
        function: {
          name: string;
          description: string;
          parameters: {
            type: string;
            properties: Record<string, unknown>;
            required: string[];
          };
        };
        server: {
          timeoutSeconds: number;
          url: string;
          secret: string;
        };
      }[];
      toolIds?: string[];
      provider: string;
      model: string;
      temperature?: number;
      knowledgeBase?: {
        provider?: string;
        topK?: number;
        fileIds?: string[];
      };
      maxTokens?: number;
      emotionRecognitionEnabled?: boolean;
    };
    voice?: {
      fillerInjectionEnabled?: boolean;
      provider?: string;
      voiceId?: string;
      stability?: number;
      similarityBoost?: number;
      style?: number;
      optimizeStreamingLatency?: number;
      useSpeakerBoost?: boolean;
    };
    firstMessageMode: "assistant-speaks-first" | "assistant-speaks-first-with-model-generated-message" | "assistant-waits-for-user",
    clientMessages: string[];
    serverMessages: string[];
    silenceTimeoutSeconds: number;
    responseDelaySeconds: number;
    llmRequestDelaySeconds: number;
    llmRequestNonPunctuatedDelaySeconds: number;
    numWordsToInterruptAssistant: number;
    maxDurationSeconds: number;
    backgroundSound: string;
    backchannelingEnabled: boolean;
    backgroundDenoisingEnabled: boolean;
    modelOutputInMessagesEnabled: boolean;
    transportConfigurations: {
      provider: string;
      timeout: number;
      record: boolean;
      recordingChannels: string;
    }[];
    name: string;
    firstMessage: string;
    voicemailDetection: {
      provider: string;
      voicemailDetectionTypes: string[];
      enabled: boolean;
      machineDetectionTimeout: number;
      machineDetectionSpeechThreshold: number;
      machineDetectionSpeechEndThreshold: number;
      machineDetectionSilenceTimeout: number;
    };
    metadata: Record<string, unknown>;
    serverUrl: string;
    serverUrlSecret: string;
    messagePlan: {
      idleMessages: string[];
      idleMessageMaxSpokenCount: number;
      idleTimeoutSeconds: number;
    };
  }
  
export type createAssistantDto = {
  agentId: string;
  firstMessage?: string;
  firstMessageMode?:
    | 'assistant-speaks-first'
    | 'assistant-speaks-first-with-model-generated-message'
    | 'assistant-waits-for-user';
  model: {
    model: string;
    provider: string;
  };
  voice?: {
    fillerInjectionEnabled?: boolean;
    provider?: string;
    voiceId?: string;
    stability?: number;
    similarityBoost?: number;
    style?: number;
    optimizeStreamingLatency?: number;
    useSpeakerBoost?: boolean;
  };
  backchannelingEnabled?: boolean;
  backgroundDenoisingEnabled?: boolean;
  silenceTimeoutSeconds?: number;
  backgroundSound?: 'off' | 'office';
};

export type voiceActivationDto = createAssistantDto;

export type updateAssistantDto = Partial<{
  name: string;
  mainPrompt: string;
  firstMessageMode:
    | 'assistant-speaks-first'
    | 'assistant-speaks-first-with-model-generated-message'
    | 'assistant-waits-for-user';
  firstMessage: string;
  tool: {
    actionType: 'added' | 'deleted' | 'updated';
    toolActionId: string;
  }[];
  model?: {
    model: string;
    provider: string;
  };
  assistantId?: string;
  toolIds: string[];
  voice?: {
    fillerInjectionEnabled?: boolean;
    provider?: string;
    voiceId?: string;
    stability?: number;
    similarityBoost?: number;
    style?: number;
    optimizeStreamingLatency?: number;
    useSpeakerBoost?: boolean;
  };
  backchannelingEnabled?: boolean;
  backgroundDenoisingEnabled?: boolean;
  silenceTimeoutSeconds?: number;
  backgroundSound?: 'off' | 'office';
  knowledgeBase?:any;
}> & { agentId: string };