import {
    <PERSON>,
    Get,
    Post,
    Req,
    <PERSON>s,
    Body,
    HttpStatus,
    Render,
    Delete,
    Param,
    Query,
    UseGuards,
    Patch,
  } from '@nestjs/common';
import { Request, Response } from 'express';
import { MyLogger } from 'src/logger/logger.service';
import { PhoneService } from './phone.service';
import { handleException } from 'helpers/handleException';


@Controller('voice/v1/phone')
export class PhoneController {
    constructor(
        private readonly logger: MyLogger,
        private readonly phoneService: PhoneService
    ) {}

    @Post('create')
    async createPhoneNumber(@Body() b: any, @Res() response: Response) {
        try {
            const newPhone = await this.phoneService.createPhoneNumber(b, b.orgId, b.agentId);
            return response.status(HttpStatus.CREATED).json(newPhone);
        } catch (error) {
            this.logger.error({
              message: error.message,
              context: 'VOICE | CREATE PHONE NUMBER',
            });
            return response.status(HttpStatus.BAD_REQUEST).json({
              error: error?.message ?? 'Unknown error. Please try again later',
            });
        }
    }

    @Get('get/:phoneId')
    async getPhoneNumber(@Param('phoneId') phoneId: string, @Res() response: Response) {
        try {
            const p = await this.phoneService.getPhoneNumber({phoneId});
            return response.status(HttpStatus.OK).json(p);
        } catch (error) {
            this.logger.error({
              message: error.message,
              context: 'VOICE | GET PHONE NUMBER',
            });
            handleException(response, error);
        }
    }

    @Delete('delete/:phoneId')
    async deletePhoneNumber(@Param('phoneId') phoneId: string, @Res() response: Response) {
        try {
            const p = await this.phoneService.deletePhoneNumber({phoneId});
            return response.status(HttpStatus.OK).json({message: 'deleted successsfully', phoneId});
        } catch (error) {
            this.logger.error({
              message: error.message,
              context: 'VOICE | DELETE PHONE NUMBER',
            });
            handleException(response, error);
        }
    }

    @Patch('update/:phoneId')
    async updatePhoneNumber(@Param('phoneId') phoneId: string, @Body() b: any, @Res() response: Response) {
        try {
            const p = await this.phoneService.updatePhoneNumber({phoneId, b});
            return response.status(HttpStatus.OK).json(p);
        } catch (error) {
            this.logger.error({
              message: error.message,
              context: 'VOICE | UPDATE PHONE NUMBER',
            });
            handleException(response, error);
        }
    }

    @Get('all/:agentId')
    async listAllPhoneNumbers(@Param('agentId') agentId: string, @Res() response: Response) {
        try {
            const p = await this.phoneService.listPhoneNumbers({agentId});
            return response.status(HttpStatus.OK).json(p);
        } catch (error) {
            this.logger.error({
              message: error.message,
              context: 'VOICE | LIST ALL PHONE NUMBERS',
            });
            handleException(response, error);
        }
    }
}
