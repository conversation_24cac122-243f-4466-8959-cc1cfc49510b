import { BadRequestException, Injectable } from '@nestjs/common';
import { vapiApiService } from 'src/api-client/services/vapi/vapi-api.service';
import {
  twilioPhoneNumberDto,
  vapiPhoneNumberDto,
  vonagePhoneNumberDto,
} from './phone.dto';
import { MyLogger } from 'src/logger/logger.service';
import { MongoVoiceNumbersService } from 'src/mongo/service/voiceNumbers/voiceNumbers.service';
import { MongoAgentService } from 'src/mongo/service/agent/mongo-agent.service';
import { AssistantsService } from '../assistants/assistants.service';
import { ToolsService } from '../tools/tools.service';

@Injectable()
export class PhoneService {
  constructor(
    private readonly vapiApiService: vapiApiService,
    private readonly logger: MyLogger,
    private readonly mongoVoiceNumberService: MongoVoiceNumbersService,
    private readonly mongoagentService: MongoAgentService,
    private readonly assistantService: AssistantsService,
    private readonly toolsService: ToolsService,
  ) {}

  async createPhoneNumber(
    b: vonagePhoneNumberDto | twilioPhoneNumberDto | vapiPhoneNumberDto,
    orgId: string,
    agentId: string,
  ) {
    if (b.provider === 'vonage') {
      if (!b.credentialId) throw new Error('credentialId is required');
    } else if (b.provider === 'twilio') {
      if (!b.twilioAccountSid) throw new Error('twilioAccountSid is required');
      if (!b.twilioAuthToken) throw new Error('twilioAuthToken is required');
    } else if (b.provider === 'vapi' || b.provider === 'None') {
      if (!b.sipUri) throw new Error('sipUri is required');
    } else {
      throw new Error('provider is required');
    }
    b['serverUrl'] =
      b.serverUrl ??
      `${process.env.BACKEND_URL}/voice/webhooks/assistant-request/${agentId}`;

    this.logger.log({
      message: 'Creating phone number with server url ' + b.serverUrl,
      context: 'VOICE | CREATE PHONE NUMBER',
    })
    const existingPhoneDoc = await this.mongoVoiceNumberService.getPhone({
      agentId: agentId,
    });
    if (existingPhoneDoc) {
      throw new BadRequestException(
        `Phone number already exists under the agentId ${agentId}`,
      );
    }
    const vapiPhoneBody: any = b;
    let assistantId: string;
    if (vapiPhoneBody.orgId) delete vapiPhoneBody.orgId;
    if (vapiPhoneBody.agentId) delete vapiPhoneBody.agentId;
    if (vapiPhoneBody.phoneNumber) {
      vapiPhoneBody.number = vapiPhoneBody.phoneNumber;
      delete vapiPhoneBody.phoneNumber;
    }
    if (vapiPhoneBody.assistantId) {
      assistantId = vapiPhoneBody.assistantId;
      delete vapiPhoneBody.assistantId;
    }
    
    if (b?.fallbackDestination?.number){
      vapiPhoneBody['fallbackDestination']['type'] = 'number'
    }
    
    const newPhone = await this.vapiApiService.createPhoneNumber(
      process.env.VAPI_TOKEN,
      {
        body: vapiPhoneBody,
      },
    );

    this.logger.log({
      message: 'Phone number created successfully',
      context: 'VOICE | CREATE PHONE NUMBER',
    });
    const data = {
      orgId: orgId,
      phoneId: newPhone.id,
      phoneNumber: vapiPhoneBody.number,
      name: b.name,
      fallbackDestination: b.fallbackDestination?.number
        ? {
            number: b.fallbackDestination?.number,
            message: b.fallbackDestination?.message ?? '',
            description: b.fallbackDestination?.description ?? '',
          }
        : undefined,
      provider: b.provider,
      agentId: agentId,
    };
    if (b.provider === 'twilio'){
      data['creds'] = {
        twilioAccountSid: b.twilioAccountSid,
        twilioAuthToken: b.twilioAuthToken
      }
    }
    await this.mongoVoiceNumberService.createPhone(data);

    return data;
  }

  async getPhoneNumber({ phoneId }: { phoneId: string }) {
    try {
      // return await this.vapiApiService.getPhoneNumber(process.env.VAPI_TOKEN, {
      //   phoneId,
      // });
      const p = await this.mongoVoiceNumberService.getPhone({ phoneId });
      const agent = await this.mongoagentService.getAgent({
        agentId: p.agentId,
      }, {
        voiceConfig: 1
      });
      
      if (!p.fallbackDestination?.number) {
        delete p.fallbackDestination;
      }

      this.logger.log({
        message: 'Phone number fetched successfully for phoneId: ' + phoneId,
        context: 'VOICE | GET PHONE NUMBER',
      });
      return {...p, transferCall: {
        transferCallEnabled: agent?.voiceConfig?.transferCall?.transferCallEnabled ?? false,
        transferCallNumber: agent?.voiceConfig?.transferCall?.transferCallNumber ?? '',
        transferMessage: agent?.voiceConfig?.transferCall?.transferMessage ?? '',
        transferCallPrompt: agent?.voiceConfig?.transferCall?.transferCallPrompt ?? '',
      }};
    } catch (error) {
      this.logger.error({
        message: error.message,
        context: 'VAPI | GET PHONE NUMBER',
      });
      return { error: error.message };
    }
  }

  async deletePhoneNumber({ phoneId }: { phoneId: string }) {
    try {
      await this.vapiApiService.deletePhoneNumber(process.env.VAPI_TOKEN, {
        phoneId,
      });
      this.logger.log({
        message: 'Phone number deleted successfully for phoneId: ' + phoneId,
        context: 'VOICE | DELETE PHONE NUMBER',
      });

      const phoneNumber = await this.mongoVoiceNumberService.deletePhone({ phoneId });
      return phoneNumber;
    } catch (error) {
      this.logger.error({
        message: error.message,
        context: 'vapiApiService.deletePhoneNumber',
      });
      return { error: error.message };
    }
  }

  async updatePhoneNumber({
    phoneId,
    b,
    agentId,
  }: {
    phoneId: string;
    b: {
      name?: string;
      fallbackDestination?: {
        number: string;
        message?: string;
        description?: string;
      };
    };
    agentId?: string;
  }) {
    const vapiUpdateBody: any = {};
    if (b?.fallbackDestination?.number) {
      vapiUpdateBody.fallbackDestination = {
        type: 'number',
        number: b.fallbackDestination.number,
        message: b?.fallbackDestination?.message,
        description: b?.fallbackDestination?.description,
      };
    }
    if (b.name) vapiUpdateBody.name = b.name;

    const updatedPhone = await this.vapiApiService.updatePhoneNumber(
      process.env.VAPI_TOKEN,
      {
        body: vapiUpdateBody,
        phoneId,
      },
    );
    const updateBody = {};
    if (b.name) updateBody['name'] = b.name;
    if (agentId) updateBody['agentId'] = agentId;
    if (b?.fallbackDestination?.number)
      updateBody['fallbackDestination'] = {
        number: b.fallbackDestination.number,
        message: b?.fallbackDestination?.message,
        description: b?.fallbackDestination?.description,
      };
    const p = await this.mongoVoiceNumberService.updatePhone(
      {
        phoneId: updatedPhone.id,
      },
      {
        $set: updateBody,
      },
    );

    this.logger.log({
      message:
        'Phone number updated successfully for phoneId: ' + updatedPhone.id,
      context: 'VOICE | UPDATE PHONE NUMBER',
    });
    return p;
  }

  async listPhoneNumbers({ agentId }: { agentId: string }) {
    try {
      const p =
        (await this.mongoVoiceNumberService.getPhones({ agentId: agentId })) ??
        [];
      return p;
    } catch (error) {
      this.logger.error({
        message: error.message,
        context: 'VOICE | LIST PHONE NUMBERS',
      });
      return { error: error.message };
    }
  }
}
