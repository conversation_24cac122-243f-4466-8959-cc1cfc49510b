interface createPhoneNumberDto {
  fallbackDestination: {
    number: string;
    message?: string;
    description?: string;
  };
  phoneNumber: string;
  name?: string;
  // assistantId?: string;
  squadId?: string;
  serverUrl?: string;
  serverUrlSecret?: string;
}

export interface vonagePhoneNumberDto extends createPhoneNumberDto {
  provider: 'vonage';
  credentialId: string;
}

export interface twilioPhoneNumberDto extends createPhoneNumberDto {
  provider: 'twilio';
  twilioAccountSid: string;
  twilioAuthToken: string;
}

export interface vapiPhoneNumberDto extends createPhoneNumberDto {
  provider: 'vapi' | 'None';
  sipUri: string;
}
