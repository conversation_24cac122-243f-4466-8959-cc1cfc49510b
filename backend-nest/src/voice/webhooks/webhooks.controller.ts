/**
 * <PERSON>les Voice Webhooks for handling end of the call report, assistant request.
 */
import {
    Controller,
    Get,
    Post,
    Req,
    Res,
    Body,
    HttpStatus,
    Render,
    Delete,
    Param,
    Query,
    UseGuards,
    Patch,
} from '@nestjs/common';
import { Request, Response } from 'express';
import {
ApiOperation,
ApiQuery,
ApiNotFoundResponse,
ApiOkResponse,
ApiParam,
} from '@nestjs/swagger';
import { MyLogger } from 'src/logger/logger.service';
import { handleException } from 'helpers/handleException';
import { WebhooksService } from './webhooks.service';
import { vapiApiService } from 'src/api-client/services/vapi/vapi-api.service';
import { MongoVoiceNumbersService } from 'src/mongo/service/voiceNumbers/voiceNumbers.service';


@Controller('voice/webhooks')
export class WebhooksController {

  constructor(
    private readonly webhooksService: WebhooksService,
    private readonly myLogger: MyLogger,
    private readonly vapiApiService: vapiApiService,
    private readonly mongoVoiceNumberService: MongoVoiceNumbersService,
  ) {}

  @Post('end-of-call-report/:agentId')
  async handleEndOfCallReport(
    @Req() req: Request,
    @Res() res: Response,
    @Body() body: any,
    @Param('agentId') agentId: string
  ){
    try {
        const callId = body?.message?.call?.id;
        const call = await this.vapiApiService.getCall(process.env.VAPI_TOKEN, callId);
        const costBreakdown = call?.costBreakdown ?? call?.cost_breakdown;

        this.myLogger.log({
          message: `Updating billing - ${body?.message?.duration_seconds} seconds for agent - ${agentId} , callId - ${callId}`,
          context: 'VOICE | END OF CALL REPORT',
        })
        await this.webhooksService.handleEndOfCallReport({
          agentId,
          usageSeconds:
            body?.message?.duration_seconds ?? body?.message?.durationSeconds,
          callId
        });

        res.status(200).json({
            message: 'End of call report received successfully'
        })
    } catch (error) {
        this.myLogger.error({
            message: error.response?.data?.message ?? error?.message,
            context: `END OF CALL REPORT | WEBHOOKS`
        });
        handleException(res, error)   
    }   
  }

  @Post('assistant-request/:agentId')
  async handleAssistantRequest(
    @Param('agentId') agentId: string,
    @Req() req: Request,
    @Res() res: Response,
    @Body() body: any
  ){
    try {
      if (body?.message?.type === 'assistant-request') {
        // const call = body?.message?.call;
        // const destination = call?.destination?.number;
        // const voicePhone = await this.mongoVoiceNumberService.getPhone({
        //   phoneNumber: destination
        // });
        const { assistantId } = await this.webhooksService.getAssistant(
          agentId,
        );
        if (!assistantId) throw new Error('Assistant not found');
        // get phone number from collection
        return res.status(200).json({assistantId})
      } else {
        throw new Error('Not an assistant request');
      }
    } catch (error) {
        this.myLogger.error({
            message: error.response?.data?.message ?? error?.message,
            context: `ASSISTANT REQUEST | WEBHOOKS`
        });
        return res.status(400).json({
          error: error?.message ?? 'Failed to retrieve an assistant'
        })
    }
  }
}
