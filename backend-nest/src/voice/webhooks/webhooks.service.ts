import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { MyLogger } from 'src/logger/logger.service';
import { MongoAgentService } from 'src/mongo/service/agent/mongo-agent.service';
import { VoiceUsageService } from 'src/organization/services/usage/voice-usage/voice-usage.service';
import { VoiceService } from '../voice.service';

@Injectable()
export class WebhooksService {
    constructor(
        private logger: MyLogger,
        private readonly voiceUsageService: VoiceUsageService,
        private readonly mongoAgentService: MongoAgentService,
        private readonly voiceService: VoiceService
    ) {}

    async handleEndOfCallReport({
        agentId,
        usageSeconds,
        callId
    }: {
        agentId: string;
        usageSeconds: number;
        callId?: string;
    }){
        const d = await this.voiceUsageService.addUsage(agentId, {
            usageSeconds,
        }, callId);
        if (d?.agent){
            this.logger.log({
                message: `Updated voice usage for agent - ${agentId}`,
                context: 'VOICE | END OF CALL REPORT',
            })
        }
    }

    async getAssistant(agentId: string) {
        const agent = await this.mongoAgentService.getAgent({ _id: agentId });
        if (!agent) {
            throw new NotFoundException(`Agent with ID ${agentId} does not exist`);
        }
        const h = await this.voiceService.getVoiceAgentHealth(agent);
        if (!h.success) throw new BadRequestException(h.failReason);
        return { assistantId: agent?.voiceConfig?.assistantId };
    }
}
