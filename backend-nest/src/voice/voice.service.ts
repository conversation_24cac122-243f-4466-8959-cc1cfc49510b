/**
 * Core service implementing voice functionality and business logic
 */
import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { MyLogger } from 'src/logger/logger.service';
import { voiceActivationDto } from './vapi/assistants/assistants.dto';
import { AssistantsService } from './vapi/assistants/assistants.service';
import { MongoAgentService } from 'src/mongo/service/agent/mongo-agent.service';
import { MongoOrganizationService } from 'src/mongo/service/organization/mongo-organization.service';
import { vapiApiService } from 'src/api-client/services/vapi/vapi-api.service';
import { MIN_BALANCE_FOR_VOICE } from 'src/lib/constant';
import { AgentDocument } from 'src/mongo/schemas/agents/agents.schema';
import { MongoVoiceUsageService } from 'src/mongo/service/voiceUsage/voice-usage.service';
import { TransferCallToolType } from './types/voice-services.dto';
import { ToolsService } from './vapi/tools/tools.service';

@Injectable()
export class VoiceService {
    constructor(
        private readonly logger: MyLogger,
        private readonly assistantService: AssistantsService,
        private readonly mongoAgentService: MongoAgentService,
        private readonly mongoOrganizationService: MongoOrganizationService,
        private readonly vapiApiService: vapiApiService,
        private readonly mongoVoiceUsageService: MongoVoiceUsageService,
        private readonly toolsService: ToolsService,
    ) {}

    async handleVoiceActivation(b: voiceActivationDto){
        return await this.assistantService.createVapiAssistant(b);
    }

    async toggleAssistantService(b){
        const agent = await this.mongoAgentService.getAgent({ _id: b.agentId });
        if (!agent) throw new BadRequestException('Agent not found');
        await this.mongoAgentService.updateAgent({
            _id: b.agentId
        }, {
            $set: {
                'voiceConfig.enabled': b.enabled
            }
        });
    }

    async handleChannelAssistantMapping(b:{
        agentId: string,
        accountId: string,
        orgId: string,
    }){
        const org = await this.mongoOrganizationService.getOrganization({ _id: b.orgId }, { 'connections.channels': 1});
        if (!org) throw new BadRequestException('Organization not found');
        const channel = (org?.connections?.channels ?? []).find(c => c.accountId === b.accountId);
        if (!channel) throw new BadRequestException('Channel not found');

        await this.mongoAgentService.updateAgent({
            _id: b.agentId
        }, {
            $set: {
                'voiceConfig.channel': {
                    accountId: b.accountId,
                    providerName: channel?.providerName,
                    name: channel?.name,
                    keyId: channel.keyId,
                }
            }
        });
    }

    async updateAssistant(b){
        const agent = await this.mongoAgentService.getAgent({ _id: b.agentId });
        if (!agent) throw new BadRequestException('Agent not found');
        const assistantId = agent?.voiceConfig?.assistantId;
        if (assistantId){
            const updateBody = {
                    'voiceConfig.firstMessage': b?.body?.firstMessage?? "",
                    'voiceConfig.firstMessageMode': b?.body?.firstMessageMode ?? "",
                    'voiceConfig.model': b?.body?.model,
            };

            const updatedAgent = await this.mongoAgentService.updateAgent({
                _id: b.agentId
            }, {
                $set: updateBody
            })
            await this.assistantService.updateVapiAssistant({
              agentId: b.agentId,
              assistantId,
              firstMessage: b?.body?.firstMessage,
              firstMessageMode: b?.body?.firstMessageMode,
              model: b?.body?.model,
              voice: b?.body?.voice,
              backchannelingEnabled: b?.body?.backchannelingEnabled,
              backgroundDenoisingEnabled: b.body?.backgroundDenoisingEnabled,
              silenceTimeoutSeconds: b?.body?.silenceTimeoutSeconds,
              backgroundSound: b.body?.backgroundSound,
            });
            return {
                assistantId,
                ...(b.body ?? {}),
                tools: updatedAgent?.voiceConfig?.tools
            }
        } else {
            this.logger.log({
                message: 'No linked voice assistant found',
                context: 'UPDATE AGENT | VOICE'
            })
            throw new BadRequestException('No linked voice assistant found');
        }
    }

    async revertVoiceAssistant(b){
        return await this.assistantService.revertVoiceAssistant(b)
    }

    async getVapiAssistant({agentId}:{agentId?: string}){
        const agent = await this.mongoAgentService.getAgent({ _id: agentId });
        const assistant = await this.assistantService.getVapiAssistant({agent});
        return {
            assistantId: assistant?.id,
            ...(agent?.voiceConfig ?? {}),
            ...(assistant ?? {}),
        }
    }

    async getVoiceCallHistory(agentId: string, limit?: number, createdAtGe?: string, createdAtLe?: string){
        const agent = await this.mongoAgentService.getAgent({ _id: agentId });
        if (!agent) throw new BadRequestException('Agent not found');
        const assistantId = agent?.voiceConfig?.assistantId;
        if (!assistantId) return { message: "No active assistant found for the agent" };
        const calls = await this.vapiApiService.listCalls(process.env.VAPI_TOKEN, assistantId, limit, createdAtGe, createdAtLe);
        const history = (calls ?? []).map(c => ({
            messages: c?.messages,
            totalCost: c?.cost,
            analysis: c?.analysis,
            createdAt: c?.createdAt,
            updatedAt: c?.updatedAt,
            type: c?.type,
            assistantId: c?.assistantId,
            callId: c?.id,
            customer: c?.customer,
            customerId: c?.customerId,
            phoneCallProvider: c?.phoneCallProvider,
        }));
        this.logger.log({
            message: `Call history fetched successfully for agentId: ${agentId} , assistantId: ${assistantId}`,
            context: 'VOICE | GET CALL HISTORY'
        })
        return {
            message: 'Call history fetched successfully',
            history
        }
    }


    async getCall(callId: string) {
        const call = await this.vapiApiService.getCall(process.env.VAPI_TOKEN, callId);
        const recordedVoiceUsage = await this.mongoVoiceUsageService.getUsageByCallId(callId)
        this.logger.log({
            message: `Call history fetched successfully for callId: ${callId}`,
            context: 'VOICE | VOICE GET CALL HISTORY'
        })
        return {
            ...call, 
            cost: recordedVoiceUsage?.cost, 
            costBreakdown: {
                numOfTokensUsed: recordedVoiceUsage?.numOfTokensUsed,
                numOfActions: recordedVoiceUsage?.numOfActions,
                numOfSeconds: recordedVoiceUsage?.numOfSeconds,
                voiceProvider: recordedVoiceUsage?.voiceProvider,
                numOfKnowledgeSource: recordedVoiceUsage?.numOfKnowledgeSource,
            }
        }
    }

    /**
     * Retrieves the health status of a voice assistant.
     * 
     * This method checks if the assistant exists, verifies the associated organization,
     * and ensures there are sufficient funds for the voice feature.
     *
     * @param assistantId - The unique identifier of the assistant.
     * @returns A promise that resolves to the AssistantHealth object.
     * @throws NotFoundException if the agent or organization is not found.
     */
    async getVoiceAgentHealth(agent: AgentDocument) {
        const {orgId} = agent;
        const organization = await this.mongoOrganizationService.getOrganization({_id: orgId});
        if(!organization) {
            throw new NotFoundException(`Could not find an organization which has an ID ${orgId}`);
        }

        const addonAmount = organization?.billing?.addonAmount ?? 0;
        if(addonAmount < MIN_BALANCE_FOR_VOICE) {
            return {
                success: false,
                failReason: `Insuffient funds. For this feature to work, you have to maintain a minimum fund of $${MIN_BALANCE_FOR_VOICE}. Current fund $${addonAmount}`
            }
        }
        return {
            success: true,
            failReason: null
        }
    } 


    async upsertTransfercallType(b: TransferCallToolType) {
        let agent = await this.mongoAgentService.getAgent({
            agentId: b.agentId
        }, {
            voiceConfig: 1
        });

        let newTransferCallTool;
        if (!agent?.voiceConfig?.transferCall?.transferCallToolId) {
            newTransferCallTool = await this.toolsService.createTransferCallTool({ transferCall: b, agentId: b.agentId });
        } else {
            newTransferCallTool = await this.toolsService.getTool({ toolId: agent?.voiceConfig?.transferCall?.transferCallToolId })
        }

        agent = await this.mongoAgentService.updateAgent({
            _id: b.agentId
        }, {
            $set: {
                'voiceConfig.transferCall': {
                    transferCallEnabled: b?.transferCallEnabled ?? false,
                    transferCallNumber: b?.transferCallNumber ?? '',
                    transferMessage: b?.transferMessage ?? '',
                    transferCallPrompt: b?.transferCallPrompt ?? '',
                    transferCallToolId: newTransferCallTool?.id ?? ''
                }
            }
        }, {
            new: true
        });

        if (newTransferCallTool) {
            const { actions, toolIds, tools, vapiPrompt } = await this.assistantService.createVapiSystemPrompt(agent);

            await this.assistantService.updateVapiAssistant({
                agentId: agent._id.toString(),
                assistantId: agent?.voiceConfig.assistantId,
                mainPrompt: vapiPrompt,
                toolIds,
                model: {
                    model: agent?.voiceConfig?.model?.model,
                    provider: agent?.voiceConfig?.model?.provider,
                },
            });
        }
    }
}