import { LoggerModule } from '../logger/logger.module';
import { HttpModule } from '@nestjs/axios';
import { Module, forwardRef } from '@nestjs/common';
import { ConnectionsService } from './connections.service';
import { OrganizationModule } from 'src/organization/organization.module';
import { OutlookController } from './outlook/outlook.controller';
import { OutlookService } from './outlook/outlook.service';

@Module({
    imports: [HttpModule, LoggerModule, OrganizationModule],
    exports: [OutlookService],
    controllers: [OutlookController],
    providers: [ConnectionsService, OutlookService],
})
export class ConnectionsModule {}
