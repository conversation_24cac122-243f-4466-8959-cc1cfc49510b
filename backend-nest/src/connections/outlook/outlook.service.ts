import { OrganizationService } from 'src/organization/services/organization.service';
import { BadRequestException, Injectable } from '@nestjs/common';
import { MongoCredentialsService } from 'src/mongo/service/credentials/mongo-credentials.service';
import { Response, query } from 'express';
import { HttpStatus } from '@nestjs/common/enums';
import { KINDS, PROVIDERS } from 'src/lib/constant';
import { ChatHqService } from 'src/organization/services/channels/chat-hq/chat-hq.service';
import { MyLogger } from 'src/logger/logger.service';
import { v4 as uuidv4 } from 'uuid';
import { handleException } from 'helpers/handleException';
import {
  getNylasAccessToken,
  getNylasAuthUrl,
  createNylasWebhook,
} from 'src/api-client/services/nylas/nylas-api';
import { WebhookTriggers } from 'nylas';

@Injectable()
export class OutlookService {
  constructor(
    private readonly organizationService: OrganizationService,
    private readonly myLogger: MyLogger,
    private readonly mongoCredentialsService: MongoCredentialsService,
  ) {}

  async connectOutlook(res: Response, code?: string, orgId?: string) {
    try {
      if (!orgId) throw new BadRequestException('Invalid orgId received');
      if (!code) {
        const authUrl = getNylasAuthUrl(orgId);
        return res.redirect(authUrl);
      }
      
      let accountId = uuidv4();

      const nylasToken = await getNylasAccessToken({ code });

      // check if credential already exists
      const oldCredential = await this.mongoCredentialsService.getCredential({
        keyId: nylasToken.grantId,
        kind: KINDS.OUTLOOK_NYLAS_CREDENTIAL,
        type: PROVIDERS.OUTLOOK_NYLAS
      });
      
      let channelData: object = {};

      // if it does, update it in credential collection & organization document
      if (oldCredential?._id) {
        await this.mongoCredentialsService.updateCredential(
          {
            _id: oldCredential._id.toString(),
          },
          {
            keyId: nylasToken.grantId,
            creds: nylasToken,
            type: PROVIDERS.OUTLOOK_NYLAS,
            organizationId: oldCredential.organizationId,
            kind: KINDS.OUTLOOK_NYLAS_CREDENTIAL,
          },
        );

        // error after update to ensure the updated credential is saved
        if (oldCredential.organizationId !== orgId) {
          const org = await this.organizationService.getOrganization({ _id: oldCredential.organizationId }, {name: 1});
          throw new BadRequestException(`This account is already connected to another organization: ${org.name}. Capri only allows one account to be connected to one organization at a time.`);
        }

        channelData = {
          credentialId: oldCredential._id.toString(),
          name: nylasToken.email,
          providerName: PROVIDERS.OUTLOOK_NYLAS,
          author: '',
          accountId,
          keyId: nylasToken.grantId,
        };
      } 
      // if it doesn't, create it in credential collection & organization document 
      else {
        const newCred = await this.mongoCredentialsService.createCredentials({
          keyId: nylasToken.grantId,
          creds: nylasToken,
          type: PROVIDERS.OUTLOOK_NYLAS,
          organizationId: orgId,
          kind: KINDS.OUTLOOK_NYLAS_CREDENTIAL,
        });

        channelData = {
          name: nylasToken.email,
          providerName: PROVIDERS.OUTLOOK_NYLAS,
          author: '',
          accountId,
          keyId: nylasToken.grantId,
          credentialId: newCred._id.toString(),
        };

        await this.organizationService.updateOrganisation({
          _id: orgId,
        },{
          $push: {
            'connections.channels': channelData,
          },
        });

        await createNylasWebhook({
          requestBody: {
            triggerTypes: [WebhookTriggers.MessageCreated, WebhookTriggers.MessageOpened, WebhookTriggers.ThreadReplied, 
              WebhookTriggers.MessageSendFailed, WebhookTriggers.MessageSendSuccess],
            webhookUrl: process.env.BACKEND_URL + '/webhook/outlook/'+ orgId,
            description: `Outlook Channel Webhook for ${orgId}`,
          }
        })
      }

      const script = `
         <script>
            window.opener.postMessage(${JSON.stringify({
              message: 'Slack channel connected successfully!',
              ...channelData,
            })}, '*');
            window.close();
          </script>
      `;

      return res.status(HttpStatus.OK).send(script);

    } catch (error) {
      this.myLogger.error({
        message: `Error in connecting outlook | orgId ${orgId} | code ${code} | error - ${error.message}`,
        context: `NYLAS OUTLOOK CONNECT`,
      });
      return res.status(HttpStatus.BAD_REQUEST).send(
        `<body style="background-color: black; color: #000;">
            <h2 style="font-size: large; color: white;">
            Oops! Encountered an error while connecting Outlook to your organization.
            <br>
            Reason - ${error?.response?.data?.message ?? error.message}</h2>
        </body>`,
      );
    }
  }
}
