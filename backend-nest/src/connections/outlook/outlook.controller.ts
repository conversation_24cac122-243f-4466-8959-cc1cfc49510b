/**
 * contains implementation of outlook integration - to connect an outlook channel
 */
import {
  Controller,
  Get,
  Post,
  Req,
  Res,
  Body,
  HttpStatus,
  Render,
  Delete,
  Param,
  Query,
} from '@nestjs/common';
import { Request, Response } from 'express';
import { handleException } from 'helpers/handleException';
import { MyLogger } from 'src/logger/logger.service';
import { ChannelsService } from 'src/organization/services/channels/channels.service';
import { OutlookService } from './outlook.service';

@Controller('v1/channel/outlook')
export class OutlookController {
  constructor(
    private readonly logger: MyLogger,
    private readonly outlookService: OutlookService,
  ) {}

  @Get('/connect')
  async connect(
    @Query() query: { code?: string; state?: string },
    @Res() res: Response,
  ) {
    const { state = undefined, code = undefined } = query;
    return await this.outlookService.connectOutlook(res, code, state);
  }
}
