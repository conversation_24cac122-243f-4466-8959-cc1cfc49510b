import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Put,
  Req,
} from '@nestjs/common';
import { AppUpdatesService } from './app-updates.service';
import { CreateAppUpdateDto } from './dto/create-app-update.dto';
import { UpdateAppUpdateDto } from './dto/update-app-update.dto';
import { RequestWithUser } from 'src/auth/auth.interface';

@Controller('app-updates')
export class AppUpdatesController {
  constructor(private readonly appUpdatesService: AppUpdatesService) {}

  @Post()
  create(@Body() createAppUpdateDto: CreateAppUpdateDto) {
    

    return this.appUpdatesService.create(createAppUpdateDto);
  }

  @Put(':id/seen')
  async appUpdateSeenByUser(
    @Param('id') id: string,
    @Req() req: RequestWithUser,
  ) {
    const userId = req.userId;
    

    return await this.appUpdatesService.appUpdateSeenByUser(id, userId);
  }

  @Get('/latest')
  findLatestAppUpdate(@Req() req: RequestWithUser) {
    const userId = req.userId;
    return this.appUpdatesService.findRecentUpdateForUser(userId);
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.appUpdatesService.findOne(+id);
  }

  @Patch(':id')
  update(
    @Param('id') id: string,
    @Body() updateAppUpdateDto: UpdateAppUpdateDto,
  ) {
    return this.appUpdatesService.update(+id, updateAppUpdateDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.appUpdatesService.remove(+id);
  }
}
