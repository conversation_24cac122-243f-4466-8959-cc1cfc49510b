import { Module } from '@nestjs/common';
import { AppUpdatesService } from './app-updates.service';
import { AppUpdatesController } from './app-updates.controller';
import { MongoAppUpdatesService } from 'src/mongo/service/app-updates/mongo-app-updates.service';

@Module({
  controllers: [AppUpdatesController],
  providers: [AppUpdatesService, MongoAppUpdatesService],
})
export class AppUpdatesModule {}
