import { Test, TestingModule } from '@nestjs/testing';
import { AppUpdatesController } from './app-updates.controller';
import { AppUpdatesService } from './app-updates.service';

describe('AppUpdatesController', () => {
  let controller: AppUpdatesController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AppUpdatesController],
      providers: [AppUpdatesService],
    }).compile();

    controller = module.get<AppUpdatesController>(AppUpdatesController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
