import { Injectable } from '@nestjs/common';
import { CreateAppUpdateDto } from './dto/create-app-update.dto';
import { UpdateAppUpdateDto } from './dto/update-app-update.dto';
import { MongoAppUpdatesService } from 'src/mongo/service/app-updates/mongo-app-updates.service';

@Injectable()
export class AppUpdatesService {
  constructor(
    private readonly mongoAppUpdatesService: MongoAppUpdatesService,
  ) {}
  async create(createAppUpdateDto: CreateAppUpdateDto) {
    return await this.mongoAppUpdatesService.createAppUpdates(
      createAppUpdateDto,
    );
  }

  async appUpdateSeenByUser(id: string, userId: string) {
    return await this.mongoAppUpdatesService.appUpdateSeenByUser(id, userId);
  }

  async findRecentUpdateForUser(userId: string) {
    return await this.mongoAppUpdatesService.findRecentUpdateForUser(userId);
  }

  findAll() {
    return `This action returns all appUpdates`;
  }

  findOne(id: number) {
    return `This action returns a #${id} appUpdate`;
  }

  update(id: number, updateAppUpdateDto: UpdateAppUpdateDto) {
    return `This action updates a #${id} appUpdate`;
  }

  remove(id: number) {
    return `This action removes a #${id} appUpdate`;
  }
}
