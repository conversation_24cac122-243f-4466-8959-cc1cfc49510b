import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose, { HydratedDocument } from 'mongoose';

export type HydratedThirdPartyAppDocument = HydratedDocument<ThirdPartyApps>

@Schema({ collection: 'thirdPartyApps', timestamps: true })
export class ThirdPartyApps {
    @Prop({ type: { type: String, enum: ['uphex'] } })
    appName: string;

    @Prop({
        id: { type: String },
        type: { type: String, enum: ['email', 'number', 'uid'] }
    })
    account: {
        id: string,
        type: string
    };

    @Prop({ type: String })
    capriOrgId: string;

    @Prop()
    capriUserId?: string;

    @Prop({ type: [{ key: String, type: String }], required: false })
    variables?: Array<{
        key: string, // i.e. template_id, prompt, etc
        type: string // i.e. string, number, date, etc
    }>;
}

export const ThirdPartyAppsSchema = SchemaFactory.createForClass(ThirdPartyApps);