import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose, { HydratedDocument } from 'mongoose';

@Schema({ collection: 'session' })
export class Session {
  @Prop({ default: false })
  active: boolean;

  @Prop({ default: false })
  saved: boolean;

  @Prop({
    type: {
      orgId: String,
      userId: String,
      agentId: String,
      sessionName: String,
    },
    _id: false,
  })
  basic: {
    orgId: string;
    userId: string;
    agentId: string;
    sessionName: string;
  };

  @Prop({ type: mongoose.Schema.Types.Mixed, required: false })
  metaData: Record<string, any>;

  @Prop({
    type: {
      createdAt: Number,
      lastUpdated: Number,
    },
    _id: false,
  })
  dates: {
    createdAt: Number;
    lastUpdated: Number;
  };

  @Prop({
    type: [
      {
        eventId: { type: String, required: true },
        sender: { type: String, required: true },
        message: { type: String, required: false },
        kind: { type: String, required: false },
        action: { type: String, required: false },
        accountId: { type: String, required: false },
        botResponse: [
          {
            type: mongoose.Schema.Types.Mixed,
            required: true,
          },
        ],
        timestamp: { type: Number, required: true },
      },
    ],
    _id: false,
  })
  events: {
    eventId: string;
    sender: string;
    message?: string;
    kind?: string;
    action?: string;
    accountId?: string;
    botResponse: {
      [key: string]: any;
    }[];
    timestamp: number;
  }[];
}

export const SessionSchema = SchemaFactory.createForClass(Session);

export type SessionDocument = HydratedDocument<Session>;
