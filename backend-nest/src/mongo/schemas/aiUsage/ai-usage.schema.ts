import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, Types } from 'mongoose';

@Schema({
  collection: 'ai_usage',
  timestamps: true,
  timeseries: {
    timeField: 'createdAt',
    metaField: 'organization',
    granularity: 'hours',
  },
})
export class AiUsage {
  @Prop({
    required: true,
    type: Types.ObjectId,
    ref: 'Organization',
    index: true,
  })
  organization: Types.ObjectId;

  @Prop({ required: true, type: Types.ObjectId, ref: 'Agent', index: true })
  agent: Types.ObjectId;

  @Prop({ required: false, default: '', index: true })
  location: string; //The locationId of the subaccount

  @Prop({ required: true, type: Number })
  numOfTokensUsed: number;

  @Prop({ required: true, type: Number })
  cost: number; //in cents

  @Prop({ required: true, type: String })
  provider: string;

  @Prop({ required: true, type: String, index: true })
  model: string;

  @Prop({ required: true, type: Date, index: true, default: Date.now })
  createdAt?: Date;
}

export type AiUsageDocument = HydratedDocument<AiUsage>;
export const AiUsageSchema = SchemaFactory.createForClass(AiUsage);
