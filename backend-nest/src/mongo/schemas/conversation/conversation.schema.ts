import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, HydratedDocument, Types } from 'mongoose';

export type ConversationDocument = HydratedDocument<Conversation> & {
    channelDetails?: {
        [key: string]: string;
    }
};

@Schema({ _id: false })
export class Actions extends Document {
    @Prop({ required: true })
    accountName: string;

    @Prop({ required: true })
    action: string;

    @Prop({ required: true })
    kind: string;

    @Prop({ required: false })
    calendarId?: string;

    @Prop({ required: false })
    locationId?: string;

    @Prop({ required: true, default: false })
    silent: boolean;

    @Prop({ required: false })
    tagName?: string;

    @Prop({ required: false })
    startDate?: string;

    @Prop({ required: false })
    endDate?: string;

    @Prop({ required: true, default: [] })
    error: string[];
}

@Schema({ _id: false })
export class ConversationRecord extends Document {
    @Prop({ required: true })
    body: string;

    @Prop({ required: true })
    dateAdded: Date;

    @Prop({ required: false })
    direction?: string;

    @Prop({ required: false })
    tokens?: number;

    @Prop({ required: false })
    aiProvider?: string;

    @Prop({ required: false })
    actions?: Actions[];

    @Prop({ required: false })
    messageType?: string;

    @Prop({required: false, default: 'pending'})
    status?: 'pending' | 'delivered' | 'received';
}

@Schema({ _id: false })
export class FollowUpRecord extends Document {
    @Prop({ required: true })
    lastAttemptAt: Date;

    @Prop({ required: false, default: 0 })
    attemptCount?: number;

    @Prop({ required: false })
    error?: string;

    @Prop({ required: false })
    followUpTaskId?: string;
}

export const ConversationRecordSchema = SchemaFactory.createForClass(ConversationRecord);
export const FollowUpRecordSchema = SchemaFactory.createForClass(FollowUpRecord);

@Schema({ collection: 'conversations', discriminatorKey: 'conversationType' })
export class Conversation {

    @Prop({ type: [ConversationRecordSchema], default: [] })
    conversation: ConversationRecord[];

    @Prop({ type: FollowUpRecordSchema, required: false })
    followUp?: FollowUpRecord;

    @Prop({ required: false })
    type?: string;

    @Prop({ required: true })
    contactId: string;

    @Prop({ required: false })
    conversationId?: string;

    @Prop({ required: true })
    resourceId: string; // eg. locationId for Gohighlevels

    @Prop({ required: false })
    agentId?: string;

    @Prop({ required: false })
    channel?: string;

    @Prop({ required: false })
    updatedAt?: Date;

    @Prop({ required: false })
    totalTokens?: number;

    @Prop({ required: false })
    orgId?: string;

    @Prop({ required: false, default: false})
    rollover?: boolean;

    @Prop({ required: false })
    rolloverReason?: string;

    @Prop({ required: false })
    rolloverDate?: Date;

    @Prop({ required: false })
    source?: string;

    @Prop({
        type: Boolean,
        required: false,
        default: false
    })
    disconnectBot?: boolean;

    @Prop({
        type: Boolean,
        required: false,
        default: false
    })
    addedAsSavedExample: boolean;
}


export const ConversationSchema = SchemaFactory.createForClass(Conversation);