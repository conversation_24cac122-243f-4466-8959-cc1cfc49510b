import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument } from 'mongoose';

export interface VariableDetail {
  name: string;
  description: string;
  type?: string;
}

@Schema({
  collection: 'agent_templates',
  timestamps: true,
})
export class AgentTemplates {
  @Prop({ required: true })
  name: string;

  @Prop({ required: true })
  prompt: string;

  @Prop()
  description?: string;

  @Prop({ type: [String], default: [] })
  tags: string[];

  @Prop([
    {
      name: { type: String, required: true },
      description: { type: String, required: false, default: '' },
      type: { type: String, required: false },
    },
  ])
  variables: VariableDetail[];

  @Prop({ required: true, index: true })
  organizationId: string;

  @Prop({ default: false })
  isDeleted: boolean;
}

export type AgentTemplatesDocument = HydratedDocument<AgentTemplates>;
export const AgentTemplatesSchema =
  SchemaFactory.createForClass(AgentTemplates);
