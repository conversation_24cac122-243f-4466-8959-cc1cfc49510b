import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

@Schema({ _id: false })
class GhlCredentials {
  @Prop({ type: String, required: false })
  accessToken?: string;
  @Prop({ type: String, required: false })
  refreshToken?: string;
}

const CredentialsSchema = SchemaFactory.createForClass(GhlCredentials);

@Schema()
export class GhlCredential extends Document {
  @Prop({ type: CredentialsSchema, required: true })
  creds: GhlCredentials;
}

export const GhlCredentialSchema = SchemaFactory.createForClass(GhlCredential);
