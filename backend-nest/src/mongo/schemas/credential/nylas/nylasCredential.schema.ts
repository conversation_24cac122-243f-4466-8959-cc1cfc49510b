import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

@Schema({ _id: false })
class NylasCreds {

 @Prop({ required: true })
 accessToken: string

 @Prop({ required: false })
 refreshToken?: string

 @Prop({ required: true })
 expiresIn: number

 @Prop({ required: true })
 idToken: string

 @Prop({ required: true })
 email: string

 @Prop({ required: false })
 scope?: string

 @Prop({ required: true })
 grantId: string

 @Prop({ required: true })
 provider: string;
}

const CredentialsSchema = SchemaFactory.createForClass(NylasCreds);

@Schema()
export class NylasCredential extends Document {
  @Prop({ type: CredentialsSchema, required: true })
  creds: NylasCreds;
}

export const NylasCredentialSchema =
  SchemaFactory.createForClass(NylasCredential);
