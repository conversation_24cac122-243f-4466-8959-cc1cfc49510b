import { <PERSON><PERSON>, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

@Schema({ _id: false })
export class AiCredential {
  @Prop()
  secret: string;
}

export const CredentialSchema = SchemaFactory.createForClass(AiCredential);

@Schema()
export class GroqCredential {
  @Prop({ type: CredentialSchema })
  creds: AiCredential;
}

export const GroqCredentialSchema = SchemaFactory.createForClass(GroqCredential);
