import { <PERSON><PERSON>, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

@Schema({ _id: false })
class AcuityTokens {
  @Prop({ type: String, required: false })
  access_token?: string;
  @Prop({ type: String, required: false })
  token_type?: string;
}
const AcuityTokensSchema = SchemaFactory.createForClass(AcuityTokens);

@Schema()
export class AcuityCredential extends Document {
  @Prop({ type: AcuityTokensSchema, required: true })
  creds:AcuityTokens;
}

export const AcuityredentialSchema = SchemaFactory.createForClass(AcuityCredential);
