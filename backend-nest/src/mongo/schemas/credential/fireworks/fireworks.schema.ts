import { <PERSON><PERSON>, Schema, SchemaFactory } from '@nestjs/mongoose';

@Schema({ _id: false })
export class AiCredential {
  @Prop()
  secret: string;
}

export const CredentialSchema = SchemaFactory.createForClass(AiCredential);

@Schema()
export class FireworksCredential {
  @Prop({ type: CredentialSchema })
  creds: AiCredential;
}

export const ClaudeCredentialSchema =
  SchemaFactory.createForClass(FireworksCredential);
