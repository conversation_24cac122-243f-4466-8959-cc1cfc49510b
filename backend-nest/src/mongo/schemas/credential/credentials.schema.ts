import { <PERSON><PERSON>, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, HydratedDocument } from 'mongoose';
import { GoogleCredential } from './google/googleCredentials.schema';
import { GhlCredential } from './ghl/ghlCredentials.schema';
import { OpenaiCredential } from './openai/openai.schema';
import { CalendlyCredential } from './calendly/calendly.schema';
import { AcuityCredential } from './acuity/acuity.schema';
import { ChathqCredential } from './chathq/chathq.schema';
import { PodiumCredential } from './podium/podiumCredentials.schema';
import { UphexCredential } from './uphex/upex.schema';
import { ClaudeCredential } from './claude/claude.schema';
import { GroqCredential } from './groq/groq.schema';
import { HubspotCredential } from './hubspot/hubspotCredentials.schema';
import { FireworksCredential } from './fireworks/fireworks.schema';
import { SlackCredential } from './slack/slackCredential.schema';
import { GeminiCredential } from './gemini/gemini.schema';
import { NylasCredential } from './nylas/nylasCredential.schema';

export type HydratedCredentialDocument = HydratedDocument<Credential> & {
  creds?: any;
  ssoToken?: string;
  webhookId?: string;
  podiumOrgId?: string;
  user?: string;
  hub_domain?: string;
  hub_id?: number;
  user_id?: number;
};

@Schema({ discriminatorKey: 'kind', timestamps: true })
export class Credential {
  @Prop({
    type: String,
    required: true,
    enum: [
      GoogleCredential.name,
      GhlCredential.name,
      OpenaiCredential.name,
      ClaudeCredential.name,
      GeminiCredential.name,
      GroqCredential.name,
      CalendlyCredential.name,
      AcuityCredential.name,
      ChathqCredential.name,
      UphexCredential.name,
      PodiumCredential.name,
      HubspotCredential.name,
      FireworksCredential.name,
      SlackCredential.name,
      NylasCredential.name,
    ],
  })
  kind: string;

  @Prop({
    type: Object,
  })
  creds: any;

  @Prop({
    type: String,
    required: true,
    default: new Date().toISOString(),
  })
  dateCreated: string;

  @Prop({
    type: String,
    required: true,
  })
  organizationId: string;

  @Prop({
    type: String,
    required: false,
  })
  calendarId: string;

  @Prop({ type: String, required: false })
  keyId?: string;

  @Prop({ type: String, required: false })
  type?: string;

  @Prop({ type: Boolean, required: false })
  alertedUser?: boolean;
}

export const CredentialsSchema = SchemaFactory.createForClass(Credential);
