import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

@Schema({ _id: false })
class ChatHQTokens {
  @Prop({ type: String, required: true })
  accessToken: string;
  @Prop({ type: String, required: true })
  accountId: string;
}
const ChathqTokensSchema = SchemaFactory.createForClass(ChatHQTokens);


@Schema()
export class ChathqCredential extends Document {
  @Prop({ type: ChathqTokensSchema, required: false })
  creds?: ChatHQTokens;

  @Prop({ type: String, required: false })
  ssoToken?: string;

  @Prop({ type: [String], required: false })
  webhookId?: string[];
}

export const ChathqCredentialSchema = SchemaFactory.createForClass(ChathqCredential);
