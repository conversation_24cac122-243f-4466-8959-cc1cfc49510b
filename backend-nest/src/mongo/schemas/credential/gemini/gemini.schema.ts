import { <PERSON><PERSON>, Schema, SchemaFactory } from '@nestjs/mongoose';

@Schema({ _id: false })
export class AiCredential {
  @Prop()
  secret: string;
}

export const CredentialSchema = SchemaFactory.createForClass(AiCredential);

@Schema()
export class GeminiCredential {
  @Prop({ type: CredentialSchema })
  creds: AiCredential;
}

export const GeminiCredentialSchema = SchemaFactory.createForClass(GeminiCredential);
