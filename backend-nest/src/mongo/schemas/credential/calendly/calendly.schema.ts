import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

@Schema({ _id: false })
class CalendlyTokens {
  @Prop({ type: String, required: false })
  access_token?: string;
  @Prop({ type: String, required: false })
  refresh_token?: string;
  @Prop({ type: String, required: false })
  expiry_date?: number;
  @Prop({ type: String, required: false })
  token_type?: string;
  @Prop({ type: String, required: false })
  id_token?: string;
  @Prop({ required: false })
  scope?: string;
}
const CalendlyTokensSchema = SchemaFactory.createForClass(CalendlyTokens);


@Schema()
export class CalendlyCredential extends Document {
  @Prop({ type: CalendlyTokensSchema, required: false })
  creds: CalendlyTokens;

  @Prop({ type: String, required: false })
  calendarId: string;
}

export const CalendlyCredentialSchema =
  SchemaFactory.createForClass(CalendlyCredential);
