import { <PERSON><PERSON>, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

@Schema({ _id: false })
export class AiCredential {
  @Prop()
  secret: string;
}

export const CredentialSchema = SchemaFactory.createForClass(AiCredential);

@Schema()
export class OpenaiCredential {
  @Prop({ type: CredentialSchema })
  creds: AiCredential;
}

export const OpenaiCredentialSchema =
  SchemaFactory.createForClass(OpenaiCredential);
