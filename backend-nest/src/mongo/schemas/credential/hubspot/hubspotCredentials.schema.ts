import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

@Schema({ _id: false })
class HubspotCredentials {
  @Prop({ type: String, required: false })
  accessToken?: string;
  @Prop({ type: String, required: false })
  refreshToken?: string;
}

const CredentialsSchema = SchemaFactory.createForClass(HubspotCredentials);

@Schema()
export class HubspotCredential extends Document {
  @Prop({ type: CredentialsSchema, required: true })
  creds: HubspotCredentials;

  @Prop({ type: String, required: false })
  timezone: string;
}

export const HubspotCredentialSchema =
  SchemaFactory.createForClass(HubspotCredential);
