import { <PERSON>p, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

@Schema({ _id: false })
class SlackCredentials {
  @Prop({ type: String, required: false })
  access_token?: string;
  @Prop({ type: String, required: false })
  token_type?: string;
  @Prop({ type: String, required: false })
  bot_user_id?: string;
  @Prop({ type: String, required: false })
  app_id?: string;

  // Team
  @Prop({ 
    type: {
      name: {type: String, required: false},
      id: {type: String, required: false}
    }, 
    required: false,
    _id: false
  })
  team?: {
    name?: string;
    id?: string;
  };

  // Enterprise
  @Prop({ 
    type: {
      name: {type: String, required: false},
      id: {type: String, required: false}
    }, 
    required: false,
    _id: false
  })
  enterprise?: {
    name?: string;
    id?: string;
  };

  // Authed User
  @Prop({ 
    type: {
      id: {type: String, required: false},
      scope: {type: String, required: false},
      access_token: {type: String, required: false},
      token_type: {type: String, required: false}
    }, 
    required: false,
    _id: false
  })
  authed_user?: {
    id?: string;
    scope?: string;
    access_token?: string;
    token_type?: string;
  };
  
  // Incoming Webhook
  @Prop({ type: {
    channel: {type: String, required: false},
    channel_id: {type: String, required: false},
    configuration_url: {type: String, required: false},
    url: {type: String, required: false}
  }, _id: false, required: false })
  incoming_webhook?: {
    channel?: string;
    channel_id?: string;
    configuration_url?: string;
    url?: string;
  };
}

const CredentialsSchema = SchemaFactory.createForClass(SlackCredentials);

@Schema()
export class SlackCredential extends Document {
  @Prop({ type: CredentialsSchema, required: true })
  creds: SlackCredentials;
}

export const SlackCredentialSchema = SchemaFactory.createForClass(SlackCredential);
