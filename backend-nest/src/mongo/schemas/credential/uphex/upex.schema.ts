import { <PERSON><PERSON>, <PERSON>hem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

@Schema({ _id: false })
export class UphexCreds {
  /* 
  client_id: string;
  client_secret: string;
  account_id: string; // uphex_account_id
  app_token: string; // uphex_app_token
  capri_token: string;
  */
  @Prop({ required: true })
  clientId: string;

  @Prop({ required: true })
  clientSecret: string;

  @Prop({ required: true })
  uphexAppToken: string;
}

export const UphexCredsSchema = SchemaFactory.createForClass(UphexCreds);

@Schema()
export class UphexCredential {
  @Prop({ type: UphexCredsSchema })
  creds: UphexCreds;
}

export const UphexCredentialSchema =
  SchemaFactory.createForClass(UphexCredential);
