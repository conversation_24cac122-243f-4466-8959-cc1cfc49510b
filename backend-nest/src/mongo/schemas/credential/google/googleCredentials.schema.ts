import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

@Schema({ _id: false })
class GoogleTokens {
  @Prop({ type: String, required: false })
  access_token?: string;
  @Prop({ type: String, required: false })
  refresh_token?: string;
  @Prop({ type: String, required: false })
  expiry_date?: number;
  @Prop({ type: String, required: false })
  token_type?: string;
  @Prop({ type: String, required: false })
  id_token?: string;
  @Prop({ required: false })
  scope?: string;
}
const GoogleTokensSchema = SchemaFactory.createForClass(GoogleTokens);

// 
@Schema()
export class GoogleCredential extends Document {
  @Prop({ type: GoogleTokensSchema, required: true })
  creds: GoogleTokens;
}

export const GoogleCredentialSchema =
  SchemaFactory.createForClass(GoogleCredential);
