import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

@Schema({ _id: false })
class PodiumCredentials {
  @Prop({ type: String, required: false })
  accessToken?: string;
  @Prop({ type: String, required: false })
  refreshToken?: string;
}

const CredentialsSchema = SchemaFactory.createForClass(PodiumCredentials);

@Schema()
export class PodiumCredential extends Document {
  @Prop({ type: CredentialsSchema, required: true })
  creds: PodiumCredentials;

  @Prop({ type: String, required: true })
  podiumOrgId: string;

  @Prop({ type: String, required: false })
  webhookId?: string;
}

export const PodiumCredentialSchema =
  SchemaFactory.createForClass(PodiumCredential);
