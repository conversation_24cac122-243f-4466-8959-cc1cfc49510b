// import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
// import { HydratedDocument } from 'mongoose';

// @Schema({ collection: 'fallbacks' })
// export class Fallback {
//   @Prop({ required: true, type: String })
//   orgId: string;

//   @Prop({ required: true, type: String })
//   agentId: string;

//   @Prop({ required: true, type: String })
//   providerName: string;

//   @Prop({ required: true, type: Number })
//   dateTime: Number;

//   @Prop({ required: true, type: String })
//   contactMessage: string;

//   @Prop({ required: true, type: String })
//   aiResponse: string;

//   @Prop({ required: true, type: {executionId: String, executionType: String}, _id: false })
//   executionDetails: {
//     executionId: string;
//     executionType: string;
//   };

//   // @Prop({ required: true, type: String })
//   // conversation: string;

//   @Prop({
//     required: true,
//     type: String,
//     enum: ['unread', 'acknowledged', 'rejected'],
//   })
//   status: 'unread' | 'acknowledged' | 'rejected';

//   @Prop({ required: true, type: String })
//   refUrl: string;
// }

// export type FallbackDocument = HydratedDocument<Fallback>;
// export const FallbackSchema = SchemaFactory.createForClass(Fallback);

//old fallback schema

import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument } from 'mongoose';

@Schema({ collection: 'fallbacks' })
export class Fallback {
  @Prop({ required: true, type: String })
  orgId: string;

  @Prop({ required: true, type: String })
  agentId: string;

  @Prop({ required: true, type: String })
  providerName: string;

  @Prop({ required: true, type: Number })
  dateTime: Number;

  @Prop({ required: true, type: String })
  contactMessage: string;

  @Prop({ required: true, type: String })
  aiResponse: string;

  @Prop({
    required: true,
    type: String,
    enum: ['unread', 'acknowledged', 'rejected'],
  })
  status: 'unread' | 'acknowledged' | 'rejected';

  @Prop({ required: true, type: String })
  refUrl: string;
}

export type FallbackDocument = HydratedDocument<Fallback>;
export const FallbackSchema = SchemaFactory.createForClass(Fallback);