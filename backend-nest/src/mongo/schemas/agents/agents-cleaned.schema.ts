import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose from 'mongoose';
import { HydratedDocument } from 'mongoose';

@Schema()
export class GhlTrigger {
  @Prop()
  subaccount: string;

  @Prop()
  channel: string;

  @Prop()
  tagOption: string;

  @Prop()
  tagValue: string;

  @Prop({
    type: String,
    required: false,
  })
  liveChatWidgetId?: string;
}

@Schema()
export class TagData {
  @Prop()
  tagValue: string;

  @Prop()
  evaluateOn: string;

  @Prop()
  reply: boolean;
}

@Schema()
export class CustomFieldData {
  @Prop()
  fieldKey: string;

  @Prop()
  reply: Boolean;

  @Prop()
  evaluateOn: string;
}

@Schema()
export class MetaData {
  @Prop({ type: TagData, _id: false })
  tagData?: TagData;

  @Prop({ type: CustomFieldData, _id: false })
  customFieldData?: CustomFieldData;
}

@Schema()
export class JsonObject {
  @Prop()
  name?: string;

  @Prop()
  generateCondition?: string;

  @Prop({ type: mongoose.Schema.Types.Mixed })
  properties?: { [key: string]: any }[];
}

@Schema()
export class AdvancedSettings {
  @Prop()
  temperature: number;

  @Prop({
    type: Number,
    default: 1000,
  }) 
  maxLength: number;

  @Prop()
  frequencyPenalty: number;
}

@Schema()
export class AIProvider {
  @Prop()
  companyId: string;

  @Prop()
  modelName: string;

  @Prop()
  accountName: string;

  @Prop()
  accountId: string;

  @Prop()
  isAdvancedSettings: boolean;

  @Prop({ type: AdvancedSettings, _id: false })
  advancedSettings?: AdvancedSettings;
}

@Schema()
export class Prompt {
  @Prop()
  promptId: string;

  @Prop()
  name: string;

  @Prop()
  promptContent: string;

  @Prop()
  fallbackPrompt?: string;

  @Prop()
  fallbackPromptActive?: boolean;

  @Prop()
  isFallbackPrompt: boolean;

  @Prop()
  customFallbackPrompt: string;
}

@Schema()
export class ActionsAdvancedSettings {
  @Prop()
  maxTokensAllowed: number;

  @Prop({
    type: AIProvider,
    _id: false,
  })
  aiProvider: AIProvider;
}

@Schema()
export class Action {
  @Prop()
  actionId: string;

  @Prop()
  accountId: string;

  @Prop()
  providerName: string;

  @Prop()
  promptContent: string;

  @Prop()
  activity: string;

  @Prop()
  accountName: string;

  @Prop()
  silent: boolean;

  @Prop()
  isAdvancedSettings: boolean;

  @Prop({ type: ActionsAdvancedSettings, _id: false })
  advancedSettings?: ActionsAdvancedSettings;

  @Prop({ type: MetaData, _id: false })
  metaData?: MetaData;

  @Prop({ type: JsonObject, _id: false })
  jsonObjects?: JsonObject;
}

@Schema({
  timestamps: true,
})
export class Trigger {
  @Prop()
  triggerId: string;

  @Prop()
  triggerName: string;

  @Prop()
  providerName: string;

  @Prop({ type: GhlTrigger, _id: false })
  data: GhlTrigger;

  @Prop()
  active: boolean;
}

@Schema({ collection: 'agent' })
export class Agent {
  @Prop({
    type: String,
    required: true,
  })
  agentName: string;

  @Prop()
  orgId: string;

  @Prop()
  userId: string;

  @Prop({
    type: Boolean,
    default: true,
  })
  disabled: boolean;

  @Prop({ type: AIProvider, _id: false })
  aiProvider: AIProvider;

  @Prop({ type: [Prompt], _id: false })
  prompts: Prompt[];

  @Prop({ type: [Action], _id: false })
  actions: Action[];

  @Prop({ type: [Trigger], _id: false })
  triggers?: Trigger[];

  @Prop()
  savedSessionIds: string[];
}

export const agentSchema = SchemaFactory.createForClass(Agent);

export type AgentDocument = HydratedDocument<Agent>;
