import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose from 'mongoose';
import { HydratedDocument } from 'mongoose';
import {
  OPTIMIZEOPTIONS,
  CAPRIAIPROVIDER,
  PROVIDERS,
  CUSTOM_LLM_7,
} from 'src/lib/constant';
import { FoldersDocument } from '../folders/folders.schema';
import { Schedule, TimeRange } from 'src/lib/types';

export enum JobType {
  FILE_UPLOAD = 'file-upload',
  WEBSITE_CRAWL = 'website-crawl',
  WEBSITE_SCRAP = 'website-scrap',
}
export interface IGhlTrigger {
  subaccount: string; //this is the accountId of the subaccount
  channel: string;
  tagOption: string;
  tagValue: string;
  task?: string;
  sourceIdentifier?: string; // can be live chat widget id, number, etc
  prompt?: string;
  history?: number;
  include?: string;
  exclude?: string;
  include_knowledge?: string;
  exclude_knowledge?: string;
  tagConditions?: Array<{
    tagOption: 'hasTag' | 'doesntHaveTag';
    tagValue: string;
  }>;
}

export interface TriggerDocument {
  triggerId: string;
  parentTriggerId: string;
  triggerName: string;
  providerName: string;
  data: IGhlTrigger;
  active: boolean;
  followUp?: FollowUpSchema;
}
export interface FAQ {
  id?: string;
  question: string;
  answer: string;
}

export interface TextContentKnowledgeSource {
  accountId: string;
  textContent: string;
}

export interface NotificationSettings {
  connectAgentToTrigger: boolean;
}

export interface FollowUpSchema {
  duration?: number;
  maxAttempts?: number;
  promptId?: string;
  isFollowupEnabled: boolean;
  conditionPrompt?: string;
  timezone?: string;
  schedule?: Schedule;
  tagConditions?: Array<{
    tagOption: 'hasTag' | 'doesntHaveTag';
    tagValue: string;
  }>;
}

const HttpGetParameterSchema = new mongoose.Schema(
  {
    name: { type: String, required: true },
    value: { type: String, required: true },
    description: { type: String, required: true },
  },
  { _id: false },
);

const ApiKeySchema = new mongoose.Schema(
  {
    key: { type: String, required: true },
    value: { type: String, required: true },
    addTo: { type: String, enum: ['Header', 'Query'], required: true },
  },
  { _id: false },
);

const HttpGetSchema = new mongoose.Schema(
  {
    name: { type: String, required: true },
    url: { type: String, required: true },
    queryParameters: { type: [HttpGetParameterSchema], required: true },
    pathVariables: { type: [HttpGetParameterSchema], required: true },
    headers: { type: [HttpGetParameterSchema], required: true },
    authorizationType: {
      type: String,
      enum: ['No Auth', 'API Key', 'OAuth 2.0'],
      required: true,
    },
    apiKey: { type: ApiKeySchema, required: false },
    responseBuilder: { type: String, required: true },
  },
  { _id: false },
);

// Unified schema for both GET and POST HTTP requests
const HttpRequestSchema = new mongoose.Schema(
  {
    name: { type: String, required: true },
    url: { type: String, required: true },
    queryParameters: { type: [HttpGetParameterSchema], required: true },
    pathVariables: { type: [HttpGetParameterSchema], required: true },
    headers: { type: [HttpGetParameterSchema], required: true },
    // Body parameters for POST/PUT requests
    bodyParameters: { type: [HttpGetParameterSchema], required: false },
    // Body content type for POST/PUT requests
    bodyType: { 
      type: String, 
      enum: ['application/json', 'application/x-www-form-urlencoded'], 
      required: false 
    },
    authorizationType: {
      type: String,
      enum: ['No Auth', 'API Key', 'OAuth 2.0'],
      required: true,
    },
    apiKey: { type: ApiKeySchema, required: false },
    responseBuilder: { type: String, required: true },
  },
  { _id: false },
);

const SlackDetailsSchema = new mongoose.Schema(
  {
    type: { type: String, enum: ['hardcoded', 'predictive'], required: true },
    text: { type: String, required: false },
    channel_id: { type: String, required: false },
  },
  { _id: false },
);

const SmsSchema = new mongoose.Schema(
  {
    type: { type: String, enum: ['hardcoded', 'predictive'], required: true },
    text: { type: String, required: false },
    phoneId: { type: String, required: false },
  },
  { _id: false },
);

const GhlEmailSchema = new mongoose.Schema(
  {
    type: { type: String, enum: ['hardcoded', 'predictive'], required: true },
    text: { type: String, required: false },
    subject: { type: String, required: false },
  },
  { _id: false },
);

@Schema({ _id: false })
class ProcessedFile {
  @Prop({ required: true })
  accountId: string;

  @Prop({ required: true })
  fileName: string;

  @Prop()
  mimeType: string;

  @Prop({ required: true, enum: JobType })
  jobType: JobType;

  @Prop()
  maxDepth?: number;

  @Prop({ required: true, default: 0 })
  characterCount: number;

  @Prop({ required: false })
  voiceFileId?: string;
}

const ProcessedFileSchema = SchemaFactory.createForClass(ProcessedFile);

@Schema({ _id: false })
class ProcessingJob {
  @Prop({ required: true })
  jobId: string;

  @Prop({ required: true })
  accountId: string;

  @Prop({ required: true })
  fileName: string;

  @Prop()
  mimeType?: string;

  @Prop({ required: true, enum: JobType })
  jobType: JobType;

  @Prop()
  maxDepth?: number;

  @Prop()
  scraping_id?: string;
}

const ProcessedJobSchema = SchemaFactory.createForClass(ProcessingJob);

const VariableSchema = new mongoose.Schema(
  {
    name: { type: String, required: true },
    type: { type: String, enum: ['static', 'dynamic'], required: true },
    value: {
      type: String,
      required: function () {
        return this.type === 'static';
      },
    },
    providerName: {
      type: String,
      required: function () {
        return this.type === 'dynamic';
      },
    },
    fieldKey: {
      type: String,
      required: function () {
        return this.type === 'dynamic';
      },
    },
  },
  { _id: true },
);

@Schema({ collection: 'agent', discriminatorKey: 'discriminatorTrigger' })
export class Agent {
  @Prop()
  agentName: string;

  @Prop()
  orgId: string;

  @Prop()
  userId: string;

  @Prop({
    required: false,
    default: '',
    index: true,
  })
  snapshotId: string;

  @Prop({
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Folders',
  })
  folderId: FoldersDocument;

  @Prop({
    default: true,
  })
  disabled: boolean;

  @Prop({ type: [ProcessedJobSchema], default: [] })
  processingJobs: ProcessingJob[];

  @Prop({ type: [ProcessedFileSchema], default: [] })
  processedFiles: ProcessedFile[];

  @Prop({
    type: {
      companyId: { type: String, default: PROVIDERS.OPENAI_HOSTED },
      modelName: { type: String, default: CUSTOM_LLM_7.MODEL },
      accountName: { type: String, default: CUSTOM_LLM_7.PUBLIC_MODEL_NAME },
      accountId: { type: String, default: CUSTOM_LLM_7.NAME },
      isAdvancedSettings: { type: Boolean, default: false },
      advancedSettings: {
        temperature: { type: Number, default: 0.3 },
        maxLength: { type: Number, default: 180 },
        frequencyPenalty: { type: Number, default: 0 },
        optimize: { type: String, default: OPTIMIZEOPTIONS.ACCURACY },
      },
    },
    _id: false,
  })
  aiProvider: {
    companyId: string;
    modelName: string;
    accountName: string;
    accountId: string;
    isAdvancedSettings: boolean;
    advancedSettings?: {
      temperature: number;
      maxLength: number;
      frequencyPenalty: number;
      optimize: string;
    };
  };

  @Prop({
    type: {
      failsafe: { type: Boolean, default: false },
      companyId: { type: String, default: 'fireworks' },
      modelName: { type: String, default: 'llama-v3-70b-instruct' },
      accountName: { type: String, default: 'Capri Hosted LLM' },
      accountId: { type: String, default: 'capriHostedLlm' },
    },
    default: {
      failsafe: false,
      companyId: CAPRIAIPROVIDER.COMPANYID,
      modelName: CAPRIAIPROVIDER.MODELNAME,
      accountName: CAPRIAIPROVIDER.ACCOUNTNAME,
      accountId: CAPRIAIPROVIDER.ACCOUNTID,
    },
    _id: false,
  })
  failsafeAiProvider: {
    failsafe: boolean;
    companyId: string;
    modelName: string;
    accountName: string;
    accountId: string;
  };

  @Prop({
    default: false,
    required: false,
  })
  multipleInbound?: boolean;

  @Prop({
    type: {
      initialWait: { type: Number, default: 0, required: false },
      maxWait: { type: Number, default: 10, required: false },
      incrementBy: { type: Number, default: 0, required: false },
    },
    _id: false,
  })
  multipleInboundConfig?: {
    initialWait?: number;
    maxWait?: number;
    incrementBy?: number;
  };

  @Prop({
    type: {
      currentActive: String,
      prompt: [
        {
          promptId: String,
          name: String,
          promptContent: String,
          fallbackPrompt: String,
          fallbackPromptActive: Boolean,
          isFallbackPrompt: Boolean,
          customFallbackPrompt: String,
          _id: false,
        },
      ],
      _id: false,
    },
  })
  prompts: {
    currentActive: string;
    prompt: {
      promptId: string;
      name: string;
      promptContent: string;
      fallbackPrompt?: string;
      fallbackPromptActive?: boolean;
      isFallbackPrompt: boolean;
      customFallbackPrompt: string;
    }[];
  };

  @Prop({
    type: [
      {
        actionId: String,
        accountId: String,
        providerName: String,
        promptContent: String,
        activity: String,
        accountName: String,
        silent: Boolean,
        evalType: { type: String, enum: ["DEFAULT", "ALWAYS", "CONFIDENCE"], default: "DEFAULT" },
        confidenceValue: { type: Number, min: 0, max: 100, default: 70 },
        includeMainPromptDetails: {
          includeMainPrompt: { type: Boolean, default: false },
          mainPromptId: { type: String, default: '' },
        },
        augmentedQueryData: { type: Boolean, default: false },
        isAdvancedSettings: Boolean,
        advancedSettings: {
          maxTokensAllowed: Number,
          aiProvider: {
            companyId: String,
            modelName: String,
            accountName: String,
            accountId: String,
          },
        },
        metaData: {
          tagData: {
            tagValue: {
              type: String,
              default: '',
            },
            evaluateOn: {
              type: String,
              default: '',
            },
            actionSelection: {
              type: String,
              default: '',
            },
            reply: {
              type: Boolean,
              default: false,
            },
          },
          customFieldData: {
            fieldKey: {
              type: String,
              default: '',
            },
            evaluateOn: {
              type: String,
              default: '',
            },
            reply: {
              type: Boolean,
              default: false,
            },
          },
          ghlCalendarMetaData: {
            evaluateOn: {
              type: String,
              default: '',
            },
            dayRange: { type: Number, default: 30 },
            maxRange: { type: Number, default: 30 },
            cancelEvent: { type: Boolean, default: false },
            rescheduleEvent: { type: Boolean, default: false },
          },
          googleCalendarMetaData: {
            cancelEvent: { type: Boolean, default: false },
            rescheduleEvent: { type: Boolean, default: false },
          },
          standardFieldData: {
            fieldKeys: [String],
            reply: Boolean,
            evaluateOn: {
              type: String,
              default: '',
            },
          },
        },
        jsonObjects: {
          type: {
            name: String,
            generateCondition: String,
            properties: [
              {
                type: mongoose.Schema.Types.Mixed,
              },
            ],
          },
          default: undefined, // Making jsonObjects optional
          _id: false,
        },
        httpGetRequestDetails: HttpGetSchema,
        httpRequestDetails: HttpRequestSchema,
        slackDetails: SlackDetailsSchema,
        sms: SmsSchema,
        ghlEmail: GhlEmailSchema,
        voice: {
          type: {
            vapiToolId: {
              type: String,
              required: false,
            },
          },
          _id: false,
          required: false,
        },
        iframeConfig: {
          connected: { type: Boolean, required: false, default: true },
        },
      },
    ],
    _id: false,
  })
  actions: {
    actionId: string;
    accountId: string;
    providerName: string;
    promptContent: string;
    activity: string;
    accountName: string;
    silent: boolean;
    evalType?: "DEFAULT" | "ALWAYS" | "CONFIDENCE";
    confidenceValue?: number;
    augmentedQueryData?: boolean;
    includeMainPromptDetails: {
      includeMainPrompt: boolean;
      mainPromptId: string;
    };
    isAdvancedSettings: boolean;
    advancedSettings?: {
      maxTokensAllowed: number;
      aiProvider: {
        companyId: string;
        modelName: string;
        accountName: string;
        accountId: string;
      };
    };
    metaData?: {
      tagData?: {
        tagValue: string;
        evaluateOn: string;
        reply: boolean;
      };
      customFieldData?: {
        fieldKey: string;
        reply: boolean;
        evaluateOn: string;
      };
      ghlCalendarMetaData: {
        evaluateOn: string;
        dayRange: number;
        maxRange: number;
        cancelEvent: boolean;
        rescheduleEvent: boolean;
      };
      googleCalendarMetaData: {
        cancelEvent: boolean;
        rescheduleEvent: boolean;
      };
      standardFieldData: {
        fieldKeys: string[];
        reply: boolean;
        evaluateOn: string;
      };
    };
    jsonObjects?: {
      name?: string;
      generateCondition?: string;
      properties?: {
        // name: string;
        // description: string;
        // type: string;
        // id: string;
        [key: string]: any;
      }[];
    };
    httpGetRequestDetails?: {
      name: string;
      url: string;
      queryParameters: {
        name: string;
        value: string;
        description: string;
      }[];
      pathVariables: {
        name: string;
        value: string;
        description: string;
      }[];
      headers: {
        name: string;
        value: string;
        description: string;
      }[];
      authorizationType: 'No Auth' | 'API Key' | 'OAuth 2.0';
      apiKey?: {
        key: string;
        value: string;
        addTo: 'Header' | 'Query';
      };
      responseBuilder: string;
    };
    httpRequestDetails?: {
      name: string;
      url: string;
      queryParameters: {
        name: string;
        value: string;
        description: string;
      }[];
      pathVariables: {
        name: string;
        value: string;
        description: string;
      }[];
      headers: {
        name: string;
        value: string;
        description: string;
      }[];
      bodyParameters?: {
        name: string;
        value: string;
        description: string;
      }[];
      bodyType?: 'application/json' | 'application/x-www-form-urlencoded';
      authorizationType: 'No Auth' | 'API Key' | 'OAuth 2.0';
      apiKey?: {
        key: string;
        value: string;
        addTo: 'Header' | 'Query';
      };
      responseBuilder: string;
    };
    voice?: {
      vapiToolId?: string;
    };
    iframeConfig?: {
      connected?: boolean;
    };
  }[];

  slackDetails?: {
    type: 'hardcoded' | 'predictive';
    text?: string;
    fileUrl?: string;
    channel: string;
  };
  sms?: {
    type: 'hardcoded' | 'predictive';
    text?: string;
    phoneId?: string;
  };
  ghlEmail?: {
    type: 'hardcoded' | 'predictive';
    text?: string;
    subject?: string;
  };

  @Prop({
    type: [
      {
        triggerId: String,
        parentTriggerId: {
          type: String,
          required: false,
          default: '',
        },
        triggerName: String,
        providerName: String,
        data: {
          subaccount: { type: String, required: false },
          channel: { type: String, required: false },
          tagOption: { type: String, required: false },
          tagValue: { type: String, required: false },
          liveChatWidget: { type: String, required: false },
          // timeOfDay: { type: Number, required: false },  // Uncomment if you decide to include it later
          task: { type: String, required: false },
          sourceIdentifier: { type: String, required: false },
          prompt: { type: String, required: false },
          history: { type: Number, required: false },
          include: { type: String, required: false },
          exclude: { type: String, required: false },
          include_knowledge: { type: String, required: false },
          exclude_knowledge: { type: String, required: false },
          tagConditions: { type: Array, required: false },
        },
        followUp: {
          type: {
            isFollowupEnabled: { type: Boolean, required: false },
            duration: { type: Number, required: false },
            maxAttempts: { type: Number, required: false },
            promptId: { type: String, required: false },
            conditionPrompt: { type: String, required: false },
            tagConditions: { type: Array, required: false },
            timezone: { type: String, required: false },
            schedule: {
              type: {
                monday: { type: Array<TimeRange>, required: false },
                tuesday: { type: Array<TimeRange>, required: false },
                wednesday: { type: Array<TimeRange>, required: false },
                thursday: { type: Array<TimeRange>, required: false },
                friday: { type: Array<TimeRange>, required: false },
                saturday: { type: Array<TimeRange>, required: false },
                sunday: { type: Array<TimeRange>, required: false },
              },
              required: false,
              default: {},
              _id: false,
            }
          },
          required: false,
          default: {
            isFollowupEnabled: false,
            duration: 15,
            maxAttempts: 2,
            promptId: '',
            conditionPrompt: '',
            timezone: '',
            tagConditions: [],
            schedule: {},
          },
          _id: false,
        },
        active: { type: Boolean, default: false, required: false },
      },
    ],
    _id: false,
  })
  triggers?: TriggerDocument[];

  @Prop()
  savedSessionIds: string[];

  @Prop({
    type: {
      takeoverType: { type: String, required: false },
      tagToAdd: { type: String, required: false },
      timeToWait: { type: Number, required: false },
    },
    _id: false,
    required: false,
  })
  humanTakeover?: {
    takeoverType?: string;
    tagToAdd?: string;
    timeToWait?: number;
  };

  @Prop({
    type: {
      handleEmptyMessage: { type: Boolean, required: false, default: false },
      withoutMessage: {
        enabled: { type: Boolean, required: false, default: false },
        tagToAdd: { type: String, required: false, default: '' },
        setSilent: { type: Boolean, required: false, default: false },
      },
      withMessage: {
        enabled: { type: Boolean, required: false, default: false },
        tagToAdd: { type: String, required: false, default: '' },
        setSilent: { type: Boolean, required: false, default: false },
      },
    },
    _id: false,
    required: false,
  })
  attachmentConfig?: {
    withoutMessage?: {
      enabled?: boolean;
      tagToAdd?: string;
      setSilent?: boolean;
    };
    withMessage?: {
      enabled?: boolean;
      tagToAdd?: string;
      setSilent?: boolean;
    };
  };

  @Prop({
    type: Number,
    required: false,
    default: 1000,
  })
  contextLength: number;

  @Prop({
    type: {
      enabled: { type: Boolean, required: false, default: false },
      tagToAdd: { type: String, required: false, default: '' },
      setSilent: { type: Boolean, required: false, default: false },
    },
    _id: false,
    required: false,
  })
  emptyMessageConfig?: {
    enabled?: boolean;
    tagToAdd?: string;
    setSilent?: boolean;
  };

  @Prop({
    type: {
      enabled: { type: Boolean, required: false, default: false },
      tagToAdd: { type: String, required: false, default: '' },
      setSilent: { type: Boolean, required: false, default: false },
    },
    _id: false,
    required: false,
  })
  fallbackConfig?: {
    enabled?: boolean;
    tagToAdd?: string;
    setSilent?: boolean;
  };

  @Prop({
    required: false,
    _id: false,
    type: {
      assistantId: { type: String, required: false },
      provider: { type: String, default: 'vapi' },
      enabled: { type: Boolean, default: false },
      firstMessage: { type: String, required: false },
      firstMessageMode: { type: String, required: false },
      transferCall: {
        type: {
          transferCallEnabled: { type: Boolean, default: false },
          transferCallToolId: { type: String, required: false },
          transferCallNumber: { type: String, required: false },
          transferMessage: { type: String, required: false },
          transferCallPrompt: { type: String, required: false },
        },
        required: false,
        _id: false,
      },
      model: {
        model: { type: String, required: false },
        provider: { type: String, required: false },
      },
      channel: {
        type: {
          accountId: { type: String },
          providerName: { type: String },
          name: { type: String },
          keyId: { type: String },
        },
        required: false,
        _id: false,
      },
      files: {
        type: [
          {
            providerFileId: { type: String, required: false },
            provider: { type: String, required: false },
          },
        ],
        required: false,
        _id: false,
      },
      tools: {
        type: [
          {
            active: Boolean,
            actionId: String,
            accountId: String,
            providerName: String,
            activity: String,
            accountName: String,
            toolId: String,
          },
        ],
        _id: false,
      },
    },
  })
  voiceConfig?: {
    assistantId: string;
    provider: 'vapi';
    enabled: boolean;
    firstMessage: string;
    firstMessageMode: string;
    transferCall?: {
        transferCallEnabled: boolean;
        transferCallToolId: string;
        transferCallNumber: string;
        transferMessage: string;
        transferCallPrompt: string;
    };
    // fillerInjectionEnabled: true
    model: {
      model: string;
      provider: string;
    };
    channel?: {
      accountId: string;
      providerName: string;
      name: string;
      keyId: string;
    };
    files: {
      providerFileId: string;
      provider: string;
    }[];
    tools: {
      active: boolean;
      actionId: string;
      accountId: string;
      providerName: string;
      activity: string;
      accountName: string;
      toolId: string;
    }[];
  };
  @Prop({
    type: [VariableSchema],
    _id: true,
    default: [],
  })
  variables: {
    _id: string;
    name: string;
    type: 'static' | 'dynamic';
    value?: string;
    providerName?: string;
    fieldKey?: string;
  }[];

  @Prop({
    type: [
      {
        id: { type: String, required: true },
        question: { type: String, required: true },
        answer: { type: String, required: true },
      },
    ],
    default: [],
  })
  faqs: FAQ[];

  @Prop({
    type: {
      accountId: String,
      textContent: String,
    },
    default: null,
    _id: false,
    required: false,
  })
  textContentKnowledgeSource: TextContentKnowledgeSource;

  @Prop({
    type: {
      connectAgentToTrigger: { type: Boolean, default: true },
    },
    _id: false,
    required: false,
    default: {
      connectAgentToTrigger: true,
    },
  })
  showNotifications: NotificationSettings;

  @Prop({
    required: false
  })
  markUnreadAfterReply?: boolean;
}

export const agentSchema = SchemaFactory.createForClass(Agent);

export type AgentDocument = HydratedDocument<Agent>;

// agentSchema.discriminator("GhlTrigger", GhlTriggerSchema);
// agentSchema.discriminator("ChathqTrigger", ChathqTriggerSchema);
