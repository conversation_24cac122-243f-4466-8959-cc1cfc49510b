import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

@Schema({ _id: false })
class Account {
  @Prop({ type: String, required: false })
  chatAccountValue?: string;
}
const AccountSchema = SchemaFactory.createForClass(Account);

@Schema({ _id: false })
class Trigger {
  @Prop({ type: String, required: false })
  chatTriggerValue?: string;
}
const TriggerSchema = SchemaFactory.createForClass(Trigger);
 

@Schema({_id: false})
export class ChathqTriggers extends Document {
  @Prop({ type: AccountSchema, required: true })
  accountDetails: Account;

  @Prop({ type: TriggerSchema, required: true })
  triggerDetails: Trigger;
}

export const ChathqTriggersSchema = SchemaFactory.createForClass(ChathqTriggers);

  @Schema({_id: false})
export class ChathqTrigger extends Document {
  @Prop([{type: ChathqTriggersSchema}])
  triggers: ChathqTriggers[];
}

export const ChathqTriggerSchema = SchemaFactory.createForClass(ChathqTrigger);
