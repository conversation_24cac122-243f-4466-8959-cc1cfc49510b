import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, HydratedDocument } from 'mongoose';
import { GhlTrigger, GhlTriggerSchema } from '../ghlTrigger/ghlTrigger';
import { ChathqTrigger, ChathqTriggerSchema } from '../chathqTrigger/chathqTrigger';
import { agentSchema } from '../agents.schema';

export type HydratedTriggerDocument = HydratedDocument<Trigger> & { 
  accountDetails: {
    [key: string]: string
  },
  triggerDetails: {
    [key: string]: string
  }
};

@Schema({ _id: false, discriminatorKey: 'provider' })
export class Trigger {
  @Prop({
    type: String,
    required: true,
    enum: [GhlTrigger.name, ChathqTrigger.name],
  })
  provider: string;

  @Prop({
    type: String,
    required: true,
  })
  name: string;

}

export const TriggersSchema = SchemaFactory.createForClass(Trigger);

TriggersSchema.discriminator("GhlTrigger", GhlTriggerSchema);
TriggersSchema.discriminator("ChathqTrigger", ChathqTriggerSchema);
