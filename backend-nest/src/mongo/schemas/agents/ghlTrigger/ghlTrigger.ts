import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

@Schema({ _id: false })
export class Account {
  @Prop({ type: String, required: false })
  accountValue?: string;
}
const AccountSchema = SchemaFactory.createForClass(Account);

@Schema({ _id: false })
export class Trigger {
  @Prop({ type: String, required: false })
  ghlValue?: string;
}
const TriggerSchema = SchemaFactory.createForClass(Trigger);
 

@Schema({ _id: false })
export class GhlTriggers extends Document {
  @Prop({ type: AccountSchema, required: true })
  accountDetails: Account;

  @Prop({ type: TriggerSchema, required: true })
  triggerDetails: Trigger;
}

export const GhlTriggersSchema = SchemaFactory.createForClass(GhlTriggers);

@Schema({_id: false})
export class GhlTrigger extends Document {
  @Prop([{type: GhlTriggersSchema}])
  triggers: GhlTriggers[];
}

export const GhlTriggerSchema = SchemaFactory.createForClass(GhlTrigger);