import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose, { HydratedDocument, Document } from 'mongoose';

export type AiHistoryDocument = HydratedDocument<AiHistory>;


@Schema({_id: false})
class aiProvider extends Document{
    @Prop({ required: false })
    credentialId: string;
    @Prop({ required: false })
    accountId: string;
    @Prop({ required: true })
    modelType: string;
    @Prop({ required: true })
    providerName: string;
}
@Schema({_id: false})
class Request extends Document{
    @Prop({ required: true })
    query: string;
    @Prop({ required: true })
    totalTokens_req: string;
}
@Schema({_id: false})
class Response extends Document{
    @Prop({ required: true })
    actionType: string;
    @Prop({ required: true, enum: ['read', 'write', 'ghl', 'message', 'customField', 'tag', 'fallback', 'outreach', 'optimize'] })
    kind: 'read' | 'write' | 'ghl' | 'message' | 'customField' | 'tag' | 'fallback' | 'outreach' | 'optimize';
    @Prop({ required: true })
    totalTokens_res: string;
    @Prop({ required: true, type: Object })
    eventData: object;
}

const RequestSchema = SchemaFactory.createForClass(Request);
const aiProviderSchema = SchemaFactory.createForClass(aiProvider);
const ResponseSchema = SchemaFactory.createForClass(Response);

@Schema({_id: false})
class Action {
    @Prop({ required: true, type: String})
    eventId: string;
    @Prop({ required: true , type: aiProviderSchema, default: {}})
    aiProvider: aiProvider;
    @Prop({ required: true, type: RequestSchema, default: {}})
    request: Request;
    @Prop({ required: true , type: ResponseSchema, default: {}})
    response: Response;
    @Prop({ required: true, type: Number})
    cost: number;
}
const ActionSchema = SchemaFactory.createForClass(Action);



@Schema({ collection: 'aiHistory', timestamps: true })
export class AiHistory {
    @Prop({ required: true })
    agentId: string;

    @Prop({ required: true })
    orgId: string;

    @Prop({ required: true })
    createdAt: Date;

    @Prop({ required: true })
    updatedAt: Date;

    @Prop({ required: true, type:{
        channelName: String,
        channelAccountId: String,
        resourceId: String,
    }, _id: false})
    channel: {
        channelName: string; // account name | channel name
        channelAccountId: string; // specific to the channel
        resourceId: string; // specific to the contact or the session
    };

    @Prop({ required: true, type: [ActionSchema]})
    actions: Action[];
}

export const AiHistorySchema = SchemaFactory.createForClass(AiHistory);
