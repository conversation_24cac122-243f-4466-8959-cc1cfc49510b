import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument } from 'mongoose';

@Schema({
  timestamps: true,
  timeseries: {
    timeField: 'createdAt',
    metaField: 'stripe_customer_id',
    granularity: 'seconds',
  },
})
export class RebillingUsage {
  @Prop({ required: true, type: Number })
  numOfTokensUsed: number;

  @Prop({ required: true, type: Number })
  cost: number;

  @Prop({ required: true, type: Number })
  earned: number;

  @Prop({ required: true, type: Number })
  stripe_units: number;

  @Prop({ required: true, type: String, index: true })
  channel_account_id: string;

  @Prop({ required: true, type: String, index: true })
  rebilling_customer_id: string; //this is the _id generated by mongo
}

export type RebillingUsageDocument = HydratedDocument<RebillingUsage>;
export const RebillingUsageSchema =
  SchemaFactory.createForClass(RebillingUsage);
