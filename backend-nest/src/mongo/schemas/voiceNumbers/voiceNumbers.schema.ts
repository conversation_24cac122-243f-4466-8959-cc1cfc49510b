import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose from 'mongoose';

@Schema({ collection: 'voiceNumbers', timestamps: true })
export class VoiceNumbers {
    @Prop({ type: String, required: true })
    orgId: string;

    @Prop({ type: String, required: true })
    phoneId: string;

    @Prop({ type: String, required: true })
    name: string;

    @Prop({ type: String, required: true })
    phoneNumber: string;

    @Prop({
        type: {
            number:  { type: String, required: true},
            message:  { type: String, required: false},
            description:  { type: String, required: false},
        },
        _id: false,
        required: false,
    })
    fallbackDestination?: {
        number: string;
        message?: string;
        description?: string;
    };

    @Prop({
        type: {
            twilioAccountSid: {type: String, required: false},
            twilioAuthToken: {type: String, required: false},
        },
        _id: false,
        required: false,
    })
    creds?: {
        twilioAccountSid?: string;
        twilioAuthToken?: string;
    }

    @Prop({
        type: {
            transferCallEnabled: { type: Boolean, default: false },
            transferCallNumber: { type: String, required: false },
            transferMessage: { type: String, required: false },
            transferCallPrompt: { type: String, required: false },
        },
        required: false,
        _id: false,
    })
    transfercall?: {
        transferCallEnabled?: boolean;
        transferCallNumber?: string;
        transferMessage?: string;
        transferCallPrompt?: string;
    }

    @Prop({type: String, required: false })
    assistantId?: string;

    @Prop({type: String, required: false })
    agentId?: string;

    @Prop({type: String, required: false })
    provider?: string;
}

export const VoiceNumbersSchema = SchemaFactory.createForClass(VoiceNumbers);
