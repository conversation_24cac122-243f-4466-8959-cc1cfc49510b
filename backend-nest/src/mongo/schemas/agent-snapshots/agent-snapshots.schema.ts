import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument } from 'mongoose';
import mongoose from 'mongoose';
import { v4 as uuidv4 } from 'uuid';

const SnapshotActionSchema = new mongoose.Schema(
  {
    snapshotActionId: { type: String, required: true, default: () => uuidv4() },
    originalActionId: { type: String, required: true },
    providerName: { type: String, required: true },
    promptContent: { type: String, required: false },
    activity: { type: String, required: true },
    accountId: { type: String, required: false },
    accountName: { type: String, required: false },
    silent: { type: Boolean, default: false },
    includeMainPromptDetails: {
      includeMainPrompt: { type: Boolean, default: false },
      mainPromptId: { type: String, default: '' },
    },
    isAdvancedSettings: { type: Boolean, default: false },
    advancedSettings: {
      maxTokensAllowed: Number,
      aiProvider: {
        companyId: String,
        modelName: String,
        accountName: String,
        accountId: String,
      },
    },
    metaData: mongoose.Schema.Types.Mixed,
    jsonObjects: {
      name: String,
      generateCondition: String,
      properties: [
        {
          type: mongoose.Schema.Types.Mixed,
        },
      ],
    },
  },
  { _id: false, minimize: true },
);


const SnapshotPromptSchema = new mongoose.Schema(
  {
    currentActive: { type: String, required: true, default: '' },
    prompt: [
      {
        snapshotPromptId: {
          type: String,
          required: true,
          default: () => uuidv4(),
        },
        originalPromptId: { type: String, required: true },
        name: { type: String, required: true },
        promptContent: { type: String, required: true },
        fallbackPrompt: { type: String, required: false },
        fallbackPromptActive: { type: Boolean, required: false },
        isFallbackPrompt: { type: Boolean, required: false },
        customFallbackPrompt: { type: String, required: false },
      },
    ],
  },
  { _id: false },
);

@Schema({
  collection: 'agent_snapshots',
  timestamps: true,
})
export class AgentSnapshots {
  @Prop({ required: true, index: true })
  orgId: string;

  @Prop({ required: false, default: '', index: true })
  parentAgentId: string;

  @Prop({ required: true })
  name: string;

  @Prop({ required: false, default: '' })
  description: string;

  @Prop({
    type: SnapshotPromptSchema,
  })
  prompts: {
    currentActive: string;
    prompt: {
      snapshotPromptId: string;
      originalPromptId: string;
      name: string;
      promptContent: string;
      fallbackPrompt: string;
      fallbackPromptActive: boolean;
      isFallbackPrompt: boolean;
      customFallbackPrompt: string;
    }[];
  };

  @Prop({ type: [SnapshotActionSchema], default: [] })
  actions: {
    snapshotActionId: string;
    originalActionId: string;
    providerName: string;
    promptContent: string;
    activity: string;
    accountId?: string;
    accountName?: string;
    silent: boolean;
    includeMainPromptDetails: {
      includeMainPrompt: boolean;
      mainPromptId: string;
    };
    isAdvancedSettings: boolean;
    advancedSettings: {
      maxTokensAllowed: number;
      aiProvider: {
        companyId: string;
        modelName: string;
        accountName: string;
        accountId: string;
      };
    };
    metaData?: {
      tagData?: any;
      customFieldData?: any;
      ghlCalendarMetaData?: any;
      googleCalendarMetaData?: any;
      standardFieldData?: any;
    };
    jsonObjects: {
      name: string;
      generateCondition: string;
      properties: any[];
    };
  }[];

  @Prop({ default: false })
  multipleInbound: boolean;

  @Prop({
    type: {
      initialWait: { type: Number, default: 0 },
      maxWait: { type: Number, default: 10 },
      incrementBy: { type: Number, default: 0 },
    },
    _id: false,
  })
  multipleInboundConfig: {
    initialWait: number;
    maxWait: number;
    incrementBy: number;
  };

  @Prop({
    type: {
      takeoverType: { type: String, default: 'None' },
      tagToAdd: String,
      timeToWait: Number,
    },
    _id: false,
  })
  humanTakeover: {
    takeoverType: string;
    tagToAdd: string;
    timeToWait: number;
  };

  @Prop({
    type: {
      handleEmptyMessage: { type: Boolean, default: false },
      withoutMessage: {
        enabled: { type: Boolean, default: false },
        tagToAdd: { type: String, default: '' },
        setSilent: { type: Boolean, default: false },
      },
      withMessage: {
        enabled: { type: Boolean, default: false },
        tagToAdd: { type: String, default: '' },
        setSilent: { type: Boolean, default: false },
      },
    },
    _id: false,
  })
  attachmentConfig: {
    withoutMessage: {
      enabled: boolean;
      tagToAdd: string;
      setSilent: boolean;
    };
    withMessage: {
      enabled: boolean;
      tagToAdd: string;
      setSilent: boolean;
    };
  };

  @Prop({
    type: [
      {
        triggerId: { type: String, default: '' },
        parentTriggerId: { type: String, default: '' },
        triggerName: { type: String, required: true },
        providerName: { type: String, required: true },
        data: {
          subaccount: { type: String, default: '' },
          channel: { type: String, default: '' },
          tagOption: { type: String, default: '' },
          tagValue: { type: String, default: '' },
          liveChatWidget: { type: String, default: '' },
          task: { type: String, default: '' },
          sourceIdentifier: { type: String, default: '' },
          prompt: { type: String, default: '' },
          history: { type: Number, default: 0 },
          include: { type: String, default: '' },
          exclude: { type: String, default: '' },
          include_knowledge: { type: String, default: '' },
          exclude_knowledge: { type: String, default: '' },
          tagConditions: { type: Array, default: [] },
        },
        followUp: {
          isFollowupEnabled: { type: Boolean, default: false },
          duration: { type: Number, default: 15 },
          maxAttempts: { type: Number, default: 2 },
          promptId: { type: String, default: '' },
          conditionPrompt: { type: String, default: '' },
          tagConditions: { type: Array, default: [] },
          timezone: { type: String, default: '' },
          schedule: { type: Object, default: {} },
        },
        active: { type: Boolean, default: false },
      },
    ],
    default: [],
  })
  triggers: {
    triggerId: string;
    parentTriggerId: string;
    triggerName: string;
    providerName: string;
    data: {
      subaccount: string;
      channel: string;
      tagOption: string;
      tagValue: string;
      liveChatWidget: string;
      task: string;
      sourceIdentifier: string;
      prompt: string;
      history: number;
      include: string;
      exclude: string;
      include_knowledge: string;
      exclude_knowledge: string;
      tagConditions: any[];
    };
    followUp: {
      isFollowupEnabled: boolean;
      duration: number;
      maxAttempts: number;
      promptId: string;
      conditionPrompt: string;
      tagConditions: any[];
      timezone: string;
      schedule: any;
    };
    active: boolean;
  }[];

  @Prop({
    type: {
      enabled: { type: Boolean, default: false },
      tagToAdd: { type: String, default: '' },
      setSilent: { type: Boolean, default: false },
    },
    _id: false,
  })
  emptyMessageConfig: {
    enabled: boolean;
    tagToAdd: string;
    setSilent: boolean;
  };

  @Prop({
    type: {
      enabled: { type: Boolean, default: false },
      tagToAdd: { type: String, default: '' },
      setSilent: { type: Boolean, default: false },
    },
    _id: false,
  })
  fallbackConfig: {
    enabled: boolean;
    tagToAdd: string;
    setSilent: boolean;
  };

  @Prop({
    type: Number,
    required: false,
    default: 1000,
  })
  contextLength: number;

  // Sharing-related fields
  @Prop({ default: false })
  isShared: boolean;

  @Prop({ default: false })
  isPublic: boolean;

  @Prop({
    type: [
      {
        orgId: { type: String, required: true },
        sharedBy: { type: String, required: true },
        sharedAt: { type: Date, default: Date.now, required: true },
        acceptedSnapshotInvite: { type: Boolean, default: false },
      },
    ],
    default: [],
  })
  sharedWith: {
    orgId: string;
    sharedBy: string;
    sharedAt: Date;
    acceptedSnapshotInvite: boolean;
  }[];

  @Prop({ type: String, required: false })
  shareLink?: string;

  @Prop({ type: Date, required: false })
  shareLinkExpiry?: Date;
}

export type AgentSnapshotsDocument = HydratedDocument<AgentSnapshots>;
export const AgentSnapshotsSchema =
  SchemaFactory.createForClass(AgentSnapshots);

// Helper function to check if an object is effectively empty (contains only empty strings, empty arrays, or false values)
function isEffectivelyEmpty(obj: any): boolean {
  if (!obj || typeof obj !== 'object') return true;
  
  return Object.values(obj).every(value => {
    if (value === '' || value === false || value === 0) return true;
    if (Array.isArray(value) && value.length === 0) return true;
    if (typeof value === 'object' && value !== null && Object.keys(value).length === 0) return true;
    return false;
  });
}

// Helper function to clean up metaData
function cleanMetaData(actions: any[]) {
  if (actions && Array.isArray(actions)) {
    actions.forEach((action, index) => {
      if (action.metaData) {
        // If metaData is completely empty
        if (typeof action.metaData === 'object' && Object.keys(action.metaData).length === 0) {
          actions[index].metaData = undefined;
        } else if (typeof action.metaData === 'object') {
          // Check and clean each sub-object
          ['tagData', 'customFieldData', 'ghlCalendarMetaData', 'googleCalendarMetaData', 'standardFieldData'].forEach(field => {
            if (action.metaData[field]) {
              // Remove if the sub-object is effectively empty
              if (isEffectivelyEmpty(action.metaData[field])) {
                delete action.metaData[field];
              }
            }
          });
          
          // If after cleaning sub-objects, metaData is empty or effectively empty, remove it
          if (Object.keys(action.metaData).length === 0 || isEffectivelyEmpty(action.metaData)) {
            actions[index].metaData = undefined;
          }
        }
      }
    });
  }
}

// Add a pre-save hook to clean up empty metaData objects in actions array
AgentSnapshotsSchema.pre('save', function(next) {
  cleanMetaData(this.actions);
  next();
});

// Add a pre-update hook to clean up empty metaData objects for findOneAndUpdate operations
AgentSnapshotsSchema.pre(['findOneAndUpdate', 'updateOne', 'updateMany'], function(next) {
  const update = this.getUpdate() as any;
  if (update && update.actions) {
    cleanMetaData(update.actions);
  } else if (update && update.$set && update.$set.actions) {
    cleanMetaData(update.$set.actions);
  }
  next();
});
