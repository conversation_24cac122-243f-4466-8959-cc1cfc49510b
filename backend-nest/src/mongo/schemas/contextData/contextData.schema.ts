import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose, { HydratedDocument } from 'mongoose';

@Schema({ collection: 'contextDatas', timestamps: true })
export class ContextData {
    @Prop({ required: true })
    orgId: string;

    @Prop({ required: true })
    capriContactId: string;

    @Prop({ required: true, index: true })
    externalContactId: string;

    @Prop({ required: true, type: [{
        adTitle: { type: String, required: false },
        adMessage: { type: String, required: false },
        adHeadline: { type: String, required: false },
        source: { type: String, enum: ['uphex'] },
        appId: String
    }], _id: false})
    data: {
        adTitle?: string,
        adMessage?: string,
            adHeadline?: string,
        source: 'uphex';
        appId: string;
    }[];

}

export const ContextDataSchema = SchemaFactory.createForClass(ContextData);
export type HydratedContextData = HydratedDocument<ContextData>;