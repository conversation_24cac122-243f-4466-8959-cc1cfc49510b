import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose, { HydratedDocument } from 'mongoose';
import { UserDocument } from '../user/user.schema';

export type AppUpdatesDocument = HydratedDocument<AppUpdates>;

@Schema({ timestamps: true })
export class AppUpdates {
  @Prop({ required: true })
  header: string;

  @Prop({ required: true })
  text: string;

  @Prop()
  link?: string;

  @Prop()
  htmlContent?: string;

  @Prop({
    type: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
      },
    ],
  })
  viewedBy?: UserDocument[];
}
export const AppUpdatesSchema = SchemaFactory.createForClass(AppUpdates);
