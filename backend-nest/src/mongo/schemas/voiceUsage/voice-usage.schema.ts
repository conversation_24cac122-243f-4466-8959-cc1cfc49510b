import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, Types } from 'mongoose';

@Schema({
  collection: 'voice_usage',
  timestamps: true,
  timeseries: {
    timeField: 'createdAt',
    metaField: 'agent',
    granularity: 'hours',
  },
})
export class VoiceUsage {
  @Prop({
    required: true,
    type: Types.ObjectId,
    ref: 'Organization',
    index: true,
  })
  organization: Types.ObjectId;

  @Prop({
    required: true,
    type: Types.ObjectId,
    ref: 'Agent',
    index: true,
  })
  agent: Types.ObjectId;

  @Prop({ required: true, type: String, index: true })
  voiceProvider: string;

  @Prop({ required: true, type: String })
  provider: string;

  @Prop({ required: true, type: String, index: true })
  model: string;

  @Prop({ required: true, type: Number })
  numOfTokensUsed: number;

  @Prop({ required: true, type: Number })
  numOfActions: number;

  @Prop({ required: true, type: Number })
  numOfKnowledgeSource: number;

  @Prop({ required: true, type: Number })
  numOfSeconds: number;

  @Prop({ required: true, type: Number })
  cost: number; //in cents

  @Prop({required: false, type: String, index: true})
  callId?: string;

  @Prop({ required: true, type: Date, index: true, default: Date.now })
  createdAt?: Date;
}

export type VoiceUsageDocument = HydratedDocument<VoiceUsage>;
export const VoiceUsageSchema = SchemaFactory.createForClass(VoiceUsage);
