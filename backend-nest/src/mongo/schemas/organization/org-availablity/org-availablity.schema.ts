import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export enum DayOfWeek {
  Monday = 'Monday',
  Tuesday = 'Tuesday',
  Wednesday = 'Wednesday',
  Thursday = 'Thursday',
  Friday = 'Friday',
  Saturday = 'Saturday',
  Sunday = 'Sunday',
}

@Schema()
export class OrgAvailability {
  @Prop({ required: true, index: true })
  orgId: string;

  @Prop({ required: true })
  userId: string;

  @Prop()
  calendarId: string;

  @Prop()
  accountId: string;

  @Prop()
  eventName: string;

  @Prop()
  eventDuration: number;

  @Prop()
  eventLocation: string;

  @Prop()
  locationValue: string;

  @Prop()
  eventDescription: string;

  @Prop()
  dateRange: number;

  @Prop({
    default: [
      {
        day: DayOfWeek.Monday,
        timeSlots: [{ start: '09:00', end: '17:00' }],
      },
      {
        day: DayOfWeek.Tuesday,
        timeSlots: [{ start: '09:00', end: '17:00' }],
      },
      {
        day: DayOfWeek.Wednesday,
        timeSlots: [{ start: '09:00', end: '17:00' }],
      },
      {
        day: DayOfWeek.Thursday,
        timeSlots: [{ start: '09:00', end: '17:00' }],
      },
      {
        day: DayOfWeek.Friday,
        timeSlots: [{ start: '09:00', end: '17:00' }],
      },
    ],
  })
  availableHours: Array<{
    day: DayOfWeek;
    timeSlots: Array<{
      start: string;
      end: string;
    }>;
  }>;

  @Prop({ default: 30 })
  startTimeIncrements: number;

  @Prop({default: false})
  acceptTnc: boolean;

  @Prop({default: 1})
  appointment_per_slot: number;
}

export type OrgAvailabilityDocument = OrgAvailability & Document;

export const OrgAvailabilitySchema =
  SchemaFactory.createForClass(OrgAvailability);
