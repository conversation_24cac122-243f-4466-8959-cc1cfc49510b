import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, Document } from 'mongoose';
import {
  OrganizationMembers,
  OrganizationMembersSchema,
} from './members/members.schema';
import {
  OrgAvailability,
  OrgAvailabilitySchema,
} from './org-availablity/org-availablity.schema';
import { PLANS } from 'src/billing/billing.map';
import { VISIBILITY_CODES } from 'src/lib/constant';

export type OrganizationDocument = HydratedDocument<Organization>;

// @Schema({ _id: false })
export class OrganizationSummary extends Document {
  @Prop()
  description: string;

  @Prop()
  location: string;

  @Prop()
  website: string;

  @Prop()
  phone: string;

  @Prop([String])
  usingFor: string[];

  @Prop()
  numOfAgents: number;
}

export const OrganizationSummarySchema =
  SchemaFactory.createForClass(OrganizationSummary);

@Schema({ _id: false })
export class SingleConnections {
  @Prop({ required: true, index: true })
  accountId: string;

  @Prop({ required: false })
  userId?: string;

  @Prop({ required: false })
  usid?: string;

  @Prop({ required: false })
  name?: string;

  @Prop({ required: true })
  providerName: string; //openai //ghl //google

  @Prop({ required: false })
  credentialId?: string;

  @Prop({ required: false })
  headers?: string;

  @Prop({ required: false })
  keyId?: string;

  @Prop({ required: false })
  timezone?: string;

  @Prop({ required: false })
  calendarId?: string;

  @Prop({ required: false })
  useContactTz?: boolean;

  @Prop({ required: false })
  author?: string;

  @Prop({ required: false })
  status?: string; //pending || failed || success

  @Prop({ required: false })
  data?: [any];

  @Prop({ required: false })
  channeluId?: string;

  @Prop({ required: false })
  reference?: string;

  @Prop({ required: false })
  agentIds?: string[];
}

export const ConnectionsSchema =
  SchemaFactory.createForClass(SingleConnections);

@Schema({ _id: false })
export class ThirdPartyAppRecord {
  @Prop({ required: true })
  clientId: string;

  @Prop({ required: true })
  accountId: string; //account id sent by the third party

  @Prop({ required: true })
  token: string; // token we can use to access the third party (NOT CAPRI TOKEN)
}

export const ThirdPartyRecordSchema =
  SchemaFactory.createForClass(ThirdPartyAppRecord);

@Schema({ _id: false })
export class OrganizationConnections extends Document {
  @Prop({ type: [ConnectionsSchema], default: [] })
  channels: SingleConnections[];

  @Prop({ type: [ConnectionsSchema], default: [] })
  dataSources: SingleConnections[];

  @Prop({ type: [ConnectionsSchema], default: [] })
  aiProvider: SingleConnections[];

  @Prop({ type: [ConnectionsSchema], default: [] })
  staticData: SingleConnections[];
}

export const OrganizationConnectionsSchema = SchemaFactory.createForClass(
  OrganizationConnections,
);

@Schema({ _id: false })
export class OrganizationSessions extends Document {
  @Prop({ required: true })
  sessionId: string;
  @Prop({ required: true })
  agentId: string;
  @Prop({ required: true })
  userId: string;
  @Prop({ required: true })
  createdAt: number;
  @Prop({ required: true })
  updatedAt: number;
  @Prop({ required: true })
  active: boolean;
  @Prop({ required: true })
  saved: boolean;
  @Prop({ required: true })
  sessionName: string;
}

export const OrganizationSessionsSchema =
  SchemaFactory.createForClass(OrganizationSessions);

@Schema({ _id: false })
export class Subscription {
  @Prop()
  priceId: string; //returned by stripe. one to one relationship with the plan name
  @Prop()
  subscriptionId: string; //returned by stripe
  // @Prop()
  // nextBillingDate: number; //handled by stripe
}
export const SubscriptionSchema = SchemaFactory.createForClass(Subscription);

@Schema({ _id: false })
export class Autopay {
  @Prop({ default: false, type: Boolean })
  autoRecharge: boolean;
  @Prop({ type: Number, max: 500, min: 1, default: 1 })
  threshold: number; //dollars
  @Prop({ type: Number, max: 500, min: 10, default: 10 })
  rechargeAmount: number; //dollars
}

export const AutopaySchema = SchemaFactory.createForClass(Autopay);

@Schema({ _id: false })
export class Billing {
  @Prop({
    required: true,
    default: 'trial',
    type: String,
    enum: ['trial', 'basic', 'pro', 'enterprise', PLANS.CUSTOMPLAN1, 'custom'],
  })
  plan:
    | 'trial'
    | 'basic'
    | 'pro'
    | 'enterprise'
    | typeof PLANS.CUSTOMPLAN1
    | 'custom';

  @Prop({ default: 'monthly', type: String })
  billingCycle: string;

  @Prop({
    default: 'active',
    type: String,
    enum: ['active', 'cancelled', 'suspended'],
  })
  status: 'active' | 'cancelled' | 'suspended';

  @Prop({ default: false, type: Boolean })
  havePaymentInfo: boolean;

  @Prop({ default: Date.now, type: Number })
  startDate: number; //epoch

  @Prop({ required: true, default: 1 })
  allowedAgents: number;
  // @Prop()
  // billingAmount: number;
  @Prop()
  email: string;
  @Prop()
  userName: string;
  @Prop({ unique: true, index: true })
  customerId: string; //returned by stripe
  @Prop({ type: [SubscriptionSchema] })
  subscriptions: Subscription[];
  @Prop({ type: Number, default: 5 })
  addonAmount: number; //dollars
  @Prop({
    type: AutopaySchema,
    default: {
      autoRecharge: false,
    },
  })
  autoPay: Autopay;
  @Prop({ type: Boolean, default: false })
  eligibleForCustomPlan1: boolean;
}
export const BillingSchema = SchemaFactory.createForClass(Billing);

@Schema({ _id: false })
export class FeatureFlags {
  @Prop({ type: Boolean, default: false })
  voice: boolean;

  @Prop({ type: Boolean, default: true })
  fileUpload: boolean;
}

export const FeatureFlagsSchema = SchemaFactory.createForClass(FeatureFlags);

@Schema({ collection: 'organization', timestamps: true })
export class Organization {
  @Prop()
  name: string;

  @Prop()
  userId: string;

  @Prop({ type: String })
  fpr: string;

  @Prop({ type: String })
  contactId: string;

  @Prop({ type: [String] })
  admin: string[];

  @Prop({ type: OrganizationSummary })
  summary: OrganizationSummary;

  @Prop({ type: [OrganizationMembersSchema] })
  members: OrganizationMembers[];

  @Prop({ type: [String], index: true })
  agents: string[];

  @Prop({ type: OrganizationConnections, default: {} })
  connections: OrganizationConnections;

  @Prop({ type: [OrganizationSessionsSchema], default: [] })
  sessions: OrganizationSessions[];

  @Prop({ type: Billing, default: {} })
  billing: Billing;

  @Prop({ type: [ThirdPartyRecordSchema], default: [] })
  thirdPartyApps: ThirdPartyAppRecord[];

  @Prop({ type: [OrgAvailabilitySchema], default: [] })
  availability: OrgAvailability[];

  @Prop({ type: Boolean, default: false })
  isWhitelisted: boolean;

  @Prop({ type: Boolean, default: false })
  isBlacklisted: boolean;

  @Prop({ type: FeatureFlagsSchema, default: {} })
  featureFlags: FeatureFlags;

  @Prop({
    type: {
      emails: { type: [String], default: [] },
      slackChannelIds: { type: [String], default: [] },
      disabled: { type: Boolean, default: true },
    },
    default: {
      emails: [],
      slackChannelIds: [],
      disabled: true,
    },
  })
  errorReport: {
    emails?: string[];
    slackChannelIds?: string[];
    disabled?: boolean;
  };

  @Prop({
    type: [
      {
        key: { type: String, required: true },
        value: { type: String, required: true },
        type: { type: String, enum: ['static', 'dynamic'], required: true },
        provider: { type: String }, // Only required if type is dynamic, like GHL
      },
    ],
    default: [],
  })
  variables: {
    key: string;
    value: string;
    type: 'static' | 'dynamic';
    provider?: string;
  }[];

  @Prop({
    type: Object,
    default: {
      visibilityCodes: [
        VISIBILITY_CODES.PLAYGROUND.CHAT,
        VISIBILITY_CODES.PLAYGROUND.UPDATE_RESPONSE,
        VISIBILITY_CODES.KNOWLEDGE.FILES,
        VISIBILITY_CODES.KNOWLEDGE.FAQS,
        VISIBILITY_CODES.KNOWLEDGE.TEXT,
        VISIBILITY_CODES.KNOWLEDGE.WEBSITE,
        VISIBILITY_CODES.AGENT.CREATE,
        VISIBILITY_CODES.AGENT.CONVERSATION,
      ],
    },
  })
  iframeConfig: {
    visibilityCodes: string[];
  };
}

export type OrganizationModel = HydratedDocument<Organization>;
export const OrganizationSchema = SchemaFactory.createForClass(Organization);
