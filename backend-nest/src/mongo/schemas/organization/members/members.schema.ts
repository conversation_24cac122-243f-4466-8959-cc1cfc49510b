import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

type TAccess = 'none' | 'read' | 'all';

@Schema({ _id: false })
export class AccessType {
  @Prop({ type: String, default: 'none' })
  accessVal: TAccess;
}

export const AccessTypeSchema = SchemaFactory.createForClass(AccessType);

@Schema({ _id: false })
export class MemberAccess extends Document {
  @Prop({ type: AccessTypeSchema, default: {} })
  fallbacks: AccessType;
  @Prop({ type: AccessTypeSchema, default: {} })
  emulator: AccessType;
  @Prop({ type: AccessTypeSchema, default: {} })
  agents: AccessType;
  @Prop({ type: AccessTypeSchema, default: {} })
  settings: AccessType;
  @Prop({ type: AccessTypeSchema, default: {} })
  billing: AccessType;
}

export const MemberAccessSchema = SchemaFactory.createForClass(MemberAccess);

@Schema({ _id: false })
export class OrganizationMembers extends Document {
  @Prop()
  userId: string;
  @Prop({ required: true, type: MemberAccessSchema })
  access: MemberAccess;
}

export const OrganizationMembersSchema =
  SchemaFactory.createForClass(OrganizationMembers);
