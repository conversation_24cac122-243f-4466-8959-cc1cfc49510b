import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, Types } from 'mongoose';
import {
  OrganizationAccess,
  OrganizationAccessSchema,
} from './userAccess.schema';
import {
  MemberAccess,
  MemberAccessSchema,
} from '../organization/members/members.schema';

@Schema({ _id: false })
export class UserSummary {
  @Prop()
  telephone: string;

  @Prop()
  website: string;

  @Prop()
  location: string;
}

export const UserSummarySchema = SchemaFactory.createForClass(UserSummary);

@Schema({ _id: false })
export class UserCredentials {
  @Prop()
  kind: 'GhlCredential' | 'GoogleCredential' | 'OpenaiCredential';
  @Prop()
  credentialId: string;
}

export const UserCredentialsSchema =
  SchemaFactory.createForClass(UserCredentials);

@Schema({ _id: false })
export class OrgRequest {
  @Prop()
  reqId: string;
  @Prop()
  orgId: string;

  @Prop()
  isAdmin: boolean;

  @Prop({ required: true, type: MemberAccessSchema })
  access: MemberAccess;
}

export const OrgRequestSchema = SchemaFactory.createForClass(OrgRequest);

@Schema({ _id: false })
export class UserRequest {
  @Prop({ type: [OrgRequestSchema], default: [] })
  orgRequests: OrgRequest[];
}

export const UserRequestSchema = SchemaFactory.createForClass(UserRequest);

// @Schema({ _id: false })
// export class OrganizationsList {
//   @Prop()
//   organizationId: string;

//   @Prop({ type: OrganizationAccessSchema })
//   access: OrganizationAccess;
// }

// export const UserOrganizationSchema =
//   SchemaFactory.createForClass(OrganizationsList);

@Schema({ collection: 'user', timestamps: true })
export class User {
  @Prop()
  name: string;

  @Prop()
  userSessionId: string;

  @Prop()
  email: string;

  @Prop()
  profilePic: string;

  @Prop({ default: false })
  organizationSetup: boolean;

  @Prop({ type: [UserCredentialsSchema] })
  credentials: UserCredentials;

  @Prop({ type: UserSummarySchema })
  summary: UserSummary;

  @Prop({ type: UserRequestSchema })
  requests: UserRequest;

  // @Prop({ type: [UserOrganizationSchema] })
  // organizations: OrganizationsList[];

  @Prop({ type: [String] })
  organizations: string[];

  @Prop({ type: String })
  telephone: string;

  @Prop({ type: String })
  pronouns: string;

  @Prop({ type: String })
  timezone: string;

  @Prop({ type: Date })
  birthday: Date;
}

export const userSchema = SchemaFactory.createForClass(User);

export type UserDocument = HydratedDocument<User>;
