import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';

enum AccessType {
  None = 'none',
  Readonly = 'readonly',
  All = 'all',
}

@Schema({ _id: false })
export class OrganizationAccess {
  @Prop({ type: String, enum: AccessType, default: AccessType.None })
  fallbacks: AccessType;

  @Prop({ type: String, enum: AccessType, default: AccessType.None })
  emulator: AccessType;

  @Prop({ type: String, enum: AccessType, default: AccessType.None })
  agents: AccessType;

  @Prop({ type: String, enum: AccessType, default: AccessType.None })
  settings: AccessType;

  @Prop({ type: String, enum: AccessType, default: AccessType.None })
  billing: AccessType;
}

export const OrganizationAccessSchema =
  SchemaFactory.createForClass(OrganizationAccess);
