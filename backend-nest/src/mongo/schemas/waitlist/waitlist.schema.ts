import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument } from 'mongoose';
import { UserRequest, UserRequestSchema } from '../user/user.schema';

@Schema({ collection: 'waitlist' })
export class WaitList {
  @Prop()
  email: string;

  @Prop({ type: UserRequestSchema })
  requests: UserRequest;
}

export type WaitListModal = HydratedDocument<WaitList>;
export const WaitListSchema = SchemaFactory.createForClass(WaitList);
