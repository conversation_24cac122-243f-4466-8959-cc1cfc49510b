import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose, { HydratedDocument } from 'mongoose';
import { OrganizationDocument } from '../organization/organization.schema';

export enum FolderType {
  Agents = 'agents',
  Integrations = 'integrations',
  Channels = 'channels',
  StaticData = 'staticData',
}

@Schema({
  timestamps: true,
})
export class Folders {
  @Prop({
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Organization',
    index: true,
  })
  organization: OrganizationDocument;

  @Prop({ type: String, required: true })
  name: string;

  @Prop({ type: String, enum: FolderType, required: true })
  type: FolderType;

  @Prop({ type: [String], default: [] })
  resourceIds: string[];

  @Prop({
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Folders',
    default: null,
    index: true,
  })
  parent: FoldersDocument;
}

export type FoldersDocument = HydratedDocument<Folders>;
export const FoldersSchema = SchemaFactory.createForClass(Folders);
