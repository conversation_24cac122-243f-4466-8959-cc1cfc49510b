import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { IsEnum } from 'class-validator';
import mongoose, { HydratedDocument } from 'mongoose';

export enum ChargeBy {
  Tokens = 'tokens',
  Message = 'message',
}

export enum Providers {
  PublicApi = 'publicApi',
  Ghl = 'ghl',
}

@Schema({ timestamps: true, _id: true })
export class Members {
  @Prop({ required: true, type: String })
  nickname: string;

  @Prop({ required: true, type: String })
  stripe_customer_id: string;

  @Prop({ type: String, default: '' })
  stripe_subscription_item_id?: string;

  @Prop({ type: Number })
  stripe_unit_price_amount?: number;

  @Prop({ required: true, type: String })
  stripe_product_id: string;

  @Prop({ required: true, type: String })
  stripe_price_id: string;

  @Prop({ required: true, type: String, enum: Providers })
  provider: string;

  @Prop({ required: true, type: String, index: true })
  channel_account_id: string;

  @Prop({ required: true, type: String })
  channel_account_name: string;

  @Prop({ type: String, enum: ChargeBy, default: ChargeBy.Tokens })
  @IsEnum(ChargeBy)
  charge_by: ChargeBy;

  @Prop({ required: true, type: Number, default: 0 })
  aggregate_token_used: number;

  @Prop({ required: true, type: Number, default: 0 })
  last_snapshot_token_count: number; //this is the token count of the last time stripe was billed

  @Prop({ required: true, type: Number, default: 1000 })
  billing_unit: number;

  @Prop({ required: true, type: Number, default: ******** })
  max_usage: number;

  @Prop({ type: String })
  name?: string;

  @Prop({ type: String })
  email?: string;

  @Prop({ type: Boolean, default: false })
  disabled: boolean;
}

export type MembersDocument = HydratedDocument<Members>;
export const MembersSchema = SchemaFactory.createForClass(Members);

@Schema({ timestamps: true })
export class Rebilling {
  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'Organization' })
  organization: mongoose.Types.ObjectId;

  @Prop({ required: true })
  stripe_api_key: string;

  @Prop({ required: true })
  stripe_account_id: string;

  @Prop({ type: [MembersSchema], default: [] })
  members: MembersDocument[];
}

export type RebillingDocument = HydratedDocument<Rebilling>;
export const RebillingSchema = SchemaFactory.createForClass(Rebilling);
