import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>E<PERSON><PERSON>, <PERSON><PERSON>ption<PERSON>, IsString } from 'class-validator';
import { CreateUserAuth0Api } from 'src/user/dto/create-user.dto';

export class CreateOrganizationDto {
  name: string;
  summary: {
    description: string;
    location: string;
    website: string;
    phone: string;
    usingFor: string[];
    numOfAgents?: number;
    howDidYouHear: string;
  };
  userId: string;
  email: string;
  fpr: string;
  userName: string;
  isCustomPlan1?: boolean;
}

export class CreateOrganizationUphexDto extends CreateUserAuth0Api {
  @IsOptional()
  @IsString()
  capriOrgApiKey: string;

  @IsString()
  accountUid: string;

  @IsString()
  @IsOptional()
  orgName: string;
}
