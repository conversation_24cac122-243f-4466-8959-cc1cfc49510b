import {
  Is<PERSON><PERSON>,
  <PERSON>NotEmpty,
  IsUrl,
  ValidateNested,
  IsEmail,
  IsOptional,
} from 'class-validator';
import { Type } from 'class-transformer';

export class OrganizationSummary {
  readonly description?: string;
  readonly location?: string;
  readonly website?: string;
  readonly phone?: string;
  readonly usingFor: string[];
  readonly numOfAgents?: number;
}

export class AccessType {
  readonly accessVal: 'none' | 'read' | 'all';
}

export class Access {
  @Type(() => AccessType)
  @ValidateNested()
  readonly fallbacks: AccessType;
  @Type(() => AccessType)
  @ValidateNested()
  readonly emulator: AccessType;
  @Type(() => AccessType)
  @ValidateNested()
  readonly agents: AccessType;
  @Type(() => AccessType)
  @ValidateNested()
  readonly settings: AccessType;
  @Type(() => AccessType)
  @ValidateNested()
  readonly billing: AccessType;
}

export class Members {
  readonly userId: string;
  @Type(() => Access)
  @ValidateNested()
  readonly access: Access;
}

export class CreateOrganizationDto {
  readonly name: string;
  readonly fpr: string;
  @IsNotEmpty()
  @IsString()
  readonly userId: string;

  // readonly admin: string[];
  // @Type(() => Members)
  // @ValidateNested()
  // readonly members: Members[];
  @Type(() => OrganizationSummary)
  @ValidateNested()
  readonly summary: OrganizationSummary;
}

export class CreateOrganizationWithUserDto extends CreateOrganizationDto {
  @IsNotEmpty()
  @IsEmail()
  readonly email: string;

  @IsOptional()
  @IsString()
  readonly userName: string;
}

export class OrgPropertiesDto extends CreateOrganizationDto {
  readonly admin: string[];
  @Type(() => Members)
  @ValidateNested()
  readonly members: Members[];
}
