import {
  IsString,
  IsNotEmpty,
  IsUrl,
  ValidateNested,
  IsOptional,
  IsEnum,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';
import { UserRequest } from 'src/mongo/schemas/user/user.schema';

class UserSummary {
  @ApiProperty({ description: 'User telephone number', type: String })
  readonly telephone: string;
  @ApiProperty({ description: 'User website', type: String })
  readonly website: string;
  @ApiProperty({ description: 'User location', type: String })
  readonly location: string;
}

export class CreateUserDto {
  @IsNotEmpty()
  @ApiProperty({ description: 'Name of the user', type: String })
  readonly name: string;

  @ApiProperty({ description: 'Email of the user', type: String })
  readonly email: string;

  @ApiProperty({ description: 'Pic URL string', type: String })
  readonly profilePic: string;

  @IsString()
  @IsNotEmpty()
  @ApiProperty({ description: 'User session ID', type: String })
  readonly userSessionId: string;

  @ApiProperty({
    description: 'Indicates whether Organization is set up or not',
    type: Boolean,
  })
  readonly organizationSetup: boolean;

  @Type(() => UserSummary)
  @ValidateNested()
  @ApiProperty({ description: 'User summary', type: UserSummary })
  readonly summary: UserSummary;

  @Type(() => UserRequest)
  @IsOptional()
  @ValidateNested()
  @ApiProperty({ description: 'Pending User requests', type: UserRequest })
  readonly requests: UserRequest;
}

export class UpdateProfileDto {
  @IsNotEmpty()
  @IsString()
  @IsEnum(
    ['name', 'profilePic', 'telephone', 'pronouns', 'timezone', 'birthday'],
    { message: 'You are not allowed to edit this property' },
  )
  key: string;

  @IsNotEmpty()
  @IsString()
  value: string;
}
