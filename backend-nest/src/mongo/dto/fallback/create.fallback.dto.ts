import { IsString, IsNotEmpty, IsUrl, ValidateNested, IsOptional, IsObject, IsIn } from 'class-validator';

export class CreateFallbackDto {
  // @IsString()
  readonly id: string;

  @IsNotEmpty()
  readonly providerName: string;

  @IsOptional()
  readonly dateTime?: string;

  @IsNotEmpty()
  readonly contactMessage: string;

  @IsString()
  readonly aiResponse: string;

  @IsString()
  @IsIn(['unread', 'acknowledged', 'rejected'])
  readonly status: 'unread' | 'acknowledged' | 'rejected';

  @IsString()
  readonly refUrl: string;
}
