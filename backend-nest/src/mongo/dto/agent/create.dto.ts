import { Type } from 'class-transformer';
import {
  IsString,
  IsNotEmpty,
  IsOptional,
  IsBoolean,
  IsArray,
  ValidateNested,
  IsUUID,
} from 'class-validator';

export class FAQDto {
  @IsUUID()
  @IsOptional()
  readonly id?: string;

  @IsString()
  @IsNotEmpty()
  readonly question: string;

  @IsString()
  @IsNotEmpty()
  readonly answer: string;
}

export class CreateAgentDto {
  @IsString()
  readonly agentName: string;

  @IsString()
  @IsOptional()
  readonly snapshotId?: string;

  @IsNotEmpty()
  @IsOptional()
  readonly aiProvider: object;

  @IsOptional()
  readonly prompts?: object;

  @IsNotEmpty()
  @IsOptional()
  readonly actions: object[];

  @IsOptional()
  readonly triggers?: object[];

  @IsNotEmpty()
  @IsString()
  readonly orgId: string;

  @IsBoolean()
  @IsOptional()
  readonly disabled: boolean;

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => FAQDto)
  readonly faqs?: FAQDto[];

  @IsBoolean()
  @IsOptional()
  readonly multipleInbound?: boolean;

  @IsOptional()
  readonly multipleInboundConfig?: {
    initialWait?: number;
    maxWait?: number;
    incrementBy?: number;
  };

  @IsOptional()
  readonly humanTakeover?: {
    takeoverType: string;
    tagToAdd: string;
    timeToWait: number;
  };

  // @IsString()
  // @IsOptional()
  // readonly discriminatorTrigger: string;
}
