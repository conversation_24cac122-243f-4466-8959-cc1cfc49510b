import { IsS<PERSON>, IsNot<PERSON>mpty, IsArray, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

export class FAQWithoutIdDto {
  @IsString()
  @IsNotEmpty()
  readonly question: string;

  @IsString()
  @IsNotEmpty()
  readonly answer: string;
}

export class UpdateFaqDto {
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => FAQWithoutIdDto)
  readonly faqs?: FAQWithoutIdDto[];
}