import {
  IsString,
  IsNotEmpty,
  IsUrl,
  ValidateNested,
  IsOptional,
  IsObject,
  IsBoolean,
} from 'class-validator';

export class ExistingAgentDataDto {
  @IsString()
  readonly agentName: string;

  @IsString()
  @IsOptional()
  readonly folder: string;
 
  @IsString()
  @IsOptional()
  readonly orgId: string;

  @IsObject()
  readonly agentImports: {
    aiProviders: Boolean;
    prompts: <PERSON>olean;
    dataSources: <PERSON>olean;
    savedSessions: Boolean;
  };
}
