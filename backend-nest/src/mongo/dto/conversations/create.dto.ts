import { IsString, IsNotEmpty, IsUrl, ValidateNested, IsOptional } from 'class-validator';

export class CreateConversationDto {
    conversation?: {
        body: string,
        dateAdded: Date,
        direction?: string,
        tokens?: number,
        aiProvider?: string,
        actions?: Object[],
        status?: string,
        messageType?: string,
    }[];
    type?: string;
    contactId: string;
    resourceId: string;
    agentId?: string;
    orgId?: string;
    channel?: string;
    updatedAt?: Date;
    totalTokens?: number;
    rollover?: boolean;
    rolloverReason?: string;
    rolloverDate?: Date;
    source?: string;// workflow, campaign, bulk_actions, api, app
    conversationId?: string;
}
