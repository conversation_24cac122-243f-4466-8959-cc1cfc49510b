import {
  IsString,
  IsNotEmpty,
  IsUrl,
  ValidateNested,
  IsOptional,
} from 'class-validator';
import { KindType } from 'src/lib/constant';

export class CreateCredentialDto {
  @IsString()
  @IsNotEmpty()
  readonly kind: string

  @IsString()
  @IsNotEmpty()
  @IsOptional()
  readonly organizationId?: string;

  @IsString()
  @IsOptional()
  readonly keyId?: string;

  @IsString()
  @IsOptional()
  readonly type?: string;

  readonly creds: any;

  readonly access_token?: string;
  readonly refresh_token?: string;

  // should be removed
  readonly providerName?: string;
  readonly userId?: string;
  readonly name?: string;
  readonly ssoToken?: string;

  readonly timezone?: string;

  @IsString()
  @IsOptional()
  webhookId?: string;

  @IsString()
  @IsOptional()
  podiumOrgId?: string;

  @IsString()
  @IsOptional()
  alertedUser?: boolean;
}
