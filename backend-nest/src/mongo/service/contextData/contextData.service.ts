import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, QueryOptions } from 'mongoose';
import { MyLogger } from 'src/logger/logger.service';
import {
  ContextData,
  HydratedContextData,
} from 'src/mongo/schemas/contextData/contextData.schema';

@Injectable()
export class MongoContextDataService {
  constructor(
    @InjectModel(ContextData.name)
    private readonly contextModel: Model<ContextData>,
    private readonly logger: MyLogger,
  ) {}

  async createContext(context: ContextData): Promise<HydratedContextData> {
    const newContext = new this.contextModel(context);
    return newContext.save();
  }

  async getContextById(id: string): Promise<HydratedContextData> {
    return await this.contextModel.findById(id).exec();
  }

  async updateContext(filter, update, options?: QueryOptions) {
    return await this.contextModel.updateOne(filter, update, options).exec();
  }

  async deleteContext(filter) {
    return await this.contextModel.deleteOne(filter).exec();
  }

  async getContext(
    filter,
    projection = {},
    options = {},
  ): Promise<HydratedContextData> {
    return await this.contextModel.findOne(filter, projection, options).exec();
  }

  async getUphexContextData(ghlContactId: string) {
    try {
      const context = await this.contextModel.findOne({
        externalContactId: ghlContactId,
      });

      if (!context) {
        return null;
      }

      const uphexData = context.data.find((d) => d.source === 'uphex');
      return {
        adTitle: uphexData?.adTitle ?? '',
        adMessage: uphexData?.adMessage ?? '',
        adHeadline: uphexData?.adHeadline ?? '',
      };
    } catch (error) {
      throw error;
    }
  }
}
