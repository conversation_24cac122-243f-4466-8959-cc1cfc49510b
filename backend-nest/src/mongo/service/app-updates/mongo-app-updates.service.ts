import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { MyLogger } from 'src/logger/logger.service';
import {
  AppUpdates,
  AppUpdatesDocument,
} from 'src/mongo/schemas/app-updates/app-updates.schema';
import { MongoUserService } from '../user/mongo-user.service';

@Injectable()
export class MongoAppUpdatesService {
  constructor(
    @InjectModel(AppUpdates.name)
    private readonly appUpdatesModel: Model<AppUpdatesDocument>,
    private readonly logger: MyLogger,
    private readonly mongoUserService: MongoUserService,
  ) {}

  async createAppUpdates(appUpdates: AppUpdates) {
    const newAppUpdates = new this.appUpdatesModel(appUpdates);
    return await newAppUpdates.save();
  }

  async appUpdateSeenByUser(appUpdateId: string, userId: string) {
    

    const user = await this.mongoUserService.getUser({ _id: userId });
    if (!user) {
      throw new NotFoundException('User not found');
    }
    const appUpdate = await this.appUpdatesModel.findByIdAndUpdate(
      { _id: appUpdateId },
      { $addToSet: { viewedBy: user._id } },
      { new: true },
    );
    if (!appUpdate) {
      throw new NotFoundException('App Update not found');
    }
    return appUpdate;
  }

  async getAppUpdatesForUser(userId: string) {
    const user = await this.mongoUserService.getUser({ _id: userId });
    if (!user) {
      throw new NotFoundException('User not found');
    }
    const appUpdates = await this.appUpdatesModel
      .find({ viewedBy: { $nin: user._id } })
      .exec();
    return appUpdates;
  }

  async findRecentUpdateForUser(userId: string) {
    const user = await this.mongoUserService.getUser({ _id: userId });
    if (!user) {
      throw new NotFoundException('User not found');
    }
    const appUpdates = await this.appUpdatesModel
      .find({ viewedBy: { $nin: user._id } })
      .sort({ createdAt: -1 })
      .limit(1)
      .exec();
    
    if (appUpdates.length === 0) {
      return { seen: true };
    }
    return { ...appUpdates[0].toObject(), seen: false };
  }
}
