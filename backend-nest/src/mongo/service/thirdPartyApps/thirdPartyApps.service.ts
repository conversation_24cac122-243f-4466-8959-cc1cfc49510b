import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { MyLogger } from 'src/logger/logger.service';
import { HydratedThirdPartyAppDocument, ThirdPartyApps } from 'src/mongo/schemas/thirdPartyApps/thirdPartyApps.schema';

@Injectable()
export class mongoThirdPartyAppsService {
    constructor(
        @InjectModel(ThirdPartyApps.name)
        private readonly thirdPartyAppModel: Model<HydratedThirdPartyAppDocument>,
        private readonly logger: MyLogger,
    ) { }

    async createThirdPartyApp(context: ThirdPartyApps): Promise<HydratedThirdPartyAppDocument> {
        const newContext = new this.thirdPartyAppModel(context);
        return newContext.save();
    }

    async getThirdPartyAppById(id: string): Promise<HydratedThirdPartyAppDocument> {
        return await this.thirdPartyAppModel.findById(id).exec();
    }

    async getThirdPartyApp(filter, projection = {}, options = {}): Promise<HydratedThirdPartyAppDocument> {
        return await this.thirdPartyAppModel.findOne(filter, projection, options).exec();
    }

    async updateThirdPartyApp(filter, update) {
        return await this.thirdPartyAppModel.updateOne(filter, update).exec();
    }

    async deleteThirdPartyApp(filter) {
        return await this.thirdPartyAppModel.deleteOne(filter).exec();
    }

    async thirdPartyAppExists(filter) {
        return await this.thirdPartyAppModel.exists(filter);
    }
}
