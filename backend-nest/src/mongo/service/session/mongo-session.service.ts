import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { CreateSessionDto } from 'src/session/dto/create-session.dto';
import { Session } from 'src/mongo/schemas/session/session.schema';
import { UpdateSessionName } from 'src/session/dto/updateSessionName_dto';
import { PineconeService } from 'src/vector/services/pinecone/pinecone.service';
import { MyLogger } from 'src/logger/logger.service';

@Injectable()
export class MongoSessionService {
  constructor(
    @InjectModel(Session.name) private readonly sessionModel: Model<Session>,
    private readonly pineconeService: PineconeService,
    private readonly logger: MyLogger,
  ) {}

  async getAllSessions(): Promise<Session[]> {
    try {
      const sessions = await this.sessionModel.find().exec();
      return sessions;
    } catch (error) {
      // 
      this.logger.error({ message: `Error fetching all sessions: ${error?.message}`, context: 'MongoSessionService.getAllSessions' });
      // throw error;
    }
  }

  async getSessions({query=null, projection=null}): Promise<Session[]> {
    try {
      const sessions = await this.sessionModel.find(query, projection).exec();
      return sessions;
    } catch (error) {
      this.logger.error({ message: `Error fetching all sessions: ${error?.message}`, context: 'MongoSessionService.getSessions' });
      // throw error;
    }
  }

  async deleteAllSessionOfOneAgent(agentId){
    try {
      await this.sessionModel.deleteMany({ 'basic.agentId': agentId }).exec();
      this.logger.log({message: `All Sessions deleted successfully.`, context: `${agentId}`});
    } catch (error) {
      this.logger.error({message: `Error deleting sessions: ${error}`, context: 'MongoSessionService.deleteAllSessionOfOneAgent'});
      throw error; // You may choose to handle or propagate the error as needed
    }
  }

  async allSavedSessionOfOneAgent(agentId){
    try {
      let savedSessions : string[] = [];
      const sessions = await this.sessionModel.find({ 'basic.agentId': agentId }).exec();
      for(const session of sessions){
        if(session['_id'] && session.saved){
          savedSessions.push(session['_id'].toString());
        }
      }
      return savedSessions;
    } catch (error) {
      this.logger.error({ message: error?.message, context: 'MongoSessionService.allSavedSessionOfOneAgent' });
    }
  }

  async createDuplicateSession_Duplicate_Agent(sessionDetail) {
    const savedAgent = await this.sessionModel.create(sessionDetail);
    return savedAgent;
}

  async createSession(createSessionDto: CreateSessionDto) {
    const date = Date.now();
    const unixtime = Math.floor(date / 1000);
    const currentDate = new Date();
    const basicData = {
      orgId: createSessionDto.orgId,
      agentId: createSessionDto.agentId,
      userId: createSessionDto.userId,
      sessionName: currentDate.toISOString(),
    };
    const dates = {
      createdAt: unixtime,
      lastUpdated: unixtime,
    };
    const updatedDto = {
      ...createSessionDto,
      basic: basicData,
      dates: dates,
    };

    const newSession = new this.sessionModel(updatedDto);
    const savedSession = await newSession.save();
    return savedSession;
  }

  async getSessionLean(sessionId) {
    try {
        const session = await this.sessionModel
            .findOne({ _id: sessionId })
            .lean() // Add .lean() method to retrieve raw data
            .exec();
        return session;
    } catch (error) {
        throw new Error('Internal Server Error');
    }
}

  //get a session
  async getSession(sessionId) {
    try {
      const session = await this.sessionModel
        .findOne({ _id: sessionId })
        .exec();
      return session;
    } catch (error) {
      throw new Error('Internal Server Error');
    }
  }

  async getOneSession(sessionId) {
    return await this.sessionModel.findOne(sessionId);
  }

  //update session
  async updateSession(sessionId, update, options = {}) {
    const sessionData = await this.sessionModel
      .findByIdAndUpdate(sessionId, update, { new: true, ...options })
      .exec();
    return sessionData;
  }

  async updateSessionDelete(sessionId, updateQuery, arrayFilters) {
    try {
      // Use the Mongoose Model to update the session document
      const updatedSession = await this.sessionModel.findOneAndUpdate(
        { _id: sessionId },
        updateQuery,
        {
          arrayFilters: arrayFilters,
          new: true, // Return the updated document
        },
      );

      return updatedSession;
    } catch (error) {
      throw new Error('Error updating session: ' + error.message);
    }
  }

  async findSessions(payload: {
    filter: any;
    sort: any;
    offset: any;
    limit: any;
    orgId: string;
  }) {
    const { filter, sort, offset, limit } = payload;
    const numericOffset = parseInt(offset, 10);
    const numericLimit = parseInt(limit, 10);
    const sessions = await this.sessionModel.aggregate([
      {
        $match: filter,
      },
      {
        $addFields: {
          eventCount: { $size: '$events' },
        },
      },
      {
        $match: {
          eventCount: { $gt: 0 },
        },
      },
      {
        $sort: sort,
      },
      {
        $skip: numericOffset,
      },
      {
        $limit: numericLimit,
      },
      {
        $project: {
          eventCount: 0,
          events: 0,
        },
      },
    ]);

    return sessions;
  }

  async updateName(sessionId, response, updateSessionDto: UpdateSessionName){
    const sessionDetails = await this.getSession(sessionId);
    if(sessionDetails){
      const updatedSession = await this.sessionModel.findOneAndUpdate(
        {_id: sessionId},
        {
          $set: {
            'basic.sessionName': updateSessionDto.name
          }
        }, 
        {new: true}
      );
      return updatedSession;
    }else{
      return response.status(404).json({
        message: "Session Not Found!"
      })
    }
  }

  async deleteSession(sessionId, response){
    const session = await this.getSession(sessionId);
    if(session){
      const deletedSession = await this.sessionModel.findOneAndDelete({_id: sessionId});
      const orgid = deletedSession.basic.orgId;
      const agentId = deletedSession.basic.agentId;
      if(session.saved){
        // set the delete object
        const deleteObject = {
          namespace: orgid,
          ids: [
          `${sessionId}`
          ],
          filter: {
          agentId: agentId
          },
      }
      //delete index from pinecone
        const index = await this.pineconeService.connectIndex(process.env.PINECONE_INDEX);
      const deletedNamespace = await index.delete1(deleteObject);
      return true;
      }
    }else{
      return response.status(404).json({
        message: "Session not found!"
      })
    }
    return true;
  }

  async deleteMessageEvent(sessionId, eventId) {
    // Use the $pull operator to remove the event directly
    const updatedSession = await this.sessionModel.findOneAndUpdate(
      { _id: sessionId },
      { $pull: { events: { eventId } } }
    );

    if (!updatedSession) {
      throw new NotFoundException('Session or event not found');
    }
    return updatedSession;
  }

}
