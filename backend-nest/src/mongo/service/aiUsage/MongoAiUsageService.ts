import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { AiUsage } from 'src/mongo/schemas/aiUsage/ai-usage.schema';

export interface Metrics {
  models: Array<{ model: string; totalTokensUsed: number; totalCost: number }>;
  totalTokensUsed: number;
  totalCost: number;
  totalMessages: number;
  dailyTokensUsed: {
    date: Date;
    tokens: number;
  }[];
  agentUsage: Array<{
    agentId: string;
    totalTokensUsed: number;
    totalCost: number;
  }>;
  locationUsage: Array<{
    location: string;
    totalTokensUsed: number;
    totalCost: number;
  }>;
}

@Injectable()
export class MongoAiUsageService {
  constructor(
    @InjectModel(AiUsage.name)
    private readonly aiUsageModel: Model<AiUsage>,
  ) {}

  addRecord(record: AiUsage): Promise<AiUsage> {
    try {
      
      const newRecord = new this.aiUsageModel(record);
      return newRecord.save();
    } catch (error) {
      
    }
  }

  async getRecordByOrganizationId({
    organizationId,
    startDate,
    endDate,
  }: {
    organizationId: string;
    startDate?: Date;
    endDate?: Date;
  }) {
    // Default to the last month if dates are not provided
    if (!startDate || !endDate) {
      const now = new Date();
      endDate = new Date(now.getTime());
      startDate = new Date();
      startDate.setMonth(now.getMonth() - 1);
      

      const records = await this.getMetrics(organizationId, startDate, endDate);

      return records;
    } else {
      
      const records = await this.getMetrics(organizationId, startDate, endDate);

      return records;
    }
  }

  async getMetrics(
    organizationId: string,
    startDate: Date,
    endDate: Date,
  ): Promise<Metrics> {
    // Model usage pipeline - no changes
    const modelPipeline = [
      {
        $match: {
          organization: new Types.ObjectId(organizationId),
          createdAt: {
            $gte: startDate,
            $lte: endDate,
          },
        },
      },
      {
        $group: {
          _id: { model: '$model' },
          totalTokensUsed: { $sum: '$numOfTokensUsed' },
          totalCost: { $sum: '$cost' },
        },
      },
      {
        $project: {
          _id: 0,
          model: '$_id.model',
          totalTokensUsed: 1,
          totalCost: 1,
        },
      },
    ];

    // Agent usage pipeline
    const agentPipeline = [
      {
        $match: {
          organization: new Types.ObjectId(organizationId),
          createdAt: {
            $gte: startDate,
            $lte: endDate,
          },
        },
      },
      {
        $group: {
          _id: { agentId: '$agent' },
          totalTokensUsed: { $sum: '$numOfTokensUsed' },
          totalCost: { $sum: '$cost' },
        },
      },
      {
        $project: {
          _id: 0,
          agentId: { $toString: '$_id.agentId' },
          totalTokensUsed: 1,
          totalCost: 1,
        },
      },
    ];

    // Location usage pipeline
    const locationPipeline = [
      {
        $match: {
          organization: new Types.ObjectId(organizationId),
          createdAt: {
            $gte: startDate,
            $lte: endDate,
          },
          location: { $ne: '' }, // Only include records with a location
        },
      },
      {
        $group: {
          _id: { location: '$location' },
          totalTokensUsed: { $sum: '$numOfTokensUsed' },
          totalCost: { $sum: '$cost' },
        },
      },
      {
        $project: {
          _id: 0,
          location: '$_id.location',
          totalTokensUsed: 1,
          totalCost: 1,
        },
      },
    ];

    const models = await this.aiUsageModel.aggregate(modelPipeline);
    const agentUsage = await this.aiUsageModel.aggregate(agentPipeline);
    const locationUsage = await this.aiUsageModel.aggregate(locationPipeline);

    const totalTokensUsed = models.reduce(
      (sum, model) => sum + model.totalTokensUsed,
      0,
    );
    const totalCost = models.reduce((sum, model) => sum + model.totalCost, 0);
    const totalMessages = await this.aiUsageModel.countDocuments({
      organization: new Types.ObjectId(organizationId),
      createdAt: {
        $gte: startDate,
        $lte: endDate,
      },
    });

    const dailyTokensUsed = await this.aiUsageModel
      .aggregate([
        {
          $match: {
            organization: new Types.ObjectId(organizationId),
            createdAt: {
              $gte: startDate,
              $lte: endDate,
            },
          },
        },
        {
          $group: {
            _id: {
              $dateToString: {
                date: '$createdAt',
              },
            },
            totalTokensUsed: { $sum: '$numOfTokensUsed' },
          },
        },
        { $sort: { _id: 1 } },
        {
          $project: {
            _id: 0,
            date: {
              $dateFromString: {
                dateString: '$_id',
              },
            },
            tokens: '$totalTokensUsed',
          },
        },
      ])
      .then((result) => result);

    return {
      models,
      totalTokensUsed,
      totalCost,
      totalMessages,
      dailyTokensUsed,
      agentUsage,
      locationUsage,
    };
  }
  async getTotalCostThisMonth(organizationId: string): Promise<number> {
    const now = new Date();
    const startDate = new Date(now.getFullYear(), now.getMonth(), 1);
    const endDate = new Date(now.getFullYear(), now.getMonth() + 1, 0);

    return 100;
  }
}
