import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { VoiceUsage } from 'src/mongo/schemas/voiceUsage/voice-usage.schema';

@Injectable()
export class MongoVoiceUsageService {
  constructor(
    @InjectModel(VoiceUsage.name)
    private readonly voiceUsageModel: Model<VoiceUsage>,
  ) {}

  async create(voiceUsageData: Partial<VoiceUsage>): Promise<VoiceUsage> {
    const createdVoiceUsage = new this.voiceUsageModel(voiceUsageData);
    return createdVoiceUsage.save();
  }

  async findById(id: string): Promise<VoiceUsage> {
    const voiceUsage = await this.voiceUsageModel.findById(id).exec();
    if (!voiceUsage) {
      throw new NotFoundException(`Voice usage with ID "${id}" not found`);
    }
    return voiceUsage;
  }

  async findByOrganization(organizationId: string): Promise<VoiceUsage[]> {
    return this.voiceUsageModel
      .find({ organization: new Types.ObjectId(organizationId) })
      .exec();
  }

  async findByAgent(agentId: string): Promise<VoiceUsage[]> {
    return this.voiceUsageModel
      .find({ agent: new Types.ObjectId(agentId) })
      .exec();
  }

  async update(
    id: string,
    updateData: Partial<VoiceUsage>,
  ): Promise<VoiceUsage> {
    const updatedVoiceUsage = await this.voiceUsageModel
      .findByIdAndUpdate(id, updateData, { new: true })
      .exec();
    if (!updatedVoiceUsage) {
      throw new NotFoundException(`Voice usage with ID "${id}" not found`);
    }
    return updatedVoiceUsage;
  }

  async delete(id: string): Promise<void> {
    const result = await this.voiceUsageModel.deleteOne({ _id: id }).exec();
    if (result.deletedCount === 0) {
      throw new NotFoundException(`Voice usage with ID "${id}" not found`);
    }
  }

  async getTotalUsageByOrganization(
    organizationId: string,
    startDate: Date,
    endDate: Date,
  ): Promise<any> {
    return this.voiceUsageModel
      .aggregate([
        {
          $match: {
            organization: new Types.ObjectId(organizationId),
            createdAt: { $gte: startDate, $lte: endDate },
          },
        },
        {
          $group: {
            _id: null,
            totalTokens: { $sum: '$numOfTokensUsed' },
            totalActions: { $sum: '$numOfActions' },
            totalKnowledgeSources: { $sum: '$numOfKnowledgeSource' },
            totalMinutes: { $sum: '$numOfMinutes' },
            totalCost: { $sum: '$cost' },
          },
        },
      ])
      .exec();
  }

  async getTotalUsageByAgent(
    agentId: string,
    startDate: Date,
    endDate: Date,
  ): Promise<any> {
    return this.voiceUsageModel
      .aggregate([
        {
          $match: {
            agent: new Types.ObjectId(agentId),
            createdAt: { $gte: startDate, $lte: endDate },
          },
        },
        {
          $group: {
            _id: null,
            totalTokens: { $sum: '$numOfTokensUsed' },
            totalActions: { $sum: '$numOfActions' },
            totalMinutes: { $sum: '$numOfMinutes' },
            totalCost: { $sum: '$cost' },
          },
        },
      ])
      .exec();
  }

  async getUsageByModel(
    organizationId: string,
    startDate: Date,
    endDate: Date,
  ): Promise<any> {
    return this.voiceUsageModel
      .aggregate([
        {
          $match: {
            organization: new Types.ObjectId(organizationId),
            createdAt: { $gte: startDate, $lte: endDate },
          },
        },
        {
          $group: {
            _id: '$model',
            totalTokens: { $sum: '$numOfTokensUsed' },
            totalActions: { $sum: '$numOfActions' },
            totalMinutes: { $sum: '$numOfMinutes' },
            totalCost: { $sum: '$cost' },
          },
        },
      ])
      .exec();
  }

  async getUsageOverTime(
    organizationId: string,
    startDate: Date,
    endDate: Date,
    interval: 'day' | 'week' | 'month',
  ): Promise<any> {
    const groupByDate = {
      day: { $dateToString: { format: '%Y-%m-%d', date: '$createdAt' } },
      week: { $dateToString: { format: '%Y-%U', date: '$createdAt' } },
      month: { $dateToString: { format: '%Y-%m', date: '$createdAt' } },
    };

    return this.voiceUsageModel
      .aggregate([
        {
          $match: {
            organization: new Types.ObjectId(organizationId),
            createdAt: { $gte: startDate, $lte: endDate },
          },
        },
        {
          $group: {
            _id: groupByDate[interval],
            totalTokens: { $sum: '$numOfTokensUsed' },
            totalActions: { $sum: '$numOfActions' },
            totalMinutes: { $sum: '$numOfMinutes' },
            totalCost: { $sum: '$cost' },
          },
        },
        { $sort: { _id: 1 } },
      ])
      .exec();
  }

  async getUsageByCallId(
    callId: string,
  ) {
    return this.voiceUsageModel.findOne({ callId }).exec();
  }
}
