import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { MyLogger } from 'src/logger/logger.service';
import { VoiceNumbers } from 'src/mongo/schemas/voiceNumbers/voiceNumbers.schema';

@Injectable()
export class MongoVoiceNumbersService {
    constructor(
        @InjectModel(VoiceNumbers.name)
        private readonly voiceNumberModel: Model<VoiceNumbers>,
        private readonly logger: MyLogger,
    ) { }

    async createPhone(b: VoiceNumbers): Promise<VoiceNumbers> {
        const newPhone = new this.voiceNumberModel(b);
        return newPhone.save();
    }

    async getPhoneById(id: string): Promise<VoiceNumbers> {
        return await this.voiceNumberModel.findById(id).exec();
    }

    async updatePhone(filter, update) {
        return await this.voiceNumberModel.findOneAndUpdate(filter, update).exec();
    }

    async deletePhone(filter) {
        return await this.voiceNumberModel.findOneAndDelete(filter).exec();
    }

    async getPhones(filter, projection = {}, options = {}): Promise<VoiceNumbers[]> {
        return await this.voiceNumberModel.find(filter, projection, options).exec();
    }

    async getPhone(filter, projection = {}, options = {}): Promise<VoiceNumbers> {
        return await this.voiceNumberModel.findOne(filter, projection, options).exec();
    }
}
