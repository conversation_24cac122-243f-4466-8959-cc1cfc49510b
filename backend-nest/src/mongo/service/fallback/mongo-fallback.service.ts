import { Injectable } from '@nestjs/common';
import { Fallback } from 'src/mongo/schemas/fallback/fallback.schema';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { EditAcknowledgementFallbackDto } from 'src/fallback/dto/edit.fallback.acknowledge.dto';
import { MyLogger } from 'src/logger/logger.service';

@Injectable()
export class MongoFallbackService {
  constructor(
    @InjectModel(Fallback.name) private readonly fallbackModel: Model<Fallback>,
    private readonly logger: MyLogger
  ) {}

  async createFallback(createFallbackDto) {
    try {
      const savedFallback = await this.fallbackModel.create(createFallbackDto);
      return savedFallback;
    } catch (error) {
      // 
      this.logger.error({ message: `Error in creating fallback: ${error?.message}`, context: 'MongoFallbackService.createFallback' });
    }
  }

  async getOneFallback(
    query,
    projection: Object = {},
    options: Object = { lean: true },
  ) {
      const fallback = await this.fallbackModel.findOne(query, projection, options).exec();
      return fallback;
  }

  async getAllFallback(getAllfallbackDto) {
    const allFallbacks = await this.fallbackModel.find().exec();
    const filteredFallbacks = allFallbacks.filter(
      (fallback) => fallback.orgId === getAllfallbackDto.orgId,
    );
    return filteredFallbacks;
  }

  async findFallbacks(payload: { filter: any; offset: number; limit: number }) {
    const { filter, offset, limit } = payload;
    this.logger.log({ message: filter, context: 'MongoFallbackService.findFallbacks' });

    const fallbacks = await this.fallbackModel
      .find(filter)
      .skip(offset)
      .limit(limit)
      .select('-events')
      .exec();

    const totalCount = await this.fallbackModel.countDocuments(filter);

    return {
      data: fallbacks,
      total: totalCount,
    };
  }

  async updateFallbackStatus(editFallbackDto:EditAcknowledgementFallbackDto) {
    const result = await this.fallbackModel
    .updateOne(
      { _id: editFallbackDto.fallbackId },
      { $set: { status: editFallbackDto.status } }
    )
    .exec();

    if (result.upsertedCount > 0) {
      return result;
    } else {
      this.logger.error({ message: 'Fallback does not exist or status was not updated', context: 'MongoFallbackService.updateFallbackStatus' });
    }
  }

  // async addExecutionDetailsToAllFallbacks() {
  //   // Update all documents that do not have the executionDetails field
  //   const updateResult = await this.fallbackModel.updateMany(
  //     {
  //       executionDetails: { $exists: false } // Select documents where executionDetails does not exist
  //     },
  //     {
  //       $set: { // Add executionDetails field
  //         executionDetails: {
  //           executionId: 1234,
  //           executionType: 'ghl'
  //         }
  //       }
  //     }
  //   ).exec();
  
  //   
  // }

}
