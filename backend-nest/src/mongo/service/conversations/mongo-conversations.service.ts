import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { MyLogger } from 'src/logger/logger.service';
import { CreateConversationDto } from 'src/mongo/dto/conversations/create.dto';
import { Conversation, ConversationSchema, ConversationDocument } from 'src/mongo/schemas/conversation/conversation.schema';

@Injectable()
export class MongoConversationService {
    constructor(
        @InjectModel(Conversation.name)
        private readonly conversationModel: Model<Conversation>,
        private readonly logger: MyLogger,
    ) { }

    async getConversations({ query = {}, projection = null, options = null }): Promise<ConversationDocument[]> {
        try {
            return await this.conversationModel.find(query, projection, options).exec();
        } catch (error) {
            this.logger.error({ message: error?.message, context: 'MongoConversationService.getConversations' });
        }
    }

    async getConversation({ query = {}, projection = null, options = null }) {
        try {
            const conversation = await this.conversationModel.findOne(query, projection, options).lean().exec();
            return conversation;
        } catch (error) {
            this.logger.error({ message: error?.message, context: 'MongoConversationService.getConversation' });
            return null;
        }
    }

    async createConversation(createConversationDto: CreateConversationDto): Promise<ConversationDocument> {
        const newConversation = new this.conversationModel(createConversationDto);
        return await newConversation.save();
    }

    async updateConversation({ query = null, updateBody = null, options = { new: true } }) {
        try {
            options['upsert'] = false;
            updateBody['$set'] = { ...(updateBody['$set']||{}), updatedAt: new Date()}
            return await this.conversationModel.updateOne(query, updateBody, options).exec();
        } catch (error) {
            this.logger.error({ message: error?.message, context: 'MongoConversationService.updateConversation' });
        }
    }

    async deleteConversations({ query = null, options = {} }) {
        try {
            return await this.conversationModel.deleteMany(query, options).exec();
        } catch (error) {
            this.logger.error({ message: error?.message, context: 'MongoConversationService.deleteConversations' });
        }
    }

    async isAlreadySaved(query = null) {
        try {
            return await this.conversationModel.exists(query);
        } catch (error) {
            this.logger.error({ message: error?.message, context: 'MongoConversationService.isAlreadySaved' });
        }
    }

    async getPaginatedConversations({ query = {}, projection = null, options = null }, { limit=undefined, page=1, sort=null}) {
        try {
            if (!sort) sort = { updatedAt: -1 };
            const result = await this.conversationModel.find(query, projection, options)
                .limit(limit)
                .skip((page - 1) * limit)
                .sort(sort)
                .lean()
                .exec();
            
            return result;
        } catch (error) {
            this.logger.error({ message: error?.message, context: 'MongoConversationService.getPaginatedConversations' });
            throw error; // Re-throw the error after logging
        }
    }

    async getConversationsCount(query = {}) {
        try {
            return await this.conversationModel.countDocuments(query).exec();
        } catch (error) {
            this.logger.error({ message: error?.message, context: 'MongoConversationService.getConversationsCount' });
            return 0;
        }
    }

    async updateFollowUpAttemptCount({ contactId, count }) {
        try {
            this.logger.log({
                message: `updating follow up attempt count - ${count}`,
                context: `${contactId}`
            })
            return await this.conversationModel.updateOne({ contactId }, { $set: { 'followUp.attemptCount': count } }).exec();
        } catch (error) {
            this.logger.error({ message: `Error while updating follow up attempt count | message - ${error?.message}`, context: 'MongoConversationService.updateFollowUpAttemptCount' });
        }
    }
}
