import { BadRequestException, Injectable } from '@nestjs/common';
import { Model, Types } from 'mongoose';
import { InjectModel } from '@nestjs/mongoose';
import {
  Autopay,
  Organization,
  OrganizationModel,
} from '../../schemas/organization/organization.schema';
import { OrgPropertiesDto } from '../../dto/organizations/create.dto';
import { MemberAccess } from 'src/mongo/schemas/organization/members/members.schema';
import { MyLogger } from 'src/logger/logger.service';
import { MongoAiUsageService } from '../aiUsage/MongoAiUsageService';
import { StripeService } from 'src/billing/stripe.service';
import { roundFloat } from 'helpers/functions';
import {
  FEATURES_WITH_THRESHOLD,
  FeatureWithThreshold,
  THRESHOLD_LIMITS,
} from 'src/billing/billing.constant';
import { CacheService } from 'src/utility/services/cache.service';

@Injectable()
export class MongoOrganizationService {
  constructor(
    @InjectModel(Organization.name)
    private readonly organizationModel: Model<OrganizationModel>,
    @InjectModel(MemberAccess.name)
    private readonly memberAccessModel: Model<MemberAccess>,
    private readonly aiUsageService: MongoAiUsageService,
    private readonly stripeService: StripeService,
    private readonly cacheService: CacheService,
    private readonly logger: MyLogger,
  ) {}

  async createOrganization(createOrganizationDto: OrgPropertiesDto) {
    const newOrganization = new this.organizationModel(createOrganizationDto);
    return await newOrganization.save();
  }

  async getOrganization(
    query: object,
    projection: object = {},
    options: object = { lean: true },
  ) {
    try {
      

      const organization = await this.organizationModel
        .findOne(query, projection, options)
        .exec();
      return organization;
    } catch (error) {
      this.logger.error({
        message: error?.message,
        context: 'MongoOrganizationService.getOrganization',
      });
    }
  }

  async getManyOrganizations(
    query: object,
    projection: object = {},
    options: object = { lean: true },
  ) {
    const organizations = await this.organizationModel
      .find(query, projection, options)
      .exec();
    return organizations;
  }

  async updateOrganization(
    query: object,
    update: object,
    options: object = { new: true },
  ) {
    const org = await this.organizationModel
      .findOneAndUpdate(query, update, options)
      .exec();
    return org;
  }

  async updateManyOrganizations(
    query: object,
    update: object,
    options: object = { new: true },
  ) {
    const org = await this.organizationModel
      .updateMany(query, update, options)
      .exec();
    return org;
  }

  async deleteOrganization(query: object, options: object = {}) {
    const deletedOrg = await this.organizationModel
      .findOneAndDelete(query, options)
      .exec();
    return deletedOrg;
  }

  async getOrganizationDoc(id: string) {
    const organization = await this.organizationModel.findById(id);
    return organization;
  }
  async findSessions(payload: {
    filter: any;
    sort: any;
    offset: number;
    limit: number;
    orgId: string;
  }) {
    const { filter, sort, offset, limit } = payload;
    this.logger.log({
      message: { filter, sort, offset, limit },
      context: 'MongoOrganizationService.findSessions',
    });

    const sessions = await this.organizationModel
      .find(filter)
      .sort(sort)
      .skip(offset)
      .limit(limit)
      .exec();

    return sessions;
  }

  async getOrganizations(query: object, projection = {}, options = {}) {
    const organizations = await this.organizationModel
      .find(query, projection, options)
      .exec();
    return organizations;
  }

  getOrganizationsCursor(filter: any, projection: any) {
    const cursor = this.organizationModel.find(filter, { projection }).cursor();
    return cursor;
  }

  async memberAccessModelClass(query: object) {
    return new this.memberAccessModel(query);
  }

  async findByIdAndUpdate(
    id: string,
    update: object,
    options: object = { new: true },
  ) {
    const org = await this.organizationModel
      .findByIdAndUpdate(id, update, options)
      .exec();
    return org;
  }

  async checkIfOrganizationExists(query: object) {
    const organization = await this.organizationModel.exists(query).exec();
    return organization;
  }

  /**
   * Updates the voice usage for an organization, recharges credits if needed,
   * and updates the organization's balance. The cost of usage is in dollars.
   *
   * @async
   * @function
   * @param {string} orgId - The ID of the organization.
   * @param {Object} usage - The voice usage details.
   * @param {number} usage.cost - The cost of the voice usage in dollars.
   * @returns {Promise<Object>} - Returns an object indicating success or failure.
   * @throws Will log and return an error if the update fails.
   */
  async updateVoiceUsage(orgId: string, usage: { cost: number }) {
    try {
      this.logger.log({
        message: { orgId, usage },
        context: 'MongoOrganizationService.updateVoiceUsage',
      });
      const org = await this.organizationModel.findById(orgId).exec();
      await this.checkAndRechargeCredits(
        org,
        orgId,
        usage.cost,
        FEATURES_WITH_THRESHOLD.CAPRI_HOSTED_VOICE,
      );
      return { success: true };
    } catch (error) {
      this.logger.error({
        message: error.message,
        context: 'MongoOrganizationService.updateVoiceUsage',
      });
      throw error?.message || 'Failed to update voice usage.';
      // return {
      //   success: false,
      //   error: error?.message || 'Failed to update voice usage.',
      // };
    }
  }

  /**
   * Updates the hosted AI usage for an organization, recharges credits if needed,
   * records the AI usage, and updates the organization's balance. The cost of usage is in dollars.
   *
   * @async
   * @function
   * @param {string} orgId - The ID of the organization.
   * @param {string} agentId - The ID of the agent.
   * @param {string} location - The locationId of the subaccount. Pass empty string if the subaccount is not appicable.
   * @param {Object} usage - The hosted AI usage details.
   * @param {number} usage.numOfTokensUsed - The number of tokens used.
   * @param {number} usage.cost - The cost of the hosted AI usage in dollars.
   * @param {string} usage.provider - The AI provider (e.g., OpenAI, Anthropic).
   * @param {string} usage.model - The AI model used.
   * @returns {Promise<Object>} - Returns an object indicating success or throws an error.
   * @throws {BadRequestException} - Throws a BadRequestException if the update fails.
   */
  async updateHostedAiUsage(
    orgId: string,
    agentId: string,
    location: string = '',
    usage: {
      numOfTokensUsed: number;
      cost: number;
      provider: string;
      model: string;
    },
  ) {
    try {
      const org = await this.organizationModel.findById(orgId).exec();
      await this.checkAndRechargeCredits(
        org,
        orgId,
        usage.cost,
        FEATURES_WITH_THRESHOLD.CAPRI_HOSTED_LLM,
      );
      await this.recordAiUsage(orgId, agentId, location, usage);
      await this.updateOrganizationBalance(orgId, usage.cost);
      return { success: true };
    } catch (error) {
      this.logger.error({
        message: error?.message,
        context: 'MongoOrganizationService.updateHostedAiUsage',
      });
      throw new BadRequestException(error.message);
    }
  }

  async checkAndRechargeCredits(
    org: any,
    orgId: string,
    cost: number,
    feature: FeatureWithThreshold,
  ): Promise<void> {
    this.logger.log({
      message: `Starting checkAndRechargeCredits for org: ${orgId}, cost: ${cost}, feature: ${feature}`,
      context: orgId,
    });
    
    const addonAmount = org.billing?.addonAmount || 0;
    if (!addonAmount) {
      this.logger.warn({
        message: `Organization ${orgId} does not have any credits.`,
        context: orgId,
      });
      throw new BadRequestException('Organization does not have any credits.');
    }

    const autoPay = org.billing?.autoPay;
    const thresholdAmount = this.getThresholdAmount(autoPay, feature);
    
    this.logger.log({
      message: `AddonAmount: ${addonAmount}, thresholdAmount: ${thresholdAmount}`,
      context: orgId,
    });

    //check if redis, if there is already a request to recharge, don't do it again
    const redisKey = `autoRecharge:${orgId}`;
    const redisValue = await this.cacheService.getData(redisKey);
    
    this.logger.log({
      message: `Redis check for key ${redisKey}, value exists: ${!!redisValue}`,
      context: orgId,
    });
    
    if (redisValue) {
      this.logger.log({
        message: `Recharge already in progress for org: ${orgId}, skipping.`,
        context: orgId,
      });
      return;
    }

    if (addonAmount - cost < thresholdAmount) {
      this.logger.log({
        message: `Initiating auto-recharge for org: ${orgId}, remaining balance (${addonAmount - cost}) falls below threshold (${thresholdAmount})`,
        context: orgId,
      });
      
      // Set a cache key to indicate recharge is in progress
      await this.cacheService.postData(redisKey, { 
        value: true, 
        ttl: 60000 // 60 seconds (60000ms) expiry
      });
      
      try {
        await this.handleAutoRecharge(org, autoPay, addonAmount);
        this.logger.log({
          message: `Auto-recharge completed successfully for org: ${orgId}`,
          context: orgId,
        });
      } catch (error) {
        this.logger.error({
          message: `Auto-recharge failed for org: ${orgId}: ${error.message}`,
          context: orgId,
        });
        // Delete the cache key if recharge fails
        await this.cacheService.deleteData(redisKey);
        throw error;
      }
    } else {
      this.logger.log({
        message: `No recharge needed for org: ${orgId}, remaining balance (${addonAmount - cost}) is above threshold (${thresholdAmount})`,
        context: orgId,
      });
    }
  }

  private getThresholdAmount(
    autoPay: any,
    feature: FeatureWithThreshold,
  ): number {
    switch (feature) {
      case FEATURES_WITH_THRESHOLD.CAPRI_HOSTED_LLM:
        return !!autoPay && autoPay?.autoRecharge ? autoPay?.threshold : 0;
      case FEATURES_WITH_THRESHOLD.CAPRI_HOSTED_VOICE:
        return THRESHOLD_LIMITS.CAPRI_HOSTED_VOICE;

      default:
        return 0;
    }
  }

  private async handleAutoRecharge(
    org: any,
    autoPay: any,
    addonAmount: number,
  ): Promise<void> {
    this.logger.log({
      message: `Starting handleAutoRecharge for org: ${org._id}, current addonAmount: ${addonAmount}`,
      context: 'MongoOrganizationService.handleAutoRecharge',
    });
    
    if (!!autoPay && autoPay?.autoRecharge) {
      this.logger.log({
        message: `AutoPay is enabled for org: ${org._id}, rechargeAmount: ${autoPay.rechargeAmount}, threshold: ${autoPay.threshold}`,
        context: 'MongoOrganizationService.handleAutoRecharge',
      });
      
      const amount = Math.round(autoPay?.rechargeAmount - addonAmount);
      
      this.logger.log({
        message: `Calculated amount to charge: ${amount} (rechargeAmount: ${autoPay.rechargeAmount} - addonAmount: ${addonAmount})`,
        context: 'MongoOrganizationService.handleAutoRecharge',
      });
      
      try {
        const payment = await this.stripeService.createPaymentIntent(
          org.billing.customerId,
          amount,
        );

        this.logger.log({
          message: { customerId: org.billing.customerId, amount, paymentId: payment?.id },
          context: 'MongoOrganizationService.handleAutoRecharge',
        });

        if (!payment) {
          this.logger.error({
            message: `Payment creation failed for org: ${org._id}, no payment returned`,
            context: 'MongoOrganizationService.handleAutoRecharge',
          });
          throw new BadRequestException('Problem recharging the account.');
        }
        
        this.logger.log({
          message: `Payment successfully created for org: ${org._id}, amount: ${amount}`,
          context: 'MongoOrganizationService.handleAutoRecharge',
        });
      } catch (error) {
        this.logger.error({
          message: `Payment creation failed for org: ${org._id}: ${error.message}`,
          context: 'MongoOrganizationService.handleAutoRecharge',
        });
        throw new BadRequestException(`Problem recharging the account: ${error.message}`);
      }
    } else {
      this.logger.warn({
        message: `AutoPay is disabled for org: ${org._id}, cannot auto-recharge`,
        context: 'MongoOrganizationService.handleAutoRecharge',
      });
      throw new BadRequestException(
        'Organization does not have enough credits.',
      );
    }
  }

  private async recordAiUsage(
    orgId: string,
    agentId: string,
    location: string,
    usage: any,
  ): Promise<void> {
    await this.aiUsageService.addRecord({
      cost: usage.cost * 100,
      numOfTokensUsed: usage.numOfTokensUsed,
      organization: new Types.ObjectId(orgId),
      agent: new Types.ObjectId(agentId),
      location,
      provider: usage.provider,
      model: usage.model,
    });
  }

  private async updateOrganizationBalance(
    orgId: string,
    cost: number,
  ): Promise<void> {
    this.logger.log({
      message: `Updating organization balance for org: ${orgId}, deducting cost: ${cost}`,
      context: 'MongoOrganizationService.updateOrganizationBalance',
    });
    
    try {
      const updatedOrg = await this.organizationModel
        .findByIdAndUpdate(
          orgId,
          {
            $inc: {
              'billing.addonAmount': -1 * cost,
            },
          },
          { new: true },
        )
        .exec();
        
      this.logger.log({
        message: `Balance updated for org: ${orgId}, new balance: ${updatedOrg.billing.addonAmount}`,
        context: 'MongoOrganizationService.updateOrganizationBalance',
      });
    } catch (error) {
      this.logger.error({
        message: `Failed to update balance for org: ${orgId}: ${error.message}`,
        context: 'MongoOrganizationService.updateOrganizationBalance',
      });
      throw error;
    }
  }

  async updateAutoPay(orgId: string, autoPay: Autopay) {
    

    const updatedOrg = await this.organizationModel
      .findByIdAndUpdate(
        orgId,
        {
          'billing.autoPay': autoPay,
        },
        { new: true },
      )
      .exec();

    return updatedOrg;
  }

  async getAddonAmount(orgId: string) {
    const org = await this.organizationModel.findById(orgId).exec();
    const addonAmount = org.billing.addonAmount;
    return roundFloat(addonAmount || 0, 2);
  }
}
