import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Response } from 'express';
import { handleException } from 'helpers/handleException';
import { Model } from 'mongoose';
import { MyLogger } from 'src/logger/logger.service';
import { OrgRequest } from 'src/mongo/schemas/user/user.schema';
import { WaitList } from 'src/mongo/schemas/waitlist/waitlist.schema';
import { NotificationService } from 'src/notification/notification.service';

@Injectable()
export class MongoWaitListService {
  constructor(
    @InjectModel(WaitList.name) private readonly waitListModel: Model<WaitList>,
    private readonly notificationService: NotificationService,
    private readonly logger: MyLogger,
  ) {}

  // async findUserExist(email: string, response: Response) {
  //   const user = await this.waitListModel.findOne({ email });
  //   if (!user) {
  //   }
  //   try {
  //   } catch (error) {
  //     handleException(response, error);
  //   }
  // }

  async pushOrgReq(email: string, orgReq: OrgRequest, name: string) {
    try {
      this.logger.log({ message: {email, orgReq}, context: 'MongoWaitListService.pushOrgReq'});

      const existingDocument = await this.waitListModel.findOne({ email });

      if (existingDocument) {
        const orgRequestExists = existingDocument.requests.orgRequests.some(
          (request) => request.orgId === orgReq.orgId,
        );

        if (orgRequestExists) {
          // If orgRequest with orgId already exists, throw an error
          throw new HttpException(
            'User already have a request from this organization.',
            HttpStatus.CONFLICT,
          );
        } else {
          // If orgRequest with orgId doesn't exist, push orgReq into orgRequests
          existingDocument.requests.orgRequests.push(orgReq);
          await existingDocument.save();
          //send email to the user
          const emailData = {
            to: email,
            subject: 'Capri AI - Request to join organization',
            text: `<body style="font-family: Arial, sans-serif;margin: 40px;">
            <div class="content" style="background-color: #f9f9f9;padding: 20px;border-radius: 10px;">
              <h2>Invitation to Join an Organization on Capri AI</h2>
              <p>Hello,</p>
              <p>We're excited to inform you that you have been invited by ${name} to join their organization on Capri AI.</p>
              <p>To accept the invitation and get started, please follow the link below:</p>
              <a href="${process.env.FRONTEND_URL}/organization?query=requests" class="button" style="display: inline-block;padding: 10px 20px;color: #ffffff;background-color: #007BFF;border: none;border-radius: 5px;text-decoration: none;">Join Organization</a>
              <p>If you have any questions or need further assistance, please don't hesitate to reach out.</p>
              <p>Looking forward to collaborating with you!</p>
            </div>
          </body>
          `,
          };
          // 
          this.logger.log({ message: {emailData, frontend_url: process.env.FRONTEND_URL}, context: 'MongoWaitListService.pushOrgReq' });

          await this.notificationService.create(emailData);
        }
      } else {
        const newDocument = new this.waitListModel({
          email,
          requests: { orgRequests: [orgReq] },
        });
        await newDocument.save();
      }
    } catch (error) {
      // handleException(response, error);
      throw error;
    }
  }

  async getOrgReq(email: string) {
    try {
      this.logger.log({message: email });
      const existingDocument = await this.waitListModel.findOne({ email });
      if (!existingDocument) {
        return;
      }
      const orgReqList = existingDocument.requests.orgRequests;
      this.logger.log({message: orgReqList });
      await this.waitListModel.findOneAndRemove({ email });

      return orgReqList;
    } catch (error) {
      throw new Error(error);
    }
  }
}
