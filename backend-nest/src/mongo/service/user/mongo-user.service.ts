import { Injectable } from '@nestjs/common';
import { User, UserDocument } from '../../schemas/user/user.schema';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { CreateUserDto } from '../../dto/users/create.dto';
import { FindQueryDto } from '../../dto/users/find.dto';
import { MyLogger } from 'src/logger/logger.service';

@Injectable()
export class MongoUserService {
  constructor(
    @InjectModel(User.name) private readonly userModel: Model<User>,
    private readonly logger: MyLogger,
  ) {}

  async createUser(createUserDto: CreateUserDto) {
    this.logger.log({
      message: createUserDto,
      context: 'MongoUserService.createUser',
    });

    const newUser = new this.userModel(createUserDto);
    const savedUser = await newUser.save();
    return savedUser;
  }

  async getUser(
    query: FindQueryDto,
    projection: Object = {},
    options: Object = { lean: true },
  ) {
    const user = await this.userModel
      .findOne(query, projection, options)
      .exec();

    return user;
  }

  async getUsers(
    query: FindQueryDto,
    projection: Object = {},
    options: Object = { lean: true },
  ): Promise<UserDocument[]> {
    return this.userModel.find(query, projection, options).exec();
  }

  async updateUser(
    query: Object,
    update: Object,
    options: Object = { new: true },
  ) {
    const updatedUser = await this.userModel
      .findOneAndUpdate(query, update, options)
      .exec();
    return updatedUser;
  }

  async deleteUser(query: Object, options: Object = {}) {
    const deletedUser = await this.userModel
      .findOneAndDelete(query, options)
      .exec();
    return deletedUser;
  }

  async InsertOrganizationIdInUser(organizationId: string, userId: string) {
    const user = await this.updateUser(
      { _id: userId },
      { organizationSetup: true, $push: { organizations: organizationId } },
    );
    return { organizationSetup: user.organizationSetup };
  }

  async getManyUsers(query: Object, projection: Object = {}) {
    const users = await this.userModel.find(query, projection).exec();
    return users;
  }
}
