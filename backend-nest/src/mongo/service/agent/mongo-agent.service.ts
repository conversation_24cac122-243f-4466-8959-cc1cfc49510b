import { Injectable, forwardRef, Inject, BadRequestException, NotFoundException } from '@nestjs/common';
import { Agent, AgentDocument } from '../../schemas/agents/agents.schema';
import { InjectModel } from '@nestjs/mongoose';
import { FilterQuery, Model, QueryOptions, UpdateQuery } from 'mongoose';
import { CreateAgentDto } from '../../dto/agent/create.dto';
import { FindQueryDto } from '../../dto/agent/find.dto';
import { PushNewPromptDto } from 'src/agent/dto/push-prompt-dto';
import { EditPromptDto } from 'src/agent/dto/edit-prompt-dto';
import { EditActivePromptDto } from 'src/agent/dto/active-prompt-dto';
import { AddDatasourceDto } from 'src/agent/dto/add-datasource-dto';
import { EditDatasourceDto, GetDatasourceDto } from 'src/agent/dto/edit-action-dto';
import { HydratedTriggerDocument } from 'src/mongo/schemas/agents/triggers/agents-triggers.schema';
import { v4 as uuidv4 } from 'uuid';
import { AddTriggerDto } from 'src/agent/dto/add-trigger-dto';
import { UpdateAiProviderDto } from 'src/agent/dto/update-agent-aiProvider-dto';
import { MongoSessionService } from '../session/mongo-session.service';
import { ExistingAgentDataDto } from 'src/mongo/dto/agent/create_duplicate_dto';
import { PineconeService } from 'src/vector/services/pinecone/pinecone.service';
import { MyLogger } from 'src/logger/logger.service';
import { CreateSessionDto } from 'src/session/dto/create-session.dto';
import { SessionService } from 'src/session/session.service';
import { CUSTOM_LLM_7, OPTIMIZEOPTIONS, PROVIDERS } from 'src/lib/constant';
import { EditMainPromptInjectionDto } from 'src/agent/dto/mainPrompt_dto';
import { HttpRequestGetService } from 'src/http-request/GET/get.service';
import { UpdateFailsafeAiProviderDto } from 'src/mongo/dto/agent/failsafe_dto';
import { ToolsService } from 'src/voice/vapi/tools/tools.service';
import { buildCreateAssistantBody, createActionToolProperty } from 'src/voice/vapi/assistants/assistants.helpers';
import { getCustomFieldSubstring } from 'src/lib/utils';
import { HttpRequestPostService } from 'src/http-request/POST/post.service';

interface IDataSources {
    actionId: string;
    accountId: string;
    providerName: string;
    promptContent: string;
    activity: string;
    accountName: string;
    isAdvancedSettings?: Boolean;
    advancedSettings?: {
        aiProvider: {
            companyId: string;
            modelName: string;
            accountName: string;
            accountId: string;
        }
    };
    metaData?: {
        tagData?: {
            tagValue: string;
            evaluateOn: string;
            reply: boolean;
        };
        customFieldData?: {
            fieldKey: string;
            reply: Boolean;
            evaluateOn: string;
        };
    };
}

interface IPrompts {
    currentActive: string;
    prompt: {
        promptId: string;
        name: string;
        promptContent: string;
        fallbackPrompt?: string;
        fallbackPromptActive?: Boolean;
        isFallbackPrompt: Boolean;
        customFallbackPrompt: string;
    }[];
}

@Injectable()
export class MongoAgentService {
  constructor(
    @InjectModel(Agent.name) private readonly agentModel: Model<Agent>,
    private readonly mongoSessionService: MongoSessionService,
    private readonly pineconeService: PineconeService,
    private readonly logger: MyLogger,
    @Inject(forwardRef(() => SessionService))
    private readonly sessionService: SessionService,
    private readonly httpRequestGetService: HttpRequestGetService,
    private readonly toolsService: ToolsService,
    private readonly httpRequestPostService: HttpRequestPostService,
  ) {}

    async createAgent(userId, createAgentDto: CreateAgentDto) {

        const agentName = createAgentDto.agentName;
        const orgId = createAgentDto.orgId;
        const existingAgent = await this.agentModel.findOne({ agentName, orgId }).lean().exec();
        if (existingAgent) {
            throw new BadRequestException('An agent with the same name already exists in the organization');
        }
       
        const agentWithUser = {
            ...createAgentDto,
            userId,
            multipleInbound: createAgentDto.multipleInbound ?? false,
            multipleInboundConfig: createAgentDto.multipleInboundConfig ?? {
                initialWait: 1,
                maxWait: 10,
                incrementBy: 30
            },
            humanTakeover: createAgentDto.humanTakeover ?? {
                takeoverType: 'None',
                tagToAdd: "",
                timeToWait: 2
            },
        };
        
        
      // Set default values for aiProvider if not provided
      if (!agentWithUser.aiProvider) {
        agentWithUser.aiProvider = {
          companyId: PROVIDERS.OPENAI_HOSTED,
          modelName: CUSTOM_LLM_7.MODEL,
          accountName: CUSTOM_LLM_7.PUBLIC_MODEL_NAME,
          accountId: CUSTOM_LLM_7.NAME,
          isAdvancedSettings: false,
          advancedSettings: {
            temperature: 0.3,
            maxLength: 1000,
            frequencyPenalty: 0,
            optimize: OPTIMIZEOPTIONS.ACCURACY,
          },
        };
      }
      const savedAgent = await this.agentModel.create(agentWithUser);
      return savedAgent;
    }

    async createDuplicateAgent(userId, agentId, createDuplicateAgentDto: ExistingAgentDataDto){
        try {
            const agentName = createDuplicateAgentDto.agentName;
            const orgId = createDuplicateAgentDto.orgId;
            const existingAgent = await this.agentModel.findOne({ agentName, orgId }).exec();
            if (existingAgent) {
                throw new BadRequestException('An agent with the same name already exists in the organization');
            }
            let duplicateAgent: any = {};
            const originalAgentData = await this.getAgent({_id: agentId});
            if(createDuplicateAgentDto.agentImports.aiProviders){
                const dupAiProvider: Object = originalAgentData.aiProvider;
                duplicateAgent.aiProvider = dupAiProvider;
            }
            if(createDuplicateAgentDto.agentImports.dataSources){
                const dupDataSource: IDataSources[] = originalAgentData.actions;
                duplicateAgent.actions = originalAgentData.actions;
            }
            if(createDuplicateAgentDto.agentImports.prompts){
                const dupPrompts: IPrompts = originalAgentData.prompts;
                duplicateAgent.prompts = dupPrompts;
            }

            if(createDuplicateAgentDto.agentName){
                duplicateAgent.agentName = createDuplicateAgentDto.agentName;
            }
            duplicateAgent.orgId = originalAgentData.orgId;
            duplicateAgent.disabled = true;
            const savedSessions:string[] = [];
            if (createDuplicateAgentDto.agentImports.savedSessions) {
              let allSavedSession: any[] =
                await this.mongoSessionService.allSavedSessionOfOneAgent(
                  agentId,
                );
              if (allSavedSession.length > 0) {
                duplicateAgent.savedSessionIds = [];

                for (const eachSavedSession of allSavedSession) {
                    savedSessions.push(eachSavedSession);
                }
              }
            }

            duplicateAgent['multipleInboundConfig'] = originalAgentData.multipleInboundConfig;
            duplicateAgent['multipleInbound'] = originalAgentData.multipleInbound;
            duplicateAgent['humanTakeover'] = originalAgentData.humanTakeover;
            if (originalAgentData?.['attachmentConfig'])
              duplicateAgent['attachmentConfig'] =
                originalAgentData?.attachmentConfig;
            if (originalAgentData?.['emptyMessageConfig']){
              duplicateAgent['emptyMessageConfig'] = originalAgentData?.emptyMessageConfig;
            }
            // if (originalAgentData?.['voiceConfig']){
            //   duplicateAgent['voiceConfig'] = originalAgentData?.voiceConfig;
            // }

            const agentWithUser = {
                ...duplicateAgent,
                userId,
            }
            const newAgent = await this.agentModel.create(agentWithUser);
            if (savedSessions.length > 0) {
              try {
                const date = Date.now();
                const unixtime = Math.floor(date / 1000);
                const currentDate = new Date();

                const sessionPromises = savedSessions.map(
                  async (eachSession) => {
                    const sessionDetail =
                      await this.mongoSessionService.getSessionLean(
                        eachSession,
                      );

                    const basicData = {
                      orgId: sessionDetail.basic.orgId,
                      agentId: newAgent._id.toString(),
                      userId: userId,
                      sessionName: currentDate.toISOString(),
                    };

                    const modifiedSession = {
                      basic: basicData,
                      metaData: sessionDetail.metaData,
                      dates: {
                        createdAt: unixtime,
                        lastUpdated: unixtime,
                      },
                      events: sessionDetail.events,
                      active: sessionDetail.active,
                      saved: sessionDetail.saved,
                    };

                    const newSession =
                      await this.mongoSessionService.createDuplicateSession_Duplicate_Agent(
                        modifiedSession,
                      );

                    await this.logger.log({
                      message: 'New duplicate session created.',
                      context: newSession._id.toString(),
                    });
                    await this.sessionService.saveChat(
                      newSession._id.toString(),
                    );

                    return newSession;
                  },
                );

                // Run all the session duplications in parallel
                await Promise.all(sessionPromises);
              } catch (error) {
                this.logger.error({
                  message: `Error duplicating the saved sessions. ${error}`,
                  context: newAgent._id.toString(),
                });
              }
            }

            return newAgent;
        } catch (error) {
            this.logger.error({message: "error creating duplicate agent: "+error.message, context: "MongoAgentService.createDuplicateAgent"});
            throw error;
        }
    }

  async getAgent(
    query: FindQueryDto,
    projection: Object = {},
    options: Object = { lean: true },
  ) {
    const agent = await this.agentModel
      .findOne(query, projection, options)
      .exec();
    return agent;
  }

  async getAgents(
    query: FindQueryDto,
    projection: Object = {},
    options: Object = { lean: true },
  ): Promise<AgentDocument[]> {
    return await this.agentModel.find(query, projection, options).exec();
  }

  async updateAgent(
    query: Object,
    update: Object,
    options: Object = { new: true },
  ) {
    const updatedAgent = await this.agentModel
      .findOneAndUpdate(query, update, options)
      .exec();
    return updatedAgent;
  }

  async updateAgentName(agentId: string, agentName: string) {
    const updatedAgent = await this.agentModel
      .findByIdAndUpdate(agentId, { agentName: agentName }, { new: true })
      .exec();
    return updatedAgent;
  }

    async updateAgentAIProvider(updateAiProviderDto: UpdateAiProviderDto) {
        const { agentId, advancedSettings, aiProvider, isAdvancedSettings, failsafeAiProvider } = updateAiProviderDto;

    const updateQuery = {
      $set: {
        'aiProvider.accountId': aiProvider.accountId,
        'aiProvider.accountName': aiProvider.accountName,
        'aiProvider.companyId': aiProvider.companyId,
        'aiProvider.modelName': aiProvider.modelName,
      },
    };

    if (isAdvancedSettings ?? false) {
      updateQuery.$set['aiProvider.isAdvancedSettings'] = true;
      if (advancedSettings) {
        for (const key in advancedSettings) {
          updateQuery.$set[`aiProvider.advancedSettings.${key}`] =
            advancedSettings[key];
        }
      } else {
        updateQuery.$set['aiProvider.advancedSettings.temperature'] = 0.3;
        updateQuery.$set['aiProvider.advancedSettings.maxLength'] = 180;
        updateQuery.$set['aiProvider.advancedSettings.frequencyPenalty'] = 0;
        updateQuery.$set['aiProvider.advancedSettings.optimize'] =
          OPTIMIZEOPTIONS.ACCURACY;
      }
    } else {
      updateQuery.$set['aiProvider.isAdvancedSettings'] = false;
    }

        if (failsafeAiProvider) {
            updateQuery.$set['failsafeAiProvider.failsafe'] = failsafeAiProvider.failsafe;
            updateQuery.$set['failsafeAiProvider.accountId'] = failsafeAiProvider.accountId;
            updateQuery.$set['failsafeAiProvider.accountName'] = failsafeAiProvider.accountName;
            updateQuery.$set['failsafeAiProvider.companyId'] = failsafeAiProvider.companyId;
            updateQuery.$set['failsafeAiProvider.modelName'] = failsafeAiProvider.modelName;
        } else {
            updateQuery.$set['failsafeAiProvider.failsafe'] = false;
        }

    const result = await this.agentModel.updateOne(
      { _id: agentId },
      updateQuery,
    );

        if (result.modifiedCount > 0) {
            const updatedAgent = await this.agentModel.findById(agentId).exec();
            return updatedAgent;
        } else {
            return null;
        }

    }

    async updateFailsafeAiProvider(
        agentId: string,
        updatePayload: UpdateFailsafeAiProviderDto
      ): Promise<Agent> {
        const updatedAgent = await this.agentModel.findByIdAndUpdate(
          agentId,
          {
            $set: {
                failsafeAiProvider: updatePayload,
            },
          },
          { new: true, runValidators: true }
        ).exec();
    
        if (!updatedAgent) {
          throw new NotFoundException(`Agent with ID ${agentId} not found`);
        }
    
        return updatedAgent;
      }

  async pushNewPrompt(pushNewPromptDto: PushNewPromptDto) {
    try {
      const { agentId, prompt } = pushNewPromptDto;

      const promptId = uuidv4();

      const newPrompt = {
        promptId,
        name: prompt.name,
        promptContent: prompt.promptContent,
        isFallbackPrompt: !!prompt?.isFallbackPrompt,
        customFallbackPrompt: prompt.customFallbackPrompt,
      };

      const agent = await this.agentModel.findOne({ _id: agentId }).exec();

      // Check if prompts.currentActive is not present or is empty
      if (!agent || !agent.prompts || !agent.prompts.currentActive) {
        const updatedAgent = await this.agentModel
          .findOneAndUpdate(
            { _id: agentId },
            {
              $set: {
                'prompts.currentActive': promptId,
              },
              $push: {
                'prompts.prompt': newPrompt,
              },
            },
            { new: true, upsert: true },
          )
          .exec();

        return updatedAgent;
      } else {
        const agent = await this.agentModel
          .findOneAndUpdate(
            { _id: agentId },
            {
              $push: {
                'prompts.prompt': newPrompt,
              },
            },
            { new: true, upsert: true },
          )
          .exec();
        this.logger.log({
          message: 'prompts.currentActive is already present.',
          context: 'MongoAgentService.pushNewPrompt',
        });
        return agent;
      }
    } catch (error) {
      this.logger.error({
        message: error.message,
        context: 'MongoAgentService.pushNewPrompt',
      });
    }
  }

  async editPrompt(editPromptDto: EditPromptDto) {
    const { agentId, promptId, prompt } = editPromptDto;

    const updatedAgent = await this.agentModel.findOneAndUpdate(
      {
        _id: agentId,
        'prompts.prompt.promptId': promptId,
      },
      {
        $set: {
          'prompts.prompt.$.name': prompt.name,
          'prompts.prompt.$.promptContent': prompt.promptContent,
          'prompts.prompt.$.isFallbackPrompt': !!prompt?.isFallbackPrompt,
          'prompts.prompt.$.customFallbackPrompt': prompt.customFallbackPrompt,
        },
      },
      { new: true }, // To return the modified document
    );

    return updatedAgent;
  }

  async editActivePrompt(editActivePromptDto: EditActivePromptDto) {
    const { agentId, promptId } = editActivePromptDto;

    const updatedAgent = await this.agentModel
      .findByIdAndUpdate(
        agentId,
        { $set: { 'prompts.currentActive': promptId } },
        { new: true },
      )
      .exec();

    if (!updatedAgent) {
      throw new Error('Agent not found');
    }

    return updatedAgent;
  }

  async deletePrompt(agentId: string, promptId: string) {
    await this.agentModel
      .findByIdAndUpdate(agentId, {
        $pull: { 'prompts.prompt': { promptId } },
      }, {
        new: true,
      })
      .exec();
    return 1;
  }

  async addDatasources(addDatasourceDto: AddDatasourceDto) {
    try {
      const { agentId, action } = addDatasourceDto;
      const agent = await this.agentModel.findById(agentId);
      if (!agent) {
        throw new Error('Agent not found');
      }

      const defaultActionId = uuidv4();

      let defaultAccountId: string;
      if (action.accountId && action.accountId !== '') {
        defaultAccountId = action.accountId;
      } else {
        defaultAccountId = defaultActionId;
      }

      let defaultSilent: Boolean;
      if (action.silent) {
        defaultSilent = action.silent;
      } else {
        defaultSilent = false;
      }

      const newAction = {
        actionId: defaultActionId,
        accountId: defaultAccountId,
        providerName: action.providerName,
        promptContent: action.promptContent,
        activity: action.activity,
        accountName: action.accountName,
        silent: defaultSilent,
        isAdvancedSettings: action.isAdvancedSettings,
        evalType: action.evalType,
        confidenceValue: action.confidenceValue,
        augmentedQueryData: action.augmentedQueryData,
      };

      let newTool = {
        active: true,
        actionId: defaultActionId,
        accountId: defaultAccountId,
        providerName: action.providerName,
        activity: action.activity,
        accountName: action.accountName,
      }

      if (action.hasOwnProperty('metaData')) {

        const { tagData, customFieldData, ghlCalendarMetaData, googleCalendarMetaData, standardFieldData } = action.metaData;

        newAction['metaData'] = {};

        if (tagData) {
          newAction['metaData'].tagData = {
            tagValue: tagData.tagValue,
            evaluateOn: tagData.evaluateOn,
            reply: tagData.reply,
          };
        }

        if (customFieldData) {
            newAction["metaData"].customFieldData = {
                fieldKey: customFieldData.fieldKey,
                reply: customFieldData.reply,
                evaluateOn: customFieldData.evaluateOn,
            };
        }

        if (ghlCalendarMetaData) {
          newAction["metaData"].ghlCalendarMetaData = {
              evaluateOn: ghlCalendarMetaData.evaluateOn,
              dayRange: ghlCalendarMetaData.dayRange,
              maxRange: ghlCalendarMetaData.maxRange
          };
        }

        if (googleCalendarMetaData) {
          newAction["metaData"].googleCalendarMetaData = {
              cancelEvent: googleCalendarMetaData.cancelEvent,
              rescheduleEvent: googleCalendarMetaData.rescheduleEvent
          };
        }

        // newAction["metaData"] = {};

        // if (action.metaData["tagData"]) {
        //     const tagData = action.metaData["tagData"];
        //     newAction["metaData"]["tagData"] = {
        //         tagValue: tagData.tagValue,
        //         evaluateOn: tagData.evaluateOn,
        //         type: tagData.tagType,
        //     };
        // }

        if (standardFieldData) {
          newAction["metaData"]["standardFieldData"] = standardFieldData;
        }

        // if (action.metaData["fieldData"]) {
        //     const customFieldData = action.metaData["fieldData"];
        //     newAction["metaData"]["customFieldData"] =  {
        //         fieldKey: customFieldData.fieldKey,
        //         reply: customFieldData.reply,
        //         evaluateOn: customFieldData.evaluateOn,
        //     };
        // }
      }

      if (action.hasOwnProperty('jsonObjects')) {
        const newProperties = action.jsonObjects.properties.map((property) => ({
          ...property,
          id: uuidv4(),
        }));

        newAction['jsonObjects'] = {
          name: action.jsonObjects.name,
          generateCondition: action.jsonObjects.generateCondition,
          properties: newProperties,
        };
      }

      if (action.hasOwnProperty('isAdvancedSettings')) {
        if (action.isAdvancedSettings == true) {
          newAction['advancedSettings'] = {
            maxTokensAllowed: action.advancedSettings.maxTokensAllowed,
            aiProvider: {
              companyId: action.advancedSettings.aiProvider.companyId,
              modelName: action.advancedSettings.aiProvider.modelName,
              accountName: action.advancedSettings.aiProvider.accountName,
              accountId: action.advancedSettings.aiProvider.accountId,
            },
          };
        } else {
          newAction['advancedSettings'] = { maxTokensAllowed: 450 };
        }
      }

      if (action.hasOwnProperty('httpGetRequestDetails')) {
        newAction['httpGetRequestDetails'] = action.httpGetRequestDetails;
        await this.httpRequestGetService.update(
          null,
          null,
          action.accountId,
          action.httpGetRequestDetails,
        );
      }

      // Handle unified httpRequestDetails for both GET and POST requests
      if (action.hasOwnProperty('httpRequestDetails')) {
        newAction['httpRequestDetails'] = action.httpRequestDetails;
        
        // Update the appropriate service based on provider name
        if (action.providerName === 'post') {
          await this.httpRequestPostService.update(
            null,
            null,
            action.accountId,
            action.httpRequestDetails,
          );
        }
      }

      

      if (action.includeMainPromptDetails) {
          newAction["includeMainPromptDetails"] = {
              includeMainPrompt: action.includeMainPromptDetails.includeMainPrompt,
              mainPromptId: action.includeMainPromptDetails.mainPromptId
          };
      }

      if (action.hasOwnProperty('slackDetails')) {
        const slackDetails = {
          type: action.slackDetails.type,
          channel_id: action.slackDetails.channel_id,
          text: action.slackDetails.text,
        }
        newAction['slackDetails'] = slackDetails ;
      }

      if (action.hasOwnProperty('sms')) {
        const smsDetails = {
          type: action.sms.type,
          text: action.sms.text,
          phoneId: action.sms.phoneId,
        }

        newAction['sms'] = smsDetails;
      }

      if (action.hasOwnProperty('ghlEmail')) {
        const ghlEmailDetails = {
          type: action.ghlEmail.type,
          text: action.ghlEmail.text || "",
          subject: action.ghlEmail.subject || "",
        }

        newAction['ghlEmail'] = ghlEmailDetails;
      }

      let tool;
      if (agent?.voiceConfig?.assistantId){
        const { properties=null, required=null, serverUrl=null, async} = createActionToolProperty({
          provider: action?.providerName,
          type: action.activity,
          agentId: agentId,
          prompt: getCustomFieldSubstring(action?.metaData?.customFieldData?.fieldKey ?? ''),
          actionId: newAction.actionId
        })
  
        if (serverUrl) tool = await this.toolsService.createTool({
          type: 'function',
          name: newAction.actionId,
          description: newAction.promptContent,
          properties: properties,
          required: required,
          serverUrl: serverUrl,
          async
        });
        if (serverUrl) newTool["toolId"] = tool?.id;
        if (serverUrl) newAction["voice"] = { vapiToolId: tool?.id };
        if (!serverUrl) newTool = undefined;
      }

      const updateBody = {
        $push: {
          actions: newAction,
        },
      }

      if (newTool && agent?.voiceConfig?.enabled){
        updateBody['$push']['voiceConfig.tools'] = newTool
      }

      const updatedAgent = await this.agentModel.findByIdAndUpdate(agentId, updateBody, {
        new: true,
      });

      return updatedAgent;
    } catch (error) {
      this.logger.error({
        message: 'error while adding agent data source. ' + error.message,
        context: 'MongoAgentService.addDatasources',
      });
    }
  }

  async editDatasources(editDatasourceDto: EditDatasourceDto) {
    try {
      const { agentId, actionId, action } = editDatasourceDto;
      const filter = { 
        _id: agentId,
        'actions.actionId': actionId 
      };

      const fieldPrefix = 'actions';
      const update: any = {
        $set: {
          [fieldPrefix + '.$.accountId']: action.accountId,
          [fieldPrefix + '.$.providerName']: action.providerName,
          [fieldPrefix + '.$.promptContent']: action.promptContent,
          [fieldPrefix + '.$.activity']: action.activity,
          [fieldPrefix + '.$.accountName']: action.accountName,
          [fieldPrefix + '.$.silent']: action.silent || false,
          [fieldPrefix + '.$.isAdvancedSettings']: action?.isAdvancedSettings,
          [fieldPrefix + '.$.evalType']: action.evalType || 'DEFAULT',
          [fieldPrefix + '.$.confidenceValue']: action.confidenceValue !== undefined ? action.confidenceValue : 70,
          [fieldPrefix + '.$.augmentedQueryData']: action.augmentedQueryData,
        },
      };

      if (action.metaData) {
        if (action.metaData.tagData) {
          update.$set['actions.$.metaData.tagData'] = action.metaData.tagData;
        }

        if (action.metaData.customFieldData) {
          update.$set['actions.$.metaData.customFieldData'] =
            action.metaData.customFieldData;
        }

        if(action.metaData.ghlCalendarMetaData) {
          update.$set['actions.$.metaData.ghlCalendarMetaData.evaluateOn'] = action.metaData.ghlCalendarMetaData.evaluateOn;
          update.$set['actions.$.metaData.ghlCalendarMetaData.dayRange'] = action.metaData.ghlCalendarMetaData.dayRange;
          update.$set['actions.$.metaData.ghlCalendarMetaData.maxRange'] = action.metaData.ghlCalendarMetaData.maxRange;
          update.$set['actions.$.metaData.ghlCalendarMetaData.cancelEvent'] = action.metaData.ghlCalendarMetaData.cancelEvent;
          update.$set['actions.$.metaData.ghlCalendarMetaData.rescheduleEvent'] = action.metaData.ghlCalendarMetaData.rescheduleEvent;
        }
      }

      if (action.metaData?.googleCalendarMetaData) {
        update.$set['actions.$.metaData.googleCalendarMetaData'] = action.metaData.googleCalendarMetaData;
      }

      if (action.metaData?.standardFieldData) {
        update.$set['actions.$.metaData.standardFieldData'] = action.metaData.standardFieldData;
      }

      if (action.isAdvancedSettings === true && action.advancedSettings) {
        update.$set['actions.$.advancedSettings.maxTokensAllowed'] =
          action.advancedSettings.maxTokensAllowed;
        update.$set['actions.$.advancedSettings.aiProvider'] = {
          accountId: action.advancedSettings.aiProvider.accountId,
          accountName: action.advancedSettings.aiProvider.accountName,
          companyId: action.advancedSettings.aiProvider.companyId,
          modelName: action.advancedSettings.aiProvider.modelName,
        };
      } else {
        update.$set['actions.$.advancedSettings.maxTokensAllowed'] = 450;
      }

       if (action.slackDetails) {
         update.$set['actions.$.slackDetails'] = {
           type: action.slackDetails.type,
           text: action.slackDetails.text,
           channel_id: action.slackDetails.channel_id,
         };
       }

      if (action.sms) {
        update.$set['actions.$.sms'] = {
          type: action.sms.type,
          text: action.sms.text,
          phoneId: action.sms.phoneId,
        };
      }

      if (action.ghlEmail) {
        update.$set['actions.$.ghlEmail'] = {
          type: action.ghlEmail.type,
          text: action.ghlEmail.text || "",
          subject: action.ghlEmail.subject || "",
        };
      }

      if (action.jsonObjects) {
        const jsonObjectsUpdate: any = {};

        if (action.jsonObjects.name) {
          jsonObjectsUpdate['actions.$.jsonObjects.name'] =
            action.jsonObjects.name;
        }

        if (action.jsonObjects.generateCondition) {
          jsonObjectsUpdate['actions.$.jsonObjects.generateCondition'] =
            action.jsonObjects.generateCondition;
        }

        const propertiesUpdate = action.jsonObjects.properties.map(
          (propertyId) => ({
            name: propertyId.name,
            description: propertyId.description,
            type: propertyId.type,
            id: propertyId.id,
          }),
        );

        jsonObjectsUpdate['actions.$.jsonObjects.properties'] =
          propertiesUpdate;

        const arrayFilters = [
          { 'elem.id': { $in: propertiesUpdate.map((p) => p.id) } },
        ];

        await this.agentModel.updateOne(
          filter,
          { $set: jsonObjectsUpdate },
          { arrayFilters },
        );
      }

      if (action.hasOwnProperty('httpGetRequestDetails')) {
        update.$set['actions.$.httpGetRequestDetails'] =
          action.httpGetRequestDetails;

          await this.httpRequestGetService.update(
            null,
            null,
            action.accountId,
            action.httpGetRequestDetails,
          );
      }

      // Handle unified httpRequestDetails for both GET and POST requests
      if (action.hasOwnProperty('httpRequestDetails')) {
        update.$set['actions.$.httpRequestDetails'] = action.httpRequestDetails;
        
        // Update the appropriate service based on provider name
        if (action.providerName === 'httpGet') {
          await this.httpRequestGetService.update(
            null,
            null,
            action.accountId,
            action.httpRequestDetails,
          );
        }
        // For POST requests, we could add similar handling here when needed
      }

      

      if (action.includeMainPromptDetails) {
        update.$set['actions.$.includeMainPromptDetails'] = {
            includeMainPrompt: action.includeMainPromptDetails.includeMainPrompt,
            mainPromptId: action.includeMainPromptDetails.mainPromptId || ""
        };
      }
      const updatedAgent = await this.agentModel.findOneAndUpdate(filter, update, { new: true, });
      return updatedAgent;
    } catch (error) {
      throw new Error(`Error editing datasource: ${error}`);
    }
  }

  async getDatasources(getDatasourceDto: GetDatasourceDto) {
    try {
      const { agentId, actionId, accountId } = getDatasourceDto;

      const getAgent = await this.agentModel.findById(agentId);

      if(actionId && actionId !== ''){
      const action = getAgent.actions.find(
        (action) => action.actionId === actionId,
      );
      return action;
      } else if(accountId && accountId !== ''){
        const action = getAgent.actions.find(
          (action) => action.accountId === accountId,
        );
        return action;
      }


      return getAgent;
    } catch (error) {
      throw new Error(`Error editing datasource: ${error}`);
    }
  }

  async addTrigger(triggerDto: AddTriggerDto) {
    const { agentId } = triggerDto;
    const agent = await this.agentModel.findById(agentId).exec();
    const uuid = uuidv4();
    const newTrigger = {
      triggerId: uuid as string,
      ...triggerDto,
      followUp: triggerDto.followUp ? triggerDto.followUp : {
        duration: 15,
        maxAttempts: 2,
        promptId: "",
        isFollowupEnabled: false,
        conditionPrompt: '',
        schedule: {
          monday: [],
          tuesday: [],
          wednesday: [],
          thursday: [],
          friday: [],
          saturday: [],
          sunday: [],
        },
        timezone: "",
        tagConditions: [],
      }
    };

   
  return this.agentModel
     .findByIdAndUpdate(
       agentId,
       { 
        $push: { triggers: newTrigger },
        'showNotifications.connectAgentToTrigger': false
        },
       { new: true },
     )
     .exec();
  }

  // async updateTrigger(
  //     query: Object,
  //     update: Object,
  //     options: Object = { new: true },)
  //     {
  //         const trigger = await this.agentModel
  //         .findOneAndUpdate(query, update, options)
  //         .exec();
  //       return org;
  //     }

  async updateTriggerStatus(updateStatusTriggerDto) {
    const { agentId, triggerId, active } = updateStatusTriggerDto;
    await this.agentModel.updateOne(
      {
        _id: agentId,
        'triggers.triggerId': triggerId,
      },
      {
        $set: {
          'triggers.$.active': active,
        },
      },
    );
    return;
  }

  async deleteTrigger(agentId, triggerId, response) {
    try {
      const agent = await this.agentModel.findById(agentId);

      if (!agent) {
        return response.status(404).json({
          message: 'Agent not found.',
        });
      }

      const triggerIndex = agent.triggers.findIndex(
        (trigger) => trigger.triggerId === triggerId,
      );

      if (triggerIndex === -1) {
        return response.status(404).json({
          message: 'Trigger not found.',
        });
      }

      // Remove the trigger from the triggers array
      agent.triggers.splice(triggerIndex, 1);

      // Save the updated agent
      await agent.save();
    } catch (error) {}
  }

    async deleteAction(agentId: string, actionId: string){
        return await this.agentModel.findByIdAndUpdate(agentId, { 
          $pull: { 'actions': { actionId } },
        }, {
          new: true,
        }).exec();
    }

    async deleteTool(agentId:string, toolId: string){
      return await this.agentModel.updateOne({_id: agentId}, { 
        // $pull: { 'voiceConfig.tools': { toolId } },
        $pull: { 'voiceConfig.tools': { toolId }},
      }).exec();
    }

  async deleteAgent(query: Object, options: Object = {}) {
    try {
      const deletedAgent = await this.agentModel
        .findOneAndDelete(query, options)
        .exec();
      const orgid = deletedAgent.orgId;
      const agentId = deletedAgent._id.toString();
      let allSavedSession: any[] =
        await this.mongoSessionService.allSavedSessionOfOneAgent(agentId);
      await this.mongoSessionService.deleteAllSessionOfOneAgent(agentId);
      if (allSavedSession.length > 0) {
        for (const eachSavedSession of allSavedSession) {
          // set the delete object
          const deleteObject = {
            namespace: orgid,
            ids: [`${eachSavedSession}`],
            filter: {
              agentId: agentId,
            },
          };
          //delete index from pinecone
          const index = await this.pineconeService.connectIndex(
            process.env.PINECONE_INDEX,
          );
          const deletedNamespace = await index.delete1(deleteObject);
        }
      }
      return deletedAgent;
    } catch (error) {
      this.logger.error({
        message: 'error deleting agent: service: ' + error.message,
        context: 'MongoAgentService.deleteAgent',
      });
    }
  }

  async toggleAgent(agentId, toggle, response?) {
    const agent = await this.agentModel.findOneAndUpdate(
      { _id: agentId },
      { $set: { disabled: toggle } },
      { new: true },
    );

    if (!agent && response) {
      return response.status(404).json({
        message: 'Agent not found.',
      });
    }

    return agent;
  }

  async checkDataSources(orgId: string, accountId: string) {
    const agent = await this.agentModel
      .find(
        { orgId: orgId, 'actions.accountId': accountId },
        {
          agentName: 1,
        },
      )
      .exec();
    return agent;
  }

  async getAgentForConnectionAccounts(orgId: string, accountIds: [string]) {
    const agent = await this.agentModel
      .find({ orgId: orgId, 'actions.accountId': { $in: accountIds } })
      .exec();
    return agent;
  }

  async getAgentsCount(filterQuery, options?) {
    const agent = await this.agentModel
      .countDocuments(filterQuery, options)
      .exec();
    return agent;
  }

    async checkAgentExists(query) {
        return await this.agentModel.exists(query);
    }

    async updateMainPromptToggle(editMainPromptDto: EditMainPromptInjectionDto){
        const {agentId, actionId, injectOrNot} = editMainPromptDto;
        const updatedAgent = await this.agentModel.findOneAndUpdate(
            { _id: agentId, 'actions.actionId': actionId },
            { $set: { 'actions.$.includeMainPromptDetails.includeMainPrompt': injectOrNot } },
            { new: true} 
        ).exec();
        if (!updatedAgent) {
            throw new NotFoundException('Agent or action not found');
        }
        return updatedAgent;
    }

    async addSavedSessionsToAgent(agentId, sessionId){
        return this.agentModel.findByIdAndUpdate(
            agentId,
            { $push: { savedSessionIds: sessionId } },
            { new: true, useFindAndModify: false },
          ).exec();
    }

    async removeSavedSessionsFromAgent(agentId, sessionId){
        return this.agentModel.findByIdAndUpdate(
            agentId,
            { $pull: { savedSessionIds: sessionId } },
            { new: true, useFindAndModify: false },
          ).exec();
    }

    async updateAgents(query: FilterQuery<AgentDocument>, update: UpdateQuery<AgentDocument>, options?: QueryOptions<AgentDocument>) {
        return await this.agentModel.updateMany(query, update, options).exec();
    }
}
