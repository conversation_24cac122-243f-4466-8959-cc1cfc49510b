import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Credential, HydratedCredentialDocument } from '../../schemas/credential/credentials.schema';
import { CreateCredentialDto } from 'src/mongo/dto/credentials/create.dto';
import { MyLogger } from 'src/logger/logger.service';

@Injectable()
export class MongoCredentialsService {
  constructor(
    @InjectModel(Credential.name)
    private readonly credentialsModel: Model<Credential>,
    private readonly logger: MyLogger,
  ) {}

  async createCredentials(
    createCredentialsDto: CreateCredentialDto,
  ): Promise<HydratedCredentialDocument> {
    // this.logger.log({ createCredentialsDto });

    const newCredentials = new this.credentialsModel(createCredentialsDto);
    const newCredentialsSaved = await newCredentials.save();

    return newCredentialsSaved;
  }

  async getCredential(
    query: Object,
    projection: Object = {},
    options: Object = { lean: true },
  ): Promise<HydratedCredentialDocument> {
    // this.logger.log('Query is ', query);
    const credentials: HydratedCredentialDocument = await this.credentialsModel
      .findOne(query, projection, options)
      .exec();
    return credentials;
  }


  async updateCredential(
    query,
    updateBody,
    options = { new: true, upsert: false },
  ) {
    const updateQuery = {
      $set: updateBody,
    };
    return await this.credentialsModel
      .updateOne(query, updateQuery, options)
      .exec();
  }

  async deleteCredentials(query: Object, options: Object = {}) {
    const deletedCred = await this.credentialsModel
      .deleteOne(query, options)
      .exec();
    return deletedCred;
  }
  async deleteCredential(
    query: Object,
    options: Object = {},
  ): Promise<HydratedCredentialDocument> {
    const deletedCred = await this.credentialsModel
      .findOneAndDelete(query, options)
      .exec();
    return deletedCred;
  }

  async getCredentialDoc(id: string): Promise<HydratedCredentialDocument> {
    return await this.credentialsModel.findById(id);
  }

  async updateCredentials({
    query,
    updateBody = {},
    options = { upsert: false },
  }) {
    const updateQuery = {
      $set: updateBody,
    };
    const r = await this.credentialsModel
      .updateMany(query, updateQuery, options)
      .exec();
    if (r.modifiedCount === 0) {
      this.logger.log({
        message: `#no_credentials_updated | No credentials updated for ${r.matchedCount} matches\nquery is ${JSON.stringify(
          query
        )},\nupdateBody is ${JSON.stringify(updateBody)}`,
        context: 'MongoCredentialsService.updateCredentials',
      })
    }
    return r;
  }

  async getCredentials(query: Object, projection?: Object, options?: Object) {
    const credentials = await this.credentialsModel
      .find(query, projection, { lean: true, ...options })
      .exec();
    return credentials;
  }

  async ifExists(query: Object) {
    return await this.credentialsModel.exists(query);
  }
}
