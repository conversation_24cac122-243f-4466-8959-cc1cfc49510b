import { Module, Global, forwardRef } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import {
  Credential,
  CredentialsSchema,
} from './schemas/credential/credentials.schema';
import {
  GoogleCredential,
  GoogleCredentialSchema,
} from './schemas/credential/google/googleCredentials.schema';
import {
  GhlCredential,
  GhlCredentialSchema,
} from './schemas/credential/ghl/ghlCredentials.schema';
import {
  OpenaiCredential,
  OpenaiCredentialSchema,
} from './schemas/credential/openai/openai.schema';
import {
  Organization,
  OrganizationSchema,
} from './schemas/organization/organization.schema';
import { User, userSchema } from './schemas/user/user.schema';
import { MongoOrganizationService } from './service/organization/mongo-organization.service';
import { MongoUserService } from './service/user/mongo-user.service';
import { MongoCredentialsService } from './service/credentials/mongo-credentials.service';
import { MongoAgentService } from './service/agent/mongo-agent.service';
import { Agent } from 'http';
import { agentSchema } from './schemas/agents/agents.schema';
import { Session, SessionSchema } from './schemas/session/session.schema';
import { MongoSessionService } from './service/session/mongo-session.service';
import {
  Conversation,
  ConversationSchema,
} from './schemas/conversation/conversation.schema';
import { MongoConversationService } from './service/conversations/mongo-conversations.service';
import {
  MemberAccess,
  MemberAccessSchema,
} from './schemas/organization/members/members.schema';
import { WaitList, WaitListSchema } from './schemas/waitlist/waitlist.schema';
import { MongoWaitListService } from './service/waitlist/mongo-waitlist.service';
import { NotificationService } from 'src/notification/notification.service';
import { LoggerModule } from 'src/logger/logger.module';
import {
  ContextData,
  ContextDataSchema,
} from './schemas/contextData/contextData.schema';
import {
  ThirdPartyApps,
  ThirdPartyAppsSchema,
} from './schemas/thirdPartyApps/thirdPartyApps.schema';
import { MongoContextDataService } from './service/contextData/contextData.service';
import { mongoThirdPartyAppsService } from './service/thirdPartyApps/thirdPartyApps.service';
import {
  AppUpdates,
  AppUpdatesSchema,
} from './schemas/app-updates/app-updates.schema';
import { MongoAppUpdatesService } from './service/app-updates/mongo-app-updates.service';
import {
  Rebilling,
  RebillingSchema,
} from './schemas/rebilling/rebilling.schema';
import {
  RebillingUsage,
  RebillingUsageSchema,
} from './schemas/rebilling-usage/rebilling-usage.schema';
import {
  AiHistory,
  AiHistorySchema,
} from './schemas/aiHistory/aiHistory_schema';
import {
  PodiumCredential,
  PodiumCredentialSchema,
} from './schemas/credential/podium/podiumCredentials.schema';
// import { MongoTokenService } from './service/organization/token/mongo-token.service';
import { SessionModule } from 'src/session/session.module';
import { Folders, FoldersSchema } from './schemas/folders/folders.schema';
import { AiUsage, AiUsageSchema } from './schemas/aiUsage/ai-usage.schema';
import { MongoAiUsageService } from './service/aiUsage/MongoAiUsageService';
import { StripeService } from 'src/billing/stripe.service';
import {
  HubspotCredential,
  HubspotCredentialSchema,
} from './schemas/credential/hubspot/hubspotCredentials.schema';
import { HttpRequestModule } from 'src/http-request/http-request.module';
import {
  SlackCredential,
  SlackCredentialSchema,
} from './schemas/credential/slack/slackCredential.schema';
import {
  VoiceNumbers,
  VoiceNumbersSchema,
} from './schemas/voiceNumbers/voiceNumbers.schema';
import { MongoVoiceNumbersService } from './service/voiceNumbers/voiceNumbers.service';
import { VoiceModule } from 'src/voice/voice.module';
import {
  VoiceUsage,
  VoiceUsageSchema,
} from './schemas/voiceUsage/voice-usage.schema';
import { MongoVoiceUsageService } from './service/voiceUsage/voice-usage.service';
import {
  NylasCredential,
  NylasCredentialSchema,
} from './schemas/credential/nylas/nylasCredential.schema';
import {
  AgentTemplates,
  AgentTemplatesSchema,
} from './schemas/agent-templates/agent-templates.schema';
import { CacheService } from 'src/utility/services/cache.service';

@Global()
@Module({
  imports: [
    LoggerModule,
    SessionModule,
    MongooseModule.forFeature([
      { name: MemberAccess.name, schema: MemberAccessSchema },
    ]),
    MongooseModule.forFeature([
      {
        name: Credential.name,
        schema: CredentialsSchema,
        discriminators: [
          { name: GoogleCredential.name, schema: GoogleCredentialSchema },
          { name: GhlCredential.name, schema: GhlCredentialSchema },
          { name: OpenaiCredential.name, schema: OpenaiCredentialSchema },
          { name: PodiumCredential.name, schema: PodiumCredentialSchema },
          { name: HubspotCredential.name, schema: HubspotCredentialSchema },
          { name: SlackCredential.name, schema: SlackCredentialSchema },
          { name: NylasCredential.name, schema: NylasCredentialSchema },
        ],
      },
    ]),
    MongooseModule.forFeature([
      { name: Organization.name, schema: OrganizationSchema },
    ]),
    MongooseModule.forFeature([
      { name: WaitList.name, schema: WaitListSchema },
    ]),
    MongooseModule.forFeature([{ name: User.name, schema: userSchema }]),
    MongooseModule.forFeature([{ name: Agent.name, schema: agentSchema }]),
    MongooseModule.forFeature([{ name: Session.name, schema: SessionSchema }]),
    MongooseModule.forFeature([
      { name: Conversation.name, schema: ConversationSchema },
    ]),
    MongooseModule.forFeature([
      { name: ContextData.name, schema: ContextDataSchema },
    ]),
    MongooseModule.forFeature([
      { name: ThirdPartyApps.name, schema: ThirdPartyAppsSchema },
    ]),
    MongooseModule.forFeature([
      { name: AppUpdates.name, schema: AppUpdatesSchema },
    ]),
    MongooseModule.forFeature([
      { name: Rebilling.name, schema: RebillingSchema },
    ]),
    MongooseModule.forFeature([
      { name: RebillingUsage.name, schema: RebillingUsageSchema },
    ]),
    MongooseModule.forFeature([
      { name: AiHistory.name, schema: AiHistorySchema },
    ]),
    MongooseModule.forFeature([{ name: Folders.name, schema: FoldersSchema }]),
    MongooseModule.forFeature([{ name: AiUsage.name, schema: AiUsageSchema }]),
    MongooseModule.forFeature([
      { name: VoiceUsage.name, schema: VoiceUsageSchema },
    ]),
    HttpRequestModule,
    MongooseModule.forFeature([
      { name: VoiceNumbers.name, schema: VoiceNumbersSchema },
    ]),
    forwardRef(() => VoiceModule),
    MongooseModule.forFeature([
      { name: AgentTemplates.name, schema: AgentTemplatesSchema },
    ]),
  ],
  providers: [
    MongoOrganizationService,
    MongoUserService,
    MongoCredentialsService,
    MongoAgentService,
    MongoSessionService,
    MongoConversationService,
    MongoWaitListService,
    MongoAppUpdatesService,
    NotificationService,
    MongoContextDataService,
    mongoThirdPartyAppsService,
    MongoAiUsageService,
    StripeService,
    MongoVoiceNumbersService,
    MongoVoiceUsageService,
    CacheService,
  ],
  exports: [
    MongoOrganizationService,
    MongoUserService,
    MongoCredentialsService,
    MongoAgentService,
    MongoSessionService,
    MongoConversationService,
    MongoWaitListService,
    MongoContextDataService,
    mongoThirdPartyAppsService,
    MongoAppUpdatesService,
    MongoAiUsageService,
    MongoVoiceNumbersService,
    MongooseModule.forFeature([
      { name: AppUpdates.name, schema: AppUpdatesSchema },
    ]),
    MongooseModule.forFeature([
      { name: Rebilling.name, schema: RebillingSchema },
    ]),
    MongooseModule.forFeature([
      { name: RebillingUsage.name, schema: RebillingUsageSchema },
    ]),
    MongooseModule.forFeature([{ name: Folders.name, schema: FoldersSchema }]),
    MongooseModule.forFeature([{ name: AiUsage.name, schema: AiUsageSchema }]),
    MongooseModule.forFeature([
      { name: VoiceUsage.name, schema: VoiceUsageSchema },
    ]),
    MongooseModule.forFeature([
      { name: AgentTemplates.name, schema: AgentTemplatesSchema },
    ]),
    MongoVoiceUsageService,
  ],
})
export class MongoModule {}
