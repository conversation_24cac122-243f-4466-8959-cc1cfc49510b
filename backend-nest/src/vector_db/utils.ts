import OpenAI from 'openai-v4';

export const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
}); //TODO: Remove hard coded values

/**
 * @info This creates vector embedding from a string to be used in any vector DB.
 * @warning Be mindful that once a particular embeddings model is used in production,
 * we should not change it. We will have to re-embed everything if we change to a
 * different model or different dimensional embedding.
 */
export async function getEmbeddings(input: string) {
  try {
    const response = await openai.embeddings.create({
      model: 'text-embedding-ada-002',
      input: input.replace(/\n/g, ' '),
    });

    return response.data[0].embedding;
  } catch (error) {
    throw new Error(
      `Failed to embed: ${
        error instanceof Error ? error.message : 'Unknown error'
      }`,
    );
  }
}

export const truncateStringByBytes = (str: string, bytes: number) => {
  const enc = new TextEncoder();
  return new TextDecoder('utf-8').decode(enc.encode(str).slice(0, bytes));
};
