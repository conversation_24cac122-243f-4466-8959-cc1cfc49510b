import { Injectable } from '@nestjs/common';
import { AdditionalMetadata, VectorDbFilter } from './types';
import { Document } from '@pinecone-database/doc-splitter';

@Injectable()
export abstract class VectorDbService {
  abstract indexName: string;
  abstract namespace: string;
  abstract vectorDimension: number;
  abstract chunkSize: number;
  abstract chunkOverlap: number;
  abstract topK: number;
  abstract minScore: number;

  abstract query(
    filter: VectorDbFilter,
    text: string,
    topK: number,
  ): Promise<string[]>;
  abstract upsert(
    additionalMetaData: AdditionalMetadata,
    text: string,
  ): Promise<Document>;
}
