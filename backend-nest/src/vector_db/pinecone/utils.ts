import type { Index, PineconeRecord } from 'pinecone-database-v3';

const sliceIntoChunks = <T>(arr: T[], chunkSize: number) => {
  return Array.from({ length: Math.ceil(arr.length / chunkSize) }, (_, i) =>
    arr.slice(i * chunkSize, (i + 1) * chunkSize),
  );
};

export const chunkedUpsert = async (
  index: Index,
  vectors: Array<PineconeRecord>,
  namespace: string,
  chunkSize = 500,
) => {
  // Split the vectors into chunks
  const chunks = sliceIntoChunks<PineconeRecord>(vectors, chunkSize);

  try {
    // Upsert each chunk of vectors into the index
    await Promise.allSettled(
      chunks.map(async (chunk, i) => {
        try {
          

          await index.namespace(namespace).upsert(chunk);
        } catch (e) {
          // logger.error(`Error upserting chunk ${i}: `, e);
          

          throw e;
        }
      }),
    );

    return true;
  } catch (error) {
    // logger.error("Error in chunkUpsert:", error);
    throw new Error(
      `Failed to upsert chunk: ${
        error instanceof Error ? error.message : 'Unknown error'
      }`,
    );
  }

  /**
   * Deletes records from Pinecone based on a list of accountIds.
   * @param accountIds - An array of accountId strings to delete.
   */
};
export const deleteRecordsByAccountIds = async (
  index: Index,
  accountIds: string[],
): Promise<void> => {
  try {
    await index.deleteMany({
      accountId: {
        $in: accountIds,
      },
    });
  } catch (error) {
    
  }
};
