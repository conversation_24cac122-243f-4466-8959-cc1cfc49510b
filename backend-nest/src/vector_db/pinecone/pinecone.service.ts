import { Injectable } from '@nestjs/common';
import { VectorDbService } from '../vector_db.service';
import {
  CreateIndexRequestMetricEnum,
  Pinecone,
  PineconeRecord,
  ServerlessSpecCloudEnum,
} from 'pinecone-database-v3';
import { AdditionalMetadata, Metadata, VectorDbFilter } from '../types';
import {
  RecursiveCharacterTextSplitter,
  Document,
} from '@pinecone-database/doc-splitter';
import * as md5 from 'md5';

import * as vectorDbUtils from '../utils';
import * as pineconeUtils from './utils';
import { MyLogger } from 'src/logger/logger.service';

enum Namespace {
  default = 'capri-default',
}

export const pinecone = new Pinecone({
  apiKey: process.env.PINCONE_API_KEY!,
}); //TODO: Remove the hard coded value

@Injectable()
export class PineconeService extends VectorDbService {
  indexName: string = process.env.PINECONE_INDEX!;
  namespace: string = Namespace.default;
  cloudName: ServerlessSpecCloudEnum = 'aws';
  metric: CreateIndexRequestMetricEnum = 'cosine';
  regionName: string = 'us-east-1';
  vectorDimension: number = 1536; // WARNING: DO NOT CHANGE ONCE IMPLEMENTED
  // The best values for the below properties are not decided yet.
  chunkSize: number = 500;
  upsertRecordCount: number = 25; // The max upsert size is 2MB or 1000 records, whichever is reached first (https://docs.pinecone.io/guides/data/upsert-data#upsert-limits)
  chunkOverlap: number = 20;
  topK: number = 5;
  minScore: number = 0.6;

  constructor(private readonly logger: MyLogger) {
    super();
  }

  async query(
    filter: VectorDbFilter,
    text: string,
    topK: number = this.topK,
  ): Promise<string[]> {
    

    if (this.indexName === '' || !this.indexName) {
      throw new Error('Pinecone index name not found.');
    }
    // Retrieve the list of indexes to check if expected index exists
    const indexes = (await pinecone.listIndexes())?.indexes;
    if (
      !indexes ||
      indexes.filter((i) => i.name === this.indexName).length !== 1
    ) {
      throw new Error(`Index ${this.indexName} does not exist`);
    }

    const index = pinecone!.Index<Metadata>(this.indexName);

    const pineconeNamespace = index.namespace(this.namespace ?? '');

    const embeddings = await vectorDbUtils.getEmbeddings(text);

    try {
      const queryResult = await pineconeNamespace.query({
        vector: embeddings,
        topK,
        includeMetadata: true,
        filter,
      });
      const matches = queryResult.matches || [];

      const qualifyingDocs = matches.filter(
        (m) => m.score && m.score > this.minScore,
      );
      const docs = matches
        ? qualifyingDocs.map((match) => (match.metadata as Metadata).chunk)
        : [];
      return docs;
    } catch (error) {
      throw new Error(`Error querying embeddings: ${error}`);
    }
  }

  async upsert(
    additionalMetaData: AdditionalMetadata,
    text: string,
  ): Promise<Document> {
    const splitter = new RecursiveCharacterTextSplitter({
      chunkSize: this.chunkSize,
      chunkOverlap: this.chunkOverlap,
    });
    const document = await this.prepareDocument(
      text,
      additionalMetaData,
      splitter,
    );

    // Create a Pinecone index if it does not exist
    const indexList: string[] =
      (await pinecone.listIndexes())?.indexes?.map((index) => index.name) || [];
    const indexExists = indexList.includes(this.indexName);
    if (!indexExists) {
      await this.createIndex();
    }

    const index = pinecone.Index(this.indexName);
    const vectors = await Promise.all(
      document.flat().map((doc) => this.embedDocument(doc, additionalMetaData)),
    );

    await pineconeUtils.chunkedUpsert(
      index,
      vectors,
      this.namespace,
      this.upsertRecordCount,
    );
    return document[0];
  }

  async createIndex() {
    await pinecone.createIndex({
      name: this.indexName,
      dimension: this.vectorDimension,
      metric: this.metric,
      waitUntilReady: true,
      spec: {
        serverless: {
          cloud: this.cloudName,
          region: this.regionName,
        },
      },
    });
  }

  async prepareDocument(
    text: string,
    additionalMetaData: AdditionalMetadata,
    splitter: RecursiveCharacterTextSplitter,
  ): Promise<Document[]> {
    const pageContent = text;

    // Split the documents using the provided splitter
    const docs = await splitter.splitDocuments([
      new Document({
        pageContent,
        metadata: {
          // Truncate the text to a maximum byte length
          text: vectorDbUtils.truncateStringByBytes(pageContent, 36000),
        },
      }),
    ]);

    return docs.map((doc: Document) => {
      return {
        pageContent: doc.pageContent,
        metadata: {
          ...doc.metadata,
          // Create a hash of the document content
          hash: md5(doc.pageContent + additionalMetaData?.agentId ?? ''),
        },
      };
    });
  }

  async embedDocument(
    doc: Document,
    additionalMetaData?: AdditionalMetadata,
  ): Promise<PineconeRecord> {
    try {
      const embedding = await vectorDbUtils.getEmbeddings(doc.pageContent);

      const hash = md5(doc.pageContent);
      const metadata = {
        // The metadata includes details about the document
        chunk: doc.pageContent, // The chunk of text that the vector represents
        // text: doc.metadata.text as string, // The text of the document // Dont send it, each object becomes too large.
        hash: doc.metadata.hash as string, // The hash of the document content
      };
      return {
        id: hash, // The ID of the vector is the hash of the document content
        values: embedding,
        metadata: { ...metadata, ...additionalMetaData }, //include the additional metadata to the pinecone document
      } as PineconeRecord;
    } catch (error) {
      this.logger.error({ message: `Error in embedDocument: ${error}` });
      throw new Error(
        `Failed to embed document: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`,
      );
    }
  }

  async deleteWithMetadata(accountIds: string[]) {
    try {
      const index = pinecone!.Index<Metadata>(this.indexName);
      const pineconeNamespace = index.namespace(this.namespace ?? '');
      await pineconeUtils.deleteRecordsByAccountIds(pineconeNamespace, accountIds);
      return { success: true };
    } catch (error) {
      throw (
        error.message ||
        `Error occured while removing pinecone indexes ${{ accountIds }}`
      );
    }
  }
}
