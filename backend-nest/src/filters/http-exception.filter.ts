import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  HttpException,
} from '@nestjs/common';
import { Response } from 'express';
import { RequestWithUser } from 'src/auth/auth.interface';
import { RequestService } from 'src/request.service';
import { MyLogger } from 'src/utils/logger';

@Catch(HttpException)
export class HttpExceptionFilter implements ExceptionFilter {
  constructor(
    private readonly requestService: RequestService,
    private readonly logger: MyLogger,
  ) {}
  catch(exception: HttpException, host: ArgumentsHost) {
    
    

    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<RequestWithUser>();
    const status = exception.getStatus();
    const errorResponse = exception.getResponse();
    // 
    // 
    // this.logger.error('Unique code from req ', request.uniqueCode);
    let message: string;
    if (typeof errorResponse === 'object' && 'message' in errorResponse) {
      message = errorResponse.message as string;
    } else {
      message = exception.message;
    }

    response.status(status).json({
      statusCode: status,
      timestamp: new Date().toISOString(),
      path: request.url,
      message: message,
      code: request.uniqueCode,
    });
  }
}
