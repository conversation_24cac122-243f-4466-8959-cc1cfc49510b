import { createParamDecorator, ExecutionContext, SetMetadata } from '@nestjs/common';

/**
 * @example f(@Access('role') role: string){...} // where f is any function where we want to use the decorator to access the access claims object
 * @param property 
 */
export const AccessClaims = (property?: string) =>
    createParamDecorator(
        (data: unknown, ctx: ExecutionContext) => {
            const request = ctx.switchToHttp().getRequest();

            // Check if request.headers contains the 'access' field
            if (request.headers && request.headers.access) {
                const accessData = request.headers.access as Record<string, any>;
                if (property === undefined) {
                    return accessData;
                }
                if (accessData.hasOwnProperty(property)) {
                    return accessData[property];
                }
            }
            // If 'access' field is not found, you can return a default value or throw an error
            throw new Error(`Access field or '${property}' property not found in request headers`);
        },
    );