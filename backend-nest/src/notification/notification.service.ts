import { Injectable } from '@nestjs/common';
import { CreateNotificationDto } from './dto/create-notification.dto';
import axios from 'axios';

@Injectable()
export class NotificationService {
  async create(createNotificationDto: CreateNotificationDto) {
    const { to, subject, text } = createNotificationDto;

    const BREVO_API_KEY = process.env.BREVO_API_KEY;
    const BREVO_ENDPOINT = 'https://api.brevo.com/v3/smtp/email';

    const headers = {
      'Content-Type': 'application/json',
      Accept: 'application/json',
      'api-key': BREVO_API_KEY,
    };

    const data = {
      sender: { email: '<EMAIL>', name: 'Capri AI' },
      to: [{ email: to }],
      subject: subject,
      htmlContent: text, // Assuming the 'text' in the request body is actually HTML content.
    };
    const response = await axios.post(BREVO_ENDPOINT, data, { headers });
    return response.data;
  }
}
