import { Injectable } from '@nestjs/common';
import {
  BedrockRuntimeClient,
  InvokeModelCommand,
} from '@aws-sdk/client-bedrock-runtime';


@Injectable()
export class LlamaService {
  private extractYesOrNo(responseText: string): string {
    const yesMatch = responseText.match(/\byes\b/i);
    const noMatch = responseText.match(/\bno\b/i);

    if (yesMatch) {
      return 'yes';
    } else if (noMatch) {
      return 'no';
    } else {
      return 'no';
    }
  }

  async yesNoGenerateLlamaResponse({
    conversationString,
    knowledgeSourceDescription,
  }:{
    conversationString: string;
    knowledgeSourceDescription: string;
  }) {
    const finalPromptContent = `
<s>[INST] <<SYS>>
You are the helpful assistant tasked with providing accurate and insightful information. 
<</SYS>>
Question: Based on the conversation and the instruction to use the knowledge source, should the knowledge source be applied? Dont be explicit, only apply if its required
Conversation: {${conversationString}}
Knowledge Source Description: ${knowledgeSourceDescription}
Please output 'yes' or 'no', if 'no' then explain.
[/INST]
`;

    
    

    const client = new BedrockRuntimeClient({
      region: 'us-east-1',
      credentials: {
        accessKeyId: process.env.AWS_ACCESS_KEY_ID,
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
      },
    });

    const modelId = 'meta.llama2-70b-chat-v1';

    const payload = {
      prompt: finalPromptContent,
      temperature: 0.4,
      top_p: 0.9,
      max_gen_len: 500,
    };
    const command = new InvokeModelCommand({
      body: JSON.stringify(payload),
      contentType: 'application/json',
      accept: 'application/json',
      modelId,
    });

    try {
      const response = await client.send(command);
      const decodedResponseBody = new TextDecoder().decode(response.body);
      const responseBody = JSON.parse(decodedResponseBody);

      let result = responseBody.generation;

      let total_count =
        responseBody.prompt_token_count + responseBody.generation_token_count;

      
      

      const answer = this.extractYesOrNo(result);
      
      let data = {
        answer,
        total_count,
      };

      return {data};
    } catch (err) {
      
    }
  }
}
