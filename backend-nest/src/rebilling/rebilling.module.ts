import { Module } from '@nestjs/common';
import { RebillingService } from './rebilling.service';
import { RebillingController } from './rebilling.controller';
import { MongooseModule } from '@nestjs/mongoose';
import {
  Rebilling,
  RebillingSchema,
} from 'src/mongo/schemas/rebilling/rebilling.schema';
import {
  RebillingUsage,
  RebillingUsageSchema,
} from 'src/mongo/schemas/rebilling-usage/rebilling-usage.schema';
import { BillingModule } from 'src/billing/billing.module';
import { StripeService } from 'src/billing/stripe.service';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Rebilling.name, schema: RebillingSchema },
    ]),
    MongooseModule.forFeature([
      { name: RebillingUsage.name, schema: RebillingUsageSchema },
    ]),
    BillingModule,
  ],
  controllers: [RebillingController],
  providers: [RebillingService, StripeService],
  exports: [
    MongooseModule.forFeature([
      { name: Rebilling.name, schema: RebillingSchema },
    ]),
    MongooseModule.forFeature([
      { name: RebillingUsage.name, schema: RebillingUsageSchema },
    ]),
  ],
})
export class RebillingModule {}
