import {
  ConflictException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { MyLogger } from 'src/logger/logger.service';
import { InjectModel } from '@nestjs/mongoose';
import {
  Members,
  Rebilling,
} from 'src/mongo/schemas/rebilling/rebilling.schema';
import mongoose, { Model } from 'mongoose';
import { RebillingUsage } from 'src/mongo/schemas/rebilling-usage/rebilling-usage.schema';
import { StripeService } from 'src/billing/stripe.service';
import { encryptStripeApiKey } from 'src/organization/services/data/site/helpers';

@Injectable()
export class RebillingService {
  constructor(
    private readonly myLogger: MyLogger,
    @InjectModel(Rebilling.name)
    private readonly rebillingModel: Model<Rebilling>,
    @InjectModel(RebillingUsage.name)
    private readonly rebillingUsageModel: Model<RebillingUsage>,
    private readonly stripeService: StripeService,
  ) {}
  async createRebilling(rebilling: Partial<Rebilling>): Promise<Rebilling> {
    //Check first if the orgId already has a rebilling associated with it. If true throw error
    const rebillingCheck = await this.rebillingModel.findOne({
      organization: rebilling.organization,
    });
    if (rebillingCheck) {
      throw new ConflictException('Organization already has a rebilling');
    }
    //Check the validity of the stripe api key
    const { stripe_api_key } = rebilling;
    const { account_id: stripe_account_id } =
      await this.stripeService.getStripeAccount(stripe_api_key);
    if (!!!stripe_account_id) {
      throw new NotFoundException('Invalid Stripe API Key');
    }
    const newRebilling = await new this.rebillingModel({
      ...rebilling,
      stripe_account_id,
    }).save();
    const rebillingObj = newRebilling.toObject();
    return {
      ...rebillingObj,
      stripe_api_key: encryptStripeApiKey(rebillingObj.stripe_api_key),
    };
  }

  async findRebillingById(id: string): Promise<Rebilling | null> {
    const rebilling = await this.rebillingModel.findById(id).exec();
    if (!rebilling) {
      throw new NotFoundException('No rebilling found');
    }
    const rebillingObj = rebilling.toObject();
    return {
      ...rebillingObj,
      stripe_api_key: encryptStripeApiKey(rebillingObj.stripe_api_key),
    };
  }

  async findRebillingByOrgId(orgId: string): Promise<Rebilling | null> {
    const rebilling = await this.rebillingModel
      .findOne({ organization: orgId })
      .exec();
    const rebillingObj = rebilling.toObject();
    if (!rebilling) {
      throw new NotFoundException('No rebilling found for this organization');
    }
    return {
      ...rebillingObj,
      stripe_api_key: encryptStripeApiKey(rebillingObj.stripe_api_key),
    };
  }

  async deleteRebilling(id: string): Promise<Rebilling | null> {
    const rebilling = await this.rebillingModel.findByIdAndDelete(id).exec();
    if (!rebilling) {
      throw new NotFoundException('No rebilling found');
    }
    const rebillingObj = rebilling.toObject();
    return {
      ...rebillingObj,
      stripe_api_key: encryptStripeApiKey(rebillingObj.stripe_api_key),
    };
  }

  async addMember(orgId: string, member: Partial<Members>): Promise<Members> {
    const rebillingCheck = await this.rebillingModel.findOne({
      'members.channel_account_id': member.channel_account_id,
    });
    if (rebillingCheck) {
      throw new ConflictException(
        'Location already has a rebilling associated with it',
      );
    }
    const rebillingObj = await this.rebillingModel.findOne({
      organization: orgId,
    });
    const { stripe_api_key } = rebillingObj;
    const { stripe_customer_id, stripe_price_id, stripe_product_id } = member;
    const { email, name } = await this.getStripeCustomerDetails(
      member.stripe_customer_id,
      orgId,
    );
    const { stripe_subscription_item_id, stripe_unit_price_amount } =
      await this.stripeService.createSubscription(
        stripe_api_key,
        stripe_customer_id,
        stripe_price_id,
      );
    if (!!!stripe_subscription_item_id) {
      throw new NotFoundException('Failed to create subscription');
    }

    const rebilling = await this.rebillingModel.findOneAndUpdate(
      { organization: orgId },
      {
        $push: {
          members: {
            $each: [
              {
                ...member,
                name,
                email,
                stripe_subscription_item_id,
                stripe_unit_price_amount,
                aggregate_token_used: 0,
                last_snapshot_token_count: 0,
              },
            ],
            $position: 0,
          },
        },
      },
      { new: true },
    );
    return rebilling.members[0];
  }

  async findMemberByCustomerId(customerId: string): Promise<Members | null> {
    return this.rebillingModel.findOne(
      {
        'members.stripe_customer_id': customerId,
      },
      { 'members.$': 1 },
    );
  }

  async findMemberById(id: string): Promise<Members | null> {
    const member = await this.rebillingModel.findOne(
      { 'members._id': id },
      { 'members.$': 1 },
    );
    

    return member.members[0];
  }

  async updateMember(id: string, updatedMember: Partial<Members>) {
    const member = await this.rebillingModel.findOneAndUpdate(
      { 'members._id': id },
      {
        $set: {
          'members.$.charge_by': updatedMember.charge_by,
          'members.$.billing_unit': updatedMember.billing_unit,
          'members.$.max_usage': updatedMember.max_usage,
          'members.$.nickname': updatedMember.nickname,
          'members.$.name': updatedMember.name,
          'members.$.email': updatedMember.email,
          'members.$.disabled': updatedMember.disabled,
          'members.$.aggregate_token_used': updatedMember.aggregate_token_used,
          'members.$.last_snapshot_token_count':
            updatedMember.last_snapshot_token_count,
        },
      },
      {
        new: true,
      },
    );
    return { message: 'Member updated successfully', member };
  }

  async updateStripeKey(
    id: string,
    body: { stripe_api_key: string },
  ): Promise<Rebilling> {
    const { stripe_api_key } = body;
    const { account_id: stripe_account_id_new } =
      await this.stripeService.getStripeAccount(stripe_api_key);
    if (!!!stripe_account_id_new) {
      throw new NotFoundException('Invalid Stripe API Key');
    }

    const rebilling = await this.rebillingModel.findById(id);
    const { stripe_account_id: stripe_account_id_old } = rebilling;
    if (stripe_account_id_old !== stripe_account_id_new) {
      throw new ConflictException(
        'Cannot update to a different stripe account',
      );
    }

    return this.rebillingModel.findByIdAndUpdate(
      id,
      { stripe_api_key: stripe_api_key },
      { new: true },
    );
  }

  async handleMemberDisable(
    id: string,
    body: { disabled: boolean },
  ): Promise<any> {
    await this.rebillingModel.updateOne(
      { 'members._id': id },
      { $set: { 'members.$.disabled': body.disabled } },
    );

    const result = await this.rebillingModel
      .aggregate([
        { $unwind: '$members' },
        { $match: { 'members._id': new mongoose.Types.ObjectId(id) } },
      ])
      .exec();

    if (result.length === 0) {
      throw new NotFoundException('No member found with this id');
    }

    return result[0].members;
  }

  async deleteMember(id: string): Promise<Members | null> {
    return this.rebillingModel.findOneAndUpdate(
      { 'members._id': id },
      { $pull: { members: { _id: id } } },
      { new: true },
    );
  }

  async createRebillingUsage(
    rebillingUsage: Partial<RebillingUsage>,
  ): Promise<RebillingUsage> {
    const { channel_account_id, numOfTokensUsed } = rebillingUsage;
    if (!!!channel_account_id) {
      throw new NotFoundException('LocationId not found');
    }
    const rebilling = await this.rebillingModel.findOne(
      { 'members.channel_account_id': channel_account_id },
      { 'members.$': 1, stripe_api_key: 1 },
    );

    const member = rebilling.members[0];

    const { stripe_api_key } = rebilling;
    let {
      _id,
      stripe_subscription_item_id,
      billing_unit,
      stripe_customer_id,
      aggregate_token_used,
      last_snapshot_token_count,
      charge_by,
      stripe_unit_price_amount,
      disabled,
      stripe_price_id,
    } = member;
    if (disabled) {
      throw new NotFoundException('Member is disabled');
    }
    let earned: number;
    let units_consumed: number;
    if (charge_by === 'tokens') {
      //charging by tokens
      units_consumed = Math.floor(
        (aggregate_token_used + numOfTokensUsed - last_snapshot_token_count) /
          billing_unit,
      );
      earned = units_consumed * stripe_unit_price_amount;


      if (units_consumed > 0) {
        await this.updateMember(_id.toString(), {
          aggregate_token_used: aggregate_token_used + numOfTokensUsed,
          last_snapshot_token_count:
            last_snapshot_token_count + billing_unit * units_consumed,
        });
      } else if (units_consumed === 0) {
        await this.updateMember(_id.toString(), {
          aggregate_token_used: aggregate_token_used + numOfTokensUsed,
        });
      }
    } else {
      //charging by message
      units_consumed = 1;
      earned = stripe_unit_price_amount;
    }
    const addUsageToStripe = await this.stripeService.addUsageToSubscription(
      stripe_api_key,
      stripe_subscription_item_id,
      units_consumed,
      stripe_customer_id,
      stripe_price_id
    );
    const newRebillingUsage = new this.rebillingUsageModel({
      ...rebillingUsage,
      rebilling_customer_id: _id,
      earned,
      stripe_units: units_consumed,
    });
    return newRebillingUsage.save();
  }

  async createRebillingUsageByLocationId(
    locationId: string,
    rebillingUsage: {
      numOfTokensUsed: number;
      cost: number;
    },
  ): Promise<RebillingUsage> {
    if (!locationId || !rebillingUsage) {
      throw new NotFoundException('LocationId or rebillingUsage not found');
    }
    const rebilling = await this.rebillingModel.findOne({
      'members.channel_account_id': locationId,
    });
    if (!rebilling) {
      throw new NotFoundException('No rebilling found for this location');
    }
    const member = rebilling.members.find(
      (member) => member.channel_account_id === locationId,
    );
    if (!member) {
      throw new NotFoundException('No member found for this location');
    }
    return await this.createRebillingUsage({
      ...rebillingUsage,
      rebilling_customer_id: member._id.toString(),
      channel_account_id: locationId,
    });
  }

  async getMembersByLocationId(locationId: string): Promise<Members> {
    const rebilling = await this.rebillingModel.findOne(
      { 'members.channel_account_id': locationId },
      { 'members.$': 1 },
    );
    if (!!!rebilling) {
      return null;
    }
    return rebilling.members[0];
  }

  async getStripeUsage(id: string): Promise<any> {
    const member = await this.rebillingModel.findOne(
      { 'members._id': id },
      {
        'members.$': 1,
        stripe_api_key: 1,
      },
    );
    const { stripe_api_key } = member;
    const { stripe_subscription_item_id } = member.members[0];
    const stripeUsage = await this.stripeService.getMeteredSubscriptionEarnings(
      stripe_api_key,
      stripe_subscription_item_id,
    );
    // const totalUsed = stripeUsage.data.reduce(
    //   (acc, curr) => acc + curr.total_usage,
    //   0,
    // );
    return stripeUsage;
  }

  async getRebillingUsageRecords(
    id: string,
    from: Date,
    to: Date,
  ): Promise<
    {
      date: string;
      numOfTokensUsed: number;
      cost: number;
      earned: number;
      numOfMessagesSent: number;
    }[]
  > {
    return await this.rebillingUsageModel.aggregate([
      {
        $match: {
          rebilling_customer_id: id,
          createdAt: { $gte: from, $lte: to },
        },
      },
      {
        $bucketAuto: {
          groupBy: '$createdAt',
          buckets: 50,
          output: {
            date: { $first: '$createdAt' },
            numOfTokensUsed: { $sum: '$numOfTokensUsed' },
            cost: { $sum: '$cost' },
            earned: { $sum: '$earned' },
            numOfMessagesSent: { $sum: 1 },
          },
        },
      },
      {
        $project: {
          _id: 0,
          date: {
            $toLong: '$date',
          },
          numOfTokensUsed: 1,
          cost: 1,
          earned: 1,
          numOfMessagesSent: 1,
        },
      },
      {
        $sort: {
          date: 1, // Sort by date in ascending order
        },
      },
    ]);
  }
  async getStripeApiKey(orgId: string): Promise<string> {
    const rebillingObj = await this.rebillingModel.findOne(
      {
        organization: orgId,
      },
      {
        stripe_api_key: 1,
      },
    );
    return rebillingObj.stripe_api_key;
  }

  async getStripeProducts(orgId: string): Promise<any> {
    const stripe_api_key = await this.getStripeApiKey(orgId);
    const products = await this.stripeService.getAllProducts(stripe_api_key);
    return products;
  }

  async getStripeCustomerDetails(
    customerId: string,
    orgId: string,
  ): Promise<any> {
    const stripe_api_key = await this.getStripeApiKey(orgId);
    return this.stripeService.getCustomerDetails(customerId, stripe_api_key);
  }

  async getStripePriceIds(orgId: string, productId: string): Promise<any> {
    const stripe_api_key = await this.getStripeApiKey(orgId);
    

    return this.stripeService.getPriceIds(stripe_api_key, productId);
  }
}
