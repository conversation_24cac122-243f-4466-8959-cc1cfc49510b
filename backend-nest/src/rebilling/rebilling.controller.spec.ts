import { Test, TestingModule } from '@nestjs/testing';
import { RebillingController } from './rebilling.controller';
import { RebillingService } from './rebilling.service';

describe('RebillingController', () => {
  let controller: RebillingController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [RebillingController],
      providers: [RebillingService],
    }).compile();

    controller = module.get<RebillingController>(RebillingController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
