import {
  Controller,
  Get,
  Post,
  Delete,
  Param,
  Body,
  Query,
  Patch,
  Put,
} from '@nestjs/common';
import { RebillingService } from './rebilling.service';
import { Rebilling } from 'src/mongo/schemas/rebilling/rebilling.schema';
import { Members } from 'src/mongo/schemas/rebilling/rebilling.schema';
import { RebillingUsage } from 'src/mongo/schemas/rebilling-usage/rebilling-usage.schema';
import * as moment from 'moment';

@Controller('rebilling')
export class RebillingController {
  constructor(private readonly rebillingService: RebillingService) {}

  @Post()
  async createRebilling(
    @Body() rebilling: Partial<Rebilling>,
  ): Promise<Rebilling> {
    return this.rebillingService.createRebilling(rebilling);
  }

  @Get(':id')
  async findRebillingById(@Param('id') id: string): Promise<Rebilling | null> {
    return this.rebillingService.findRebillingById(id);
  }

  @Get('organization/:orgId')
  async findRebillingByOrgId(
    @Param('orgId') orgId: string,
  ): Promise<Rebilling | null> {
    return this.rebillingService.findRebillingByOrgId(orgId);
  }

  @Patch(':id/stripe-key')
  async updateStripeKey(
    @Param('id') id: string,
    @Body() body: { stripe_api_key: string },
  ): Promise<Rebilling> {
    return this.rebillingService.updateStripeKey(id, body);
  }

  @Delete(':id')
  async deleteRebilling(@Param('id') id: string): Promise<Rebilling | null> {
    return this.rebillingService.deleteRebilling(id);
  }

  @Post('organization/:orgId/members')
  async addMember(
    @Param('orgId') orgId: string,
    @Body() member: Partial<Members>,
  ): Promise<Members> {
    return this.rebillingService.addMember(orgId, member);
  }

  @Delete('members/:id')
  async deleteMember(@Param('id') id: string): Promise<Members | null> {
    return this.rebillingService.deleteMember(id);
  }

  @Get('members/customer-id/:customerId')
  async findMemberByCustomerId(
    @Param('customerId') customerId: string,
  ): Promise<Members | null> {
    return this.rebillingService.findMemberByCustomerId(customerId);
  }

  @Post('members/usage')
  async addRebillingUsage(
    @Body() usage: Partial<RebillingUsage>,
  ): Promise<RebillingUsage> {
    return this.rebillingService.createRebillingUsage({
      ...usage,
    });
  }

  @Get('members/:id/metered-subscription-earnings')
  async getStripeUsage(@Param('id') id: string): Promise<number> {
    return this.rebillingService.getStripeUsage(id);
  }

  @Put('members/:id')
  async updateMember(
    @Param('id') id: string,
    @Body() updatedMember: Partial<Members>,
  ) {
    return this.rebillingService.updateMember(id, updatedMember);
  }

  @Patch('members/:id/toggle-disabled')
  async handleMemberDisable(
    @Param('id') id: string,
    @Body() body: { disabled: boolean },
  ): Promise<Members> {
    return this.rebillingService.handleMemberDisable(id, body);
  }

  @Get('members/location-id/:locationId')
  async getMembersByLocationId(
    @Param('locationId') locationId: string,
  ): Promise<Members> {
    

    return this.rebillingService.getMembersByLocationId(locationId);
  }

  @Get('members/:id/tokens-used')
  async getTokensUsed(
    @Param('id') id: string,
    @Query('from') from: string,
    @Query('to') to: string,
  ): Promise<
    {
      date: string;
      numOfTokensUsed: number;
      cost: number;
    }[]
  > {
    const fromDate = from
      ? new Date(Number(from))
      : moment().subtract(1, 'week').toDate();
    const toDate = to ? new Date(Number(to)) : new Date();

    const usageRecords = await this.rebillingService.getRebillingUsageRecords(
      id,
      fromDate,
      toDate,
    );
    

    // return usageRecords.reduce(
    //   (total, record) => total + record.numOfTokensUsed,
    //   0,
    // );
    return usageRecords;
  }

  @Get('members/:id')
  async findMemberById(@Param('id') id: string): Promise<Members | null> {
    return this.rebillingService.findMemberById(id);
  }

  //Stripe
  @Get('stripe/products')
  async getStripeProducts(@Query('orgId') orgId: string): Promise<any> {
    return this.rebillingService.getStripeProducts(orgId);
  }

  @Get(':orgId/stripe/customer/:customerId')
  async getStripePrice(
    @Param('orgId') orgId: string,
    @Param('customerId') customerId: string,
  ): Promise<any> {
    return this.rebillingService.getStripeCustomerDetails(customerId, orgId);
  }

  @Get(':orgId/stripe/price-ids/:productId')
  async getStripePriceIds(
    @Param('orgId') orgId: string,
    @Param('productId') productId: string,
  ): Promise<any> {
    return this.rebillingService.getStripePriceIds(orgId, productId);
  }
}
