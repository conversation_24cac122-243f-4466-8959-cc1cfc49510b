<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Hubspot Connector</title>
  </head>
  <style>
    :root {
      --third: #00adb5;
      --fourth: #eeeeee;
      --primary: #1a1919;
      --secondary: #282a2e;
      --gray: #939394;
      --font-color: #eeeeee;
      --violet: #6b63ff;
    }

    body {
      background-color: var(--primary);
      color: var(--font-color);
      font-family: 'Roboto', sans-serif;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      text-align: center;
    }

    select {
      width: 100%;
      padding: 12px 20px;
      margin: 8px 0;
      box-sizing: border-box;
      border: 1px solid #ccc;
      border-radius: 4px;
    }
    .container {
      padding: 24px 30px;
      height: 100%;
    }

    #message {
      font-size: 14px;
      font-weight: 400;
      color: var(--gray);
    }

    .form {
      width: 80%;
    }

    .form-group {
      margin-bottom: 1rem;
    }

    .form-group label {
      margin-bottom: 0.5rem;
      font-size: 14px;
      font-weight: 500;
      color: #bcbcbe;
    }

    #channelName {
      padding: 12px 10px;
      margin-top: 15px;
      width: 250px;
    }

    button {
      width: 80%;
      background-color: var(--violet);
      color: white;
      padding: 14px 20px;
      margin: 8px 0;
      border: none;
      border-radius: 4px;
    }
    button:hover {
      background-color: var(--violet);
    }
  </style>
  <body>
    <!-- title: 'Success', data: { locations, HubspotOrgId, orgId, accessToken, refreshToken } -->
    <h1>Hubspot Connector</h1>
    <p id="message"><%-message%></p>
    <br />
    <% if (title==='Success' ) { %>
    <form>
      <div class="form-group">
        <label for="locations">Name your hubspot account</label>
        <br>
        <input type="text" name="channelName" id="channelName">
        <p style="font-size: smaller; color: var(--gray);">Skip to use the default name 'hubspot channel'</p>
      </div>
      <button
        id="submitBtn"
        onclick="submitPref()"
        type="button"
        class="btn btn-warning"
      >
        Submit
      </button>
    </form>
    <% } %>
    <script>
      let orgId,accountId, channelName, cName;

      if ('<%= title %>' === 'Success') {
        orgId = '<%= orgId %>';
        accountId = '<%= accountId %>';
      }


      function submitPref(e) {
        const submitbtn = document.getElementById('submitBtn');
        if (submitbtn) {
          submitbtn.innerText = 'Submitting...';
          submitbtn.disabled = true;
        }
        const channelNameInput = document.getElementById('channelName');
        if (channelNameInput) {
        channelNameInput.disabled = true;
        channelName = channelNameInput.value;
        }
        const data = {
          orgId,
          accountId,
          channelName,
        };
        fetch('/hubspot/update/channel', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(data),
        })
          .then((res) => res.json())
          .then((data) => {
            
            document.getElementById(
              'message',
            ).innerHTML = `Your hubspot channel has been successfully created!`;
            window.opener.postMessage(data, '*');
          });
      }
    </script>
  </body>
</html>
