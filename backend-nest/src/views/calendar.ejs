<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>Capri > leadconnector calendar Oauth Page</title>
    <style>
        :root {
            --primary: #1a1919;
            --secondary: #282a2e;
            --gray:  #939394;
            --font-color: #EEEEEE;
            --violet: #6b63ff;
        }

        body {
            background-color: var(--primary);
            color: var(--font-color);
            font-family: 'Roboto', sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            text-align: center;
        }

        .container {
            padding: 24px 30px;
            height: 100%;
        }

        .container h2 {
            font-size: 18px;
            font-weight: 500;
            color: var(--font-color);
        } 

        #message {
            font-size: 14px;
            font-weight: 400;
            color: var(--gray);
        }

        .form {
            width: 80%;
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .form-group label {
            margin-bottom: 0.5rem;
            font-size: 14px;
            font-weight: 500;
            color: #bcbcbe;
        }

        button {
            width: 80%;
            background-color: var(--violet);
            color: white;
            padding: 14px 20px;
            margin: 8px 0;
            border: none;
            border-radius: 4px;
        }

        .v2 {
            width: 70%;
            text-align: center;
        }
        .v2 br {
            margin-bottom: 2rem;
        }
        small {
            color: rgb(226, 11, 11);
        }
        
        select {
            width: 100%;
            padding: 12px 20px;
            margin: 8px 0;
            box-sizing: border-box;
            border: 1px solid var(--primary);
            border-radius: 4px;
        }

        /* style select > options */
        select option {
            color: var(--font-color);
            background-color: var(--primary);
            font-size: 16px;
            font-weight: 500;
        }
    </style>
</head>

<body class="container">
    <h2>Capri leadconnector integration</h2>
    <p id="message"><%-message%></p>
    <br>
    <% if (success ) { %>
        <form>
            <div class="form-group">
                <label for="calendars">Select a calendar</label><span style="color: red;">*</span>
                <select name="calendars" id="calendars" onchange="changeCalendarSelect(this)">
                </select>
            </div>
            <div class="form-group">
                <label for="timezones">Select a Custom Timezone</label>
                <select name="timezones" id="timezones" onchange="changeTzSelect(this)">
                </select>
            </div>
            <div class="form-group">
                <input type="checkbox" id="contactTz" value="checkedValue" name="contactTz">
                <label for="contactTz">Use contact timezone over the calendar timezone if available</label>
            </div>
            <button id="submitbtn" onclick="submitCalendar()" type="button" class="btn btn-warning">Submit</button>
        </form>
    <% } %>
    <br>
    <script>
        const credId = '<%= credentialId %>';
        const accountId = '<%= accountId %>';
        const orgId = '<%= orgId %>';
        const locationId = '<%= locationId %>';
        const businessName = '<%= businessName %>';
        const defaultTz = '<%= defaultTz %>';
        const calendars = JSON.parse(`<%= JSON.stringify(calendars) %>`.replace(/&#34;/g, '"'));
        const timezones = JSON.parse(`<%= JSON.stringify(timezones) %>`.replace(/&#34;/g, '"'));
        const connectionData = JSON.parse(`<%= JSON.stringify(connectionData) %>`.replace(/&#34;/g, '"'));
        let timezone, calendarId, calendarName;
        
        const tzselectId = document.getElementById('timezones');
        const calendarSelectId = document.getElementById('calendars');
        const contactTzCheckBox = document.getElementById('contactTz');

        calendars.forEach(calendar => {
            const option = document.createElement('option');
            option.value = calendar.id;
            option.text = calendar.name;
            calendarSelectId.appendChild(option);
        });

        [defaultTz, ...timezones].forEach(tz => {
            const option = document.createElement('option');
            option.value = tz;
            option.text = tz;
            tzselectId.appendChild(option);
        });

        calendarId = calendars[0].id;
        calendarName = calendars[0].name;
        timezone = defaultTz;

        function changeCalendarSelect(sel) {
            calendarName = sel.options[sel.selectedIndex].text
            calendarId = sel.value
            
        }

        function changeTzSelect(sel) {
            timezone = sel.value
            
        }

        // a submit function with a fetch 
        function submitCalendar(e) {
            const submitbtn = document.getElementById('submitbtn')
            if (submitbtn){
                submitbtn.innerText = 'Submitting...';
                submitbtn.disabled = true;
            }
            let useContactTz = false;
            if (contactTzCheckBox) {
                if (contactTzCheckBox.checked) {
                    useContactTz = true;
                }
            }

            fetch('/leadconnector/preferences', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    calendarId,
                    timezone,
                    orgId,
                    calendarName,
                    accountId,
                    businessName,
                    useContactTz,
                    connectionData
                })
            })
            .then(res => res.json())
            .then(data => {
                if (data?.error){
                document.getElementById('message').innerHTML = '<p>' + 'Calendar already connected to the organization' + '</p>';
                } else {
                document.getElementById('message').innerHTML = '<p>' + 'Your Gohighlevel calendar has been connected !' + '</p>';
                }
                let d = {}
                if (data?.error){
                    d.error = data.error;
                    if (submitbtn){
                        submitbtn.innerText = 'Submit';
                        submitbtn.disabled = false;
                    }
                } else {
                    d = {
                        credentialId: credId,
                        author: businessName,
                        providerName: 'ghlCalendar',
                        accountId,
                        name: calendarName,
                    }
                }
                window.opener.postMessage(d, '*')
                }).catch(err =>{
                    
                });
        }
    </script>
</body>

</html>