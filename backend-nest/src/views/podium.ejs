<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Podium Connector</title>
  </head>
  <style>
    :root {
      --third: #00adb5;
      --fourth: #eeeeee;
      --primary: #1a1919;
      --secondary: #282a2e;
      --gray: #939394;
      --font-color: #eeeeee;
      --violet: #6b63ff;
    }

    body {
      background-color: var(--primary);
      color: var(--font-color);
      font-family: 'Roboto', sans-serif;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      text-align: center;
    }

    select {
      width: 100%;
      padding: 12px 20px;
      margin: 8px 0;
      box-sizing: border-box;
      border: 1px solid #ccc;
      border-radius: 4px;
    }
    .container {
      padding: 24px 30px;
      height: 100%;
    }

    #message {
      font-size: 14px;
      font-weight: 400;
      color: var(--gray);
    }

    .form {
      width: 80%;
    }

    .form-group {
      margin-bottom: 1rem;
    }

    .form-group label {
      margin-bottom: 0.5rem;
      font-size: 14px;
      font-weight: 500;
      color: #bcbcbe;
    }

    button {
      width: 80%;
      background-color: var(--violet);
      color: white;
      padding: 14px 20px;
      margin: 8px 0;
      border: none;
      border-radius: 4px;
    }
    button:hover {
      background-color: var(--violet);
    }
  </style>
  <body>
    <!-- title: 'Success', data: { locations, podiumOrgId, orgId, accessToken, refreshToken } -->
    <h1>Podium Connector</h1>
    <p id="message"><%-message%></p>
    <br />
    <% if (title==='Success' ) { %>
    <form>
      <div class="form-group">
        <label for="locations">Select a Podium location</label>
        <select
          onchange="changeSelect(this)"
          class="form-control"
          name="locations"
          id="locations"
        ></select>
      </div>
      <button
        id="submitBtn"
        onclick="submitPref()"
        type="button"
        class="btn btn-warning"
      >
        Submit
      </button>
    </form>
    <% } %>
    <script>
      let orgId,
        accessToken,
        refreshToken,
        locationName,
        locations,
        podiumOrgId;
      let locationId = document.getElementById('locations').value;

      if ('<%= title %>' === 'Success') {
        orgId = '<%= orgId %>';
        accessToken = '<%= accessToken %>';
        podiumOrgId = '<%= podiumOrgId %>';
        refreshToken = '<%= refreshToken %>';
        podiumOrgId = '<%= podiumOrgId %>';
        locations = JSON.parse(
          `<%= JSON.stringify(locations) %>`.replace(/&#34;/g, '"'),
        );
        // deep print locations
        (Object.keys(locations) || []).forEach((key) => {
          
        });
      }

      const locationIdDom = document.getElementById('locations');
      locations.forEach((location) => {
        const option = document.createElement('option');
        option.value = location.uid;
        option.text = location.displayName;
        locationIdDom.appendChild(option);
      });

      locationName = locations?.[0]?.displayName || '';
      locationId = locations?.[0]?.uid || '';

      function changeSelect(sel) {
        locationName = sel.options[sel.selectedIndex].text;
        locationId = sel.value;
      }

      function submitPref(e) {
        const submitbtn = document.getElementById('submitBtn');
        if (submitbtn) {
          submitbtn.innerText = 'Submitting...';
          submitbtn.disabled = true;
        }
        const data = {
          locationId,
          orgId,
          tokens: {
            accessToken,
            refreshToken,
          },
          podiumOrgId,
          locationName,
        };
        fetch('/podium/connect/location', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(data),
        })
          .then((res) => res.json())
          .then((data) => {
            
            document.getElementById(
              'message',
            ).innerHTML = `Your Podium location '${locationName}' has been successfully connected!`;
            if (locationId) window.opener.postMessage(data, '*');
          });
      }
    </script>
  </body>
</html>
