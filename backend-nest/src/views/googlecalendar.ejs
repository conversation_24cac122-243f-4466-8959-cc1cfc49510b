<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>Google Calendar</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/4.5.2/css/bootstrap.min.css">
    <style>
        body {
            padding-top: 50px;
        }
        button {
            width: 100%;
            background-color: #393E46;
            color: white;
            padding: 14px 20px;
            margin: 8px 0;
            border: none;
            border-radius: 4px;
        }
    </style>
</head>

<body class="container">
    <main>
        <!-- if success display the profile picture -->
        <%= message %>
            <% if (success) { %>
                <div class="jumbotron" id="jumbo">
                    <img src="<%= user.picture %>" alt="<%= user.name %>" class="rounded-circle">
                    <h1 class="display-5">Hello, <%= user.name %>!</h1>
                    <hr class="my-4">
                    <p class="lead">Please select a calendar to connect to your account.</p>
                </div>
                <% } %>
                    <% if (success && calendars.length> 0) { %>
                        <form>
                            <div class="form-group">
                                <label for="calendars">Calendars</label>
                                <select onchange="changeSelect(this)" class="form-control" name="calendars" id="calendars">
                                    <% calendars.forEach(calendar=> { %>
                                        <option value="<%=calendar.id%>">
                                            <!-- <%=calendar.description%> || <%=calendar.summary%> -->
                                            <% if (calendar.summary !== '') { %>
                                                <%=calendar.summary%>
                                                    <% } else { %>
                                                        <%=calendar.description%>
                                                            <% } %>
                                        </option>
                                        <% }) %>
                                </select>
                            </div>
                            <button onclick="submitCalendar()" type="button" class="btn btn-primary">Submit</button>
                        </form>
                        <% } %>
    </main>
</body>

<script>
    let orgId, accountId, credentialId, userName, providerName, calendarName;
    if ( '<%=success%>' == 'true' ) {
        orgId = '<%=orgId%>';
        accountId = '<%=accountId%>';
        credentialId = '<%=credentialId%>';
        userName = '<%=userName%>';
        providerName = '<%=providerName%>';
        calendarName = "";
    }

    function changeSelect(sel) {
            calendarName = sel.options[sel.selectedIndex].text
    }

    function submitCalendar(e) {
        const calendarId = document.getElementById('calendars').value;
        fetch('/googlecalendar/preferences', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                calendarId,
                orgId,
                accountId,
                calendarName,
            })
        }).then(res => res.json())
            .then(data => {
                const parentData = {
                    credentialId,
                    author: userName,
                    providerName,
                    accountId,
                    name: calendarName,
                }
                document.getElementById('jumbo').innerHTML = '<h1 class="display-4">Success !</h1><hr class="my-4"><p class="lead">You have successfully connected your Google Calendar to your account.</p>';
                window.opener.postMessage(parentData, '*')
            })
            .catch(err => 
    }

</script>

</html>