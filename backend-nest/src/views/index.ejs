<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Capri-LeadConnector Oauth Page</title>
</head>
<style>
    :root {
        --third: #00ADB5;
        --fourth: #EEEEEE;
        --primary: #1a1919;
        --secondary: #282a2e;
        --gray:  #939394;
        --font-color: #EEEEEE;
        --violet: #6b63ff;
    }

    body {
        background-color: var(--primary);
        color: var(--font-color);
        font-family: 'Roboto', sans-serif;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        text-align: center;
    }

    .container {
            padding: 24px 30px;
            height: 100%;
        }

    #message {
        font-size: 14px;
        font-weight: 400;
        color: var(--gray);
    }

    .form {
            width: 80%;
    }

    .form-group {
        margin-bottom: 1rem;
    }

    #v2msg {
        font-size: 13px;
        font-weight: 400;
        color: var(--gray);
    }

    .form-group label {
        margin-bottom: 0.5rem;
        font-size: 14px;
        font-weight: 500;
        color: #bcbcbe;
    }

    button {
        width: 80%;
        background-color: var(--violet);
        color: white;
        padding: 14px 20px;
        margin: 8px 0;
        border: none;
        border-radius: 4px;
    }
    button:hover {
        background-color: var(--primary);
    }

    #migratebtn {
        font-weight: 500;
        font-size: 14px;
        box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.2);
    }

    .v2 {
        width: 77%;
        text-align: center;
    }
    .v2 br {
        margin-bottom: 2rem;
    }
    small {
        color: rgb(196, 45, 45);
    }

#snackbar {
  visibility: hidden;
  min-width: 250px;
  margin-left: -200px;
  background-color: var(--secondary);
  color: var(--font-color);
  text-align: center;
  border-radius: 2px;
  padding: 16px;
  position: fixed;
  z-index: 1;
  left: 50%;
  bottom: 30px;
  font-size: 14px;
}

#cancelbtn {
    background-color: var(--font-color);
    color: var(--primary);
    border: 2px solid var(--primary);;
}
#snackbar.show {
  visibility: visible;
  -webkit-animation: fadein 0.5s, fadeout 0.5s 2.5s;
  animation: fadein 0.5s, fadeout 0.5s 2.5s;
}

@-webkit-keyframes fadein {
  from {bottom: 0; opacity: 0;} 
  to {bottom: 30px; opacity: 1;}
}

@keyframes fadein {
  from {bottom: 0; opacity: 0;}
  to {bottom: 30px; opacity: 1;}
}

@-webkit-keyframes fadeout {
  from {bottom: 30px; opacity: 1;} 
  to {bottom: 0; opacity: 0;}
}

@keyframes fadeout {
  from {bottom: 30px; opacity: 1;}
  to {bottom: 0; opacity: 0;}
}
</style>

<body class="container">
    <h2>Capri - LeadConnnector integration</h2>
    <p id="message"><%-message%></p>
    <br>
    <div id="v2" class="v2">
        <% if (version === 'v2') { %>
        <p id="v2msg">This account has data in our legacy version. Do you want to import this data now?</p>
        <small>( legacy app data will not be affected )</small>
        <!-- <small>Note: you have <%-legacyRemaining%> other locations in v2 to migrate</small> -->
        <br><br><br>
        <button id="migratebtn" onclick="showSnackbar()">Import Data</button>
        <button id="cancelbtn" onclick="cancel()">Do Not Import Data</button>
        <% } %>
    </div>
    <div id="snackbar">Data importing started. This may take a few seconds...</div>
    <script>
        let channelData = {};
        let credentialId, locationId, accountId, providerName, businessName;
        let title = '<%= title %>';
        let userId = '<%= userId %>';
        let orgId = '<%= orgId %>';
        let version = '<%= version %>';
        let v2Data = JSON.parse((`<%= JSON.stringify(v2loc) %>`||'').replace(/&#34;/g, '"'));
        
        if (title === 'Success') {
            credentialId = '<%= credentialId %>';
            locationId = '<%= locationId %>';
            accountId = '<%= accountId %>';
            providerName = '<%= providerName %>';
            businessName = '<%= businessName %>';

            channelData = {
                credentialId,
                locationId,
                accountId,
                providerName,
                name: businessName,
            };
        } else if (title == 'Failure') {
            channelData = {
                error: '<%= message %>',
            };
        }
        

        if (version !== 'v2') {
            window.opener.postMessage(channelData, '*');
        }
        
        // function migrateOne(){
        //     alert('');
        //     const elemc = document.getElementById('cancelbtn')
        //     if (elemc) elemc.innerHTML = 'Close';
        //     try {
        //         migrate();
        //     } catch (err) {
        
        //     }
        // }

        function migrate(){
            fetch(`/migration/schedule`,{
                method: 'POST',
                body: JSON.stringify({
                    orgId,
                    locationIds: [locationId],
                    userId: userId,
                }),
                headers: {
                "Content-type": "application/json",
                }
            }).catch(err => 
            window.opener.postMessage(channelData, '*');
        }

        function cancel(){
            if (accountId){
                window.opener.postMessage(channelData, '*');
            }
        }

        function showSnackbar() {
            var x = document.getElementById("snackbar");
            x.className = "show";
            setTimeout(function(){ 
                x.className = x.className.replace("show", ""); 
                migrate(); 
            }, 3000);
        }
    </script>    
</body>

</html>