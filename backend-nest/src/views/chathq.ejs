<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>Chat HQ - Capri Connection</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/4.5.2/css/bootstrap.min.css">
    <style>
        :root {
                --primary: #222831;
                --secondary: #393E46;
                --third: #00ADB5;
                --fourth: #EEEEEE;
            }
        body {
            padding-top: 50px;
        }

        select {
            width: 100%;
            padding: 12px 20px;
            margin: 8px 0;
            box-sizing: border-box;
            border: 1px solid #ccc;
            border-radius: 4px;
        }

        /* style select > options */
        select option {
            color: black;
            background-color: var(--fourth);
            font-size: 16px;
            font-weight: 500;
        }

        button {
            width: 100%;
            background-color: var(--secondary);
            color: white;
            padding: 14px 20px;
            margin: 8px 0;
            border: none;
            border-radius: 4px;
        }
        form {
            width: 90%;
        }
    </style>
</head>

<body class="container">
    <main>
        <!-- if success display the profile picture -->
        <p><%= message %></p>
        <% if (success) { %>
            <div class="jumbotron" id="jumbo">
                <h1 class="display-4">Hello, <%-userName%>!</h1>
                <hr class="my-4">
                <p class="lead"> Please select a livechat widget to continue</p>
            </div>
        <% } %>
        <% if (success && widgets.length> 0) { %>
            <form>
                <div class="form-group">
                    <label for="widgets">Livechat widgets</label>
                    <select name="widgets" id="widgets" onchange="changeSelect(this)">
                    </select>
                </div>
                <button onclick="submitPreference()" type="button" class="btn btn-primary">Submit</button>
            </form>
        <% } %>
    </main>
</body>

<script>
    let orgId, accountId, userName, widgetId, widgetName, accId, accessToken;
    if ( '<%= success %>' == 'true' ) {
        orgId = '<%= orgId %>';
        accountId = '<%= accountId %>';
        accId = '<%= accId %>';
        userName = '<%= username %>';
        widgets = JSON.parse(`<%= JSON.stringify(widgets) %>`.replace(/&#34;/g, '"'));
        accessToken = '<%= accessToken %>'
    }
    const widgetSelectID = document.getElementById('widgets');
    widgets.forEach(widget => {
            const option = document.createElement('option');
            option.value = widget.id;
            option.text = widget.name;
            widgetSelectID.appendChild(option);
        });
    widgetId = widgets[0].id;
    widgetName = widgets[0].name;
    function changeSelect(sel) {
            widgetName = sel.options[sel.selectedIndex].text
            widgetId = sel.value
            
    }

    function submitPreference(e) {
        const calendarId = document.getElementById('widgets').value;
        fetch('/chathq/preferences', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                orgId,
                accountId,
                widgetId,
                widgetName,
                author: userName,
                accId,
                accessToken
            })
        }).then(res => res.json())
            .then(data => {
                document.getElementById('jumbo').innerHTML = '<h1 class="display-4">Success !</h1><hr class="my-4"><p class="lead">You have successfully connected your Google Calendar to your account.</p>';
                if (accountId) window.opener.postMessage({
                    status: 'success',
                    data: data
                }, '*')
                else window.opener.postMessage({
                    status: 'failed',
                }, '*')
            })
            .catch(err => {
                window.opener.postMessage({
                    status: 'failed',
                }, '*')
                
            });
    }

</script>

</html>