import { Test, TestingModule } from '@nestjs/testing';
import { AgentTemplatesController } from './agent-templates.controller';

describe('AgentTemplatesController', () => {
  let controller: AgentTemplatesController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AgentTemplatesController],
    }).compile();

    controller = module.get<AgentTemplatesController>(AgentTemplatesController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
