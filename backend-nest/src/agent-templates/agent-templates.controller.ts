import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { AuthGuard } from 'src/auth/auth.guard';
import {
  AgentTemplates,
  VariableDetail,
} from 'src/mongo/schemas/agent-templates/agent-templates.schema';
import { AgentTemplatesService } from './agent-templates.service';
import { RequestWithUser } from 'src/auth/auth.interface';

interface CreateTemplateDto {
  name: string;
  prompt: string;
  description?: string;
  tags?: string[];
  variables?: VariableDetail[];
  organizationId: string;
}

interface UpdateTemplateDto extends Partial<CreateTemplateDto> {
  isDeleted?: boolean;
}

@Controller('agent-templates')
@UseGuards(AuthGuard)
export class AgentTemplatesController {
  constructor(private readonly agentTemplatesService: AgentTemplatesService) {}

  @Post()
  async create(
    @Body() createTemplateDto: CreateTemplateDto,
  ): Promise<AgentTemplates> {
    return this.agentTemplatesService.create({
      ...createTemplateDto,
      organizationId: createTemplateDto.organizationId,
    });
  }

  @Get()
  async findAll(
    @Query('organizationId') organizationId: string,
    @Query('tags') tags?: string,
  ): Promise<AgentTemplates[]> {
    if (tags) {
      const tagArray = tags.split(',').filter(Boolean);
      return this.agentTemplatesService.findByTags(tagArray, organizationId);
    }
    return this.agentTemplatesService.findAll(organizationId);
  }

  @Get('all')
  async findAllTemplates(
    @Query('organizationId') organizationId: string,
    @Req() req: RequestWithUser,
  ): Promise<AgentTemplates[]> {
    const token = req.headers['x-api-token'] as string;
    
    return this.agentTemplatesService.findAllTemplates(organizationId, token);
  }

  @Get(':id')
  async findOne(
    @Param('id') id: string,
    @Query('organizationId') organizationId: string,
  ): Promise<AgentTemplates> {
    return this.agentTemplatesService.findOne(id, organizationId);
  }

  @Put(':id')
  async update(
    @Param('id') id: string,
    @Body() updateTemplateDto: UpdateTemplateDto,
    @Query('organizationId') organizationId: string,
  ): Promise<AgentTemplates> {
    return this.agentTemplatesService.update(
      id,
      organizationId,
      updateTemplateDto,
    );
  }

  @Delete(':id')
  async remove(
    @Param('id') id: string,
    @Query('organizationId') organizationId: string,
    @Query('hard') hard?: boolean,
  ): Promise<AgentTemplates> {
    if (hard) {
      return this.agentTemplatesService.hardDelete(id, organizationId);
    }
    return this.agentTemplatesService.softDelete(id, organizationId);
  }
}
