import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { AgentTemplates } from 'src/mongo/schemas/agent-templates/agent-templates.schema';
import { TokensService } from 'src/tokens/tokens.service';

interface CreateTemplateDto {
  name: string;
  prompt: string;
  description?: string;
  tags?: string[];
  variables?: Array<{
    name: string;
    description: string;
    type?: string;
  }>;
  organizationId: string;
}

interface UpdateTemplateDto extends Partial<CreateTemplateDto> {
  isDeleted?: boolean;
}

@Injectable()
export class AgentTemplatesService {
  constructor(
    @InjectModel(AgentTemplates.name)
    private readonly agentTemplatesModel: Model<AgentTemplates>,
    private readonly tokenService: TokensService,
  ) {}

  async create(createTemplateDto: CreateTemplateDto): Promise<AgentTemplates> {
    const createdTemplate = new this.agentTemplatesModel(createTemplateDto);
    return createdTemplate.save();
  }

  async findAll(organizationId: string): Promise<AgentTemplates[]> {
    return this.agentTemplatesModel
      .find({ organizationId, isDeleted: false })
      .exec();
  }

  async findAllTemplates(
    organizationId: string,
    token: string,
  ): Promise<AgentTemplates[]> {
    if (token && !organizationId) {
      const tokenDoc = await this.tokenService.getTokenByToken({
        token: token,
      });
      organizationId = tokenDoc.orgId;
    }
    return this.agentTemplatesModel
      .find({ organizationId, isDeleted: false })
      .exec();
  }

  async findOne(id: string, organizationId: string): Promise<AgentTemplates> {
    return this.agentTemplatesModel
      .findOne({ _id: id, organizationId, isDeleted: false })
      .exec();
  }

  async update(
    id: string,
    organizationId: string,
    updateTemplateDto: UpdateTemplateDto,
  ): Promise<AgentTemplates> {
    return this.agentTemplatesModel
      .findOneAndUpdate(
        { _id: id, organizationId },
        { $set: updateTemplateDto },
        { new: true },
      )
      .exec();
  }

  async softDelete(
    id: string,
    organizationId: string,
  ): Promise<AgentTemplates> {
    return this.agentTemplatesModel
      .findOneAndUpdate(
        { _id: id, organizationId },
        { $set: { isDeleted: true } },
        { new: true },
      )
      .exec();
  }

  async hardDelete(
    id: string,
    organizationId: string,
  ): Promise<AgentTemplates> {
    return this.agentTemplatesModel
      .findOneAndDelete({ _id: id, organizationId })
      .exec();
  }

  async findByTags(
    tags: string[],
    organizationId: string,
  ): Promise<AgentTemplates[]> {
    return this.agentTemplatesModel
      .find({
        organizationId,
        isDeleted: false,
        tags: { $in: tags },
      })
      .exec();
  }
}
