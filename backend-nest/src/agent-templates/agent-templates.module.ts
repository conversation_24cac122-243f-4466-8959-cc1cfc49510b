import { Modu<PERSON> } from '@nestjs/common';
import { AgentTemplatesController } from './agent-templates.controller';
import { AgentTemplatesService } from './agent-templates.service';
import { MongooseModule } from '@nestjs/mongoose';
import {
  AgentTemplates,
  AgentTemplatesSchema,
} from 'src/mongo/schemas/agent-templates/agent-templates.schema';
import { AuthModule } from 'src/auth/auth.module';
import { TokensModule } from 'src/tokens/tokens.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: AgentTemplates.name, schema: AgentTemplatesSchema },
    ]),
    AuthModule,
    TokensModule,
  ],
  controllers: [AgentTemplatesController],
  providers: [AgentTemplatesService],
  exports: [AgentTemplatesService],
})
export class AgentTemplatesModule {}
