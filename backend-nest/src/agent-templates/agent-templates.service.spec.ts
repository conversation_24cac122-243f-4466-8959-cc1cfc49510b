import { Test, TestingModule } from '@nestjs/testing';
import { AgentTemplatesService } from './agent-templates.service';

describe('AgentTemplatesService', () => {
  let service: AgentTemplatesService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [AgentTemplatesService],
    }).compile();

    service = module.get<AgentTemplatesService>(AgentTemplatesService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
