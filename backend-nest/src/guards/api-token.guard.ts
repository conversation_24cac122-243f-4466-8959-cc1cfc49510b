import {
  CanActivate,
  ExecutionContext,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { OrganizationService } from 'src/organization/services/organization.service';
import { TokensService } from 'src/tokens/tokens.service';

@Injectable()
export class ApiTokenGuard implements CanActivate {
  constructor(private readonly tokensService: TokensService) {}
  async canActivate(context: ExecutionContext) {
    const request = context.switchToHttp().getRequest();
    const apiToken = request.headers['x-api-token'];

    if (!apiToken) {
      throw new UnauthorizedException(
        'Please include the API tokens in the headers',
      );
    }
    const validateToken = await this.tokensService.validateToken(apiToken);
    if (!validateToken) {
      throw new UnauthorizedException('Invalid API token');
    }
    const orgId = validateToken.orgId;
    request.orgId = orgId;
    // 

    return true;
  }
}
