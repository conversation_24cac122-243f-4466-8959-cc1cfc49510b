import {
  CanActivate,
  ExecutionContext,
  Injectable,
  UnauthorizedException,
  mixin
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { OrganizationService } from 'src/organization/services/organization.service';

/**
 * Allows access by checking whether the user accessing the route has access to it
 * @example '@UseGuards(AuthGuard, allowAccessGuard('fallbacks', 'read'))'
 * @param feature 'fallbacks' | 'emulator' | 'agents' | 'settings' | 'billing' 
 * @param accessType 'read' | undefined
 */
export const allowAccessGuard = (feature: string, accessType: string = '') => {
  @Injectable()
  class RoleGuardMixin implements CanActivate {
    constructor(
      readonly organizationService: OrganizationService,
    ) {}

    canActivate(
      context: ExecutionContext
    ): boolean | Promise<boolean> | Observable<boolean>{
      const request = context.switchToHttp().getRequest();
      const userId = request.userId;

      return this.validateAccess(userId, request);
    }

    async validateAccess(userId: string, request): Promise<boolean> {
      if (!userId) {
        throw new UnauthorizedException();
      }

      const organization = await this.organizationService.getOrganization({
        'members.userId': userId,
      }, {
        'members.$': 1,
        'admin': 1,
      });

      if (!organization) {
        throw new UnauthorizedException();
      }
      const access = organization.members?.[0].access?.[feature]?.accessVal;
      const admins = organization.admin;

      if (admins.includes(userId)) {
        request['isAdmin'] = true;
        return true;
      }

      if (access === 'none') {
        return false;
      }
      if ((access === 'all') || (access === 'read' && accessType === 'read')) {
        return true;
      }
      return false;
    }
  }

  const guard = mixin(RoleGuardMixin);
  return guard;
}