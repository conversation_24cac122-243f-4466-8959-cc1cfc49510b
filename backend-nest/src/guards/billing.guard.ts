import {
  CanActivate,
  ExecutionContext,
  HttpException,
  HttpStatus,
  Inject,
  Injectable,
  Logger,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Observable } from 'rxjs';
import { planMap } from 'src/billing/billing.map';
import { IS_PUBLIC_KEY } from 'src/decorators/public.decorator';
import { MongoAgentService } from 'src/mongo/service/agent/mongo-agent.service';
import { MongoOrganizationService } from 'src/mongo/service/organization/mongo-organization.service';
import { RequestService } from 'src/request.service';

// @Injectable()
// export class BillingLimitsGuard implements CanActivate {
//   public get reflector(): Reflector {
//     return this._reflector;
//   }
//   // private readonly logger = new Logger(BillingLimitsGuard.name)
//   constructor(
//     readonly requestService: RequestService,
//     readonly mongoAgentService: MongoAgentService,
//     readonly mongoOrganizationService: MongoOrganizationService,
//     private readonly _reflector: Reflector,
//   ) {
//     
//   }
//   canActivate(
//     context: ExecutionContext,
//   ): boolean | Promise<boolean> | Observable<boolean> {
//     

//     //   this.logger.log(BillingLimitsGuard.name)
//     
//     const isPublic = this.reflector.getAllAndOverride<boolean>(IS_PUBLIC_KEY, [
//       context.getHandler(),
//       context.getClass(),
//     ]);
//     if (isPublic) {
//       return true;
//     }
//     const request = context.switchToHttp().getRequest();

//     return this.checkBillingQuota();
//   }

//   async checkBillingQuota(): Promise<boolean> {
//     

//     const orgId = this.requestService.getOrgDetails().orgId;
//     const organizationSetup = this.requestService.getUser().organizationSetup;
//     

//     if (!organizationSetup) {
//       //user has not setup organization yet. Let him set the organization
//       return true;
//     }
//     

//     const billingMap = planMap;
//     // const billingDetails = this.requestService.getBillingDetails();
//     const billingDetails = this.requestService.getOrgDetails().billing;
//     

//     //check if billing cycle has ended
//     const { startDate, plan, email, status } = billingDetails;
//     const endDate_14 = startDate + 14 * 24 * 60 * 60 * 1000; //14 days in ms
//     const endDate_30 = startDate + 30 * 24 * 60 * 60 * 1000; //30 days in ms
//     //check if the status is suspended
//     if (status === 'suspended') {
//       throw new HttpException(
//         'Should redirect to the billing plan page',
//         HttpStatus.PAYMENT_REQUIRED,
//       );
//     }
//     //check if trial has ended
//     if (plan === 'trial' && Date.now() > endDate_14) {
//       //if the trial expired and they are not on suspended, change it to suspended
//       const orgStatusUpdated =
//         await this.mongoOrganizationService.updateOrganization(
//           { _id: orgId },
//           { 'billing.status': 'cancelled' },
//         );
//       throw new HttpException(
//         'Should redirect to the billing plan page',
//         HttpStatus.PAYMENT_REQUIRED,
//       );
//     }
//     //check for resource limit
//     const agentCount = await this.mongoAgentService.getAgentsCount({ orgId });
//     let maxAgentAllowed = billingMap[billingDetails.plan]?.limits?.maxAgents;
//     if (maxAgentAllowed === undefined) {
//       // return;
//       
//       throw new HttpException(`Invalid plan`, HttpStatus.FORBIDDEN);
//     }
//     if (agentCount === maxAgentAllowed) {
//       throw new HttpException(
//         `You have reached the maximum number of agents allowed for your current plan. Please upgrade your plan to add more agents.`,
//         HttpStatus.PAYMENT_REQUIRED,
//       );
//     } else if (agentCount > maxAgentAllowed) {
//       throw new HttpException(
//         `Your current plan does not support the number of agents you have. Please upgrade your plan or disable ${
//           agentCount - maxAgentAllowed
//         } agents to continue.`,
//         HttpStatus.PAYMENT_REQUIRED,
//       );
//     }
//     

//     return true;
//   }
// }

@Injectable()
export class BillingLimitsGuard implements CanActivate {
  constructor(private reflector: Reflector) {}
  private readonly logger = new Logger(BillingLimitsGuard.name);
  canActivate(
    context: ExecutionContext,
  ): boolean | Promise<boolean> | Observable<boolean> {
    this.logger.log(BillingLimitsGuard.name);
    const isPublic = this.reflector.getAllAndOverride<boolean>(IS_PUBLIC_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);
    if (isPublic) {
      return true;
    }
    const request = context.switchToHttp().getRequest();
    return this.testin();
  }

  async testin(): Promise<boolean> {
    return true;
  }
}
