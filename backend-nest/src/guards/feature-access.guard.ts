import { Injectable, CanActivate, ExecutionContext } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { FeatureFlagService } from 'src/feature-flags/feature-flags.service';

@Injectable()
export class FeatureAccessGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    private featureFlagService: FeatureFlagService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const requiredFeature = this.reflector.get<string>(
      'feature',
      context.getHandler(),
    );
    if (!requiredFeature) {
      return true;
    }

    const request = context.switchToHttp().getRequest();
    const orgId = request.cookies['orgId'];
    

    return this.featureFlagService.isFeatureEnabled(orgId, requiredFeature);
  }
}
