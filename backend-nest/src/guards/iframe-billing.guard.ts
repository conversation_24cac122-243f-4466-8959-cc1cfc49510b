import {
  Injectable,
  CanActivate,
  ExecutionContext,
  ForbiddenException,
} from '@nestjs/common';
import { PLANS } from 'src/billing/billing.map';
import { MyLogger } from 'src/logger/logger.service';
import { MongoOrganizationService } from 'src/mongo/service/organization/mongo-organization.service';

@Injectable()
export class IframeBillingGuard implements CanActivate {
  constructor(
    private readonly myLogger: MyLogger,
    private readonly organizationMongoService: MongoOrganizationService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const orgId = request.orgId;
    const organization = await this.organizationMongoService.getOrganization({
      _id: orgId,
    });
    this.myLogger.log({
      message: { name: 'coolName', orgId, organization },
      context: IframeBillingGuard.name,
    });
    const cond_1 = organization.billing.plan === 'enterprise';
    const cond_2 =
      organization.billing.plan === 'pro' &&
      organization.billing?.billingCycle === 'yearly';
    const cond_3 = organization.billing.plan === PLANS.CUSTOMPLAN1;
    this.myLogger.log({
      message: { cond_1, cond_2 },
      context: IframeBillingGuard.name,
    });
    if (cond_1 || cond_2 || cond_3) {
      return true;
    }

    throw new ForbiddenException('Your plan does not support this feature.');
  }
}
