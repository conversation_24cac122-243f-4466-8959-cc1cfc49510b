import {
  CanActivate,
  ExecutionContext,
  Injectable,
  UnauthorizedException,
  mixin
} from '@nestjs/common';
import { HttpStatusCode } from 'axios';
import { Observable } from 'rxjs';
import { planMap } from 'src/billing/billing.map';
import { OrganizationService } from 'src/organization/services/organization.service';

/**
 * Allows access by checking whether the user accessing the route has access to it
 * @example '@UseGuards(BillingExcludeGuard('webscraping'))'
 * @param featureType defines the feature that the route is for, on which this guard is implemented 
 */
export const BillingExcludeGuard = (featureType: string) => {
  @Injectable()
  class BillingExcludeGuardMixin implements CanActivate {
    constructor(
      readonly organizationService: OrganizationService,
    ) { }

    canActivate(
      context: ExecutionContext
    ): boolean | Promise<boolean> | Observable<boolean> {
      const request = context.switchToHttp().getRequest();
      const orgId = request.cookies['orgId'];
      return this.validateAccess(orgId, request);
    }

    async validateAccess(orgId: string, request): Promise<boolean> {
      if (!orgId) {
        throw new UnauthorizedException({
          message: `No organization found, log out and log in again`,
          statusCode: HttpStatusCode.BadRequest,
        });
      }
      let billingDetails = request?.billingDetails;
      if (!billingDetails) {
        const {billing, agentCount} = await this.organizationService.getBillingDetails({ orgId });
        billingDetails = billing;
        request.billingDetails = billingDetails;
      }
      const billingMap = planMap;
      const excludedFeatures = billingMap[billingDetails.plan]?.excludedFeatures;
      if (excludedFeatures?.includes(featureType)) {
        throw new UnauthorizedException({
          message: `Your current plan does not support ${featureType} feature. Please upgrade your plan to access this feature.`,
          statusCode: HttpStatusCode.Forbidden,
        });
      }
      return true;
    }
  }

  const guard = mixin(BillingExcludeGuardMixin);
  return guard;
}

