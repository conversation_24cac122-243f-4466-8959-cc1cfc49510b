import {
  CanActivate,
  ExecutionContext,
  HttpException,
  Injectable,
  UnauthorizedException,
  mixin,
} from '@nestjs/common';
import { HttpStatusCode } from 'axios';
import { Observable } from 'rxjs';
import { planMap } from 'src/billing/billing.map';
import { MyLogger } from 'src/logger/logger.service';
import { MongoAgentService } from 'src/mongo/service/agent/mongo-agent.service';
import { OrganizationService } from 'src/organization/services/organization.service';

@Injectable()
export class BillingLimitsGuard implements CanActivate {
  constructor(
    private readonly mongoAgentService: MongoAgentService,
    private readonly organizationService: OrganizationService,
    private readonly logger: MyLogger,
  ) {}
  canActivate(
    context: ExecutionContext,
  ): boolean | Promise<boolean> | Observable<boolean> {
    const request = context.switchToHttp().getRequest();
    const orgId = request.cookies['orgId'];

    return this.checkBillingQuota(orgId, request);
  }

  async checkBillingQuota(orgId: string, request: any): Promise<boolean> {
    if (!orgId) {
      throw new UnauthorizedException({
        message: `No organization found`,
        statusCode: HttpStatusCode.BadRequest,
      });
    }
    const billingMap = planMap;
    const { billing, agentCount } =
      await this.organizationService.getBillingDetails({ orgId });
    this.logger.log({ message: { billing, agentCount } });
    //Implement the billing.status check here and throw the httpException accordingly
    if (billing.status === 'suspended') {
      throw new HttpException(
        `Your account has been suspended due to non-payment. Please update your payment details to continue.`,
        HttpStatusCode.PaymentRequired,
      );
    }
    // const agentCount = await this.mongoAgentService.getAgentsCount({ orgId });
    let maxAgentAllowed =
      billing?.allowedAgents ?? billingMap[billing.plan]?.limits?.maxAgents;
    

    if (maxAgentAllowed === undefined) {
      this.logger.error({
        message: `@error invalid plan received from orgId: ${orgId}`,
        context: 'BillingLimitsGuard.checkBillingQuota',
      });
      throw new UnauthorizedException({
        message: 'Invalid plan',
        statusCode: HttpStatusCode.Forbidden,
      });
    }
    // if (agentCount === maxAgentAllowed) {
    //   throw new HttpException(
    //     `You have reached the maximum number of agents allowed for your current plan. Please upgrade your plan to add more agents.`,
    //     HttpStatusCode.PaymentRequired,
    //   );
    // }
    if (agentCount > maxAgentAllowed) {
      throw new HttpException(
        `Your current plan does not support the number of agents you have. Please upgrade your plan or disable ${
          agentCount - maxAgentAllowed
        } agents to continue.`,
        HttpStatusCode.PaymentRequired,
      );
    }
    request.billing = billing;
    return true;
  }
}
