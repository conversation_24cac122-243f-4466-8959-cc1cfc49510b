import { Injectable, CanActivate, ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class ClientAuthGuard implements CanActivate {
  constructor(private configService: ConfigService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    
    
    const request = context.switchToHttp().getRequest();
    const clientId = request.headers['x-client-id'];
    const clientSecret = request.headers['x-client-secret'];

    // Alternative: Basic Auth implementation
    // const authHeader = request.headers.authorization;
    // if (authHeader && authHeader.startsWith('Basic ')) {
    //   const base64Credentials = authHeader.split(' ')[1];
    //   const credentials = Buffer.from(base64Credentials, 'base64').toString('ascii');
    //   const [clientId, clientSecret] = credentials.split(':');
    // }

    if (!clientId || !clientSecret) {
      throw new UnauthorizedException('Missing client credentials');
    }
    
    
    const validClientId = this.configService.get<string>('UPHEX_CLIENT_ID') || "uphex-client-id";
    const validClientSecret = this.configService.get<string>('UPHEX_CLIENT_SECRET') || "uphex-client-secret";

    if (clientId !== validClientId || clientSecret !== validClientSecret) {
      throw new UnauthorizedException('Invalid client credentials');
    }

    return true;
  }
}
