import {
  IsString,
  <PERSON>NotEmpty,
  IsO<PERSON>al,
  IsIn,
  IsBoolean,
  IsArray,
  IsObject,
} from 'class-validator';
import { PROVIDERS } from '../../lib/constant';

class Properties {
  @IsOptional()
  @IsString()
  name?: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsString()
  type?: string;

  @IsOptional()
  @IsString()
  value?: string;

  @IsOptional()
  @IsString()
  propertyId?: string;
}

class CustomField {
  @IsOptional()
  @IsString()
  field_value?: string;

  @IsOptional()
  @IsString()
  fieldKey?: string;
}

class CalendarEventAttendee {
  @IsString()
  email: string;
}

class CalendarEvent {
  @IsString()
  summary: string;

  @IsString()
  description: string;

  @IsString()
  startDate: string;

  @IsString()
  endDate: string;

  @IsArray()
  attendees: CalendarEventAttendee[];
}

export class EditResponseActionDto {
  @IsNotEmpty()
  @IsString()
  sessionId: string;

  @IsNotEmpty()
  @IsString()
  eventId: string;

  @IsOptional()
  @IsString()
  accountId?: string;

  @IsOptional()
  @IsString()
  accountName?: string;

  @IsOptional()
  @IsString()
  @IsIn([
    `${PROVIDERS.GHL_CALENDAR}`,
    `${PROVIDERS.GOOGLE_SHEET}`,
    `${PROVIDERS.WEBSITE}`,
    `${PROVIDERS.GOOGLE_CALENDAR}`,
    `${PROVIDERS.Generate_JSON}`,
    `${PROVIDERS.GET}`,
    `${PROVIDERS.PDF}`,
    `${PROVIDERS.CustomField}`,
    `${PROVIDERS.uphex}`,
    `${PROVIDERS.SMS}`,
    `${PROVIDERS.GHL_EMAIL}`,
  ])
  providerName?: string;

  @IsOptional()
  @IsString()
  rowStart?: string;

  @IsOptional()
  @IsString()
  rowEnd?: string;

  @IsOptional()
  @IsString()
  action?: string;

  @IsOptional()
  @IsString()
  startDate?: string;

  @IsOptional()
  @IsString()
  endDate?: string;

  @IsOptional()
  @IsString()
  message?: string;

  @IsOptional()
  @IsBoolean()
  deleted?: Boolean;

  @IsOptional()
  @IsArray()
  properties?: Properties[];

  @IsOptional()
  @IsObject()
  customField?: CustomField;

  @IsOptional()
  @IsObject()
  //set default value to type and text
  slackResponse?: {
    type?: string;
    text?: string;
  };

  @IsOptional()
  @IsArray()
  calendarEvents?: CalendarEvent[];

  @IsOptional()
  @IsObject()
  //set default value to type and text
  sms?: {
    type?: string;
    text?: string;
    phoneId?: string;
  };

  @IsOptional()
  @IsObject()
  ghlEmail?: {
    type?: string;
    text?: string;
    subject?: string;
  };
}
