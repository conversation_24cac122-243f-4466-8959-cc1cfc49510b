import {
  IsString,
  IsNotEmpty,
  IsOptional,
  IsDate,
  ValidateNested,
  IsArray,
  IsObject,
  IsBoolean,
} from 'class-validator';

export class CreateSessionWithoutUserIdDto {
  @IsOptional()
  @IsString()
  orgId?: string;

  @IsNotEmpty()
  @IsString()
  agentId: string;

  @IsOptional()
  @IsObject()
  metaData?: Object;

  @IsOptional()
  @IsString()
  active?: string;

  @IsOptional()
  @IsBoolean()
  saved?: boolean;
}

export class CreateSessionDto extends CreateSessionWithoutUserIdDto {
  @IsOptional()
  @IsString()
  userId?: string;
}
