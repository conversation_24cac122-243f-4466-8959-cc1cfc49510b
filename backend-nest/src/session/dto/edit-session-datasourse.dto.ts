import { IsString, IsNotEmpty, IsOptional, IsDate, ValidateNested, IsArray, IsObject, IsBoolean } from 'class-validator';

export class EditSessionDataSource{
  @IsNotEmpty()
  @IsString()
  orgId: string;

  @IsNotEmpty()
  @IsString()
  agentId: string;

  @IsNotEmpty()
  @IsString()
  userId: string;

  @IsOptional()
  @IsString()
  googlesheet?: string;

  @IsOptional()
  @IsBoolean()
  saved?: boolean;
}