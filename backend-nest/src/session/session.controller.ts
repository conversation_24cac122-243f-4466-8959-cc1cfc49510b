import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Res,
  Delete,
  HttpStatus,
  Query,
  Req,
  HttpException,
  UseGuards,
  Put,
} from '@nestjs/common';
import { Response } from 'express';
import { AgentService } from 'src/agent/agent.service';
import { MongoAgentService } from 'src/mongo/service/agent/mongo-agent.service';
import { MongoOrganizationService } from 'src/mongo/service/organization/mongo-organization.service';
import { SessionService } from './session.service';
import { CreateSessionDto, CreateSessionWithoutUserIdDto } from './dto/create-session.dto';
import { MongoSessionService } from 'src/mongo/service/session/mongo-session.service';
import { UserQueryDto } from './dto/user-query.dto';
import { EmulatorResponseDto } from './dto/emulator-response.dto';
import { SaveChatDto } from './dto/save-chat.dto';
import { EditResponseActionDto } from './dto/edit-response-action.dto';
import { v4 as uuidv4 } from 'uuid';
import { SessionListService } from './outreachService/session-list.service';
import { AuthGuard } from 'src/auth/auth.guard';
import { RequestWithUser } from 'src/auth/auth.interface';
import { BillingLimitsGuard } from 'src/guards/billing/billing-limits.guard';
import { UpdateSessionName } from './dto/updateSessionName_dto';
import { GhlService } from '../organization/services/channels/ghl/ghl.service';
import { MyLogger } from '../logger/logger.service';
import { MongoConversationService } from 'src/mongo/service/conversations/mongo-conversations.service';

@Controller('session')
export class SessionController {
  constructor(
    private readonly sessionService: SessionService,
    private readonly sessionListService: SessionListService,
    private readonly agentService: AgentService,
    private readonly mongoAgentService: MongoAgentService,
    private readonly mongoOrganizationService: MongoOrganizationService,
    private readonly mongoSessionService: MongoSessionService,
    private readonly mongoConversationService: MongoConversationService,
    private readonly ghlService: GhlService,
    private readonly logger: MyLogger,
  ) {}

  @Get('agents/ready/:orgid')
  async getAllAgents(@Param('orgid') orgId: string, @Res() response: Response) {
    const organization = await this.mongoOrganizationService.getOrganization({
      _id: orgId,
    });
    const agents = organization.agents;

    const filteredAgents = await Promise.all(
      agents.map(async (agentId) => {
        const agent = await this.mongoAgentService.getAgent({ _id: agentId });
        if (agent) {
          if (agent.prompts?.prompt.length > 0 && agent.aiProvider) {
            return agent;
          }
        }
      }),
    );
    return response.json({
      message: 'All ready agents belonging to the organization fetched',
      data: filteredAgents,
    });
  }

  @Post('/start')
  @UseGuards(AuthGuard)
  async createSession(
    @Body() createSessionDto: CreateSessionWithoutUserIdDto,
    @Req() request: RequestWithUser,
    @Res() response: Response,
  ) {
    const userId = request.userId;
    const createdSession = await this.sessionService.createSession(
      { ...createSessionDto, userId },
      response,
    );

    response.status(HttpStatus.CREATED).json({
      message: 'Session Successfully Started',
      data: createdSession,
    });
  }


  @Post('duplicate/:sessionId')
  async duplicateSession(
    @Body() createSessionDto: CreateSessionWithoutUserIdDto,
    @Param('sessionId') sessionId: string,
    @Req() req: RequestWithUser,
    @Res() response: Response,
  ) {
    try {
      let userName = req.username;

      const sessionDetails = await this.sessionService.duplicateSession(
        createSessionDto,
        sessionId,
        userName,
      );

      return response.status(HttpStatus.CREATED).json({
        message: 'Session duplicated successfully',
        data: sessionDetails,
      });
    } catch (error) {
      return response.status(400).json({
        error: error.message,
      });
    }
  }

  @Post('history/train')
  async historyTrain(
    @Body() createSessionDto: CreateSessionWithoutUserIdDto,
    @Req() req: RequestWithUser,
    @Res() response: Response,
    @Query('locationId') locationId: string,
    @Query('conversationId') conversationId?: string,
    @Query('contactId') contactId?: string,
  ) {
    const userId = req.userId;

    try {
      const agentDetails = await this.mongoAgentService.getAgent({
        _id: createSessionDto.agentId,
      });

      const agentHistory =
        await this.mongoConversationService.getPaginatedConversations(
          {
            query: {
              resourceId: locationId,
              ...(contactId ? {contactId: contactId}: {conversationId: conversationId}),
              agentId: createSessionDto.agentId,
            },
          },
          { limit: 1000 },
        );

      let userName = req.username;
      let agentName = agentDetails.agentName;
      let allMessages = [];
      let hasNextPage = true;
      let lastMessageIdReceived = undefined;

      while (hasNextPage) {
        const params = {
          lastMessageId: lastMessageIdReceived,
        };

        // Remove undefined values from params
        Object.keys(params).forEach(
          (key) => params[key] === undefined && delete params[key],
        );

        let contactConversationId = conversationId;

        if (agentHistory.length > 0) {
          let agentConversation = agentHistory[0];
          let converation = agentConversation?.conversation[0];

          if (converation) {
            //pushing the agent conversation to allMessages until the last message
            let index = 0;
            while (converation && converation.status === 'delivered') {
              allMessages.push(agentConversation.conversation[index]);
              index++;
              converation = agentConversation?.conversation[index];
            }
          }
          hasNextPage = false;
        } else if (contactId) {
          let contactConversations = await this.ghlService.searchConversations(
            req.uniqueCode,
            locationId,
            {
              contactId: contactId,
            },
          );

          contactConversationId = contactConversations[0].id;
        }
        
        //if allMessages is empty, then fetch the messages from the conversationId
        if (allMessages.length === 0 || hasNextPage) {
          const response = await this.ghlService.getMessagesByConversationId(
            locationId,
            contactConversationId,
            params,
          );

          allMessages = [...allMessages, ...response.messages.messages];
          lastMessageIdReceived = response.messages.lastMessageId;
          hasNextPage = response.messages.nextPage;
        }
      }

      const createdSession = await this.sessionService.createSession(
        {
          ...createSessionDto,
          userId,
        },
        response,
      );

      let sessionId = createdSession._id.toString();

      await this.sessionService.saveTrainingConversation({
        sessionId: sessionId,
        messages: allMessages,
      });

      const formattedSession: any = {
        _id: createdSession.id.toString(),
        active: createdSession.active,
        saved: createdSession.saved,
        basic: {
          orgId: createdSession.basic.orgId,
          userId: createdSession.basic.userId,
          agentId: createdSession.basic.agentId,
          sessionName: createdSession.basic.sessionName,
          userName: userName,
          agentName: agentName,
        },
        dates: {
          createdAt: createdSession.dates.createdAt,
          lastUpdated: createdSession.dates.lastUpdated,
        },
      };

      response.status(201).send(formattedSession);
    } catch (error) {
      this.logger.error({
        message: `Error in historyTrain: ${JSON.stringify(
          error.response.data,
        )}`,
        context: this.constructor.name,
      });
      response.status(500).send(error.response.data);
    }
  }

  @Get('recent')
  async fetchRecentSession(
    @Query('offset') offset: number,
    @Query('limit') limit: number,
    @Query('createdBy')
    createdBy: 'createdByMe' | 'createdByOthers' | 'createdByAnyone',
    @Query('sortBy') sortBy: 'lastUsed' | 'lastCreated',
    @Query('agent') agent: string,
    @Query('orgId') orgId: string,
    @Query('saved') saved: 'true' | 'false',
    @Req() request: RequestWithUser,
    @Res() response: Response,
  ) {
    try {
      const userId = request.userId;
      const list = await this.sessionListService.fetchSessions({
        offset,
        limit,
        createdBy,
        sortBy,
        agent,
        orgId,
        saved,
        userId,
      });
      return response.json({
        message: 'Successfully fetched recent sessions',
        data: list,
      });
    } catch (error) {
      return response.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        error: error.message,
      });
    }
  }

  @Get('/:sessionId')
  async getSession(
    @Param('sessionId') sessionId: string,
    @Res() response: Response,
  ) {
    try {
      const sessionDetails = await this.sessionService.getSession(
        sessionId,
        response,
      );
    } catch (error) {
      throw new Error('Internal Server Error');
    }
  }

  @UseGuards(BillingLimitsGuard)
  @Post('/chat/message')
  async sendMessage(
    @Body() userQueryDto: UserQueryDto,
    @Res() response: Response,
  ) {
    try {
       await this.sessionService.sendMessage(
        userQueryDto,
        response,
      );
    } catch (error) {
      return response.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        message: error.message,
      });
    }
  }

  @Post('/chat/save')
  async saveSession(
    @Body() saveChatDto: SaveChatDto,
    @Res() response: Response,
  ) {
    const { sessionId } = saveChatDto;
    await this.sessionService.saveChat(sessionId, response);
  }

  @Post('/chat/unsave')
  async unSaveSession(
    @Body() saveChatDto: SaveChatDto,
    @Res() response: Response,
  ) {
    const { sessionId } = saveChatDto;
    await this.sessionService.unSaveChat(sessionId, response);
  }

  @Get('/saved/list/:agentId')
  async getSavedSessionFromAgent(
    @Param('agentId') agentId: string,
    @Res() response: Response,
  ) {
    try {
      await this.sessionService.getSavedSessionFromAgent(agentId, response);
    } catch (error) {
      throw new Error('Internal Server Error');
    }
  }

  @Get('/chat/message/info/:sessionId/:eventId')
  async getListOfBotResponse(
    @Param('sessionId') sessionId: string,
    @Param('eventId') eventId: string,
    @Res() response: Response,
  ) {
    const listOfBotResponse = await this.sessionService.getListOfBotResponse(
      sessionId,
      eventId,
      response,
    );
  }

  @Get('chat/message/action/info/:sessionId/:eventId')
  async getSpecificBotResponse(
    @Param('sessionId') sessionId: string,
    @Param('eventId') eventId: string,
    @Res() response: Response,
  ) {
    const specificOfBotResponse =
      await this.sessionService.getSpecificOfBotResponse(
        sessionId,
        eventId,
        response,
      );
  }

  @Post('/chat/response/edit/googleSheet')
  async editGoogleSheet(
    @Body() editResponseActionDto: EditResponseActionDto,
    @Res() response: Response,
  ) {
    await this.sessionService.editGoogleSheet(editResponseActionDto, response);
  }

  @Post('/chat/response/edit/googleCalendar')
  async editGoogleCalendar(
    @Body() editResponseActionDto: EditResponseActionDto,
    @Res() response: Response,
  ) {
    await this.sessionService.editGoogleCalendar(
      editResponseActionDto,
      response,
    );
  }

  @Post('/chat/response/edit/ghlCalendar')
  async editGhlCalendar(
    @Body() editResponseActionDto: EditResponseActionDto,
    @Res() response: Response,
  ) {
    await this.sessionService.editGhlCalendar(editResponseActionDto, response);
  }

  @Post('/chat/response/edit/generateJson')
  async editGenerateJson(
    @Body() editResponseActionDto: EditResponseActionDto,
    @Res() response: Response,
  ) {
    await this.sessionService.editGenerateJson(editResponseActionDto, response);
  }

  @Post('/chat/response/edit/customField')
  async editCustomField(
    @Body() editResponseActionDto: EditResponseActionDto,
    @Res() response: Response,
  ) {
    await this.sessionService.editCustomField(editResponseActionDto, response);
  }

  @Post('/chat/response/edit/message')
  async editMessage(
    @Body() editResponseActionDto: EditResponseActionDto,
    @Res() response: Response,
  ) {
    await this.sessionService.editMessage(editResponseActionDto, response);
  }

  @Post('/chat/response/add/googleSheet')
  async addGoogleSheet(
    @Body() editResponseActionDto: EditResponseActionDto,
    @Res() response: Response,
  ) {
    await this.sessionService.addGoogleSheet(editResponseActionDto, response);
  }

  @Post('/chat/response/add/website')
  async addWebsite(
    @Body() editResponseActionDto: EditResponseActionDto,
    @Res() response: Response,
  ) {
    await this.sessionService.addWebsite(editResponseActionDto, response);
  }

  @Post('/chat/response/add/googleCalendar')
  async addGoogleCalendar(
    @Body() editResponseActionDto: EditResponseActionDto,
    @Res() response: Response,
  ) {
    await this.sessionService.addGoogleCalendar(
      editResponseActionDto,
      response,
    );
  }

  @Post('/chat/response/add/ghlCalendar')
  async addGhlCalendar(
    @Body() editResponseActionDto: EditResponseActionDto,
    @Res() response: Response,
  ) {
    await this.sessionService.addGhlCalendar(editResponseActionDto, response);
  }

  @Post('/chat/response/add/ghl/tag')
  async addTag(
    @Body() editResponseActionDto: EditResponseActionDto,
    @Res() response: Response,
  ) {
    await this.sessionService.addGhlTag(editResponseActionDto, response);
  }

  @Post('/chat/response/add/generateJson')
  async addGenerateJson(
    @Body() editResponseActionDto: EditResponseActionDto,
    @Res() response: Response,
  ) {
    await this.sessionService.addGenerateJson(editResponseActionDto, response);
  }

  @Post('/chat/response/add/customField')
  async addCustomField(
    @Body() editResponseActionDto: EditResponseActionDto,
    @Res() response: Response,
  ) {
    await this.sessionService.addCustomField(editResponseActionDto, response);
  }

  @Post('/chat/response/add/googleDocs')
  async addGoogleDocs(
    @Body() editResponseActionDto: EditResponseActionDto,
    @Res() response: Response,
  ) {
    await this.sessionService.addGoogleDocs(editResponseActionDto, response);
  }

  @Post('/chat/response/add/httpGet')
  async addHttpGet(
    @Body() editResponseActionDto: EditResponseActionDto,
    @Res() response: Response,
  ) {
    await this.sessionService.addHttpGet(editResponseActionDto, response);
  }

  @Post('/chat/response/edit/httpGet')
  async editHttpGet(
    @Body() editResponseActionDto: EditResponseActionDto,
    @Res() response: Response,
  ) {
    await this.sessionService.editHttpGet(editResponseActionDto, response);
  }

  @Post('/chat/response/add/httpPost')
  async addHttpPost(
    @Body() editResponseActionDto: EditResponseActionDto,
    @Res() response: Response,
  ) {
    await this.sessionService.addHttpPost(editResponseActionDto, response);
  }

  @Post('/chat/response/edit/httpPost')
  async editHttpPost(
    @Body() editResponseActionDto: EditResponseActionDto,
    @Res() response: Response,
  ) {
    await this.sessionService.editHttpPost(editResponseActionDto, response);
  }

  @Post('/chat/response/add/slack')
  async addSlack(
    @Body() editResponseActionDto: EditResponseActionDto,
    @Res() response: Response,
  ) {
    await this.sessionService.addSlack(editResponseActionDto, response);
  }

  @Post('/chat/response/edit/slack')
  async editSlack(
    @Body() editResponseActionDto: EditResponseActionDto,
    @Res() response: Response,
  ) {
    await this.sessionService.editSlack(editResponseActionDto, response);
  }

  @Post('/chat/response/add/sms')
  async addSMS(
    @Body() editResponseActionDto: EditResponseActionDto,
    @Res() response: Response,
  ) {
    await this.sessionService.addSms(editResponseActionDto, response);
  }

  @Post('/chat/response/edit/sms')
  async editSMS(
    @Body() editResponseActionDto: EditResponseActionDto,
    @Res() response: Response,
  ) {
    await this.sessionService.editSms(editResponseActionDto, response);
  }

  @Post('/chat/response/add/email')
  async addEmail(
    @Body() editResponseActionDto: EditResponseActionDto,
    @Res() response: Response,
  ) {
    await this.sessionService.addEmail(editResponseActionDto, response);
  }

  @Post('/chat/response/edit/email')
  async editEmail(
    @Body() editResponseActionDto: EditResponseActionDto,
    @Res() response: Response,
  ) {
    await this.sessionService.editEmail(editResponseActionDto, response);
  }

  @Post('/chat/response/delete/dataSource')
  async deleteDataSource(
    @Body() deleteResponseActionDto: EditResponseActionDto,
    @Res() response: Response,
  ) {
    await this.sessionService.deleteDataSource(
      deleteResponseActionDto,
      response,
    );
  }

  @Post('/chat/response/delete/message')
  async deleteMessage(
    @Body() deleteResponseActionDto: EditResponseActionDto,
    @Res() response: Response,
  ) {
    const updatedSession = await this.sessionService.deleteMessage(
      deleteResponseActionDto,
      response,
    );
    return response.status(200).json({
      message: 'Message deleted successfully!',
    });
  }

  @Post('/chat/response/restore/dataSource')
  async restoreDataSource(
    @Body() deleteResponseActionDto: EditResponseActionDto,
    @Res() response: Response,
  ) {
    await this.sessionService.restoreDataSource(
      deleteResponseActionDto,
      response,
    );
  }

  @Get('/agent/name/:sessionId')
  async getAgentName(
    @Param('sessionId') sessionId: string,
    @Res() response: Response,
  ) {
    try {
      const data = await this.sessionService.getSessionAgentName(sessionId);
      return response.status(200).json({
        message: 'Agent name fetched successfully',
        data,
      });
    } catch (error) {
      return response.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        message: error.message,
      });
    }
  }

  @Put('/:sessionId/name')
  async updateName(
    @Param('sessionId') sessionId: string,
    @Res() response: Response,
    @Body() updateSessionDto: UpdateSessionName,
  ) {
    try {
      const sessionDetails = await this.sessionService.updateName(
        sessionId,
        response,
        updateSessionDto,
      );
    } catch (error) {
    }
  }

  @Delete('/:sessionId')
  async deleteSession(
    @Param('sessionId') sessionId: string,
    @Res() response: Response,
  ) {
    await this.sessionService.deleteSession(sessionId, response);
  }

  @Post('/test/httpPost')
  async testHttpPost(
    @Body() testData: any,
    @Res() response: Response,
  ) {
    try {
      const result = await this.sessionService.testHttpPostRequest(testData);
      response.status(200).json({
        success: true,
        message: 'POST request test completed',
        data: result
      });
    } catch (error) {
      response.status(500).json({
        success: false,
        message: 'POST request test failed',
        error: error.message
      });
    }
  }
  //controller ends here
}
