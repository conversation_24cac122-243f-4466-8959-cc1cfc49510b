// outreach.service.ts
import { Injectable } from '@nestjs/common';
import { OpenaiService } from 'src/ai_response/openai';

@Injectable()
export class OutreachService {
  constructor(
    private readonly openai: OpenaiService
  ){}
  async outreach(eventId, credid, type, agentDetails, 
    outreachPrompt: string,
    conversationHistoryString: string,
    modelObj: any,
    aiProviderSettings: any,
    executionId: string,
    training?: string
  ) {
    let prompt = 'You are a sales assistant, responsible for engaging potential prospects in conversation that will ultimately lead to the prospect responding positively to your outreach efforts.\n';
    prompt += 'The contact you are reaching out to has shown interest but has stopped responding so far. Engage the contact again.\n';
    prompt += '\nExamples of common outreach messages:\n';
    prompt += 'Sales Assistant: Hey, you still there?\n';
    
    // Variables documentation:
    // We use a placeholder format [VARIABLE_NAME] to indicate personalization points
    // in the sample messages. These should be replaced with actual recipient data
    // before sending the message.
    // Common variables:
    // - [FIRST_NAME]: The recipient's first name
    // - [COMPANY]: The recipient's company name
    // - [PRODUCT]: Your product/service name relevant to the conversation
    
    prompt += 'Sales Assistant: Hey [FIRST_NAME], just wanted to check in!\n';
    prompt += 'Sales Assistant: Hey [FIRST_NAME], just wanted to check in and see if you had any questions!\n';
    prompt += 'Sales Assistant: Just checking in here, [FIRST_NAME]!\n\n';
    prompt += outreachPrompt + '\n';
    if(training !== undefined){
      prompt += training + '\n';
    }
    prompt += '\nWrite your message as the assistant only. Do not continue the conversation after your message. You should never repeat the same message verbatim. Keep your messages short and to the point, and try to keep the conversation going.';

    try {

      let companyId = modelObj.companyId;
    
      const message = [
        {
          role: 'system',
          content: prompt,
        },
        {
          role: 'user',
          content: conversationHistoryString,
        },
      ];

      
     let outreachMessage = await this.openai.generateBotResponse(eventId, credid, type, agentDetails,
        message,
        150,
        modelObj,
        aiProviderSettings,
      );

      
      return {prompt , outreachMessage };
    }
    catch (error) {
      
    }
  }
}
