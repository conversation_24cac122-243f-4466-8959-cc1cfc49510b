import { Injectable } from '@nestjs/common';
import { AgentDocument } from 'src/mongo/schemas/agents/agents.schema';
import { Session } from 'src/mongo/schemas/session/session.schema';
import { UserDocument } from 'src/mongo/schemas/user/user.schema';
import { MongoAgentService } from 'src/mongo/service/agent/mongo-agent.service';
import { MongoCredentialsService } from 'src/mongo/service/credentials/mongo-credentials.service';
import { MongoOrganizationService } from 'src/mongo/service/organization/mongo-organization.service';
import { MongoSessionService } from 'src/mongo/service/session/mongo-session.service';
import { MongoUserService } from 'src/mongo/service/user/mongo-user.service';

// type BasicInfo = typeof Session.prototype.basic;

// type EnrichedBasicInfo = BasicInfo & {
//   userName?: string;
//   agentName?: string;
// };

export class EnrichedSession extends Session {
  basic: Session['basic'] & {
    userName?: string;
    agentName?: string;
  };
}

@Injectable()
export class SessionListService {
  constructor(
    private readonly mongoSessionService: MongoSessionService,
    private readonly mongoUserService: MongoUserService,
    private readonly mongoOrganizationService: MongoOrganizationService,
    private readonly mongoAgentService: MongoAgentService,
    private readonly mongoCredentialService: MongoCredentialsService,
  ) {}
  async fetchSessions(payload: {
    offset: number;
    limit: number;
    createdBy: 'createdByMe' | 'createdByOthers' | 'createdByAnyone';
    sortBy: 'lastUsed' | 'lastCreated';
    agent: string; //this will be 'all' or an agentID
    orgId: string;
    saved: 'true' | 'false' | 'all';
    userId?: string;
  }) {
    const { offset, limit, createdBy, sortBy, agent, orgId, saved, userId } =
      payload;
    const filter = { 'basic.orgId': orgId };
    if (agent !== 'all') {
      filter['basic.agentId'] = agent;
    }
    switch (saved) {
      case 'true':
        filter['saved'] = true;
        break;
      case 'false':
        filter['saved'] = false;
        break;
    }
    if (userId) {
      switch (createdBy) {
        case 'createdByMe':
          filter['basic.userId'] = userId;
          break;
        case 'createdByOthers':
          filter['basic.userId'] = { $ne: userId };
          break;
        default:
        // no additional filter needed for 'createdByAnyone'
      }
    }
    const sort = {};
    switch (sortBy) {
      case 'lastUsed':
        sort['dates.lastUpdated'] = -1;
        break;
      case 'lastCreated':
        sort['dates.createdAt'] = -1;
        break;
    }
    const list = await this.mongoSessionService.findSessions({
      filter,
      limit,
      offset,
      sort,
      orgId,
    });

    // Use Sets to gather unique userIds and agentIds
    const userIdSet = new Set<string>();
    const agentIdSet = new Set<string>();

    for (const session of list) {
      userIdSet.add(session.basic.userId);
      agentIdSet.add(session.basic.agentId);
    }

    // Convert Sets to arrays and fetch the users and agents in bulk
    const userIds = Array.from(userIdSet).filter(
      (id) => id && id.length === 24,
    ); // Only valid ObjectId strings
    const agentIds = Array.from(agentIdSet).filter(
      (id) => id && id.length === 24,
    );

    const users: UserDocument[] = await this.mongoUserService.getUsers({
      _id: { $in: userIds },
    });
    const agents: AgentDocument[] = await this.mongoAgentService.getAgents({
      _id: { $in: agentIds },
    });

    const userLookup = {};
    for (const user of users) {
      userLookup[user._id.toString()] = user;
    }

    const agentLookup = {};
    for (const agent of agents) {
      agentLookup[agent._id.toString()] = agent;
    }

    const resList: EnrichedSession[] = [];

    for (const session of list) {
      const enrichedSession = session as EnrichedSession;

      if (agentLookup[session.basic.agentId]) {
        enrichedSession.basic.agentName =
          agentLookup[session.basic.agentId].agentName;
      }

      if (userLookup[session.basic.userId]) {
        enrichedSession.basic.userName = userLookup[session.basic.userId].name;
      }

      resList.push(enrichedSession);
    }

    return resList;
  }
}
