import { PROVIDERS } from "src/lib/constant";
import { PineconeService } from "src/vector/services/pinecone/pinecone.service";

export async function getActionSamples(agentId, actionId, embedding, orgid, type, filterId, activity){
    const pineconeService = new PineconeService();
    await pineconeService.onModuleInit();
    const index = await pineconeService.connectIndex(process.env.PINECONE_INDEX);
    try {
      if(type == PROVIDERS.GOOGLE_SHEET){ 
        
        const query = {
          namespace: orgid,
          //id: actionId,
          vector: embedding,
          topK: 10,
          includeMetadata: true,
          filter: {
            accountId: {$eq: filterId},
            type: {$eq: type}, 
            activity: activity
          }
        };
  
        const queryResponse = await pineconeService.query( 
          index,  
          query
        );
  
        return queryResponse; 
        
      }else if(type == PROVIDERS.WEBSITE){
        
        const query = {
          namespace: orgid,
          //id: actionId,
          vector: embedding,
          topK: 10,
          includeMetadata: true,
          filter: {
            accountId: { $eq: filterId }, 
            type: { $eq: type },
            activity: activity
          }
        };
  
        const queryResponse = await pineconeService.query( 
          index, 
          query
        );
  
        return queryResponse;
        
      }else if(type == PROVIDERS.GHL_CALENDAR){

        const query = {
          namespace: orgid,
          //id: actionId,
          vector: embedding,
          topK: 10,
          includeMetadata: true,
          filter: {
            type: { $eq: type },
            accountId: filterId,
            activity: activity
          }
        };
   
        const queryResponse = await pineconeService.query( 
          index, 
          query
        );

        return queryResponse;

      }
    } catch (error) {
      
    }
  }

export async function tokenOptimize(sessionId, agentId, orgid, agentData, conversationHistory, embedding){
    let systemPromptForYesNo = 'You are an intelligent Ai assistant responsible for deciding when an action is applicable to a conversation, based on the description of the action and the messages within the conversation.';
    systemPromptForYesNo += '\n\n' + 'Below are the actions that should be evaluated:';

    for(const [index, action] of agentData.actions.entries()){

        if(action.activity === 'read' || action.activity === 'tag' || action.activity === "customField"){
            let samplesPrompt_static:string;
            let actionNumber = index + 1;
            samplesPrompt_static = `\n\nAction ID: ${action.actionId}`;
            samplesPrompt_static += '\nDescription\n' + action.promptContent;
            systemPromptForYesNo += samplesPrompt_static;
            let yesSamples = '\n\nBelow are a list of previous conversations where this action was appropriately applied:';
            let noSamples = '\n\nBelow are a list of previous conversations where this action is not relevant and should not be applied:'
            let yesBool: boolean = false;
            let noBool: boolean = false;
            let messages_yes: any[] = [];
            let messages_no: any[] = [];
            const filterId = action.accountId;
            const actionIndex = await getActionSamples(agentId, action.actionId, embedding, orgid, action.providerName, filterId, action.activity);
            if(actionIndex){
              if(actionIndex.matches.length > 0){
                // systemPromptForYesNo += "\nHere's some additional information from the knowledge source: ";
                for (const [index, content] of actionIndex.matches.entries()) { 
                  if(content.metadata['content'] != undefined && content.metadata['result'] == 'yes'){
                    let actionSamples = content.metadata['content'];
                    if(typeof actionSamples == "string"){
                      actionSamples = actionSamples.replace(/\n/g, '');
                      actionSamples = actionSamples.replace(/,(?=(.*user:))/g, '');
                      actionSamples = actionSamples.replace(/(user:)/g, '\n$1');
                      actionSamples = actionSamples.replace(/(assistant:)/g, '\n$1');
                    }
                    const promptContent = `Here is the conversation so far:\n\n${actionSamples}`;
                    // systemPromptForYesNo += content.metadata['content'];
                    messages_yes.push(
                      {
                        role: "user",
                        content: promptContent
                      },
                      {
                        role: "assistant",
                        content: content.metadata['result']
                      },
                    );
                    yesBool = true;
                  }if(content.metadata['content'] != undefined && content.metadata['result'] == 'no'){
                    let actionSamples = content.metadata['content'];
                    if(typeof actionSamples == "string"){
                      actionSamples = actionSamples.replace(/\n/g, '');
                      actionSamples = actionSamples.replace(/,(?=(.*user:))/g, '');
                      actionSamples = actionSamples.replace(/(user:)/g, '\n$1');
                      actionSamples = actionSamples.replace(/(assistant:)/g, '\n$1');
                    }
                    const promptContent = `Here is the conversation so far:\n\n${actionSamples}`;
                    // systemPromptForYesNo += content.metadata['content'];
                    messages_no.push(
                      {
                        role: "user",
                        content: promptContent
                      },
                      {
                        role: "assistant",
                        content: content.metadata['result']
                      },
                    );
                    noBool = true;
                  }
                }
              }else{
                
              }
            }else{
              
            }

            let samplesPrompt:string;

            if(yesBool == true){
                yesSamples += '\n\n' + messages_yes.join('\n');
                samplesPrompt += '\n\n' + yesSamples;
                systemPromptForYesNo += '\n\n' + samplesPrompt;
            }
            if(noBool == true){
                noSamples += '\n\n' + messages_no.join('\n');
                samplesPrompt += '\n\n' + noSamples;
                systemPromptForYesNo += '\n\n' + samplesPrompt;
            }
        }

    } 

    systemPromptForYesNo += '\n\n' + "You must respond with a JSON object with a parameter called 'actions' and the value must be an array with each action's ID even if there is only one action ID that should be committed during this conversation turn. Use the following formatted JSON object for reference:";
    systemPromptForYesNo += '```\n\
    {\n\
        "actions": ["action_id_1"]\n\
    }```\n';
    systemPromptForYesNo += '\n\ or \n';
    systemPromptForYesNo += '```\n\
    {\n\
        "actions": ["action_id_1",\n\
        "action_id_2"]\n\
    }```\n';
    systemPromptForYesNo += 'If no actions should be performed during this conversation turn, simply generate the following JSON:';
    systemPromptForYesNo += '```\n\
    {\n\
        "actions": "none"\n\
    }```\n';

    return systemPromptForYesNo;
}