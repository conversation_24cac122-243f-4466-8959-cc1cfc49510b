import { Action, IBotActionResponse, IJsonResponseStructure, IRolloverDetails } from "src/interfaces/interfaces";
import { CUSTOM_LLM, KINDS, PROVIDERS } from "src/lib/constant";
import { v4 as uuidv4 } from 'uuid';

export async function sheetLangChain(action, embeddingProvider, apiKey, query, organizationDetails, agentId, orgid, systemPrompt, sessionId){
    const botResponseForEachDataSource : IBotActionResponse[] = [];
    const accountId = action.accountId;

    let sheetId: string, sheetReference: string;

    let sheetName = action.accountName;

    const sheetEmbeddings = await this.vectorService.createEmbedding(
        embeddingProvider,
        apiKey,
      query,
    );

    const sheetEmbedding = sheetEmbeddings[0].embedding;

    for (const dataSource of organizationDetails.connections
        .dataSources) {
        if (dataSource.accountId == accountId) {
        sheetId = dataSource.accountId;
        sheetReference = dataSource.reference;
        }
    }

    let sheetPrompt =
        '\n\nYou have been provided a data source with the following description:\n\n';
    sheetPrompt += action.promptContent;
    const sessionIndexSheet = await this.getSessionSamples(
        agentId,
        orgid,
        sheetEmbedding,
        PROVIDERS.GOOGLE_SHEET,
        sheetId,
    );

    if (sessionIndexSheet) {
        if (sessionIndexSheet.matches.length > 0) {
        let totalSemanticSheetContent: string = '';
        sheetPrompt +='\n\n' + 'Use the following data from this data source as context to continue the conversation:\n\n';
        systemPrompt += sheetPrompt;
        for (const content of sessionIndexSheet.matches) {
            // 
            if (content.metadata['content'] !== undefined) {
            let nestedSheetPrompt = content.metadata['content'];
            if (typeof nestedSheetPrompt == 'string') {
                nestedSheetPrompt = nestedSheetPrompt.replace(
                /\n{3,}(?=(user:|Contact:))/g,
                '\n\n',
                );
                totalSemanticSheetContent += nestedSheetPrompt;
                const checkSheetLength = await this.numberOfTokens(
                totalSemanticSheetContent,
                );
                if (
                action.advancedSettings &&
                action.advancedSettings.hasOwnProperty(
                    'maxTokensAllowed',
                )
                ) {
                if (
                    checkSheetLength.length <
                    action.advancedSettings.maxTokensAllowed
                ) {
                    systemPrompt += nestedSheetPrompt;
                    systemPrompt += '\n\n';
                }
                } else {
                if (checkSheetLength.length < 450) {
                    systemPrompt += nestedSheetPrompt;
                    systemPrompt += '\n\n';
                }
                }
            }
            }
        }
        } else {
        await this.logMe("Zero matches for sheet samples in pinecone for ", action.providerName, sessionId);
        }
    } else {
        
    }

    // fill in a new botResponse
    const newBotResponse: IBotActionResponse = {
        eventId: uuidv4(),
        sender: 'bot',
        kind: PROVIDERS.GOOGLE_SHEET,
        accountId: sheetId,
        accountName: sheetName,
        deleted: false,
        action: 'read',
        eventData: {},
        reference: sheetReference,
        timestamp: Math.floor(Date.now() / 1000),
    };

    // push the new bot response to the botResponse array
    botResponseForEachDataSource.push(newBotResponse);
}

export async function siteLangChain(action, apiKey, query, organizationDetails, agentId, orgid, systemPrompt, sessionId){
    const botResponseForEachDataSource : IBotActionResponse[] = [];
    const accountId = action.accountId;

    // 
    await this.logMe(
        'Yes for reading: ',
        action.providerName,
        sessionId,
    );
    let sitePrompt =
        '\n\nYou have been provided a data source with the following description:';
    sitePrompt += '\n\n' + action.promptContent;

    const siteEmbeddings = await this.vectorService.createEmbedding(
        apiKey,
        query,
    );

    const siteEmbedding = siteEmbeddings[0].embedding;

    let websiteName: string,
        websiteAccountId: string,
        websiteReference: string;

    for (const name of organizationDetails.connections.staticData) {
        if (name.accountId === accountId) {
        websiteAccountId = name.accountId;
        websiteReference = name.reference;
        websiteName = name.name;
        }
    }

    const sessionIndexWebsite = await this.getSessionSamples(
        agentId,
        orgid,
        siteEmbedding,
        PROVIDERS.WEBSITE,
        websiteAccountId,
    );

    if (sessionIndexWebsite) {
        if (sessionIndexWebsite.matches.length > 0) {
        sitePrompt +=
            '\n\n' +
            'Use the following data from this data source as context to continue the conversation:';
        systemPrompt += sitePrompt;
        let totalSemanticSiteContent: string = '';

        for (const content of sessionIndexWebsite.matches) {
            if (content.metadata['content'] != undefined) {
            let nestedSitePrompt =
                '\n\n' + content.metadata['content'];
            if (typeof nestedSitePrompt == 'string') {
                nestedSitePrompt = nestedSitePrompt.replace(
                /\n{3,}(?=(user:|Contact:))/g,
                '\n\n',
                );

                totalSemanticSiteContent += nestedSitePrompt;
                // sitePrompt += '\n\n' + content.metadata['content'];
                const systemPromptLength = await this.numberOfTokens(
                totalSemanticSiteContent,
                );
                // 
                if (
                action.advancedSettings &&
                action.advancedSettings.hasOwnProperty(
                    'maxTokensAllowed',
                )
                ) {
                if (
                    systemPromptLength.length <
                    action.advancedSettings.maxTokensAllowed
                ) {
                    systemPrompt += nestedSitePrompt;
                    const metaData = content.metadata['content'];
                    const contentLines = metaData.split('\n');
                    const userMessage = contentLines.find((line) =>
                    line.startsWith('user:'),
                    );
                    const assistantMessage = contentLines.find(
                    (line) => line.startsWith('assistant:'),
                    );
                    const userText = userMessage
                    ? userMessage.replace('user:', '').trim()
                    : '';
                    const assistantText = assistantMessage
                    ? assistantMessage
                        .replace('assistant:', '')
                        .trim()
                    : '';
                }
                } else {
                if (systemPromptLength.length < 450) {
                    // 
                    systemPrompt += nestedSitePrompt;
                    // 
                    // sitePrompt += '\n\n' + content.metadata['content']; //content.metadata['content'] = " user: user1\nassistant:assistant1 "
                    // 
                    const metaData = content.metadata['content'];

                    const contentLines = metaData.split('\n');

                    const userMessage = contentLines.find((line) =>
                    line.startsWith('user:'),
                    );

                    const assistantMessage = contentLines.find(
                    (line) => line.startsWith('assistant:'),
                    );

                    const userText = userMessage
                    ? userMessage.replace('user:', '').trim()
                    : '';

                    const assistantText = assistantMessage
                    ? assistantMessage
                        .replace('assistant:', '')
                        .trim()
                    : '';
                }
                }
            }
            }
        }
        } else {
        await this.logMe("Zero matches in pinecone for: ", action.providerName, sessionId);
        }
    } else {
        
    }

    const newBotResponse: IBotActionResponse = {
        eventId: uuidv4(),
        sender: 'bot',
        kind: PROVIDERS.WEBSITE,
        accountName: websiteName,
        accountId: accountId,
        deleted: false,
        action: 'read',
        eventData: {},
        reference: websiteReference,
        timestamp: Math.floor(Date.now() / 1000),
    };
    botResponseForEachDataSource.push(newBotResponse);


}

export async function ghlCalendarChain(action, query, organizationDetails, agentId, orgid, systemPrompt, sessionId, contactDetails, conversationHistoryString, agentData, model_Obj, aiProvAdvSett){ 
    const maxTokens = 300;
    const yesNoToken = 7;
    let usage_yesNoToken = 0;
    let rolloverDetails: IRolloverDetails = {
      rollover: false,
      rolloverReason: '',
      rolloverDate: ''
    }
    let criticalErrors: string[] = [];
    let action_aiHistory: Action[] = [];

    let secretKey = model_Obj.key;
    const botResponseForEachDataSource : IBotActionResponse[] = [];
    const accountId = action.accountId; 
    await this.logMe(
        'Yes for reading: ',
        action.providerName,
        sessionId,
    );
    let timezone, calendarId, credentialId, locationId, ghlReference;
    for(const dataSource of organizationDetails.connections.dataSources){

        if( dataSource.accountId === accountId ){
        if(dataSource.useContactTz && dataSource.useContactTz == true && contactDetails && contactDetails.contactTimezone){
            timezone = contactDetails.contactTimezone;
            await this.logMe('read timezone: ', timezone,sessionId)
        }else{
            timezone = dataSource.timezone;
            await this.logMe('read timezone: ', timezone,sessionId)
        }
        calendarId = dataSource.calendarId;
        credentialId = dataSource.credentialId;
        locationId = dataSource.keyId;
        ghlReference = dataSource.reference;
        }
    }

    if (calendarId === undefined) {
        
    } else if (timezone === undefined) {
        
    } else if (credentialId === undefined) {
        
    }

    const credentialData =
        await this.mongoCredentialService.getCredential({
        _id: credentialId,
        kind: KINDS.GHL_CREDENTIAL,
        });

    if (!credentialData) {
        
    }

    const ghlAccessToken = credentialData['creds'].accessToken;
    const ghlRefreshToken = credentialData['creds'].refreshToken;

    const tokens = {
        access_token: ghlAccessToken,
        refresh_token: ghlRefreshToken,
    };

    // initialize all the variables
    var date = new Date();
    var epochDate = date.getTime();
    var tomorrowEpoch = epochDate + 86400000;
    var in72Hours = epochDate + 259200000;
    var long72Hours = new Date(in72Hours).toLocaleDateString(
        'en-US',
        {
        weekday: 'long',
        month: 'long',
        day: 'numeric',
        year: 'numeric',
        timeZone: timezone,
        },
    );
    var in72DayOfWeek = new Date(in72Hours).toLocaleDateString(
        'en-US',
        { weekday: 'long', timeZone: timezone },
    );
    var in48Hours = epochDate + 172800000;
    var in96Hours = epochDate + 345600000;
    var in96Date = new Date(in96Hours);
    var in96DayOfWeek = in96Date.toLocaleDateString('en-US', {
        weekday: 'long',
        timeZone: timezone,
    });
    var in96LongDate = new Date(in96Hours).toLocaleDateString(
        'en-US',
        {
        weekday: 'long',
        month: 'long',
        day: 'numeric',
        timeZone: timezone,
        },
    );
    var longDate = date.toLocaleDateString('en-US', {
        weekday: 'long',
        month: 'long',
        day: 'numeric',
        year: 'numeric',
        timeZone: timezone,
    });
    var longTomorrow = new Date(tomorrowEpoch).toLocaleDateString(
        'en-US',
        {
        weekday: 'long',
        month: 'long',
        day: 'numeric',
        year: 'numeric',
        timeZone: timezone,
        },
    );
    var long48Hours = new Date(in48Hours).toLocaleDateString(
        'en-US',
        {
        weekday: 'long',
        month: 'long',
        day: 'numeric',
        year: 'numeric',
        timeZone: timezone,
        },
    );
    var in48HoursDayOfWeek = new Date(in48Hours).toLocaleDateString(
        'en-US',
        { weekday: 'long', timeZone: timezone },
    );
    var slotPrompts = [];
    //get the end of this week and beginning of next week
    var numericDay = date.getDay();
    var endOfWeekDiff = 6 - numericDay;
    if (endOfWeekDiff == 0) {
        endOfWeekEpoch = in48Hours;
        var endOfWeek = 'today';
        var endOfWeekDayofWeek = 'today';
        var nextWeekEpoch = tomorrowEpoch;
    } else {
        var endOfWeekEpoch = epochDate + endOfWeekDiff * 86400000;
        var endOfWeek = new Date(endOfWeekEpoch).toLocaleDateString(
        'en-US',
        {
            weekday: 'long',
            month: 'long',
            day: 'numeric',
            year: 'numeric',
            timeZone: timezone,
        },
        );
        var endOfWeekDayofWeek = new Date(
        endOfWeekEpoch,
        ).toLocaleDateString('en-US', {
        weekday: 'long',
        timeZone: timezone,
        });
        var nextWeekEpoch = endOfWeekEpoch + 86400000;
    }

    var nextWeek = new Date(nextWeekEpoch).toLocaleDateString(
        'en-US',
        {
        weekday: 'long',
        month: 'long',
        day: 'numeric',
        year: 'numeric',
        timeZone: timezone,
        },
    );
    var endOfNextWeekEpoch = nextWeekEpoch + 86400000 * 6;
    var endOfNextWeek = new Date(
        endOfNextWeekEpoch,
    ).toLocaleDateString('en-US', {
        weekday: 'long',
        month: 'long',
        day: 'numeric',
        year: 'numeric',
        timeZone: timezone,
    });
    var tenDaysFromNow = epochDate + 86400000 * 10;

    // update the system prompt for GHL Calendar
    var systemPromptTraining =
        '\n\nYou are a smart and intelligent Named Entity Recognition (NER) system. You are an expert at identifying Natural Language entities that can be extracted from a conversation happening between a contact and an assistant. The specific entities you excel the most at identifying are those that relate to the availability window of the contact. When the contact describes time windows that they are available, or the time window in which they are interested in booking an appointment, you are able to generate a JSON object that describes the start and end date of the time window described, relative to the current date and time. The start and date are always described as epoch times, rounded to milliseconds (not seconds). Here is the structure you should follow for your output:\n\n';
    systemPromptTraining +=
        '```\n\
    {\n\
        "start" : epoch (integer),\n\
        "end" : epoch (integer)\n\
    }```';
    systemPromptTraining +=
        '\nWhen provided with a conversation, you should be able to generate a JSON object that describes the start and end date of the time window described, relative to the current date and time.';
    var commonSystemPromptTraining =
        '\nThe current date today is ' +
        longDate +
        ', represented in epoch as ' +
        epochDate +
        '.';
    commonSystemPromptTraining +=
        '\nThe date tomorrow is ' +
        longTomorrow +
        ', represented in epoch as ' +
        tomorrowEpoch +
        '.';
    commonSystemPromptTraining +=
        '\nThe date for ' +
        in48HoursDayOfWeek +
        ' is ' +
        long48Hours +
        ', represented in epoch as ' +
        in48Hours +
        '.';
    commonSystemPromptTraining +=
        '\nThe date for ' +
        in72DayOfWeek +
        ' is ' +
        long72Hours +
        ', represented in epoch as ' +
        in72Hours +
        '.';
    commonSystemPromptTraining +=
        '\nThe date for ' +
        in96DayOfWeek +
        ' is ' +
        in96LongDate +
        ', represented in epoch as ' +
        in96Hours +
        '.';
    commonSystemPromptTraining +=
        '\nThe end of the current week (this week) is ' +
        endOfWeek +
        ', represented in epoch as ' +
        endOfWeekEpoch +
        '.';
    commonSystemPromptTraining +=
        '\nThe beginning of next week is ' +
        nextWeek +
        ', represented in epoch as ' +
        nextWeekEpoch +
        '.';
    commonSystemPromptTraining +=
        '\nThe end of next week is ' +
        endOfNextWeek +
        ', represented in epoch as ' +
        endOfNextWeekEpoch +
        '.\n\n';

    systemPromptTraining += commonSystemPromptTraining;

    var exampleUserString1 = commonSystemPromptTraining;
    exampleUserString1 += 'Here is the conversation so far:\n\n';
    exampleUserString1 +=
        'assistant: Hey there! Can we go ahead and get you scheduled?\n';
    exampleUserString1 += 'user: Sure\n';
    exampleUserString1 +=
        'assistant: Perfect, lets go ahead and get you scheduled for our first meeting. What day works best for you?\n';
    exampleUserString1 +=
        'user: Any day this week after 3 is fine\n';
    exampleUserString1 +=
        'Generate the JSON object appropriate for the conversation above.';
    var completionExample1 =
        'The JSON object for this conversation should be:\n\n';
    completionExample1 +=
        '```\n\
    {\n\
        "start" : ' +
        epochDate +
        ',\n\
        "end" : ' +
        in96Hours +
        '\n\
    }```\n';

    var exampleUserString2 = commonSystemPromptTraining;
    exampleUserString2 += 'Here is the conversation so far:\n\n';
    exampleUserString2 +=
        'assistant: What day would work best for you?\n';
    exampleUserString2 += 'user: Im free tomorrow\n';
    exampleUserString2 +=
        'Generate the JSON object appropriate for the conversation above.';
    var completionExample2 =
        'The JSON object for this conversation should be:\n\n';
    completionExample2 +=
        '```\n\
    {\n\
        "start" : ' +
        epochDate +
        ',\n\
        "end" : ' +
        in48Hours +
        '\n\
    }```\n';

    var exampleUserString3 = commonSystemPromptTraining;
    exampleUserString3 += 'Here is the conversation so far:\n\n';
    exampleUserString3 +=
        'assistant: We can go over everything together at our first meeting!\n';
    exampleUserString3 += 'user: Okay sounds good\n';
    exampleUserString3 +=
        'assistant: Perfect, let me go ahead and get you on the schedule. Did you have a day in mind that works for you?\n';
    exampleUserString3 +=
        'user: Im free on ' + in96DayOfWeek + '\n';
    exampleUserString3 +=
        'Generate the JSON object appropriate for the conversation above.';
    //add 36 hours from beginning of in96Hours
    var endTime3 = in96Hours + 129600000;
    var completionExample3 =
        'The JSON object for this conversation should be:\n\n';
    completionExample3 +=
        '```\n\
    {\n\
        "start" : ' +
        in96Hours +
        ',\n\
        "end" : ' +
        endTime3 +
        '\n\
    }```\n';

    var exampleUserString4 = commonSystemPromptTraining;
    exampleUserString4 += 'Here is the conversation so far:\n\n';
    exampleUserString4 +=
        'assistant: We can go over everything together at our first meeting!\n';
    exampleUserString4 += 'user: Okay sounds good\n';
    exampleUserString4 +=
        'assistant: Perfect, let me go ahead and get you on the schedule. Did you have a day in mind that works for you?\n';
    exampleUserString4 +=
        'user: Im free on ' + in96DayOfWeek + '\n';
    exampleUserString4 += `assistant: Great, we have a few times available on ${in96LongDate}, what time would you prefer?\n`;
    exampleUserString4 +=
        'user: Actually sorry, what about tomorrow?\n';
    exampleUserString4 +=
        'Generate the JSON object appropriate for the conversation above.';
    var completionExample4 =
        'The JSON object for this conversation should be:\n\n';
    completionExample4 +=
        '```\n\
    {\n\
        "start" : ' +
        epochDate +
        ',\n\
        "end" : ' +
        in72Hours +
        '\n\
    }```\n';

    var exampleUserString5 = commonSystemPromptTraining;
    exampleUserString5 += 'Here is the conversation so far:\n\n';
    exampleUserString5 +=
        'assistant: What day works best for you?\n';
    exampleUserString5 += 'user: Next week?\n';
    exampleUserString5 +=
        'Generate the JSON object appropriate for the conversation above.';
    var completionExample5 =
        'The JSON object for this conversation should be:\n\n';
    completionExample5 +=
        '```\n\
    {\n\
        "start" : ' +
        nextWeekEpoch +
        ',\n\
        "end" : ' +
        endOfNextWeekEpoch +
        '\n\
    }```\n';

    var exampleUserString6 = commonSystemPromptTraining;
    exampleUserString6 += 'Here is the conversation so far:\n\n';
    exampleUserString6 +=
        'assistant: Hey there! Can we go ahead and get you scheduled?\n';
    exampleUserString6 += 'user: Sure\n';
    exampleUserString6 +=
        'assistant: Great! When would you like me to schedule it for?\n';
    exampleUserString6 +=
        'user: As soon as possible! What is the next availability you have?\n';
    exampleUserString6 +=
        'Generate the JSON object appropriate for the conversation above.';
    var completionExample6 =
        'The JSON object for this conversation should be:\n\n';
    completionExample6 +=
        '```\n\
    {\n\
        "start" : ' +
        epochDate +
        ',\n\
        "end" : ' +
        in96Hours +
        '\n\
    }```\n';

    var realConversationHistory = commonSystemPromptTraining;
    realConversationHistory +=
        'Here is the conversation so far:\n\n';
    realConversationHistory += conversationHistoryString;

    // this is the message
    let messagesGhlRead = [
        { role: 'system', content: systemPromptTraining },
    ];

    messagesGhlRead.push(
        {
        role: 'user',
        content: exampleUserString1,
        },
        {
        role: 'assistant',
        content: completionExample1,
        },
        {
        role: 'user',
        content: exampleUserString2,
        },
        {
        role: 'assistant',
        content: completionExample2,
        },
        {
        role: 'user',
        content: exampleUserString3,
        },
        {
        role: 'assistant',
        content: completionExample3,
        },
        {
        role: 'user',
        content: exampleUserString4,
        },
        {
        role: 'assistant',
        content: completionExample4,
        },
        {
        role: 'user',
        content: exampleUserString5,
        },
        {
        role: 'assistant',
        content: completionExample5,
        },
        {
        role: 'user',
        content: exampleUserString6,
        },
        {
        role: 'assistant',
        content: completionExample6,
        },
    );

    if (query && query !== '') {
       messagesGhlRead.push({ role: 'user', content: query });
    }
   

    let modelObj = {...model_Obj};
    let trainingResponse_response_response =
        await this.generateBotResponse(
        uuidv4(), credentialId, action, agentData,
        messagesGhlRead,
        maxTokens,
        modelObj,
        aiProvAdvSett,
        );
    const trainingResponse_response = trainingResponse_response_response.response;
    const actions = action_aiHistory.push(trainingResponse_response_response.message_aiHistory_structure);
    let trainingResponse = '';
    let ghlTrainingResponse_token_length = 0;
    let ghlTrainingResponse_input_tokens = 0;
    let companyId = modelObj.companyId;
    if (companyId === PROVIDERS.OPENAI_PROVIDER) {
      trainingResponse =
        trainingResponse_response.data.choices[0].message.content;
      usage_yesNoToken += trainingResponse_response.data.usage.total_tokens;
      ghlTrainingResponse_token_length +=
        trainingResponse_response.data.usage.completion_tokens;
      ghlTrainingResponse_input_tokens +=
        trainingResponse_response.data.usage.prompt_tokens;
    } else if (companyId === PROVIDERS.CLAUDE_PROVIDER) {
      trainingResponse = trainingResponse_response.content[0].text;
      usage_yesNoToken +=
        trainingResponse_response.usage.input_tokens +
        trainingResponse_response.usage.output_tokens;
      ghlTrainingResponse_token_length +=
        trainingResponse_response.usage.output_tokens;
      ghlTrainingResponse_input_tokens +=
        trainingResponse_response.usage.input_tokens;
    } else if (companyId === PROVIDERS.GROQ_PROVIDER) {
      trainingResponse = trainingResponse_response.choices[0].message.content;
      usage_yesNoToken += trainingResponse_response.usage.total_tokens;
      ghlTrainingResponse_token_length +=
        trainingResponse_response.usage.completion_tokens;
      ghlTrainingResponse_input_tokens +=
        trainingResponse_response.usage.prompt_tokens;
    } else if (companyId === PROVIDERS.FIREWORKS_PROVIDER) {
      trainingResponse = trainingResponse_response.choices[0].message.content;
      usage_yesNoToken += trainingResponse_response.usage.total_tokens;
      ghlTrainingResponse_token_length +=
        trainingResponse_response.usage.completion_tokens;
      ghlTrainingResponse_input_tokens +=
          trainingResponse_response.usage.prompt_tokens;
        
        const pricePerMillion = CUSTOM_LLM.COST_PER_MILLION;
        const usageInMillions = usage_yesNoToken / 1000000;
        const cost = usageInMillions * pricePerMillion;

        await this.mongoOrganizationService.updateHostedAiUsage(orgid, agentId, locationId, {
          numOfTokensUsed: usage_yesNoToken,
          cost: cost,
          provider: companyId,
          model: CUSTOM_LLM.PUBLIC_MODEL_NAME,
        });
    }
    messagesGhlRead.push({
        role: 'assistant',
        content: trainingResponse,
    });

    trainingResponse = trainingResponse.replace(
        /(\r\n|\n|\r)/gm,
        '',
    );
    let completionVars = trainingResponse.split('```');

    if (completionVars.length < 3) {
        
        var start = epochDate;
        var end = tenDaysFromNow;
    } else {
        try {
        var timeObject = JSON.parse(completionVars[1]);
        var start = parseInt(timeObject.start);
        var end = parseInt(timeObject.end);

        if (isNaN(start) || isNaN(end)) {
            
            var start = epochDate;
            var end = tenDaysFromNow;

            
        }
        } catch (err) {
        
        var start = epochDate;
        var end = tenDaysFromNow;

        
        }
    }

    var options = {
        method: 'GET',
        url: `https://services.leadconnectorhq.com/calendars/${calendarId}/free-slots?startDate=${start}&endDate=${end}&timezone=${timezone}`,
        headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + tokens.access_token,
        Version: '2021-04-15',
        },
    };

    try {
        // var responseOne = await axios(options);
        var daysList = await this.ghlApisService.makeGhlApiCall(
            locationId,
            options.method,
            options.url,
            undefined,
            { headers: options.headers },
            tokens,
        );
        // var daysList = response.data;

        var numberOfDays: number = Object.keys(daysList).length;

        await this.logMe(
            'Number of days: ',
            numberOfDays - 1,
            sessionId,
        );
        // 
        if (numberOfDays == undefined || numberOfDays == 1) {
            var start = epochDate;
            var end = endOfNextWeekEpoch;
            await this.logMe(
                `No times found in time range, defaulting to start: ${start} end: `,
                end,
                'SessionService',
            );
            // 

            options = {
                method: 'GET',
                url: `https://services.leadconnectorhq.com/calendars/${calendarId}/free-slots?startDate=${start}&endDate=${end}&timezone=${timezone}`,
                headers: {
                'Content-Type': 'application/json',
                Authorization: 'Bearer ' + tokens.access_token,
                Version: '2021-04-15',
                },
            };
            try {
                var daysList = await this.ghlApisService.makeGhlApiCall(
                    locationId,
                    options.method,
                    options.url,
                    undefined,
                    { headers: options.headers },
                    tokens,
                );
                var numberOfDays = Object.keys(daysList).length;

                await this.logMe(
                    'Number of days when less than 1: ',
                    numberOfDays,
                    sessionId,
                );
                // var daysList = response.data;
            } catch (err) {
                await this.errorMe(
                    'Error code while reading calendar: ',
                    err,
                    'SessionService',
                );
                // 
                if (err.response && err.response.status && err.response.status == 401) {
                await this.errorMe(
                    'Your ghl access token has expired: ',
                    sessionId,
                    'SessionService',
                );
                return null;
                // 
                } else if (err.response && err.response.status && err.response.status == 403) {
                    await this.errorMe(
                        'Your calendar is not accessible: ',
                        sessionId,
                        'SessionService',
                    );
                    criticalErrors.push(
                        'Your calendar might not be active. Kindly check your calendar.',
                    );
                    for (const response of botResponseForEachDataSource) {
                        if (
                        response.kind == PROVIDERS.GHL_CALENDAR &&
                        response.action == action.activity
                        ) {
                        response.errors = criticalErrors;
                        }
                    }
                    return null;
                }
            }
        }

        for (var day in daysList) {
        if (day !== 'traceId') {
            //convert day to conversational format
            var year = parseInt(day.split('-')[0], 10);
            var month = parseInt(day.split('-')[1], 10);
            var dayOfMonth = parseInt(day.split('-')[2], 10);
            var date = new Date(year, month - 1, dayOfMonth);
            var longDate = date.toLocaleDateString('en-US', {
            weekday: 'long',
            month: 'long',
            day: 'numeric',
            });

            var daySlots = daysList[day].slots;
            if (
            daySlots !== undefined &&
            daySlots.length > 0 &&
            daySlots.length <= 5
            ) {
            var slotPrompt =
                'On ' +
                longDate +
                ', the following times are available:';

            for (var s = 0; s < daySlots.length; s++) {
                var slot = daySlots[s];
                var timeSlot = slot.split('T')[1];
                var actualTime = timeSlot.split('-')[0];
                var hour = parseInt(actualTime.split(':')[0]);
                var minute = actualTime.split(':')[1];
                var ampm = 'am';
                if (hour > 12) {
                hour -= 12;
                ampm = 'pm';
                } else if (hour === 12) {
                ampm = 'pm';
                } else if (hour === 0) {
                hour = 12;
                }
                slot = hour + ':' + minute + ampm;

                slotPrompt += slot + ', ';
                //if it's the last slot, remove the comma and add a new line
                if (s === daySlots.length - 1) {
                slotPrompt = slotPrompt.slice(0, -2);
                slotPrompt += '\n\n';
                }
            }
            } else if (
            daySlots !== undefined &&
            daySlots.length > 5
            ) {
            var selectedSlots = [];
            var slotPrompt =
                'On ' +
                longDate +
                ', the following times are available:\n';

            while (selectedSlots.length < 5) {
                var randomIndex = Math.floor(
                Math.random() * daySlots.length,
                );
                if (
                selectedSlots.indexOf(daySlots[randomIndex]) === -1
                ) {
                selectedSlots.push(daySlots[randomIndex]);
                }
            }

            for (var s = 0; s < selectedSlots.length; s++) {
                var slot = selectedSlots[s];
                var timeSlot = slot.split('T')[1];
                var actualTime = timeSlot.split('-')[0];
                var hour = parseInt(actualTime.split(':')[0]);
                var minute = actualTime.split(':')[1];
                var ampm = 'am';
                if (hour > 12) {
                hour -= 12;
                ampm = 'pm';
                } else if (hour === 12) {
                ampm = 'pm';
                } else if (hour === 0) {
                hour = 12;
                }
                var formattedSlot = hour + ':' + minute + ampm;

                slotPrompt += formattedSlot + ', ';
                //if it's the last slot, remove the comma and add a new line
                if (s === selectedSlots.length - 1) {
                slotPrompt = slotPrompt.slice(0, -2);
                slotPrompt += '\n\n';
                }
            }
            } else {
            var slotPrompt =
                '\n\nOn ' + day + ', there are no available times.';
            }

            var slotPromptString = '';
            slotPromptString +=
            'After checking the calendar, the following results were returned:\n\n';
            for (var s = 0; s < slotPrompts.length; s++) {
            slotPromptString += slotPrompts[s];
            }
            slotPrompts.push(slotPrompt);
        }
        }

        //get today's date as a conversational string
        var today = new Date();
        var todayStamp = today.getTime();
        var tomorrowStamp = todayStamp + 24 * 60 * 60 * 1000;
        var nextDayStamp = tomorrowStamp + 24 * 60 * 60 * 1000;
        var dayAfterStamp = nextDayStamp + 24 * 60 * 60 * 1000;
        var dayAfterDayAfterStamp =
        dayAfterStamp + 24 * 60 * 60 * 1000;
        var lastDayStamp =
        dayAfterDayAfterStamp + 24 * 60 * 60 * 1000;
        var tomorrow = new Date(tomorrowStamp);

        var todayString = today.toLocaleDateString('en-US', {
        weekday: 'long',
        month: 'long',
        day: 'numeric',
        timeZone: timezone,
        });
        var tomorrowString = tomorrow.toLocaleDateString('en-US', {
        weekday: 'long',
        month: 'long',
        day: 'numeric',
        timeZone: timezone,
        });
        var nextDayString = new Date(nextDayStamp).toLocaleDateString(
        'en-US',
        {
            weekday: 'long',
            month: 'long',
            day: 'numeric',
            timeZone: timezone,
        },
        );
        var nextDayStringOfWeek = new Date(
        nextDayStamp,
        ).toLocaleDateString('en-US', {
        weekday: 'long',
        timeZone: timezone,
        });
        var dayAfterString = new Date(
        dayAfterStamp,
        ).toLocaleDateString('en-US', {
        weekday: 'long',
        month: 'long',
        day: 'numeric',
        timeZone: timezone,
        });
        var dayAfterStringOfWeek = new Date(
        dayAfterStamp,
        ).toLocaleDateString('en-US', {
        weekday: 'long',
        timeZone: timezone,
        });
        var dayAfterDayAfterString = new Date(
        dayAfterDayAfterStamp,
        ).toLocaleDateString('en-US', {
        weekday: 'long',
        month: 'long',
        day: 'numeric',
        timeZone: timezone,
        });
        var dayAfterDayAfterStringOfWeek = new Date(
        dayAfterDayAfterStamp,
        ).toLocaleDateString('en-US', {
        weekday: 'long',
        timeZone: timezone,
        });
        var lastDayString = new Date(lastDayStamp).toLocaleDateString(
        'en-US',
        {
            weekday: 'long',
            month: 'long',
            day: 'numeric',
            timeZone: timezone,
        },
        );
        var lastDayStringOfWeek = new Date(
        lastDayStamp,
        ).toLocaleDateString('en-US', {
        weekday: 'long',
        timeZone: timezone,
        });

        const newBotResponse: IBotActionResponse = {
        eventId: uuidv4(),
        sender: 'bot',
        kind: PROVIDERS.GHL_CALENDAR,
        accountName: action.accountName,
        accountId: accountId,
        deleted: false,
        action: 'read',
        eventData: {
            startDate: start,
            endDate: end,
        },
        reference: ghlReference,
        timestamp: Math.floor(Date.now() / 1000),
        };

        // push the new bot response to the botResponse array
        botResponseForEachDataSource.push(newBotResponse);
        systemPrompt +=
        '\n\nAfter checking the calendar, the following available days and times were found:';
        systemPrompt += slotPrompts.join('');
        systemPrompt +=
        'You should only refer the listed times above when discussing availability with the contact. Assume that any other days or times not listed above are not confirmed as available.';
        const todayDate = new Date();
        const todayIs = new Date().toLocaleDateString('en-US', {
        weekday: 'long',
        month: 'long',
        day: 'numeric',
        timeZone: timezone,
        });
        todayDate.setDate(todayDate.getDate() + 1);
        const tomorrowIs = todayDate.toLocaleDateString('en-US', { weekday: 'long', month: 'long', day: 'numeric', timeZone: timezone });
        systemPrompt += `For context, today is ${todayIs} and tomorrow is ${tomorrowIs}\n\n`;
        await this.logMe(
        'Days and Dates available: ',
        slotPrompts.join(''),
        sessionId,
        );
    }
    catch(err) {
        await this.errorMe('Error while reading calendar: ', err, 'SessionService');
        // 
        if (err.response && err.response.status && err.response.status == 401) {
        await this.errorMe(
            'Your ghl access token has expired: ',
            sessionId,
            'SessionService',
        );
        return null;
        // 
        } else if (err.response && err.response.status && err.response.status == 403) {
        await this.errorMe(
            'Your calendar is not accessible: ',
            sessionId,
            'SessionService',
        );
        criticalErrors.push(
            'Your calendar might not be active. Kindly check your calendar.',
        );
        for (const response of botResponseForEachDataSource) {
            if (
            response.kind == PROVIDERS.GHL_CALENDAR &&
            response.action == action.activity
            ) {
            response.errors = criticalErrors;
            }
        }
        return null;
        // await this.logMe('Your ghl access token has expired: ', sessionId, 'SessionService');
        }else if(err.response && err.response.data){
            await this.errorMe('Read calendar error message: ',err.response.data.message, sessionId);
            await this.errorMe("Reach calendar error status: ", err.response.data.statusCode, sessionId);
        }
    }
    // 
}

export async function ghlChannel(action, query, organizationDetails, agentId, orgid, systemPrompt, sessionId, contactDetails, conversationHistoryString, agentData, modelObj, credentialId, aiProvAdvSett, webhook, embedding, usage_yesNoToken){
    const maxTokens = 300;
    const botResponseForEachDataSource : IBotActionResponse[] = [];
    const accountId = action.accountId;
    if(webhook == true){
        if(action.metaData.tagData){
            let tagValue = action.metaData.tagData.tagValue;
            let evaluateOnDb = action.metaData.tagData.evaluateOn;

            if(evaluateOnDb === "everyTurn"){

                await this.logMe(
                'Yes for reading: ',
                action.providerName,
                sessionId,
                );
                // 
                let tagName = action.accountName;
                let tagPrompt =
                '\n\nYou have been provided a data source with the following description:';
                tagPrompt += '\n' + action.promptContent;

                const tagSamples = await this.getTagSamples(
                agentId,
                embedding,
                orgid,
                action.activity,
                );

                // 

                if (tagSamples.matches.length !== 0) {
                    tagPrompt +=
                        '\n\n' +
                        'Use the following data from this data source as context to continue the conversation:';
                    systemPrompt += tagPrompt;
                    let totalSemanticTagContent: string = '';
                    for (const content of tagSamples.matches) {
                        if (content.metadata['content'] != undefined) {
                        let nestedTagPrompt =
                            '\n\n' + content.metadata['content'];
                        if (typeof nestedTagPrompt == 'string') {
                            totalSemanticTagContent += nestedTagPrompt;
                            const systemPromptLength =
                            await this.numberOfTokens(
                                totalSemanticTagContent,
                            );

                            if (
                            action.advancedSettings &&
                            action.advancedSettings.hasOwnProperty(
                                'maxTokensAllowed',
                            )
                            ) {
                            if (
                                systemPromptLength.length <
                                action.advancedSettings.maxTokensAllowed
                            ) {
                                systemPrompt += nestedTagPrompt;
                            }
                            } else {
                            if (systemPromptLength.length < 450) {
                                systemPrompt += nestedTagPrompt;
                            }
                            }
                        }
                        }
                    }
                }

                // 

                // fill in a new botResponse
                const newBotResponse: IBotActionResponse = {
                eventId: uuidv4(),
                sender: 'bot',
                kind: PROVIDERS.GHL_CHANNEL,
                accountId: accountId,
                accountName: tagName,
                deleted: false,
                action: 'tag',
                eventData: {
                    tag: {
                    name: tagName,
                    silent: false,
                    },
                    // genericValue:
                },
                reference: '',
                timestamp: Math.floor(Date.now() / 1000),
                };

                // push the new bot response to the botResponse array
                botResponseForEachDataSource.push(newBotResponse);

            }else if(evaluateOnDb === "contactNoTag"){
                if(!contactDetails.tags.includes(tagValue)){

                    await this.logMe(
                        'Yes for reading: ',
                        action.providerName,
                        sessionId,
                    );
                    // 
                    let tagName = action.accountName;
                    let tagPrompt =
                        '\n\nYou have been provided a data source with the following description:';
                    tagPrompt += '\n' + action.promptContent;

                    const tagSamples = await this.getTagSamples(
                        agentId,
                        embedding,
                        orgid,
                        action.activity,
                    );

                    // 

                    if (tagSamples.matches.length !== 0) {
                        tagPrompt +=
                        '\n\n' +
                        'Use the following data from this data source as context to continue the conversation:';
                        systemPrompt += tagPrompt;
                        let totalSemanticTagContent: string = '';
                        for (const content of tagSamples.matches) {
                        if (content.metadata['content'] != undefined) {
                            let nestedTagPrompt =
                            '\n\n' + content.metadata['content'];
                            if (typeof nestedTagPrompt == 'string') {
                            totalSemanticTagContent += nestedTagPrompt;
                            const systemPromptLength =
                                await this.numberOfTokens(
                                totalSemanticTagContent,
                                );

                            if (
                                action.advancedSettings &&
                                action.advancedSettings.hasOwnProperty(
                                'maxTokensAllowed',
                                )
                            ) {
                                if (
                                systemPromptLength.length <
                                action.advancedSettings.maxTokensAllowed
                                ) {
                                systemPrompt += nestedTagPrompt;
                                }
                            } else {
                                if (systemPromptLength.length < 450) {
                                systemPrompt += nestedTagPrompt;
                                }
                            }
                            }
                        }
                        }
                    }

                    const newBotResponse: IBotActionResponse = {
                        eventId: uuidv4(),
                        sender: 'bot',
                        kind: PROVIDERS.GHL_CHANNEL,
                        accountId: accountId,
                        accountName: tagName,
                        deleted: false,
                        action: 'tag',
                        eventData: {
                        tag: {
                            name: tagName,
                            silent: false,
                        },
                        },
                        reference: '',
                        timestamp: Math.floor(Date.now() / 1000),
                    };

                    // push the new bot response to the botResponse array
                    botResponseForEachDataSource.push(newBotResponse);
                    
                
                }else{
                    
                }
            }
        }else if(action.metaData.customFieldData){
            let fieldValue = action.metaData.customFieldData.fieldKey;
            let evaluateOnDb = action.metaData.customFieldData.evaluateOn;
            let query_reply = action.metaData.customFieldData.reply;
            let accountName = action.accountName;

            if(evaluateOnDb === "everyTurn"){
                await this.logMe(
                'Yes for reading: ',
                action.providerName,
                sessionId,
                );
                // 
                var customFieldTrainingPrompt =
                'You are a smart and intelligent Named Entity Recognition (NER) system. You are an expert at identifying Natural Language entities that can be extracted from a conversation happening between a contact and an assistant. The specific entities you excel the most at identifying are those that relate to the json values required for a specific key. When the contact describes the values required to be filled against the specific keys, you are able to generate a JSON object that describes the key and value, relative to the conversation history. The key and value are always described as strings. Here is the structure you should follow for your output:\n\n';
                customFieldTrainingPrompt +=
                '\n\
                {\n\
                    "key" : value (any)\n\
                }';
                customFieldTrainingPrompt +=
                '\nWhen provided with a conversation, you should be able to generate a JSON object that describes the key and value of the json it required, relative to the conversation history.';
                customFieldTrainingPrompt +=
                '\nNote: There is only one key value pair to be generated, so generate it correctly.\n';
                customFieldTrainingPrompt += `Here is the conversation so far:\n\n${conversationHistoryString}\n\nBased on the conversation above, generate the json required for the following key.\n\nBelow is the value you need to fill.\n`;
                // let localJsonSystemPromptAppend = '```\n';
                let localJsonSystemPromptAppend = `${action.metaData.customFieldData['fieldKey']}`;
                localJsonSystemPromptAppend += '\n';
                // localJsonSystemPromptAppend += '```\n';
                customFieldTrainingPrompt += localJsonSystemPromptAppend;
                // customFieldTrainingPrompt += '\n' + systemPromptForYesNo;
                // this is the message
                let messagesJsonGenerate = [
                { role: 'system', content: customFieldTrainingPrompt },
                ];

                messagesJsonGenerate.push({
                role: 'user',
                content: query,
                });

                let trainingResponse_response_response =
                await this.generateBotResponse(
                    uuidv4(), credentialId, action, agentData,
                    messagesJsonGenerate,
                    maxTokens,
                    modelObj,
                    aiProvAdvSett,
                );
                const trainingResponse_response = trainingResponse_response_response.response;
                let trainingResponse = '';
                let companyId = modelObj.companyId;
                if (companyId === PROVIDERS.OPENAI_PROVIDER) {
                  trainingResponse =
                    trainingResponse_response.data.choices[0].message.content;
                  usage_yesNoToken +=
                    trainingResponse_response.data.usage.total_tokens;
                } else if (companyId === PROVIDERS.CLAUDE_PROVIDER) {
                  trainingResponse = trainingResponse_response.content[0].text;
                  usage_yesNoToken +=
                    trainingResponse_response.usage.input_tokens +
                    trainingResponse_response.usage.output_tokens;
                } else if (companyId === PROVIDERS.GROQ_PROVIDER) {
                  trainingResponse =
                    trainingResponse_response.choices[0].message.content;
                  usage_yesNoToken +=
                    trainingResponse_response.usage.total_tokens;
                } else if (companyId === PROVIDERS.FIREWORKS_PROVIDER) {
                  trainingResponse =
                    trainingResponse_response.choices[0].message.content;
                  usage_yesNoToken +=
                      trainingResponse_response.usage.total_tokens;
                    
                    const pricePerMillion = CUSTOM_LLM.COST_PER_MILLION;
                    const usageInMillions = usage_yesNoToken / 1000000;
                    const cost = usageInMillions * pricePerMillion;

                    await this.mongoOrganizationService.updateHostedAiUsage(
                      orgid,
                      {
                        numOfTokensUsed: usage_yesNoToken,
                        cost: cost,
                        provider: companyId,
                        model: CUSTOM_LLM.PUBLIC_MODEL_NAME,
                      },
                    );
                }
                try {
                let jsonObjectComplete = trainingResponse.trim();
                const jsonRegex = /\{.*\}/s;
                const match = jsonObjectComplete.match(jsonRegex);
                if (match) {
                    const extractedJson = match[0];
                    var completionVars = JSON.parse(extractedJson);
                    const numberOfProperties =
                    Object.keys(completionVars).length;
                    if (numberOfProperties == 1) {
                    const requiredValue =
                        Object.values(completionVars)[0];
                    // fill in a new botResponse
                    const newBotResponse: IBotActionResponse = {
                        eventId: uuidv4(),
                        sender: 'bot',
                        kind: PROVIDERS.GHL_CHANNEL,
                        accountId: accountId,
                        accountName: accountName,
                        deleted: false,
                        action: PROVIDERS.CustomField,
                        eventData: {
                            // [fieldValue]: requiredValue,
                            customFieldData: {
                                fieldKey: fieldValue,
                                reply: query_reply,
                                evaluateOn: evaluateOnDb,
                                field_value: requiredValue,
                            },
                        },
                        timestamp: Math.floor(Date.now() / 1000),
                    };
                    // push the new bot response to the botResponse array
                    botResponseForEachDataSource.push(newBotResponse);
                    } else {
                    
                    }
                } else {
                    
                }
                } catch (error) {
                
                }
            
            }else if(evaluateOnDb === "isEmpty"){
                const fieldExists = contactDetails.customFields.some(element => element.fieldKey === fieldValue);
                // If the field doesn't exist, log an error message
                if (fieldExists) {
                    for(const eachValue of contactDetails.customFields){
                        if(eachValue.fieldKey == fieldValue){
                        if(eachValue.value == ''){
                            await this.logMe(
                            'Yes for reading: ',
                            action.providerName,
                            sessionId,
                            );
                            // 
                            var customFieldTrainingPrompt =
                            'You are a smart and intelligent Named Entity Recognition (NER) system. You are an expert at identifying Natural Language entities that can be extracted from a conversation happening between a contact and an assistant. The specific entities you excel the most at identifying are those that relate to the json values required for a specific key. When the contact describes the values required to be filled against the specific keys, you are able to generate a JSON object that describes the key and value, relative to the conversation history. The key and value are always described as strings. Here is the structure you should follow for your output:\n\n';
                            customFieldTrainingPrompt +=
                            '\n\
                            {\n\
                            "key" : value (any)\n\
                            }';
                            customFieldTrainingPrompt +=
                            '\nWhen provided with a conversation, you should be able to generate a JSON object that describes the key and value of the json it required, relative to the conversation history.';
                            customFieldTrainingPrompt +=
                            '\nNote: There is only one key value pair to be generated, so generate it correctly.\n';
                            customFieldTrainingPrompt += `Here is the conversation so far:\n\n${conversationHistoryString}\n\nBased on the conversation above, generate the json required for the following key.\n\nBelow is the value you need to fill.\n`;
                            let localJsonSystemPromptAppend = `${action.metaData.customFieldData['fieldKey']}`;
                            localJsonSystemPromptAppend += '\n';
                            customFieldTrainingPrompt +=
                            localJsonSystemPromptAppend;
                            // customFieldTrainingPrompt +=
                            // '\n' + systemPromptForYesNo;
                            // this is the message
                            let messagesJsonGenerate = [
                            {
                                role: 'system',
                                content: customFieldTrainingPrompt,
                            },
                            ];

                            messagesJsonGenerate.push({
                            role: 'user',
                            content: query,
                            });

                            let trainingResponse_response_response =
                            await this.generateBotResponse(
                                uuidv4(), credentialId, action, agentData,
                                messagesJsonGenerate,
                                maxTokens,
                                modelObj,
                                aiProvAdvSett,
                            );
                            const trainingResponse_response = trainingResponse_response_response.response;
                            let trainingResponse = '';
                            let ghlChannel_iE_token_length = 0;
                            let ghlChannel_iE_input_tokens = 0;
                            let companyId = modelObj.companyId;
                            if (companyId === PROVIDERS.OPENAI_PROVIDER) {
                              trainingResponse =
                                trainingResponse_response.data.choices[0]
                                  .message.content;
                              usage_yesNoToken +=
                                trainingResponse_response.data.usage
                                  .total_tokens;
                              ghlChannel_iE_token_length +=
                                trainingResponse_response.data.usage
                                  .completion_tokens;
                              ghlChannel_iE_input_tokens +=
                                trainingResponse_response.data.usage
                                  .prompt_tokens;
                            } else if (
                              companyId === PROVIDERS.CLAUDE_PROVIDER
                            ) {
                              trainingResponse =
                                trainingResponse_response.content[0].text;
                              usage_yesNoToken +=
                                trainingResponse_response.usage.input_tokens +
                                trainingResponse_response.usage.output_tokens;
                              ghlChannel_iE_token_length +=
                                trainingResponse_response.usage.output_tokens;
                              ghlChannel_iE_input_tokens +=
                                trainingResponse_response.usage.input_tokens;
                            } else if (companyId === PROVIDERS.GROQ_PROVIDER) {
                              trainingResponse =
                                trainingResponse_response.choices[0].message
                                  .content;
                              usage_yesNoToken +=
                                trainingResponse_response.usage.total_tokens;
                              ghlChannel_iE_token_length +=
                                trainingResponse_response.usage
                                  .completion_tokens;
                              ghlChannel_iE_input_tokens +=
                                trainingResponse_response.usage.prompt_tokens;
                            } else if (
                              companyId === PROVIDERS.FIREWORKS_PROVIDER
                            ) {
                              trainingResponse =
                                trainingResponse_response.choices[0].message
                                  .content;
                              usage_yesNoToken +=
                                trainingResponse_response.usage.total_tokens;
                              ghlChannel_iE_token_length +=
                                trainingResponse_response.usage.completion_tokens;
                              ghlChannel_iE_input_tokens +=
                                  trainingResponse_response.usage.prompt_tokens;
                                
                                const pricePerMillion =
                                  CUSTOM_LLM.COST_PER_MILLION;
                                const usageInMillions =
                                  usage_yesNoToken / 1000000;
                                const cost = usageInMillions * pricePerMillion;

                                await this.mongoOrganizationService.updateHostedAiUsage(
                                  orgid,
                                  {
                                    numOfTokensUsed: usage_yesNoToken,
                                    cost: cost,
                                    provider: companyId,
                                    model: CUSTOM_LLM.PUBLIC_MODEL_NAME,
                                  },
                                );
                            }
                            try {
                            let jsonObjectComplete =
                                trainingResponse.trim();
                            const jsonRegex = /\{.*\}/s;
                            const match =
                                jsonObjectComplete.match(jsonRegex);
                            if (match) {
                                const extractedJson = match[0];
                                var completionVars =
                                JSON.parse(extractedJson);
                                const numberOfProperties =
                                Object.keys(completionVars).length;
                                if (numberOfProperties == 1) {
                                const requiredValue =
                                    Object.values(completionVars)[0];
                                // fill in a new botResponse
                                const newBotResponse: IBotActionResponse = {
                                    eventId: uuidv4(),
                                    sender: 'bot',
                                    kind: PROVIDERS.GHL_CHANNEL,
                                    accountId: accountId,
                                    accountName: accountName,
                                    deleted: false,
                                    action: PROVIDERS.CustomField,
                                    eventData: {
                                    // [fieldValue]: requiredValue,
                                    customFieldData: {
                                        fieldKey: fieldValue,
                                        reply: query_reply, //if silent false, reply: true
                                        evaluateOn: evaluateOnDb,
                                        field_value: requiredValue,
                                    },
                                    },
                                    timestamp: Math.floor(Date.now() / 1000),
                                };

                                // push the new bot response to the botResponse array
                                botResponseForEachDataSource.push(
                                    newBotResponse,
                                );
                                } else {
                                
                                }
                            } else {
                                
                            }
                            } catch (error) {
                            
                            }
                        
                        }else {
                            
                        }
                        }
                    }
                }else{
                    
                }
            }
        }
    }else if(webhook == false){
        if(action.metaData.tagData){
            await this.logMe(
                'Yes for reading: ',
                action.providerName,
                sessionId,
            );
            let tagName = action.accountName;
            let tagPrompt =
                '\n\nYou have been provided a data source with the following description:';
            tagPrompt += '\n' + action.promptContent;

            const tagSamples = await this.getTagSamples(
                agentId,
                embedding,
                orgid,
                action.activity,
            );

            // 

            if (tagSamples.matches.length !== 0) {
                tagPrompt +=
                '\n\n' +
                'Use the following data from this data source as context to continue the conversation:';
                systemPrompt += tagPrompt;
                let totalSemanticTagContent: string = '';
                for (const content of tagSamples.matches) {
                if (content.metadata['content'] != undefined) {
                    let nestedTagPrompt =
                    '\n\n' + content.metadata['content'];
                    if (typeof nestedTagPrompt == 'string') {
                    totalSemanticTagContent += nestedTagPrompt;
                    const systemPromptLength =
                        await this.numberOfTokens(
                        totalSemanticTagContent,
                        );

                    if (
                        action.advancedSettings &&
                        action.advancedSettings.hasOwnProperty(
                        'maxTokensAllowed',
                        )
                    ) {
                        if (
                        systemPromptLength.length <
                        action.advancedSettings.maxTokensAllowed
                        ) {
                        systemPrompt += nestedTagPrompt;
                        } else {
                        if (systemPromptLength.length < 450) {
                            systemPrompt += nestedTagPrompt;
                        }
                        }
                    }
                    }
                }
                }
            }

            const newBotResponse: IBotActionResponse = {
                eventId: uuidv4(),
                sender: 'bot',
                kind: PROVIDERS.GHL_CHANNEL,
                accountId: accountId,
                accountName: tagName,
                deleted: false,
                action: 'tag',
                eventData: {
                tag: {
                    name: tagName,
                    silent: false,
                },
                // genericValue:
                },
                reference: '',
                timestamp: Math.floor(Date.now() / 1000),
            };

            // push the new bot response to the botResponse array
            botResponseForEachDataSource.push(newBotResponse);
        }else if(action.metaData.customFieldData){
            let fieldValue = action.metaData.customFieldData.fieldKey;
            let evaluateOnDb = action.metaData.customFieldData.evaluateOn;
            let query_reply = action.metaData.customFieldData.reply;
            let accountName = action.accountName;
            await this.logMe(
                'Yes for reading: ',
                action.providerName,
                sessionId,
            );
            var customFieldTrainingPrompt =
                'You are a smart and intelligent Named Entity Recognition (NER) system. You are an expert at identifying Natural Language entities that can be extracted from a conversation happening between a contact and an assistant. The specific entities you excel the most at identifying are those that relate to the json values required for a specific key. When the contact describes the values required to be filled against the specific keys, you are able to generate a JSON object that describes the key and value, relative to the conversation history. The key is always described as strings and the value is described as any variable like number, string or boolean. Here is the structure you should follow for your output:\n\n';
            customFieldTrainingPrompt +=
                '\n\
            {\n\
                "key" : value (any)\n\
            }';
            customFieldTrainingPrompt +=
                '\nWhen provided with a conversation, you should be able to generate a JSON object that describes the key and value of the json it required, relative to the conversation history.';
            customFieldTrainingPrompt +=
                '\nNote: There is only one key value pair to be generated, so generate it correctly in the right structure of json.\n';
            customFieldTrainingPrompt += `Here is the conversation so far:\n\n${conversationHistoryString}\n\nBased on the conversation above, generate the json required for the following key.\n\nBelow is the value you need to fill.\n`;
            let localJsonSystemPromptAppend = '\n';
            localJsonSystemPromptAppend += `${action.metaData.customFieldData['fieldKey']}`;
            localJsonSystemPromptAppend += '\n';
            // localJsonSystemPromptAppend += '```\n';
            customFieldTrainingPrompt += localJsonSystemPromptAppend;
            // customFieldTrainingPrompt += '\n' + systemPromptForYesNo;
            // this is the message
            let messageCustomField = [
                { role: 'system', content: customFieldTrainingPrompt },
            ];

            messageCustomField.push({
                role: 'user',
                content: query,
            });

            let trainingResponse_response_response =
                await this.generateBotResponse(
                uuidv4(), credentialId, action, agentData, 
                messageCustomField,
                maxTokens,
                modelObj,
                aiProvAdvSett,
            );
            let generateJson_response_token_length = 0;
            const trainingResponse_response = trainingResponse_response_response.response;
            let trainingResponse = '';
            let companyId = modelObj.companyId;
            if (companyId === PROVIDERS.OPENAI_PROVIDER) {
              trainingResponse =
                trainingResponse_response.data.choices[0].message.content;
              usage_yesNoToken +=
                trainingResponse_response.data.usage.total_tokens;
              generateJson_response_token_length +=
                trainingResponse_response.data.usage.completion_tokens;
            } else if (companyId === PROVIDERS.CLAUDE_PROVIDER) {
              trainingResponse = trainingResponse_response.content[0].text;
              usage_yesNoToken +=
                trainingResponse_response.usage.input_tokens +
                trainingResponse_response.usage.output_tokens;
              generateJson_response_token_length +=
                trainingResponse_response.usage.output_tokens;
            } else if (companyId === PROVIDERS.GROQ_PROVIDER) {
              trainingResponse =
                trainingResponse_response.choices[0].message.content;
              usage_yesNoToken += trainingResponse_response.usage.total_tokens;
              generateJson_response_token_length +=
                trainingResponse_response.usage.completion_tokens;
            } else if (companyId === PROVIDERS.FIREWORKS_PROVIDER) {
              trainingResponse =
                trainingResponse_response.choices[0].message.content;
              usage_yesNoToken += trainingResponse_response.usage.total_tokens;
              generateJson_response_token_length +=
                  trainingResponse_response.usage.completion_tokens;
                
                const pricePerMillion = CUSTOM_LLM.COST_PER_MILLION;
                const usageInMillions = usage_yesNoToken / 1000000;
                const cost = usageInMillions * pricePerMillion;

                await this.mongoOrganizationService.updateHostedAiUsage(orgid, {
                  numOfTokensUsed: usage_yesNoToken,
                  cost: cost,
                  provider: companyId,
                  model: CUSTOM_LLM.PUBLIC_MODEL_NAME,
                });
            }
            try {
                let jsonObjectComplete = trainingResponse.trim();
                const jsonRegex = /\{.*\}/s;
                const match = jsonObjectComplete.match(jsonRegex);
                if (match) {
                const extractedJson = match[0];
                var completionVars = JSON.parse(extractedJson);
                const numberOfProperties =
                    Object.keys(completionVars).length;
                if (numberOfProperties == 1) {
                    const requiredValue =
                    Object.values(completionVars)[0];
                    // fill in a new botResponse
                    const newBotResponse: IBotActionResponse = {
                    eventId: uuidv4(),
                    sender: 'bot',
                    kind: PROVIDERS.GHL_CHANNEL,
                    accountId: accountId,
                    accountName: accountName,
                    deleted: false,
                    action: PROVIDERS.CustomField,
                    eventData: {
                        // [fieldValue]: requiredValue,
                        customFieldData: {
                        fieldKey: fieldValue,
                        field_value: requiredValue,
                        },
                    },
                    timestamp: Math.floor(Date.now() / 1000),
                    };

                    // push the new bot response to the botResponse array
                    botResponseForEachDataSource.push(newBotResponse);
                } else {
                    
                }
                } else {
                
                }
            } catch (error) {
                
            }

        }
    }
}

export async function generateJson(action, apiKey, query, organizationDetails, agentId, orgid, systemPrompt, sessionId, conversationHistoryString, modelObj, agentData, embedding, usage_yesNoToken, aiProvAdvSett, credentialId){
    const maxTokens = 300;
    const botResponseForEachDataSource : IBotActionResponse[] = [];
    await this.logMe(
        'Yes for reading: ',
        action.providerName,
        sessionId,
    );
    // 
    let jsonId: string, sheetReference: string;

    let jsonName = action.accountName;
    const accountId = action.accountId;
    // get jsonId and sheet reference from organization (jsonId = credentialId "in case of googleSheet")
    for (const dataSource of organizationDetails.connections
        .dataSources) {
        if (dataSource.accountId == accountId) {
        jsonId = dataSource.accountId;
        }
    }

    let jsonPrompt =
        '\n\nYou have been provided a data source with the following description:\n\n';
    jsonPrompt += action.promptContent;
    const jsonIndex = await this.getSessionSamples(
        agentId,
        orgid,
        embedding,
        PROVIDERS.Generate_JSON,
        jsonId,
    );

    // const [rowStart, rowEnd] = action.headers.split(":");

    if (jsonIndex) {
        if (jsonIndex.matches.length > 0) {
        jsonPrompt +=
            '\n\nUse the following data for the above data source as context to continue the conversation:\n';
        systemPrompt += jsonPrompt;
        let totalSemanticSheetContent: string = '';

        for (const content of jsonIndex.matches) {
            // 
            if (content.metadata['content'] !== undefined) {
            let nestedSheetPrompt = content.metadata['content'];
            if (typeof nestedSheetPrompt == 'string') {
                nestedSheetPrompt = nestedSheetPrompt.replace(
                /\n{3,}(?=(user:|Contact:))/g,
                '\n\n',
                );
                totalSemanticSheetContent += nestedSheetPrompt;
                const checkSheetLength = await this.numberOfTokens(
                totalSemanticSheetContent,
                );
                if (
                action.advancedSettings &&
                action.advancedSettings.hasOwnProperty(
                    'maxTokensAllowed',
                )
                ) {
                if (
                    checkSheetLength.length <
                    action.advancedSettings.maxTokensAllowed
                ) {
                    systemPrompt += nestedSheetPrompt;
                    systemPrompt += '\n\n';
                }
                } else {
                if (checkSheetLength.length < 450) {
                    systemPrompt += nestedSheetPrompt;
                    systemPrompt += '\n\n';
                }
                }
            }
            }
        }
        } else {
        
        }
    } else {
        
    }
    var jsonPromptContentTraining =
        '\n\nYou are a smart and intelligent Named Entity Recognition (NER) system. You are an expert at identifying Natural Language entities that can be extracted from a conversation happening between a contact and an assistant. The specific entities you excel the most at identifying are those that relate to the json values required for a specific key. When the contact describes the values required to be filled against the specific keys, you are able to generate a JSON object that describes the key and value, relative to the conversation history. The key and value are always described as strings. Here is the structure you should follow for your output:\n\n';
    jsonPromptContentTraining +=
        '```\n\
    {\n\
        "key one" : value one (string),\n\
        "key two" : value two (string),\n\
    }```';
    jsonPromptContentTraining +=
        '\nWhen provided with a conversation, you should be able to generate a JSON object that describes the key and value of the json it required, relative to the conversation history.';
    jsonPromptContentTraining +=
        '\nNote: There may be any number of key value pairs to be generated, so generate all of them.\n';
    jsonPromptContentTraining += `Here is the conversation so far:\n\n${conversationHistoryString}\n\nBased on the conversation above, generate the json required for the following keys.\n\nBelow are the values you need to fill according to its description: \n`;
    let localJsonSystemPromptAppend = '```\n';
    for (const property of action.jsonObjects.properties) {
        localJsonSystemPromptAppend +=
        `${property['name']}` + ': ' + `${property['description']}`;
        localJsonSystemPromptAppend += '\n';
    }
    localJsonSystemPromptAppend += '```\n';
    jsonPromptContentTraining += localJsonSystemPromptAppend;
    // this is the message
    let messagesJsonGenerate = [
        { role: 'system', content: jsonPromptContentTraining },
    ];

    messagesJsonGenerate.push({
        role: 'user',
        content: query,
    });
    let generateJson_value_generate_token_length = 0;
    let generateJson_value_input_tokens = 0;

    let trainingResponse_response_response =
        await this.generateBotResponse(
        uuidv4(), credentialId, action, agentData,
        messagesJsonGenerate,
        maxTokens,
        modelObj,
        aiProvAdvSett,
        );
    const trainingResponse_response = trainingResponse_response_response.response;
    let trainingResponse = '';
    let companyId = modelObj.companyId;
    if (companyId === PROVIDERS.OPENAI_PROVIDER) {
      trainingResponse =
        trainingResponse_response.data.choices[0].message.content;
      usage_yesNoToken += trainingResponse_response.data.usage.total_tokens;
      generateJson_value_generate_token_length +=
        trainingResponse_response.data.usage.completion_tokens;
      generateJson_value_input_tokens +=
        trainingResponse_response.data.usage.prompt_tokens;
    } else if (companyId === PROVIDERS.CLAUDE_PROVIDER) {
      trainingResponse = trainingResponse_response.content[0].text;
      usage_yesNoToken +=
        trainingResponse_response.usage.input_tokens +
        trainingResponse_response.usage.output_tokens;
      generateJson_value_generate_token_length +=
        trainingResponse_response.usage.output_tokens;
      generateJson_value_input_tokens +=
        trainingResponse_response.usage.input_tokens;
    } else if (companyId === PROVIDERS.GROQ_PROVIDER) {
      trainingResponse = trainingResponse_response.choices[0].message.content;
      usage_yesNoToken += trainingResponse_response.usage.total_tokens;
      generateJson_value_generate_token_length +=
        trainingResponse_response.usage.completion_tokens;
      generateJson_value_input_tokens +=
        trainingResponse_response.usage.prompt_tokens;
    } else if (companyId === PROVIDERS.FIREWORKS_PROVIDER) {
      trainingResponse = trainingResponse_response.choices[0].message.content;
      usage_yesNoToken += trainingResponse_response.usage.total_tokens;
      generateJson_value_generate_token_length +=
        trainingResponse_response.usage.completion_tokens;
      generateJson_value_input_tokens +=
          trainingResponse_response.usage.prompt_tokens;
        
        const pricePerMillion = CUSTOM_LLM.COST_PER_MILLION;
        const usageInMillions = usage_yesNoToken / 1000000;
        const cost = usageInMillions * pricePerMillion;

        await this.mongoOrganizationService.updateHostedAiUsage(orgid, {
          numOfTokensUsed: usage_yesNoToken,
          cost: cost,
          provider: companyId,
          model: CUSTOM_LLM.PUBLIC_MODEL_NAME,
        });
    }
    
    let completionVars = trainingResponse.split('```');
    const numberOfProperties = Object.keys(completionVars).length;
    var jsonObjectComplete = JSON.parse(completionVars[0]);

    let jsonResponseStructure: IJsonResponseStructure[] = [];
    for (const property of action.jsonObjects.properties) {
        if (jsonObjectComplete.hasOwnProperty(property.name)) {
        jsonResponseStructure.push({
            name: property.name,
            description: property.description,
            type: property.type,
            id: property.id,
            value: jsonObjectComplete[`${property.name}`],
        });
        }
    }

    // 
    
    // fill in a new botResponse
    const newBotResponse: IBotActionResponse = {
        eventId: uuidv4(),
        sender: 'bot',
        kind: PROVIDERS.Generate_JSON,
        accountId: accountId,
        accountName: jsonName,
        deleted: false,
        action: 'read',
        eventData: {
        properties: [...jsonResponseStructure],
        },
        timestamp: Math.floor(Date.now() / 1000),
    };

    // push the new bot response to the botResponse array
    botResponseForEachDataSource.push(newBotResponse);
}

export async function googleDocs(action, apiKey, query, organizationDetails, agentId, orgid, systemPrompt, sessionId){
    const botResponseForEachDataSource : IBotActionResponse[] = [];
    const accountId = action.accountId;
    await this.logMe(
        'Yes for reading: ',
        action.providerName,
        sessionId,
    );
    // 
    let googleDocId: string, googleDocReference: string;

    let googleDocName = action.accountName;

    const googleDocEmbeddings =
    await this.vectorService.createEmbedding(apiKey, query);

    const googleDocEmbedding = googleDocEmbeddings[0].embedding;

    // get googleDocId and sheet reference from organization (googleDocId = credentialId "in case of googleSheet")
    for (const dataSource of organizationDetails.connections
    .staticData) {
        if (dataSource.accountId == accountId) {
            googleDocId = dataSource.accountId;
            googleDocReference = dataSource.reference;
        }
    }

    let googleDocPrompt =
    '\n\nYou have been provided a data source with the following description:\n\n';
    googleDocPrompt += action.promptContent;
    const sessionIndexGoogleDoc = await this.getSessionSamples(
    agentId,
    orgid,
    googleDocEmbedding,
    PROVIDERS.GOOGLE_DOCS,
    googleDocId,
    );

    // const [rowStart, rowEnd] = action.headers.split(":");

    if (sessionIndexGoogleDoc) {
        if (sessionIndexGoogleDoc.matches.length > 0) {
            googleDocPrompt +=
            '\n\nUse the following data from this data source as context to continue the conversation:\n\n';
            systemPrompt += googleDocPrompt;
            let totalSemanticGoogleDocsContent: string = '';

            for (const content of sessionIndexGoogleDoc.matches) {
            // 
            if (
                content.metadata['question'] !== undefined &&
                content.metadata['answer'] !== undefined
            ) {
                let nestedGoogleDocsPrompt = content.metadata['question'];
                nestedGoogleDocsPrompt += '\n';
                nestedGoogleDocsPrompt += content.metadata['answer'];
                if (typeof nestedGoogleDocsPrompt == 'string') {
                // nestedGoogleDocsPrompt = nestedGoogleDocsPrompt.replace(/\n{3,}(?=(user:|Contact:))/g, '\n\n');
                totalSemanticGoogleDocsContent +=
                    nestedGoogleDocsPrompt;
                const checkGoogleDocsLength = await this.numberOfTokens(
                    totalSemanticGoogleDocsContent,
                );
                if (
                    action.advancedSettings &&
                    action.advancedSettings.hasOwnProperty(
                    'maxTokensAllowed',
                    )
                ) {
                    if (
                    checkGoogleDocsLength.length <
                    action.advancedSettings.maxTokensAllowed
                    ) {
                    systemPrompt += nestedGoogleDocsPrompt;
                    systemPrompt += '\n\n';
                    }
                } else {
                    if (checkGoogleDocsLength.length < 450) {
                    systemPrompt += nestedGoogleDocsPrompt;
                    systemPrompt += '\n\n';
                    }
                }
                }
            } else {
                
            }
            }
        } else {
            await this.logMe('No semantic data in ', action.providerName, sessionId);
        }
    } else {
        await this.logMe("Google Doc Not found in pinecone", '', sessionId);
    }

    // fill in a new botResponse
    const newBotResponse: IBotActionResponse = {
    eventId: uuidv4(),
    sender: 'bot',
    kind: PROVIDERS.GOOGLE_DOCS,
    accountId: accountId,
    accountName: googleDocName,
    deleted: false,
    action: 'read',
    eventData: {},
    reference: googleDocReference,
    timestamp: Math.floor(Date.now() / 1000),
    };

    // push the new bot response to the botResponse array
    botResponseForEachDataSource.push(newBotResponse);

}

export async function pdf(action, apiKey, query, organizationDetails, agentId, orgid, systemPrompt, sessionId){
    const botResponseForEachDataSource : IBotActionResponse[] = [];
    const accountId = action.accountId;
    await this.logMe(
        'Yes for reading: ',
        action.providerName,
        sessionId,
    );
    // 
    let pdfId: string, pdfReference: string;

    let pdfName = action.accountName;

    const pdfEmbeddings = await this.vectorService.createEmbedding(
        apiKey,
        query,
    );

    const pdfEmbedding = pdfEmbeddings[0].embedding;

    // get googleDocId and sheet reference from organization (googleDocId = credentialId "in case of googleSheet")
    for (const dataSource of organizationDetails.connections
        .staticData) {
        if (dataSource.accountId == accountId) {
        pdfId = dataSource.accountId;
        pdfReference = dataSource.reference;
        }
    }

    let pdfDocPrompt =
        '\n\nYou have been provided a data source with the following description:\n\n';
    pdfDocPrompt += action.promptContent;
    const sessionIndexPdf = await this.getSessionSamples(
        agentId,
        orgid,
        pdfEmbedding,
        PROVIDERS.PDF,
        pdfId,
    );

    // const [rowStart, rowEnd] = action.headers.split(":");

    if (sessionIndexPdf) {
        if (sessionIndexPdf.matches.length > 0) {
        pdfDocPrompt +=
            '\n\nUse the following data from this data source as context to continue the conversation:\n';
        systemPrompt += pdfDocPrompt;
        let totalSemanticPdfContent: string = '';

        for (const content of sessionIndexPdf.matches) {
            // 
            if (
            content.metadata['question'] !== undefined &&
            content.metadata['answer']
            ) {
            let nestedPdfPrompt = content.metadata['question'];
            nestedPdfPrompt += '\n';
            nestedPdfPrompt += content.metadata['answer'];
            if (typeof nestedPdfPrompt == 'string') {
                // nestedPdfPrompt = nestedPdfPrompt.replace(/\n{3,}(?=(user:|Contact:))/g, '\n\n');
                totalSemanticPdfContent += nestedPdfPrompt;
                const checkPdfLength = await this.numberOfTokens(
                totalSemanticPdfContent,
                );
                if (
                action.advancedSettings &&
                action.advancedSettings.hasOwnProperty(
                    'maxTokensAllowed',
                )
                ) {
                if (
                    checkPdfLength.length <
                    action.advancedSettings.maxTokensAllowed
                ) {
                    systemPrompt += nestedPdfPrompt;
                    systemPrompt += '\n\n';
                }
                } else {
                if (checkPdfLength.length < 450) {
                    systemPrompt += nestedPdfPrompt;
                    systemPrompt += '\n\n';
                }
                }
            }
            }
        }
        } else {
        
        }
    } else {
        
    }

    // fill in a new botResponse
    const newBotResponse: IBotActionResponse = {
        eventId: uuidv4(),
        sender: 'bot',
        kind: PROVIDERS.PDF,
        accountId: accountId,
        accountName: pdfName,
        deleted: false,
        action: 'read',
        eventData: {},
        reference: pdfReference,
        timestamp: Math.floor(Date.now() / 1000),
    };

    // push the new bot response to the botResponse array
    botResponseForEachDataSource.push(newBotResponse);

}
// export async function performLangChainRead(action, matchedActionId, sessionId, orgid, model_Obj, systemPrompt, organizationDetails, query, agentId, agentData, embedding, conversationHistoryString, contactDetails){
//     const maxTokens = 300;
//     const yesNoToken = 7;
//     let usage_yesNoToken = 0;
//     let rolloverDetails: IRolloverDetails = {
//       rollover: false,
//       rolloverReason: '',
//       rolloverDate: ''
//     }

//     let action_aiHistory: Action[] = [];
//     const botResponseForEachDataSource : IBotActionResponse[] = [];

//     let secretKey = model_Obj.key;
//     let companyId = model_Obj.companyId;

//     let apiKey;
//     if (companyId === PROVIDERS.OPENAI_PROVIDER) {
//       apiKey = secretKey;
//     } else if (companyId === PROVIDERS.CLAUDE_PROVIDER) {
//       apiKey = process.env.OPENAI_API_KEY;
//     } else if (companyId === PROVIDERS.GROQ_PROVIDER) {
//       apiKey = process.env.OPENAI_API_KEY;
//     } else {
//       
//     }

    
// }

// export async function langchainRead(agent, aiActions, sessionId, orgid, model_Obj, systemPrompt, organizationDetails, query, agentId, agentData, embedding, conversationHistoryString, contactDetails){
//     const actions = agent.actions;

//     for(const actionIds of aiActions){
//         const matchedAction = actions.find(eachAction => eachAction.actionId === actionIds.actionId);
//         if(matchedAction){
//             await performLangChainRead(matchedAction, actionIds.actionId, sessionId, orgid, model_Obj, systemPrompt, organizationDetails, query, agentId, agentData, embedding, conversationHistoryString, contactDetails);
//         }
//     }
// }
