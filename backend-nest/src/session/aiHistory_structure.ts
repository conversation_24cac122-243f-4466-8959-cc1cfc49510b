import { AIProvider_aiHistory, RequestDetails_aiHistory, ResponseDetail_aiHistory, Action, Channel_aiHistory, AiHistoryRecordDto } from "src/interfaces/interfaces";
import { CUSTOM_LLM, PROVIDERS, billing } from "src/lib/constant";

async function calculateCost(modelObj, inputTokens, outputTokens) {
  try {
    let totalCost = 0;
    const model = modelObj.model_name;
    const provider = modelObj.companyId;
    
    // Check if the provider exists in billing
    if (billing[provider] && billing[provider][model]) {
      // Calculate cost based on the model's input and output token rates
      totalCost += inputTokens * billing[provider][model].inputTokens;
      totalCost += outputTokens * billing[provider][model].outputTokens;
      return totalCost;
    }
    
    // If provider-specific model not found, search across all providers
    for (const category in billing) {
      if (Object.prototype.hasOwnProperty.call(billing, category)) {
        const models = billing[category];
        if (models[model]) {
          totalCost += inputTokens * models[model].inputTokens;
          totalCost += outputTokens * models[model].outputTokens;
          return totalCost;
        }
      }
    }
    
    
    return totalCost;
  } catch (error) {
    
    return 0;
  }
}

export async function calculateTotalCost(actions: Action[]): Promise<{ totalCost: number; totalTokens: number }> {
    let totalCost = 0;
    let totalTokens = 0;
    for (const action of actions) {
      totalCost += action.cost;
      totalTokens += action.request.totalTokens_req + action.response.totalTokens_res;
    }
    return {totalCost, totalTokens};
  }

export async function structure_aiHistory_action(agentData, modelObj, response, messages, action, credid, action_eventId): Promise<Action>{
    let aiProvider_aiHistory_sheet: AIProvider_aiHistory = {
        accountId: agentData.aiProvider ? agentData.aiProvider.accountId : '',
        credentialId:  agentData.aiProvider ? credid: "",
        modelType: modelObj.model_name,
        providerName: agentData.aiProvider ? agentData.aiProvider.companyId : CUSTOM_LLM.PROVIDERS
    };

    let inputTokens = 0, outputTokens = 0;
    if(modelObj.companyId == PROVIDERS.OPENAI_PROVIDER || modelObj.companyId == PROVIDERS.OPENAI_HOSTED || modelObj.companyId == PROVIDERS.OPENROUTER_PROVIDER){
        inputTokens += response.data.usage.prompt_tokens;
        outputTokens += response.data.usage.completion_tokens;
    }else if(modelObj.companyId == PROVIDERS.CLAUDE_PROVIDER){
        inputTokens += response.usage.input_tokens;
        outputTokens += response.usage.output_tokens;
    }else if(modelObj.companyId == PROVIDERS.GROQ_PROVIDER || modelObj == PROVIDERS.FIREWORKS_PROVIDER){
        inputTokens += response.usage.prompt_tokens;
        outputTokens += response.usage.completion_tokens;
    } else if(modelObj.companyId == PROVIDERS.GOOGLE_GEMINI){
        inputTokens += response.usageMetadata.promptTokenCount;
        outputTokens += response.usageMetadata.candidatesTokenCount;
    } else {
        
    }

    const messages_as_string = messages.map(item=>`${item.role}:${item.content}`).join('');
    let requestDetails_aiHistory_sheet: RequestDetails_aiHistory = {
        query: messages_as_string,
        totalTokens_req: inputTokens
    };
    let responseDetails_aiHistory_sheet: ResponseDetail_aiHistory = {
        actionType: action.providerName || action,
        kind: action.activity || action,
        totalTokens_res: outputTokens,
        eventData: {}
    };

    const totalCost_token = await calculateCost(modelObj, inputTokens, outputTokens);

    const actions:Action = {
        eventId: action_eventId,
        aiProvider: aiProvider_aiHistory_sheet,
        request: requestDetails_aiHistory_sheet,
        response: responseDetails_aiHistory_sheet,
        cost: totalCost_token
    }

    return actions;
}

export async function structure_aiHistory_action_optimize(agentData, modelObj, response, messages, credid, action_eventId): Promise<Action>{
     let aiProvider_aiHistory_sheet: AIProvider_aiHistory = {
       accountId: agentData.aiProvider ? agentData.aiProvider.accountId : '',
       credentialId: agentData.aiProvider ? credid : '',
       modelType: modelObj.model_name,
       providerName: agentData.aiProvider
         ? agentData.aiProvider.companyId
         : CUSTOM_LLM.PROVIDERS,
     };


    let inputTokens = 0, outputTokens = 0;
    if(modelObj.companyId == PROVIDERS.OPENAI_PROVIDER){
        inputTokens += response.data.usage.prompt_tokens;
        outputTokens += response.data.usage.completion_tokens;
    }else if(modelObj.companyId == PROVIDERS.CLAUDE_PROVIDER){
        inputTokens += response.usage.input_tokens;
        outputTokens += response.usage.output_tokens;
    }else if (
      modelObj.companyId == PROVIDERS.GROQ_PROVIDER ||
      modelObj == PROVIDERS.FIREWORKS_PROVIDER
    ) {
      inputTokens += response.usage.prompt_tokens;
      outputTokens += response.usage.completion_tokens;
    }

    const messages_as_string = messages.map(item=>`${item.role}:${item.content}`).join('');
    let requestDetails_aiHistory_sheet: RequestDetails_aiHistory = {
        query: messages_as_string,
        totalTokens_req: inputTokens
    };
    let responseDetails_aiHistory_sheet: ResponseDetail_aiHistory = {
        actionType: "AllAction",
        kind: "optimize",
        totalTokens_res: outputTokens,
        eventData: {}
    };

    const totalCost_token = await calculateCost(response, inputTokens, outputTokens);

    const actions:Action = {
        eventId: action_eventId,
        aiProvider: aiProvider_aiHistory_sheet,
        request: requestDetails_aiHistory_sheet,
        response: responseDetails_aiHistory_sheet,
        cost: totalCost_token
    }

    return actions;
}

export async function structure_aiHistory_message(agentData, modelObj, response, messages, credid, action_eventId): Promise<Action>{
   let aiProvider_aiHistory_sheet: AIProvider_aiHistory = {
     accountId: agentData.aiProvider ? agentData.aiProvider.accountId : '',
     credentialId: agentData.aiProvider ? credid : '',
     modelType: modelObj.model_name,
     providerName: agentData.aiProvider
       ? agentData.aiProvider.companyId
       : CUSTOM_LLM.PROVIDERS,
   };


    const messages_as_string = messages.map(item=>`${item.role}:${item.content}`).join('');
    let requestDetails_aiHistory_sheet: RequestDetails_aiHistory = {
        query: messages_as_string,
        totalTokens_req: 0
    };
    let responseDetails_aiHistory_sheet: ResponseDetail_aiHistory = {
        actionType: 'message',
        kind: 'message',
        totalTokens_res: 0,
        eventData: {}
    };

    const actions:Action = {
        eventId: action_eventId,
        aiProvider: aiProvider_aiHistory_sheet,
        request: requestDetails_aiHistory_sheet,
        response: responseDetails_aiHistory_sheet,
        cost: 0
    }

    return actions;
}

export async function structure_finalAiHistory(contactId, channelType, locationId, complete_action_aiHistory, agentId, orgid){
    const channel_aiHistory_Details:Channel_aiHistory = {
        channelAccountId:contactId,
        channelName:channelType,
        resourceId:locationId
      }
      const completeAiHistory:AiHistoryRecordDto = {
        agentId: agentId,
        orgId: orgid,
        createdAt: new Date(),
        updatedAt: new Date(),
        channel: channel_aiHistory_Details,
        actions: complete_action_aiHistory
      }
      try {
        const {totalCost, totalTokens} = await calculateTotalCost(complete_action_aiHistory);
        const rebillingUsage = {
            numOfTokensUsed: totalTokens,
            cost: totalCost
        }
        return {rebillingUsage, completeAiHistory};
        // await this.rebillingService.createRebillingUsageByLocationId(locationId, rebillingUsage);
        // const aiHistory = await this.mongoAiHistoryService.createAiHistory(completeAiHistory);
        // 
      } catch (error) {
        
        // await this.errorMe("Error getting rebilling details: ", error, contactId);
      }
}

export async function removePunctuation(response): Promise<string>{
  // Replace punctuation with an empty string
  var stringWithoutPunctuation = response.replace(/[^\w\s]/g, '');
  // Replace newlines and extra spaces with a single space
  stringWithoutPunctuation = stringWithoutPunctuation
    .replace(/\s+/g, ' ')
    .trim();
  return stringWithoutPunctuation;
}