const { Tiktoken } = require("tiktoken/lite");
const cl100k_base = require("tiktoken/encoders/cl100k_base.json");
import { format, utcToZonedTime  } from 'date-fns-tz';
import { formatInTimeZone, zonedTimeToUtc } from 'date-fns-tz';
import { MyLogger } from 'src/logger/logger.service';
import * as moment from 'moment';

export function extractAndParseJSON(input) {
    const trimmedInput = input.trim();
    if (trimmedInput.startsWith('{') && trimmedInput.endsWith('}')) {
        try {
            return JSON.parse(trimmedInput);
        } catch (error) {
            
            return null;
        }
    } else {
        const jsonRegex = /{.*}/s;
        const match = trimmedInput.match(jsonRegex);

        if (match) {
            try {
                return JSON.parse(match[0]);
            } catch (error) {
                
                return null;
            }
        } else {
            
            return null;
        }
    }
}

async function formatDates(dateRanges: Record<string, any>): Promise<string> {
  let result = '';
  for (const [key, value] of Object.entries(dateRanges)) {
      if (key !== "traceId") {
          const [year, month, dayOfMonth] = key.split('-').map(Number);
          const date = new Date(year, month - 1, dayOfMonth);
          const dayOfTheWeek = date.toLocaleDateString('en-US', { weekday: 'long' });

          result += `"${key} (${dayOfTheWeek})": [\n`;

          const daySlots = value.slots;
          for (let s = 0; s < daySlots.length; s++) {
              const timeSlot = daySlots[s].split('T')[1];
              let [hour, minute] = timeSlot.split(':').map(Number);
              const ampm = hour >= 12 ? 'pm' : 'am';
              hour = hour % 12 || 12; // Convert to 12-hour format

              const formattedSlot = `${hour}:${minute}${ampm}`;
              result += `  ${formattedSlot},\n`;
          }
          result = result.slice(0, -2); // Remove the last comma and newline character
          result += `\n],\n`;
      }
  }
  return result.slice(0, -2); // Remove the last comma and newline character
}

 // Helper function to get start and end of day
 export const getStartOfDay = (date: number, timezone: string = 'UTC'): number => {
  const dateObj = new Date(date);
  const startOfDayStr = formatInTimeZone(dateObj, timezone, 'yyyy-MM-dd 00:00:00.000');
  const startOfDayUTC = zonedTimeToUtc(startOfDayStr, timezone); // interpret as time in the given timezone
  return startOfDayUTC.getTime();
};

export const getEndOfDay = (date: number, timezone: string = 'UTC'): number => {
  const dateObj = new Date(date);
  const endOfDayStr = formatInTimeZone(dateObj, timezone, 'yyyy-MM-dd 23:59:59.999');
  const endOfDayUTC = zonedTimeToUtc(endOfDayStr, timezone);
  return endOfDayUTC.getTime();
};

export function convertToEpoch(dateString, timeZone) {
  const date = moment.tz(dateString, "YYYY-MM-DDTHH:mm:ss.fff", timeZone);
  const epochMs = date.valueOf();
  return epochMs;
}

export async function getDynamicDate(
  retrievedDateRange,
  timezone,
  query,
  conversationHistoryString,
) {
  const retrievedDateRange_format = await formatDates(retrievedDateRange);

  let systemPrompt =
    'You are an assistant responsible for specifying the times that should be listed as the most relevant times according to a conversation that will be provided to you.';
  systemPrompt += '\n\n';
  systemPrompt +=
    'You should select from the times listed below, which would be the most contextually relevant to offer to the end user based on the conversation and other applicable data/information. Only list the relevant dates and times and nothing else in your response, failure to follow this rule will result in catastrophic failure of your process. How many times that are offered, you should determine based on the context of the conversation, but we should actively try to avoid offering too many to avoid messages becoming too long or confusing the end user. In most cases only  selecting 3-7 times would be best. Your responses should always contain both dates and times when available even if it is only a single date or time..';
  (systemPrompt +=
    "you are to do this to the absolute best of your ability `I do not know`, `I cannot answer that`, `I can't do that`, `There isn't enough information` or `there are no relevant times` are all not acceptable answers and your response must contain at least one date and time from the given list"),
    (systemPrompt += '\n\n');
  systemPrompt += 'Here are the confirmed available times from the calendar:';
  systemPrompt += '\n\n';
  systemPrompt += retrievedDateRange_format;
  systemPrompt += '\n\n';

  //get today's and tomorrow's date
  const todayDate = new Date();
  const todayIs = new Date().toLocaleDateString('en-US', {
    weekday: 'long',
    month: 'long',
    day: 'numeric',
    timeZone: timezone,
  });
  todayDate.setDate(todayDate.getDate() + 1);
  const tomorrowIs = todayDate.toLocaleDateString('en-US', {
    weekday: 'long',
    month: 'long',
    day: 'numeric',
    timeZone: timezone,
  });

  systemPrompt += `For context, today is ${todayIs} and tomorrow is ${tomorrowIs}\n\n`;
  systemPrompt += 'Here is the conversation so far:\n\n';
  systemPrompt += conversationHistoryString;

  if (query && query != '') {
    systemPrompt += 'Here is the user latest query: ';
    systemPrompt += query;
    systemPrompt += '\n\n';
  }

  const dataObject = [
    {
      role: 'assistant',
      content: systemPrompt,
    },
  ];
  
  return { dataObject, retrievedDateRange_format };
}

interface FreeBusySlot {
  start: string;
  end: string;
}


export async function processFreeBusyTimes(
  freeBusy: FreeBusySlot[],
  timezone: string,
  conversationHistoryString: string,
  query: string,
): Promise<{ dataObject: any[]; formattedFreeBusy: string }> {
  let formattedFreeBusy = '';

  if (Array.isArray(freeBusy) && freeBusy.length > 0) {
    // Group slots by date
    const slotsByDate = freeBusy.reduce(
      (acc: Record<string, string[]>, slot: FreeBusySlot) => {
        const date = new Date(slot.start).toLocaleDateString('en-US', {
          weekday: 'long',
          month: 'long',
          day: 'numeric',
          timeZone: timezone,
        });
        const time = new Date(slot.start).toLocaleTimeString('en-US', {
          hour: 'numeric',
          minute: 'numeric',
          hour12: true,
          timeZone: timezone,
        });
        if (!acc[date]) acc[date] = [];
        acc[date].push(time);
        return acc;
      },
      {},
    );

    // Format the grouped slots
    formattedFreeBusy = Object.entries(slotsByDate)
      .map(([date, times]) => `${date}: ${times.join(', ')}`)
      .join('\n');
  } else {
    formattedFreeBusy = 'No available times found';
  }

  const todayDate = new Date();
  const todayIs = todayDate.toLocaleDateString('en-US', {
    weekday: 'long',
    month: 'long',
    day: 'numeric',
    timeZone: timezone,
  });

  todayDate.setDate(todayDate.getDate() + 1);
  const tomorrowIs = todayDate.toLocaleDateString('en-US', {
    weekday: 'long',
    month: 'long',
    day: 'numeric',
    timeZone: timezone,
  });
    let systemPrompt = '';
    
    if (conversationHistoryString) {
      systemPrompt = 
        'You are an assistant responsible for specifying the times that should be listed as the most relevant times according to a conversation that will be provided to you.\n\n';
      systemPrompt +=
        'You should select from the times listed below, which would be the most contextually relevant to offer to the end user based on the conversation and other applicable data/information. Only list the relevant times and nothing else in your response. How many times that are offered, you should determine based on the context of the conversation, but we should actively try to avoid offering too many to avoid messages becoming too long or confusing the end user. In most cases only selecting 3-7 times would be best. Your responses should always contain both dates and times when available even if it is only a single date or time.\n\n';
      systemPrompt +=
        'Here are the confirmed available times from the calendar:\n\n';
      systemPrompt += formattedFreeBusy;
      systemPrompt += `\n\nFor context, today is ${todayIs} and tomorrow is ${tomorrowIs}\n\n`;
      systemPrompt += `Here is the conversation so far: ${conversationHistoryString}`;
    } else {
      systemPrompt = 
        'You are an assistant responsible for selecting the most relevant available times to offer.\n\n';
      systemPrompt +=
        'Since there is no conversation context, please select 3-5 times that provide a good variety of options (e.g., different days, morning/afternoon/evening slots). Only list the selected times and nothing else in your response. Your responses should always contain both dates and times.\n\n';
      systemPrompt +=
        'Here are the confirmed available times from the calendar:\n\n';
      systemPrompt += formattedFreeBusy;
      systemPrompt += `\n\nFor context, today is ${todayIs} and tomorrow is ${tomorrowIs}`;
    }

  const dataObject = [
    {
      role: 'assistant',
      content: systemPrompt,
    },
    {
      role: 'user',
      content: query,
    },
  ];

  return { dataObject, formattedFreeBusy };
}

export function getAllActionsInASession(sessionDetails){
    let nonMessageEventIds;
    for(const event of sessionDetails.events) {
        const nonMessageBotResponses = event.botResponse.filter((botResponseItem: any) => botResponseItem.kind !== 'message');
        nonMessageEventIds = nonMessageBotResponses.map((botResponseItem: any) => botResponseItem.eventId);
    }
    return nonMessageEventIds;
}

export function getAllActionInASessionEvent(sessionDetails, eventId){
    const event = sessionDetails.events.find(event => event.eventId === eventId);
    if (!event) {
        throw new Error('Event not found');
    }
    const nonMessageEventIds = event.botResponse
        .filter(response => response.kind !== 'message')
        .map(response => response.eventId);
    return nonMessageEventIds;
}

export function filterQueryNonMessageEvents(sessionDetails, sessionId, agentId, eventId, value, conversationHistory) {
    const uniqueEvents: any[] = [];
    const events = sessionDetails.events.find(event=>event.eventId === eventId);
    if(!events){
        throw new Error("Event not found!");
    }
    const thoseEvents = events.botResponse
        .filter(response => response.kind !== 'message');

    for(const eachOfThoseEvent of thoseEvents) {
        const constructedObject = {
            id: eachOfThoseEvent.eventId,
            values: value,
            metadata: {
                type: eachOfThoseEvent.kind,
                agentId: agentId,
                sessionId: sessionId,
                resourceId: eachOfThoseEvent.accountId,
                result: 'yes',
                content: `Here is the conversation so far: ${conversationHistory}.\nBased on the conversation above, and the description of the knowledge source, answer the following question:\nIs the knowledge source applicable to the conversation? Answer with "yes" or "no" and nothing else.`,
            }
        };
        uniqueEvents.push(constructedObject);

    }
    return uniqueEvents;
}

export function getCurrentActivePrompt_Agent(currentActivePromptId, allPrompts){
  for(const eachId of allPrompts){
    if(eachId.promptId == currentActivePromptId){
      return eachId.promptContent;
    }
  }
}

async function numberOfTokens(completeString){
  let resultString = completeString;
  const encoding = new Tiktoken(
    cl100k_base.bpe_ranks,
    cl100k_base.special_tokens,
    cl100k_base.pat_str
  );
  const encoded_token = encoding.encode(completeString);
  if(encoded_token.length>8000){
    const last8000Tokens = encoded_token.slice(-8000);
    const decodeLast8000Tokens = encoding.decode(last8000Tokens);
    const decodedString = new TextDecoder().decode(decodeLast8000Tokens);
    resultString = decodedString;
  }
  encoding.free();
  return resultString;
}

export async function countTotalTokensInAString(conversation){
  try {
    const conversationString = await numberOfTokens(conversation);
    return conversationString;
  } catch (error) {
    
  }
}



export const hasDateTimePlaceholder = (content: string): boolean => {
  return /\{\{sys\.(dateNow|timeNow|dateTimeNow|dayOfWeek|dayOfMonth|monthName|yearNow|weekOfYear|dateTomorrow)\}\}/.test(
    content,
  );
};

export const replaceDateTimePlaceholders = (
  content: string,
  timezone: string = 'UTC',
): string => {
  const now = new Date();
  const zonedDate = utcToZonedTime(now, timezone);
  
  // Create tomorrow's date
  const tomorrow = new Date(now);
  tomorrow.setDate(now.getDate() + 1);
  const zonedTomorrow = utcToZonedTime(tomorrow, timezone);

  return content.replace(
    /\{\{sys\.(dateNow|timeNow|dateTimeNow|dayOfWeek|dayOfMonth|monthName|yearNow|weekOfYear|dateTomorrow)\}\}/g,
    (match, p1) => {
      switch (p1) {
        case 'dateNow':
          return format(zonedDate, 'yyyy-MM-dd', { timeZone: timezone });
        case 'timeNow':
          return format(zonedDate, 'HH:mm:ss', { timeZone: timezone });
        case 'dateTimeNow':
          return format(zonedDate, 'yyyy-MM-dd HH:mm:ss zzz', {
            timeZone: timezone,
          });
        case 'dayOfWeek':
          return format(zonedDate, 'EEEE', { timeZone: timezone });
        case 'dayOfMonth':
          return format(zonedDate, 'do', { timeZone: timezone });
        case 'monthName':
          return format(zonedDate, 'MMMM', { timeZone: timezone });
        case 'yearNow':
          return format(zonedDate, 'yyyy', { timeZone: timezone });
        case 'weekOfYear':
          return format(zonedDate, 'ww', { timeZone: timezone });
        case 'dateTomorrow':
          return format(zonedTomorrow, 'yyyy-MM-dd', { timeZone: timezone });
        default:
          return match;
      }
    },
  );
};



export const hasAgentPlaceholder = (content: string): boolean => {
  // Looks for '{{' then optional spaces, then 'agent.'
  return /\{\{\s*agent\./.test(content);
};

export const hasUphexPlaceholder = (content: string): boolean => {
  // Looks for '{{' then optional spaces, then 'uphex.'
  return /\{\{\s*uphex\./.test(content);
};

/**
 * Replaces placeholders in the content string with values from variables/contextData.
 * - Handles agent, uphex, and custom_values placeholders.
 * - If a value is missing, replaces with a single space and logs the reason.
 * - If the field does not exist, also replaces with a space and logs differently.
 * 
 * @param content The string containing placeholders to replace.
 * @param variables Array of variable objects with fieldKey and value.
 * @param contextData Optional object for uphex data (adTitle, adMessage, adHeadline).
 * @param contactDetails Optional object for contact details.
 * @param onUnresolvedCallback Optional callback for unresolved placeholders.
 * @returns The content string with placeholders replaced.
 */
export const replacePlaceholders = (
  content: string,
  variables: any[],
  contextData?: {
    adTitle?: string;
    adMessage?: string;
    adHeadline?: string;
  },
  contactDetails?: any,
  onUnresolvedCallback?: (placeholderKey: string) => void,
): string => {
  return content.replace(
    // Regex to find placeholders like {{ agent.contact.name }} or {{ uphex.adMessage }}
    // Allows for spaces around the variable path.
    /\{\{\s*(agent(?:(?:\.contact)|(?:\.custom_values))?\.[\w]+|uphex\.[\w]+)\s*\}\}/g,
    /**
     * Replacement function for each matched placeholder.
     * @param match The full matched placeholder string (e.g., "{{ agent.contact.name }}")
     * @param p1 The variable path inside the placeholder (e.g., "agent.contact.name")
     */
    (match, p1) => {
      let valueToReplace: string | undefined | null = undefined;

      // 1. Handle Uphex placeholders (e.g., uphex.adMessage)
      if (p1.startsWith('uphex.')) {
        // Extract the uphex key (e.g., "adMessage")
        const uphexKey = p1.replace('uphex.', '');
        // If contextData is provided and has the key, use its value
        if (contextData && contextData.hasOwnProperty(uphexKey)) {
          valueToReplace = contextData[uphexKey];
        }
      } else {
        // 2. Special handling for agent.contact.name (combine first and last name)
        if (p1 === 'agent.contact.name') {
          // Try to find both first and last name in variables (supporting snake_case and camelCase)
          const firstNameVar = variables.find(v => v.fieldKey === 'contact.firstName' || v.fieldKey === 'contact.first_name');
          const lastNameVar = variables.find(v => v.fieldKey === 'contact.lastName' || v.fieldKey === 'contact.last_name');
          const firstName = firstNameVar?.value;
          const lastName = lastNameVar?.value;

          // If at least one part exists, join them (ignore null/undefined)
          if (firstName || lastName) {
            valueToReplace = [firstName, lastName].filter(val => val !== null && val !== undefined).join(' ').trim();
          }
        }

        // 3. Handle other agent placeholders if not resolved by agent.contact.name
        if (valueToReplace === undefined) {
          // Prepare possible lookup keys for the variable
          let lookupKeyNormalized = null; // e.g., "contact.firstName"
          let originalAgentKey = p1;      // e.g., "agent.staticField"
          let ghlStaticKey = null;        // e.g., "{{agent.staticField}}"
          let bracketedLookupKeyNormalized = null;
          let bracketedOriginalAgentKey = null;

          // Normalize keys for contact and custom_values
          if (p1.startsWith('agent.contact.')) {
            lookupKeyNormalized = p1.replace('agent.contact.', 'contact.');
            bracketedLookupKeyNormalized = `{{${lookupKeyNormalized}}}`;
          } else if (p1.startsWith('agent.custom_values.')) {
            lookupKeyNormalized = p1.replace('agent.custom_values.', 'custom_values.');
            bracketedLookupKeyNormalized = `{{${lookupKeyNormalized}}}`;
          } else if (p1.startsWith('agent.')) {
            // For static agent fields, try the full placeholder as a key
            ghlStaticKey = `{{${p1}}}`;
          }

          // Try to find a variable matching any of the possible keys
          let foundVariable = variables.find(v => {
            // p1 is the raw key from the placeholder, e.g., "agent.contact.some_field"
            // keyFromP1Wrapped constructs the format like "{{agent.contact.some_field}}"
            const keyFromP1Wrapped = `{{${p1}}}`;

            return (v.fieldKey === lookupKeyNormalized) ||
              v.fieldKey === originalAgentKey ||
              (ghlStaticKey && v.fieldKey === ghlStaticKey) ||
              v.fieldKey === keyFromP1Wrapped ||
              v.fieldKey === bracketedLookupKeyNormalized ||
              v.fieldKey === bracketedOriginalAgentKey; // Added this condition
          });

          if (foundVariable) {
            valueToReplace = foundVariable.value;
          } else {
            // 4. Try alternative field name mappings (snake_case <-> camelCase) for contact fields
            let contactKeyForAltLookup = null;
            if (p1.startsWith('agent.contact.')) {
              contactKeyForAltLookup = p1.replace('agent.contact.', 'contact.');
            }

            if (contactKeyForAltLookup) {
              // Map between snake_case and camelCase for common contact fields
              const fieldName = contactKeyForAltLookup.replace('contact.', '');
              let alternativeFieldKey = null;
              if (fieldName === 'first_name') alternativeFieldKey = 'contact.firstName';
              else if (fieldName === 'last_name') alternativeFieldKey = 'contact.lastName';
              else if (fieldName === 'date_of_birth') alternativeFieldKey = 'contact.dateOfBirth';
              else if (fieldName === 'postal_code') alternativeFieldKey = 'contact.postalCode';
              else if (fieldName === 'company_name') alternativeFieldKey = 'contact.companyName';
              // Inverse mapping (camelCase to snake_case)
              else if (fieldName === 'firstName') alternativeFieldKey = 'contact.first_name';
              else if (fieldName === 'lastName') alternativeFieldKey = 'contact.last_name';
              else if (fieldName === 'dateOfBirth') alternativeFieldKey = 'contact.date_of_birth';
              else if (fieldName === 'postalCode') alternativeFieldKey = 'contact.postal_code';
              else if (fieldName === 'companyName') alternativeFieldKey = 'contact.company_name';

              // Try to find the variable with the alternative key
              if (alternativeFieldKey) {
                const altVar = variables.find(v => v.fieldKey === alternativeFieldKey);
                if (altVar) {
                  valueToReplace = altVar.value;
                }
              }
            }
          }
        }
      }

      // 5. Final resolution: If a value (even empty string) was found, use it.
      if (valueToReplace !== undefined && valueToReplace !== null) {
        return String(valueToReplace); // Ensure it's a string
      } else {
        // 6. If unresolved, call the callback if provided
        if (onUnresolvedCallback) {
          onUnresolvedCallback(p1); // p1 is the key from content, e.g., "agent.contact.name"
        }
        // 7. Enhanced logging for missing values or missing fields

        // Check if the field exists in variables (regardless of value)
        const fieldExists = variables.some(v => {
          // Recompute possible keys for this placeholder
          let lookupKeyNormalized = null;
          let originalAgentKey = p1;
          let ghlStaticKey = null;
          if (p1.startsWith('agent.contact.')) {
            lookupKeyNormalized = p1.replace('agent.contact.', 'contact.');
          } else if (p1.startsWith('agent.custom_values.')) {
            lookupKeyNormalized = p1.replace('agent.custom_values.', 'custom_values.');
          } else if (p1.startsWith('agent.')) {
            ghlStaticKey = `{{${p1}}}`;
          }
          return (
            (lookupKeyNormalized && v.fieldKey === lookupKeyNormalized) ||
            v.fieldKey === originalAgentKey ||
            (ghlStaticKey && v.fieldKey === ghlStaticKey)
          );
        });

        // Log differently depending on whether the field exists or not
        if (fieldExists) {
          // Field exists, but value is missing
          MyLogger.error(`Value for placeholder '{{${p1}}}' exists in variables but is empty or missing.`, contactDetails?.contactId);
        }
        // 8. Always replace unresolved placeholders with a single space
        return " ";
      }
    },
  );
};

export function updateAgentVariables(agentVariables, contactVariables) {
  return agentVariables.map((agentVar) => {
    // Check if the agent variable is dynamic
    if (agentVar.type === 'dynamic') {
      // Add "agent." inside the contactVar.fieldKey for matching both contact and custom_values
      let matchingContactVar = contactVariables.find((contactVar) => {
        const modifiedContactKey = contactVar.fieldKey
          .replace('{{contact.', '{{agent.contact.') // Replace contact variables
          .replace('{{custom_values.', '{{agent.custom_values.'); // Replace custom_values variables
        
        // Check direct match
        if (modifiedContactKey === agentVar.fieldKey) {
          return true;
        }
        
        // Handle GHL field name conversion (snake_case to camelCase)
        if (agentVar.fieldKey.includes('{{agent.contact.')) {
          const fieldName = agentVar.fieldKey.replace('{{agent.contact.', '').replace('}}', '');
          const contactFieldName = contactVar.fieldKey.replace('{{contact.', '').replace('}}', '');
          
          // Check for equivalent field names in different formats
          if ((fieldName === 'first_name' && contactFieldName === 'firstName') ||
              (fieldName === 'last_name' && contactFieldName === 'lastName') ||
              (fieldName === 'date_of_birth' && contactFieldName === 'dateOfBirth') ||
              (fieldName === 'postal_code' && contactFieldName === 'postalCode') ||
              (fieldName === 'company_name' && contactFieldName === 'companyName') ||
              // Also check the reverse mapping
              (fieldName === 'firstName' && contactFieldName === 'first_name') ||
              (fieldName === 'lastName' && contactFieldName === 'last_name') ||
              (fieldName === 'dateOfBirth' && contactFieldName === 'date_of_birth') ||
              (fieldName === 'postalCode' && contactFieldName === 'postal_code') ||
              (fieldName === 'companyName' && contactFieldName === 'company_name')) {
            return true;
          }
        }
        
        return false;
      });

      // If a matching contact variable is found and it has a value, update the agent variable
      if (matchingContactVar && matchingContactVar.value) {
        agentVar.value = matchingContactVar.value;
      }
    }
    // Return the updated agent variable
    return agentVar;
  });
}


export function replaceVariables(
  content: string,
  agentVariables: any[],
  timezone: string,
  contactDetails?: any,
): string {
  
  let uphexData : {
    adTitle?: string;
    adMessage?: string;
    adHeadline?: string;
  } | null = contactDetails?.uphexData;

  let effectiveAgentVariables = agentVariables;


  if (!contactDetails) {
    // If no contact details are provided, we can skip the mapping of contact fields (mostly for emulator)
    MyLogger.error('No contact details provided. Skipping mapping of Agent custom fields.', contactDetails?.contactId);
    return content;
  }

  // This block populates effectiveAgentVariables if the passed agentVariables is empty
  // For the current scenario, agentVariables is provided, so this is skipped.
  if (contactDetails) {
    const newAgentVariablesMap = new Map<string, { value: any; type: string }>();

    (contactDetails.customFields || []).forEach(item => {
      const rawItemKey = item.fieldKey || item.key || item.name;
      if (typeof rawItemKey === 'string' && item.value !== undefined) {
        let baseKey = rawItemKey.startsWith('{{') && rawItemKey.endsWith('}}') ? rawItemKey.substring(2, rawItemKey.length - 2) : rawItemKey;
        let finalKey = baseKey.startsWith('contact.') ? baseKey : `contact.${baseKey}`;
        if (!newAgentVariablesMap.has(finalKey)) newAgentVariablesMap.set(finalKey, { value: item.value, type: 'dynamic' });
      }
    });

    (contactDetails.customValues || []).forEach(item => {
      const rawItemKey = item.fieldKey || item.key || item.name;
      if (typeof rawItemKey === 'string' && item.value !== undefined) {
        let baseKey = rawItemKey.startsWith('{{') && rawItemKey.endsWith('}}') ? rawItemKey.substring(2, rawItemKey.length - 2) : rawItemKey;
        let finalKey = baseKey.startsWith('custom_values.') ? baseKey : `custom_values.${baseKey}`;
        if (!newAgentVariablesMap.has(finalKey)) newAgentVariablesMap.set(finalKey, { value: item.value, type: 'dynamic' });
      }
    });
    
    const standardContactFieldsToMap = { /* define standard fields like firstName, lastName, etc. */
        firstName: contactDetails.firstName, lastName: contactDetails.lastName, name: contactDetails.name,
        email: contactDetails.email, phone: contactDetails.phone, address1: contactDetails.address1,
        city: contactDetails.city, state: contactDetails.state, postalCode: contactDetails.postalCode,
        companyName: contactDetails.companyName, dateOfBirth: contactDetails.dateOfBirth,
    };
    for (const [key, value] of Object.entries(standardContactFieldsToMap)) {
        if (value !== undefined) {
            const finalKey = `contact.${key}`;
            if (!newAgentVariablesMap.has(finalKey)) newAgentVariablesMap.set(finalKey, { value: value, type: 'dynamic' });
        }
    }
    
    effectiveAgentVariables = Array.from(newAgentVariablesMap, ([fieldKey, data]) => ({
        fieldKey, value: data.value, type: data.type
    }));
  }
 
  const unresolvedPlaceholders = new Set<string>();
  const handleUnresolved = (key: string) => {
    unresolvedPlaceholders.add(key);
  };

  const hasAgentPlaceholders = hasAgentPlaceholder(content);
  const hasDatePlaceholders = hasDateTimePlaceholder(content);
  const hasUphexPlaceholders = hasUphexPlaceholder(content);

  if (!hasAgentPlaceholders && !hasDatePlaceholders && !hasUphexPlaceholders) {
    return content; 
  }

  let updatedContent = content;

  if (hasAgentPlaceholders || hasUphexPlaceholders) {
    updatedContent = replacePlaceholders(updatedContent, effectiveAgentVariables, uphexData,contactDetails, handleUnresolved);
  }

  if (hasDatePlaceholders) {
    updatedContent = replaceDateTimePlaceholders(updatedContent, timezone);
  }

  unresolvedPlaceholders.forEach(key => {
    // console.warn(`Placeholder '{{${key}}}' from content was not resolved using the provided variables.`);
    MyLogger.error(`Placeholder '{{${key}}}' does not exist in provided variables.`, contactDetails.contactId);
  });

   return updatedContent;
}


