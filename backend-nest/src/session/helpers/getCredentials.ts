import { KINDS, PROVIDERS } from "src/lib/constant";
import { MyLogger } from "src/logger/logger.service";
import { MongoCredentialsService } from 'src/mongo/service/credentials/mongo-credentials.service';

export async function getCredentials(
  companyId: string,
  credentialId: string,
  mongoCredentialService: MongoCredentialsService,
  logger: MyLogger,
  sessionId?: string
): Promise<string> {
  // Handle custom LLM providers
  if ([PROVIDERS.OPENAI_HOSTED, PROVIDERS.FIREWORKS, PROVIDERS.OPENROUTER_PROVIDER, PROVIDERS.GROQ_HOSTED].includes(companyId)) {
    logger.log({
      message: `#********** Handling custom LLM provider: ${companyId}`,
      context: sessionId || 'getCredentials',
    });

    if (companyId === PROVIDERS.OPENAI_HOSTED) {
      return process.env.OPENAI_API_KEY;
    } else if (companyId === PROVIDERS.FIREWORKS) {
      return process.env.FIREWORKS_API_KEY;
    } else if (companyId === PROVIDERS.OPENROUTER_PROVIDER) {
      return process.env.OPENROUTER_API_KEY;
    } else if (companyId === PROVIDERS.GROQ_HOSTED) {
      return process.env.GROQ_API_KEY;
    }
  }

  // Handle standard providers
  let credentialData;
  switch (companyId) {
    case PROVIDERS.OPENAI_PROVIDER:
      credentialData = await mongoCredentialService.getCredential({
        _id: credentialId,
        kind: KINDS.OPENAI_CREDENTIAL,
      });
      break;
    case PROVIDERS.CLAUDE_PROVIDER:
      credentialData = await mongoCredentialService.getCredential({
        _id: credentialId,
        kind: KINDS.CLAUDE_CREDENTIAL,
      });
      break;
    case PROVIDERS.GROQ_PROVIDER:
      credentialData = await mongoCredentialService.getCredential({
        _id: credentialId,
        kind: KINDS.GROQ_CREDENTIAL,
      });
      break;
    case PROVIDERS.GOOGLE_GEMINI:
      credentialData = await mongoCredentialService.getCredential({
        _id: credentialId,
        kind: KINDS.GEMINI_CREDENTIAL,
      });
      break;
    default:
      throw new Error(`Unsupported AI Provider: ${companyId}`);
  }

  return credentialData['creds'].secret;
}