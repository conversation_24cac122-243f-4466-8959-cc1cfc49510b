import { PROVIDERS } from 'src/lib/constant';

export function getApiKeyAndProvider({
  companyId,
}: {
  companyId: string;
}) {
  let apiKey;
  let embeddingProvider;

  switch (companyId) {
    case PROVIDERS.OPENAI_PROVIDER:
      // This case should be handled by the caller since it needs secretKey
      throw new Error('OpenAI provider requires secretKey to be handled separately');

    case PROVIDERS.CLAUDE_PROVIDER:
    case PROVIDERS.GROQ_PROVIDER:
    case PROVIDERS.FIREWORKS_PROVIDER:
    case PROVIDERS.GOOGLE_GEMINI:
    case PROVIDERS.OPENROUTER_PROVIDER:
    case PROVIDERS.GROQ_HOSTED:
      apiKey = process.env.OPENAI_API_KEY;
      embeddingProvider = PROVIDERS.OPENAI_PROVIDER;
      break;
    
    case PROVIDERS.OPENAI_HOSTED:
      apiKey = process.env.OPENAI_API_KEY;
      embeddingProvider = PROVIDERS.OPENAI_PROVIDER;  
      break;

    default:
      throw new Error(`Unsupported provider: ${companyId}`);
  }

  return { apiKey, embeddingProvider };
}