import { MongoOrganizationService } from 'src/mongo/service/organization/mongo-organization.service';
import {
  PROVIDERS,
} from 'src/lib/constant';
import { AiModelsService } from 'src/ai-models/ai-models.service';

interface ProcessYesNoResponseParams {
  aiResponse: any;
  companyId: string;
  modelType?: string;
  orgid?: string;
  mongoOrganizationService?: MongoOrganizationService;
  usage_yesNoToken: number;
  aiModelsService: AiModelsService;
  agentId: string;
  location?: string;
}

interface ProcessYesNoResponseResult {
  finalAiResponse: string;
  usage_yesNoToken: number;
}

export async function processResponse({
  aiResponse,
  companyId,
  modelType,
  orgid,
  mongoOrganizationService,
  usage_yesNoToken,
  aiModelsService,
  agentId,
  location
}: ProcessYesNoResponseParams): Promise<ProcessYesNoResponseResult> {
  let finalAiResponse = '';

  if (companyId === PROVIDERS.OPENAI_PROVIDER) {
    finalAiResponse = aiResponse.data.choices[0].message.content;
    usage_yesNoToken += aiResponse.data.usage.total_tokens;
  } else if (companyId === PROVIDERS.OPENAI_HOSTED || companyId === PROVIDERS.OPENROUTER_PROVIDER) {
    finalAiResponse = aiResponse.data.choices[0].message.content;
    usage_yesNoToken += aiResponse.data.usage.total_tokens;
    let totalTokens = aiResponse.data.usage.total_tokens;

    if (mongoOrganizationService && orgid && modelType) {
        const model = await aiModelsService.findOne(undefined, companyId, modelType);
        await updateHostedAiUsage(
          mongoOrganizationService,
          orgid,
          agentId,
          location,
          totalTokens,
          model.costPerMillion,
          companyId,
          model.model
        );
    }
  } else if (companyId === PROVIDERS.CLAUDE_PROVIDER) {
    finalAiResponse = aiResponse.content[0].text;
    usage_yesNoToken +=
      aiResponse.usage.input_tokens +
      aiResponse.usage.output_tokens;
  } else if (companyId === PROVIDERS.GROQ_PROVIDER || companyId === PROVIDERS.GROQ_HOSTED) {
    finalAiResponse = aiResponse.choices[0].message.content;
    usage_yesNoToken += aiResponse.usage.total_tokens;
    if (companyId === PROVIDERS.GROQ_HOSTED) {
      let totalTokens = aiResponse.usage.total_tokens;
      if (mongoOrganizationService && orgid) {
        const model = await aiModelsService.findOne(undefined, companyId, modelType);
        await updateHostedAiUsage(
          mongoOrganizationService,
          orgid,
          agentId,
          location,
          totalTokens,
          model.costPerMillion,
          companyId,
          model.model
        );
      }
    }
  } else if (companyId === PROVIDERS.FIREWORKS_PROVIDER) {
    finalAiResponse = aiResponse.data.choices[0].message.content;
    usage_yesNoToken += aiResponse.data.usage.total_tokens;
    let totalTokens = aiResponse.data.usage.total_tokens;

    if (mongoOrganizationService && orgid) {
      const model = await aiModelsService.findOne(undefined, companyId, modelType);
      await updateHostedAiUsage(
        mongoOrganizationService,
        orgid,
        agentId,
        location,
        totalTokens,
        model.costPerMillion,
        companyId,
        model.model
      );
    }
  } else if (companyId === PROVIDERS.GOOGLE_GEMINI) {
    finalAiResponse = aiResponse.candidates[0].content.parts[0].text || '';
    usage_yesNoToken +=
      aiResponse.usageMetadata.candidatesTokenCount +
      aiResponse.usageMetadata.promptTokenCount;
  }

  return {
    finalAiResponse,
    usage_yesNoToken,
  };
}

async function updateHostedAiUsage(
  mongoOrganizationService: MongoOrganizationService,
  orgid: string,
  agentId: string,
  location: string,
  tokenCount: number,
  pricePerMillion: number,
  provider: string,
  model: string
) {
  const usageInMillions = tokenCount / 1000000;
  const cost = usageInMillions * pricePerMillion;

  location = location ?? "";

  await mongoOrganizationService.updateHostedAiUsage(orgid, agentId, location, {
    numOfTokensUsed: tokenCount,
    cost: cost,
    provider: provider,
    model: model,
  });
}