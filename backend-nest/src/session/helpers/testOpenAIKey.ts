import { Response } from 'express';
import { VectorService } from 'src/vector/services/vector.service';
import { MyLogger } from 'src/logger/logger.service';


export async function validateOpenAIKey(
  companyId: string,
  secretKey: string,
  sessionId: string,
  response: Response,
  logger: MyLogger,
  vectorService: VectorService
) {
  if (companyId === 'openai' || companyId === 'openai-hosted') {
    await logger.log({
      message: `TESTING ${companyId.toUpperCase()} KEY FOR ANY ERRORS`,
      context: sessionId,
    });

    let embeddings = [];

    try {
      embeddings = await vectorService.createOpenAIEmbedding(
        secretKey,
        'HELLO WORLD'
      );

      logger.log({
        message: `#564783218421 ${companyId.toUpperCase()} KEY IS VALID`,
        context: sessionId,
      });
    } catch (error) {
      logger.error({
        message: `Error testing ${companyId.toUpperCase()} key: ${error.response.data.error.message}`,
        context: sessionId,
      });
      const errorMessage = error.response.data.error.message;
      return response.status(error.response.status).json({
        message: errorMessage,
      });
    }
  }
}