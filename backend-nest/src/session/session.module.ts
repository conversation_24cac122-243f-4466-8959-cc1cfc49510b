import { Module, forwardRef } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import {
  Session,
  SessionSchema,
} from 'src/mongo/schemas/session/session.schema';
import { SessionController } from './session.controller';
import { SessionService } from './session.service';
import { MongoSessionService } from 'src/mongo/service/session/mongo-session.service';
import { AgentService } from 'src/agent/agent.service';
import { SessionListService } from './outreachService/session-list.service';
import { SheetService } from 'src/organization/services/integrations/sheet/sheet.service';
import { OrganizationService } from 'src/organization/services/organization.service';
import { BillingService } from 'src/billing/billing.service';
import { StripeService } from 'src/billing/stripe.service';
import { AgentModule } from 'src/agent/agent.module';
import { FallbackModule } from 'src/fallback/fallback.module';
import { AuthGuard } from 'src/auth/auth.guard';
import { UserService } from 'src/user/user.service';
import { OutreachModule } from './outreachService/outreach_module';
import { OpenaiModule } from 'src/ai_response/openai_service_module';
import { LlamaModule } from '../llama/llama.module';
import { AiHistoryModule } from 'src/ai_History_Record/aiHistory_module';
import { RebillingModule } from 'src/rebilling/rebilling.module';
import { RebillingService } from 'src/rebilling/rebilling.service';
import { LangchainReadModule } from 'src/langchain_read_action/langchain_read.module';
import { LangchainWriteModule } from 'src/langchain_write_action/langchain_write.module';
import { FoldersModule } from 'src/folders/folders.module';
import { GhlService } from '../organization/services/channels/ghl/ghl.service';
import { OrganizationModule } from 'src/organization/organization.module';
import { ApiClientModule } from 'src/api-client/api-client.module';
import { UtilityModule } from 'src/utility/utility.module';
import { ExternalsModule } from 'src/externals/externals.module';
import { ActionsService } from 'src/organization/services/actions/actions.service';
import { NotificationService } from 'src/notification/notification.service';
import { AiModelsService } from 'src/ai-models/ai-models.service';
import { AiModelsModule } from 'src/ai-models/ai-models.module';
import { AuditLogModule } from 'src/audit-log/audit-log.module';

@Module({
  imports: [
    MongooseModule.forFeature([{ name: Session.name, schema: SessionSchema }]),
    forwardRef(() => AgentModule),
    FallbackModule,
    OutreachModule,
    OpenaiModule,
    LlamaModule,
    AiHistoryModule,
    LangchainReadModule,
    LangchainWriteModule,
    FoldersModule,
    forwardRef(() => OrganizationModule),
    ApiClientModule,
    UtilityModule,
    ExternalsModule,
    AiModelsModule,
    AuditLogModule
  ],
  controllers: [SessionController],
  providers: [
    MongoSessionService,
    OrganizationService,
    SessionService,
    SessionListService,
    SheetService,
    BillingService,
    StripeService,
    UserService,
    RebillingService,
    ActionsService,
    NotificationService
  ],
  exports: [SessionService],
})
export class SessionModule {}
