import { Modu<PERSON> } from '@nestjs/common';
import { FoldersService } from './folders.service';
import { FoldersController } from './folders.controller';
import { MongooseModule } from '@nestjs/mongoose';
import {
  Folders,
  FoldersSchema,
} from 'src/mongo/schemas/folders/folders.schema';
import {
  Organization,
  OrganizationSchema,
} from 'src/mongo/schemas/organization/organization.schema';
import { Agent, agentSchema } from 'src/mongo/schemas/agents/agents.schema';

@Module({
  imports: [
    MongooseModule.forFeature([{ name: Folders.name, schema: FoldersSchema }]),
    MongooseModule.forFeature([
      { name: Organization.name, schema: OrganizationSchema },
    ]),
    MongooseModule.forFeature([{ name: Agent.name, schema: agentSchema }]),
  ],
  controllers: [FoldersController],
  providers: [FoldersService],
  exports: [
    FoldersService,
    MongooseModule.forFeature([{ name: Folders.name, schema: FoldersSchema }]),
    MongooseModule.forFeature([
      { name: Organization.name, schema: OrganizationSchema },
    ]),
    MongooseModule.forFeature([{ name: Agent.name, schema: agentSchema }]),
  ],
})
export class FoldersModule {}
