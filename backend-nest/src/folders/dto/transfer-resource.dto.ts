import {
  IsEnum,
  IsMongoId,
  IsNotEmpty,
  IsO<PERSON>al,
  IsString,
} from 'class-validator';
import { FolderType } from 'src/mongo/schemas/folders/folders.schema';

export class FileTransferDto {
  @IsString()
  @IsMongoId()
  @IsNotEmpty()
  resourceId: string;

  @IsString()
  @IsEnum(FolderType)
  @IsNotEmpty()
  resourceType: FolderType;

  @IsString()
  @IsMongoId()
  @IsNotEmpty()
  orgId: string;

  @IsString()
  @IsMongoId()
  @IsOptional()
  folderId: string;
}

export class FolderTransferDto {
  @IsString()
  @IsMongoId()
  @IsNotEmpty()
  sourceFolderId: string;

  @IsString()
  @IsMongoId()
  @IsNotEmpty()
  destinationFolderId: string;
}
