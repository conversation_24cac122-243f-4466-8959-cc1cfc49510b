import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Param,
  Body,
  Patch,
  Query,
} from '@nestjs/common';
import { FoldersService } from './folders.service';
import { Folders } from 'src/mongo/schemas/folders/folders.schema';
import {
  FileTransferDto,
  FolderTransferDto,
} from './dto/transfer-resource.dto';

@Controller('folders')
export class FoldersController {
  constructor(private readonly foldersService: FoldersService) {}

  @Post()
  create(@Body() createFolderDto: Folders) {
    return this.foldersService.create(createFolderDto);
  }

  @Get('/organization/:orgId')
  findAll(@Param('orgId') orgId: string, @Query('type') type: string) {
    return this.foldersService.findAll({ orgId, type });
  }

  @Get('/organization/:orgId/nested')
  findAllFoldersNested(
    @Param('orgId') orgId: string,
    @Query('type') type: string,
  ) {
    return this.foldersService.findAllFoldersNested(orgId, type);
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.foldersService.findByFolderId(id);
  }

  @Patch(':id')
  update(@Param('id') id: string, @Body() updateFolderDto: Partial<Folders>) {
    return this.foldersService.update(id, updateFolderDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.foldersService.remove(id);
  }

  @Post('file-transfer')
  transferFile(
    @Body()
    fileTransferDto: FileTransferDto,
  ) {
    return this.foldersService.transferFile(fileTransferDto);
  }

  @Post('folder-transfer')
  transferFolder(
    @Body()
    fileTransferDto: FolderTransferDto,
  ) {
    return this.foldersService.transferFolder(fileTransferDto);
  }

  @Post('start')
  startFolder(@Body() body: { orgId: string }) {
    return this.foldersService.startFolder(body.orgId);
  }

  @Post(':folderId/import')
  importFile(@Param('folderId') folderId: string, @Body() file: string) {
    // TODO: Implement the importFile method in the FoldersService
    return this.foldersService.importFile(folderId, file);
  }
  // @Get('switch/all')
  // switchAll() {
  //   return this.foldersService.switchAll();
  // }
}
