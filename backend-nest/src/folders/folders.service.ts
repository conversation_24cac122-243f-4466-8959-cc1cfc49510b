import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { CreateFolderDto } from './dto/create-folder.dto';
import { UpdateFolderDto } from './dto/update-folder.dto';
import {
  Folders,
  FoldersDocument,
} from 'src/mongo/schemas/folders/folders.schema';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import {
  Organization,
  OrganizationDocument,
} from 'src/mongo/schemas/organization/organization.schema';
import {
  FileTransferDto,
  FolderTransferDto,
} from './dto/transfer-resource.dto';
import { Agent, AgentDocument } from 'src/mongo/schemas/agents/agents.schema';

type AgentType = {
  _id: string;
  agentName: string;
  aiProvider: {
    companyId: string;
    accountId: string;
    accountName: string;
  };
  mainPrompt: string;
  actions: string[];
};
type Resource =
  | AgentType
  | {
      name: string;
      id: string;
    };

type Folder = {
  _id: string;
  name: string;
  parent: string | null;
  children?: Folder[];
};

@Injectable()
export class FoldersService {
  constructor(
    @InjectModel(Folders.name)
    private readonly foldersModel: Model<FoldersDocument>,

    @InjectModel(Organization.name)
    private readonly organizationModel: Model<OrganizationDocument>,

    @InjectModel(Agent.name)
    private readonly agentModel: Model<AgentDocument>,
  ) {}

  async create(createFolderDto: Folders) {
    let parent: string = createFolderDto.parent?.toString();
    let org: string = createFolderDto.organization?.toString();
    if (!!!createFolderDto.parent && !!!createFolderDto.organization) {
      throw new BadRequestException('Parent or organization is required');
    }
    if (!!!createFolderDto.parent) {
      //get the folderId of the organization root folder of same type
      const folder = await this.foldersModel
        .findOne({
          organization: createFolderDto.organization,
          type: createFolderDto.type,
          parent: null,
        })
        .exec();
      parent = folder._id?.toString();
    }
    if (!createFolderDto.organization) {
      const { organization } = await this.foldersModel
        .findById(createFolderDto.parent)
        .exec();
      org = organization?.toString();
    }
    return this.foldersModel.create({
      ...createFolderDto,
      organization: org,
      parent,
    });
  }

  async findAll({ orgId, type }: { orgId: string; type: string }) {
    //Return the top layer of that type
    const folder = await this.foldersModel
      .findOne({ organization: orgId, type, parent: null })
      .exec();
    if (!!!folder) {
      throw new NotFoundException('Folder not found');
    }
    const { resourceIds } = folder;
    const folderContent = this.findResources({
      type,
      resourceIds,
    });
    const childrenFolders = this.findChildrenFolders(folder._id?.toString());
    const [resources, folders] = await Promise.all([
      folderContent,
      childrenFolders,
    ]);
    return {
      resources,
      folders,
    };
  }

  async findByFolderId(id: string) {
    const folder = await this.foldersModel.findById(id).exec();
    if (!!!folder) {
      throw new NotFoundException('Folder not found');
    }
    const { type, resourceIds, organization } = folder;
    const allResources = this.findResources({
      type,
      resourceIds,
      orgId: organization?.toString(),
    });
    const findChildrenFolders = this.findChildrenFolders(id);
    const [resources, folders] = await Promise.all([
      allResources,
      findChildrenFolders,
    ]);
    return {
      resources,
      folders,
    };
  }

  async findChildrenFolders(parentFolderId: string) {
    const folders = await this.foldersModel
      .find(
        { parent: parentFolderId },
        {
          name: 1,
          createdAt: 1,
          updatedAt: 1,
        },
      )
      .exec();
    return folders;
  }

  async findResources({
    type,
    resourceIds,
    orgId,
  }: {
    type: string;
    resourceIds: string[];
    orgId?: string;
  }) {
    let resources: Resource[] = [];
    if (type === 'agents') {
      const resourcePromises = resourceIds.map(async (resourceId) => {
        let resource: any;
        resource = await this.agentModel
          .findById(resourceId, {
            agentName: 1,
            aiProvider: 1,
            prompts: 1,
            actions: 1,
          })
          .exec();

        return {
          _id: resource?._id,
          agentName: resource?.agentName,
          aiProvider: resource?.aiProvider,
          mainPrompt: resource?.prompts?.prompt?.find(
            (prompt) => prompt.promptId === resource?.prompts?.currentActive,
          )?.name,
          actions: resource?.actions?.map((action) => action.providerName),
        };
      });
      resources = await Promise.all(resourcePromises);
    } else if (type === 'integrations') {
      const { organization } = await this.foldersModel.findById(orgId).exec();
      const org = await this.organizationModel
        .findById(organization, {
          'connections.dataSources': 1,
        })
        .exec();
      resources = org.connections.dataSources.map((dataSource) => ({
        name: dataSource?.name,
        id: dataSource?.accountId,
      }));
    } else if (type === 'channels') {
      const { organization } = await this.foldersModel.findById(orgId).exec();
      const org = await this.organizationModel
        .findById(organization, {
          'connections.channels': 1,
        })
        .exec();
      resources = org.connections.channels.map((channel) => ({
        name: channel?.name,
        id: channel?.accountId,
      }));
    } else if (type === 'staticData') {
      const { organization } = await this.foldersModel.findById(orgId).exec();
      const org = await this.organizationModel
        .findById(organization, {
          'connections.staticData': 1,
        })
        .exec();
      resources = org.connections.dataSources.map((staticData) => ({
        name: staticData?.name,
        id: staticData?.accountId,
      }));
    }

    return resources;
  }

  update(id: string, updateFolderDto: Partial<Folders>) {
    return this.foldersModel
      .findByIdAndUpdate(id, updateFolderDto, { new: true })
      .exec();
  }

  async remove(id: string) {
    if (!!!id) {
      throw new BadRequestException('Folder Id is required');
    }
    const [parentFolder, folderResources] = await Promise.all([
      this.foldersModel.findOne({ parent: id }).exec(),
      this.foldersModel.findById(id, { resourceIds: 1, parent: 1 }).exec(),
    ]);

    if (!!!folderResources.parent) {
      
      throw new BadRequestException('The root folder cannot be deleted');
    }

    if (parentFolder) {
      throw new BadRequestException(
        'The current folder is a parent of another folder',
      );
    }

    if (folderResources && folderResources.resourceIds.length > 0) {
      throw new BadRequestException('The current folder has resources');
    }
    const folderDeleted = await this.foldersModel.findByIdAndDelete(id).exec();
    return {
      newParent: folderDeleted.parent,
    };
  }

  async startFolder(orgId: string) {
    const org = await this.organizationModel.findById(orgId).exec();
    if (!!!org) {
      throw new NotFoundException('Organization not found');
    }
    const agents = org.agents;
    // const integrations = org.connections.dataSources.map(
    //   (connection) => connection.accountId,
    // );
    // const channels = org.connections.channels.map(
    //   (connection) => connection.accountId,
    // );
    // const staticData = org.connections.staticData.map(
    //   (connection) => connection.accountId,
    // );

    const checkIfThisScriptAlreadyRan = await this.foldersModel
      .findOne({ organization: orgId })
      .exec();

    if (checkIfThisScriptAlreadyRan) {
      throw new BadRequestException('This script already ran');
    }

    const agentsFolder = await this.foldersModel.create({
      organization: orgId,
      name: 'root',
      type: 'agents',
      resourceIds: agents,
    });
    // const integrationsFolder = await this.foldersModel.create({
    //   organization: orgId,
    //   name: 'root',
    //   type: 'integrations',
    //   resourceIds: integrations,
    // });
    // const channelsFolder = await this.foldersModel.create({
    //   organization: orgId,
    //   name: 'root',
    //   type: 'channels',
    //   resourceIds: channels,
    // });
    // const staticDataFolder = await this.foldersModel.create({
    //   organization: orgId,
    //   name: 'root',
    //   type: 'staticData',
    //   resourceIds: staticData,
    // });
    return {
      agentsFolder,
      // integrationsFolder,
      // channelsFolder,
      // staticDataFolder,
    };
  }

  async transferFile(fileTransferDto: FileTransferDto) {
    const {
      folderId: folderIdFromPayload,
      orgId,
      resourceId,
      resourceType,
    } = fileTransferDto;
    let folderId = folderIdFromPayload;
    const checkIfResourceExists = this.checkResourceExist(
      orgId,
      resourceId,
      resourceType,
    );

    if (!!!checkIfResourceExists) {
      throw new NotFoundException('Resource not found');
    }

    // Start a new session and transaction
    const session = await this.foldersModel.startSession();
    session.startTransaction();

    if (!folderId) {
      // Find the root folder of the org
      const folderResource = await this.foldersModel.findOne({
        organization: orgId,
        parent: null,
      });
      folderId = folderResource._id?.toString();
    }

    try {
      // Transfer to the new folder and remove from the old folder
      await this.foldersModel
        .findOneAndUpdate(
          { resourceIds: resourceId },
          { $pull: { resourceIds: resourceId } },
          { session },
        )
        .exec();

      await this.foldersModel
        .findByIdAndUpdate(
          folderId,
          { $push: { resourceIds: resourceId } },
          { session },
        )
        .exec();
      const resource = await this.agentModel.findById(resourceId).exec();
      // If all operations were successful, commit the transaction
      await session.commitTransaction();
      return {
        _id: resource?._id,
        agentName: resource?.agentName,
        aiProvider: resource?.aiProvider,
        mainPrompt: resource?.prompts?.prompt?.find(
          (prompt) => prompt.promptId === resource?.prompts?.currentActive,
        )?.name,
        actions: resource?.actions?.map((action) => action.providerName),
      };
    } catch (error) {
      // If any operation failed, abort the transaction
      await session.abortTransaction();
      throw error;
    } finally {
      // End the session
      session.endSession();
    }
  }

  async transferFolder(fileTransferDto: FolderTransferDto) {
    const { sourceFolderId, destinationFolderId } = fileTransferDto;
    let destinationFolder = destinationFolderId;
    if (!!!destinationFolderId) {
      //transfer to the root folder of the organization
      const { organization, type } = await this.foldersModel
        .findById(sourceFolderId)
        .exec();
      const rootFolder = await this.foldersModel
        .findOne({ organization, type, parent: null })
        .exec();
      if (!!!rootFolder) {
        throw new NotFoundException('Root folder not found');
      }
      destinationFolder = rootFolder._id?.toString();
    }
    if (sourceFolderId === destinationFolderId) {
      throw new BadRequestException(
        'Source folder and destination folder cannot be the same',
      );
    }
    //change the parent of the sourceFolderId folder to that of the  destinationFolderId
    const sourceFolder = await this.foldersModel
      .findByIdAndUpdate(sourceFolderId, { parent: destinationFolder })
      .exec();

    return {
      sourceFolder,
    };
  }

  async checkResourceExist(
    orgId: string,
    resourceId: string,
    resourceType: string,
  ): Promise<boolean> {
    if (resourceType === 'agents') {
      const checkIfResourceExists = await this.organizationModel
        .findOne({ _id: orgId, agents: resourceId })
        .exec();
      if (checkIfResourceExists) {
        return true;
      }
    } else if (resourceType === 'integrations') {
      const checkIfResourceExists = await this.organizationModel
        .findOne({ _id: orgId, 'connections.dataSources': resourceId })
        .exec();
      if (checkIfResourceExists) {
        return true;
      }
    } else if (resourceType === 'channels') {
      const checkIfResourceExists = await this.organizationModel
        .findOne({ _id: orgId, 'connections.channels': resourceId })
        .exec();
      if (checkIfResourceExists) {
        return true;
      }
    } else if (resourceType === 'staticData') {
      const checkIfResourceExists = await this.organizationModel
        .findOne({ _id: orgId, 'connections.staticData': resourceId })
        .exec();
      if (checkIfResourceExists) {
        return true;
      }
    }
  }

  async addResourceToFolder({
    organization,
    folderId,
    resourceId,
    type,
  }: {
    organization?: string;
    folderId?: string;
    resourceId: string;
    type: string;
  }) {
    let folder: string = folderId;
    if (!!!resourceId) {
      throw new BadRequestException('Resource Id is required');
    } else if (!!!folderId) {
      //get the folderId of the organization root folder of same type
      const folderObj = await this.foldersModel
        .findOne({
          organization,
          type,
          parent: null,
        })
        .exec();
      folder = folderObj._id?.toString();
    }
    return this.foldersModel
      .findByIdAndUpdate(folder, {
        $push: { resourceIds: resourceId },
      })
      .exec();
  }

  async findAllFoldersNested(orgId: string, type: string) {
    const folders = await this.foldersModel
      .find({ organization: orgId, type }, 'name parent resourceIds')
      .lean();
    const {} = folders;
    
    const nestedFolders = this.nestFolders(folders, null);
    

    return nestedFolders;
  }

  nestFolders(folders: any[], parentId: string | null): any[] {
    return folders
      .filter((folder) => String(folder.parent) === String(parentId))
      .map((folder) => ({
        ...folder,
        children: this.nestFolders(folders, String(folder._id)),
      }));
  }

  async removeResourceId({
    resourceId,
    type,
    organization,
  }: {
    resourceId: string;
    type: string;
    organization: string;
  }) {
    const folder = await this.foldersModel.findOne({
      organization,
      type,
      resourceIds: { $in: [resourceId] },
    });

    if (!folder) {
      throw new NotFoundException('Resource not found');
    }

    if (folder.parent === null) {
      throw new Error('The root folder cannot be deleted');
    }

    await this.foldersModel.updateOne(
      { _id: folder._id },
      { $pull: { resourceIds: resourceId } },
    );

    return;
  }

  importFile(folderId: string, file: string) {
    const folder = this.foldersModel.findById(folderId).exec();
  }

  async switchAll() {
    try {
      const organizations = await this.organizationModel.find().exec();

      const promises = organizations.map(async (org) => {
        const result = await this.startFolder(org._id?.toString());
        
        return result;
      });

      const results = await Promise.all(promises);

      return results;
    } catch (error) {
      
    }
  }
}
