import {
  Controller,
  Get,
  Post,
  Req,
  Res,
  Body,
  HttpStatus,
  Render,
  Delete,
  Param,
  Query,
  UseGuards,
  Patch,
  UnauthorizedException,
} from '@nestjs/common';
import { Request, Response } from 'express';
import {
  ApiOperation,
  ApiQuery,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiParam,
} from '@nestjs/swagger';
import { ExternalApplicationService } from 'src/externals/services/application/v1-application.service';
import { handleException } from 'helpers/handleException';
import { MyLogger } from 'src/logger/logger.service';
import {
  AddApplicationDto,
  NewLeadDto,
} from 'src/externals/dtos/v1-application.dto';
import { TokensService } from 'src/tokens/tokens.service';
import { PROVIDERS, THIRD_PARTY_APPS } from 'src/lib/constant';

@Controller('v1')
export class ApplicationController {
  constructor(
    private readonly externalApplicationService: ExternalApplicationService,
    private readonly logger: MyLogger,
    private readonly tokenService: TokensService,
  ) {}


  /**
   * This function is used to process live events from a Uphex server, to build uphex context taking the Ad campaign data into account.
   */
  @Post('application/events/:provider')
  async processEvents(
    @Req() req: Request,
    @Res() res: Response,
    @Body() body: NewLeadDto,
    @Param('provider') provider: string
  ) {
    try {
      this.logger.log({
        message: `Processing events for provider ${provider} | body: ${JSON.stringify(body)}`,
        context: `External app event`,
      })

      const token = await this.tokenService.validateToken(
        body.capriOrgApiKey,
      );
      if (!token?.token) {
        throw new UnauthorizedException('Capri Token Not Valid');
      }
      const { capriOrgId, capriContactId } = await this.externalApplicationService.processEvents(
        token.orgId,
        body,
        'uphex'
      );
      res
        .status(HttpStatus.OK)
        .json({ message: 'Events processed successfully', data: { capri: { organizationId: capriOrgId, contactId: capriContactId } } });
    } catch (error) {
      this.logger.error({
        message: `Error processing events for provider ${provider}`,
        context: `External app event`,
      });
      handleException(res, error);
    }
  }
}
