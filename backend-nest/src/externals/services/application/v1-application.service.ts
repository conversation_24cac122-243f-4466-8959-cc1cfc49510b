/**
 *  This function is used to process live events from a Uphex server, to build uphex context taking the Ad campaign data into account.
 */
import { BadRequestException, Injectable } from '@nestjs/common';
import {
  AddApplicationDto,
  NewLeadDto,
} from 'src/externals/dtos/v1-application.dto';
import { KINDS, PROVIDERS, THIRD_PARTY_APPS } from 'src/lib/constant';
import { MyLogger } from 'src/logger/logger.service';
import { MongoContextDataService } from 'src/mongo/service/contextData/contextData.service';
import { MongoCredentialsService } from 'src/mongo/service/credentials/mongo-credentials.service';
import { MongoOrganizationService } from 'src/mongo/service/organization/mongo-organization.service';
import { mongoThirdPartyAppsService } from 'src/mongo/service/thirdPartyApps/thirdPartyApps.service';
import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class ExternalApplicationService {
  constructor(
    private readonly logger: MyLogger,
    private readonly mongoCredentialService: MongoCredentialsService,
    private readonly mongoThirdPartyAppsService: mongoThirdPartyAppsService,
    private readonly mongoContextDataService: MongoContextDataService,
  ) { }



  async addExternalApplication(orgId: string, body: AddApplicationDto) {
    let capriContactId = uuidv4();
    this.logger.log({ message: `Adding application | ${JSON.stringify({ orgId, body })}`, context: 'ADD APPLICATION' });

    const appDoc = await this.mongoThirdPartyAppsService.thirdPartyAppExists({
      capriOrgId: orgId,
      'account.id': body?.account?.id,
      appName: 'uphex',
    })

    if (appDoc?._id) {
      this.logger.log({ message: 'Application already exists', context: 'ADD APPLICATION' });
      return { newApp: appDoc };
    }

    const newApp = await this.mongoThirdPartyAppsService.createThirdPartyApp({
      appName: body.appName ?? "uphex",
      account: { ...(body?.account) },
      capriOrgId: body.capriOrgId ?? orgId,
      capriUserId: body.capriUserId,
      variables: [],
    });
    if (newApp?._id) {
      this.logger.log({ message: `Application added successfully - id : ${newApp?._id}`, context: 'ADD APPLICATION' });
    }

    return { newApp, capriContactId };
  }



  async processEvents(orgId: string, body: NewLeadDto, appName?: string) {
    const appDoc = await this.mongoThirdPartyAppsService.getThirdPartyApp({
      capriOrgId: orgId,
    });

    if (!appDoc) {
      throw new BadRequestException(`Your application is not found for capri org Id - ${orgId}`);
    }

    const existingContext = await this.mongoContextDataService.getContext({
      orgId,
      externalContactId: body.data?.ghlContactId,
    });

    //  if already exists update else create new context document
    if (existingContext) {
      let data = existingContext.data;
      if (appName === 'uphex') data = data.filter((d) => d.source !== 'uphex');
      await this.mongoContextDataService.updateContext(
        { _id: existingContext?._id?.toString() },
        {
          $set: {
            data: [...data, {
              adTitle: body.data?.adTitle,
              adMessage: body.data?.adMessage,
              adHeadline: body.data?.adHeadline,
              source: "uphex",
              appId: appDoc._id.toString(),
            }],
          },
        }
      );
      return { capriOrgId: orgId, capriContactId: existingContext.capriContactId };
    } else {
      let capriContactId = uuidv4();
      await this.mongoContextDataService.createContext({
        orgId,
        capriContactId: capriContactId,
        externalContactId: body.data?.ghlContactId,
        data: [
          {
            adTitle: body.data?.adTitle,
            adMessage: body.data?.adMessage,
            adHeadline: body.data?.adHeadline,
            source: "uphex",
            appId: appDoc._id.toString(),
          },
        ],
      });
      return { capriOrgId: orgId, capriContactId };
    }
  }
}