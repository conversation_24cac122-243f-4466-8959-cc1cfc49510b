import { forwardRef, Module } from '@nestjs/common';
import { ApplicationController } from './controllers/application/v1-application.controller';
import { ExternalApplicationService } from './services/application/v1-application.service';
import { OrganizationModule } from 'src/organization/organization.module';
import { LoggerModule } from 'src/logger/logger.module';
import { TokensModule } from 'src/tokens/tokens.module';

@Module({
  imports: [forwardRef(() => OrganizationModule), LoggerModule, TokensModule],
  controllers: [ApplicationController],
  providers: [ExternalApplicationService],
  exports: [ExternalApplicationService],
})
export class ExternalsModule {}
