import {
  IsString,
  IsNotEmpty,
  IsUrl,
  ValidateNested,
  IsBoolean,
  IsEmail,
} from 'class-validator';
import { Type } from 'class-transformer';

export class OrgRequestHandleDto {
  @IsNotEmpty()
  @IsBoolean()
  status: boolean;
  @IsNotEmpty()
  @IsString()
  orgId: string;
}

export class OrgRequestHandleWithUserDto extends OrgRequestHandleDto {
  @IsNotEmpty()
  @IsEmail()
  email: string;
  @IsNotEmpty()
  @IsString()
  userId: string;
}
