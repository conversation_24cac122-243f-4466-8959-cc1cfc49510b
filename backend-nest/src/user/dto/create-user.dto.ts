import {
  Is<PERSON><PERSON>,
  <PERSON><PERSON>otEmpty,
  IsUrl,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Is<PERSON><PERSON>,
  IsOptional,
  Matches,
} from 'class-validator';
import { Type } from 'class-transformer';

class UserSummary {
  readonly telephone: string;
  readonly website: string;
  readonly location: string;
}

export class CreateUserDto {
  @IsNotEmpty()
  readonly name: string;
  readonly email: string;
  readonly profilePic: string;
  readonly organizationSetup: boolean;
  readonly usid?: string;
  @Type(() => UserSummary)
  @ValidateNested()
  readonly summary: UserSummary;
}

export class CreateUserAuth0Api {
  @IsNotEmpty()
  @IsEmail()
  email: string;

  @IsOptional()
  given_name: string;

  @IsOptional()
  family_name: string;

  @IsNotEmpty()
  @IsString()
  name: string;

  @IsOptional()
  nickname: string;

  @IsOptional()
  @IsUrl()
  picture: string;

  @IsOptional()
  @Matches(
    /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*])[A-Za-z\d!@#$%^&*]{8,}$/,
    {
      message:
        'Password must contain at least 8 characters, including at least 3 of the following: lower case letters, upper case letters, numbers, and special characters (!@#$%^&*).',
    },
  )
  password: string;
}
