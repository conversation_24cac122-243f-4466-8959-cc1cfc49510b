import {
  Body,
  Controller,
  Post,
  Res,
  Get,
  Param,
  Req,
  UseGuards,
  HttpStatus,
  Put,
} from '@nestjs/common';
import { UserService } from './user.service';
import { Request, Response } from 'express';
import {
  CreateUserDto,
  UpdateProfileDto,
} from 'src/mongo/dto/users/create.dto';
import { GetUserDto } from './dto/get.dto';
import { AuthGuard } from 'src/auth/auth.guard';
import { ApiOperation } from '@nestjs/swagger';
import { RequestWithUser } from 'src/auth/auth.interface';
import { OrgRequestHandleDto } from './dto/user-org-dto';
import { MyLogger } from '../logger/logger.service';

@Controller('user')
export class UserController {
  constructor(
    private readonly userService: UserService,
    private readonly logger: MyLogger,
  ) {}

  @Get('sessionid')
  async test(@Req() request: Request, @Res() response: Response) {
    const usid = request.query.usid;
    const email = request.query.email;
    
    

    
    const savedSession = this.userService.createSavedSession(
      { usid, email },
      response,
    );
  }

  @Post('/id')
  async createUser(
    @Body() createUserDto: CreateUserDto,
    @Res() response: Response,
  ) {
    

    const createdUser = await this.userService.createUser(
      createUserDto,
      response,
    );
  }

  @Put('profile')
  async updateProfile(
    @Body() updateUserDto: UpdateProfileDto,
    @Req() req: RequestWithUser,
  ) {
    const userId = req.userId;
    const updatedUser = await this.userService.updateProfile(
      updateUserDto,
      userId,
    );
    return updatedUser;
  }

  @Get('profile')
  async getProfile(@Req() req: RequestWithUser) {
    const userId = req.userId;
    const user = await this.userService.getUser(userId);
    return user;
  }

  @Get(':id')
  async getUser(@Param() param: GetUserDto, @Res() response: Response) {
    const user = await this.userService.getUser(param.id);
  }
  @Get('org/getall')
  @UseGuards(AuthGuard)
  @ApiOperation({ summary: 'Get all organizations joined by an user' })
  async getAllOrganization(@Req() req: RequestWithUser, @Res() res: Response) {
    try {
      const userId = req.userId;
      const orgList = await this.userService.getAllOrg({
        userId,
      });
      return res.status(HttpStatus.OK).json({ data: orgList });
    } catch (error) {
      
      this.logger.error({
        message: `Failed to get all organizations for user with error: ${error.message}`,
        context: this.getAllOrganization.name,
      });
      return res
        .status(HttpStatus.INTERNAL_SERVER_ERROR)
        .json({ error: 'Internal Server Error' });
    }
  }

  @Get('org/requests')
  @UseGuards(AuthGuard)
  @ApiOperation({ summary: 'Get all organizations requests' })
  async getAllOrganizationRequests(
    @Req() req: RequestWithUser,
    @Res() res: Response,
  ) {
    try {
      const userId = req.userId;
      

      

      const requests = await this.userService.getAllOrgReq({
        userId,
      });
      return res.status(HttpStatus.OK).json(requests);
    } catch (error) {
      this.logger.error({
        message: `Failed to get all organizations request for user with error: ${error.message}`,
        context: this.getAllOrganizationRequests.name,
      });
      return res
        .status(HttpStatus.INTERNAL_SERVER_ERROR)
        .json({ error: 'Internal Server Error' });
    }
  }

  @Post('org/handlereq')
  @UseGuards(AuthGuard)
  @ApiOperation({ summary: 'Handle the user request' })
  async handleOrgReq(
    @Req() req: RequestWithUser,
    @Body() payload: OrgRequestHandleDto,
    @Res() response: Response,
  ) {
    try {
      const userId = req.userId;
      const email = req.email;
      const handleReq = await this.userService.handleOrgJoinReq({
        ...payload,
        userId,
        email,
      });
      return response
        .status(HttpStatus.OK)
        .json({ message: 'Handled the request' });
    } catch (error) {
      this.logger.error({
        message: `Failed to handle the org request for user with error: ${error.message}`,
        context: this.handleOrgReq.name,
      });
      return response
        .status(HttpStatus.INTERNAL_SERVER_ERROR)
        .json({ error: 'Internal Server Error' });
    }
  }
}
