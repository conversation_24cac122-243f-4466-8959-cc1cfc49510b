import { Injectable, Res, HttpStatus } from '@nestjs/common';
// import { CreateUserDto } from './dto/create-user.dto';
import { Response } from 'express';
import { MongoUserService } from 'src/mongo/service/user/mongo-user.service';
import { User, UserDocument } from 'src/mongo/schemas/user/user.schema';
import { MongoOrganizationService } from 'src/mongo/service/organization/mongo-organization.service';
import { OrgRequestHandleWithUserDto } from './dto/user-org-dto';
import {
  MemberAccess,
  OrganizationMembers,
} from 'src/mongo/schemas/organization/members/members.schema';
import { MongoWaitListService } from 'src/mongo/service/waitlist/mongo-waitlist.service';
import {
  CreateUserDto,
  UpdateProfileDto,
} from 'src/mongo/dto/users/create.dto';
import { MyLogger } from '../logger/logger.service';
import { CreateUserAuth0Api } from './dto/create-user.dto';
import { ManagementClient } from 'auth0';
import {v4 as uuidv4} from "uuid";

@Injectable()
export class UserService {
  private management: ManagementClient;
  constructor(
    private readonly mongoUserService: MongoUserService,
    private readonly mongoOrganizationService: MongoOrganizationService,
    private readonly mongoWaitListService: MongoWaitListService,
    private readonly logger: MyLogger,
  ) {
    this.management = new ManagementClient({
      domain: process.env.AUTH0_DOMAIN,
      clientId: process.env.AUTH0_CLIENT_ID,
      clientSecret: process.env.AUTH0_CLIENT_SECRET,
    });
  }
  async createUser(createUserDto: CreateUserDto, @Res() response: Response) {
    try {
      const { email } = createUserDto;
      

      const existingUser = await this.mongoUserService.getUser({ email });

      if (existingUser) {
        //return userId if an user is already associated with the given user email
        return response.status(HttpStatus.OK).json({
          userId: existingUser._id,
          organizationSetup: existingUser.organizationSetup,
          orgId: existingUser.organizations[0],
        });
      } else {
        //user is not found then create an user and return the userId
        const { userSessionId, ...data } = createUserDto;
        //fetch the orgList if present
        

        const orgList = await this.mongoWaitListService.getOrgReq(email);
        

        // const newUser = await this.mongoUserService.createUser(createUserDto);
        const newUser = await this.mongoUserService.createUser({
          ...data,
          userSessionId,
          requests: {
            orgRequests: orgList,
          },
        });
        const dateInTheFuture = new Date();
        dateInTheFuture.setFullYear(dateInTheFuture.getFullYear() + 100);
        response.cookie('usid', createUserDto.userSessionId, {
          httpOnly: true,
          secure: process.env.NODE_ENV !== 'DEVELOPMENT',
          expires: dateInTheFuture,
          domain: process.env.DOMAIN,
          sameSite: process.env.NODE_ENV === 'DEVELOPMENT' ? 'lax' : 'none',
        });
        return response.status(HttpStatus.CREATED).json({
          userId: newUser._id,
          organizationSetup: newUser.organizationSetup,
          orgId: newUser.organizations[0],
        });
      }
    } catch (error) {
      this.logger.error({
        message: `Failed to create user ${createUserDto.email}`,
        context: this.createUser.name,
      });
      return response
        .status(HttpStatus.INTERNAL_SERVER_ERROR)
        .json({ error: 'Internal Server Error' });
    }
  }

  async updateProfile(updateUserDto: UpdateProfileDto, userId: string) {
    const { key, value } = updateUserDto;
    const updateBody = { [key]: value };
    const updatedUser = await this.mongoUserService.updateUser(
      { _id: userId },
      updateBody,
    );
    return updatedUser;
  }

  async getUser(userId: string) {
    try {
      const user = await this.mongoUserService.getUser({ _id: userId });
      return user;
    } catch (error) {
      this.logger.error({
        message: `Failed to get user with id ${userId} with error ${error.message}`,
        context: this.getUser.name,
      });
      return null;
    }
  }

  async createSavedSession({ usid, email }, response: Response) {
    try {
      const user = await this.mongoUserService.updateUser(
        { email },
        { userSessionId: usid },
      );
      if (!user) {
        return response.status(404).json({ message: 'User not found' });
      }
      
      
      

      const dateInTheFuture = new Date();
      dateInTheFuture.setFullYear(dateInTheFuture.getFullYear() + 100);
      response.cookie('usid', usid, {
        httpOnly: true,
        secure: process.env.NODE_ENV !== 'DEVELOPMENT',
        expires: dateInTheFuture,
        domain: process.env.DOMAIN,
        sameSite: process.env.NODE_ENV === 'DEVELOPMENT' ? 'lax' : 'none',
      });

      return response.status(200).json({
        message: 'Successfully added session ID to the user',
        organizationSetup: user.organizationSetup,
      });
    } catch (error) {
      this.logger.error({
        message: `Failed to create saved session for user ${email} with usid ${usid} with error ${error.message}`,
        context: this.createSavedSession.name,
      });
      return null;
    }
  }

  async findUserBySessionId(sessionId: string): Promise<UserDocument | null> {
    const user = await this.mongoUserService.getUser({
      userSessionId: sessionId,
    });

    return user;
  }

  async getAllOrg(data: { userId: string }) {
    const { userId } = data;
    const user = await this.getUser(userId);
    if (!user) {
      throw Error('User not found');
    }
    const orgIdList = user.organizations;
    const orgPromises = orgIdList.map((id) =>
      this.mongoOrganizationService.getOrganization(
        { _id: id },
        { _id: 1, name: 1, userId: 1 },
      ),
    );

    const orgList = await Promise.all(orgPromises);

    return orgList;
  }

  async getAllOrgReq(body: { userId: string }) {
    const { userId } = body;
    

    const reqList = await this.mongoUserService.getUser(
      { _id: userId },
      { requests: 1 },
    );

    const organizationRequestsList = reqList.requests?.orgRequests || [];
    const organizationRequests = await Promise.all(
      (reqList.requests?.orgRequests || []).map(async (item) => {
        const org = await this.mongoOrganizationService.getOrganization(
          { _id: item.orgId },
          { name: 1, _id: 0 },
        );

        return {
          ...org,
          ...item,
        };
      }),
    );

    const organizationRequestsLength = organizationRequestsList.length;
    return { organizationRequests, organizationRequestsLength };
  }

  async handleOrgJoinReq(payload: OrgRequestHandleWithUserDto) {
    const { email, orgId, status, userId } = payload;
    if (status === false) {
      const user = await this.mongoUserService.updateUser(
        { _id: userId, 'requests.orgRequests.orgId': orgId },
        {
          $pull: { 'requests.orgRequests': { orgId: orgId } },
        },
      );
      return;
    }
    /** 
    ✅check the user document if they have a request for that orgId
    If no then Error
    If yes then 
    1. ✅Push the orgId in user.organizations
    2. Push the userId with the access values in organization.members
  **/
    const user = await this.mongoUserService.updateUser(
      { _id: userId, 'requests.orgRequests.orgId': orgId },
      {
        $pull: { 'requests.orgRequests': { orgId: orgId } },
        $push: {
          organizations: orgId,
        },
      },
      {
        // new: true,
        projection: { 'requests.orgRequests.$': 1 },
      },
    );
    const userObject = user?.requests.orgRequests[0];
    
    if (!userObject) {
      this.logger.error({
        message: `Failed to handle org join request for user ${email} with orgId ${orgId}`,
        context: this.handleOrgJoinReq.name,
      });
      throw new Error('Problem fetching the user');
    }
    const accessObject =
      await this.mongoOrganizationService.memberAccessModelClass(
        userObject.access,
      );
    

    let updateObject: any = {
      $push: {
        members: {
          userId,
          access: accessObject,
        },
      },
    };

    if (userObject.isAdmin) {
      updateObject.$push.admin = userId;
    }

    const org = await this.mongoOrganizationService.updateOrganization(
      { _id: orgId },
      updateObject,
    );

    

    return;
  }

  async updateUser(
    query: Object,
    updateBody: any,
    options: any = { new: true, upsert: false },
  ) {
    const updatedUser = await this.mongoUserService.updateUser(
      query,
      updateBody,
      options,
    );
    return updatedUser;
  }

  private generateUserPassword(): string {
    const lowercase = 'abcdefghijklmnopqrstuvwxyz';
    const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const numbers = '0123456789';
    const special = '!@#$%^&*';
    
    // Generate base password with at least one character from each set
    let password = '';
    password += lowercase[Math.floor(Math.random() * lowercase.length)];
    password += uppercase[Math.floor(Math.random() * uppercase.length)];
    password += numbers[Math.floor(Math.random() * numbers.length)];
    password += special[Math.floor(Math.random() * special.length)];
    
    // Add random characters to meet minimum length
    const allChars = lowercase + uppercase + numbers + special;
    while (password.length < 12) {
        password += allChars[Math.floor(Math.random() * allChars.length)];
    }
    
    // Shuffle the password
    return password.split('').sort(() => Math.random() - 0.5).join('');
}

  async createAuth0User(userData: CreateUserAuth0Api) {
    const { email, name, picture, password: passwordFromPayload, family_name, given_name, nickname } = userData;
    const password = passwordFromPayload || this.generateUserPassword();
    const userPayload = {
      // Required fields
      email,
      name,
      // Preset fields
      user_metadata: {},
      blocked: false,
      email_verified: true,
      app_metadata: {},
      user_id: uuidv4(),
      connection: 'Username-Password-Authentication',
      verify_email: true,
      // Optional fields
      picture: picture || null,
      password: password,
      given_name,
      family_name,
      nickname,
    }; 
    if(!userPayload.picture) {
      delete userPayload.picture;
    }
    const user = await this.management.users.create(userPayload);

    
    
    return {...user, password};
  }

  async getAuth0UserById(userId: string) {
    return await this.management.users.get({ id: userId });
  }
}
