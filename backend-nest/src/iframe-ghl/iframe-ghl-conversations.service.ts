import { BadRequestException, Injectable } from '@nestjs/common';
import { SendMessageDto } from './dto/conversation.dto';
import { GhlApisService } from 'src/api-client/services/ghl-apis/ghl-apis.service';
import { MongoCredentialsService } from 'src/mongo/service/credentials/mongo-credentials.service';
import { KINDS } from 'src/lib/constant';
import { IghlSendMessage } from 'src/api-client/dto/ghl.dto';
import { MongoConversationService } from 'src/mongo/service/conversations/mongo-conversations.service';
import axios from 'axios';

// Dummy data types
export interface Contact {
  id: string;
  name: string;
  locationId: string;
  firstName: string;
  lastName: string;
  email: string;
  emailLowerCase: string;
  companyName: string;
  phone: string;
  dnd: boolean;
  address1: string;
  city: string;
  state: string;
  country: string;
  postalCode: string;
  website: string;
  tags: string[];
  dateAdded: string;
  dateUpdated: string;
  lastActivity: string;
}

export interface Conversation {
  contactId: string;
  locationId: string;
  deleted: boolean;
  inbox: boolean;
  type: number;
  unreadCount: number;
  assignedTo: string;
  id: string;
  starred: boolean;
}

export interface Message {
  id: string;
  type: number;
  messageType: string;
  locationId: string;
  contactId: string;
  conversationId: string;
  dateAdded: string;
  body?: string;
  direction: 'inbound' | 'outbound';
  status?: string;
  contentType: string;
  attachments?: string[];
  userId?: string;
  conversationProviderId?: string;
}

export interface MessagesResponse {
  lastMessageId: string;
  nextPage: boolean;
  messages: Message[];
  conversationProviderId?: string;
}

export interface ContactMessage {
  agentName: string;
  channelName: string;
  channel: string;
  lastMessage: string;
  lastMessageAt: string;
  lastMessageStatus: string;
  rollover: boolean;
  rolloverReason: string;
  rolloverDate: string;
  contactId: string;
  conversationId: string;
  channelAccountId: string;
  refUrl: string;
  contactName: string;
  disconnectBot: boolean;
  addedAsSavedExample: boolean;
}

@Injectable()
export class IframeGhlConversationsService {
  // Dummy data
  readonly DUMMY_CONTACT: Contact = {
    id: 'seD4PfOuKoVMLkEZqohJ',
    name: 'John Smith',
    locationId: 've9EPM428h8vShlRW1KT',
    firstName: 'John',
    lastName: 'Smith',
    email: '<EMAIL>',
    emailLowerCase: '<EMAIL>',
    companyName: 'Acme Inc',
    phone: '+***********',
    dnd: false,
    address1: '123 Main St',
    city: 'San Francisco',
    state: 'CA',
    country: 'US',
    postalCode: '94105',
    website: 'https://www.example.com',
    tags: ['VIP Customer', 'Tech Industry'],
    dateAdded: '2023-07-02T05:18:26.704Z',
    dateUpdated: '2024-02-15T05:18:26.704Z',
    lastActivity: '2024-03-16T11:39:30.564Z',
  };

  private readonly DUMMY_CONVERSATION: Conversation = {
    contactId: 'seD4PfOuKoVMLkEZqohJ',
    locationId: 've9EPM428h8vShlRW1KT',
    deleted: false,
    inbox: true,
    type: 2,
    unreadCount: 3,
    assignedTo: 've9EPM428h8vShlRW1KT',
    id: 'tDtDnQdgm2LXpyiqYvZ6',
    starred: true,
  };

  private readonly allMessages: Message[] = this.generateDummyMessages(
    40,
    this.DUMMY_CONVERSATION.id,
    this.DUMMY_CONVERSATION.contactId,
  );

  private readonly allConversations: ContactMessage[] = [
    {
      agentName: 'Sales Bot',
      channelName: 'SMS Channel',
      channel: 'SMS',
      lastMessage: 'Hi there! How can I help you today?',
      lastMessageAt: '2024-04-16T11:39:30.564Z',
      lastMessageStatus: 'delivered',
      rollover: false,
      rolloverReason: '',
      rolloverDate: '',
      contactId: this.DUMMY_CONTACT.id,
      conversationId: this.DUMMY_CONVERSATION.id,
      channelAccountId: 'channel123',
      refUrl: `https://app.gohighlevel.com/v2/location/${this.DUMMY_CONTACT.locationId}/contacts/detail/${this.DUMMY_CONTACT.id}`,
      contactName: `${this.DUMMY_CONTACT.firstName} ${this.DUMMY_CONTACT.lastName}`,
      disconnectBot: false,
      addedAsSavedExample: false,
    },
    {
      agentName: 'Support Bot',
      channelName: 'WhatsApp Channel',
      channel: 'WhatsApp',
      lastMessage: 'Thank you for contacting support',
      lastMessageAt: '2024-04-15T08:22:15.123Z',
      lastMessageStatus: 'delivered',
      rollover: false,
      rolloverReason: '',
      rolloverDate: '',
      contactId: 'contact456',
      conversationId: this.DUMMY_CONVERSATION.id,
      channelAccountId: 'channel456',
      refUrl: `https://app.gohighlevel.com/v2/location/${this.DUMMY_CONTACT.locationId}/contacts/detail/contact456`,
      contactName: 'Jane Doe',
      disconnectBot: false,
      addedAsSavedExample: true,
    },
  ];

  constructor(
    private readonly ghlApiService: GhlApisService,
    private readonly mongoCredentialsService: MongoCredentialsService,
    private readonly mongoConversationsService: MongoConversationService,
  ) { }

  private async getGhlTokens({ location }: { location: string }) {

    const credential = await this.mongoCredentialsService.getCredential({
      keyId: location,
      kind: KINDS.GHL_CREDENTIAL,
    });

    const ghlToken = {
      access_token: credential?.creds?.accessToken,
      refresh_token: credential?.creds?.refreshToken,
    };

    if ((ghlToken?.access_token ?? ghlToken?.refresh_token) === undefined)
      throw new BadRequestException(`Invalid leadconnector connection detected. Please refresh your Capri leadconnector connection.`)

    return { ghlToken };
  }

  // Generate dummy messages
  private generateDummyMessages(
    count: number,
    conversationId: string,
    contactId: string,
  ): Message[] {
    const messages: Message[] = [];
    const now = new Date();
    const messageTypes = [
      'TYPE_SMS',
      'TYPE_EMAIL',
      'TYPE_CALL',
      'TYPE_LIVE_CHAT',
    ];
    const statuses = ['delivered', 'read', 'sent'];
    const bodies = [
      "Hi there! I'm interested in your services.",
      'Can you tell me more about your pricing?',
      "Thanks for the information. I'll get back to you soon.",
      "I'd like to schedule a demo. What times are available next week?",
      'Do you offer any discounts for annual subscriptions?',
      "I'm having an issue with my account. Can you help me troubleshoot?",
      'Just following up on our previous conversation.',
      'Great! That works for me.',
      "I've attached the requested documents for your review.",
      'Looking forward to our meeting tomorrow.',
    ];

    for (let i = 0; i < count; i++) {
      const date = new Date(now);
      date.setHours(date.getHours() - i);

      const isOutbound = i % 3 === 0;
      const messageType =
        messageTypes[Math.floor(Math.random() * messageTypes.length)];
      const hasAttachments = Math.random() > 0.8;

      messages.push({
        id: `msg_${i}_${Date.now()}`,
        type:
          messageType === 'TYPE_SMS' ? 2 : messageType === 'TYPE_EMAIL' ? 3 : 1,
        messageType,
        locationId: 've9EPM428h8vShlRW1KT',
        contactId,
        conversationId,
        dateAdded: date.toISOString(),
        body: bodies[i % bodies.length],
        direction: isOutbound ? 'outbound' : 'inbound',
        status: statuses[Math.floor(Math.random() * statuses.length)],
        contentType: 'text/plain',
        attachments: hasAttachments
          ? ['https://example.com/attachment.pdf']
          : undefined,
      });
    }

    return messages;
  }

  async findAll(
    location: string,
    page: number,
    filters: Record<string, string>,
    search: string,
  ) {
    const conversations = await this.mongoConversationsService.getConversations({
      query: {
        resourceId: location,
      },
      projection: {
        conversation: 0,
      }
    });

    return conversations;
  }

  async findOne(conversationId: string, location: string) {

    const { ghlToken } = await this.getGhlTokens({ location });
    const conversation = await this.ghlApiService.getConversation({ tokens: ghlToken, locationId: location, conversationId });
    const dbConversation = await this.mongoConversationsService.getConversation({
      query: {
        contactId: conversation?.contactId,
      },
      projection: {
        disconnectBot: 1,
        addedAsSavedExample: 1,
      }
    })
    return {...conversation, disconnectBot: dbConversation?.disconnectBot ?? false, addedAsSavedExample: dbConversation?.addedAsSavedExample ?? false};
  }

  async getMessages(conversationId: string, location: string) {

    const { ghlToken } = await this.getGhlTokens({ location });
    const conversationMessages = await this.ghlApiService.getMessagesByConversationId(ghlToken, conversationId, {});

    // TODO: correct params
    return conversationMessages;
  }

  async getPaginatedMessages(
    conversationId: string,
    lastMessageId: string = '',
    limit = 20,
    location: string,
  ): Promise<MessagesResponse> {

    

    const { ghlToken } = await this.getGhlTokens({ location });
    const conversationMessages = await this.ghlApiService.getMessagesByConversationId(ghlToken, conversationId, { limit, lastMessageId });

    if (!conversationMessages?.messages) throw new Error(`No messages found for conversation ID: ${conversationId}`)
    return conversationMessages?.messages;
  }

  async sendMessage(
    data: {
      conversationId: string;
      location: string;
    } & SendMessageDto,
  ) {

    const { ghlToken } = await this.getGhlTokens({ location: data.location });
    const newMessage = await this.ghlApiService.sendMessage(ghlToken, data.location, data as IghlSendMessage);

    return newMessage;
  }

  async toggleEnableBot(conversationId: string, location: string, enable: boolean) {
    
    

    const { ghlToken } = await this.getGhlTokens({ location: location });
    const conversation = await this.ghlApiService.getConversation({
      tokens: ghlToken,
      locationId: location,
      conversationId,
    });

    await this.mongoConversationsService.updateConversation({
      query: {
        contactId: conversation?.contactId,
      },
      updateBody: {
        '$set': {
          disconnectBot: !enable
        }
      }
    })
    return { success: true };
  }

  async toggleSaveConversation(
    conversationId: string,
    location: string,
    agentId: string,
    orgId: string,
    save: boolean,
  ) {
    
    
    
    await axios.post(`${process.env.BACKEND_URL}/session/history/train?locationId=${location}&conversationId=${conversationId}`, {
      agentId,
      orgId
    });

    return { success: true };
  }

  async getEmailById(emailId: string, location: string) {

    
    
    const { ghlToken } = await this.getGhlTokens({ location });
    const email = await this.ghlApiService.getEmailById(ghlToken, emailId);

    return email;
  }
}
