import { forwardRef, Module } from '@nestjs/common';
import { IframeGhlService } from './iframe-ghl.service';
import { IframeGhlController } from './iframe-ghl.controller';
import { TokensService } from 'src/tokens/tokens.service';
import { TokensModule } from 'src/tokens/tokens.module';
import { SessionService } from 'src/session/session.service';
import { MongoModule } from 'src/mongo/mongo.module';
import { MongoFallbackService } from 'src/mongo/service/fallback/mongo-fallback.service';
import { SessionModule } from 'src/session/session.module';
import { OutreachModule } from 'src/session/outreachService/outreach_module';
import { FallbackModule } from 'src/fallback/fallback.module';
import { SessionListService } from 'src/session/outreachService/session-list.service';
import { AiHistoryModule } from 'src/ai_History_Record/aiHistory_module';
import { RebillingService } from 'src/rebilling/rebilling.service';
import { StripeService } from 'src/billing/stripe.service';
import { LangchainReadModule } from 'src/langchain_read_action/langchain_read.module';
import { LangchainWriteModule } from 'src/langchain_write_action/langchain_write.module';
import { OpenaiService } from 'src/ai_response/openai';
import { CacheService } from 'src/utility/services/cache.service';
import { ActionsService } from 'src/organization/services/actions/actions.service';
import { PineconeService } from 'src/vector_db/pinecone/pinecone.service';
import { VectorDbModule } from 'src/vector_db/vector_db.module';
import { IframeHelperService } from './iframe-helper.service';
import { KnowledgeService } from 'src/voice/vapi/knowledge/knowledge.service';
import { VoiceModule } from 'src/voice/voice.module';
import { AgentFileUpload } from 'src/agent/file-upload.service';
import { BullModule } from '@nestjs/bull';
import { OrganizationModule } from 'src/organization/organization.module';
import { FoldersModule } from 'src/folders/folders.module';
import { AiModelsModule } from 'src/ai-models/ai-models.module';
import { IframeGhlConversationsController } from './iframe-ghl-conversations.controller';
import { IframeGhlConversationsService } from './iframe-ghl-conversations.service';
import { IframeGhlContactsService } from './iframe-ghl-contacts.service';
import { IframeGhlContactsController } from './iframe-ghl-contacts.controller';
import { AuditLogService } from 'src/audit-log/audit-log.service';
import { AuditLogModule } from 'src/audit-log/audit-log.module';
@Module({
  imports: [
    TokensModule,
    MongoModule,
    SessionModule,
    OutreachModule,
    FallbackModule,
    AiHistoryModule,
    LangchainReadModule,
    LangchainWriteModule,
    VectorDbModule,
    VoiceModule,
    BullModule.registerQueue({
      name: 'file-processing',
    }),
    BullModule.registerQueue({
      name: 'website-processing',
    }),
    forwardRef(() => OrganizationModule),
    FoldersModule,
    AiModelsModule,
    AuditLogModule,
  ],
  controllers: [
    IframeGhlController,
    IframeGhlConversationsController,
    IframeGhlContactsController,
  ],
  providers: [
    IframeGhlService,
    IframeGhlConversationsService,
    IframeGhlContactsService,
    TokensService,
    SessionService,
    SessionListService,
    MongoFallbackService,
    RebillingService,
    StripeService,
    OpenaiService,
    CacheService,
    ActionsService,
    PineconeService,
    IframeHelperService,
    KnowledgeService,
    AgentFileUpload
  ],
  exports: [
    IframeGhlService,
    IframeGhlConversationsService,
    IframeHelperService,
  ],
})
export class IframeGhlModule {}
