import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Req,
  Query,
  Res,
  UseInterceptors,
  UploadedFiles,
} from '@nestjs/common';
import { IframeGhlService } from './iframe-ghl.service';
import { ApiTokenGuard } from 'src/guards/api-token.guard';
import { RequestWithUser } from 'src/auth/auth.interface';
import {
  AddGhlTagResponseDto,
  CreateSessionDto,
  SendMessageDto,
  UpdateGhlCalendarActionDto,
  UpdateSessionMessageDto,
  UpdateSessionNameDto,
} from './dto/iframe-sessions.dto';
import { Response } from 'express';
import { IframeBillingGuard } from 'src/guards/iframe-billing.guard';
import { UpdateAgentDto } from './dto/update-agent.dto';
import { SendNewMessageDto } from './dto/send-new-message.dto';
import { AddFaqDto, EditFaqDto } from './dto/faq.dto';
import { EditTextContentKnowledgeSourceDto } from './dto/text-content.dto';
import { ScrapeUrlsDto } from './dto/scarpe-urls.dto';
import { FilesInterceptor } from '@nestjs/platform-express';
import { EditCrmCalendarDto } from './dto/edit-crm-calendar.dto';
import { AddCrmCalendarDto } from './dto/add-crm-calendar.dto';
import { AddTriggerDto } from './dto/add-trigger.dto';
import { EditTriggerDto } from './dto/edit-trigger.dto';
import { AddTagsCustomFieldsDto } from './dto/add-tags-custom-fields.dto';
import { EditTagsCustomFieldsDto } from './dto/edit-tags-custom-fields.dto';
import { AddAgentDto } from './dto/add-agent.dto';
import { EditChatbotResponseActionDto } from './dto/edit-response-action.dto';
import { ToggleAgentStatusDto } from './dto/toggle-agent-status.dto';

@Controller('iframe')
@UseGuards(ApiTokenGuard)
export class IframeGhlController {
  constructor(private readonly iframeGhlService: IframeGhlService) {}

  @UseGuards(IframeBillingGuard)
  @Post('sessions')
  async createSession(
    @Body() createSessionDto: CreateSessionDto,
    @Req() req: RequestWithUser,
    @Res() response: Response,
  ) {
    const orgId = req.orgId;
    const newCreateDto = {
      ...createSessionDto,
      orgId,
    };
    return await this.iframeGhlService.createSession(newCreateDto, response);
  }

  @UseGuards(IframeBillingGuard)
  @Get('sessions/all/:agentId')
  findAllSessions(
    @Query('offset') offset: number,
    @Query('limit') limit: number,
    @Query('sortBy') sortBy: 'lastUsed' | 'lastCreated',
    @Query('saved') saved: 'true' | 'false',
    @Param('agentId') agentId: string,
    @Req() req: RequestWithUser,
  ) {
    const orgId = req.orgId;
    return this.iframeGhlService.findAll({
      agentId,
      orgId,
      offset,
      limit,
      sortBy,
      saved,
    });
  }

  @Get('sessions/:sessionId')
  findOneSession(
    @Param('sessionId') sessionId: string,
    @Res() response: Response,
  ) {
    return this.iframeGhlService.findOne(sessionId, response);
  }

  @Patch('sessions/:sessionId/session-name')
  updateSessionName(
    @Param('sessionId') sessionId: string,
    @Body() updateSessionNameDto: UpdateSessionNameDto,
    @Res() response: Response,
  ) {
    return this.iframeGhlService.updateSessionName(
      sessionId,
      response,
      updateSessionNameDto,
    );
  }

  @Patch('sessions/:sessionId/save')
  saveSession(
    @Param('sessionId') sessionId: string,
    @Res() response: Response,
  ) {
    return this.iframeGhlService.saveSession(sessionId, response);
  }

  @Patch('sessions/:sessionId/unsave')
  unSaveSession(
    @Param('sessionId') sessionId: string,
    @Res() response: Response,
  ) {
    return this.iframeGhlService.unSaveSession(sessionId, response);
  }

  @Delete('sessions/:sessionId')
  async removeSession(
    @Param('sessionId') sessionId: string,
    @Res() response: Response,
  ) {
    return this.iframeGhlService.remove(sessionId, response);
  }

  @Post('sessions/:sessionId/message')
  sendSessionMessage(
    @Param('sessionId') sessionId: string,
    @Body() sendMessageDto: SendMessageDto,
    @Res() response: Response,
  ) {
    return this.iframeGhlService.sendSessionMessage(
      sessionId,
      sendMessageDto,
      response,
    );
  }

  @Patch('sessions/:sessionId/message')
  updateSessionMessages(
    @Param('sessionId') sessionId: string,
    @Body() updateSessionMessageDto: UpdateSessionMessageDto,
    @Res() response: Response,
  ) {
    return this.iframeGhlService.updateSessionMessages(
      sessionId,
      updateSessionMessageDto,
      response,
    );
  }

  @Post('sessions/:sessionId/add-response/ghl-calendar')
  addSessionGhlCalendar(
    @Param('sessionId') sessionId: string,
    @Body() updateGhlCalendarActionDto: UpdateGhlCalendarActionDto,
    @Res() response: Response,
  ) {
    return this.iframeGhlService.addSessionGhlCalendar(
      sessionId,
      updateGhlCalendarActionDto,
      response,
    );
  }

  @Patch('sessions/:sessionId/edit-response/ghl-calendar')
  updateSessionGhlCalendar(
    @Param('sessionId') sessionId: string,
    @Body() updateGhlCalendarActionDto: UpdateGhlCalendarActionDto,
    @Res() response: Response,
  ) {
    return this.iframeGhlService.updateSessionGhlCalendar(
      sessionId,
      updateGhlCalendarActionDto,
      response,
    );
  }

  @Delete('sessions/:sessionId/response/:eventId')
  async removeBotResponse(
    @Param('sessionId') sessionId: string,
    @Param('eventId') eventId: string,
    @Res() response: Response,
  ) {
    await this.iframeGhlService.removeBotResponse(sessionId, eventId, response);
  }

  @Get('agents/:agentId/tags-connections')
  getTagsConnections(
    @Param('agentId') agentId: string,
    @Query('location') location: string,
  ) {
    return this.iframeGhlService.getTagsConnections(agentId, location);
  }

  @Post('sessions/:sessionId/add-response/ghl-tag')
  addGhlTagResponse(
    @Param('sessionId') sessionId: string,
    @Body() addGhlTagResponseDto: AddGhlTagResponseDto,
    @Res() response: Response,
  ) {
    const action = 'tag';
    return this.iframeGhlService.addGhlTagResponse(
      sessionId,
      {
        ...addGhlTagResponseDto,
        action,
      },
      response,
    );
  }

  @Post('agents')
  createAgent(@Body() addAgentDto: AddAgentDto, @Req() req: RequestWithUser) {
    const token = req.headers['x-api-token'] as string;
    return this.iframeGhlService.createAgent(addAgentDto, token);
  }

  @Get('agents')
  async getAgents(@Query('location') location: string) {
    return this.iframeGhlService.getAgents(location);
  }

  @Get('agents/check-name')
  async getAgentNameStatus(
    @Query('location') location: string,
    @Query('name') name: string,
  ) {
    return this.iframeGhlService.getAgentNameStatus(location, name);
  }

  @Get('agents/:agentId')
  async getAgent(
    @Param('agentId') agentId: string,
    @Query('location') location: string,
  ) {
    return this.iframeGhlService.getAgent(agentId, location);
  }

  @Patch('agents/:agentId/toggle-status')
  toggleAgentStatus(
    @Param('agentId') agentId: string,
    @Body() toggleAgentStatusDto: ToggleAgentStatusDto,
  ) {
    return this.iframeGhlService.toggleAgentStatus(
      agentId,
      toggleAgentStatusDto,
    );
  }

  @Patch('agents/:agentId')
  updateAgent(
    @Param('agentId') agentId: string,
    @Body() updateAgentDto: UpdateAgentDto,
  ) {
    return this.iframeGhlService.updateAgent(agentId, updateAgentDto);
  }

  @Post('agents/:agentId/send-message')
  sendNewMessageToAgent(
    @Param('agentId') agentId: string,
    @Body() sendNewMessageDto: SendNewMessageDto,
  ) {
    return this.iframeGhlService.sendNewMessageToAgent(sendNewMessageDto);
  }

  @Post('agents/:agentId/sessions')
  createNewSession(@Param('agentId') agentId: string) {
    return this.iframeGhlService.createNewSession(agentId);
  }

  @Post('agents/:agentId/faqs')
  addFaq(@Param('agentId') agentId: string, @Body() addFaqDto: AddFaqDto) {
    return this.iframeGhlService.addFaq(agentId, addFaqDto);
  }

  @Patch('agents/:agentId/faqs/:faqId')
  editFaq(
    @Param('agentId') agentId: string,
    @Param('faqId') faqId: string,
    @Body() editFaqDto: EditFaqDto,
  ) {
    return this.iframeGhlService.editFaq(agentId, faqId, editFaqDto);
  }

  @Delete('agents/:agentId/faqs/:faqId')
  deleteFaq(@Param('agentId') agentId: string, @Param('faqId') faqId: string) {
    return this.iframeGhlService.deleteFaq(agentId, faqId);
  }

  @Patch('agents/:agentId/text-content')
  editTextContentKnowledgeSource(
    @Param('agentId') agentId: string,
    @Body()
    editTextContentKnowledgeSourceDto: EditTextContentKnowledgeSourceDto,
  ) {
    return this.iframeGhlService.editTextContentKnowledgeSource(
      agentId,
      editTextContentKnowledgeSourceDto,
    );
  }

  @Get('agents/:agentId/links')
  getLinks(@Param('agentId') agentId: string, @Query('url') url: string) {
    return this.iframeGhlService.getLinks(agentId, url);
  }

  @Post('agents/:agentId/scrape-urls')
  scrapeUrls(
    @Param('agentId') agentId: string,
    @Body() scrapeUrlsDto: ScrapeUrlsDto,
  ) {
    return this.iframeGhlService.scrapeUrls(agentId, scrapeUrlsDto.urls);
  }

  @Delete('agents/:agentId/links/:accountId')
  async deleteWebsite(
    @Param('agentId') agentId: string,
    @Param('accountId') accountId: string,
  ) {
    return this.iframeGhlService.deleteWebsite(agentId, accountId);
  }

  @Post('agents/:agentId/file-upload')
  @UseInterceptors(FilesInterceptor('files'))
  async uploadFiles(
    @Param('agentId') agentId: string,
    @UploadedFiles() files: Express.Multer.File[],
  ) {
    
    const queuedFiles = await this.iframeGhlService.processFiles(
      agentId,
      files,
    );
    // return this.fileUploadService.processFiles(agentId, files);
    return queuedFiles;
  }

  @Delete('agents/:agentId/file/:accountId')
  deleteFile(
    @Param('agentId') agentId: string,
    @Param('accountId') accountId: string,
  ) {
    return this.iframeGhlService.deleteFile(agentId, accountId);
  }

  @Get('agents/:agentId/crm-calendars')
  getCrmCalendars(
    @Param('agentId') agentId: string,
    @Query('location') location: string,
  ) {
    return this.iframeGhlService.getCrmCalendars(agentId, location);
  }

  @Get('agents/:agentId/crm-calendars-connections')
  getCrmCalendarsConnections(
    @Param('agentId') agentId: string,
    @Query('location') location: string,
  ) {
    return this.iframeGhlService.getCrmCalendarsConnections(agentId, location);
  }

  @Post('agents/:agentId/crm-calendars')
  addCrmCalendar(
    @Param('agentId') agentId: string,
    @Body() addCrmCalendarDto: AddCrmCalendarDto,
  ) {
    return this.iframeGhlService.addCrmCalendar(agentId, addCrmCalendarDto);
  }

  @Post('agents/:agentId/crm-calendars/:accountId/toggle-connection')
  disconnectCrmCalendar(
    @Param('agentId') agentId: string,
    @Param('accountId') accountId: string,
  ) {
    return this.iframeGhlService.toggleCrmCalendarConnection(
      agentId,
      accountId,
    );
  }

  @Patch('agents/:agentId/crm-calendars/:accountId')
  editCrmCalendar(
    @Param('agentId') agentId: string,
    @Param('accountId') accountId: string,
    @Body() editCrmCalendarDto: EditCrmCalendarDto,
  ) {
    return this.iframeGhlService.editCrmCalendar(
      agentId,
      accountId,
      editCrmCalendarDto,
    );
  }

  @Delete('agents/:agentId/crm-calendars/:accountId')
  deleteCrmCalendar(
    @Param('agentId') agentId: string,
    @Param('accountId') accountId: string,
  ) {
    return this.iframeGhlService.deleteCrmCalendar(agentId, accountId);
  }

  @Post('agents/:agentId/triggers')
  addTrigger(
    @Param('agentId') agentId: string,
    @Body() addTriggerDto: AddTriggerDto,
  ) {
    return this.iframeGhlService.addTrigger(agentId, addTriggerDto);
  }

  @Patch('agents/:agentId/triggers/:triggerId')
  editTrigger(
    @Param('agentId') agentId: string,
    @Param('triggerId') triggerId: string,
    @Body() editTriggerDto: EditTriggerDto,
  ) {
    return this.iframeGhlService.editTrigger(
      agentId,
      triggerId,
      editTriggerDto,
    );
  }

  @Delete('agents/:agentId/triggers/:triggerId')
  deleteTrigger(
    @Param('agentId') agentId: string,
    @Param('triggerId') triggerId: string,
  ) {
    return this.iframeGhlService.deleteTrigger(agentId, triggerId);
  }

  @Get('agents/:agentId/tags')
  getTags(
    @Param('agentId') agentId: string,
    @Query('location') location: string,
  ) {
    return this.iframeGhlService.getTags(agentId, location);
  }

  @Get('agents/:agentId/custom-fields')
  getCustomFields(
    @Param('agentId') agentId: string,
    @Query('location') location: string,
  ) {
    return this.iframeGhlService.getCustomFields(agentId, location);
  }

  @Get('agents/:agentId/custom-fields-connections')
  getCustomFieldsConnections(
    @Param('agentId') agentId: string,
    @Query('location') location: string,
  ) {
    return this.iframeGhlService.getCustomFieldsConnections(agentId, location);
  }

  @Get('agents/:agentId/standard-fields-connections')
  getStandardFieldsConnections(
    @Param('agentId') agentId: string,
    @Query('location') location: string,
  ) {
    return this.iframeGhlService.getStandardFieldsConnections(
      agentId,
      location,
    );
  }

  @Post('agents/:agentId/tags-custom-fields')
  addTagsCustomFields(
    @Param('agentId') agentId: string,
    @Body() addTagsCustomFieldsDto: AddTagsCustomFieldsDto,
  ) {
    return this.iframeGhlService.addTagsCustomFields(
      agentId,
      addTagsCustomFieldsDto,
    );
  }

  @Patch('agents/:agentId/tags-custom-fields/:actionId')
  editTagsCustomFields(
    @Param('agentId') agentId: string,
    @Param('actionId') actionId: string,
    @Body() editTagsCustomFieldsDto: EditTagsCustomFieldsDto,
  ) {
    return this.iframeGhlService.editTagsCustomFields(
      agentId,
      actionId,
      editTagsCustomFieldsDto,
    );
  }

  @Delete('agents/:agentId/tags-custom-fields/:actionId')
  deleteTagsCustomFields(
    @Param('agentId') agentId: string,
    @Param('actionId') actionId: string,
  ) {
    return this.iframeGhlService.deleteTagsCustomFields(agentId, actionId);
  }

  @Post('agents/:agentId/tags-custom-fields/:actionId/toggle')
  toggleTagsCustomFields(
    @Param('agentId') agentId: string,
    @Param('actionId') actionId: string,
  ) {
    return this.iframeGhlService.toggleTagsCustomFields(agentId, actionId);
  }

  @Post('sessions/:sessionId/chat/response/edit/message')
  editChatbotResponseMessage(
    @Param('sessionId') sessionId: string,
    @Body() editChatbotResponseActionDto: EditChatbotResponseActionDto,
    @Res() response: Response,
  ) {
    return this.iframeGhlService.editChatbotResponse(
      editChatbotResponseActionDto,
      response,
    );
  }

  @Get('/sessions/:sessionId')
  getSession(@Param('sessionId') sessionId: string, @Res() response: Response) {
    return this.iframeGhlService.getSession(sessionId, response);
  }

  @Get('agents/:agentId/session-history')
  getSessionHistory(
    @Param('agentId') agentId: string,
    @Query('page') page: number,
    @Query('limit') limit: number,
    @Query('createdBy')
    createdBy: 'createdByMe' | 'createdByOthers' | 'createdByAnyone',
    @Query('sortBy') sortBy: 'lastUsed' | 'lastCreated',
    @Query('saved') saved: 'true' | 'false' | 'all',
  ) {
    return this.iframeGhlService.getSessionHistory(
      agentId,
      page,
      limit,
      createdBy,
      sortBy,
      saved,
    );
  }
  @Get('conversations')
  getConversations(@Query('location') location: string) {
    // return this.iframeGhlService.getConversations(location);
    return {
      totalPage: 1,
      currentPage: 1,
      result: [
        {
          agentName: 'Agent Name',
          channelName: 'Chatbot retruiting channel',
          channel: 'SMS',
          lastMessage: 'Last Message sent by the contact or the agency',
          lastMessageAt: '2025-01-02T06:51:28.489Z',
          lastMessageStatus: 'delivered',
          rollover: true,
          rolloverReason: 'Error caused by the rollover',
          rolloverDate: '2025-02-02T07:17:08.525Z',
          contactId: 'Contact ID',
          channelAccountId: 'Channel Account ID',
          refUrl:
            'https://app.gohighlevel.com/v2/location/VGZndtOFHrO4NIELBQYo/contacts/detail/2LywU6bGXLaxsUldBnFK',
          contactName: 'John Doe',
        },
      ],
    };
  }
}
