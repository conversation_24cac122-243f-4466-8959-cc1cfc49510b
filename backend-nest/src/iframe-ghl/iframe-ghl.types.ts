export interface CrmCalendar {
  id: string; // calendar ID as stored in HighLevel
  isActive: boolean;
  locationId: string;
  name: string;
  description: string;
  slug: string;
}

// The extended details of the CrmCalendarConnection are stored in the Capri DB.
export interface CrmCalendarConnection extends CrmCalendar {
  accountId: string; // accountId of the Calendar connection as stored in Capri
  silent: boolean;
  readDetails: {
    actionId: string;
    prompt: string;
    connected: boolean; // This is fetched from iframeConfig.connected
  };
  writeDetails: {
    actionId: string;
    prompt: string;
    connected: boolean; // This is fetched from iframeConfig.connected
  };
}

export type TagOption = 'hasTag' | 'doesntHaveTag';

export interface Trigger {
  triggerId: string;
  triggerName: string;
  providerName: string;
  data: {
    subaccount?: string;
    channel?: string; // SMS, GMB, FB, IG, Live_Chat, WhatsApp
    tagOption?: string;
    tagValue?: string;
    task?: string;
    sourceIdentifier?: string;
    prompt?: string;
    history?: number;
    include?: string;
    exclude?: string;
    include_knowledge?: string;
    exclude_knowledge?: string;
    tagConditions?: Array<{
      tagOption: TagOption;
      tagValue: string;
    }>;
  };
  active: boolean;
}

export interface Tag {
  id: string;
  name: string;
  locationId: string;
}

export interface TagConnections extends Tag {
  accountId: string;
  actionId: string;
  prompt?: string;
  iframeConfig: {
    connected: boolean;
  };
}

export interface CustomFields {
  id: string;
  name: string;
  fieldKey: string;
  placeholder: string;
  dataType: string;
  position: number;
  picklistOptions: string[];
  picklistImageOptions: string[];
  isAllowedCustomOption: boolean;
  isMultiFileAllowed: boolean;
  maxFileLimit: number;
  locationId: string;
  model: string;
}

export interface CustomFieldsConnection extends CustomFields {
  accountId: string;
  actionId: string;
  prompt?: string;
  iframeConfig: {
    connected: boolean;
  };
}

export interface StandardFieldsConnection {
  accountId: string;
  actionId: string;
  fieldKeys: string[];
  iframeConfig: {
    connected: boolean;
  };
}
