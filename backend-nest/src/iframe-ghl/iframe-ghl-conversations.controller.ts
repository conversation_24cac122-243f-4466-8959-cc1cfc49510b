import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Query,
  Req,
  UnauthorizedException,
} from '@nestjs/common';
import {
  IframeGhlConversationsService,
  ContactMessage,
  Message,
  MessagesResponse,
} from './iframe-ghl-conversations.service';
import { SendMessageDto } from './dto/conversation.dto';
import { V1ConversationsService } from 'src/organization/services/conversations/v1-conversations.service';
import { TokensService } from 'src/tokens/tokens.service';
import { GetRolloverQueryParams } from 'src/organization/dto/conversations/conversations-params.dto';

@Controller('iframe/conversations')
export class IframeGhlConversationsController {
  constructor(
    private readonly iframeGhlConversationsService: IframeGhlConversationsService,
    private readonly conversationService: V1ConversationsService,
    private readonly tokenService: TokensService,
  ) {}

  /**
   * Get all conversations with optional filtering
   *
   * Supported filters:
   * - channels: Comma-separated list of channel values (FB, SMS, WhatsApp, Email, Live_Chat, GMB, IG)
   * - messageStatuses: Comma-separated list of message statuses (delivered, pending)
   * - rollover: Boolean flag for filtering by rollover status (true/false)
   * - dateFrom: ISO string for filtering messages from this date
   * - dateTo: ISO string for filtering messages to this date
   * - limit: Number of records per page
   *
   * Additional parameters:
   * - search: Optional search term to filter conversations. This can be used to search for a specific conversation by contact name, agent name or message content.
   */
  @Get('/all')
  async findAll(
    @Req() req: Request,
    @Query('location') location: string,
    @Query('page') page = 1,
    @Query('filters') filters: string = '{}',
    @Query('search') search = '',
  ): Promise<{
    totalPage: number;
    currentPage: number;
    result: ContactMessage[];
  }> {
    const token = await this.tokenService.getTokenByToken({
      token: req.headers['x-api-token'] as string,
    });
    if (!token) throw new UnauthorizedException('Invalid token');

    const filtersJson = JSON.parse(filters);

    const conversations = await this.conversationService.getRolloverConversations({
      channelAccountId: location,
      page: page,
      limit: +(filtersJson?.limit ?? 10),
      orgId: token.orgId,
      status: filtersJson?.rollover ? 'rollover': 'all',
      channels: filtersJson?.channels,
      dateFrom: filtersJson?.dateFrom,
      dateTo: filtersJson?.dateTo,
      search
    });
    return conversations;
  }

  @Get(':conversationId')
  async findOne(
    @Param('conversationId') conversationId: string,
    @Query('location') location: string,
  ): Promise<ContactMessage[]> {
    return this.iframeGhlConversationsService.findOne(conversationId, location);
  }

  @Get(':conversationId/messages')
  async getMessages(
    @Param('conversationId') conversationId: string,
    @Query('location') location: string,
  ): Promise<Message[]> {
    return await this.iframeGhlConversationsService.getMessages(
      conversationId,
      location,
    );
  }

  @Get(':conversationId/paginated-messages')
  async getPaginatedMessages(
    @Param('conversationId') conversationId: string,
    @Query('limit') limit = 20,
    @Query('location') location: string,
    @Query('lastMessageId') lastMessageId?: string,
  ): Promise<MessagesResponse> {
    return await this.iframeGhlConversationsService.getPaginatedMessages(
      conversationId,
      lastMessageId,
      limit,
      location,
    );
  }

  @Post(':conversationId/send')
  async sendMessage(
    @Param('conversationId') conversationId: string,
    @Query('location') location: string,
    @Body() messageData: SendMessageDto,
  ): Promise<Message> {
    return await this.iframeGhlConversationsService.sendMessage({
      conversationId,
      location,
      ...messageData,
    });
  }

  @Post(':conversationId/toggle-enable-bot')
  async toggleEnableBot(
    @Param('conversationId') conversationId: string,
    @Query('location') location: string,
    @Body() body: { enable: boolean },
  ): Promise<{ success: boolean }> {
    return this.iframeGhlConversationsService.toggleEnableBot(
      conversationId,
      location,
      body.enable,
    );
  }

  @Post(':conversationId/toggle-save-conversation')
  async toggleSaveConversation(
    @Req() req: Request,
    @Param('conversationId') conversationId: string,
    @Query('location') location: string,
    @Body() body: { save: boolean; agentId: string },
  ): Promise<{ success: boolean }> {
    const token = await this.tokenService.getTokenByToken({
      token: req.headers['x-api-token'] as string,
    });
    if (!token) throw new UnauthorizedException('Invalid token');

    return this.iframeGhlConversationsService.toggleSaveConversation(
      conversationId,
      location,
      body.agentId,
      token.orgId,
      body.save,
    );
  }

  @Get('emails/:emailId')
  async getEmailById(
    @Param('emailId') emailId: string,
    @Query('location') location: string,
  ): Promise<ContactMessage[]> {
    return this.iframeGhlConversationsService.getEmailById(emailId, location);
  }
}
