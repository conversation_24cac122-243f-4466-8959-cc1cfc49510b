import { Injectable, NotFoundException } from '@nestjs/common';
import { PineconeService } from 'src/vector_db/pinecone/pinecone.service';
import { MongoAgentService } from 'src/mongo/service/agent/mongo-agent.service';
import { FAQ, JobType } from 'src/mongo/schemas/agents/agents.schema';
import { v4 as uuidv4 } from 'uuid';
import axios from 'axios';
import {
  HandleResourceDto,
  MessageStatus,
} from 'src/agent/dto/handle-resourc.dto';
import { KnowledgeService } from 'src/voice/vapi/knowledge/knowledge.service';

@Injectable()
export class IframeHelperService {
  constructor(
    private readonly pineconeService: PineconeService,
    private readonly mongoAgentService: MongoAgentService,
    private readonly knowledgeService: KnowledgeService,
  ) {}

  async handleResourceStatus(agentId: string, payload: HandleResourceDto) {
    try {
      const {
        jobId,
        accountId,
        characterCount,
        status,
        fileName,
        jobType,
        textContent,
        failedReason,
        maxDepth = 1,
      } = payload;
      const agent = await this.mongoAgentService.getAgent({ _id: agentId });
      if (!agent) {
        throw new NotFoundException(`Agent with ID ${agentId} not found`);
      }
      const { orgId } = agent;
      if (status === 'success') {
        // upsert data to pinecone
        await this.pineconeService.upsert(
          { accountId, agentId, orgId },
          textContent,
        );
        // upload to vapi
        let voiceFile = await this.knowledgeService.uploadVapiFile({
          agentId,
          fileContent: textContent,
          fileName,
        });
        // put the job from processing to processed
        await this.mongoAgentService.updateAgent(
          { _id: agentId },
          {
            $pull: { processingJobs: { jobId } },
            $push: {
              processedFiles: {
                accountId,
                fileName,
                characterCount,
                jobType,
                voiceFileId: voiceFile.vapiFileId,
                maxDepth,
              },
            },
          },
        );
      } else if (status === 'failed') {
        //TODO : Handle for when the resouce processing returns a failed status
      }
      return {
        status: 'success',
      };
    } catch (error) {
      
    }
  }

  private async processMultipleWebsites(
    scraping_id: string,
    agentId: string,
    orgId: string,
    urls: string[],
    processingJobs: any[],
  ) {
    try {
      const response = await axios.post(
        `${process.env.WEBSCRAPER_URL}/scrape-urls`,
        { scraping_id, urls },
      );

      const scrapedResults = response.data.map((item: any) => ({
        accountId: uuidv4(),
        url: item.url,
        content: item.content.content,
      }));

      await Promise.all([
        ...scrapedResults.map(({ accountId, ...rest }) =>
          this.pineconeService.upsert(
            { orgId, accountId, agentId },
            JSON.stringify(rest),
          ),
        ),
        ...processingJobs.map((job, index) =>
          this.handleResourceStatus(agentId, {
            ...job,
            characterCount: scrapedResults[index].content.length,
            textContent: scrapedResults[index].content,
            status: MessageStatus.SUCCESS,
          }),
        ),
      ]);
    } catch (error) {
      await Promise.all(
        processingJobs.map((job) =>
          this.handleResourceStatus(agentId, {
            ...job,
            characterCount: 0,
            textContent: '',
            status: MessageStatus.FAILED,
            failedReason: `Failed to scrape website ${job.fileName}`,
          }),
        ),
      );
    }
  }

  async handleNewFaq(agentId: string, faqs: FAQ[] = []) {
    try {
      if (!agentId || !faqs) {
        throw new Error('Agent ID and FAQs are required');
      }

      // Step 1: Retrieve agent data
      const agentData = await this.mongoAgentService.getAgent({ _id: agentId });
      if (!agentData) {
        throw new Error('Agent not found');
      }

      const { faqs: oldFaqs = [], orgId } = agentData;

      // Step 2: Delete old FAQs from Pinecone
      let idsToDelete: string[] = [];
      if (Array.isArray(oldFaqs) && oldFaqs.length > 0) {
        idsToDelete = [...new Set(oldFaqs.map((faq) => faq.id))];
        

        const deleteResult = await this.pineconeService.deleteWithMetadata(
          idsToDelete,
        );
        if (!deleteResult.success) {
          throw new Error('Failed to delete old FAQs from Pinecone');
        }
      }

      // Step 3: Add new records to Pinecone
      const upsertPromises = faqs.map((faq) => {
        const { id, ...faqWithoutId } = faq;
        return this.pineconeService.upsert(
          {
            orgId,
            accountId: id,
            agentId,
          },
          JSON.stringify(faqWithoutId),
        );
      });

      // Wait for all upserts to complete
      await Promise.all(upsertPromises);

      // Step 4: Update agent with new FAQs
      await this.mongoAgentService.updateAgent({ _id: agentId }, { faqs });

      return {
        success: true,
        faqs,
      };
    } catch (error) {
      
      throw new Error(
        error instanceof Error
          ? error.message
          : 'An unexpected error occurred while updating FAQs',
      );
    }
  }

  async addRawTextKnowledgeSource(agentId: string, rawTextContent: string) {
    const { textContentKnowledgeSource, orgId } =
      await this.mongoAgentService.getAgent({ _id: agentId });
    let body: { accountId: string; textContent: string } | null = null;
    if (!textContentKnowledgeSource) {
      body = {
        accountId: uuidv4(),
        textContent: rawTextContent,
      };
    } else {
      body = {
        accountId: textContentKnowledgeSource.accountId,
        textContent: rawTextContent,
      };
      await this.pineconeService.deleteWithMetadata([
        textContentKnowledgeSource.accountId,
      ]);
    }
    

    await this.pineconeService.upsert(
      {
        orgId,
        accountId: body.accountId,
        agentId,
      },
      JSON.stringify(rawTextContent),
    );
    await this.mongoAgentService.updateAgent(
      { _id: agentId },
      { textContentKnowledgeSource: body },
    );
    return body;
  }

  async fetchAllLinks(website: string) {
    const endpoint = `${process.env.WEBSCRAPER_URL}/extract-links`;
    const body = { url: website };
    try {
      const response = await axios.post(endpoint, body);
      if (response.status === 200) {
        const links = response.data?.links;
        return links;
      }
    } catch (error) {
      throw error?.message || 'Fail to retrieve links';
    }
  }

  async scrapeUrls(agentId: string, urls: string[]) {
    const agentData = await this.mongoAgentService.getAgent({ _id: agentId });
    const { orgId } = agentData;

    let scraping_id = uuidv4();

    const processingJobs = urls.map((url) => ({
      jobId: 'manual-processing-website',
      accountId: uuidv4(),
      fileName: url,
      jobType: JobType.WEBSITE_SCRAP,
      maxDepth: 0,
    }));

    try {
      await this.mongoAgentService.updateAgent(
        { _id: agentId },
        { $push: { processingJobs: { $each: processingJobs } } },
      );

      // Return early response indicating processing started
      this.processMultipleWebsites(
        scraping_id,
        agentId,
        orgId,
        urls,
        processingJobs,
      );

      return {
        success: true,
        message: 'Website processing started',
        processingJobs,
      };
    } catch (error) {
      throw error?.message || `Failed to initiate processing for ${urls}`;
    }
  }

  async removeKnowledgeSourceProcessedFiles(
    agentId: string,
    accountId: string,
  ) {
    await this.pineconeService.deleteWithMetadata([accountId]);
    const agent = await this.mongoAgentService.updateAgent(
      { _id: agentId },
      {
        $pull: {
          processedFiles: { accountId },
        },
      },
      { new: false },
    );
    const file = agent?.processedFiles?.find(
      (file) => file.accountId === accountId,
    );
    if (agent?.voiceConfig?.assistantId && file?.voiceFileId) {
      await this.knowledgeService.deleteVapiFile(file.voiceFileId, agentId);
      await this.mongoAgentService.updateAgent(
        { _id: agentId },
        {
          'voiceConfig.files': {
            $pull: {
              providerFileId: file.voiceFileId,
            },
          },
        },
      );
    }
    if (!agent) {
      throw new NotFoundException('Agent does not exist.');
    }
    return { success: true };
  }
}
