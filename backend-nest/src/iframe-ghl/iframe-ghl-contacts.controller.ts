import { Controller, Get, Param, Query } from '@nestjs/common';
import { Contact } from './iframe-ghl-conversations.service';
import { IframeGhlContactsService } from './iframe-ghl-contacts.service';

@Controller('iframe/contacts')
export class IframeGhlContactsController {
  constructor(
    private readonly iframeGhlContactsService: IframeGhlContactsService,
  ) {}

  @Get(':contactId')
  async getContact(
    @Param('contactId') contactId: string,
    @Query('location') location: string,
  ): Promise<Contact> {
    return this.iframeGhlContactsService.getContact(contactId, location);
  }

  @Get(':contactId/conversations')
  async getConversationIdFromContactId(
    @Param('contactId') contactId: string,
    @Query('location') location: string,
  ): Promise<string> {
      return await this.iframeGhlContactsService.getConversationIdFromContactId(contactId, location);
  }
}
