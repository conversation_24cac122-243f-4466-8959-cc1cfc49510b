import { Test, TestingModule } from '@nestjs/testing';
import { IframeGhlController } from './iframe-ghl.controller';
import { IframeGhlService } from './iframe-ghl.service';

describe('IframeGhlController', () => {
  let controller: IframeGhlController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [IframeGhlController],
      providers: [IframeGhlService],
    }).compile();

    controller = module.get<IframeGhlController>(IframeGhlController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
