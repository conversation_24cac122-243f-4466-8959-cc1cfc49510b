import { Test, TestingModule } from '@nestjs/testing';
import { IframeGhlService } from './iframe-ghl.service';

describe('IframeGhlService', () => {
  let service: IframeGhlService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [IframeGhlService],
    }).compile();

    service = module.get<IframeGhlService>(IframeGhlService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
