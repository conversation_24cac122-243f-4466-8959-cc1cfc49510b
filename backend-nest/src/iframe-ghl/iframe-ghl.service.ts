import { HttpStatus, Injectable, NotFoundException } from '@nestjs/common';
import { Response } from 'express';
import {
  AddGhlTagResponseWithActionDto,
  CreateSessionDto,
  GetRecentSessionsDto,
  SendMessageDto,
  UpdateGhlCalendarActionDto,
  UpdateSessionMessageDto,
  UpdateSessionNameDto,
} from './dto/iframe-sessions.dto';
import { SessionService } from 'src/session/session.service';
import { SessionListService } from 'src/session/outreachService/session-list.service';
import { MongoAgentService } from 'src/mongo/service/agent/mongo-agent.service';
import { UpdateAgentDto } from './dto/update-agent.dto';
import { SendNewMessageDto } from './dto/send-new-message.dto';
import { AddFaqDto, EditFaqDto } from './dto/faq.dto';
import { v4 as uuidv4 } from 'uuid';
import { IframeHelperService } from './iframe-helper.service';
import { EditTextContentKnowledgeSourceDto } from './dto/text-content.dto';
import { AgentFileUpload } from 'src/agent/file-upload.service';
import { AgentDocument, JobType } from 'src/mongo/schemas/agents/agents.schema';
import {
  CrmCalendar,
  CrmCalendarConnection,
  CustomFields,
  CustomFieldsConnection,
  StandardFieldsConnection,
  Tag,
} from './iframe-ghl.types';
import { EditCrmCalendarDto } from './dto/edit-crm-calendar.dto';
import { AddCrmCalendarDto } from './dto/add-crm-calendar.dto';
import { AddTriggerDto } from './dto/add-trigger.dto';
import { EditTriggerDto } from './dto/edit-trigger.dto';
import { AddTagsCustomFieldsDto } from './dto/add-tags-custom-fields.dto';
import { EditTagsCustomFieldsDto } from './dto/edit-tags-custom-fields.dto';
import { GhlApisService } from 'src/api-client/services/ghl-apis/ghl-apis.service';
import { MongoCredentialsService } from 'src/mongo/service/credentials/mongo-credentials.service';
import { KINDS, PROVIDERS, VISIBILITY_CODES } from 'src/lib/constant';
import { OrganizationService } from 'src/organization/services/organization.service';

import { AddAgentDto } from './dto/add-agent.dto';
import { TokensService } from 'src/tokens/tokens.service';
import { FoldersService } from 'src/folders/folders.service';
import { EditChatbotResponseActionDto } from './dto/edit-response-action.dto';
import { ToggleAgentStatusDto } from './dto/toggle-agent-status.dto';

@Injectable()
export class IframeGhlService {
  constructor(
    private readonly sessionService: SessionService,
    private readonly sessionListService: SessionListService,
    private readonly mongoAgentService: MongoAgentService,
    private readonly iframeHelperService: IframeHelperService,
    private readonly fileUploadService: AgentFileUpload,
    private readonly ghlApisService: GhlApisService,
    private readonly credentialService: MongoCredentialsService,
    private readonly organizationService: OrganizationService,
    private readonly tokenService: TokensService,
    private readonly foldersService: FoldersService,
    private readonly mongoCredentialsService: MongoCredentialsService,
  ) {}
  async createSession(createSessionDto: CreateSessionDto, response: Response) {
    try {
      const newSession = await this.sessionService.createSession(
        createSessionDto,
      );
      if (newSession) {
        return response.status(HttpStatus.CREATED).json({
          message: 'Session Successfully Started',
          data: newSession,
        });
      }
    } catch (error) {
      return `Error creating session in iframe ${error}`;
    }
  }
  findAll(recentSessionsDto: GetRecentSessionsDto) {
    const { agentId, orgId, offset, limit, sortBy, saved } = recentSessionsDto;
    return this.sessionListService.fetchSessions({
      offset,
      limit,
      createdBy: 'createdByMe',
      sortBy,
      agent: agentId,
      orgId,
      saved,
    });
  }

  async findOne(sessionId: string, response) {
    await this.sessionService.getSession(sessionId, response);
    // return `This action returns a session bearing ID: #${sessionId}`;
  }

  async updateSessionName(
    sessionId: string,
    response,
    updateSessionName: UpdateSessionNameDto,
  ) {
    await this.sessionService.updateName(
      sessionId,
      response,
      updateSessionName,
    );
    // return `This action updates a #${sessionId} session's name`;
  }

  async saveSession(sessionId: string, response) {
    await this.sessionService.saveChat(sessionId, response);
    // return `This action updates a #${sessionId} session's status to ${updateStatusDto.saved}`;
  }

  async unSaveSession(sessionId: string, response) {
    await this.sessionService.unSaveChat(sessionId, response);
    // return `This action updates a #${sessionId} session's status to ${updateStatusDto.saved}`;
  }

  async sendSessionMessage(
    sessionId: string,
    sendMessageDto: SendMessageDto,
    response: Response,
  ) {
    const queryDto = {
      sessionId,
      ...sendMessageDto,
    };
    await this.sessionService.sendMessage(queryDto, response);
  }

  async updateSessionMessages(
    sessionId: string,
    updateSessionName: UpdateSessionMessageDto,
    response,
  ) {
    const newEditMessageDto = {
      ...updateSessionName,
      sessionId,
    };
    await this.sessionService.editMessage(newEditMessageDto, response);
  }

  async addSessionGhlCalendar(
    sessionId: string,
    updateSessionName: UpdateGhlCalendarActionDto,
    response,
  ) {
    const newUpdatedSessionName = {
      ...updateSessionName,
      sessionId,
    };
    await this.sessionService.addGhlCalendar(newUpdatedSessionName, response);
    // return `This action updates a #${sessionId} session's GHL Calendar`;
  }

  async updateSessionGhlCalendar(
    sessionId: string,
    updateSessionName: UpdateGhlCalendarActionDto,
    response,
  ) {
    const newUpdatedSessionName = {
      ...updateSessionName,
      sessionId,
    };
    await this.sessionService.editGhlCalendar(newUpdatedSessionName, response);
    // return `This action updates a #${sessionId} session's GHL Calendar`;
  }

  async addGhlTagResponse(
    sessionId: string,
    adGhlTagResponseDto: AddGhlTagResponseWithActionDto,
    response,
  ) {
    const newAddGhlTagResponseDto = {
      ...adGhlTagResponseDto,
      sessionId,
    };
    await this.sessionService.addGhlTag(newAddGhlTagResponseDto, response);
    // return `This action updates a #${sessionId} session's tag`;
  }

  async remove(sessionId: string, response) {
    await this.sessionService.deleteSession(sessionId, response);
    // return `This action removes a #${sessionId} session`;
  }

  async removeBotResponse(sessionId: string, eventId: string, response) {
    const newDto = {
      sessionId,
      eventId,
    };
    await this.sessionService.deleteDataSource(newDto, response);
    // return `This action removes a #${sessionId} session's bot response`;
  }

  async createAgent(addAgentDto: AddAgentDto, token: string) {
    const { name, prompt, templateId, location } = addAgentDto;
    // TODO: What should be the userId?
    const userId = '';
    const tokenDoc = await this.tokenService.getTokenByToken({
      token: token,
    });
    const orgId = tokenDoc.orgId;
    
    const newAgent = await this.mongoAgentService.createAgent(userId, {
      actions: [],
      agentName: name,
      aiProvider: {},
      disabled: true,
      orgId,
      faqs: [],
      triggers: [],
    });
    const agentId = newAgent._id?.toString();
    if (!agentId) {
      throw new NotFoundException('Agent not found');
    }
    await this.mongoAgentService.pushNewPrompt({
      agentId,
      prompt: {
        name: 'Default Prompt',
        promptContent: prompt,
        isFallbackPrompt: false,
        customFallbackPrompt: null,
      },
    });
    // Push agentId to the the channel object whose keyId matches the location
    // Push agentId to the agentId array
    await this.organizationService.updateOrganisation(
      { _id: orgId, 'connections.channels.keyId': location },
      {
        $push: { 'connections.channels.$.agentIds': agentId, agents: agentId },
      },
    );
    // Add the agent to the folder
    await this.foldersService.addResourceToFolder({
      resourceId: newAgent._id?.toString(),
      type: 'agents',
      folderId: null,
      organization: orgId,
    });
    
    return { _id: agentId };
  }

  async getTagsConnections(agentId: string, location: string) {
    try {
      // Get the agent
      const agent = await this.mongoAgentService.getAgent({ _id: agentId });
      if (!agent) {
        throw new NotFoundException('Agent not found');
      }

      // Get GHL credentials for the location
      const credentialData = await this.credentialService.getCredential({
        keyId: location,
        kind: KINDS.GHL_CREDENTIAL,
      });

      if (!credentialData?.creds?.accessToken) {
        throw new Error('No GHL token found for location');
      }

      const tokens = {
        access_token: credentialData.creds?.accessToken,
        refresh_token: credentialData.creds?.refreshToken,
      };

      // Fetch tags from GHL API
      const { tags } = await this.ghlApisService.getTags(tokens, location); // Destructure tags from response

      // Filter actions to get only tag actions
      const tagActions = agent.actions.filter(
        (action) => action.providerName === 'ghl' && action.activity === 'tag',
      );

      // Only return tags that have corresponding actions in the agent
      const tagConnections = tags
        .filter((tag) =>
          tagActions.some((action) => action.accountId === tag.id),
        )
        .map((tag) => {
          // Find matching action for this tag
          const action = tagActions.find(
            (action) => action.accountId === tag.id,
          );

          return {
            ...tag,
            accountId: action.accountId,
            actionId: action.actionId,
            prompt: action.promptContent,
            iframeConfig: {
              connected: action.iframeConfig?.connected ?? false,
            },
          };
        });

      return tagConnections;
    } catch (error) {
      
      throw new Error(`Failed to fetch tag connections: ${error.message}`);
    }
  }

  async getAgentNameStatus(location: string, name: string) {
    const agent = await this.mongoAgentService.getAgent({ agentName: name });
    if (agent) {
      return {
        nameTaken: true,
      };
    }
    return {
      nameTaken: false,
    };
  }

  async getAgents(location: string): Promise<{
    channelConfigured: boolean;
    canCreateAgent: boolean;
    canViewConversations: boolean;
    agents: AgentDocument[];
  }> {
    try {
      // Step 1: Find organization with matching location in channels
      const organization = await this.organizationService.getOrganization({
        'connections.channels.keyId': location,
      });

      const canCreateAgent = organization.iframeConfig.visibilityCodes.includes(
        VISIBILITY_CODES.AGENT.CREATE,
      );
      const canViewConversations =
        organization.iframeConfig.visibilityCodes.includes(
          VISIBILITY_CODES.AGENT.CONVERSATION,
        );

      if (!organization) {
        return {
          channelConfigured: false,
          canCreateAgent,
          canViewConversations,
          agents: [],
        };
      }

      // Step 2: Get agentIds from the matching channel
      const channel = organization.connections.channels.find(
        (channel) => channel.keyId === location,
      );

      if (!channel?.agentIds?.length) {
        return {
          channelConfigured: true,
          canCreateAgent,
          canViewConversations,
          agents: [],
        };
      }

      // Step 3: Get agents using the agentIds
      const agents = await this.mongoAgentService.getAgents({
        _id: { $in: channel.agentIds },
      });

      return {
        channelConfigured: true,
        canCreateAgent,
        canViewConversations,
        agents,
      };
    } catch (error) {
      
      throw new Error(`Failed to fetch agents: ${error.message}`);
    }
  }

  async getAgent(agentId: string, location: string) {
    const agent = await this.mongoAgentService.getAgent({ _id: agentId });
    const org = await this.organizationService.getOrganization({
      _id: agent.orgId,
    });
    const visibilityCodes = org.iframeConfig.visibilityCodes;
    return { ...agent, visibilityCodes };
  }

  async toggleAgentStatus(
    agentId: string,
    toggleAgentStatusDto: ToggleAgentStatusDto,
  ) {
    const { disabled } = toggleAgentStatusDto;
    return this.mongoAgentService.updateAgent(
      { _id: agentId },
      { $set: { disabled } },
    );
  }

  async updateAgent(agentId: string, updateAgentDto: UpdateAgentDto) {
    const { agentName, prompt } = updateAgentDto;

    // Get current agent to access the prompts
    const currentAgent = await this.mongoAgentService.getAgent({
      _id: agentId,
    });

    // Build the update object
    const update = {
      $set: {},
    };

    // Basic field updates
    if (agentName) {
      update.$set['agentName'] = agentName;
    }

    // Handle prompt update
    if (prompt) {
      // Set up query to match the active prompt
      const query = {
        _id: agentId,
        'prompts.prompt.promptId': currentAgent.prompts.currentActive,
      };

      update.$set['prompts.prompt.$.promptContent'] = prompt;

      // Perform the update using the service method
      const updatedAgent = await this.mongoAgentService.updateAgent(
        query,
        update,
        { new: true },
      );

      if (!updatedAgent) {
        throw new NotFoundException(`Agent with ID ${agentId} not found`);
      }

      return updatedAgent;
    }

    // If only updating agentName (no prompt update)
    if (Object.keys(update.$set).length > 0) {
      const updatedAgent = await this.mongoAgentService.updateAgent(
        { _id: agentId },
        update,
        { new: true },
      );

      if (!updatedAgent) {
        throw new NotFoundException(`Agent with ID ${agentId} not found`);
      }

      return updatedAgent;
    }

    return currentAgent;
  }

  async sendNewMessageToAgent(sendNewMessageDto: SendNewMessageDto) {
    const { sessionId } = sendNewMessageDto;
    const dummy: any = {
      eventId: '71917d44-bd96-4777-a501-9ad02fc572c5',
      sender: 'human',
      message: 'This is a test',
      botResponse: [
        {
          eventId: '055021de-9c4a-407b-b8ac-5dfb0ec248ad',
          sender: 'bot',
          kind: 'slack',
          accountName: 'slack-integration-test-5',
          accountId: '708ddb4d-b7d7-4d2f-b594-f265d0355cd6',
          deleted: false,
          action: 'write',
          eventData: {
            slackResponse: {
              type: 'predictive',
              text: "No problem! If you need any further information or assistance, feel free to ask. I'm here to help!",
              channel_id: 'C07GZCEBF1N',
            },
          },
          channelId: 'C07GZCEBF1N',
          silent: false,
          timestamp: *************,
        },
        {
          eventId: '166a3bf1-29ba-4de2-a7d3-201e1b59e4e1',
          sender: 'bot',
          kind: 'message',
          action: 'read',
          eventData: {
            message:
              "Sorry, I don't think I'm the best person to answer that for you.",
          },
          timestamp: *************,
        },
      ],
      timestamp: *************,
    };
    if (!sessionId) {
      dummy.sessionId = '123';
    }
    return dummy;
  }

  async createNewSession(agentId: string) {
    const agent = await this.mongoAgentService.getAgent({ _id: agentId });
    const newSession = await this.sessionService.createSession({
      agentId,
      orgId: agent.orgId,
      userId: agent.userId,
    });
    return { sessionId: newSession?._id };
  }

  async addFaq(agentId: string, addFaqDto: AddFaqDto) {
    const agentData = await this.mongoAgentService.getAgent({ _id: agentId });
    const newFaq = { id: uuidv4(), ...addFaqDto };
    const updatedFaqs = [...agentData.faqs, newFaq];
    await this.iframeHelperService.handleNewFaq(agentId, updatedFaqs);
    return { success: true };
  }

  async editFaq(agentId: string, faqId: string, editFaqDto: EditFaqDto) {
    const agentData = await this.mongoAgentService.getAgent({ _id: agentId });
    const updatedFaqs = agentData.faqs.map((faq) =>
      faq.id === faqId ? { ...faq, ...editFaqDto } : faq,
    );
    await this.iframeHelperService.handleNewFaq(agentId, updatedFaqs);
    return { success: true };
  }

  async deleteFaq(agentId: string, faqId: string) {
    const agentData = await this.mongoAgentService.getAgent({ _id: agentId });
    const faqsNotToDelete = agentData.faqs.filter((faq) => faq.id !== faqId);
    await this.iframeHelperService.handleNewFaq(agentId, faqsNotToDelete);
    return { success: true };
  }

  async editTextContentKnowledgeSource(
    agentId: string,
    editTextContentKnowledgeSourceDto: EditTextContentKnowledgeSourceDto,
  ) {
    const { textContent } = editTextContentKnowledgeSourceDto;
    await this.iframeHelperService.addRawTextKnowledgeSource(
      agentId,
      textContent,
    );
    return { success: true };
  }

  async getLinks(agentId: string, url: string) {
    const links = await this.iframeHelperService.fetchAllLinks(url);
    const linksFormatted = links.map((link: string) => ({
      url: link,
      title: link,
    }));
    return linksFormatted;
  }

  async scrapeUrls(agentId: string, urls: string[]) {
    const response = await this.iframeHelperService.scrapeUrls(agentId, urls);
    return response;
  }

  async deleteWebsite(agentId: string, accountId: string) {
    const response =
      await this.iframeHelperService.removeKnowledgeSourceProcessedFiles(
        agentId,
        accountId,
      );
    return response;
  }

  async processFiles(agentId: string, files: Express.Multer.File[]) {
    if (files.length === 0) return;
    const { orgId } = await this.mongoAgentService.getAgent(
      { _id: agentId },
      { orgId: 1 },
    );
    const accountId = uuidv4();
    const jobDetails = await this.fileUploadService.processFiles(
      { agentId, orgId, accountId },
      files,
    );

    const processingJobs = jobDetails.map((job) => ({
      jobId: job.id,
      accountId,
      fileName: job.fileName,
      mimeType: job.mimeType,
      jobType: JobType.FILE_UPLOAD,
    }));

    await this.mongoAgentService.updateAgent(
      { _id: agentId },
      { $push: { processingJobs: { $each: processingJobs } } },
    );

    return { message: 'Files queued for processing', processingJobs };
  }

  async deleteFile(agentId: string, accountId: string) {
    const response =
      await this.iframeHelperService.removeKnowledgeSourceProcessedFiles(
        agentId,
        accountId,
      );
    return response;
  }

  /**
   * Get CRM Calendars
   * @param agentId - The ID of the agent as stored in Capri
   * @param locationId - The location of the HighLevel subaccount
   * @returns {CrmCalendar[]} An array of CRM Calendars
   * @description Get CRM Calendars from the HighLevel subaccount. This is fetched directly from the HighLevel API.
   */
  async getCrmCalendars(
    agentId: string,
    locationId: string,
  ): Promise<CrmCalendar[]> {
    try {
      const credentialData = await this.credentialService.getCredential({
        keyId: locationId,
        kind: KINDS.GHL_CREDENTIAL,
      });

      if (!credentialData?.creds?.accessToken)
        throw new Error('No GHL token found for location');

      const tokens = {
        access_token: credentialData.creds?.accessToken,
        refresh_token: credentialData.creds?.refreshToken,
      };

      // Fetch calendars from GHL API
      const calendarsData = await this.ghlApisService.getGhlCalendars(
        tokens,
        locationId,
      );

      // Transform the response to match our CrmCalendar interface
      const crmCalendars = calendarsData.calendars.map((calendar) => ({
        id: calendar.id,
        isActive: calendar.isActive ?? true,
        locationId: locationId,
        name: calendar.name,
        description: calendar.description || '',
        slug: calendar.slug,
      }));

      return crmCalendars;
    } catch (error) {
      
      throw new Error(`Failed to fetch CRM calendars: ${error.message}`);
    }
  }

  /**
   * Get CRM Calendars Connections
   * @param agentId - The ID of the agent as stored in Capri
   * @param locationId - The location of the HighLevel subaccount
   * @returns {CrmCalendarConnection[]} An array of CRM Calendars Connections
   * @description Get CRM Calendars Connections from the Capri DB. The Calendar Details (like id, slug, name, description) are fetched from the HighLevel API.
   */
  async getCrmCalendarsConnections(
    agentId: string,
    locationId: string,
  ): Promise<CrmCalendarConnection[]> {
    const agent = await this.mongoAgentService.getAgent({ _id: agentId });
    const org = await this.organizationService.getOrganization({
      _id: agent.orgId,
    });

    // Get calendar actions from agent
    const calendarActions = agent.actions.filter(
      (action) => action.providerName === PROVIDERS.GHL_CALENDAR,
    );

    // Get accountIds from calendar actions
    const actionAccountIds = calendarActions.map((action) => action.accountId);

    // Find calendars in org.connections.datasources that match agent's actions
    const calendars = org.connections.dataSources.filter(
      (datasource) =>
        datasource.keyId === locationId &&
        actionAccountIds.includes(datasource.accountId),
    );

    if (calendars.length === 0) {
      return [];
    }

    const credentialData = await this.credentialService.getCredential({
      keyId: locationId,
      kind: KINDS.GHL_CREDENTIAL,
    });

    if (!credentialData?.creds?.accessToken)
      throw new Error('No GHL token found for location');

    const tokens = {
      access_token: credentialData.creds?.accessToken,
      refresh_token: credentialData.creds?.refreshToken,
    };

    // Get calendar details from HighLevel API
    const calendarDetails = await this.ghlApisService.getGhlCalendars(
      tokens,
      locationId,
    );

    // Map calendar details to connections, only for calendars in agent's actions
    const crmCalendarConnections = calendarDetails.calendars
      .filter((calendar) => calendars.some((c) => c.calendarId === calendar.id))
      .map((calendar) => {
        const orgCalendar = calendars.find((c) => c.calendarId === calendar.id);

        // Find read and write actions separately
        const readAction = calendarActions.find(
          (action) =>
            action.accountId === orgCalendar.accountId &&
            action.activity === 'read',
        );

        const writeAction = calendarActions.find(
          (action) =>
            action.accountId === orgCalendar.accountId &&
            action.activity === 'write',
        );

        return {
          id: calendar.id,
          isActive: calendar.isActive,
          locationId: locationId,
          name: calendar.name,
          description: calendar.description,
          slug: calendar.slug,
          accountId: orgCalendar.accountId,
          silent: readAction?.silent || writeAction?.silent,
          readDetails: {
            actionId: readAction?.actionId,
            prompt: readAction?.promptContent,
            connected: readAction?.iframeConfig?.connected,
          },
          writeDetails: {
            actionId: writeAction?.actionId,
            prompt: writeAction?.promptContent,
            connected: writeAction?.iframeConfig?.connected,
          },
        };
      });

    return crmCalendarConnections;
  }

  /**
   * Add CRM Calendar
   * @param agentId - The ID of the agent as stored in Capri
   * @param addCrmCalendarDto - The details to add
   * @returns {{ success: boolean }} True if the CRM Calendar was added successfully
   * @description Add a CRM Calendar to the HighLevel subaccount. Create the read and write action in the Capri DB action schema.
   */
  async addCrmCalendar(
    agentId: string,
    addCrmCalendarDto: AddCrmCalendarDto,
  ): Promise<{ success: boolean }> {
    const { id, readPrompt, writePrompt, silent } = addCrmCalendarDto;

    try {
      // Get the organization ID from the agent
      const agent = await this.mongoAgentService.getAgent({ _id: agentId });

      // Generate unique accountId for this calendar connection
      const accountId = uuidv4();
      const credentialData = await this.mongoCredentialsService.getCredential({
        keyId: addCrmCalendarDto.location,
        kind: KINDS.GHL_CREDENTIAL,
      });

      const credentialId = credentialData?._id.toString();

      const tokens = {
        access_token: credentialData.creds?.accessToken,
        refresh_token: credentialData.creds?.refreshToken,
      };

      const { location = undefined } = await this.ghlApisService.getGhlLocation(
        tokens,
        addCrmCalendarDto.location,
      );

      const { calendar } = await this.ghlApisService.getGhlCalendars(
        tokens,
        addCrmCalendarDto.location,
        id,
      );

      // Add calendar connection to organization
      await this.organizationService.updateOrganisation(
        { _id: agent.orgId },
        {
          $push: {
            'connections.dataSources': {
              accountId,
              userId: agent.userId,
              providerName: PROVIDERS.GHL_CALENDAR,
              credentialId,
              timezone: location.timezone,
              keyId: location.id,
              calendarId: id,
              author: location.name,
              reference: `https://link.msgsndr.com/widget/booking/${id}`,
              name: `${calendar.name} - ${location.name}`,
            },
          },
        },
      );

      // Add read action

      const readAction = {
        actionId: uuidv4(),
        accountId,
        providerName: PROVIDERS.GHL_CALENDAR,
        promptContent: readPrompt,
        activity: 'read' as const,
        accountName: `${calendar.name} - ${location.name}`,
        silent,
        isAdvancedSettings: false,
        deleted: false, // Add the deleted property
        iframeConfig: { connected: true },
        metaData: {
          ghlCalendarMetaData: {
            evaluateOn: 'everyTurn' as const,
            dayRange: 7, // Default to 7 days
            maxRange: 30, // Default max range
          },
        },
      };

      // Add write action
      const writeAction = {
        actionId: uuidv4(),
        accountId,
        providerName: PROVIDERS.GHL_CALENDAR,
        promptContent: writePrompt,
        activity: 'write' as const,
        accountName: `${calendar.name} - ${location.name}`,
        silent,
        isAdvancedSettings: false,
        deleted: false, // Add the deleted property
        iframeConfig: { connected: true },
        metaData: {
          ghlCalendarMetaData: {
            evaluateOn: 'everyTurn' as const,
            dayRange: 7,
            maxRange: 30,
          },
        },
      };

      // Add read datasource
      await this.mongoAgentService.addDatasources({
        agentId,
        action: readAction,
      });

      // Add write datasource
      await this.mongoAgentService.addDatasources({
        agentId,
        action: writeAction,
      });

      return { success: true };
    } catch (error) {
      
      throw new Error(`Failed to add CRM calendar: ${error.message}`);
    }
  }

  /**
   * Disconnect CRM Calendar
   * @param agentId - The ID of the agent as stored in Capri
   * @param accountId - The accountId of the CRM Calendar Connection as stored in Capri
   * @returns {{ success: boolean }} True if the CRM Calendar Connection was disconnected successfully
   * @description Disconnect a CRM Calendar Connection from the HighLevel subaccount. Switch the `iframeConfig.connected` to false.
   */
  async toggleCrmCalendarConnection(
    agentId: string,
    accountId: string,
  ): Promise<{ success: boolean; newConnectionStatus: boolean }> {
    // Get the agent to ensure it exists
    const agent = await this.mongoAgentService.getAgent({ _id: agentId });

    if (!agent) {
      throw new Error('Agent not found');
    }

    const currentConnectionStatus = agent.actions
      .filter((action) => action.accountId === accountId)
      .map((action) => action.iframeConfig?.connected);

    const isPartiallyConnected = currentConnectionStatus.some(
      (status) => status === true,
    );

    const isFullyConnected = currentConnectionStatus.every(
      (status) => status === true,
    );

    const newConnectionStatus = isFullyConnected
      ? false
      : isPartiallyConnected
      ? false
      : true;

    // Update all actions for this calendar connection to set connected: false
    await this.mongoAgentService.updateAgent(
      { _id: agentId },
      {
        $set: {
          'actions.$[elem].iframeConfig.connected': newConnectionStatus,
        },
      },
      {
        arrayFilters: [{ 'elem.accountId': accountId }],
        multi: true,
      },
    );

    return { success: true, newConnectionStatus };
  }

  /**
   * Edit CRM Calendar
   * @param agentId - The ID of the agent as stored in Capri
   * @param accountId - The accountId of the CRM Calendar Connection as stored in Capri
   * @param editCrmCalendarDto - The details to update
   * @returns {{ success: boolean }} True if the CRM Calendar was updated successfully
   * @description Update the CRM Calendar Connection in the Capri DB.
   */
  async editCrmCalendar(
    agentId: string,
    accountId: string,
    editCrmCalendarDto: EditCrmCalendarDto,
  ): Promise<{ success: boolean }> {
    const { readPrompt, writePrompt, silent } = editCrmCalendarDto;

    try {
      // Get the agent to ensure it exists
      const agent = await this.mongoAgentService.getAgent({ _id: agentId });

      if (!agent) {
        throw new Error('Agent not found');
      }

      // Update read action
      await this.mongoAgentService.updateAgent(
        { _id: agentId },
        {
          $set: {
            'actions.$[elem].promptContent': readPrompt,
            'actions.$[elem].silent': silent,
          },
        },
        {
          arrayFilters: [
            {
              'elem.accountId': accountId,
              'elem.providerName': PROVIDERS.GHL_CALENDAR,
              'elem.activity': 'read',
            },
          ],
        },
      );

      // Update write action
      await this.mongoAgentService.updateAgent(
        { _id: agentId },
        {
          $set: {
            'actions.$[elem].promptContent': writePrompt,
            'actions.$[elem].silent': silent,
          },
        },
        {
          arrayFilters: [
            {
              'elem.accountId': accountId,
              'elem.providerName': PROVIDERS.GHL_CALENDAR,
              'elem.activity': 'write',
            },
          ],
        },
      );

      return { success: true };
    } catch (error) {
      
      throw new Error(`Failed to edit CRM calendar: ${error.message}`);
    }
  }

  /**
   * Delete CRM Calendar
   * @param agentId - The ID of the agent as stored in Capri
   * @param accountId - The accountId of the CRM Calendar Connection as stored in Capri
   * @returns {{ success: boolean }} True if the CRM Calendar Connection was deleted successfully
   * @description Delete the CRM Calendar Connection from the Capri DB.
   */
  async deleteCrmCalendar(
    agentId: string,
    accountId: string,
  ): Promise<{ success: boolean }> {
    try {
      // Get the agent to ensure it exists
      const agent = await this.mongoAgentService.getAgent({ _id: agentId });

      if (!agent) {
        throw new Error('Agent not found');
      }

      // Remove calendar connection from organization's dataSources
      await this.organizationService.updateOrganisation(
        { _id: agent.orgId },
        {
          $pull: {
            'connections.dataSources': {
              accountId: accountId,
              providerName: PROVIDERS.GHL_CALENDAR,
            },
          },
        },
      );

      // Mark actions as deleted in the agent's actions array
      await this.mongoAgentService.updateAgent(
        { _id: agentId },
        {
          $set: {
            'actions.$[elem].deleted': true,
            'actions.$[elem].iframeConfig.connected': false,
          },
        },
        {
          arrayFilters: [
            {
              'elem.accountId': accountId,
              'elem.providerName': PROVIDERS.GHL_CALENDAR,
            },
          ],
          multi: true,
        },
      );

      return { success: true };
    } catch (error) {
      
      throw new Error(`Failed to delete CRM calendar: ${error.message}`);
    }
  }

  /**
   * Add Trigger
   * @param agentId - The ID of the agent as stored in Capri
   * @param addTriggerDto - The details to add
   * @returns {{ success: boolean }} True if the Trigger was added successfully
   * @description Add a Trigger. Follow the normal trigger creation process.
   */
  async addTrigger(
    agentId: string,
    addTriggerDto: AddTriggerDto,
  ): Promise<{ success: boolean }> {
    try {
      const agent = await this.mongoAgentService.getAgent({ _id: agentId });
      if (!agent) {
        throw new NotFoundException('Agent not found');
      }

      //using location find the subaccount from organisation channels
      const organization = await this.organizationService.getOrganization({
        _id: agent.orgId,
      });
      const subaccount = organization.connections.channels.find(
        (channel) => channel.keyId === addTriggerDto.location,
      );

      const newTrigger = {
        triggerId: uuidv4(),
        triggerName: addTriggerDto.triggerName,
        providerName: 'ghl',
        data: {
          subaccount: subaccount.accountId,
          channel: addTriggerDto.channel || 'SMS',
          task: addTriggerDto.task || 'respond',
          prompt: addTriggerDto.prompt || '',
          history: addTriggerDto.history || 24,
          include: addTriggerDto.include || '',
          exclude: addTriggerDto.exclude || '',
          exclude_knowledge: addTriggerDto.excludeKnowledge || '',
          tagConditions: addTriggerDto.tagConditions || [],
        },
        followUp: addTriggerDto.followUp,
        active: addTriggerDto.active || true,
      };

      await this.mongoAgentService.updateAgent(
        { _id: agentId },
        { $push: { triggers: newTrigger } },
      );

      return { success: true };
    } catch (error) {
      
      throw new Error(`Failed to add trigger: ${error.message}`);
    }
  }

  /**
   * Edit Trigger
   * @param agentId - The ID of the agent as stored in Capri
   * @param triggerId - The ID of the Trigger as stored in Capri
   * @param editTriggerDto - The details to update
   * @returns {{ success: boolean }} True if the Trigger was updated successfully
   * @description Update the Trigger. Follow the normal trigger update process.
   */
  async editTrigger(
    agentId: string,
    triggerId: string,
    editTriggerDto: EditTriggerDto,
  ): Promise<{ success: boolean }> {
    try {
      const agent = await this.mongoAgentService.getAgent({ _id: agentId });
      if (!agent) {
        throw new NotFoundException('Agent not found');
      }

      const triggerExists = agent.triggers.some(
        (trigger) => trigger.triggerId === triggerId,
      );
      if (!triggerExists) {
        throw new NotFoundException('Trigger not found');
      }

      const updateData = {
        'triggers.$.triggerName': editTriggerDto.triggerName,
        'triggers.$.data.channel': editTriggerDto.channel,
        'triggers.$.data.task': editTriggerDto.task,
        'triggers.$.data.prompt': editTriggerDto.prompt,
        'triggers.$.data.history': editTriggerDto.history,
        'triggers.$.data.include': editTriggerDto.include,
        'triggers.$.data.exclude': editTriggerDto.exclude,
        'triggers.$.data.exclude_knowledge': editTriggerDto.excludeKnowledge,
        'triggers.$.data.tagConditions': editTriggerDto.tagConditions,
        'triggers.$.active': editTriggerDto.active,
        'triggers.$.followUp': editTriggerDto.followUp,
      };

      // Remove undefined values
      Object.keys(updateData).forEach(
        (key) => updateData[key] === undefined && delete updateData[key],
      );

      await this.mongoAgentService.updateAgent(
        { _id: agentId, 'triggers.triggerId': triggerId },
        { $set: updateData },
      );

      return { success: true };
    } catch (error) {
      
      throw new Error(`Failed to edit trigger: ${error.message}`);
    }
  }

  /**
   * Delete Trigger
   * @param agentId - The ID of the agent as stored in Capri
   * @param triggerId - The ID of the Trigger as stored in Capri
   * @returns {{ success: boolean }} True if the Trigger was deleted successfully
   * @description Delete the Trigger. Follow the normal trigger deletion process.
   */
  async deleteTrigger(
    agentId: string,
    triggerId: string,
  ): Promise<{ success: boolean }> {
    try {
      const agent = await this.mongoAgentService.getAgent({ _id: agentId });
      if (!agent) {
        throw new NotFoundException('Agent not found');
      }

      const triggerExists = agent.triggers.some(
        (trigger) => trigger.triggerId === triggerId,
      );
      if (!triggerExists) {
        throw new NotFoundException('Trigger not found');
      }

      await this.mongoAgentService.updateAgent(
        { _id: agentId },
        { $pull: { triggers: { triggerId: triggerId } } },
      );

      return { success: true };
    } catch (error) {
      
      throw new Error(`Failed to delete trigger: ${error.message}`);
    }
  }

  /**
   * Toggle Tags Custom Fields
   * @param agentId - The ID of the agent as stored in Capri
   * @param actionId - The ID of the Action as stored in Capri
   * @returns {{ success: boolean }} True if the Action was toggled successfully
   * @description Toggle the Action. Follow the normal action toggle process.
   */
  async toggleTagsCustomFields(
    agentId: string,
    actionId: string,
  ): Promise<{ success: boolean }> {
    try {
      // Get the agent to ensure it exists
      const agent = await this.mongoAgentService.getAgent({ _id: agentId });
      if (!agent) {
        throw new NotFoundException('Agent not found');
      }

      // Find the action in the agent's actions array
      const action = agent.actions.find(
        (action) => action.actionId === actionId,
      );
      if (!action) {
        throw new NotFoundException('Action not found');
      }

      // Check if the action is a GHL custom field or tag
      if (
        action.providerName === 'ghl' &&
        (action.activity === 'customField' || action.activity === 'tag')
      ) {
        // Update the action's iframeConfig.connected value
        await this.mongoAgentService.updateAgent(
          {
            _id: agentId,
            'actions.actionId': actionId,
          },
          {
            $set: {
              'actions.$.iframeConfig.connected':
                !action.iframeConfig?.connected,
            },
          },
        );
      }

      return { success: true };
    } catch (error) {
      
      throw new Error(`Failed to toggle tags/custom fields: ${error.message}`);
    }
  }

  /**
   * Get Tags
   * @param agentId - The ID of the agent as stored in Capri
   * @param location - The location of the HighLevel subaccount
   * @returns {{ tags: Tag[] }} An array of Tags from the GHL API
   * @description Get all Tags from the HighLevel subaccount.
   */
  async getTags(agentId: string, location: string): Promise<{ tags: Tag[] }> {
    try {
      // Get GHL credentials for the location
      const credentialData = await this.credentialService.getCredential({
        keyId: location,
        kind: KINDS.GHL_CREDENTIAL,
      });

      if (!credentialData?.creds?.accessToken) {
        throw new Error('No GHL token found for location');
      }

      const tokens = {
        access_token: credentialData.creds?.accessToken,
        refresh_token: credentialData.creds?.refreshToken,
      };

      // Fetch tags from GHL API
      const tagsData = await this.ghlApisService.getTags(tokens, location);

      // Transform the response to match our Tag interface
      const tags = tagsData.tags.map((tag) => ({
        id: tag.id,
        name: tag.name,
        locationId: location,
      }));

      return { tags };
    } catch (error) {
      
      throw new Error(`Failed to fetch tags: ${error.message}`);
    }
  }

  /**
   * Get Custom Fields
   * @param agentId - The ID of the agent as stored in Capri
   * @param location - The location of the HighLevel subaccount
   * @returns {{ customFields: CustomFields[] }} An array of Custom Fields
   * @description Get Custom Fields from the HighLevel subaccount. This is fetched directly from the HighLevel API.
   */
  async getCustomFields(
    agentId: string,
    location: string,
  ): Promise<{ customFields: CustomFields[] }> {
    try {
      // Get GHL credentials for the location
      const credentialData = await this.credentialService.getCredential({
        keyId: location,
        kind: KINDS.GHL_CREDENTIAL,
      });

      if (!credentialData?.creds?.accessToken) {
        throw new Error('No GHL token found for location');
      }

      const tokens = {
        access_token: credentialData.creds?.accessToken,
        refresh_token: credentialData.creds?.refreshToken,
      };

      // Fetch custom fields from GHL API
      const customFieldsData = await this.ghlApisService.getCustomFields(
        tokens,
        location,
      );

      // Transform the response to match our CustomFields interface
      const customFields = customFieldsData.customFields.map((field) => ({
        id: field.id,
        name: field.name,
        fieldKey: field.fieldKey,
        placeholder: field.placeholder || '',
        dataType: field.dataType,
        position: field.position || 0,
        picklistOptions: field.picklistOptions || [],
        picklistImageOptions: field.picklistImageOptions || [],
        isAllowedCustomOption: field.isAllowedCustomOption || false,
        isMultiFileAllowed: field.isMultiFileAllowed || false,
        maxFileLimit: field.maxFileLimit || 1,
        locationId: location,
        model: field.model || 'contact',
      }));

      return { customFields };
    } catch (error) {
      
      throw new Error(`Failed to fetch custom fields: ${error.message}`);
    }
  }

  /**
   * Get Custom Fields Connections
   * @param agentId - The ID of the agent as stored in Capri
   * @param location - The location of the HighLevel subaccount
   * @returns {CustomFieldsConnection[]} An array of Custom Fields Connections
   * @description Get Custom Fields Connections from Capri DB. The Custom Field Details (like id, name, fieldKey, placeholder, dataType, position, picklistOptions, picklistImageOptions, isAllowedCustomOption, isMultiFileAllowed, maxFileLimit, locationId, model) are fetched from the HighLevel API.
   */
  async getCustomFieldsConnections(
    agentId: string,
    location: string,
  ): Promise<CustomFieldsConnection[]> {
    try {
      // Get the agent and organization
      const agent = await this.mongoAgentService.getAgent({ _id: agentId });

      // Get all custom fields from GHL API
      const { customFields } = await this.getCustomFields(agentId, location);

      // Filter actions to get only custom field actions
      const customFieldActions = agent.actions.filter(
        (action) =>
          action.providerName === 'ghl' && action.activity === 'customField',
      );

      // Only return custom fields that have corresponding actions in the agent
      const customFieldConnections = customFields
        .filter((field) => {
          return customFieldActions.some(
            (action) =>
              action.accountName.replace(/[{}]/g, '') === field.fieldKey,
          );
        })
        .map((field) => {
          // Find matching action for this custom field
          const action = customFieldActions.find(
            (action) =>
              action.accountName.replace(/[{}]/g, '') === field.fieldKey,
          );

          // Since we filtered above, action should always exist
          return {
            ...field,
            accountId: action.accountId,
            actionId: action.actionId,
            prompt: action.promptContent,
            iframeConfig: {
              connected: action.iframeConfig?.connected ?? false,
            },
          };
        });

      return customFieldConnections;
    } catch (error) {
      
      throw new Error(
        `Failed to fetch custom fields connections: ${error.message}`,
      );
    }
  }

  /**
   * Get Standard Fields Connections
   * @param agentId - The ID of the agent as stored in Capri
   * @param location - The location of the HighLevel subaccount
   * @returns {StandardFieldsConnection[]} An array of Standard Fields Connections
   * @description Get Standard Fields Connections from Capri DB. The Standard Field Details (like id, name, fieldKey, placeholder, dataType, position, picklistOptions, picklistImageOptions, isAllowedCustomOption, isMultiFileAllowed, maxFileLimit, locationId, model) are fetched from the HighLevel API.
   */
  async getStandardFieldsConnections(
    agentId: string,
    location: string,
  ): Promise<StandardFieldsConnection[]> {
    try {
      // Get the agent
      const agent = await this.mongoAgentService.getAgent({ _id: agentId });
      if (!agent) {
        throw new NotFoundException('Agent not found');
      }

      // Filter actions to get only standard field actions
      const standardFieldActions = agent.actions.filter(
        (action) =>
          action.providerName === 'ghl' && action.activity === 'standardField',
      );

      if (standardFieldActions.length === 0) {
        return [];
      }

      // Map the actions to StandardFieldsConnection objects
      const standardFieldsConnections = standardFieldActions.map((action) => ({
        accountId: action.accountId,
        actionId: action.actionId,
        prompt: action.promptContent,
        fieldKeys: action.metaData?.standardFieldData?.fieldKeys || [],
        iframeConfig: {
          connected: action.iframeConfig?.connected ?? false,
        },
      }));

      return standardFieldsConnections;
    } catch (error) {
      
      throw new Error(
        `Failed to fetch standard fields connections: ${error.message}`,
      );
    }
  }

  /**
   * Add Tags Custom Fields
   * @param agentId - The ID of the agent as stored in Capri
   * @param addTagsCustomFieldsDto - The details to add
   * @returns {{ success: boolean }} True if the Tags Custom Fields were added successfully
   * @description Add Tags Custom Fields. Follow the normal tags custom fields creation process.
   */
  async addTagsCustomFields(
    agentId: string,
    addTagsCustomFieldsDto: AddTagsCustomFieldsDto,
  ): Promise<{ success: boolean }> {
    try {
      // Get the agent to ensure it exists
      const agent = await this.mongoAgentService.getAgent({ _id: agentId });
      if (!agent) {
        throw new NotFoundException('Agent not found');
      }

      if (addTagsCustomFieldsDto.type === 'customField') {
        //get the custom field from the ghl api
        const { customFields } = await this.getCustomFields(
          agentId,
          addTagsCustomFieldsDto.location,
        );
        const customField = customFields.find(
          (field) => field.id === addTagsCustomFieldsDto.id,
        );

        if (!customField) {
          throw new NotFoundException('Custom field not found');
        }

        // Generate unique IDs
        const actionId = uuidv4();
        const accountId = `{{${customField.fieldKey}}}`;

        // Create the new action
        const newAction = {
          actionId,
          accountId,
          providerName: 'ghl',
          promptContent: addTagsCustomFieldsDto.prompt,
          activity: addTagsCustomFieldsDto.type,
          accountName: accountId,
          silent: false,
          isAdvancedSettings: false,
          deleted: false,
          iframeConfig: {
            connected: true,
          },
          metaData: {
            customFieldData: {
              fieldKey: customField.fieldKey,
              reply: true,
              evaluateOn: 'isEm',
            },
          },
        };

        // Add the new action to the agent
        await this.mongoAgentService.updateAgent(
          { _id: agentId },
          { $push: { actions: newAction } },
        );
      } else if (addTagsCustomFieldsDto.type === 'tag') {
        // Get GHL credentials for the location
        const credentialData = await this.credentialService.getCredential({
          keyId: addTagsCustomFieldsDto.location,
          kind: KINDS.GHL_CREDENTIAL,
        });

        if (!credentialData?.creds?.accessToken) {
          throw new Error('No GHL token found for location');
        }

        const tokens = {
          access_token: credentialData.creds?.accessToken,
          refresh_token: credentialData.creds?.refreshToken,
        };

        // Get the tag from the ghl api
        const { tags } = await this.ghlApisService.getTags(
          tokens,
          addTagsCustomFieldsDto.location,
        );
        const tag = tags.find((tag) => tag.id === addTagsCustomFieldsDto.id);
        if (!tag) {
          throw new NotFoundException('Tag not found');
        }

        // Generate unique IDs
        const actionId = uuidv4();
        const accountId = addTagsCustomFieldsDto.id;

        // Create the new action for tag
        const newAction = {
          actionId,
          accountId,
          providerName: 'ghl',
          promptContent: addTagsCustomFieldsDto.prompt,
          activity: 'tag',
          accountName: accountId,
          silent: false,
          isAdvancedSettings: false,
          deleted: false,
          iframeConfig: {
            connected: true,
          },
          metaData: {
            tagData: {
              tagValue: addTagsCustomFieldsDto.id,
              reply: false,
              evaluateOn: 'isEmpty',
            },
          },
        };

        // Add the new action to the agent
        await this.mongoAgentService.updateAgent(
          { _id: agentId },
          { $push: { actions: newAction } },
        );
      } else if (addTagsCustomFieldsDto.type === 'standardField') {
        const standardFields = addTagsCustomFieldsDto.selectedStandardFields;
        
        const actionId = uuidv4();
        const accountId = uuidv4(); // Using UUID as accountId for standard fields
        

        const newAction = {
          actionId,
          accountId,
          providerName: 'ghl',
          promptContent: "", // Not required for standard fields
          activity: 'standardField',
          accountName: `${standardFields.join(', ')}`,
          silent: false,
          isAdvancedSettings: false,
          deleted: false,
          iframeConfig: {
            connected: true,
          },
          metaData: {
            standardFieldData: {
              fieldKeys: standardFields,
              reply: true,
              evaluateOn: 'isEmpty',
            },
          },
        };
        
        // Add the new action to the agent
        await this.mongoAgentService.updateAgent(
          { _id: agentId },
          { $push: { actions: newAction } },
        );
      }

      return { success: true };
    } catch (error) {
      
      throw new Error(`Failed to add tags/custom fields: ${error.message}`);
    }
  }

  /**
   * Edit Tags Custom Fields
   * @param agentId - The ID of the agent as stored in Capri
   * @param actionId - The ID of the Action as stored in Capri
   * @param editTagsCustomFieldsDto - The details to update
   * @returns {{ success: boolean }} True if the Tags Custom Fields were updated successfully
   * @description Update the Tags Custom Fields. Follow the normal tags custom fields update process.
   */
  async editTagsCustomFields(
    agentId: string,
    actionId: string,
    editTagsCustomFieldsDto: EditTagsCustomFieldsDto,
  ): Promise<{ success: boolean }> {
    try {
      // Get the agent to ensure it exists
      const agent = await this.mongoAgentService.getAgent({ _id: agentId });
      if (!agent) {
        throw new NotFoundException('Agent not found');
      }

      // Find the action in the agent's actions array
      const action = agent.actions.find(
        (action) => action.actionId === actionId,
      );
      if (!action) {
        throw new NotFoundException('Action not found');
      }

      // Verify this is a GHL tag, custom field, or standard field action
      if (
        action.providerName !== 'ghl' ||
        (action.activity !== 'customField' && 
         action.activity !== 'tag' && 
         action.activity !== 'standardField')
      ) {
        throw new Error('Invalid action type for update');
      }

      // Build update object with only provided fields
      const updateFields: Record<string, any> = {};

      if (editTagsCustomFieldsDto.prompt !== undefined) {
        updateFields['actions.$.promptContent'] =
          editTagsCustomFieldsDto.prompt;
      }

      if (editTagsCustomFieldsDto.silent !== undefined) {
        updateFields['actions.$.silent'] = editTagsCustomFieldsDto.silent;
      }

      if (editTagsCustomFieldsDto.name !== undefined) {
        updateFields['actions.$.accountName'] = editTagsCustomFieldsDto.name;
      }

      // Handle standard field specific updates
      if (
        action.activity === 'standardField' && 
        editTagsCustomFieldsDto.selectedStandardFields !== undefined
      ) {
        updateFields['actions.$.metaData.standardFieldData.fieldKeys'] = 
          editTagsCustomFieldsDto.selectedStandardFields;
          
        // Update account name to reflect new field keys if not explicitly provided
          updateFields['actions.$.accountName'] = 
            `${editTagsCustomFieldsDto.selectedStandardFields.join(', ')}`;
        
      }

      if (Object.keys(updateFields).length === 0) {
        return { success: true }; // No fields to update
      }

      // Update the action
      await this.mongoAgentService.updateAgent(
        {
          _id: agentId,
          'actions.actionId': actionId,
        },
        {
          $set: updateFields,
        },
      );

      return { success: true };
    } catch (error) {
      
      throw new Error(`Failed to update tags/custom fields: ${error.message}`);
    }
  }

  /**
   * Delete Tags Custom Fields
   * @param agentId - The ID of the agent as stored in Capri
   * @param actionId - The ID of the Action as stored in Capri
   * @returns {{ success: boolean }} True if the Tags Custom Fields were deleted successfully
   * @description Delete the Tags Custom Fields. Follow the normal tags custom fields deletion process.
   */
  async deleteTagsCustomFields(
    agentId: string,
    actionId: string,
  ): Promise<{ success: boolean }> {
    try {
      // Get the agent to ensure it exists
      const agent = await this.mongoAgentService.getAgent({ _id: agentId });
      if (!agent) {
        throw new NotFoundException('Agent not found');
      }

      // Find the action in the agent's actions array
      const action = agent.actions.find(
        (action) => action.actionId === actionId,
      );
      if (!action) {
        throw new NotFoundException('Action not found');
      }

      // Verify this is a GHL tag or custom field action
      if (
        action.providerName !== 'ghl' ||
        (action.activity !== 'customField' && action.activity !== 'tag')
      ) {
        throw new Error('Invalid action type for deletion');
      }

      // Remove the action entirely
      await this.mongoAgentService.updateAgent(
        { _id: agentId },
        {
          $pull: {
            actions: { actionId: actionId },
          },
        },
      );

      return { success: true };
    } catch (error) {
      
      throw new Error(`Failed to delete tags/custom fields: ${error.message}`);
    }
  }

  async editChatbotResponse(
    editChatbotResponseActionDto: EditChatbotResponseActionDto,
    response: Response,
  ) {
    const currentSession = await this.sessionService.getSession(
      editChatbotResponseActionDto.sessionId,
      response,
    );
    if (!currentSession.saved) {
      await this.sessionService.saveChat(
        editChatbotResponseActionDto.sessionId,
        response,
      );
    }
    await this.sessionService.editMessage(
      editChatbotResponseActionDto,
      response,
    );
  }

  async getSession(sessionId: string, response: Response) {
    await this.sessionService.getSession(sessionId, response);
  }

  async getSessionHistory(
    agentId: string,
    page: number,
    limit: number,
    createdBy: 'createdByMe' | 'createdByOthers' | 'createdByAnyone',
    sortBy: 'lastUsed' | 'lastCreated',
    saved: 'true' | 'false' | 'all',
  ) {
    const defaultPage = 0;
    const defaultLimit = 10;
    const defaultSortBy = 'lastUsed';
    const defaultCreatedBy = 'createdByAnyone';
    const defaultSaved = 'all';
    const { orgId, userId } = await this.mongoAgentService.getAgent(
      { _id: agentId },
      { orgId: 1, userId: 1 },
    );
    return this.sessionListService.fetchSessions({
      agent: agentId,
      offset: (page || defaultPage) * (limit || defaultLimit),
      limit: limit || defaultLimit,
      createdBy: createdBy || defaultCreatedBy,
      sortBy: sortBy || defaultSortBy,
      saved: saved || defaultSaved,
      orgId,
      userId,
    });
  }
}
