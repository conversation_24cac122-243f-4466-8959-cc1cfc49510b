import { BadRequestException, Injectable, InternalServerErrorException } from '@nestjs/common';
import { IframeGhlConversationsService } from './iframe-ghl-conversations.service';
import { GhlApisService } from 'src/api-client/services/ghl-apis/ghl-apis.service';
import { MongoCredentialsService } from 'src/mongo/service/credentials/mongo-credentials.service';
import { KINDS } from 'src/lib/constant';
import { access } from 'fs';

@Injectable()
export class IframeGhlContactsService {
  constructor(
    private readonly iframeGhlConversationsService: IframeGhlConversationsService,
    private readonly ghlApiService: GhlApisService,
    private readonly mongoCredentialsService: MongoCredentialsService,
  ) {}

  private async getGhlTokens({ location }: { location: string }) {
  
      const credential = await this.mongoCredentialsService.getCredential({
        keyId: location,
        kind: KINDS.GHL_CREDENTIAL,
      });
  
      const ghlToken = {
        access_token: credential?.creds?.accessToken,
        refresh_token: credential?.creds?.refreshToken,
      };
  
      if ((ghlToken?.access_token ?? ghlToken?.refresh_token) === undefined)
        throw new BadRequestException(`Invalid leadconnector connection detected. Please refresh your Capri leadconnector connection.`)
  
      return { ghlToken };
  }

  async getContact(contactId: string, location: string) {
    

    const { ghlToken } = await this.getGhlTokens({ location });
    const getContactResponse = await this.ghlApiService.getContact(ghlToken, location, contactId);

    if (!getContactResponse?.contact){
      throw new BadRequestException(`Contact not found`)
    }

    return getContactResponse?.contact;
  }

  async getConversationIdFromContactId(contactId: string, location: string) {
    const { ghlToken } = await this.getGhlTokens({ location });
    const c = await this.ghlApiService.searchConversations(ghlToken, location, {
      contactId,
    });
    if ((c?.conversations ?? []).length == 0) throw new Error(`No conversation found for contact ${contactId}`);
    return c?.conversations?.[0]?.id;
  }
}
