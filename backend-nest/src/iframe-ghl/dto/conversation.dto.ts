import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>num,
  IsN<PERSON>ber,
  IsOptional,
  IsString,
} from 'class-validator';

export class SendMessageDto {
  @IsEnum(['SMS', 'Email', 'WhatsApp', 'IG', 'FB', 'Custom', 'Live_Chat'])
  type: string;

  @IsString()
  contactId: string;

  @IsString()
  message: string;

  @IsArray()
  @IsOptional()
  attachments?: string[];

  @IsString()
  @IsOptional()
  appointmentId?: string;

  @IsString()
  @IsOptional()
  emailFrom?: string;

  @IsArray()
  @IsOptional()
  emailCc?: string[];

  @IsArray()
  @IsOptional()
  emailBcc?: string[];

  @IsString()
  @IsOptional()
  html?: string;

  @IsString()
  @IsOptional()
  subject?: string;

  @IsString()
  @IsOptional()
  replyMessageId?: string;

  @IsString()
  @IsOptional()
  templateId?: string;

  @IsString()
  @IsOptional()
  threadId?: string;

  @IsNumber()
  @IsOptional()
  scheduledTimestamp?: number;

  @IsString()
  @IsOptional()
  conversationProviderId?: string;

  @IsString()
  @IsOptional()
  emailTo?: string;

  @IsEnum(['reply', 'reply_all'])
  @IsOptional()
  emailReplyMode?: string;

  @IsString()
  @IsOptional()
  fromNumber?: string;

  @IsString()
  @IsOptional()
  toNumber?: string;
}
