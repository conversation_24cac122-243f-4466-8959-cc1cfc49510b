import { IsNotEmpty, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Optional, IsString } from 'class-validator';

export class UpdateAgentDto {
  @IsString()
  @IsNotEmpty()
  agentName: string;
  @IsString()
  @IsNotEmpty()
  prompt: string;
  @IsString()
  @IsOptional()
  model: AIModel;
  @IsNumber()
  @IsOptional()
  temperature: number;
  @IsNumber()
  @IsOptional()
  maxTokens: number;
}

export enum AIModel {
  GPT_4 = 'gpt-4',
  GPT_3_5_TURBO = 'gpt-3.5-turbo',
}
