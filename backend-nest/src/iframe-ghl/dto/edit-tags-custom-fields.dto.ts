import {
  IsString,
  IsEnum,
  IsBoolean,
  IsO<PERSON>al,
  IsArray,
} from 'class-validator';

enum ActionType {
  tag = 'tag',
  custom_field = 'customField',
  standard_field = 'standardField',
}

export class EditTagsCustomFieldsDto {
  @IsString()
  accountId: string;

  @IsString()
  @IsOptional()
  token: string;

  @IsEnum(ActionType)
  type: ActionType;

  @IsString()
  @IsOptional()
  id: string;

  @IsString()
  @IsOptional()
  prompt: string;

  @IsString()
  @IsOptional()
  name: string;

  @IsBoolean()
  @IsOptional()
  silent: boolean;

  @IsArray()
  @IsOptional()
  selectedStandardFields: string[];
}
