import {
  IsString,
  IsBoolean,
  IsOptional,
  IsArray,
  IsEnum,
  ValidateNested,
  IsNumber,
  IsObject,
} from 'class-validator';
import { Type } from 'class-transformer';

enum TagOption {
  hasTag = 'hasTag',
  doesntHaveTag = 'doesntHaveTag',
}

class TagCondition {
  @IsEnum(TagOption)
  tagOption: TagOption;

  @IsString()
  tagValue: string;
}

class TimeRange {
  @IsString()
  start: string;

  @IsString()
  end: string;
}

export class FollowUp {
  @IsBoolean()
  isFollowupEnabled: boolean;

  @IsNumber()
  duration: number;

  @IsNumber()
  maxAttempts: number;

  @IsString()
  promptId: string;

  @IsString()
  timezone: string;

  @IsObject()
  schedule: {
    [key: string]: TimeRange[];
  };
}

export class EditTriggerDto {
  @IsBoolean()
  @IsOptional()
  active?: boolean;

  @IsString()
  triggerName: string;

  @IsString()
  channel: string;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => TagCondition)
  tagConditions: TagCondition[];

  @IsString()
  location: string;

  @ValidateNested()
  @Type(() => FollowUp)
  followUp: FollowUp;

  @IsString()
  @IsOptional()
  token: string;

  @IsString()
  @IsOptional()
  task: string;

  @IsString()
  @IsOptional()
  prompt: string;

  @IsNumber()
  @IsOptional()
  history: number;

  @IsString()
  @IsOptional()
  include: string;

  @IsString()
  @IsOptional()
  exclude: string;

  @IsString()
  @IsOptional()
  excludeKnowledge: string;
}
