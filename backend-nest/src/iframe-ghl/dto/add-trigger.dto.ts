import {
  IsString,
  IsBoolean,
  IsOptional,
  IsArray,
  IsEnum,
  ValidateNested,
  IsNumber,
} from 'class-validator';
import { Type } from 'class-transformer';
import { FollowUp } from './edit-trigger.dto';

enum TagOption {
  hasTag = 'hasTag',
  doesntHaveTag = 'doesntHaveTag',
}

class TagCondition {
  @IsEnum(TagOption)
  tagOption: TagOption;

  @IsString()
  tagValue: string;
}

export class AddTriggerDto {
  @IsBoolean()
  @IsOptional()
  active?: boolean;

  @IsString()
  triggerName: string;

  @IsString()
  channel: string;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => TagCondition)
  tagConditions: TagCondition[];

  @IsString()
  location: string;

  @IsString()
  @IsOptional()
  token: string;

  @IsString()
  @IsOptional()
  task: string;

  @IsString()
  @IsOptional()
  prompt: string;

  @IsNumber()
  @IsOptional()
  history: number;

  @IsString()
  @IsOptional()
  include: string;

  @IsString()
  @IsOptional()
  exclude: string;

  @IsString()
  @IsOptional()
  excludeKnowledge: string;

  @ValidateNested()
  @Type(() => FollowUp)
  followUp: FollowUp;
}
