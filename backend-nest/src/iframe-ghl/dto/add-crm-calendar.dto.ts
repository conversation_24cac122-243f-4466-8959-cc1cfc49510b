import { IsBoolean, IsOptional, IsString } from 'class-validator';

export class AddCrmCalendarDto {
  @IsString()
  id: string; // The ID of the CRM Calendar as stored in HighLevel

  @IsString()
  location: string;

  @IsString()
  readPrompt: string;

  @IsString()
  writePrompt: string;

  @IsBoolean()
  silent: boolean;

  @IsString()
  @IsOptional()
  token: string;

  @IsString()
  @IsOptional()
  name: string;
}
