import { IsIn, <PERSON>N<PERSON><PERSON>, IsOptional, IsString } from 'class-validator';
import { IsBoolean } from 'class-validator';

export class CreateSessionDto {
  @IsString()
  agentId: string;
}

export class GetRecentSessionsDto {
  @IsString()
  agentId: string;

  @IsString()
  orgId: string;

  @IsNumber()
  offset: number;

  @IsNumber()
  limit: number;

  @IsIn(['lastUsed', 'lastCreated'])
  sortBy: 'lastUsed' | 'lastCreated';

  @IsIn(['true', 'false', 'all'])
  saved: 'true' | 'false' | 'all';
}

export class UpdateSessionNameDto {
  @IsString()
  name: string;
}

export class UpdateStatusDto {
  @IsBoolean()
  saved: boolean;
}

export class SendMessageDto {
  @IsString()
  @IsOptional()
  sender: string;

  @IsString()
  sessionId: string;

  @IsString()
  query: string;
}

export class UpdateSessionMessageDto {
  @IsString()
  message: string;

  @IsString()
  eventId: string;
}

export class UpdateGhlCalendarActionDto {
  @IsString()
  startDate: string;

  @IsString()
  endDate: string;

  @IsString()
  eventId: string;

  @IsString()
  action: string;

  @IsString()
  accountId: string;
}

export class AddGhlTagResponseDto {
  @IsString()
  eventId: string;

  @IsString()
  accountId: string;
}

export class AddGhlTagResponseWithActionDto extends AddGhlTagResponseDto {
  @IsString()
  action: string;
}
