import { IsString, IsEnum, IsArray } from 'class-validator';

enum ActionType {
  tag = 'tag',
  custom_field = 'customField',
  standard_field = 'standardField',
}

export class AddTagsCustomFieldsDto {
  @IsEnum(ActionType)
  type: ActionType;

  @IsString()
  id: string;

  // @IsString()
  // token: string;

  @IsString()
  prompt: string;

  @IsString()
  location: string;

  @IsArray()
  selectedStandardFields: string[];
}
