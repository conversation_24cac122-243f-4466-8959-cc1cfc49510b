import { Injectable } from '@nestjs/common';
import Groq from 'groq-sdk';

interface IAiProviderAdvSettings {
  temperature: number;
  maxLength: number;
}

@Injectable()
export class GroqService {
  async generateBotResponse(
    messages: any[],
    maxTokens: number,
    modelObj: any,
    aiProvAdvSett: IAiProviderAdvSettings,
    retryCount = 3,
  ): Promise<any> {
    try {
      const groq = new Groq({
        apiKey: process.env.GROQ_API_KEY,
      });

      let advAiProvSett: IAiProviderAdvSettings = {
        temperature: 0.4,
        maxLength: maxTokens,
      };

      if (aiProvAdvSett) {
        advAiProvSett.temperature = aiProvAdvSett.temperature;
        advAiProvSett.maxLength = aiProvAdvSett.maxLength;
      }

      const response = await groq.chat.completions.create({
        model: 'mixtral-8x7b-32768',
          messages: messages,
          max_tokens: advAiProvSett.maxLength,
          temperature: advAiProvSett.temperature,
          stop: null,
          stream: false
      });
      return response;
    } catch (error) {
      
      if (error.response && error.response.status !== 401 && retryCount > 0) {
    
        await new Promise((resolve) => setTimeout(resolve, 3000));
        return this.generateBotResponse(
          messages,
          maxTokens,
          modelObj,
          aiProvAdvSett,
          retryCount - 1,
        );
      } else {
        
        return error.response.data;
      }
    }
  }
}
