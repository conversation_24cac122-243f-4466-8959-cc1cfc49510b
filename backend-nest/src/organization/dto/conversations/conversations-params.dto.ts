import { IghlSendMessage } from "src/api-client/dto/ghl.dto";
export interface GetRolloverQueryParams {
  page: number;
  limit: number;
  status: 'rollover' | 'replied' | 'all';
  sortBy?: 'updatedAt' | 'channel' | 'agentName' | 'status';
  sortOrder?: 'asc' | 'desc';
  agentId?: string;
  contactId?: string;
  channel?: string;
  channels?: string;
  channelAccountId?: string;
  orgId?: string;
  dateFrom?: string;
  dateTo?: string;
  search?:string;
}


export interface getSingleRolloverConversationDto {
    contactId: string;
    agentId?: string;
    channelAccountId?: string;
    showAll?: string;
}
export interface sendMessageBodyDto extends IghlSendMessage {
    channelName: 'ghl';
    locationId: string;
}