// Rollover conversations list

export interface GetRolloverConversationsDto {
    agentId: String;
    channelName: String;
    lastMessage: String;
    lastMessageAt: Date;
    rolloverReason: String;
    rolloverDate: Date;
}

// Rollover single conversation

export interface GetRolloverSingleConversationDto {
   status: 'pending' | 'delivered' // message status
   dateAdded: Date;
   direction: 'inbound' | 'outbound';
   messageType: string;
   message: string;
}[]