import { ApiProperty } from "@nestjs/swagger";

// ----------------------------- Channels -----------------------------

// ----------------------------- Integrations -----------------------------

export class GetIntegrationsDto {
    @ApiProperty({ description: 'Organization Id', type: String })
    id: string;
    @ApiProperty({ description: 'List of data sources', type: [Object] })
    datasources: Object[];
}

// ----------------------------- AI provider -----------------------------

// ----------------------------- Organizations -----------------------------

// ----------------------------- staticData -----------------------------
export interface IscrapedData {
  message: string;
  data: {
    url: string;
    result: {
      question: string;
      answer: string;
    }[];
  }[];
  unprocessed_urls: string[];
  invalid_urls: string[];
}