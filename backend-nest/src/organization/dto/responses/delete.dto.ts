import { ApiProperty } from "@nestjs/swagger";

// ----------------------------- Channels -----------------------------
export class DeletedChannelData {
    @ApiProperty({ description: 'AccountId of the channel deleted', type: String })
    accountId: string;
}

export class DeleteChannelResponseDto {
    @ApiProperty({ description: 'message', type: String })
    message: string;
    @ApiProperty({ description: 'data', type: DeletedChannelData })
    data: DeletedChannelData;
}

// ----------------------------- Integrations -----------------------------

export class DeleteIntegrationResDto extends DeleteChannelResponseDto { } 

// ----------------------------- AI provider -----------------------------

// ----------------------------- Organizations -----------------------------
