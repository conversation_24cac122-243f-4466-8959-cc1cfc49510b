import { ApiProperty } from "@nestjs/swagger";

// ----------------------------- Channels -----------------------------

// ----------------------------- Integrations -----------------------------

export class GoogleSheetData {
    @ApiProperty({ description: 'Google Sheet Id', type: String })
    credentialId: string
    @ApiProperty({ description: 'Google Sheet Name', type: String })
    name: string
    @ApiProperty({ description: 'googleSheet', type: String })
    providerName: string
    @ApiProperty({ description: 'Google Sheet Header range', example: 'A1:B1', type: String })
    headers: string
    @ApiProperty({ description: 'Account Id of the integration', type: String })
    accountId: string
}

export class ConnectSheetDto {
    @ApiProperty({ description: 'message' })
    message: string;
    @ApiProperty({ description: 'Data of the Google sheet added', type: GoogleSheetData })
    data: GoogleSheetData;
}

// ----------------------------- AI provider -----------------------------

// ----------------------------- Organizations -----------------------------