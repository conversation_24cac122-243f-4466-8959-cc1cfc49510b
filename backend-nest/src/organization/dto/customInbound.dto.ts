import {
  IsString,
  IsNotEmpty,
  IsUrl,
  ValidateNested,
  IsOptional,
  IsMongoId,
  IsUUID,
  IsArray,
  isURL,
  IsNumber,
  IsObject,
  IsNotIn,
  isUUID,
  IsBoolean,
} from 'class-validator';

export class BaseConversationDto {
  @IsNotEmpty()
  @IsString()
  agent: string;

  @IsNotEmpty()
  @IsString()
  sessionId: string;

  @IsOptional()
  type?: 'outbound' | 'inbound';
  // queryData?: any;

  @IsString()
  @IsOptional()
  prompt?: string;

  @IsString()
  @IsOptional()
  promptId?: string;

  @IsBoolean()
  @IsOptional()
  training?: boolean;

  @IsString()
  @IsOptional()
  outreach?: string;

  @IsNumber()
  @IsOptional()
  history?: number;

  @IsString()
  @IsOptional()
  knowledge: string;

  @IsString()
  @IsOptional()
  task?: string;
}

export class V1InboundConversationDto extends BaseConversationDto {
  @IsNotEmpty()
  @IsString()
  message?: string;
}

export class V1EmailConversationDto extends BaseConversationDto {
  @IsNotEmpty()
  @IsString()
  subject?: string;

  @IsNotEmpty()
  @IsString()
  body?: string;
}
