export interface ICheckLocationRequest {
    locationId: string;
    accessToken: string;
    refreshToken: string;
}

export interface ICheckLocationResponse {
    status: 'success' | 'error',
    message?: string; 
    version?: string,
    legacyRemaining?: string[];
}

export interface IMigrateLocationRequest {
    locationId: string;
}

export interface IMigrateLocationResponse {
    status: 'success' | 'error',
    message?: string;
    location: {
        prompt: string;
        openAi_key: string;
        model_type: string;
        calendar_id: string;
        googleSheet: string;
        name: string;
        sessions: {
            id: string;
            values: number[];
            metadata: {
                prompt?: string;
                content?: string;
                type?: string;
                agentId?: string;
            }
        }[]
    }
}

export interface IMigrateFinishRequest {
    locationId: string;
    orgId: string;
}