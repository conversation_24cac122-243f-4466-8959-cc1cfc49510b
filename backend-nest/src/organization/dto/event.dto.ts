import { IsISO8601, <PERSON><PERSON><PERSON>E<PERSON><PERSON>, <PERSON><PERSON>ptional, IsString } from 'class-validator';

export class EventDto {
  @IsNotEmpty()
  @IsString()
  orgId: string;

  @IsNotEmpty()
  @IsString()
  summary: string;

  @IsNotEmpty()
  @IsString()
  accountId: string;

  @IsOptional()
  @IsString()
  calendarId?: string = 'primary';

  @IsNotEmpty()
  @IsISO8601()
  startDateTime: string;

  @IsNotEmpty()
  @IsISO8601()
  endDateTime: string;
}
