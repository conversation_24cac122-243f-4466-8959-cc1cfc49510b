import {
  IsString,
  IsNotEmpty,
  IsObject,
  ValidateNested,
  IsNumber,
  Min,
} from 'class-validator';
import { Type } from 'class-transformer';

class VoiceDetailsDto {
  @IsNumber()
  @Min(0)
  numOfTokensUsed: number;

  @IsNumber()
  @Min(0)
  numOfSeconds: number;
}

export class AddUsageDto {
  @IsString()
  @IsNotEmpty()
  agentId: string;

  @IsObject()
  @ValidateNested()
  @Type(() => VoiceDetailsDto)
  voiceDetails: VoiceDetailsDto;
}
