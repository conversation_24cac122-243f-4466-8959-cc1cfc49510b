import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsUrl, ValidateNested, IsObject, IsOptional } from 'class-validator';

export class SheetAuthDto {
    @IsString()
    @IsNotEmpty()
    @ApiProperty({ description: 'Google Sheet link' })
    readonly sheetId: string;

    @IsString()
    @IsNotEmpty()
    @ApiProperty({ description: 'Organization Id' })
    readonly orgId: string;

    @IsString()
    @IsOptional()
    @ApiProperty({ description: 'Range of the columns to look for the data in the sheet', example: 'A1:B1' })
    readonly headers?: string;

    @IsString()
    @ApiProperty({ description: 'User Name' })
    readonly userName: string;

    @IsString()
    sheetName?: string;
}