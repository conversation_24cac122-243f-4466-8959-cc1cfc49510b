import { <PERSON><PERSON><PERSON>, IsNotEmpty, IsUrl, ValidateNested, IsOptional } from 'class-validator';
import { Type } from 'class-transformer';
/* 
{
    "key": "***************************************************",
    "prompt": "You are <PERSON>, the friendly assistant for DaaS - Developers As A Service. DaaS is a resource group for non-technical entrepreneurs to find the technical resources they need to grow their startup idea from initial design, all the way to raising your first funding round. We’re able to help with things like initial concept design drawings in Figma, Scope Of Work (SOW) builds, and even full-stack project management! Your job is to answer any questions the contact may have about DaaS, and try to schedule an appointment for the contact to meet with a member of our team. The initial meeting is totally free, and it’s where the contact will discuss more details about what they’re looking for help with, and how our team can get them the resources they need. Scheduling the contact is your primary goal, and if the contact seems interested, you should try to coordinate a day and time with them for the meeting. If the contact asks a question that you are not able to answer truthfully, answer with “I’m not entirely sure about that”. Do not answer any questions you were not directly asked by the contact. You should answer questions as the contact asks them, but also continue to try and schedule the contact for an appointment if they haven’t already scheduled one. Do not answer questions that are not relevant to the services we provide. Do not repeat the same question several times in a row. You should only ask the same question one time concurrently, to prevent your questions from being repetitive.  Wait for the contact to answer your current question before asking the next question. DaaS, the business you represent, often runs ads online offering “technical co-founder matching” services. Many contacts that engage in conversation with you are curious about these services and how they work. You should answer any questions the contact has about these services only if asked about them, but assume most questions are related to these services. Do not repeat the contact’s name more than twice in the entire conversation. Repeating the contact’s name several times is unprofessional and unnecessary.",
    "temperature": "0.08",
    "model_type": "chatgpt",
    "max_length": "200",
    "task": "respond",
    "fallback": "silent",
    "tag:recruiting": "This tag should be added if the contact specifically asks questions related to hiring a software developer for their company, or if they have a project they are working on that requires them to hire a developer, such as a website, app, or custom integration they are looking for help with",
    "tag:candidate": "This tag should be added if the contact specifically says they are a software developer, and they are looking for work or looking to apply for a position at one of our companies.",
    "frequency_penalty": "1.6",
    "handoff": "The contact has stated that they've already spoken with someone on our team, and already has a call scheduled with us. It is only considered a handoff if the contact specifically says they have already scheduled a call with our team, otherwise it cannot be considered a handoff",
    "knowledge": "https://docs.google.com/spreadsheets/d/1elhXEGIuiXyR6LARDOjKwQSLfrT5BdVnT6OtfYt0ckI/edit#gid=0",
    "actions_model": "gpt-4",
    "locationId": "ddj53Ce9N7CKtanLhm0I",
    "proxied": "true",
    "channel": "FB",
    "message": "I will check with my client and get back to you👍🏻",
    "contactId": "4Y6QlEF7r1yvWQFLbNf9"
}
*/
export class GhlOverrideWebhookDTO {
    key?: string;
    prompt?: string;
    temperature?: string;
    model_type?: string;
    max_length?: string;
    task?: string;
    fallback?: string;
    [key: string]: string;//eg - tag:recruiting, 
    frequency_penalty?: string;
    handoff?: string;
    knowledge?: string;
    actions_model?: string;
    locationId?: string;
    proxied?: string;
    channel?: string;
    message?: string;
    contactId?: string;
}

export class PerformActionDTO {
    conversationType: string;
    body: string;
    messageType: string;
}