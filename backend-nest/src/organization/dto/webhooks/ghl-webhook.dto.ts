export interface IbotResponse {
    eventId?: string;
    sender?: string;
    kind?: string;
    accountName?: string;
    accountId?: string;
    calendarId?: string;
    deleted?: boolean;
    action?: string;
    eventData?: IeventData;
    timestamp?: number;
    message: string;
    locationId?: string;
    errors?: string[];
}

export interface IeventData {
    startDate: string;
    endDate: string;
}