import { ApiOperation, ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsUrl, ValidateNested, IsOptional, IsMongoId } from 'class-validator';

export class DeleteChannelDto {
    @IsString()
    @IsNotEmpty()
    @IsMongoId()
    @ApiProperty({ description: 'Organization ID', type: String })
    readonly orgId: string;

    @IsString()
    @IsOptional()
    @ApiProperty({ description: 'The Account Id attached with the channel', type: String })
    readonly accountId?: string;

    @IsString()
    @IsOptional()
    readonly provider?: string;
}

export class DeleteIntegrationDto extends DeleteChannelDto { }

export class DeleteStaticDataDto extends DeleteChannelDto { }