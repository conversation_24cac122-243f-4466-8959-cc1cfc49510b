import { ApiOperation, ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsString,
  IsNotEmpty,
  IsUrl,
  ValidateNested,
  IsOptional,
  IsMongoId,
  IsUUID,
  IsArray,
  isURL,
  IsNumber,
  IsObject,
  IsEnum,
} from 'class-validator';

export class CalendarPreferenceDto {
  @IsString()
  @IsNotEmpty()
  @IsMongoId()
  @ApiProperty({ type: String, description: 'organization ID' })
  orgId: string;

  @IsString()
  @IsNotEmpty()
  @ApiProperty({
    type: String,
    description: 'UUID assigned to the channel in Database',
  })
  @IsUUID()
  accountId: string;

  @ApiProperty({ type: String })
  calendarId: string;

  @IsOptional()
  @ApiProperty({ type: String, example: 'Asia/Kolkata' })
  timezone?: string;

  @IsOptional()
  @ApiProperty({ type: String, example: 'My Calendar' })
  calendarName?: string;

  @IsOptional()
  businessName?: string;

  @IsOptional()
  useContactTz?: boolean;

  @IsNotEmpty()
  connectionData?: any;
}

export class ConnectChathqQueryDto {
  @IsString()
  @IsNotEmpty()
  @ApiProperty({ type: String, description: 'User SSO Token' })
  ssoToken: string;
}

export class connectPodiumLocDto {
  // orgId, locationId, podiumOrgId
  @IsString()
  @IsNotEmpty()
  orgId: string;

  @IsString()
  @IsNotEmpty()
  locationId: string;

  @IsNotEmpty()
  tokens: {
    accessToken: string;
    refreshToken: string;
  };

  @IsString()
  @IsNotEmpty()
  locationName: string;

  podiumOrgId?: string;
}

// -----------------------------Integrations-------------------------------
export class SheetVectorOperationDto {
  @IsString()
  @IsNotEmpty()
  orgId: string;

  @IsString()
  @IsNotEmpty()
  sheetId: string;

  @IsString()
  @IsNotEmpty()
  headers: string;

  @IsString()
  @IsNotEmpty()
  accountId: string;

  @IsString()
  @IsOptional()
  sheetTitle?: string;
}
export class SheetUpdateWebhookDto {
  rowNumber: number;
  newValue: string;
  endRow: number;
  task: string;
  spreadsheetId: string;
  data: Object[];
  capri_token: string;
}

// -----------------------------data-------------------------------

export class ScrapeSiteDto {
  @IsString()
  @IsNotEmpty()
  @ApiProperty({ type: String, description: 'organization ID' })
  orgId: string;
}

export class updateEmbeddingsDto {
  //accountId, domain, chunks
  @IsString()
  @IsNotEmpty()
  @ApiProperty({ type: String, description: 'Account ID' })
  accountId: string;

  @IsString()
  @IsNotEmpty()
  @ApiProperty({ type: String, description: 'Domain' })
  name: string;

  @IsArray()
  @IsNotEmpty()
  @ApiProperty({ type: [String], description: 'Chunks' })
  chunks: Object[];
}

export class EditDataSourceDto {
  name: string;
  headers?: string; //googleSheets
  timezone?: string; //ghlCalendar
  useContactTz?: Boolean;
}

enum DayOfWeek {
  Monday,
  Tuesday,
  Wednesday,
  Thursday,
  Friday,
  Saturday,
  Sunday,
}

class TimeSlot {
  @IsString()
  start: string;

  @IsString()
  end: string;
}

class AvailableHour {
  @IsEnum(DayOfWeek)
  day: DayOfWeek;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => TimeSlot)
  timeSlots: TimeSlot[];
}

export class EditGoogleCalendarDto {
  @IsString()
  eventName: string;

  @IsNumber()
  eventDuration: number;

  @IsString()
  eventLocation: string;

  @IsString()
  locationValue: string;

  @IsString()
  eventDescription: string;

  @IsNumber()
  dateRange: number;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => AvailableHour)
  availableHours: AvailableHour[];

  @IsNumber()
  startTimeIncrements: number;
}
