import {
  IsBoolean,
  IsEmail,
  IsN<PERSON>E<PERSON>y,
  IsOptional,
  IsString,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export enum AccessType {
  none = 'none',
  read = 'read',
  all = 'all',
}

export class PageAccessDto {
  @IsOptional()
  @ApiProperty({ description: 'Page access', type: String })
  accessVal: AccessType;
}

export class AddNewMemberDto {
  @IsNotEmpty()
  @IsEmail()
  @ApiProperty({ description: 'User email', type: String })
  email: string;

  @IsNotEmpty()
  @IsString()
  @ApiProperty({
    description: 'Organization ID where the user is requested to join',
    type: String,
  })
  orgId: string;
  @IsNotEmpty()
  @IsBoolean()
  @ApiProperty({
    description: 'This property states whether the added user is an admin',
    type: Boolean,
  })
  isAdmin: boolean;

  @IsOptional()
  @ApiProperty({ description: 'Fallback page access' })
  fallbacks: PageAccessDto;

  @IsOptional()
  @ApiProperty({ description: 'Emulator page access' })
  emulator: PageAccessDto;

  @IsOptional()
  @ApiProperty({ description: 'Agents page access' })
  agents: PageAccessDto;

  @IsOptional()
  @ApiProperty({ description: 'Settings page access' })
  settings: PageAccessDto;

  @IsOptional()
  @ApiProperty({ description: 'Billing page access' })
  billing: PageAccessDto;
}
