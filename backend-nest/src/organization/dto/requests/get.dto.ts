import { <PERSON><PERSON><PERSON>, <PERSON>NotEmpty, <PERSON>Optional, IsString } from 'class-validator';
import { ApiProperty } from "@nestjs/swagger";

export class GetOrganizationDto {
    @IsNotEmpty()
    @ApiProperty({ description: 'Organization ID', type: String })
    orgId: string;
}

export class GetPodiumConnectionDto {
    @IsOptional()
    @ApiProperty({ description: 'Organization ID', type: String, required: false, })
    code?: string;

    @IsOptional()
    @ApiProperty({ description: 'State for the oauth2 connection', type: String, required: false })
    state?: string;
}

export class GetChannelListDto {
    @IsString()
    @IsNotEmpty()
    @ApiProperty({ description: 'Organization ID', type: String })
    orgId: string;

    @IsString()
    @IsOptional()
    @ApiProperty({ description: 'provider', type: String, items: { type: 'string', enum: ['podium', 'ghl', 'chathq'] } })
    provider?: string;
}