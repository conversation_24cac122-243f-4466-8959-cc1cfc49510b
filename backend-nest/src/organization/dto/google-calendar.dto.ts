import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsNotEmpty,
  IsUrl,
  ValidateNested,
  IsObject,
  IsOptional,
} from 'class-validator';

export class GoogleCalendarAuthDto {
  @IsString()
  @IsNotEmpty()
  @ApiProperty({ description: 'Google calendar Id' })
  readonly calendarId: string;

  @IsString()
  @IsNotEmpty()
  @ApiProperty({ description: 'Organization Id' })
  readonly orgId: string;

  @IsString()
  @ApiProperty({ description: 'User Name' })
  readonly userName: string;

  @IsString()
  readonly calendarName?: string;

  @IsString()
  @ApiProperty({ description: 'Calendar Access Token' })
  readonly access_token: string;

  @IsString()
  @ApiProperty({ description: 'Calendar Refress Token' })
  readonly refresh_token: string;
}
