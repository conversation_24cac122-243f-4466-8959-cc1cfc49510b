// manages organization calendar availability settings
import { Body, Controller, Get, Param, Put } from '@nestjs/common';
import { OrgAvailability } from '../../../mongo/schemas/organization/org-availablity/org-availablity.schema';
import { calendarAvailabilityService } from '../../services/calendar-availablity/calendar-availablity.service';

@Controller('integration/')
export class calendarAvailabilityController {
  constructor(
    private readonly calendarAvailabilityService: calendarAvailabilityService,
  ) {}

  @Get(':orgId/google-calendar/:accountId')
  async findById(
    @Param('orgId') orgId: string,
    @Param('accountId') accountId: string,
  ) {
    return await this.calendarAvailabilityService.findOrgAvailability({
      orgId,
      accountId,
    });
  }

  @Put(':orgId/google-calendar/:accountId')
  async update(
    @Param('orgId') orgId: string,
    @Param('accountId') accountId: string,
    @Body() orgAvailability: OrgAvailability,
  ): Promise<OrgAvailability> {
    return await this.calendarAvailabilityService.update(
      orgId,
      accountId,
      orgAvailability,
    );
  }
}
