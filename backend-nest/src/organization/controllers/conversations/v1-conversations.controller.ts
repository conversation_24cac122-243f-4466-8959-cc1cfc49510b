// Contains the processes that serve the conversations page inside an agent.
import { Controller, Get, Post, Req, Res, Body, HttpStatus, Render, Delete, Param, Query, UseGuards, Patch, } from '@nestjs/common';
import { Request, Response } from 'express';
import {
ApiOperation,
ApiQuery,
ApiNotFoundResponse,
ApiOkResponse,
ApiParam,
} from '@nestjs/swagger';
import { v4 as uuidv4 } from 'uuid';
import { GhlApisService } from 'src/api-client/services/ghl-apis/ghl-apis.service';
import { CONNECTION_TYPES, KINDS, PROVIDERS } from 'src/lib/constant';
import { RequestWithUser } from 'src/auth/auth.interface';
import { ApiclientService } from 'src/api-client/services/apiclient.service';
import { handleException } from 'helpers/handleException';
import { MyLogger } from 'src/logger/logger.service';
import { GetRolloverQueryParams, getSingleRolloverConversationDto, sendMessageBodyDto } from 'src/organization/dto/conversations/conversations-params.dto';
import { V1ConversationsService } from 'src/organization/services/conversations/v1-conversations.service';

  
@Controller('conversations')
export class V1ConversationsController {

    constructor(
        private readonly conversationService: V1ConversationsService,
    ){}

    @Get('rollover/:contactId')
    async getRolloverConversation(
        @Req() req: RequestWithUser,
        @Query() query: getSingleRolloverConversationDto,
        @Param('contactId') contactId: string,
        @Res() response: Response,
    ) {
        const conversation = await this.conversationService.getSingleRolloverConversation(req, query, contactId);
        return response.status(HttpStatus.OK).json(conversation);
    }

    @Get('/rollovers')
    async getRollovers(
        @Req() req: RequestWithUser,
        @Query() query: GetRolloverQueryParams,
        @Res() response: Response,
    ) {
        const conversation = await this.conversationService.getRolloverConversations(query);
        return response.status(HttpStatus.OK).json(conversation);
    }

    @Post('/send/:orgId')
    async send(
        @Req() req: RequestWithUser,
        @Body() body: sendMessageBodyDto,
        @Param('orgId') orgId: string,
        @Res() response: Response,
    ) {
        const conversation = await this.conversationService.sendMessage(body.channelName, body.locationId, body, orgId);
        return response.status(HttpStatus.CREATED).json(conversation);
    }

    @Post('rollover/mark/:contactId')
    async markRolloverStatus(
        @Req() req: RequestWithUser,
        @Body() body: {
            set: boolean
        },
        @Param('contactId') contactId: string,
        @Res() response: Response,
    ){
        const conversation = await this.conversationService.markRolloverStatus(contactId, body);
        return response.status(HttpStatus.OK).json({ message: `contact : ${contactId} marked as ${body.set ? 'rollover' : 'not a rollover'} successfully`});
    }
}
