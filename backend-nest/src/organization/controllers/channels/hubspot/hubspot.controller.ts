//  manages HubSpot CRM integration
import {
  Controller,
  Get,
  Post,
  Req,
  Res,
  Body,
  HttpStatus,
  Render,
  Delete,
  Param,
  Query,
} from '@nestjs/common';
import { Request, Response } from 'express';
import {
  ApiOperation,
  ApiQuery,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiParam,
} from '@nestjs/swagger';
import { handleException } from 'helpers/handleException';
import { MyLogger } from 'src/logger/logger.service';
import { HubspotService } from 'src/organization/services/channels/hubspot/hubspot.service';
import { getHubspotConnectionURL } from 'src/organization/services/channels/hubspot/hubspot.constant';

@Controller('hubspot')
export class HubspotController {
  constructor(
    private readonly logger: MyLogger,
    private readonly hubspotService: HubspotService,
  ) {}

  @Get('connect')
  async connectHubspot(@Query() Q, @Res() res: Response) {
    try {
      const redirectUri = process.env.BACKEND_URL + '/hubspot/connect';
      let { code = undefined, state = undefined } = Q;
      // if (!state) throw new Error('Invalid state passed in URL');
      if (!code) {
        const hubspotConnectionURL = getHubspotConnectionURL(
          process.env.HUBSPOT_CLIENT_ID,
          redirectUri,
          state
        );
        res.redirect(301, hubspotConnectionURL);
        return;
      } else {
        const orgId = state; //decodeCode(state).orgId;
        this.logger.log({
          message: `Organization received: ${orgId}`,
        });
        const channelData = await this.hubspotService.connectHubspot(orgId, code);
        return res.render('hubspot', {
          type: 'Success',
          message: 'Hubspot connected successfully!',
          title: 'Success',
          orgId,
          ...channelData,
        });
      }
    } catch (error) {
      this.logger.error({
        message: 'Error in connecting hubspot ' + error?.message,
      });
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).send(error?.message);
      // res.render('podium', {
      //   type: 'Error',
      //   message: 'Failed to connect podium',
      //   title: 'Failure',
      //   locations: undefined,
      //   podiumOrgId: undefined,
      //   orgId: undefined,
      //   accessToken: undefined,
      //   refreshToken: undefined,
      // });
    }
  }

  @Post('/update/channel')
  async updateHubspot(@Body() body, @Res() res: Response) {
    const data = await this.hubspotService.updateHubspotChannelName(body.orgId, body.accountId, body.channelName);
    res.status(HttpStatus.OK).send(data);
  }
}
