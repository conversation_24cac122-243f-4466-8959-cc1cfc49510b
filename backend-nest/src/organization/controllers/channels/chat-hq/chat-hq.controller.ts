//  manages ChatHQ integration
import { ChatHqService } from 'src/organization/services/channels/chat-hq/chat-hq.service';
import {
  Controller,
  Get,
  Post,
  Req,
  Res,
  Body,
  HttpStatus,
  Render,
  Delete,
  Param,
  Query,
  UseGuards,
} from '@nestjs/common';
import { Request, Response, query } from 'express';
import {
  ApiOperation,
  ApiQuery,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiParam,
  ApiBody,
} from '@nestjs/swagger';
import { ConnectChathqQueryDto } from 'src/organization/dto/requests/post.dto';
import { AuthGuard } from 'src/auth/auth.guard';
import { RequestWithUser } from 'src/auth/auth.interface';
import { handleException } from 'helpers/handleException';
import { MyLogger } from 'src/logger/logger.service';

@Controller('chathq')
export class ChatHqController {
  constructor(
    private readonly chatHqService: ChatHqService,
    private readonly myLogger: MyLogger,
  ) {}

  @ApiOperation({ summary: 'Connect ChatHQ channel' })
  @ApiOkResponse({
    description: 'A rendered view for setting the chatHq widget preferences',
  })
  @Get('connect')
  async connect(
    @Req() req: RequestWithUser,
    @Query() query: ConnectChathqQueryDto,
    @Res() res: Response,
  ) {
    try {
      return await this.chatHqService.connectChatHq(req, query, res);
    } catch (err) {
      this.myLogger.error({
        message: `@error | ${req.uniqueCode} | connect chatHq | ` + err.message,
      });
      handleException(res, err);
    }
  }

  @ApiOperation({ summary: 'Set chatHQ connection preferences' })
  @ApiOkResponse({ status: 200 })
  @Post('preferences')
  async setPreferences(
    @Req() req: RequestWithUser,
    @Body() body: any,
    @Res() res: Response,
  ) {
    try {
      const {
        orgId,
        accountId,
        widgetId,
        widgetName,
        author = '',
        accId = '',
        accessToken = '',
      } = body;
      const data = await this.chatHqService.setWidget(
        orgId,
        accountId,
        widgetId,
        widgetName,
        author,
        accId,
        accessToken,
      );
      return res.status(HttpStatus.OK).json({
        status: 'success',
        message: 'CHat HQ widget set successfully',
        data,
      });
    } catch (error) {
      this.myLogger.error({
        message:
          `error | reqId ${req.uniqueCode} | setting chat hq widget preference: ` +
          error.message,
      });
      handleException(res, error);
    }
  }
}
