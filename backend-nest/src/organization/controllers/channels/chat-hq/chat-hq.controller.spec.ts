import { Test, TestingModule } from '@nestjs/testing';
import { ChatHqController } from './chat-hq.controller';

describe('ChatHqController', () => {
  let controller: ChatHqController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ChatHqController],
    }).compile();

    controller = module.get<ChatHqController>(ChatHqController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
