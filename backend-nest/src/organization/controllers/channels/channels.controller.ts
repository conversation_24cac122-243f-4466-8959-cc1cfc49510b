// Contains the code to connect Slack channel, get all the organization's channel, to connect a GHL channel, to delete a channel, to get a mapped agent for channel, to get a channel list, to edit an agent's channel.
import {
  Controller,
  Get,
  Post,
  Req,
  Res,
  Body,
  HttpStatus,
  Render,
  Delete,
  Param,
  Query,
  UseGuards,
  Patch,
} from '@nestjs/common';
import { Request, Response } from 'express';
import {
  ApiOperation,
  ApiQuery,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiParam,
} from '@nestjs/swagger';
import { OrganizationService } from 'src/organization/services/organization.service';
import { decodeCode } from 'src/lib/utils';
import { DeleteChannelDto } from '../../dto/requests/delete.dto';
import { v4 as uuidv4 } from 'uuid';
import { GhlApisService } from 'src/api-client/services/ghl-apis/ghl-apis.service';
import { MongoCredentialsService } from 'src/mongo/service/credentials/mongo-credentials.service';
import { CONNECTION_TYPES, KINDS, PROVIDERS } from 'src/lib/constant';
import { DeleteChannelResponseDto } from 'src/organization/dto/responses/delete.dto';
import { ChannelsService } from 'src/organization/services/channels/channels.service';
import { AuthGuard } from 'src/auth/auth.guard';
import { RequestWithUser } from 'src/auth/auth.interface';
import { ApiclientService } from 'src/api-client/services/apiclient.service';
import { GetChannelListDto } from 'src/organization/dto/requests/get.dto';
import { MigrationService } from 'src/migration/services/migration.service';
import { allowAccessGuard } from 'src/guards/can-access/can-access.guard';
import { UserService } from 'src/user/user.service';
import { HydratedCredentialDocument } from 'src/mongo/schemas/credential/credentials.schema';
import { handleException } from 'helpers/handleException';
import { ApiTokenGuard } from 'src/guards/api-token.guard';
import { MyLogger } from 'src/logger/logger.service';

@Controller('channels')
export class ChannelsController {
  constructor(
    private readonly organizationService: OrganizationService,
    private readonly mongoCredentialsService: MongoCredentialsService,
    private readonly ghlApiService: GhlApisService,
    private readonly channelService: ChannelsService,
    private readonly migrationService: MigrationService,
    private readonly userService: UserService,
    private readonly myLogger: MyLogger,
  ) {}

  @Get('slack/connect')
  async connectSlack(@Req() req: RequestWithUser, @Query() query: {code?:string, state?:string}, @Res() res: Response) {
    const { state=undefined, code=undefined } = query
    return await this.channelService.connectSlack(res, code, state);
  }

  @ApiOperation({ summary: 'Get all channels for an organization' })
  @ApiParam({ name: 'orgId', required: true })
  @Get(':orgId')
  async getChannels(@Param('orgId') orgId: string, @Res() res: Response) {
    const result = await this.channelService.getAllChannels(orgId);
    res.status(HttpStatus.OK).json(result);
  }

  @ApiOperation({ summary: 'Connect Gohighelvel channel' })
  @ApiOkResponse({
    description: 'An HTML page which shows the status of the connection',
  })
  @ApiQuery({ name: 'code', required: true })
  @ApiQuery({ name: 'state', required: true })
  @Get('connect/leadconnector')
  async connectGhl(
    @Query('code') code: string,
    @Query('state') state: string,
    @Res() res: Response,
  ) {
    let msg;
    try {
      if (!code || !state) throw new Error('Invalid request');
      const { orgId, usid = undefined } = decodeCode(state);
      this.myLogger.log({
        message: { orgId, usid },
      });

      const user: any = await this.userService.findUserBySessionId(usid);
      const userId = (user?._id || '').toString();
      if (userId === '') throw new Error('Invalid user Id received');

      const organization = await this.organizationService.getOrganizationDoc(
        orgId,
      );
      if (!organization) {
        return res.render('index', {
          type: 'Error',
          message: 'Organization not found',
          title: 'Failure',
          data: {},
        });
      }

      let { access_token, refresh_token, locationId } =
        await this.ghlApiService.exchangeGhlCodeForToken(code);
      const tokens = {
        access_token,
        refresh_token,
      };
      let channelData: any;

      await this.mongoCredentialsService.updateCredentials({
        query: { keyId: locationId, kind: KINDS.GHL_CREDENTIAL },
        updateBody: {
          creds: {
            accessToken: tokens.access_token,
            refreshToken: tokens.refresh_token,
          },
        },
      });

      const ghlCredInOtherOrg =
        await this.organizationService.checkChannelUnderOrgs({
          locationId,
          exceptOrgId: orgId,
          type: 'channel',
        });
      if (ghlCredInOtherOrg?.id)
        this.myLogger.log({
          message: `GHL connection: The channel already exists in another organization (orgId : ${ghlCredInOtherOrg.organizationId}) with credential Id: ${ghlCredInOtherOrg._id}`,
        });
      if (ghlCredInOtherOrg) {
        return res.render('index', {
          type: 'Error',
          message:
            'This leadconnector account/location is already connected to a different organization. Please remove the location from the other organization if you wish to proceed',
          title: 'Failure',
          credentialId: '',
          locationId,
          accountId: '',
          providerName: '',
          businessName: '',
          legacyRemaining: '',
          v2loc: '',
          userId: '',
          version: '',
          orgId: '',
        });
      }

      let { location } = await this.ghlApiService.getGhlLocation(
        tokens,
        locationId,
      );
      const ghlChannelIndex = organization.connections.channels.findIndex(
        (channel) => channel.keyId === locationId,
      );

      const accountId = uuidv4();
      const existingChannel =
        ghlChannelIndex !== -1
          ? organization.connections.channels[ghlChannelIndex]
          : undefined;
      let credentials;
      if (existingChannel?.credentialId) {
        await this.mongoCredentialsService.updateCredential(
          { _id: existingChannel.credentialId },
          {
            creds: {
              accessToken: tokens.access_token,
              refreshToken: tokens.refresh_token,
            },
            alertedUser: false,
          },
        );
        credentials = {
          id: existingChannel.credentialId,
        };
      } else {
        credentials = await this.mongoCredentialsService.createCredentials({
          kind: 'GhlCredential',
          keyId: locationId,
          creds: {
            accessToken: tokens.access_token,
            refreshToken: tokens.refresh_token,
          },
          organizationId: orgId,
          type: 'channel',
          alertedUser: false,
        });
      }
      msg = 'Channel Connected successfully';
      channelData = {
        credentialId: credentials.id,
        name: location?.name,
        providerName: PROVIDERS.GHL_CHANNEL,
        accountId,
        keyId: locationId,
        userId,
      };
      // update connection if it already exists else create a new one
      if (ghlChannelIndex !== -1) {
        channelData['accountId'] = existingChannel.accountId;
        await this.organizationService.updateOrganisation(
          { _id: orgId, 'connections.channels.keyId': locationId },
          { $set: { 'connections.channels.$': channelData } },
        );
      } else {
        await this.organizationService.updateOrganisation(
          { _id: orgId },
          { $push: { 'connections.channels': channelData } },
        );
      }
      let connectionData = {
        credentialId: credentials.id,
        accountId: channelData?.accountId,
        locationId: locationId,
        providerName: PROVIDERS.GHL_CHANNEL,
      };

      let v2loc = await this.migrationService.checkLocationInV2({
        locationId,
        accessToken: tokens.access_token,
        refreshToken: tokens.refresh_token,
      });
      this.myLogger.log({
        message: `channel for locationId ${locationId} created successfully`,
      });
      return res.render(
        'index',
        {
          type: 'Success',
          message: msg,
          title: 'Success',
          businessName: location?.name,
          ...connectionData,
          legacyRemaining: (v2loc?.legacyRemaining || []).length,
          v2loc,
          version: v2loc?.version || '',
          orgId,
          userId,
        },
        (err, html) => {
          if (err) {
            return res
              .status(HttpStatus.INTERNAL_SERVER_ERROR)
              .json({ message: err.message || 'Error in rendering page' });
          }
          return res.status(HttpStatus.OK).send(html);
        },
      );
    } catch (error) {
      return res.render('index', {
        type: 'Error',
        message:
          error?.message || 'Connection unsuccessful. Please Try Again !',
        title: 'Failure',
        credentialId: '',
        locationId: '',
        accountId: '',
        providerName: '',
        businessName: '',
        legacyRemaining: '',
        v2loc: '',
        userId: '',
        version: '',
        orgId: '',
      });
    }
  }

  @Delete('delete/:orgId/:accountId')
  @ApiOkResponse({
    type: DeleteChannelResponseDto,
    description: 'Channel deleted successfully',
  })
  @UseGuards(AuthGuard, allowAccessGuard('settings'))
  async deleteChannel(
    @Req() req: RequestWithUser,
    @Param() params: DeleteChannelDto,
    @Res() res: Response,
  ) {
    return await this.channelService.deleteConnectionfromOrg(
      params,
      res,
      req?.userId,
      req['isAdmin'],
    );
  }

  @Get('ghl/:locationId')
  @UseGuards(ApiTokenGuard)
  async getMappedAgent(
    @Req() req: RequestWithUser,
    @Param('locationId') locationId: string,
    @Res() res: Response,
  ) {
    try {
      const orgId = req['orgId'];
      const result = await this.channelService.getMappedAgentId(
        req.uniqueCode,
        orgId,
        locationId,
      );
      res.status(HttpStatus.OK).json(result);
    } catch (err) {
      this.myLogger.error({
        message:
          `@error in getting mapped agentId | reqId ${req.uniqueCode} > ` +
          err.message,
      });
      handleException(res, err);
    }
  }

  @ApiOperation({
    summary: 'Get all channels for an organization under the category',
  })
  @Get(':orgId/:provider')
  async getChannelList(
    @Param() params: GetChannelListDto,
    @Res() res: Response,
  ) {
    try {
      const { orgId, provider } = params;
      const obj = await this.organizationService.getOrganization(
        { _id: orgId },
        { 'connections.channels': 1 },
      );
      const channels = obj.connections.channels.filter(
        (channel) => channel.providerName === provider,
      );
      res.status(HttpStatus.OK).json({
        id: obj._id,
        channels: channels.map((channel) => {
          return {
            accountId: channel.accountId,
            name: channel.name,
            providerName: channel.providerName,
            keyId: channel.keyId,
          };
        }),
      });
    } catch (err) {
      this.myLogger.error({
        message: `@error in route /channels/:orgId/:provider > ` + err.message,
      });
      res
        .status(HttpStatus.INTERNAL_SERVER_ERROR)
        .json({ message: 'Error in fetching channels.' });
    }
  }

  @Patch(':orgId/ghl/:accountId')
  async editAgentsChannel(
    @Req() req: RequestWithUser,
    @Param('orgId') orgId: string,
    @Param('accountId') accountId: string,
    @Body() body: { agentIds: string[] },
    @Res() res: Response,
  ) {
    try {
      const result = await this.channelService.mapChannelToAgents(
        orgId,
        accountId,
        body.agentIds,
      );
      res.status(HttpStatus.OK).json({ message: result.message });
    } catch (err) {
      this.myLogger.error({
        message:
          `@error in route /:orgId/ghl/:accountId | reqId ${req.uniqueCode} > ` +
          err.message,
      });
      handleException(res, err);
    }
  }
}
