//  handles Podium review platform integration
import {
  Controller,
  Get,
  Post,
  Req,
  Res,
  Body,
  HttpStatus,
  Render,
  Delete,
  Param,
  Query,
} from '@nestjs/common';
import { Request, Response } from 'express';
import {
  ApiO<PERSON>ation,
  ApiQuery,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiParam,
} from '@nestjs/swagger';
import { PodiumService } from 'src/organization/services/channels/podium/podium.service';
import { GetPodiumConnectionDto } from 'src/organization/dto/requests/get.dto';
import { connectPodiumLocDto } from 'src/organization/dto/requests/post.dto';
import { WebhooksService } from 'src/organization/webhooks/webhooks.service';
import { handleException } from 'helpers/handleException';
import { MyLogger } from 'src/logger/logger.service';

@Controller('podium')
export class PodiumController {
  constructor(
    private readonly podiumService: PodiumService,
    private readonly webhookService: WebhooksService,
    private readonly myLogger: MyLogger,
  ) {}

  @Get('connect')
  async connectPodium(
    @Query() Q: GetPodiumConnectionDto,
    @Res() res: Response,
  ) {
    try {
      const redirectUri = process.env.BACKEND_URL + '/podium/connect';
      

      let { code = undefined, state = undefined } = Q;
      this.myLogger.log({
        message: { code, state },
      });
      if (!state) throw new Error('Invalid state passed in URL');
      if (!code) {
        const clientId = process.env.PODIUM_CLIENT_ID;
        const scope =
          'write_campaign_messages read_campaigns read_contacts write_contacts read_locations write_locations read_messages write_messages read_organizations read_users';
        return res.render('alert', {
          title: 'Alert!',
          message: `Before continuing please ensure that you are already logged in with Podium within the same browser. To continue, click on the link below.`,
          link: `https://api.podium.com/oauth/authorize?client_id=${clientId}&redirect_uri=${redirectUri}&scope=${scope}&state=${state}`,
        });
      } else {
        const orgId = state; //decodeCode(state).orgId;
        this.myLogger.log({
          message: `Organization received: ${orgId}`,
        });
        const {
          locations = undefined,
          podiumOrgId = undefined,
          tokens = null,
        } = await this.podiumService.connectPodium(code, orgId);
        if (!podiumOrgId)
          return res.render('podium', {
            type: 'Error',
            message: 'Podium Organization connection failed',
            title: 'Failure',
          });

        return res.render('podium', {
          type: 'Success',
          message: 'Podium channel connected successfully!',
          title: 'Success',
          locations,
          podiumOrgId,
          orgId,
          ...tokens,
        });
      }
    } catch (error) {
      this.myLogger.error({
        message: 'Error in connecting podium ' + error?.message,
      });
      res.render('podium', {
        type: 'Error',
        message: 'Failed to connect podium',
        title: 'Failure',
        locations: undefined,
        podiumOrgId: undefined,
        orgId: undefined,
        accessToken: undefined,
        refreshToken: undefined,
      });
    }
  }

  @Post('connect/location')
  async connectPodiumLocation(
    @Body() body: connectPodiumLocDto,
    @Res() res: Response,
  ) {
    try {
      const { orgId, locationId, tokens, locationName, podiumOrgId } = body;
      const data = await this.podiumService.connectPodiumLocation(
        orgId,
        locationId,
        tokens,
        locationName,
        podiumOrgId,
      );
      data['author'] = data.keyId;
      return res
        .status(HttpStatus.OK)
        .json({ message: `Connected podium location successfully`, data });
    } catch (error) {
      this.myLogger.error({
        message: 'Error in connecting podium location ' + error?.message,
      });
      handleException(res, error);
    }
  }

  @Post('webhook')
  async handleWebhook(@Body() body: any, @Res() res: Response) {
    res.sendStatus(200);
    try {
      await this.webhookService.messageHandler(
        'podium',
        body,
        body,
        body.orgId,
      );
    } catch (error) {
      this.myLogger.error({
        message: `@error in processing podium webhook:` + error.message,
      });
    }
  }
}
