// Contains the code to process GHL webhook to update contact custom fields to search conversations to get messages by conversation id.
import {
  <PERSON>,
  Get,
  Post,
  Req,
  Res,
  Body,
  HttpStatus,
  Put,
  Query,
  Param,
} from '@nestjs/common';
import { Response } from 'express';
import { ApiOperation } from '@nestjs/swagger';
import { GhlService } from 'src/organization/services/channels/ghl/ghl.service';
import { RequestWithUser } from 'src/auth/auth.interface';
import { MyLogger } from 'src/logger/logger.service';
// const supportedChannels = ["SMS"];

@Controller('leadconnector')
export class GhlController {
  constructor(
    private readonly ghlService: GhlService,
    private readonly myLogger: MyLogger,
  ) {}

  @ApiOperation({ summary: 'Webhook for gohighlevel events or crm' })
  @Post('/webhook')
  async processWebhook(
    @Req() req: RequestWithUser,
    @Res() res: Response,
    @Body() payload: { [key: string]: any },
  ) {
    res.sendStatus(HttpStatus.OK);
    try {
      // if (payload?.direction === "inbound") return;
      if (payload.type === 'ContactDelete') {
        await this.ghlService.deleteContacts(payload);
      } else if (
        payload.type === 'OutboundMessage' ||
        payload.type === 'InboundMessage' || 
        payload.type === 'followUp'
      ) {
        // Never log emails
        if (payload.messageType === 'Email') {
          // Skip logging for all email messages
        } else {
          this.myLogger.log({
            message: `${payload.correlationId ? `SCHEDULED REQUEST` : 'GHL CRM WEBHOOK'} | source ${payload.source} | locationId: ${
              payload.locationId
            } | contactId: ${payload.contactId} | payload: ${JSON.stringify(
              payload,
            )}`,
          });
        }
        await this.ghlService.performAction(req?.uniqueCode, payload, payload);
      }
    } catch (error) {
      this.myLogger.error({
        message:
          `@error | reqId ${req.uniqueCode} | ghlwebhook: ` + error.message,
      });
      this.myLogger.error({
        message:
          `@error | reqId ${req.uniqueCode} | ghlwebhook: ` + error.message,
      });
    }
  }

  @ApiOperation({
    summary:
      'Webhook for handling gohighlevel workflow events for conversations',
  })
  @Post('/workflow/webhook')
  async processOverrideWebhook(
    @Req() req: RequestWithUser,
    @Res() res: Response,
    @Body() payload: any,
  ) {
    this.myLogger.log({
      message: `${payload.correlationId ? `SCHEDULED WORKFLOW WEBHOOK REQUEST` : 'GHL WORKFLOW WEBHOOK'} | locationId: ${
        payload.locationId
      } | contactId: ${payload.contactId} | payload: ${JSON.stringify(
        payload,
      )}`,
    });
    
    res.sendStatus(HttpStatus.OK);
    try {
      const messageBody = {
        contactId: payload.contactId,
        body: payload.message,
        messageType: payload.channel || payload.messageType,
        locationId: payload.locationId,
        direction: 'inbound',
      };
      // check if any key startswith tags: and add the string after tag: to tags array
      const tags = Object.keys(payload)
        .filter((key) => key.startsWith('tag:'))
        .map((key) => key.split(':')[1]);
      await this.ghlService.performAction(
        req?.uniqueCode,
        messageBody,
        payload,
        tags,
        'ghl',
      );
    } catch (error) {
      this.myLogger.error({
        message:
          `@error | reqId ${req.uniqueCode} | ghl workflow webhook: ` +
          error.message,
      });
    }
  }

  @Post('/createContactAndAddTag')
  @ApiOperation({
    summary:
      'Whenever a new trial user signs up, they need to be added as a contact with their email and name to our Capri Client Acquisition CRM account and tagged as a trial user.',
  })
  async createContactAndAddTag(
    @Req() req: RequestWithUser,
    @Res() res: Response,
  ) {
    try {
      let { name, email } = req.body;

      //Capri Client Acquisition CRM account
      const locationId = 'hqD2EpUwBJg1nEBWr4jT';

      let tags = ['trial_user'];

      // Create a contact and add a tag
      const contact = await this.ghlService.createContact(
        req.uniqueCode,
        locationId,
        name,
        email,
        tags,
      );

      return res.status(HttpStatus.OK).json(contact);
    } catch (error) {
      this.myLogger.error({
        message:
          `@error | reqId ${req.uniqueCode} | createContactAndAddTag: ` +
          error.message,
      });
      return res
        .status(HttpStatus.INTERNAL_SERVER_ERROR)
        .json({ message: error.message });
    }
  }

  @Put('/updateContactCustomFields')
  @ApiOperation({
    summary:
      'Whenever a user updates their profile, we need to update their custom fields in our Capri Client Acquisition CRM account.',
  })
  async updateContactCustomFields(
    @Req() req: RequestWithUser,
    @Res() res: Response,
  ) {
    try {
      let { locationId, contactId, customField } = req.body;

      // Update a contact custom field
      const contact = await this.ghlService.updateCustomField(
        locationId,
        contactId,
        customField,
      );

      return res.status(HttpStatus.OK).json(contact);
    } catch (error) {
      this.myLogger.error({
        message:
          `@error | reqId ${req.uniqueCode} | updateContactCustomFields: ` +
          error.message,
      });
      return res
        .status(HttpStatus.INTERNAL_SERVER_ERROR)
        .json({ message: error.message });
    }
  }

  @Get('/getContact')
  async getContact(@Req() req: RequestWithUser, @Res() res: Response) {
    try {
      let { locationId, contactId, email } = req.body;

      const contact = await this.ghlService.getContact(
        req.uniqueCode,
        locationId,
        contactId,
        email,
      );

      return res.status(HttpStatus.OK).json(contact);
    } catch (error) {
      this.myLogger.error({
        message:
          `@error | reqId ${req.uniqueCode} | getContact: ` + error.message,
      });
      return res
        .status(HttpStatus.INTERNAL_SERVER_ERROR)
        .json({ message: error.message });
    }
  }
  @Get('/searchConversations')
  @ApiOperation({
    summary: 'Search conversations based on given parameters',
  })
  async searchConversations(
    @Req() req: RequestWithUser,
    @Res() res: Response,
    @Query('locationId') locationId: string,
    @Query('assignedTo') assignedTo?: string,
    @Query('contactId') contactId?: string,
    @Query('id') id?: string,
    @Query('lastMessageAction') lastMessageAction?: string,
    @Query('lastMessageDirection') lastMessageDirection?: string,
    @Query('lastMessageType') lastMessageType?: string,
    @Query('limit') limit?: number,
    @Query('query') query?: string,
    @Query('scoreProfile') scoreProfile?: string,
    @Query('scoreProfileMax') scoreProfileMax?: number,
    @Query('scoreProfileMin') scoreProfileMin?: number,
    @Query('sort') sort?: string,
    @Query('sortBy') sortBy?: string,
    @Query('sortScoreProfile') sortScoreProfile?: string,
    @Query('startAfterDate') startAfterDate?: string,
    @Query('status') status?: string,
  ) {
    try {
      const params = {
        locationId,
        assignedTo,
        contactId,
        id,
        lastMessageAction,
        lastMessageDirection,
        lastMessageType,
        limit,
        query,
        scoreProfile,
        scoreProfileMax,
        scoreProfileMin,
        sort,
        sortBy,
        sortScoreProfile,
        startAfterDate,
        status,
      };

      // Remove undefined values from params
      Object.keys(params).forEach(
        (key) => params[key] === undefined && delete params[key],
      );

      const result = await this.ghlService.searchConversations(
        req.uniqueCode,
        locationId,
        params,
      );

      return res.status(HttpStatus.OK).json(result);
    } catch (error) {
      this.myLogger.error({
        message:
          `@error | reqId ${req.uniqueCode} | searchConversations: ` +
          error.message,
      });
      return res
        .status(HttpStatus.INTERNAL_SERVER_ERROR)
        .json({ message: error.message });
    }
  }

  @Get('/:locationId/:conversationId/messages')
  @ApiOperation({
    summary: 'Get messages by conversation ID',
  })
  async getMessagesByConversationId(
    @Res() res: Response,
    @Param('locationId') locationId: string,
    @Param('conversationId') conversationId: string,
    @Query('lastMessageId') lastMessageId?: string,
    @Query('limit') limit?: number,
  ) {
    try {
      const params = {
        lastMessageId,
        limit,
      };

      // Remove undefined values from params
      Object.keys(params).forEach(
        (key) => params[key] === undefined && delete params[key],
      );

      const result = await this.ghlService.getMessagesByConversationId(
        locationId,
        conversationId,
        params,
      );

      return res.status(HttpStatus.OK).json(result);
    } catch (error) {
      // Handle error
      return res
        .status(HttpStatus.INTERNAL_SERVER_ERROR)
        .json({ message: error.message });
    }
  }
}
