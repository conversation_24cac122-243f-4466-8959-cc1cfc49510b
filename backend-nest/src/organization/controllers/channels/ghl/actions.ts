import { PROVIDERS } from "src/lib/constant"
/**
 * @example const result = Actions[PROVIDERS.GHL_CALENDAR].handler(callbackFunction, startDate);
 * @example Actions[PROVIDERS.GHL_CALENDAR].success(startDate)
 */
export const Actions: ActionsMap = {
    [PROVIDERS.GHL_CALENDAR]: {
        'write': {
            action: 'create appointment',
            provider: PROVIDERS.GHL_CALENDAR,
            handler: (callback, ...args) => {
                // Call the callback function with the provided arguments
                return callback(...args);
            },
            success: (startDate) => `An appointment has been scheduled on ${startDate}`,
            error: (startDate) => `An error occurred while scheduling an appointment on ${startDate}`
        },
    },
}

export interface AppointmentAction {
    action: string;
    provider: string;
    handler: (...args: any[]) => any;
    success: (...args: any[]) => string;
    error: (...args: any[]) => string;
}

interface ActionsMap {
    [key: string]: {
        'read'?: AppointmentAction;
        'write'?: AppointmentAction;
    }
}