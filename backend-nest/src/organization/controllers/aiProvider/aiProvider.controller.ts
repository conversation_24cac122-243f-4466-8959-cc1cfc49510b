//  handles AI provider integration endpoints
import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Query,
  Req,
  Res,
  UseGuards,
} from '@nestjs/common';
import { Response, Request } from 'express';
import { AuthGuard } from 'src/auth/auth.guard';
import { RequestWithUser } from 'src/auth/auth.interface';
import { allowAccessGuard } from 'src/guards/can-access/can-access.guard';
import { AiProviderService } from 'src/organization/services/aiProviders/aiProvider.service';

@Controller('aiprovider')
export class AiProviderController {
  constructor(private readonly aiProviderService: AiProviderService) {}

  @Get(':orgId')
  async getAiProviders(
    @Param('orgId') orgId: string,
    @Res() response: Response,
  ) {
    const aiProviders = await this.aiProviderService.getAiProviders(
      orgId,
      response,
    );
  }

  @UseGuards(AuthGuard)
  @Post('/create')
  async createAiProvider(
    @Req() req: RequestWithUser,
    @Body() body: any,
    @Res() response: Response,
  ) {
    const userId = req.userId;
    const createAiProvider = await this.aiProviderService.createAiProvider(
      { ...body, userId },
      response,
    );
  }

  @Get('/agent/check')
  @UseGuards(AuthGuard, allowAccessGuard('settings'))
  async checkAiAccountIsBeingUsedInAgent(
    @Req() req: RequestWithUser,
    @Query('orgId') orgId: string,
    @Query('accountId') accountId: string,
    @Res() response: Response,
  ) {
    await this.aiProviderService.checkAiAccount(
      orgId,
      accountId,
      response,
      req.userId,
      req['isAdmin'],
    );
  }

  @Delete('/delete')
  @UseGuards(AuthGuard, allowAccessGuard('settings'))
  async deleteAiAccount(
    @Req() req: RequestWithUser,
    @Query('orgId') orgId: string,
    @Query('accountId') accountId: string,
    @Res() response: Response,
  ) {
    const deleteAiAccount = await this.aiProviderService.deleteAiAccount(
      orgId,
      accountId,
      response,
      req.userId,
      req['isAdmin'],
    );
  }

  @Get('list/:orgId')
  async getAiProvidersList(
    @Param('orgId') orgId: string,
    @Res() response: Response,
  ) {
    try {
      const aiProviders = await this.aiProviderService.getAllAiProviders(orgId);
      response.status(200).json({ message: 'success', data: aiProviders });
    } catch (err) {
      response.status(err?.code || 500).json({ message: err.message });
    }
  }
}
