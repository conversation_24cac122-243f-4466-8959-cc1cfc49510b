// Contains all organization-related API controllers
import {
  Controller,
  Post,
  Body,
  Res,
  HttpStatus,
  Get,
  Param,
  HttpException,
  Delete,
  Query,
  Put,
  Req,
  RawBodyRequest,
  Patch,
  UseGuards,
} from '@nestjs/common';
import { Response } from 'express';
import { OrganizationService } from '../services/organization.service';
import { GetOrganizationDto } from '../dto/requests/get.dto';
import { ApiHeader, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { AddNewMemberDto } from '../dto/requests/addNewMemberDto';
import { ChoosePlanBasicDto } from 'src/billing/dto/ChoosePlanDto';
import {
  CreateOrganizationDto,
  CreateOrganizationUphexDto,
} from 'src/mongo/dto/organizations/createOrg.dto';
import { handleException } from 'helpers/handleException';
import { RequestWithUser } from 'src/auth/auth.interface';
import { Public } from 'src/decorators/public.decorator';
import { UserService } from 'src/user/user.service';
import { GhlService } from '../services/channels/ghl/ghl.service';
import { v4 as uuidv4 } from 'uuid';
import { MyLogger } from 'src/logger/logger.service';
import { MongoAiUsageService } from 'src/mongo/service/aiUsage/MongoAiUsageService';
import { MongoOrganizationService } from 'src/mongo/service/organization/mongo-organization.service';

import { AutoPayDto } from '../dto/autopay.dto';
import { AddUsageDto } from '../dto/add-usage.dto';
import { VoiceUsageService } from '../services/usage/voice-usage/voice-usage.service';
import { ClientAuthGuard } from 'src/guards/uphex-auth.guard';
import { UpdateVisibilityCodesDto } from '../dto/update-visibility-codes';

@Public()
@Controller('organization')
// @UseGuards(BillingLimitsGuard)
// @UseFilters(BillingRedirectFilter)
export class OrganizationController {
  constructor(
    private readonly organizationService: OrganizationService,
    private readonly mongoOrganizationService: MongoOrganizationService,
    private readonly userService: UserService,
    private readonly ghlService: GhlService,
    private readonly myLogger: MyLogger,
    private readonly mongoAiUsageService: MongoAiUsageService,
    private readonly voiceUsageService: VoiceUsageService,
  ) {}
  @Post('create')
  @ApiOperation({ summary: 'Creates a new organization' })
  async createOrganization(
    @Body() createOrganizationDto: CreateOrganizationDto,
    @Req() request: RequestWithUser,
    @Res() response: Response,
  ) {
    const checkIfUserHasOrg = await this.organizationService.checkIfUserHasOrg(
      request.email,
    );
    const userId = request.userId;
    this.myLogger.log({ message: { createOrganizationDto } });

    let ghlTagAdded;
    const uuid = uuidv4();

    try {
      ghlTagAdded = await this.ghlService.createContact(
        uuid,
        'hqD2EpUwBJg1nEBWr4jT',
        createOrganizationDto.userName,
        createOrganizationDto.email,
        ['trial_user'],
      );
      this.myLogger.log({ message: { ghlTagAdded } });
    } catch (error) {
      this.myLogger.error({
        message: 'Error creating org ' + error,
        context: 'OrganizationController',
      });
      // Handle the error as needed
    }

    const newOrganization = this.organizationService.createOrganization(
      { ...createOrganizationDto, userId },
      response,
    );
  }

  @Post('create/uphex')
  @UseGuards(ClientAuthGuard)
  @ApiOperation({ summary: 'Creates a new organization for Uphex' })
  @ApiHeader({
    name: 'X-Client-Id',
    description: 'Client ID for authentication',
    required: true,
  })
  @ApiHeader({
    name: 'X-Client-Secret',
    description: 'Client Secret for authentication',
    required: true,
  })
  @ApiResponse({
    status: 201,
    description: 'Organization successfully created',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid client credentials',
  })
  async createOrganizationForUphex(
    @Body() createOrganizationForUphex: CreateOrganizationUphexDto,
  ) {
    return await this.organizationService.createOrganizationForUphex(
      createOrganizationForUphex,
    );
  }

  @Get(':orgId/setup-status')
  @ApiOperation({ summary: 'Get Organization Setup Status' })
  async getOrganizationSetupStatus(@Param() param: { orgId: string }) {
    try {
      const status = await this.organizationService.getOrganizationSetupStatus(
        param.orgId,
      );
      return status;
    } catch (error) {
      return { error: error.message };
    }
  }

  @Get('remove-cookie')
  async removeCookie(@Res() response: Response) {
    try {
      response.clearCookie('usid');
      response.clearCookie('orgId');
      response.status(HttpStatus.NO_CONTENT).send();
    } catch (error) {
      handleException(response, error);
    }
  }

  @Get('set-cookie/:orgId')
  @ApiOperation({ summary: 'Sets the cookie to the frontend' })
  async setCookie(@Param() param: { orgId: string }, @Res() res: Response) {
    try {
      const { orgId } = param;
      const dateInTheFuture = new Date();
      dateInTheFuture.setFullYear(dateInTheFuture.getFullYear() + 100);
      res.cookie('orgId', orgId, {
        httpOnly: true,
        secure: process.env.NODE_ENV !== 'DEVELOPMENT',
        expires: dateInTheFuture,
        domain: process.env.DOMAIN,
        sameSite: process.env.NODE_ENV === 'DEVELOPMENT' ? 'lax' : 'none',
      });
      return res.status(HttpStatus.NO_CONTENT).send();
    } catch (error) {
      return res
        .status(HttpStatus.INTERNAL_SERVER_ERROR)
        .json({ error: 'Internal Server Error' });
    }
  }

  @Public()
  @Get(':orgId')
  @ApiOperation({ summary: 'Get Organization' })
  async getOrganization(
    @Param() param: GetOrganizationDto,
    @Res() res: Response,
  ) {
    this.myLogger.log({
      message: 'Getting organization ' + param.orgId,
      context: 'OrganizationController',
    });
    try {
      const org = await this.organizationService.getOrganization({
        _id: param.orgId,
      });
      const dateInTheFuture = new Date();
      dateInTheFuture.setFullYear(dateInTheFuture.getFullYear() + 100);
      res.cookie('orgId', org._id.toString(), {
        httpOnly: true,
        secure: process.env.NODE_ENV !== 'DEVELOPMENT',
        expires: dateInTheFuture,
        domain: process.env.DOMAIN,
        sameSite: process.env.NODE_ENV === 'DEVELOPMENT' ? 'lax' : 'none',
      });
      return res.status(HttpStatus.OK).json(org);
    } catch (error) {
      this.myLogger.error({
        message: error.message,
        context: 'OrganizationController',
      });
      return res
        .status(HttpStatus.INTERNAL_SERVER_ERROR)
        .json({ error: 'Internal Server Error' });
    }
  }

  @Post('billing/plan/choose')
  @ApiOperation({ summary: 'Choose a new plan' })
  async choosePlan(
    @Body() payload: ChoosePlanBasicDto,
    @Res() response: Response,
  ) {
    try {
      const data = await this.organizationService.choosePlan(payload);
      return response
        .status(200)
        .json({ message: 'Plan updated successfully', data });
    } catch (error) {
      this.myLogger.error({
        message: error.message,
        context: 'OrganizationController',
      });
      if (error instanceof HttpException) {
        return response
          .status(error.getStatus())
          .json({ message: error.getResponse() });
      } else {
        return response
          .status(HttpStatus.INTERNAL_SERVER_ERROR)
          .json({ error: error.message || 'Internal Server Error' });
      }
    }
  }

  @Get('billing/details/:orgId')
  @Public()
  @ApiOperation({ summary: 'Gets the billing details of the organization' })
  async getBillingDetails(
    @Param() param: { orgId: string },
    @Res() response: Response,
  ) {
    try {
      const { billing, agentCount, billingExpired, nextPaymentDate } =
        await this.organizationService.getBillingDetails({
          orgId: param.orgId,
        });
      return response.status(200).json({
        message: 'Billing details fetched successfully.',
        billingDetails: billing,
        agentCount,
        nextPaymentDate,
        billingExpired,
      });
    } catch (error) {
      if (error instanceof HttpException) {
        return response
          .status(error.getStatus())
          .json({ message: error.getResponse() });
      } else {
        return response
          .status(HttpStatus.INTERNAL_SERVER_ERROR)
          .json({ error: 'Internal Server Error' });
      }
    }
  }

  @Put('billing/subscription/cancel/:orgId')
  @ApiOperation({ summary: 'Cancels the subscription of the organization' })
  async cancelBilling(
    @Param() param: { orgId: string },
    @Res() response: Response,
  ) {
    try {
      const cancelSubscription =
        await this.organizationService.cancelSubscription({
          orgId: param.orgId,
        });
      return response.status(200).json({
        message: 'Cancelled subscription successfully',
      });
    } catch (error) {
      if (error instanceof HttpException) {
        return response
          .status(error.getStatus())
          .json({ message: error.getResponse() });
      } else {
        return response
          .status(HttpStatus.INTERNAL_SERVER_ERROR)
          .json({ error: 'Internal Server Error' });
      }
    }
  }

  @Get('/billing/success')
  @ApiOperation({ summary: 'The success route of Stripe checkout' })
  async stripeSuccess(
    @Query('session_id') session_id: string,
    @Query('price_id') price_id: string,
    @Res() response: Response,
  ) {
    try {
      this.myLogger.log({
        message: `Stripe checkout success ${session_id} ${price_id}}`,
        context: 'OrganizationController',
      });
      const stripeRes = await this.organizationService.handleStripeSuccess({
        session_id,
        price_id,
      });
      return response.redirect(
        `${process.env.FRONTEND_URL}/home/<USER>/upgrade?success=true`,
      );
    } catch (error) {
      return response
        .status(HttpStatus.INTERNAL_SERVER_ERROR)
        .json({ error: 'Internal Server Error' });
    }
  }

  @Get('stats/:orgId')
  @ApiOperation({ summary: 'Get Organization Stats' })
  async getOrganizationStats(
    @Param() param: GetOrganizationDto,
    @Res() res: Response,
  ) {
    try {
      const org = await this.organizationService.getOrganization({
        _id: param.orgId,
      });
      const totalAgents = org.agents.length;
      const totalAiConnections = org.connections.aiProvider.length;
      const totalConnections =
        org.connections.channels.length +
        org.connections.dataSources.length +
        org.connections.staticData.length;
      const totalSavedSessions = org.sessions.filter(
        (item) => item.saved === true,
      ).length;

      return res.status(HttpStatus.OK).json({
        message: 'Stats fetched successfully.',
        data: {
          totalAgents,
          totalAiConnections,
          totalConnections,
          totalSavedSessions,
        },
      });
    } catch (error) {
      handleException(res, error);
    }
  }

  @Get('resources/:orgId')
  @ApiOperation({ summary: 'Get Organization' })
  async getOrganizationResources(
    @Param() param: GetOrganizationDto,
    @Res() res: Response,
  ) {
    try {
      const org = await this.organizationService.getOrganization({
        _id: param.orgId,
      });
      const resources = [
        ...org.connections.dataSources,
        ...org.connections.staticData,
      ];
      return res.status(HttpStatus.OK).json({ datasources: resources });
    } catch (error) {
      return res
        .status(HttpStatus.INTERNAL_SERVER_ERROR)
        .json({ error: 'Internal Server Error' });
    }
  }

  @Post('member/add')
  @ApiOperation({ summary: 'Adds a new member to the organization' })
  async addNewMember(
    @Body() addNewMemberDto: AddNewMemberDto,
    @Req() request: RequestWithUser,
    @Res() response: Response,
  ) {
    try {
      const userId = request.userId; //person who sent the request
      const res = await this.organizationService.addNewMember(
        addNewMemberDto,
        userId,
      );
      return response.status(200).json({ message: 'Request sent' });
    } catch (error) {
      handleException(response, error);
    }
  }

  @Delete('member/:orgId/:deletedUserId')
  @ApiOperation({ summary: 'Remove a member' })
  async removeMember(
    @Param() param: { orgId: string; deletedUserId: string },
    @Req() request: RequestWithUser,
    @Res() response: Response,
  ) {
    try {
      //get the userId
      const { deletedUserId, orgId } = param;
      const userId = request.userId;
      const org = await this.organizationService.getOrganization(
        { _id: orgId },
        { admin: 1 },
      );
      const { admin } = org;
      if (!admin.includes(userId) && userId !== deletedUserId) {
        throw new HttpException(
          'Only admins can remove user.',
          HttpStatus.FORBIDDEN,
        );
      }
      if (admin.length === 1 && deletedUserId === admin[0]) {
        throw new HttpException(
          'Organization required atleast once admin.',
          HttpStatus.FORBIDDEN,
        );
      }
      const userRemovedFromOrg =
        await this.organizationService.updateOrganisation(
          { _id: orgId },
          {
            $pull: {
              admin: deletedUserId,
              members: { userId: deletedUserId },
            },
          },
        );
      const userRemovedFromUser = await this.userService.updateUser(
        { _id: deletedUserId },
        {
          $pull: {
            organizations: orgId,
          },
        },
      );
      return response
        .status(200)
        .json({ message: 'User removed successfully.' });
    } catch (error) {
      handleException(response, error);
    }
  }

  @Get('members/:orgId')
  @ApiOperation({ summary: 'Get Organization members' })
  async getOrganizationMembers(
    @Param() param: GetOrganizationDto,
    @Res() res: Response,
  ) {
    try {
      const people = await this.organizationService.getOrganizationMembers({
        _id: param.orgId,
      });
      return res.status(HttpStatus.OK).json({ people });
    } catch (error) {
      return res
        .status(HttpStatus.INTERNAL_SERVER_ERROR)
        .json({ error: 'Internal Server Error' });
    }
  }

  @Get('/billing/cron/test')
  async testCron() {
    await this.organizationService.checkBillingStatus();
    return 'Cron job ran successfully';
  }

  // @Get('/script/validate-plans')
  // async validatePlans() {
  //   const orgIds = await this.organizationService.validatePlans();
  //   return { message: 'Script ran successfully', orgIds };
  // }

  @Post('/webhook/stripe/plan-expired')
  async billingExpired(
    @Req() req: RawBodyRequest<Request>,
    @Res() res: Response,
  ) {
    try {
      const sig = req.headers['stripe-signature'];
      const event = this.organizationService.handleBillingExpired(req, sig);
      return res.json({ received: true });
    } catch (error) {
      this.myLogger.error({
        message: error.message,
      });
    }
  }

  @Post('/webhook/stripe/llm-creds-added')
  async llmCredsAdded(
    @Req() req: RawBodyRequest<Request>,
    @Res() res: Response,
  ) {
    try {
      const sig = req.headers['stripe-signature'];
      const event = this.organizationService.handleLlmCredsAdded(req, sig);
      return res.json({ received: true });
    } catch (error) {
      this.myLogger.error({
        message: error.message,
      });
    }
  }

  // @Public()
  // @Get('/script/suspended-active-agents')
  // async getNumOfSuspendedActiveAgents() {
  //   return await this.organizationService.getNumOfSuspendedActiveAgents();
  // }
  // @Get('/script/billing-revalidate')
  // async billingRevalidate() {
  //   await this.organizationService.revalidatePlans();
  //   return 'Script ran successfully';

  // }

  @Post('/ai-usage')
  async createAiUsage(@Body() body: any) {
    try {
      const { orgId, agentId, location, usage } = body;
      const usageObj = await this.mongoOrganizationService.updateHostedAiUsage(
        orgId,
        agentId,
        location,
        usage,
      );
      // return this.mongoAiUsageService.addRecord(body);
    } catch (error) {
      
    }
  }

  @Patch(':orgId/billing/autopay')
  async updateAutoPay(@Body() body: AutoPayDto, @Param('orgId') orgId: string) {
    try {
      return this.mongoOrganizationService.updateAutoPay(orgId, body);
    } catch (error) {
      
    }
  }

  @Patch(':orgId/iframe/visibility-codes')
  async updateIframeVisibilityCodes(
    @Param() param: { orgId: string },
    @Body() body: UpdateVisibilityCodesDto,
    @Res() res: Response,
  ) {
    try {
      const { orgId } = param;
      const iframeConfig =
        await this.organizationService.updateIframeVisibilityCodes(orgId, body);
      return res.status(HttpStatus.OK).json({ iframeConfig });
    } catch (error) {
      this.myLogger.error({
        message: error.message,
        context: `Organization ${param.orgId} | Update`,
      });
      handleException(res, error);
    }
  }

  @Get('/ai-usage/:orgId/month')
  getAiUsageThisMonth(@Param('orgId') orgId: string) {
    return this.mongoAiUsageService.getTotalCostThisMonth(orgId);
  }
  @Get('/ai-usage/:orgId')
  async getAiUsage(
    @Param('orgId') orgId: string,
    @Query('from') from?: string,
    @Query('to') to?: string,
  ) {
    try {
      

      return await this.mongoAiUsageService.getRecordByOrganizationId({
        organizationId: orgId,
        startDate: from ? new Date(Number(from)) : undefined,
        endDate: to ? new Date(Number(to)) : undefined,
      });
    } catch (error) {
      
    }
  }

  @Get(':orgId/billing/addon-amount')
  async getAddonAmount(@Param('orgId') orgId: string) {
    const addonAmount = await this.mongoOrganizationService.getAddonAmount(
      orgId,
    );
    return { addonAmount };
  }

  @Patch(':id/error-report')
  async updateErrorReport(
    @Param('id') organizationId: string,
    @Body()
    errorReportDto: {
      emails?: string[];
      slackChannelIds?: string[];
      disabled?: boolean;
    },
  ) {
    const query = { _id: organizationId };

    const updateBody = {
      $set: {
        'errorReport.emails': errorReportDto.emails || [], // Update emails if provided, else keep an empty array
        'errorReport.slackChannelIds': errorReportDto.slackChannelIds || [], // Update slackChannelIds if provided
        'errorReport.disabled':
          errorReportDto.disabled !== undefined
            ? errorReportDto.disabled
            : false,
      },
    };

    const updatedOrg = await this.organizationService.updateOrganisation(
      query,
      updateBody,
    );

    return updatedOrg;
  }

  @Post('/usage')
  async addUsage(@Body() body: AddUsageDto) {
    const { agentId, voiceDetails } = body;
    return await this.voiceUsageService.addUsage(agentId, {
      usageSeconds: voiceDetails.numOfSeconds,
    });
  }

  @Get('/list/phones')
  async listPhoneNumbers(@Query('orgId') orgId: string, @Res() res: Response) {
    try {
      const phones =
        await this.organizationService.listPhoneNumbersForOrganization(orgId);
      return res.status(HttpStatus.OK).json(phones);
    } catch (error) {
      this.myLogger.error({
        message: error.message,
        context: `Organization ${orgId} | List phone numbers`,
      });
      handleException(res, error);
    }
  }
}
