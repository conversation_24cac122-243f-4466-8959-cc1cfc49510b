import {
  Body,
  Controller,
  Delete,
  HttpStatus,
  Param,
  Post,
  Req,
  Res,
} from '@nestjs/common';
import { Response } from 'express';
import { RequestWithUser } from '../../../../auth/auth.interface';
import { GoogleDocsService } from '../../../services/data/google-docs/goole-docs.service';

@Controller('google-docs')
export class GoogleDocsController {
  constructor(private readonly googleDocsService: GoogleDocsService) {}

  @Post('connect/:orgId')
  async connectDoc(
    @Param('orgId') orgId: string,
    @Body('docLink') docLink: string,
    @Body('userName') userName: string,
    @Req() req: RequestWithUser,
    @Res() res: Response,
  ) {
    let userId = req.userId;
    try {
      const docId = this.extractDocId(docLink);
      const doc = await this.googleDocsService.connectDoc({
        docId,
        orgId,
        userId,
        docLink,
        userName,
      });
      res.status(HttpStatus.OK).json(doc);
    } catch (error) {
      res.status(HttpStatus.BAD_REQUEST).json({ message: error.message });
    }
  }

  @Delete('disconnect/:orgId')
  async disconnectDoc(
    @Param('orgId') orgId: string,
    @Body('docLink') docLink: string,
    @Res() res: Response,
  ): Promise<void> {
    try {
      const docId = this.extractDocId(docLink);
      const doc = await this.googleDocsService.disconnectDoc({
        orgId,
        docId,
      });
      res.status(HttpStatus.OK).json(doc);
    } catch (error) {
      res.status(HttpStatus.BAD_REQUEST).json({ message: error.message });
    }
  }

  // Utility method to extract the docId from a Google Docs link.
  private extractDocId(docLink: string): string {
    const match = docLink.match(/\/d\/(.+?)\//);
    if (!match) throw new Error('Invalid Google Docs link.');
    return match[1];
  }
}
