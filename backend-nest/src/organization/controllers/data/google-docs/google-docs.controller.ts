import {
  Body,
  Controller,
  Delete,
  Get,
  HttpStatus,
  Param,
  Patch,
  Post,
  Req,
  Res,
} from '@nestjs/common';
import { Response } from 'express';
import { RequestWithUser } from '../../../../auth/auth.interface';
import { GoogleDocsService } from '../../../services/data/google-docs/goole-docs.service';

@Controller('data/google-docs')
export class GoogleDocsController {
  constructor(private readonly googleDocsService: GoogleDocsService) {}

  @Post('connect/:orgId')
  async connectDoc(
    @Param('orgId') orgId: string,
    @Body('docLink') docLink: string,
    @Body('userName') userName: string,
    @Req() req: RequestWithUser,
    @Res() res: Response,
  ) {
    let userId = req.userId;
    try {
      const docId = this.extractDocId(docLink);
      const doc = await this.googleDocsService.connectDoc({
        docId,
        orgId,
        userId,
        docLink,
        userName,
      });
      res.status(HttpStatus.OK).json(doc);
    } catch (error) {
      res.status(HttpStatus.BAD_REQUEST).json({ message: error.message });
    }
  }

  @Patch('update/:orgId/:accountId')
  async updateDoc(
    @Param('orgId') orgId: string,
    @Param('accountId') accountId: string,
    @Body('name') name: string,
    @Req() req: RequestWithUser,
    @Res() res: Response,
  ) {
    let userId = req.userId;
    try {
      const update = await this.googleDocsService.updateDocData({
        orgId,
        accountId,
        userId,
        name,
      });
      res.status(HttpStatus.OK).json(update);
    } catch (error) {
      res.status(HttpStatus.BAD_REQUEST).json({ message: error.message });
    }
  }

  @Get('get/:orgId/:accountId')
  async getDoc(
    @Param('orgId') orgId: string,
    @Param('accountId') accountId: string,
    @Req() req: RequestWithUser,
    @Res() res: Response,
  ) {
    let userId = req.userId;
    try {
      const doc = await this.googleDocsService.getStaticData({
        orgId,
        accountId,
        userId,
      });

      const transformedData: any = doc.data.map((item: any) => ({
        id: item._id,
        metadata: {
          content: `${item.question}\n${item.answer}`,
        },
      }));

  
      const newResponse = {
        ...doc,
        data: transformedData,
      };

      res.status(HttpStatus.OK).json(newResponse);
    } catch (error) {
      res.status(HttpStatus.BAD_REQUEST).json({ message: error.message });
    }
  }


  @Delete('disconnect/:orgId')
  async disconnectDoc(
    @Param('orgId') orgId: string,
    @Body('docLink') docLink: string,
    @Res() res: Response,
  ): Promise<void> {
    try {
      const docId = this.extractDocId(docLink);
      const doc = await this.googleDocsService.disconnectDoc({
        orgId,
        docId,
      });
      res.status(HttpStatus.OK).json(doc);
    } catch (error) {
      res.status(HttpStatus.BAD_REQUEST).json({ message: error.message });
    }
  }

  // Utility method to extract the docId from a Google Docs link.
  private extractDocId(docLink: string): string {
    const match = docLink.match(/\/d\/(.+?)\//);
    if (!match) throw new Error('Invalid Google Docs link.');
    return match[1];
  }
}
