import {
  Body,
  Controller,
  Delete,
  Param,
  Patch,
  Post,
  Req,
} from '@nestjs/common';
import { QnAService } from '../../../services/data/QnA/QnA.service';
import { CreateQnADto } from '../../../dto/qna.dto';
import { RequestWithUser } from '../../../../auth/auth.interface';

@Controller('data/qna')
export class QnAController {
  constructor(private readonly qnaService: QnAService) {}

  @Post('create/:orgId')
  async create(
    @Body() createQnADto: CreateQnADto,
    @Param('orgId') orgId: string,
    @Req() req: RequestWithUser,
  ) {
    return this.qnaService.create(createQnADto, orgId, req.userId);
  }

  @Post('add/:orgId/:accountId')
  async addQnAPair(
    @Param('orgId') orgId: string,
    @Param('accountId') accountId: string,
    @Body('question') question: string,
    @Body('answer') answer: string,
  ) {
    return this.qnaService.addQnAPair(orgId, accountId, question, answer);
  }

  @Patch('update/:orgId/:accountId/:qnaId')
  async updateQnAPair(
    @Param('orgId') orgId: string,
    @Param('accountId') accountId: string,
    @Param('qnaId') qnaId: string,
    @Body('question') question?: string, 
    @Body('answer') answer?: string,
  ) {
    return this.qnaService.updateQnAPair(
      orgId,
      accountId,
      qnaId,
      question,
      answer,
    );
  }

  @Delete('delete/:orgId/:accountId/:qnaId')
  async deleteQnAPair(
    @Param('orgId') orgId: string,
    @Param('accountId') accountId: string,
    @Param('qnaId') qnaId: string,
  ) {
    return this.qnaService.deleteQnAPair(orgId, accountId, qnaId);
  }
}
