/**
 * Manages static data resources. Holds function like get all static data, delete a static data, get a static data, edit a data source.
 */
import {
  Controller,
  Get,
  Post,
  Req,
  Res,
  Body,
  HttpStatus,
  Render,
  Delete,
  Param,
  Query,
  UseGuards,
  Patch,
  BadRequestException,
} from '@nestjs/common';
import { Request, Response } from 'express';
import {
  ApiOperation,
  ApiQuery,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiParam,
} from '@nestjs/swagger';
import { DataService } from 'src/organization/services/data/data.service';
import { DeleteStaticDataDto } from 'src/organization/dto/requests/delete.dto';
import { AuthGuard } from 'src/auth/auth.guard';
import { allowAccessGuard } from 'src/guards/can-access/can-access.guard';
import { RequestWithUser } from 'src/auth/auth.interface';
import { handleException } from 'helpers/handleException';
import { EditDataSourceDto } from 'src/organization/dto/requests/post.dto';
import { ObjectId } from 'mongodb';

// NOTES: For static data, key ID will be the length of the content array

@Controller('data')
export class DataController {
  constructor(private readonly dataService: DataService) {}

  @Get(':orgId')
  @UseGuards(AuthGuard, allowAccessGuard('settings', 'read'))
  @ApiOperation({ summary: "Get all static data's for an organization" })
  @ApiParam({
    name: 'orgId',
    required: true,
    description: 'Organization Id where to query',
  })
  async listStaticData(@Param('orgId') orgId: string, @Res() res: Response) {
    try {
      const result = await this.dataService.listStaticData(orgId);
      res.status(HttpStatus.OK).json(result);
    } catch (error) {
      res
        .status(HttpStatus.INTERNAL_SERVER_ERROR)
        .json({ message: error.message });
    }
  }

  @Delete('delete/:orgId/:accountId')
  @UseGuards(AuthGuard, allowAccessGuard('settings'))
  @ApiOkResponse({
    description: 'Delete a static data',
    type: DeleteStaticDataDto,
  })
  async deleteStaticData(
    @Req() req: RequestWithUser,
    @Param() params: DeleteStaticDataDto,
    @Query('ids') ids?: string,
    @Res() res?: Response,
  ) {
    try {
      await this.dataService.deleteStaticData(
        params,
        ids,
        req.userId,
        req['isAdmin'],
      );
      res.status(HttpStatus.OK).json({ message: 'Deleted successfully' });
    } catch (error) {
      handleException(res, error);
    }
  }

  @Get('/:orgId/:accountId')
  @UseGuards(AuthGuard, allowAccessGuard('settings', 'read'))
  @ApiOperation({ summary: 'Get a static data for a data source' })
  @ApiParam({
    name: 'orgId',
    required: true,
    description: 'Organization Id where to query',
  })
  @ApiParam({
    name: 'accountId',
    required: true,
    description: 'Account Id',
  })
  async getStaticData(
    @Param('orgId') orgId: string,
    @Param('accountId') accountId: string,
    @Res() res: Response,
  ) {
    try {
      const result = await this.dataService.getStaticData(orgId, accountId);
      res.status(HttpStatus.OK).json(result);
    } catch (error) {
      res
        .status(HttpStatus.INTERNAL_SERVER_ERROR)
        .json({ message: error.message });
    }
  }

  @Patch('/:orgId/:accountId')
  @UseGuards(AuthGuard, allowAccessGuard('settings'))
  @ApiOperation({ summary: 'Edit a data source' })
  async editIntegration(
    @Req() req: RequestWithUser,
    @Body() body: EditDataSourceDto,
    @Res() res: Response,
  ) {
    try {
      const orgId = req.cookies['orgId'] || req.params.orgId;
      if (!ObjectId.isValid(orgId))
        throw new BadRequestException('Invalid orgId');
      const { accountId } = req.params;
      const response = await this.dataService.editStaticDataSource(
        orgId,
        body,
        accountId,
      );
      res.status(HttpStatus.OK).send(response);
    } catch (error) {
      handleException(res, error);
    }
  }
}
