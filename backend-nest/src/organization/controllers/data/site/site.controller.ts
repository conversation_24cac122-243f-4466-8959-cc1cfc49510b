import {
  Controller,
  Get,
  Post,
  Req,
  Res,
  Body,
  HttpStatus,
  Render,
  Delete,
  Param,
  Query,
  UseGuards,
} from '@nestjs/common';
import { Request, Response } from 'express';
import {
  ApiOperation,
  ApiQuery,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiParam,
} from '@nestjs/swagger';
import { SiteService } from 'src/organization/services/data/site/site.service';
import {
  ScrapeSiteDto,
  updateEmbeddingsDto,
} from 'src/organization/dto/requests/post.dto';
import { getDomainFromUrl } from 'src/lib/utils';
import { DeleteStaticDataDto } from 'src/organization/dto/requests/delete.dto';
import { ApiclientService } from 'src/api-client/services/apiclient.service';
import { methods } from 'src/api-client/utils/channel.constants';
import { AuthGuard } from 'src/auth/auth.guard';
import { RequestWithUser } from 'src/auth/auth.interface';

@Controller('site')
export class SiteController {
  constructor(
    private readonly siteService: SiteService,
    private readonly apiclientService: ApiclientService,
  ) {}

  @ApiOperation({ summary: 'Get links' })
  @Get('/links')
  async getLinks(
    @Query('url') url: string,
    @Query('depth') depth: number = 2,
    @Res() res: Response,
  ) {
    try {
      const reqUrl = `${process.env.SCRAPER_FUNC_URL}/links?url=${url}&depth=${depth}`;
      const result = await this.apiclientService.apiRequest<any>(
        methods.GET,
        reqUrl,
        null,
        null,
      );
      res.status(HttpStatus.OK).json({ ...result });
    } catch (err) {
      res
        .status(HttpStatus.INTERNAL_SERVER_ERROR)
        .json({ message: err.message });
    }
  }

  @Get('/scrape/:orgId')
  @ApiOperation({ summary: 'Scrape the sites provided in body' })
  @UseGuards(AuthGuard)
  async scrapeSites(
    @Req() req: RequestWithUser,
    @Param() params: ScrapeSiteDto,
    @Query('urls') urls: string,
    @Res() res: Response,
  ) {
    let reqStatus;
    try {
      if (urls.length === 0) {
        reqStatus = HttpStatus.BAD_REQUEST;
        throw new Error('No urls provided');
      }

      const domain =
        getDomainFromUrl((urls || '').split(' ')[0]) ||
        (urls || '').split(' ')[0] ||
        'website';
      return await this.siteService.scrapeSites(
        params.orgId,
        urls,
        req.username,
        domain,
        res,
        req.userId,
      );
      // res.status(HttpStatus.OK).json({ message: 'Scraping started' });
    } catch (error) {
      reqStatus = reqStatus || HttpStatus.INTERNAL_SERVER_ERROR;
      res.status(reqStatus).json({ message: error.message });
    }
  }

  @ApiOperation({ summary: 'Get site embeddings content' })
  @ApiQuery({
    name: 'ids',
    required: false,
    description: 'ids of the embeddings to retrieve',
  })
  @ApiParam({
    name: 'orgId',
    required: true,
    description: 'id of the organization',
  })
  @ApiParam({
    name: 'accountId',
    required: false,
    description: 'id of the account',
  })
  @Get('/:orgId')
  async getSiteEmbeddings(
    @Param('orgId') orgId: string,
    @Query('ids') ids?: string,
    @Query('accountId') accId?: string,
    @Res() res?: Response,
  ) {
    try {
      const idsArr = ids ? ids.split(' ') : [];
      const { chunks, accountId, name, type } =
        await this.siteService.retrieveEmbeddedScrapedData(
          orgId,
          idsArr,
          accId,
        );
      const sortedData = chunks.sort(
        (a, b) => a.metadata['index'] - b.metadata['index'],
      );
      res
        .status(HttpStatus.OK)
        .json({ accountId, name, type, data: sortedData });
    } catch (error) {
      res
        .status(HttpStatus.INTERNAL_SERVER_ERROR)
        .json({ message: error.message });
    }
  }

  @ApiOperation({ summary: 'Update embeddings' })
  @Post('/:orgId')
  async updateEmbeddings(
    @Param('orgId') orgId: string,
    @Body() body: updateEmbeddingsDto,
    @Res() res: Response,
  ) {
    const { accountId, name: domain, chunks } = body;
    return await this.siteService.updateScrapedData(
      orgId,
      accountId,
      domain,
      chunks,
      res,
    );
  }
}
