import {
  Controller,
  Post,
  UseInterceptors,
  UploadedFile,
  HttpStatus,
  ParseFilePipeBuilder,
  Param,
  Res,
  Req,
  Body,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { Express } from 'express';
import { PdfService } from '../../../services/data/pdf/pdf.service';
import { v4 as uuidv4 } from 'uuid';
import { RequestWithUser } from '../../../../auth/auth.interface';
import { MyLogger } from 'src/logger/logger.service';

@Controller('data/pdf')
export class PdfController {
  constructor(
    private readonly pdfService: PdfService,
    private readonly myLogger: MyLogger,
  ) {}

  @Post('upload/:orgId/:userName')
  @UseInterceptors(FileInterceptor('file'))
  async uploadFile(
    @Param('orgId') orgId: string,
    @UploadedFile(
      new ParseFilePipeBuilder()
        .addFileTypeValidator({ fileType: 'application/pdf' })
        .addMaxSizeValidator({ maxSize: 10 * 1024 * 1024 }) // 10MB
        .build({ errorHttpStatusCode: HttpStatus.UNPROCESSABLE_ENTITY }),
    )
    file: Express.Multer.File,
    @Param('userName') userName: string,
    @Res() res,
    @Req() req: RequestWithUser,
  ) {
    let userId = req.userId;
    let pdfLink = await this.pdfService.uploadToGoogleStorage(file, orgId);
    let text = await this.pdfService.extractTextFromPdf(file);
    // let qa = await this.pdfService.generateQnAPairsWithMistralAI(text);
    let qa = await this.pdfService.generateQnAPairsWithOpenAI(text);
    let qaPairs = await this.pdfService.parseQnAPairs(qa);

    let qaPairIds = qaPairs.map(() => uuidv4());

    let connections = await this.pdfService.savePdfData(
      orgId,
      userId,
      qaPairs,
      pdfLink,
      userName,
      file.filename,
      qaPairIds,
    );

    this.pdfService
      .savePdfInPineCone({
        pdfLink,
        orgId,
        qaPairs,
        qaPairIds,
      })
      .catch((error) => {
        this.myLogger.error({
          message: 'Error in savePdfInPineCone:' + error,
        });
      });

    res.status(HttpStatus.OK).json({
      connections,
    });
  }
}
