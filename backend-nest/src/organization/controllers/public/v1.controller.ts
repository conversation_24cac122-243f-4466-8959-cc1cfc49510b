import {
  <PERSON>,
  Get,
  Post,
  Req,
  <PERSON>s,
  Body,
  HttpStatus,
  Render,
  Delete,
  Param,
  Query,
  UseGuards,
  BadRequestException,
  UnauthorizedException,
} from '@nestjs/common';
import { Request, Response } from 'express';
import {
  ApiOperation,
  ApiQuery,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiParam,
} from '@nestjs/swagger';
import { decodeCode } from 'src/lib/utils';
import { v4 as uuidv4 } from 'uuid';
import { CONNECTION_TYPES, KINDS, PROVIDERS } from 'src/lib/constant';
import { Publicv1Service } from 'src/organization/services/public/v1.service';
import {
  V1EmailConversationDto,
  V1InboundConversationDto,
} from 'src/organization/dto/customInbound.dto';
import { handleException } from 'helpers/handleException';
import { RequestWithUser } from 'src/auth/auth.interface';
import { ObjectId } from 'mongodb';
import { ThrottlerGuard } from '@nestjs/throttler';
import { MyLogger } from 'src/logger/logger.service';
import { JSDOM } from 'jsdom';
import { GhlService } from 'src/organization/services/channels/ghl/ghl.service';

@Controller('v1')
export class Publicv1Controller {
  constructor(
    private readonly publicv1service: Publicv1Service,
    private readonly myLogger: MyLogger,
    private readonly ghlService: GhlService,
  ) {}

  @ApiOperation({ summary: 'Public API for capri conversation' })
  @Post('conversation/query')
  @UseGuards(ThrottlerGuard)
  async processConversation(
    @Req() req: RequestWithUser,
    @Body() body: V1InboundConversationDto,
    @Res() res: Response,
  ) {
    try {
      this.myLogger.log({
        message:
          `PUBLIC API WEBHOOK | public api conversation received | agent ${body.agent} | payload: ` +
          JSON.stringify(body),
      });
      let authorization = (req.headers['authorization'] || '') as any;
      authorization = authorization.split(' ');
      const capriToken =
        authorization.length > 1 ? authorization[1] : undefined;
      if (!capriToken) throw new UnauthorizedException('Capri token not found');
      // if body?.agent not a mongo ObjectId
      if (!ObjectId.isValid(body?.agent || ''))
        throw new BadRequestException('Requested agent does not exist');
      const result = await this.publicv1service.processConversation(
        req.uniqueCode,
        capriToken,
        body,
      );
      res.status(HttpStatus.OK).json(result);
    } catch (err) {
      this.myLogger.error({
        message: `@error | reqId ${req.uniqueCode} | message: ${err.message}`,
      });
      handleException(res, err);
    }
  }

  @Get('/connect')
  async connect(@Req() req: Request, @Res() res: Response) {
    if (
      req.headers.authorization &&
      req.headers.authorization.split(' ')?.[1]
    ) {
      const data = await this.publicv1service.getToken(
        req.headers.authorization.split(' ')?.[1],
        res,
      );
    }
  }

  @Post('email/query')
  @UseGuards(ThrottlerGuard)
  async processEmailConversation(
    @Req() req: RequestWithUser,
    @Body() body: V1EmailConversationDto & { message: string},
    @Res() res: Response,
  ) {
    try {
      this.myLogger.log({
        message:
          `PUBLIC API WEBHOOK | public api email conversation received | agent ${body?.agent} | payload: ` +
          JSON.stringify(body),
      });
      let authorization = (req.headers['authorization'] || '') as any;
      authorization = authorization.split(' ');
      const capriToken =
        authorization.length > 1 ? authorization[1] : undefined;
      if (!capriToken) throw new UnauthorizedException('Capri token not found');
      // if body?.agent not a mongo ObjectId
      if (!ObjectId.isValid(body?.agent || ''))
        throw new BadRequestException('Requested agent does not exist');

      const subject = body.subject;
      let emailBody = new JSDOM(body.body);
      body.message = emailBody.window.document.body.textContent;
      const result = await this.publicv1service.processConversation(
        req.uniqueCode,
        capriToken,
        body,
      );
      res.status(HttpStatus.OK).json({ ...result, subject });
    } catch (err) {
      this.myLogger.error({
        message: `@error | reqId ${req.uniqueCode} | message: ${err.message}`,
      });
      handleException(res, err);
    }
  }

  @Post('/extension/leadconnector/conversations')
  async getConversations(
    @Req() req,
    @Res() res: Response,
    @Body() body: any,
  ){
    this.myLogger.log({
      message:
        `PUBLIC API WEBHOOK | public api get conversations | agent ${body?.agent} | payload: ` +
        JSON.stringify(body),
    });
    try {
      const contactId = body?.contact_id?.trim();
      const locationId = body?.location?.id?.trim()
      const requestUrl = body?.customData?.url?.trim();
      const contactData = body;
      if (!body?.customData?.capri_token) throw new UnauthorizedException('Capri token not found');
      res.sendStatus(HttpStatus.OK);
      const locationData = body?.location;
      await this.ghlService.getConversations({
        contactData,
        locationData,
        contactId,
        locationId,
        url: requestUrl,
        capriToken: body?.customData?.capri_token?.trim()
      });
    } catch (err) {
      this.myLogger.error({
        message: `GET CONVERSATIONS ERROR | contactId: ${body?.contact_id} | locationId: ${body?.location?.id} | reqId ${req.uniqueCode} | message: ${err.message}`,
        context: 'V1Controller.getConversations',
      });
      handleException(res, err);
    }
  }

  @Get('/extension/leadconnector/conversations')
  async getConversationsByUrl(
    @Req() req,
    @Res() res: Response,
    @Body() body: any,
  ){
    this.myLogger.log({
      message:
        `PUBLIC API WEBHOOK | public api get conversations | agent ${body?.agent} | payload: ` +
        JSON.stringify(body),
    });
    try {
      const contactId = body?.contact_id?.trim();
      const locationId = body?.location?.id?.trim()
      const contactData = body;
      if (!body?.customData?.capri_token) throw new UnauthorizedException('Capri token not found');
      const locationData = body?.location;
      const result = await this.ghlService.getConversations({
        contactData,
        locationData,
        contactId,
        locationId,
        capriToken: body?.customData?.capri_token?.trim()
      });

      res.status(HttpStatus.OK).json(result);
    } catch (err) {
      this.myLogger.error({
        message: `GET CONVERSATIONS ERROR | contactId: ${body?.contact_id} | locationId: ${body?.location?.id} | reqId ${req.uniqueCode} | message: ${err.message}`,
        context: 'V1Controller.getConversations',
      });
      handleException(res, err);
    }
  }
}
