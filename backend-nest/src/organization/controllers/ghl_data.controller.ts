import {
  Controller,
  Get,
  Post,
  Req,
  Res,
  Body,
  HttpStatus,
  Render,
  Delete,
  Param,
  Query,
  UseGuards,
} from '@nestjs/common';
import { Request, Response } from 'express';
import {
  ApiOperation,
  ApiQuery,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiParam,
} from '@nestjs/swagger';
import { RequestWithUser } from 'src/auth/auth.interface';
import { handleException } from 'helpers/handleException';
import { GhlService } from 'src/organization/services/channels/ghl/ghl.service';
import { ApiTokenGuard } from 'src/guards/api-token.guard';
import { MyLogger } from 'src/logger/logger.service';

@Controller('ghl-data')
export class GhlDataController {
  constructor(
    private readonly ghlService: GhlService,
    private readonly myLogger: MyLogger,
  ) {}

  @Get('/list/calendars')
  @UseGuards(ApiTokenGuard)
  async getCalendarsForLocation(
    @Req() req: RequestWithUser,
    @Res() res: Response,
    @Query('locationId') locationId: string,
  ) {
    try {
      const calendars = await this.ghlService.getCalendarsList(
        req.uniqueCode,
        locationId,
      );
      res.status(HttpStatus.OK).json(calendars);
    } catch (err) {
      this.myLogger.error({
        message: `reqId ${req.uniqueCode} | Error in retrieving calendars for location ${locationId}: ${err.message}`,
      });
      handleException(res, err);
    }
  }

  @Get('/list/tags')
  @UseGuards(ApiTokenGuard)
  async getTagsForLocation(
    @Req() req: RequestWithUser,
    @Res() res: Response,
    @Query('locationId') locationId: string,
  ) {
    try {
      const tags = await this.ghlService.getAllLocationTags(
        req.uniqueCode,
        locationId,
      );
      res.status(HttpStatus.OK).json(tags);
    } catch (err) {
      this.myLogger.error({
        message: `reqId ${req.uniqueCode} | Error in retrieving tags for location ${locationId}: ${err.message}`,
      });
      handleException(res, err);
    }
  }
}
