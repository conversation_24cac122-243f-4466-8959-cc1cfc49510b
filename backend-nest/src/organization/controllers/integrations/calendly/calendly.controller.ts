import {
  Body,
  Controller,
  Get,
  HttpException,
  HttpStatus,
  Post,
  Query,
  Redirect,
  Res,
} from '@nestjs/common';
import { Response } from 'express';
import { CalendlyService } from '../../../services/integrations/calendly/calendly.service';

@Controller('calendly')
export class CalendlyController {
  constructor(private readonly calendlyService: CalendlyService) {}

  @Get('login')
  getCalendlyAuth(@Query('orgId') orgId: string) {
    const queryParams = {
      client_id: process.env.CALENDLY_CLIENT_ID,
      response_type: 'code',
      redirect_uri: process.env.CALENDLY_REDIRECT_URI,
      state: orgId,
    };

    return {
      url: `https://auth.calendly.com/oauth/authorize?${new URLSearchParams(
        queryParams,
      )}`,
    };
  }

  @Get('callback')
  async calendlyCallback(
    @Query('code') code: string,
    @Query('state') state: string,
    @Res() res: Response,
  ) {
    try {
      let orgId = state;
      const tokens = await this.calendlyService.getAccessToken(code);

      const user = await this.calendlyService.getUserInfo({
        access_token: tokens.access_token,
      });

      let connection;
      let calendarCreds = await this.calendlyService.getCalendarCreds(orgId);

      if (calendarCreds && calendarCreds.credential !== null) {
        connection = await this.calendlyService.updateCalendarCreds({
          orgId,
          user,
          newTokens: tokens,
        });
      } else {
        connection = await this.calendlyService.saveCalendarCreds(
          orgId,
          tokens,
          user,
        );
      }
      const userDetails = {
        message: 'Calendly connected successfully!',
        userName: user.resource.name,
        access_token: tokens.access_token,
        refresh_token: tokens.refresh_token,
        user,
        orgId,
        ...connection,
        success: true,
      };
      res.send(
        `<script>window.opener.postMessage(${JSON.stringify(userDetails)}, ${
          process.env.FRONTEND_URL
        });</script>`,
      );
    } catch (error) {
      res.status(500).send('Calendly integration failed');
    }
  }

  @Post('availability')
  async getUserAvailability(
    @Body() body: { orgId: string; start_time: string; end_time: string },
    @Res() res: Response,
  ) {
    try {
      const userBusy = await this.calendlyService.getAvailability({
        orgId: body.orgId,
        start_time: body.start_time,
        end_time: body.end_time,
      });

      const availableSlots = this.generateAvailableSlots(
        body.start_time,
        body.end_time,
        userBusy.collection,
      );

      res.send(availableSlots);
    } catch (error) {
      res.status(500).send('Error fetching user availability from Calendly');
    }
  }

  @Post('create')
  async createSingleUseLink(
    @Body()
    body: {
      orgId: string;
      eventTypeUri: string;
      date: string;
      time: string;
    },
  ): Promise<any> {
    try {
      const { orgId, eventTypeUri, date, time } = body;
      if (!orgId || !eventTypeUri || !date || !time) {
        throw new HttpException(
          'Missing required parameters',
          HttpStatus.BAD_REQUEST,
        );
      }

      const linkData = await this.calendlyService.createSingleUseSchedulingLink(
        orgId,
        eventTypeUri,
        date,
        time,
      );
      return linkData;
    } catch (error) {
      throw new HttpException(
        `Error in creating link: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('connect')
  async connectCalendly(
    @Query('orgId') orgId: string,
    @Body() body: { eventTypeUri: string },
  ) {
    let { eventTypeUri } = body;
    try {
      return await this.calendlyService.CalendlyConnect({
        orgId,
        eventTypeUri,
      });
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Get('events')
  async listEventTypesByUser(@Query('orgId') orgId: string) {
    try {
      return await this.calendlyService.listEventTypesByUser(orgId);
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  private generateAvailableSlots(
    startTime: string,
    endTime: string,
    busySlots: { start_time: string; end_time: string }[],
  ): { start_time: string; end_time: string }[] {
    const availableSlots = [];
    const currentTime = new Date(startTime);
    const endTimeDate = new Date(endTime);

    while (currentTime < endTimeDate) {
      const slotEndTime = new Date(currentTime.getTime() + 30 * 60000); // 30 minutes in milliseconds

      if (!this.isSlotBusy(currentTime, slotEndTime, busySlots)) {
        availableSlots.push({
          start_time: currentTime.toISOString(),
          end_time: slotEndTime.toISOString(),
        });
      }

      currentTime.setTime(currentTime.getTime() + 30 * 60000); // Move to the next 30-minute slot
    }

    return availableSlots;
  }

  private isSlotBusy(
    startTime: Date,
    endTime: Date,
    busySlots: { start_time: string; end_time: string }[],
  ): boolean {
    for (const busySlot of busySlots) {
      const busyStartTime = new Date(busySlot.start_time);
      const busyEndTime = new Date(busySlot.end_time);

      // Check if the current slot overlaps with any busy slot
      if (
        (startTime >= busyStartTime && startTime < busyEndTime) ||
        (endTime > busyStartTime && endTime <= busyEndTime)
      ) {
        return true;
      }
    }

    return false;
  }
}
