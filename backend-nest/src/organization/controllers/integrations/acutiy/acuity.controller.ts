import {
  Body,
  Controller,
  Get,
  HttpStatus,
  Post,
  Query,
  Res,
} from '@nestjs/common';
import { Response } from 'express';
import { AcuityService } from '../../../services/integrations/acuity/acuity.service';
import * as Acuity from 'acuityscheduling';
import { MyLogger } from 'src/logger/logger.service';

@Controller('acuity')
export class AcuityController {
  constructor(
    private readonly acuityService: AcuityService,
    private readonly myLogger: MyLogger,
  ) {}

  @Get('login')
  getAcuityAuth(@Query('state') orgId: string) {
    const clientId = process.env.ACUITY_CLIENT_ID;
    const redirectUri = process.env.ACUITY_REDIRECT_URI;
    const scope = 'api-v1';

    const queryParams = new URLSearchParams({
      client_id: clientId,
      response_type: 'code',
      redirect_uri: redirectUri,
      scope: scope,
      state: orgId,
    });

    const authUrl = `https://secure.acuityscheduling.com/oauth2/authorize?${queryParams.toString()}`;
    return {
      url: authUrl,
    };
  }

  @Get('callback')
  async acuityCallback(
    @Query('code') code: string,
    @Query('state') state: string,
    @Res() res: Response,
  ) {
    try {
      let orgId = state;

      let tokenResponse = await this.acuityService.getAccessToken({
        code,
      });

      const user = await this.acuityService.getUserInfo({
        access_token: tokenResponse.access_token,
      });

      this.myLogger.log({
        message: { user },
      });

      let connection;
      let acuityCreds = await this.acuityService.getSchedulingCreds(orgId);

      this.myLogger.log({
        message: { acuityCreds },
      });

      if (acuityCreds !== null) {
        connection = await this.acuityService.updateSchedulingCreds(
          orgId,
          tokenResponse,
          user,
        );
      } else {
        connection = await this.acuityService.saveSchedulingCreds(
          orgId,
          tokenResponse,
          user,
        );
      }

      this.myLogger.log({
        message: { connection },
      });

      return res.status(HttpStatus.OK).json({
        message: 'Acuity connected successfully!',
        userName: user.displayName,
        access_token: tokenResponse.access_token,
        user,
        orgId,
        ...connection,
        success: true,
      });
    } catch (error) {
      res.status(500).send('Acuity Scheduling integration failed');
    }
  }

  @Post('availability')
  async getUserAvailability(
    @Body() body: { orgId: string; start_time: string; end_time: string },
    @Res() res: Response,
  ) {
    try {
    } catch (error) {
      res
        .status(500)
        .send('Error fetching user availability from Acuity Scheduling');
    }
  }
}
