import { <PERSON>, <PERSON>, Req, <PERSON><PERSON>, Body, Param, Get, Put } from '@nestjs/common';
import { Response } from 'express';
import { GoogleCalendarService } from 'src/organization/services/integrations/google-calendar/google-calendar.service';
import { ApiOperation } from '@nestjs/swagger';
import { MyLogger } from 'src/logger/logger.service';
import { MongoOrganizationService } from '../../../../mongo/service/organization/mongo-organization.service';
import { RequestWithUser } from '../../../../auth/auth.interface';
import { calendarAvailabilityService } from '../../../services/calendar-availablity/calendar-availablity.service';
import { CreateHttpGetRequestDto } from 'src/http-request/GET/dto/create-get.dto';
import { HttpGetService } from 'src/organization/services/integrations/http-get/http-get.service';
import { HttpRequestGetService } from 'src/http-request/GET/get.service';
import { UpdateHttpGetRequestDto } from 'src/http-request/GET/dto/update-get.dto';
import { IntegrationService } from 'src/organization/services/integrations/integration.service';


@Controller('http-get')
export class HttpGetController {
  constructor(
    private readonly mongoOrganizationService: MongoOrganizationService,
    private readonly googleCalendarService: GoogleCalendarService,
    private readonly calendarAvailabilityService: calendarAvailabilityService,
    private readonly httpGetService: HttpGetService,
    private readonly myLogger: MyLogger,
    private readonly httpRequestGetService: HttpRequestGetService,
        private readonly integrationService: IntegrationService,
  ) {}

  @ApiOperation({ summary: 'Connect Http GET Request' })
  @Post('connect')
  async connect(
    @Body() createHttpGetRequestDto: CreateHttpGetRequestDto,
    @Req() req: RequestWithUser,
    @Res() res: Response,
  ) {
    try {
      let userId = req.userId;
      let usid = req.cookies['usid'];

      let saveDataSource = await this.httpGetService.saveHttpGetAsDataSource(
        createHttpGetRequestDto.orgId,
        userId,
        usid,
        createHttpGetRequestDto.name,
        createHttpGetRequestDto,
      );

      return res.send({
        message: 'HTTP GET request added successful',
        data: saveDataSource,
      });
    } catch (error) {
      this.myLogger.error({ message: error });
      if (error.response) {
        this.myLogger.error({ message: error.response.data });
      }
      return res.status(500).json({ error: 'An error occurred' });
    }
  }

  @ApiOperation({ summary: 'Fetch Http GET Request data' })
  @Get('fetch/:orgId/:accountId')
  async get(
    @Param('orgId') orgId: string,
    @Param('accountId') accountId: string,
    @Req() req: RequestWithUser,
    @Res() res: Response,
  ) {
    try {
      let getData = await this.httpRequestGetService.findOne(
        null,
        orgId,
        accountId,
      );

      return res.send({
        message: 'HTTP GET request fetch successful',
        data: getData,
      });
    } catch (error) {
      this.myLogger.error({ message: error });
      if (error.response) {
        this.myLogger.error({ message: error.response.data });
      }
      return res.status(500).json({ error: 'An error occurred' });
    }
  }

  @ApiOperation({ summary: 'Update Http GET Request' })
  @Put('update/:orgId/:accountId')
  async update(
    @Param('orgId') orgId: string,
    @Param('accountId') accountId: string,
    @Body() updateHttpGetRequestDto: UpdateHttpGetRequestDto,
    @Req() req: RequestWithUser,
    @Res() res: Response,
  ) {
    try {
      let updatedDataSource = await this.httpRequestGetService.update(
        null,
        orgId,
        accountId,
        updateHttpGetRequestDto,
      );

      if(updateHttpGetRequestDto.name){
        await this.integrationService.updateDataSource(
          orgId,
          accountId,
          null,
          updateHttpGetRequestDto.name,
        );
      }

      return res.send({
        message: 'HTTP GET request updated successfully',
        data: updatedDataSource,
      });
    } catch (error) {
      this.myLogger.error({ message: error });
      if (error.response) {
        this.myLogger.error({ message: error.response.data });
      }
      return res
        .status(500)
        .json({
          error: 'An error occurred while updating the HTTP GET request',
        });
    }
  }
}
