import { Controller, Post, Get, Put, Delete, Body, Res, HttpStatus, Param, Query, Render } from '@nestjs/common';
import { Response } from 'express';
import { ApiOkResponse, ApiOperation, ApiParam } from '@nestjs/swagger';
import { GhlCalendarService } from 'src/organization/services/integrations/ghl-calendar/ghl-calendar.service';
import { CalendarPreferenceDto } from 'src/organization/dto/requests/post.dto';

@Controller('leadconnector')
export class GhlCalendarController {
    constructor(
        private readonly ghlCalendarService: GhlCalendarService,
    ) { }

    @Get('/calendar')
    @ApiOperation({ summary: 'Connect Lead Connector Calendar' })
    @ApiOkResponse({ description: 'A HTML rendered page' })
    async connectGhlCalendar(@Query('code') code: string, @Query('state') state: string, @Res() res: Response) {
        return await this.ghlCalendarService.connectGhlCalendar(code, state, res);
    }

    @Post('/preferences')
    @ApiOperation({ summary: 'Set Lead Connector Calendar preferences' })
    async setGhlCalendarPreferences(
        @Body() body: CalendarPreferenceDto,
        @Res() response: Response,
    ) {
        return await this.ghlCalendarService.setGhlCalendarPreference(body, response);
    }
}
