import { <PERSON>, <PERSON>, Req, <PERSON><PERSON>, Body, Param, Get, Put } from '@nestjs/common';
import { Response } from 'express';
import { ApiOperation } from '@nestjs/swagger';
import { MyLogger } from 'src/logger/logger.service';
import { MongoOrganizationService } from '../../../../mongo/service/organization/mongo-organization.service';
import { RequestWithUser } from '../../../../auth/auth.interface';
import { CreateHttpPostRequestDto } from 'src/http-request/POST/dto/create-post.dto';
import { HttpPostService } from 'src/organization/services/integrations/http-post/http-post.service';
import { HttpRequestPostService } from 'src/http-request/POST/post.service';
import { UpdateHttpPostRequestDto } from 'src/http-request/POST/dto/update-post.dto';
import { IntegrationService } from 'src/organization/services/integrations/integration.service';

@Controller('http-post')
export class HttpPostController {
  constructor(
    private readonly mongoOrganizationService: MongoOrganizationService,
    private readonly httpPostService: HttpPostService,
    private readonly myLogger: MyLogger,
    private readonly httpRequestPostService: HttpRequestPostService,
    private readonly integrationService: IntegrationService,
  ) {}

  @ApiOperation({ summary: 'Connect Http POST Request' })
  @Post('connect')
  async connect(
    @Body() createHttpPostRequestDto: CreateHttpPostRequestDto,
    @Req() req: RequestWithUser,
    @Res() res: Response,
  ) {
    try {
      let userId = req.userId;
      let usid = req.cookies['usid'];

      let saveDataSource = await this.httpPostService.saveHttpPostAsDataSource(
        createHttpPostRequestDto.orgId,
        userId,
        usid,
        createHttpPostRequestDto.name,
        createHttpPostRequestDto,
      );

      return res.send({
        message: 'HTTP POST request added successful',
        data: saveDataSource,
      });
    } catch (error) {
      this.myLogger.error({ message: error });
      if (error.response) {
        this.myLogger.error({ message: error.response.data });
      }
      return res.status(500).json({ error: 'An error occurred' });
    }
  }

  @ApiOperation({ summary: 'Fetch Http POST Request data' })
  @Get('fetch/:orgId/:accountId')
  async get(
    @Param('orgId') orgId: string,
    @Param('accountId') accountId: string,
    @Req() req: RequestWithUser,
    @Res() res: Response,
  ) {
    try {
      let getData = await this.httpRequestPostService.findOne(
        null,
        orgId,
        accountId,
      );

      return res.send({
        message: 'HTTP POST request fetch successful',
        data: getData,
      });
    } catch (error) {
      this.myLogger.error({ message: error });
      if (error.response) {
        this.myLogger.error({ message: error.response.data });
      }
      return res.status(500).json({ error: 'An error occurred' });
    }
  }

  @ApiOperation({ summary: 'Update Http POST Request' })
  @Put('update/:orgId/:accountId')
  async update(
    @Param('orgId') orgId: string,
    @Param('accountId') accountId: string,
    @Body() updateHttpPostRequestDto: UpdateHttpPostRequestDto,
    @Req() req: RequestWithUser,
    @Res() res: Response,
  ) {
    try {
      let updatedDataSource = await this.httpRequestPostService.update(
        null,
        orgId,
        accountId,
        updateHttpPostRequestDto,
      );

      if(updateHttpPostRequestDto.name){
        await this.integrationService.updateDataSource(
          orgId,
          accountId,
          null,
          updateHttpPostRequestDto.name,
        );
      }

      return res.send({
        message: 'HTTP POST request updated successfully',
        data: updatedDataSource,
      });
    } catch (error) {
      this.myLogger.error({ message: error });
      if (error.response) {
        this.myLogger.error({ message: error.response.data });
      }
      return res
        .status(500)
        .json({
          error: 'An error occurred while updating the HTTP POST request',
        });
    }
  }
} 