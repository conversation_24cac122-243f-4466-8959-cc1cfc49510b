import {
  Controller,
  Get,
  Post,
  Req,
  Res,
  Body,
  HttpStatus,
  Render,
  Delete,
  Param,
  Query,
  NotFoundException,
} from '@nestjs/common';
import { Request, Response } from 'express';
import {
  ApiOperation,
  ApiQuery,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiParam,
} from '@nestjs/swagger';
import { SheetService } from 'src/organization/services/integrations/sheet/sheet.service';
import {
  SheetUpdateWebhookDto,
  SheetVectorOperationDto,
} from 'src/organization/dto/requests/post.dto';
import { TokensService } from 'src/tokens/tokens.service';
import { MyLogger } from 'src/logger/logger.service';
import { handleException } from 'helpers/handleException';

@Controller('google-sheet')
export class GoogleSheetController {
  constructor(
    private readonly sheetService: SheetService,
    private readonly tokenService: TokensService,
    private readonly myLogger: MyLogger,
  ) {}

  @ApiOperation({ summary: 'Route to trigger sheet to embedding conversion' })
  @Post('/webhook/embed')
  async embedSheet(
    @Body() body: SheetVectorOperationDto,
    @Res() res: Response,
  ) {
    res.sendStatus(200);
    try {
      const { accountId, headers, orgId, sheetId } = body;
      await this.sheetService.handleSheetData(body);
    } catch (error) {
      this.myLogger.error({
        message: error,
      });
      res
        .status(HttpStatus.INTERNAL_SERVER_ERROR)
        .json({ message: error.message });
    }
  }

  @ApiOperation({ summary: 'Route to handle the batch data from cloud task' })
  @Post('/batch')
  async processBatch(@Body() body: any, @Res() res: Response) {
    res.sendStatus(200);
    try {
      await this.sheetService.processBatchTask(body, res);
    } catch (error) {
      this.myLogger.error({
        message:
          `@error while handling batch task for orgId ${body?.orgId} and sheetId ${body.sheeId} :` +
          error?.message,
      });
      // res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message: error.message });
    }
  }

  @Post('/retrieve')
  async retrieveVectors(@Body() body: any, @Res() res: Response) {
    try {
      const { orgId, sheetId, content } = body;
      const result = await this.sheetService.querySheetVectors(
        orgId,
        sheetId,
        content,
      );
      res.status(HttpStatus.OK).json({ message: 'Success', data: result });
    } catch (error) {
      this.myLogger.error({
        message: error,
      });
      res
        .status(HttpStatus.INTERNAL_SERVER_ERROR)
        .json({ message: error.message });
    }
  }

  @Post('/retrieve/all')
  async retrieveSheetVectors(@Body() body: any, @Res() res: Response) {
    try {
      const { orgId, sheetId, accountId, sheetName = undefined } = body;
      const result = await this.sheetService.retrieveSheetData(
        orgId,
        sheetId,
        accountId,
        sheetName,
      );
      res.status(HttpStatus.OK).json({ message: 'Success', data: result });
    } catch (error) {
      this.myLogger.error({
        message: error,
      });
      res
        .status(HttpStatus.INTERNAL_SERVER_ERROR)
        .json({ message: error.message });
    }
  }

  @Post('/webhook/update')
  async update(@Body() body: SheetUpdateWebhookDto, @Res() res: Response) {
    res.sendStatus(200);
    try {
      if (body.task !== 'update') return;
      const token = await this.tokenService.getTokenByToken({
        token: body.capri_token,
      });
      if (!token?.token) throw new NotFoundException('Token not found');
      this.myLogger.log({
        message: `Sheet update webhook received for orgId: ${token.orgId}`,
        context: 'Sheet update webhook',
      });
      await this.sheetService.processRowUpdate(body, token.orgId);
    } catch (error) {
      this.myLogger.error({
        message: error?.message,
        context: 'Sheet update webhook',
      });
    }
  }

  @Get('/titles')
  async getSheetTitle(
    @Query('sheetId') sheetId: string,
    @Req() req: Request,
    @Res() res: Response,
  ) {
    let orgId = req.cookies['orgId'];
    await this.sheetService.getSheetTitles(sheetId, orgId, res);
  }

  @Post('/refresh/connection')
  async refreshConnection(
    @Body() body: { orgId: string; accountId: string },
    @Res() res: Response,
  ) {
    try {
      const { orgId, accountId } = body;
      await this.sheetService.refreshConnection(orgId, accountId);
      res.status(HttpStatus.OK).json({ message: 'Success' });
    } catch (error) {
      this.myLogger.error({
        message: error,
      });
      handleException(res, error);
    }
  }
}
