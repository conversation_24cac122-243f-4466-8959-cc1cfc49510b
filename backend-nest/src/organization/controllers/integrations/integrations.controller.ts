import {
  Controller,
  Post,
  Get,
  Put,
  Delete,
  Body,
  Res,
  HttpStatus,
  Param,
  Query,
  <PERSON>der,
  Req,
  UseGuards,
  Patch,
  BadRequestException,
} from '@nestjs/common';
import { SheetAuthDto } from '../../dto/sheet-auth.dto';
import { IntegrationService } from '../../services/integrations/integration.service';
import { Response } from 'express';
import { getGoogleSheet } from '../../../lib/google_funcs';
import { DeleteIntegrationDto } from '../../dto/requests/delete.dto';
import { v4 as uuidv4 } from 'uuid';
import { PROVIDERS } from 'src/lib/constant';
import { ApiOkResponse, ApiOperation, ApiParam } from '@nestjs/swagger';
import { GetIntegrationsDto } from 'src/organization/dto/responses/get.dto';
import { DeleteIntegrationResDto } from 'src/organization/dto/responses/delete.dto';
import {
  ConnectSheetDto,
  GoogleSheetData,
} from 'src/organization/dto/responses/post.dto';
import { OrganizationService } from 'src/organization/services/organization.service';
import { SheetService } from 'src/organization/services/integrations/sheet/sheet.service';
import { RequestWithUser } from 'src/auth/auth.interface';
import { AuthGuard } from 'src/auth/auth.guard';
import { allowAccessGuard } from 'src/guards/can-access/can-access.guard';
import { handleException } from 'helpers/handleException';
import { ObjectId } from 'mongodb';
import { MyLogger } from 'src/logger/logger.service';

@Controller('integrations')
export class IntegrationsController {
  constructor(
    private readonly integrationService: IntegrationService,
    private readonly organizationService: OrganizationService,
    private readonly sheetService: SheetService,
    private readonly myLogger: MyLogger,
  ) {}

  @Get(':orgId/google-calendar/:accountId')
  async findById(
    @Param('orgId') orgId: string,
    @Param('accountId') accountId: string,
  ) {
    return await this.integrationService.findGoogleCalendars(orgId, accountId);
  }

  @Get(':orgId')
  @UseGuards(AuthGuard, allowAccessGuard('settings', 'read'))
  @ApiOperation({ summary: 'Get all integrations for an organization' })
  @ApiParam({
    name: 'orgId',
    required: true,
    description: 'Organization Id to query the list of organizations',
  })
  @ApiOkResponse({
    description: 'List of integrations',
    type: GetIntegrationsDto,
  })
  async getIntegrations(@Param('orgId') orgId: string, @Res() res: Response) {
    return await this.integrationService.getDataSource(orgId, res);
  }

  @Post('sheet')
  @UseGuards(AuthGuard, allowAccessGuard('settings'))
  @ApiOperation({ summary: 'Connect Google Sheet' })
  @ApiOkResponse({ type: ConnectSheetDto })
  async connectSheet(
    @Req() req: RequestWithUser,
    @Body() body: SheetAuthDto,
    @Res() res: Response,
  ) {
    return await this.integrationService.connectSheet(body, res, req.userId);
  }

  @Delete('delete/:orgId/:accountId')
  @UseGuards(AuthGuard, allowAccessGuard('settings'))
  @ApiOkResponse({
    description: 'Delete a data source',
    type: DeleteIntegrationResDto,
  })
  async deleteIntegration(
    @Req() req: RequestWithUser,
    @Param() params: DeleteIntegrationDto,
    @Res() res: Response,
  ) {
    try {
      this.integrationService.deleteDataSource(
        params,
        res,
        req['isAdmin'],
        req.userId,
      );
    } catch (error) {
      handleException(res, error);
    }
  }

  @Patch(':orgId/google-calendar/:accountId')
  @UseGuards(AuthGuard, allowAccessGuard('settings'))
  @ApiOperation({ summary: 'Edit Google Calendar' })
  async editGoogleCalendar(
    @Req() req: RequestWithUser,
    @Param('orgId') orgId: string,
    @Param('accountId') accountId: string,
    @Body() body,
    @Res() res: Response,
  ) {
    const response = await this.integrationService.editGoogleCalendar(
      orgId,
      accountId,
      body,
      req.userId,
    );
    res.status(HttpStatus.OK).send(response);
  }

  @Patch('/:orgId/:provider/:accountId')
  @UseGuards(AuthGuard, allowAccessGuard('settings'))
  @ApiOperation({ summary: 'Edit a data source' })
  async editIntegration(
    @Req() req: RequestWithUser,
    @Body() body,
    @Res() res: Response,
  ) {
    try {
      const orgId = req.params.orgId || req.cookies['orgId'];
      const { provider, accountId } = req.params;
      if (!ObjectId.isValid(orgId))
        throw new BadRequestException('Invalid orgId');
      const response = await this.integrationService.editDataSource(
        orgId,
        body,
        provider,
        accountId,
      );
      res.status(HttpStatus.OK).send(response);
    } catch (error) {
      handleException(res, error);
    }
  }

  @Post('vector/delete')
  async deleteVector(@Body() body: any, @Res() res: Response) {
    res.sendStatus(200);
    try {
      const { provider, orgId, accountId } = body;
      if (provider === PROVIDERS.GOOGLE_SHEET) {
        const { sheetId, sheetName = undefined } = body;
        const result = await this.sheetService.deleteSheetVectors(
          orgId,
          sheetId,
          accountId,
          sheetName,
        );
        this.myLogger.log({
          message:
            `@result from deleting sheet vectors:` +
            JSON.stringify(result, null, 2),
        });
      }
    } catch (error) {
      handleException(res, error);
    }
  }

  @Get(':orgId/timezones')
  @UseGuards(AuthGuard, allowAccessGuard('settings'))
  @ApiOperation({ summary: 'Get all timezones' })
  async getTimezones(@Param('orgId') orgId: string, @Res() res: Response) {
    try {
      const timezones = await this.integrationService.getTimezones(orgId);
      res.status(HttpStatus.OK).send(timezones);
    } catch (error) {
      handleException(res, error);
    }
  }
}
