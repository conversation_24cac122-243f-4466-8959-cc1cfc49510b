import * as moment from 'moment';

export function generateDailyUTCSlots(
  start: moment.Moment,
  end: moment.Moment,
  eventDuration: number,
) {
  const slots = [];
  const startDate = moment.utc(start).startOf('day');
  const endDate = moment.utc(end).endOf('day');

  let currentDate = startDate.clone();

  while (currentDate.isSameOrBefore(endDate)) {
    let currentSlot = currentDate.clone().startOf('day'); // Start at 00:00
    const dailyEnd = currentDate.clone().endOf('day'); // End at 23:59:59

    while (currentSlot.isBefore(dailyEnd)) {
      const slotEnd = moment.min(
        currentSlot.clone().add(eventDuration, 'minutes'),
        dailyEnd,
      );
      slots.push({
        start: currentSlot.toISOString(),
        end: slotEnd.toISOString(),
      });
      currentSlot = slotEnd;
    }

    currentDate.add(1, 'day');
  }

  return slots;
}

export function generateSlots(
  start: moment.Moment,
  end: moment.Moment,
  durationMinutes: number,
) {
  const slots = [];
  let current = start.clone();

  while (current.isBefore(end)) {
    const slotEnd = moment.min(
      current.clone().add(durationMinutes, 'minutes'),
      end,
    );
    slots.push({
      start: current.toISOString(),
      end: slotEnd.toISOString(),
    });
    current = slotEnd;
  }

  return slots;
}

export function processOrgAvailability(
  availableHours: Array<{
    day: string;
    timeSlots: Array<{ start: string; end: string }>;
  }>,
  startDate: moment.Moment,
  endDate: moment.Moment,
  calendarTimeZone: string,
) {
  const orgAvailabilitySlots: {
    day: string;
    timeSlots: { start: string; end: string }[];
  }[] = [];

  availableHours.forEach((dayAvailability) => {
    let currentDate = startDate.clone();
    while (currentDate.isSameOrBefore(endDate)) {
      const currentDayOfWeek = currentDate.format('dddd');
      if (
        currentDayOfWeek.toLowerCase() === dayAvailability.day.toLowerCase()
      ) {
        orgAvailabilitySlots.push({
          day: dayAvailability.day.toLowerCase(),
          timeSlots: dayAvailability.timeSlots.map(({ start, end }) => ({
            start: timeToUTC(start, calendarTimeZone, currentDate),
            end: timeToUTC(end, calendarTimeZone, currentDate),
          })),
        });
      }
      currentDate = currentDate.add(1, 'day');
    }
  });

  return orgAvailabilitySlots;
}

// Helper function to convert time to UTC
function timeToUTC(
  time: string,
  timezone: string,
  date: moment.Moment,
): string {
  return moment
    .tz(`${date.format('YYYY-MM-DD')} ${time}`, timezone)
    .utc()
    .format();
}