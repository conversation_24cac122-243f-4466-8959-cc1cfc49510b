import {
  Controller,
  Get,
  Post,
  Req,
  Res,
  Body,
  HttpStatus,
  Delete,
  Param,
  Query,
  Put,
  Patch,
} from '@nestjs/common';
import { Request, Response } from 'express';
import { GoogleCalendarService } from 'src/organization/services/integrations/google-calendar/google-calendar.service';
import {
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
} from '@nestjs/swagger';
import { CalendarPreferenceDto } from 'src/organization/dto/requests/post.dto';
import { EventDto } from '../../../dto/event.dto';
import { MyLogger } from 'src/logger/logger.service';
import { MongoOrganizationService } from '../../../../mongo/service/organization/mongo-organization.service';
import axios from 'axios';
import { DayOfWeek } from '../../../../mongo/schemas/organization/org-availablity/org-availablity.schema';
import { RequestWithUser } from '../../../../auth/auth.interface';
import * as moment from 'moment';
import { calendarAvailabilityService } from '../../../services/calendar-availablity/calendar-availablity.service';
import { generateDailyUTCSlots, generateSlots, processOrgAvailability } from './helper';
import { tz } from 'moment-timezone';
@Controller('google-calendar')
export class GoogleCalendarController {
  constructor(
    private readonly mongoOrganizationService: MongoOrganizationService,
    private readonly googleCalendarService: GoogleCalendarService,
    private readonly calendarAvailabilityService: calendarAvailabilityService,
    private readonly myLogger: MyLogger,
  ) {}
  @Get('login')
  async login(@Query('orgId') orgId: string, @Req() req: RequestWithUser) {
    let userId = req.userId;
    const baseAuthUrl = await this.googleCalendarService.getAuthUrl();

    // Manually append orgId as a query parameter to the URL
    const redirectUrl = `${baseAuthUrl}&state=${orgId}:${userId}`;

    return { redirectUrl };
  }

  @Get('callback')
  async callback(
    @Query('code') code: string,
    @Query('state') state: string,
    @Res() res: Response,
  ) {
    let [orgId, userId] = state.split(':');

    const tokens = await this.googleCalendarService.exchangeCodeForAccessToken(
      code,
      orgId,
    );

    const connection = await this.googleCalendarService.saveGoogleCalendarCreds(
      orgId,
      tokens,
      userId,
    );

    const { calendar } = this.googleCalendarService.getOauth2Client(tokens);
    const calendarsList = await calendar.calendarList.list();
    const calendars = calendarsList.data.items;

    // return res.status(HttpStatus.OK).json({
    //   message: 'Google Account connected successfully!',
    //   calendars,
    //   userName: user.name,
    //   user,
    //   orgId,
    //   ...connection,
    //   success: true,
    // });
    // Create a client-side script that sends a message to the parent window
    const script = `
  <script>
    window.opener.postMessage(${JSON.stringify({
      message: 'Google Account connected successfully!',
      calendars,
      orgId,
      ...connection,
      success: true,
    })}, '*');
    window.close();
  </script>
`;

    // Send the script in the response
    res.send(script);
  }

  @ApiOperation({ summary: 'Connect Google Calendar' })
  @ApiOkResponse({
    description: 'An HTML page which shows the status of the connection',
  })
  @Post('connect')
  async connect(
    @Body()
    body: {
      orgId: string;
      calendarId: string;
      calendarTimeZone: string;
      eventName: string;
      eventDuration: number;
      eventLocation: string; //"zoom" || "physical"
      locationValue: string; //the url value or the physical location value
      eventDescription: string;
      dateRange: number;
      availableHours: Array<{
        day: DayOfWeek;
        timeSlots: Array<{
          start: string;
          end: string;
        }>;
      }>;
      startTimeIncrements: number;
      acceptTnc: boolean;
      appointment_per_slot: number;
    },
    @Req() req: RequestWithUser,
    @Res() res: Response,
  ) {
    try {
      const {
        orgId,
        calendarId,
        calendarTimeZone,
        eventName,
        eventDuration,
        eventLocation,
        locationValue,
        eventDescription,
        dateRange,
        availableHours,
        startTimeIncrements,
        acceptTnc,
        appointment_per_slot,
      } = body;

      let userId = req.userId;
      let usid = req.cookies['usid'];

      let credential = await this.googleCalendarService.getCalendarCreds(
        orgId,
        userId,
      );

      let newTokens = await axios.post('https://oauth2.googleapis.com/token', {
        client_id: process.env.GOOGLE_SERVICE_CLIENT_ID,
        client_secret: process.env.GOOGLE_SERVICE_CLIENT_SECRET,
        refresh_token: credential.credential.creds.refresh_token,
        grant_type: 'refresh_token',
      });
      await this.googleCalendarService.saveGoogleCalendarCreds(
        orgId,
        newTokens.data,
        userId,
      );

      const user = await this.googleCalendarService.getUserInfo(newTokens.data);

      const conn =
        await this.googleCalendarService.saveGoogleCalendarAsDataSource(
          orgId,
          credential.credential._id,
          user,
          calendarId,
          calendarTimeZone,
          eventName,
          userId,
          usid,
        );

      await this.calendarAvailabilityService.create(orgId, {
        orgId,
        userId,
        accountId: conn.accountId,
        calendarId,
        eventName,
        eventDuration,
        eventLocation,
        eventDescription,
        dateRange,
        locationValue,
        availableHours,
        startTimeIncrements,
        acceptTnc,
        appointment_per_slot,
      });

      return res.send({
        message: 'Google calendar added successfully!',
        userName: user.name,
        userId,
        orgId,
        ...conn,
      });
    } catch (error) {
      this.myLogger.error({ message: error });
      this.myLogger.error({ message: error.response.data });
      return res.status(500).json({ error: 'An error occurred' });
    }
  }

  @ApiOperation({ summary: 'Check Google Calendar Freebusy' })
  @ApiOkResponse({
    description:
      'Freebusy information for the specified calendar and time range',
  })
  @ApiNotFoundResponse({
    description: 'Calendar not found or freebusy information not available',
  })
  @Get('freebusy')
  async checkFreeBusy(
    @Body()
    body: {
      orgId: string;
      calendarId: string;
      accountId: string;
      start: string;
      end: string;
    },
    @Res() res: Response,
    @Req() req: RequestWithUser,
  ) {
    try {
      const { calendarId, accountId, start, end } = body;
      let userId = req.userId;

      // 1. Fetch calendar availability from DB
     const [calendarAvailability, organization] = await Promise.all([
       this.calendarAvailabilityService.findOrgAvailability({
         orgId: body.orgId,
         accountId: body.accountId,
       }),
       this.mongoOrganizationService.getOrganization({ _id: body.orgId }),
     ]);

      let calendarTimeZone = organization.connections.dataSources.find(
        (connection) => connection.calendarId === calendarId,
      )?.timezone;

      // 3. Convert start and end to UTC
      const startUTC = moment.utc(parseInt(start, 10));
      const endUTC = moment.utc(parseInt(end, 10));

      
      

      // 4. Generate slots
      const eventDuration = calendarAvailability[0].eventDuration || 15;
      const locationValue = calendarAvailability[0].locationValue || '';
      const eventDescription = calendarAvailability[0].eventDescription || '';
      const slotsUTC = generateDailyUTCSlots(startUTC, endUTC, eventDuration);

      

      // 5. Get busy slots from Google Calendar
      // Adjust start and end times for the Google Calendar API call
      const googleStartUTC = startUTC.clone().subtract(1, 'day').startOf('day');
      const googleEndUTC = endUTC.clone().add(1, 'day').endOf('day');

      const freeBusyResponse =
        await this.googleCalendarService.checkFreeBusyAvailability(
          body.orgId,
          calendarId,
          googleStartUTC.format('YYYY-MM-DDTHH:mm:ssZ'),
          googleEndUTC.format('YYYY-MM-DDTHH:mm:ssZ'),
          userId,
        );

      const busySlots: any = freeBusyResponse.calendars[calendarId]?.busy || [];

      

      // this.myLogger.log({
      //   message: `Busy slots in UTC: ${JSON.stringify(busySlots)}`,
      //   context: 'GoogleCalendarController.checkFreeBusy',
      // });

      // 6. Process orgAvailabilitySlots
      const orgAvailabilitySlots = processOrgAvailability(
        calendarAvailability[0].availableHours,
        startUTC,
        endUTC,
        calendarTimeZone,
      );

      
      

      // 7. Filter out busy slots and intersect with org availability
      const availableSlots = slotsUTC.filter((slot) => {
        // Check if slot is not busy
        const isNotBusy = !busySlots.some(
          (busySlot) =>
            moment(slot.start).isBefore(moment(busySlot.end)) &&
            moment(slot.end).isAfter(moment(busySlot.start)),
        );

        // Check if slot is within org availability
        const isWithinOrgAvailability = orgAvailabilitySlots.some((orgSlot) => {
          const slotStart = moment.tz(slot.start, calendarTimeZone);
          const slotEnd = moment.tz(slot.end, calendarTimeZone);
          const orgSlotStart = moment.tz(
            orgSlot.timeSlots[0].start,
            calendarTimeZone,
          );
          const orgSlotEnd = moment.tz(
            orgSlot.timeSlots[0].end,
            calendarTimeZone,
          );

          return (
            slotStart.isSameOrAfter(orgSlotStart) &&
            slotEnd.isSameOrBefore(orgSlotEnd)
          );
        });

        return isNotBusy && isWithinOrgAvailability;
      });

      

      const localAvailableSlots = availableSlots.map((slot) => ({
        start: moment.utc(slot.start).tz(calendarTimeZone).format(),
        end: moment.utc(slot.end).tz(calendarTimeZone).format(),
      }));

      

      return res.status(HttpStatus.OK).send(localAvailableSlots);
    } catch (error) {
      this.myLogger.error({
        message: error,
      });
      return res.status(HttpStatus.NOT_FOUND).json({
        error: 'Calendar not found or freebusy information not available',
      });
    }
  }

  @ApiOperation({ summary: 'Check Google Calendar Capri Settings' })
  @Get('settings')
  async checkSettings(
    @Body() body: { orgId: string; accountId: string },
    @Res() res: Response,
  ): Promise<Response> {
    try {
      const { accountId, orgId } = body;

      const calendarAvailability =
        await this.calendarAvailabilityService.findOrgAvailability({
          orgId,
          accountId,
        });

      return res.json(calendarAvailability[0]);
    } catch (error) {
      this.myLogger.error({ message: error });

      return res.status(HttpStatus.NOT_FOUND).json({
        error: 'Calendar not found or freebusy information not available',
      });
    }
  }

  @Post('events')
  @ApiOperation({ summary: 'Create Event on Google Calendar' })
  async createEvent(
    @Body() eventDto: EventDto,
    @Res() res: Response,
    @Req() req: RequestWithUser,
  ) {
    try {
     
        const [calendarAvailability, organization] = await Promise.all([
          this.calendarAvailabilityService.findOrgAvailability({
            orgId: eventDto.orgId,
            accountId: eventDto.accountId,
          }),
          this.mongoOrganizationService.getOrganization({
            _id: eventDto.orgId,
          }),
        ]);


      const eventDuration =
        calendarAvailability.find(
          (availability) =>
            availability.orgId === eventDto.orgId &&
            availability.calendarId === eventDto.calendarId,
        )?.eventDuration || 30; // Default to 30 minutes if not found

      let appointment_per_slot =
        calendarAvailability.find(
          (availability) =>
            availability.orgId === eventDto.orgId &&
            availability.calendarId === eventDto.calendarId,
        )?.appointment_per_slot || 1;

        this.myLogger.log({
          message: `appointment_per_slot: ${appointment_per_slot}`,
          context: 'GoogleCalendarController.createEvent',
        })

      const calendarTimeZone =
        organization.connections.dataSources.find(
          (connection) => connection.calendarId === eventDto.calendarId,
        )?.timezone || 'UTC'; // Default to UTC if not found

      const moment = require('moment-timezone');

      // Parse the start time in the calendar's time zone
      const startMoment = moment.tz(eventDto.startDateTime, calendarTimeZone);

      // Calculate the end time
      const endMoment = startMoment.clone().add(eventDuration, 'minutes');

      // Format times for Google Calendar API (RFC3339 format)
      const formattedStart = startMoment.format('YYYY-MM-DDTHH:mm:ssZ');
      const formattedEnd = endMoment.format('YYYY-MM-DDTHH:mm:ssZ');


        const calendarEvents =
          await this.googleCalendarService.listCalendarEvents(
            eventDto.orgId,
            req.userId,
            eventDto.calendarId,
            formattedStart,
            formattedEnd,
          );

          if (calendarEvents.length >= appointment_per_slot) {
            this.myLogger.log({
              message: `Time slot is full. No more than ${appointment_per_slot} appointments are allowed.`,
              context: 'GoogleCalendarController.createEvent',
            })
            return res.status(HttpStatus.BAD_REQUEST).json({
              error: `Time slot is full. No more than ${appointment_per_slot} appointments are allowed.`,
            });
          }

      const event = {
        summary: eventDto.summary,
        start: {
          dateTime: formattedStart,
          timeZone: calendarTimeZone,
        },
        end: {
          dateTime: formattedEnd,
          timeZone: calendarTimeZone,
        },
      };

      const createdEvent = await this.googleCalendarService.createEvent(
        req.userId,
        eventDto.orgId,
        eventDto.calendarId,
        event,
      );

      return res.status(HttpStatus.CREATED).json({
        message: 'Event created successfully',
        data: createdEvent,
      });
    } catch (error) {
      this.myLogger.error({
        message: error,
      });
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        error: 'Error creating event on Google Calendar',
      });
    }
  }

  @Patch('events/:eventId')
  @ApiOperation({ summary: 'Update Event on Google Calendar' })
  async updateEvent(
    @Req() req: RequestWithUser,
    @Res() res: Response
  ) {
    try {
      const userId = req.userId;
           
       let orgId = req.body['orgId'];
       let calendarId = req.body['calendarId'];
       let accountId = req.body['accountId']; 
       let eventId = req.body['eventId'];
       let summary = req.body['summary'];
       let startDateTime = req.body['startDateTime'];

      const [calendarAvailability, organization] = await Promise.all([
        this.calendarAvailabilityService.findOrgAvailability({
          orgId,
          accountId
        }),
        this.mongoOrganizationService.getOrganization({
          _id: orgId,
        }),
      ]);


    const eventDuration =
      calendarAvailability.find(
        (availability) =>
          availability.orgId === orgId &&
          availability.calendarId === calendarId,
      )?.eventDuration || 30; // Default to 30 minutes if not found

    let appointment_per_slot =
      calendarAvailability.find(
        (availability) =>
          availability.orgId === orgId &&
          availability.calendarId === calendarId,
      )?.appointment_per_slot || 1;

      this.myLogger.log({
        message: `appointment_per_slot: ${appointment_per_slot}`,
        context: 'GoogleCalendarController.createEvent',
      })

    const calendarTimeZone =
      organization.connections.dataSources.find(
        (connection) => connection.calendarId === calendarId,
      )?.timezone || 'UTC'; // Default to UTC if not found

      const moment = require('moment-timezone');


      // Parse the start time in the calendar's time zone
      const startMoment = moment.tz(startDateTime, calendarTimeZone);

      // Calculate the end time
      const endMoment = startMoment.clone().add(eventDuration, 'minutes');

      // Format times for Google Calendar API (RFC3339 format)
      const formattedStart = startMoment.format('YYYY-MM-DDTHH:mm:ssZ');
      const formattedEnd = endMoment.format('YYYY-MM-DDTHH:mm:ssZ');

      let event = {
        summary,
        start: {
          dateTime: formattedStart,
          timeZone: calendarTimeZone,
        },
        end: {
          dateTime: formattedEnd,
          timeZone: calendarTimeZone,
        },
      }


      const updatedEvent = await this.googleCalendarService.updateEvent(
        userId,
        orgId,
        calendarId,
        eventId,
        event
      );

      return res.status(HttpStatus.OK).json({
        message: 'Event updated successfully',
        data: updatedEvent
      });
    } catch (error) {
      this.myLogger.error({
        message: `Error updating event: ${error}`,
        context: 'GoogleCalendarController.updateEvent'
      });
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        error: 'Error updating event on Google Calendar'
      });
    }
  }

  @Get('calendars')
  @ApiOperation({ summary: 'List Calendars from Google Calendar' })
  async listCalendars(@Req() req: RequestWithUser, @Res() res: Response) {
    try {
      let orgId = req.query.orgId as string;

      const calendars = await this.googleCalendarService.listCalendars(
        orgId,
        req.userId,
      );

      return res.status(HttpStatus.OK).json({
        data: calendars,
      });
    } catch (error) {
      this.myLogger.error({
        message: error,
      });
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        error: 'Error listing calendars from Google Calendar',
      });
    }
  }

  @Get('calendar/events')
  @ApiOperation({ summary: 'List Calendars from Google Calendar' })
  async listCalendarEvents(@Req() req: RequestWithUser, @Res() res: Response) {
    try {
      let orgId = req.query.orgId as string;
      let calendarId = req.query.calendarId as string;
      let start = req.query.start as string;
      let end = req.query.end as string;

      const calendarEvents =
        await this.googleCalendarService.listCalendarEvents(
          orgId,
          req.userId,
          calendarId,
          start,
          end,
        );

      return res.status(HttpStatus.OK).json({
        data: calendarEvents,
      });
    } catch (error) {
      this.myLogger.error({
        message: error,
      });
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        error: 'Error listing calendar events from Google Calendar',
      });
    }
  }



  @Delete('events/:eventId')
  @ApiOperation({ summary: 'Delete Event from Google Calendar' })
  async deleteEvent(
    @Param('eventId') eventId: string,
    @Query('orgId') orgId: string,
    @Query('calendarId') calendarId: string = 'primary',
    @Res() res: Response,
    @Req() req: RequestWithUser,
  ) {
    try {
      let userId = req.userId;
      await this.googleCalendarService.deleteEvent(
        userId,
        orgId,
        calendarId,
        eventId,
      );

      return res.status(HttpStatus.NO_CONTENT).send();
    } catch (error) {
      this.myLogger.error({
        message: error,
      });
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        error: 'Error deleting event from Google Calendar',
      });
    }
  }
}
