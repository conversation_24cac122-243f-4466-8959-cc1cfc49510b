import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import axios from 'axios';
import { CONNECTION_TYPES, PROVIDERS } from '../../../../lib/constant';
import { MongoCredentialsService } from '../../../../mongo/service/credentials/mongo-credentials.service';
import { IntegrationService } from '../integration.service';
import { CreateCredentialDto } from '../../../../mongo/dto/credentials/create.dto';
import getId from 'get-short-id';
import * as Acuity from 'acuityscheduling';
import { MyLogger } from 'src/logger/logger.service';

interface TokenResponse {
  access_token: string;
  token_type: string;
}

@Injectable()
export class AcuityService {
  private clientId = process.env.ACUITY_CLIENT_ID;
  private clientSecret = process.env.ACUITY_CLIENT_SECRET;
  private redirectUri = process.env.ACUITY_REDIRECT_URI;

  constructor(
    private readonly mongoCredentialsService: MongoCredentialsService,
    private readonly integrationService: IntegrationService,
    private readonly myLogger: MyLogger
  ) {}

  async getAccessToken({ code }: { code: string }): Promise<TokenResponse> {
    const acuity = Acuity.oauth({
      clientId: this.clientId,
      clientSecret: this.clientSecret,
      redirectUri: this.redirectUri,
    });

    return new Promise((resolve, reject) => {
      acuity.requestAccessToken(code, (error, tokenResponse) => {
        if (error) {
          this.myLogger.error({
            message: 'Error Getting the tokens' + error?.message
          });
          reject(error);
        } else {
          resolve(tokenResponse);
        }
      });
    });
  }

  async saveSchedulingCreds(orgId: string, tokens: any, user: any) {
    let accountId = getId({
      count: 15,
    });

    const credentialBody: CreateCredentialDto = {
      organizationId: orgId,
      creds: tokens,
      keyId: user?.id,
      type: CONNECTION_TYPES.DATASOURCES,
      kind: 'AcuityCredential',
    };

    const cred = await this.mongoCredentialsService.createCredentials(
      credentialBody,
    );

    const dataSource = {
      credentialId: cred._id,
      author: user?.firstName + '' + user?.lastName,
      providerName: PROVIDERS.ACUITY,
      accountId,
      keyId: user?.id,
    };

    await this.integrationService.saveDataSource(orgId, dataSource);
    return { ...dataSource, accountId };
  }

  async updateSchedulingCreds(orgId: string, newTokens: any, user: any) {
    const query = {
      organizationId: orgId,
      keyId: user?.id,
      kind: 'AcuityCredential',
    };

    // Update body with the new tokens
    const updateBody = {
      creds: newTokens,
    };

    try {
      const updatedCredential =
        await this.mongoCredentialsService.updateCredential(query, updateBody);
      return updatedCredential;
    } catch (error) {
      throw new Error('Error updating Calendly credentials');
    }
  }

  async getUserInfo({ access_token }: { access_token: string }) {
    try {
      const url = 'https://acuityscheduling.com/api/v1/me';
      const headers = {
        Authorization: `Bearer ${access_token}`,
      };

      const response = await axios.get(url, { headers });

      return response.data;
    } catch (error) {
      
      throw error;
    }
  }

  async getSchedulingCreds(orgId: string) {
    const credentialQuery = {
      organizationId: orgId,
      kind: 'AcuityCredential',
    };

    const credential = await this.mongoCredentialsService.getCredential(
      credentialQuery,
    );

    return {
      credential,
    };
  }

  async getAvailability(orgId: string, start_time: string, end_time: string) {
    // Implement Acuity Scheduling availability retrieval logic
  }
}
