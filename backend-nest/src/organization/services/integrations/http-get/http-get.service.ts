import { HttpException, Injectable } from '@nestjs/common';
import { calendar_v3, google } from 'googleapis';
import { CONNECTION_TYPES, KINDS, PROVIDERS } from 'src/lib/constant';
import { CreateCredentialDto } from 'src/mongo/dto/credentials/create.dto';
import { MongoCredentialsService } from 'src/mongo/service/credentials/mongo-credentials.service';
import { ApiclientService } from 'src/api-client/services/apiclient.service';
import { methods } from 'src/api-client/utils/channel.constants';
import { IntegrationService } from '../integration.service';
import { OrganizationService } from '../../organization.service';
import { HttpStatus } from '@nestjs/common/enums';
import { IGoogleDto } from 'src/organization/dto/google.dto';
import { v4 as uuidv4 } from 'uuid';
import axios from 'axios';
import { MyLogger } from 'src/logger/logger.service';
import { HttpRequestGetService } from 'src/http-request/GET/get.service';

@Injectable()
export class HttpGetService {
  oAuth2Client: any;
  googleCalendar: any;

  constructor(
    private readonly mongoCredentialsService: MongoCredentialsService,
    private readonly apiClientService: ApiclientService,
    private readonly integrationService: IntegrationService,
    private readonly organizationService: OrganizationService,
    private readonly myLogger: MyLogger,
    private readonly httpRequestGetService: HttpRequestGetService,
  ) {}

  async saveHttpGetAsDataSource(
    orgId: string,
    userId: string,
    usid: string,
    eventName: string,
    createHttpGetRequestDto,
  ) {
    let accountId = uuidv4();

    //add account id to the request
    createHttpGetRequestDto.accountId = accountId;

    let createGet = await this.httpRequestGetService.create(createHttpGetRequestDto);

    const dataSource = {
      providerName: PROVIDERS.GET,
      accountId,
      keyId: createGet._id.toString(),
      name: eventName,
      userId,
      usid,
      author: createHttpGetRequestDto.author,
    };

    await this.integrationService.saveDataSource(orgId, dataSource);
    return { ...dataSource, ...createHttpGetRequestDto };
  }
  async getCalendarCreds(orgId: string, userId: string) {
    try {
      const credentialQuery = {
        organizationId: orgId,
        keyId: userId,
      };
      const credential = await this.mongoCredentialsService.getCredential(
        credentialQuery,
      );

      if (!credential) {
        throw new HttpException(
          'Calendar credentials not found',
          HttpStatus.NOT_FOUND,
        );
      }

      return { credential };
    } catch (error) {
      
      throw new HttpException(
        'Internal Server Error',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async saveGoogleCalendarCreds(orgId: string, tokens: any, userId: string) {
    const existingCredential = await this.mongoCredentialsService.getCredential(
      {
        organizationId: orgId,
        keyId: userId,
      },
    );

    if (existingCredential) {
      const credentialBody: CreateCredentialDto = {
        organizationId: orgId,
        creds: {
          ...tokens,
          refresh_token: tokens.refresh_token
            ? tokens.refresh_token
            : existingCredential.creds.refresh_token,
        },
        keyId: userId,
        type: CONNECTION_TYPES.DATASOURCES,
        kind: 'GoogleCredential',
      };

      const updatedCredential =
        await this.mongoCredentialsService.updateCredential(
          {
            organizationId: orgId,
            keyId: userId,
          },
          credentialBody,
        );
      return updatedCredential;
    } else {
      const credentialBody: CreateCredentialDto = {
        organizationId: orgId,
        creds: tokens,
        keyId: userId,
        type: CONNECTION_TYPES.DATASOURCES,
        kind: 'GoogleCredential',
      };

      const newCredential =
        await this.mongoCredentialsService.createCredentials(credentialBody);
      return newCredential;
    }
  }
}
