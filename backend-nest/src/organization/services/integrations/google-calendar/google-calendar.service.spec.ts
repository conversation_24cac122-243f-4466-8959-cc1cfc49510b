import { Test, TestingModule } from '@nestjs/testing';
import { GoogleCalendarService } from './google-calendar.service';
import { createMock, DeepMocked } from '@golevelup/ts-jest';
import { MongoCredentialsService } from 'src/mongo/service/credentials/mongo-credentials.service';
import { ApiclientService } from 'src/api-client/services/apiclient.service';
import { IntegrationService } from '../integration.service';

describe('GoogleCalendarService', () => {
  let calendarService: GoogleCalendarService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [GoogleCalendarService],
    }).compile();

    calendarService = module.get<GoogleCalendarService>(GoogleCalendarService);
  });

  it('should be defined', () => {
    expect(calendarService).toBeDefined();
  });
});
