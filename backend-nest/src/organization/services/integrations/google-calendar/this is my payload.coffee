this is my payload


Read this calendar whenever the user talks about appointment. This calendar will provide a range of dates that will help the user to know about the availability. So use this only when user queries about anything regarding appointment.

Add appointments to this calendar once the contact has confirmed their preferred Day and Time. You should only add appointments to the calendar once the day and time have been clearly established and confirmed by the assistant, and do not add the appointment more than once for the same contact.

{
    "orgId": "6561047156b3825f13b1c878",
    "accountId": "e56174d5-3ca7-48cf-9cc1-f65cc4da7924",
    "calendarId": "<EMAIL>",
    "start": "*************",
    "end": "*************"
}

these googleBusySlots

Google busySlots:  [

  {

    start: '2024-04-30T09:30:00+05:30',

    end: '2024-04-30T10:30:00+05:30'

  },

  {

    start: '2024-04-30T14:30:00+05:30',

    end: '2024-04-30T15:30:00+05:30'

  },

  {

    start: '2024-04-30T16:00:00+05:30',

    end: '2024-04-30T17:00:00+05:30'

  },

  {

    start: '2024-05-01T09:30:00+05:30',

    end: '2024-05-01T10:30:00+05:30'

  },

  {

    start: '2024-05-01T13:00:00+05:30',

    end: '2024-05-01T14:00:00+05:30'

  }

]

these are Org availability slots: 

 [

  { day: 'tuesday', timeSlots: [ [Object] ] },

  { day: 'wednesday', timeSlots: [ [Object] ] }

]

timeSlots are from 9:00am to 17:00pm

and this is the response i am getting

