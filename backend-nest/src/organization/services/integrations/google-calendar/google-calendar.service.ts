import { HttpException, Injectable } from '@nestjs/common';
import { calendar_v3, google } from 'googleapis';
import { CONNECTION_TYPES, KINDS, PROVIDERS } from 'src/lib/constant';
import { CreateCredentialDto } from 'src/mongo/dto/credentials/create.dto';
import { MongoCredentialsService } from 'src/mongo/service/credentials/mongo-credentials.service';
import { ApiclientService } from 'src/api-client/services/apiclient.service';
import { methods } from 'src/api-client/utils/channel.constants';
import { IntegrationService } from '../integration.service';
import { OrganizationService } from '../../organization.service';
import { HttpStatus } from '@nestjs/common/enums';
import { IGoogleDto } from 'src/organization/dto/google.dto';
import { v4 as uuidv4 } from 'uuid';
import axios from 'axios';
import { MyLogger } from 'src/logger/logger.service';

// const CLIENT_ID =
// const REDIRECT_URI = ;
const scope = [
  'https://www.googleapis.com/auth/calendar.readonly',
  'https://www.googleapis.com/auth/calendar.events',
  'https://www.googleapis.com/auth/userinfo.profile',
];

@Injectable()
export class GoogleCalendarService {
  oAuth2Client: any;
  googleCalendar: any;

  constructor(
    private readonly mongoCredentialsService: MongoCredentialsService,
    private readonly apiClientService: ApiclientService,
    private readonly integrationService: IntegrationService,
    private readonly organizationService: OrganizationService,
    private readonly myLogger: MyLogger,
  ) {
    this.oAuth2Client = new google.auth.OAuth2(
      process.env.GOOGLE_SERVICE_CLIENT_ID,
      process.env.GOOGLE_SERVICE_CLIENT_SECRET,
      process.env.GOOGLE_SERVICE_REDIRECT_URL,
    );
    this.googleCalendar = google.calendar({ version: 'v3' });
  }

  async getAuthUrl() {
    // Go to this URL to acquire a refresh token
    return await this.oAuth2Client.generateAuthUrl({
      access_type: 'offline',
      scope: scope,
      prompt: 'consent',
    });
  }

  async getToken(code: string) {
    const { tokens } = await this.oAuth2Client.getToken(code);
    return tokens;
  }

  getOauth2Client(tokens: any) {
    if (this.oAuth2Client !== undefined) {
      this.oAuth2Client.setCredentials(tokens);
      google.options({ auth: this.oAuth2Client });
      return { calendar: this.googleCalendar, authClient: this.oAuth2Client };
    } else {
      throw new Error('OAuth2 Client not initialized');
    }
  }

  async saveGoogleCalendarCreds(orgId: string, tokens: any, userId: string) {
    const existingCredential = await this.mongoCredentialsService.getCredential(
      {
        organizationId: orgId,
        keyId: userId,
      },
    );

    if (existingCredential) {
      const credentialBody: CreateCredentialDto = {
        organizationId: orgId,
        creds: {
          ...tokens,
          refresh_token: tokens.refresh_token
            ? tokens.refresh_token
            : existingCredential.creds.refresh_token,
        },
        keyId: userId,
        type: CONNECTION_TYPES.DATASOURCES,
        kind: 'GoogleCredential',
      };

      const updatedCredential =
        await this.mongoCredentialsService.updateCredential(
          {
            organizationId: orgId,
            keyId: userId,
          },
          credentialBody,
        );
      return updatedCredential;
    } else {
      const credentialBody: CreateCredentialDto = {
        organizationId: orgId,
        creds: tokens,
        keyId: userId,
        type: CONNECTION_TYPES.DATASOURCES,
        kind: 'GoogleCredential',
      };

      const newCredential =
        await this.mongoCredentialsService.createCredentials(credentialBody);
      return newCredential;
    }
  }

  async saveGoogleCalendarAsDataSource(
    orgId: string,
    credentialId: any,
    user: IGoogleDto,
    calendarId: string,
    calendarTimeZone: string,
    eventName: string,
    userId: string,
    usid: string,
  ) {
    let accountId = uuidv4();

    const dataSource = {
      credentialId,
      author: user?.name,
      providerName: PROVIDERS.GOOGLE_CALENDAR,
      accountId,
      keyId: user?.sub,
      name: eventName,
      calendarId,
      timezone: calendarTimeZone,
      userId,
      usid,
    };

    await this.integrationService.saveDataSource(orgId, dataSource);
    return { ...dataSource, accountId };
  }
  async getCalendarCreds(orgId: string, userId: string) {
    try {
      const credentialQuery = {
        organizationId: orgId,
        keyId: userId,
        kind: 'GoogleCredential',
      };
      const credential = await this.mongoCredentialsService.getCredential(
        credentialQuery,
      );

      if (!credential) {
        throw new HttpException(
          'Calendar credentials not found',
          HttpStatus.NOT_FOUND,
        );
      }

      return { credential };
    } catch (error) {
      
      throw new HttpException(
        'Internal Server Error',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async getUserInfo(tokens: any): Promise<IGoogleDto> {
    try {
      return await this.apiClientService.apiRequest(
        methods.GET,
        'https://www.googleapis.com/oauth2/v3/userinfo',
        undefined,
        {
          headers: { Authorization: 'Bearer ' + tokens.access_token },
        },
      );
    } catch (error) {
      
    }
  }

  async exchangeCodeForAccessToken(code: string, orgId: string) {
    try {
      const tokens = await this.oAuth2Client.getToken(code);

      this.oAuth2Client.setCredentials(tokens);
      google.options({ auth: this.oAuth2Client });

      return tokens.tokens;
    } catch (error) {
      
      throw error;
    }
  }

  async checkFreeBusyAvailability(
    orgId: string,
    calendarId: string,
    timeMin: string,
    timeMax: string,
    userId: string,
  ) {
    try {
      const url = `https://www.googleapis.com/calendar/v3/freeBusy?key=${process.env.GOOGLE_SERVICE_API_KEY}`;

      let credential = await this.getCalendarCreds(orgId, userId);

      const requestBody = {
        timeMin,
        timeMax,
        items: [{ id: calendarId }],
      };

      let newTokens = await axios.post('https://oauth2.googleapis.com/token', {
        client_id: process.env.GOOGLE_SERVICE_CLIENT_ID,
        client_secret: process.env.GOOGLE_SERVICE_CLIENT_SECRET,
        refresh_token: credential.credential.creds.refresh_token,
        grant_type: 'refresh_token',
      });

      await this.saveGoogleCalendarCreds(orgId, newTokens.data, userId);

      const headers = {
        Authorization: `Bearer ${newTokens.data.access_token}`,
        Accept: 'application/json',
        'Content-Type': 'application/json',
      };

      const response = await axios.post(url, requestBody, { headers });

      return response.data;
    } catch (error) {
      
      throw new HttpException(
        'Error checking freebusy availability',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async createEvent(
    userId: string,
    orgId: string,
    calendarId: string,
    event: calendar_v3.Schema$Event,
  ): Promise<calendar_v3.Schema$Event> {
    try {
      let credential = await this.getCalendarCreds(orgId, userId);

      let tokens = credential.credential.creds;

      this.oAuth2Client.setCredentials(tokens);
      google.options({ auth: this.oAuth2Client });

      let newTokens = await axios.post('https://oauth2.googleapis.com/token', {
        client_id: process.env.GOOGLE_SERVICE_CLIENT_ID,
        client_secret: process.env.GOOGLE_SERVICE_CLIENT_SECRET,
        refresh_token: credential.credential.creds.refresh_token,
        grant_type: 'refresh_token',
      });

      await this.saveGoogleCalendarCreds(orgId, newTokens.data, userId);

      const calendar = google.calendar({
        version: 'v3',
        auth: this.oAuth2Client,
      });


      const response = await calendar.events.insert({
        calendarId,
        requestBody: event,
      });

      return response.data;
    } catch (error) {
      throw new HttpException(
        'Error creating event on Google Calendar',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async listCalendars(
    orgId: string,
    userId: string,
  ): Promise<calendar_v3.Schema$CalendarListEntry[]> {
    try {
      let credential = await this.getCalendarCreds(orgId, userId);

      let tokens = credential.credential.creds;

      this.oAuth2Client.setCredentials(tokens);
      google.options({ auth: this.oAuth2Client });

      let newTokens = await axios.post('https://oauth2.googleapis.com/token', {
        client_id: process.env.GOOGLE_SERVICE_CLIENT_ID,
        client_secret: process.env.GOOGLE_SERVICE_CLIENT_SECRET,
        refresh_token: credential.credential.creds.refresh_token,
        grant_type: 'refresh_token',
      });

      await this.saveGoogleCalendarCreds(orgId, newTokens.data, userId);

      const calendar = google.calendar({ version: 'v3' });

      const response = await calendar.calendarList.list();
      return response.data.items || [];
    } catch (error) {
      throw new HttpException(
        'Error listing calendars from Google Calendar',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async listCalendarEvents(
    orgId: string,
    userId: string,
    calendarId: string,
    start?: string,
    end?: string,
  ): Promise<calendar_v3.Schema$CalendarListEntry[]> {
    try {
      let credential = await this.getCalendarCreds(orgId, userId);
      let tokens = credential.credential.creds;

      this.oAuth2Client.setCredentials(tokens);
      google.options({ auth: this.oAuth2Client });

      let newTokens = await axios.post('https://oauth2.googleapis.com/token', {
        client_id: process.env.GOOGLE_SERVICE_CLIENT_ID,
        client_secret: process.env.GOOGLE_SERVICE_CLIENT_SECRET,
        refresh_token: credential.credential.creds.refresh_token,
        grant_type: 'refresh_token',
      });

      await this.saveGoogleCalendarCreds(orgId, newTokens.data, userId);

      const calendar = google.calendar({ version: 'v3' });

      const isISO8601 = (str: string): boolean =>
        /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d{3})?([+-]\d{2}:\d{2}|Z)?$/.test(str);

      const isValidTimestamp = (str: string): boolean => {
        const num = parseInt(str);
        return !isNaN(num) && num > 0;
      };

      const convertToISO8601 = (timeStr?: string): string | undefined => {
        if (!timeStr) return undefined;
        
        if (isISO8601(timeStr)) return timeStr;
        if (isValidTimestamp(timeStr)) return new Date(parseInt(timeStr)).toISOString();
        
        throw new HttpException(
          `Invalid time format. Expected ISO8601 or timestamp, got: ${timeStr}`,
          HttpStatus.BAD_REQUEST
        );
      };

      // Default time range if start/end not provided
      let timeMin: string;
      let timeMax: string;

      if (!start && !end) {
        // Default to today through next 30 days
        const now = new Date();
        // Set to start of day (12:00 AM)
        now.setHours(0, 0, 0, 0);
        const thirtyDaysFromNow = new Date();
        thirtyDaysFromNow.setDate(now.getDate() + 30);
        
        timeMin = now.toISOString();
        timeMax = thirtyDaysFromNow.toISOString();
    
      } else {
        // For custom start date, set to start of day
        const startDate = start ? new Date(convertToISO8601(start)) : new Date();
        startDate.setHours(0, 0, 0, 0);
        timeMin = startDate.toISOString();
        
        timeMax = convertToISO8601(end) || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString();
      }

      const response = await calendar.events.list({
        calendarId,
        timeMin,
        timeMax,
        singleEvents: true,
        orderBy: 'startTime',
      });

      return response.data.items || [];
    } catch (error) {
    
      if (error instanceof HttpException) {
        throw error;
      }
      
      throw new HttpException(
        error?.response?.data?.error || 'Error listing calendar events',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async updateEvent(
    userId: string,
    orgId: string,
    calendarId: string,
    eventId: string,
    event: calendar_v3.Schema$Event,
  ) {
    try {
      let credential = await this.getCalendarCreds(orgId, userId);

      let tokens = credential.credential.creds;

      this.oAuth2Client.setCredentials(tokens);
      google.options({ auth: this.oAuth2Client });

      let newTokens = await axios.post('https://oauth2.googleapis.com/token', {
        client_id: process.env.GOOGLE_SERVICE_CLIENT_ID,
        client_secret: process.env.GOOGLE_SERVICE_CLIENT_SECRET,
        refresh_token: credential.credential.creds.refresh_token,
        grant_type: 'refresh_token',
      });

      await this.saveGoogleCalendarCreds(orgId, newTokens.data, userId);

      const calendar = google.calendar({ version: 'v3' });

      const response = await calendar.events.update({
        calendarId,
        eventId,
        requestBody: event,
      });

      return response.data;
    } catch (error) {
      throw new HttpException(
        'Error updating event on Google Calendar',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async deleteEvent(
    userId: string,
    orgId: string,
    calendarId: string,
    eventId: string,
  ): Promise<void> {
    try {
      let credential = await this.getCalendarCreds(orgId, userId);

      let tokens = credential.credential.creds;

      this.oAuth2Client.setCredentials(tokens);
      google.options({ auth: this.oAuth2Client });

      let newTokens = await axios.post('https://oauth2.googleapis.com/token', {
        client_id: process.env.GOOGLE_SERVICE_CLIENT_ID,
        client_secret: process.env.GOOGLE_SERVICE_CLIENT_SECRET,
        refresh_token: credential.credential.creds.refresh_token,
        grant_type: 'refresh_token',
      });

      await this.saveGoogleCalendarCreds(orgId, newTokens.data, userId);

      const calendar = google.calendar({
        version: 'v3',
        auth: this.oAuth2Client,
      });

      await calendar.events.delete({
        calendarId,
        eventId,
      });
    } catch (error) {
      throw new HttpException(
        'Error deleting event from Google Calendar',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
