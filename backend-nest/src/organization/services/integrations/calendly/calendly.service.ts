import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import axios from 'axios';
import { CONNECTION_TYPES, KINDS, PROVIDERS } from '../../../../lib/constant';
import { MongoCredentialsService } from '../../../../mongo/service/credentials/mongo-credentials.service';
import { IntegrationService } from '../integration.service';
import { CreateCredentialDto } from '../../../../mongo/dto/credentials/create.dto';
import getId from 'get-short-id';
import { OrganizationService } from '../../organization.service';
import { MyLogger } from 'src/logger/logger.service';

@Injectable()
export class CalendlyService {
  private clientId = process.env.CALENDLY_CLIENT_ID;
  private clientSecret = process.env.CALENDLY_CLIENT_SECRET;
  private redirectUri = process.env.CALENDLY_REDIRECT_URI;

  constructor(
    private readonly mongoCredentialsService: MongoCredentialsService,
    private readonly integrationService: IntegrationService,
    private readonly organizationService: OrganizationService,
    private readonly myLogger: MyLogger
  ) {}

  async getAccessToken(code: string) {
    const encodedParams = new URLSearchParams();
    encodedParams.append('code', code);
    encodedParams.append('redirect_uri', this.redirectUri);
    encodedParams.append('grant_type', 'authorization_code');

    const options = {
      method: 'POST',
      url: 'https://auth.calendly.com/oauth/token',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        Accept: 'application/json',
        Authorization: `Basic ${Buffer.from(
          `${this.clientId}:${this.clientSecret}`,
        ).toString('base64')}`,
      },
      data: {
        code,
        redirect_uri: this.redirectUri,
        grant_type: 'authorization_code',
      },
    };

    try {
      const { data } = await axios.request(options);
      return data;
    } catch (error) {
      throw new Error('Error fetching access token');
    }
  }

  async saveCalendarCreds(orgId: string, tokens: any, user: any) {
    const credentialBody: CreateCredentialDto = {
      organizationId: orgId,
      creds: tokens,
      keyId: user?.resource?.uri,
      type: CONNECTION_TYPES.DATASOURCES,
      kind: 'CalendlyCredential',
    };

    await this.mongoCredentialsService.createCredentials(credentialBody);
  }

  async updateCalendarCreds({
    orgId,
    user,
    newTokens,
  }: {
    orgId: string;
    user: any;
    newTokens?: any;
  }) {
    const query = {
      organizationId: orgId,
      keyId: user?.resource?.uri,
      kind: 'CalendlyCredential',
    };

    const updateBody: { creds?: any; calendarId?: string } = {};

    if (newTokens) {
      updateBody.creds = newTokens;
    }

    try {
      const updatedCredential =
        await this.mongoCredentialsService.updateCredential(query, updateBody);
      return updatedCredential;
    } catch (error) {
      throw new Error('Error updating Calendly credentials: ' + error.message);
    }
  }

  async getUserInfo({ access_token }: { access_token: string }) {
    const options = {
      method: 'GET',
      url: 'https://api.calendly.com/users/me',
      headers: {
        Accept: 'application/json',
        Authorization: `Bearer ${access_token}`,
      },
    };

    try {
      const { data } = await axios.request(options);
      return data;
    } catch (error) {
      this.myLogger.log({
        message: 'Error getting user info, error: ' + error.response.data
      });
      return error;
    }
  }

  async getCalendarCreds(orgId: string) {
    const credentialQuery = {
      organizationId: orgId,
      kind: 'CalendlyCredential',
    };

    const credential = await this.mongoCredentialsService.getCredential(
      credentialQuery,
    );

    return {
      credential,
    };
  }

  async isTokenValid(token: string): Promise<boolean> {
    const options = {
      method: 'POST',
      url: 'https://auth.calendly.com/oauth/introspect',
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      data: {
        client_id: this.clientId,
        client_secret: this.clientSecret,
        token: token,
      },
    };

    try {
      const response = await axios.request(options);
      return response.data.active;
    } catch (error) {
      
      return false;
    }
  }

  async refreshToken(refresh_token: string) {
    const options = {
      method: 'POST',
      url: 'https://auth.calendly.com/oauth/token',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        Authorization: `Basic ${Buffer.from(
          `${this.clientId}:${this.clientSecret}`,
        ).toString('base64')}`,
      },
      data: {
        refresh_token: refresh_token,
        grant_type: 'refresh_token',
      },
    };

    try {
      const { data } = await axios.request(options);
      return data;
    } catch (error) {
      
      throw new Error('Error refreshing access token');
    }
  }

  private async updatedTokens(credential: any): Promise<any> {
    const refreshedTokenData = await this.refreshToken(
      credential.creds.refresh_token,
    );

    await this.updateCalendarCreds({
      orgId: credential.organizationId,
      newTokens: refreshedTokenData,
      user: await this.getUserInfo({
        access_token: refreshedTokenData.access_token,
      }),
    });
    return refreshedTokenData.access_token;
  }

  async getAvailability({ orgId, start_time, end_time }) {
    let credential = await this.getCalendarCreds(orgId);

    let access_token = await this.updatedTokens(credential.credential);

    const options = {
      method: 'GET',
      url: 'https://api.calendly.com/user_busy_times',
      params: {
        user: credential.credential.keyId,
        start_time,
        end_time,
      },
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${access_token}`,
      },
    };

    try {
      const { data } = await axios.request(options);
      return data;
    } catch (error) {
      
      throw new HttpException(
        'Internal Server Error',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async createSingleUseSchedulingLink(
    orgId: string,
    eventTypeUri: string,
    date: string,
    time: string,
  ): Promise<any> {
    const credential = await this.getCalendarCreds(orgId);

    let access_token = await this.updatedTokens(credential.credential);

    let user = await this.getUserInfo({ access_token });
    let userTimeZone = user.resource.timezone;

    const requestBody = {
      max_event_count: 1,
      owner: eventTypeUri,
      owner_type: 'EventType',
    };

    const options = {
      method: 'POST',
      url: 'https://api.calendly.com/scheduling_links',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${access_token}`,
      },
      data: requestBody,
    };

    try {
      const { data } = await axios.request(options);
      const formattedDate = `${date}T${time}`;
      const bookingUrl = `${data.resource.booking_url}/${formattedDate}?month=${
        date.split('-')[0]
      }-${date.split('-')[1]}&date=${date}`;

      return { bookingUrl };
    } catch (error) {
      
      throw new HttpException(
        'Error creating single-use scheduling link',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async listEventTypesByUser(orgId: string): Promise<any> {
    const credential = await this.getCalendarCreds(orgId);

    let access_token = await this.updatedTokens(credential.credential);

    let user = await this.getUserInfo({
      access_token,
    });

    const options = {
      method: 'GET',
      url: `https://api.calendly.com/event_types?user=${encodeURIComponent(
        user?.resource?.uri,
      )}`,
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${access_token}`,
      },
    };

    try {
      const { data } = await axios.request(options);
      return data;
    } catch (error) {
      
      throw new HttpException(
        'Error listing event types',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async CalendlyConnect({
    orgId,
    eventTypeUri,
  }: {
    orgId: string;
    eventTypeUri: string;
  }) {
    const credential = await this.getCalendarCreds(orgId);

    let access_token = await this.updatedTokens(credential.credential);

    let user = await this.getUserInfo({
      access_token,
    });

    let connections = await this.saveCalendarDatasource(
      orgId,
      user,
      eventTypeUri,
    );

    return {
      isSuccess: true,
      user,
      connections,
      credential: credential,
    };
  }

  async saveCalendarDatasource(orgId: string, user: any, calendarId: string) {
    let accountId = getId({
      count: 15,
    });

    let cred = await this.getCalendarCreds(orgId);

    const dataSource = {
      credentialId: cred.credential._id,
      author: user?.resource?.name,
      providerName: PROVIDERS.CALENDLY,
      accountId,
      keyId: calendarId,
    };

    const org = await this.organizationService.getOrganizationDoc(orgId);
    if (org) {
      const indexToUpdate = org.connections.dataSources.findIndex(
        (dataSource) => dataSource.keyId === calendarId,
      );
      if (indexToUpdate !== -1) {
        this.myLogger.error({
          message: 'Calendly Event type exists: ' + indexToUpdate
        });
        throw new Error('Duplicate Calendar Id: ' + indexToUpdate);
      }
    }

    await this.integrationService.saveDataSource(orgId, dataSource);
    return { ...dataSource, accountId };
  }
}
