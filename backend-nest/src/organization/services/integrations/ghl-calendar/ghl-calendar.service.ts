import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { GhlApisService } from 'src/api-client/services/ghl-apis/ghl-apis.service';
import { MongoCredentialsService } from 'src/mongo/service/credentials/mongo-credentials.service';
import { OrganizationService } from '../../organization.service';
import { IntegrationService } from '../integration.service';
import { decodeCode } from 'src/lib/utils';
import { CONNECTION_TYPES, KINDS, PROVIDERS } from 'src/lib/constant';
import { Response } from 'express';
import { v4 as uuidv4 } from 'uuid';
import { HttpStatus } from '@nestjs/common/enums';
import { CalendarPreferenceDto } from 'src/organization/dto/requests/post.dto';
import { MigrationService } from 'src/migration/services/migration.service';
import { UserService } from 'src/user/user.service';
import { handleException } from 'helpers/handleException';
import { MyLogger } from 'src/logger/logger.service';

@Injectable()
export class GhlCalendarService {
    constructor(
        private readonly ghlApiService: GhlApisService,
        private readonly organizationService: OrganizationService,
        private readonly mongoCredentialsService: MongoCredentialsService,
        private readonly integrationService: IntegrationService,
        private readonly migrationService: MigrationService,
        private readonly userService: UserService,
        private readonly myLogger: MyLogger
    ) { }

    async connectGhlCalendar(code: string, state: string, res: Response) {
        let message: string;
        try {
            if (!(code && state)) throw new BadRequestException('Invalid Request');
            const { orgId, usid = undefined } = decodeCode(state);
            let { access_token, refresh_token, locationId } = await this.ghlApiService.exchangeGhlCodeForToken(code);
            const tokens = {
                access_token,
                refresh_token
            }
            let accountId: string = uuidv4(), credentialId: string;
            const user = await this.userService.findUserBySessionId(usid);
            const userId = ((user as any)?._id || "").toString();
            if (!userId) throw new BadRequestException('User not found for the session. Please log out and log in again to continue.');

            const organization = await this.organizationService.getOrganizationDoc(orgId);
            if (!organization) {
                throw new NotFoundException('Organization not found');
            }
            // const ghlDatasourceIndex = organization.connections.dataSources.findIndex(channel => channel.keyId === locationId);

            let { location = undefined } = await this.ghlApiService.getGhlLocation(tokens, locationId);

            await this.mongoCredentialsService.updateCredentials({
                query: { keyId: locationId, kind: KINDS.GHL_CREDENTIAL },
                updateBody: {
                    creds: {
                        accessToken: tokens.access_token,
                        refreshToken: tokens.refresh_token,
                    }
                }
            });

            // let existingCred = await this.organizationService.checkChannelUnderOrgs({ locationId });
            // if (existingCred?.id) 

            const calendarConnectionData = {
                providerName: PROVIDERS.GHL_CALENDAR,
                accountId,
                author: location.name, 
                keyId: locationId,
                timezone: location?.timezone,
                userId: userId || "",
                tokens: {
                    accessToken: tokens.access_token,
                    refreshToken: tokens.refresh_token,
                }
            }

            const timezones = await this.ghlApiService.getTimezones(tokens, locationId);
            let { calendars = [] } = await this.ghlApiService.getGhlCalendars(tokens, locationId);
            calendars = calendars.map(calendar => {
                return {
                    id: calendar.id,
                    name: (calendar.name).replace(/[^a-zA-Z0-9 ]/g, ""),
                }
            });
            message = 'Connection Successful! Please select a calendar to continue.';

            res.render('calendar', {
                success: true,
                message, orgId,
                timezones,
                calendars,
                locationId,
                businessName: location?.name,
                accountId,
                credentialId,
                defaultTz: location?.timezone,
                connectionData: calendarConnectionData
            });
        } catch (error) {
            this.myLogger.log({
                message: `@error while connecting Leadconnector calendar :` + error.message
              });
            res.render('calendar', {
                success: false,
                message: error?.message || 'Connection Failed! Please try again.',
                orgId: state,
                timezones: [],
                calendars: [],
                locationId: '',
                credentialId: "",
                accountId: null,
                businessName: "",
                defaultTz: "",
                connectionData: {}
            });
        }
    }

    async setGhlCalendarPreference(body: CalendarPreferenceDto, res: Response) {
        try {
            // if calendar Id already exists in db then 
            const existing = await this.organizationService.getOrganization({
                _id: body.orgId,
                'connections.dataSources.calendarId': body.calendarId,
            });

            if (existing) {
                throw new BadRequestException("Alert: Selected GHL Calendar already exists under your account!");
            } else {
                const newCredential = await this.mongoCredentialsService.createCredentials({
                    kind: KINDS.GHL_CREDENTIAL,
                    keyId: body.connectionData?.keyId,
                    creds: {
                        accessToken: body.connectionData?.tokens?.accessToken,
                        refreshToken: body.connectionData?.tokens?.refreshToken,
                    },
                    organizationId: body.orgId,
                });

                await this.organizationService.updateOrganisation({ _id: body.orgId }, {
                    $push: {
                        'connections.dataSources':
                        {
                            credentialId: newCredential?.id?.toString(),
                            ...body.connectionData,
                            calendarId: body.calendarId,
                            timezone: body.timezone,
                            name: body.calendarName + ' - ' + body.businessName, 
                            reference: `https://link.msgsndr.com/widget/booking/${body.calendarId}`,
                            useContactTz: body.useContactTz || false
                        }
                    }
                });
            }

            return res.status(HttpStatus.OK).json({ message: 'Organization updated successfully' });
        } catch (error) {
            this.myLogger.error({
                message: `error while setting GHL calendar preferences | orgId: ${body.orgId} | locationId: ${body.connectionData?.keyId} | calendarId: ${body.calendarId} :` + error.message,
                context: `CONNECT GHL CALENDAR | set preferences`
              });
            // handleException(res, error);
            return res.status(error?.statusCode || HttpStatus.INTERNAL_SERVER_ERROR).json({ error: error.message });
        }
    }
}
