import { HttpException, Injectable } from '@nestjs/common';
import { PROVIDERS } from 'src/lib/constant';
import { IntegrationService } from '../integration.service';
import { v4 as uuidv4 } from 'uuid';
import { MyLogger } from 'src/logger/logger.service';
import { HttpRequestPostService } from 'src/http-request/POST/post.service';

@Injectable()
export class HttpPostService {
  constructor(
    private readonly integrationService: IntegrationService,
    private readonly myLogger: MyLogger,
    private readonly httpRequestPostService: HttpRequestPostService,
  ) {}

  async saveHttpPostAsDataSource(
    orgId: string,
    userId: string,
    usid: string,
    eventName: string,
    createHttpPostRequestDto,
  ) {
    let accountId = uuidv4();

    //add account id to the request
    createHttpPostRequestDto.accountId = accountId;

    let createPost = await this.httpRequestPostService.create(createHttpPostRequestDto);

    const dataSource = {
      providerName: PROVIDERS.POST,
      accountId,
      keyId: createPost._id.toString(),
      name: eventName,
      userId,
      usid,
      author: createHttpPostRequestDto.author,
    };

    await this.integrationService.saveDataSource(orgId, dataSource);
    return { ...dataSource, ...createHttpPostRequestDto };
  }
} 