import {
  Injectable,
  forwardRef,
  Inject,
  HttpException,
  NotFoundException,
  ForbiddenException,
  BadRequestException,
} from '@nestjs/common';
import { OrganizationService } from '../organization.service';
import { v4 as uuidv4 } from 'uuid';
import { PROVIDERS } from 'src/lib/constant';
import { MongoCredentialsService } from 'src/mongo/service/credentials/mongo-credentials.service';
import { HttpStatus } from '@nestjs/common/enums';
import { Response } from 'express';
import { MongoAgentService } from 'src/mongo/service/agent/mongo-agent.service';
// import { ApiclientService } from 'src/api-client/services/apiclient.service';
import { SheetAuthDto } from 'src/organization/dto/sheet-auth.dto';
import { getGoogleSheet } from 'src/lib/google_funcs';
import {
  GoogleSpreadsheet,
  GoogleSpreadsheetWorksheet,
} from 'google-spreadsheet';
import { isValidHeaderRange, encodeObjectToBase64, decodeBase64ToObject } from 'src/lib/utils';
import { TaskService } from 'src/gcp/services/task.service';
import { EditDatasourceDto } from 'src/agent/dto/edit-action-dto';
import {
  EditDataSourceDto,
  EditGoogleCalendarDto,
} from 'src/organization/dto/requests/post.dto';
import { SheetService } from './sheet/sheet.service';
import { MyLogger } from 'src/logger/logger.service';
import { calendarAvailabilityService } from '../../services/calendar-availablity/calendar-availablity.service';
import { HttpRequestGetService } from 'src/http-request/GET/get.service';

@Injectable()
export class IntegrationService {
  constructor(
    private readonly organizationService: OrganizationService,
    private readonly mongoCredentialService: MongoCredentialsService,
    private readonly mongoAgentService: MongoAgentService,
    private readonly taskService: TaskService,
    private readonly sheetService: SheetService,
    private readonly myLogger: MyLogger,
    private readonly calendarAvailabilityService: calendarAvailabilityService,
    private readonly httpRequestGetService: HttpRequestGetService,
  ) {}

  filterdataSources(organization: any, filterKey: string, filterValue: string) {
    return organization.connections.dataSources.find(
      (channel) => channel[filterKey] === filterValue,
    );
  }

  parseSheetId(url: string) {
    const regex = /\/d\/([a-zA-Z0-9-_]+)\//;
    const match = url.match(regex);
    if (match && match[1]) {
      return match[1];
    } else {
      throw new Error('Invalid Google Sheets URL');
    }
  }

  async saveDataSource(orgId: string, data: Object) {
    const updateRes = await this.organizationService.updateOrganisation(
      { _id: orgId },
      {
        $push: {
          'connections.dataSources': data,
        },
      },
    );
    return;
  }

  async saveStaticData(orgId: string, data: Object) {
    const updateRes = await this.organizationService.updateOrganisation(
      { _id: orgId },
      {
        $push: {
          'connections.staticData': data,
        },
      },
    );
    return;
  }

  async getStaticData(orgId: string, accountId: string) {
    const org = await this.organizationService.getOrganization(
      { _id: orgId },
      { 'connections.staticData': 1 },
    );
    if (!org) {
      throw new NotFoundException('Organization not found');
    }
    const staticData = org.connections.staticData.find(
      (staticData) => staticData.accountId === accountId,
    );
    if (!staticData) {
      throw new NotFoundException('Static data not found');
    }
    return staticData;
  }

  async deleteDataSource(params, res, isAdmin = false, userId?: string) {
    const existingAgent = await this.mongoAgentService.checkDataSources(
      params.orgId,
      params.accountId,
    );
    if (existingAgent.length > 0) {
      const agentNames = existingAgent.map((agent) => agent.agentName).join(', ');
      const prefix = existingAgent.length === 1 ? 'by ' : 'by agents: ';
      return res.status(HttpStatus.CONFLICT).json({
        message: `Cannot delete datasource as it is being used ${prefix} ${agentNames}`,
      });
    }

    const { connections } = await this.organizationService.getOrganization(
      { _id: params.orgId },
      {
        'connections.dataSources': 1,
      },
    );

    if (!connections) {
      throw new Error(`Organization with ID ${params.orgId} not found`);
    }
    if (connections.dataSources.length === 0) {
      throw new Error(`DataSource with ID ${params.accountId} not found`);
    }

    if (connections)
      var dataSource = connections?.dataSources.find(
        (DataSource) => DataSource.accountId === params.accountId,
      );
    if (!dataSource) {
      this.myLogger.log({
        message: `DataSource with ID ${params.accountId} not found`,
      });
    }

    if (!(isAdmin || userId === dataSource.userId)) {
      this.myLogger.log({
        message: {
          userId,
          owner: dataSource.userId,
          isAdmin,
          cond: isAdmin || userId !== dataSource.userId,
        },
      });

      throw new HttpException(
        'You do not have permission to delete this datasource',
        HttpStatus.FORBIDDEN,
      );
    }

    if (dataSource.providerName === PROVIDERS.GOOGLE_SHEET) {
      await this.taskService.createHttpTask(
        'dwy-master',
        'us-central1',
        'v3-migration-tasks',
        `${process.env.BACKEND_URL}/integrations/vector/delete`,
        {
          orgId: params.orgId,
          provider: PROVIDERS.GOOGLE_SHEET,
          sheetId: dataSource.keyId,
          accountId: params.accountId,
          sheetName: dataSource.channeluId,
        },
        2,
      );
    }

    if (dataSource.providerName === PROVIDERS.GOOGLE_CALENDAR) {
      await this.calendarAvailabilityService.delete(
        params.orgId,
        params.accountId,
      );
    }

    if (dataSource.providerName === PROVIDERS.GET) {
      await this.httpRequestGetService.remove(null,params.orgId,params.accountId);
    }

    await this.organizationService.updateOrganisation(
      {
        _id: params.orgId,
        'connections.dataSources.accountId': params.accountId,
      },
      {
        $pull: {
          'connections.dataSources': {
            accountId: params.accountId,
          },
        },
      },
    );

    try {
      await this.mongoCredentialService.deleteCredentials({
        _id: dataSource.credentialId,
      });
    } catch (err) {}
    return res.status(HttpStatus.OK).json({
      message: `Deleted connection`,
      data: { accountId: params.accountId },
    });
  }

  async getDataSource(orgId: string, res: Response) {
    try {
      // if (process.env.NODE_ENV === 'DEVELOPMENT') {
      //     this.apiClientService.rawApiRequest({
      //         method: 'GET',
      //         url: 'https://capri-scraper.onrender.com/',
      //     })
      // }
      const obj = await this.organizationService.getOrganization(
        { _id: orgId },
        { 'connections.dataSources': 1 },
      );
      res
        .status(HttpStatus.OK)
        .json({ id: obj._id, datasources: obj.connections.dataSources });
    } catch (err) {
      this.myLogger.error({
        message: err.message,
      });
      res
        .status(HttpStatus.INTERNAL_SERVER_ERROR)
        .json({ message: err.message });
    }
  }

  async getOrgByCredentialId(credentialId: string) {
    return await this.organizationService.getOrganization({
      'connections.dataSources.credentialId': credentialId,
    });
  }

  async updateDataSource(
    orgId: string,
    accountId: string,
    data?: Object,
    name?: string,
  ) {
    let updateFields = {};

    if (name) {
      updateFields['connections.dataSources.$.name'] = name;
    }

    if (data) {
      updateFields['connections.dataSources.$'] = data;
    }

    // Ensure we have something to update
    if (!Object.keys(updateFields).length) {
      throw new Error('No update data provided');
    }

    const updateRes = await this.organizationService.updateOrganisation(
      {
        _id: orgId,
        'connections.dataSources.accountId': accountId,
      },
      {
        $set: updateFields,
      },
    );

    return updateRes;
  }

  async updateStaticData(orgId: string, accountId: string, name?: string) {
    let updateFields = {};

    if (name) {
      updateFields['connections.staticData.$.name'] = name;
    }

    // Ensure we have something to update
    if (!Object.keys(updateFields).length) {
      throw new Error('No update data provided');
    }

    const updateRes = await this.organizationService.updateOrganisation(
      {
        _id: orgId,
        'connections.staticData.accountId': accountId,
      },
      {
        $set: updateFields,
      },
    );

    return updateRes;
  }

  async connectSheet(body: SheetAuthDto, res?: Response, userId?: string) {
    let doc: GoogleSpreadsheet,
      accountId: string,
      message: string,
      httpcode: number;

    (message = 'Sheet Created'), (httpcode = HttpStatus.CREATED);
    try {
      let { sheetId, orgId, headers = undefined, userName, sheetName } = body;
      if (headers) {
        if (!isValidHeaderRange(headers)) {
          httpcode = HttpStatus.BAD_REQUEST;
          throw new Error('Invalid header range');
        }
      }
      let dataSource,
        link = sheetId;
      let collData;
      let googleSheetId = this.parseSheetId(sheetId);
      const org = await this.organizationService.getOrganizationDoc(orgId);
      if (org) {
        const indexToUpdate = org.connections.dataSources.findIndex(
          (dataSource) =>
            dataSource.keyId === googleSheetId &&
            dataSource.channeluId === sheetName,
        );
        doc = await getGoogleSheet(googleSheetId);
        let sheet: GoogleSpreadsheetWorksheet;
        if (sheetName) {
          sheet = doc.sheetsByTitle[sheetName];
        } else {
          sheet = doc.sheetsByIndex[0];
          sheetName = sheet.title;
        }
        if (!sheet) throw new Error('Sheet title in the document not found');
        if (!doc) throw new Error('No sheet found');

        let collectionLength = await sheet?.rowCount;
        if (indexToUpdate !== -1) {
          // sheet exists
          accountId = org.connections.dataSources[indexToUpdate].accountId;
          dataSource = {};
        } else {
          // sheet does not exist => Create new
          accountId = uuidv4();

          const cred = await this.mongoCredentialService.createCredentials({
            organizationId: orgId,
            keyId: googleSheetId,
            providerName: PROVIDERS.GOOGLE_SHEET,
            name: sheetName,
            creds: {
              secret: googleSheetId,
            },
            kind: 'OpenaiCredential',
          });

          dataSource = {
            credentialId: cred?.id || googleSheetId,
            name: doc.title + ' - ' + sheetName,
            providerName: PROVIDERS.GOOGLE_SHEET,
            headers: headers || 'A1:B1',
            accountId,
            channeluId: sheetName,
            author: userName,
            reference: link,
            userId,
            keyId: googleSheetId,
            data: [],
          };

          collData = dataSource.data;
          await this.saveDataSource(orgId, dataSource);

          message = doc ? 'Sheet connected' : 'Sheet not found';
          httpcode = doc ? HttpStatus.CREATED : HttpStatus.NOT_FOUND;
        }
        const payload = {
          orgId,
          sheetId: googleSheetId,
          headers,
          accountId,
          sheetTitle: sheetName,
        };
        let timeInSeconds = 1;
        await this.taskService.createHttpTask(
          'dwy-master',
          'us-central1',
          'v3-migration-tasks',
          `${process.env.BACKEND_URL}/google-sheet/webhook/embed`,
          payload,
          timeInSeconds,
        );
        message = 'Sheet Created';
        httpcode = HttpStatus.CREATED;
      } else {
        message = 'Organization not found';
        httpcode = HttpStatus.NOT_FOUND;
      }
      if (res)
        res
          .status(httpcode)
          .json({ message, data: { ...dataSource, sheetName } });
      else {
        return dataSource;
      }
    } catch (error) {
      this.myLogger.error({
        message: error.message,
      });
      message = error.message;
      httpcode = HttpStatus.INTERNAL_SERVER_ERROR;
      if (!res) return {};
      else res.status(httpcode).json({ message });
    }
  }

  async editGoogleCalendar(
    orgId: string,
    accountId: string,
    body: EditGoogleCalendarDto,
    userId: string,
  ) {
    try {
      const org = await this.organizationService.getOrganizationDoc(orgId);
      if (!org) {
        throw new NotFoundException('Organization not found');
      }
      const admins = org.admin;
      const isAdmin = admins.includes(userId);
      const calendar = org.availability.find(
        (connection) => connection.accountId === accountId,
      );

      if (!calendar) {
        throw new NotFoundException('Calendar not found');
      }

      if (!isAdmin && calendar.userId !== userId) {
        this.myLogger.log({
          message: {
            userId,
            owner: calendar.userId,
          },
        });
        throw new ForbiddenException(
          'You do not have permission to edit this calendar',
        );
      }

      const updatedCalendar = await this.organizationService.updateOrganisation(
        {
          _id: orgId,
          'availability.accountId': accountId,
        },
        {
          $set: {
            'availability.$.eventName': body.eventName,
            'availability.$.eventDuration': body.eventDuration,
            'availability.$.eventLocation': body.eventLocation,
            'availability.$.locationValue': body.locationValue,
            'availability.$.eventDescription': body.eventDescription,
            'availability.$.dateRange': body.dateRange,
            'availability.$.availableHours': body.availableHours,
            'availability.$.startTimeIncrements': body.startTimeIncrements,
          },
        },
      );

      return updatedCalendar;
    } catch (error) {
      this.myLogger.error({
        message: error.message,
      });
      throw new Error(error.message);
    }
  }

  async editDataSource(
    orgId: string,
    body: EditDataSourceDto,
    provider: string,
    accountId: string,
  ) {
    try {
      let updateBody = {};
      if (body?.name)
        updateBody = { 'connections.dataSources.$.name': body.name };
      if (provider === PROVIDERS.GOOGLE_SHEET && body?.headers) {
        updateBody['connections.dataSources.$.headers'] = body.headers;
      }
      if (body?.timezone && provider === PROVIDERS.GHL_CALENDAR) {
        updateBody['connections.dataSources.$.timezone'] = body.timezone;
      }
      if (
        body?.useContactTz !== undefined &&
        provider === PROVIDERS.GHL_CALENDAR
      ) {
        updateBody['connections.dataSources.$.useContactTz'] =
          body.useContactTz;
      }
      if (updateBody)
        await this.organizationService.updateOrganisation(
          {
            _id: orgId,
            'connections.dataSources.accountId': accountId,
          },
          {
            $set: updateBody,
          },
        );

      if (provider === PROVIDERS.GOOGLE_SHEET) {
        let conn = await this.organizationService.getOrganization(
          { _id: orgId, 'connections.dataSources.accountId': accountId },
          { 'connections.dataSources.$': 1 },
        );
        let sheetData = conn.connections.dataSources[0];
        await this.sheetService.handleSheetData({
          orgId,
          sheetId: sheetData.keyId,
          accountId,
          headers: body.headers,
        });
      }

      return { message: 'Resource updated successfully' };
    } catch (err) {
      this.myLogger.error({
        message: err.message,
      });
      throw new Error(err.message);
    }
  }

  async findGoogleCalendars(orgId: string, accountId: string) {
    const org = await this.organizationService.getOrganizationDoc(orgId);
    if (!org) {
      throw new NotFoundException('Organization not found');
    }
    const calendar = org.availability.find(
      (connection) => connection.accountId === accountId,
    );

    if (!calendar) {
      throw new NotFoundException('Calendar not found');
    }

    return calendar;
  }

  async getTimezones(orgId: string) {
    try {
      const org = await this.organizationService.getOrganizationDoc(orgId);
      if (!org) {
        throw new NotFoundException('Organization not found');
      }

      const timezonesArray = [];

      org.connections.dataSources.forEach((connection) => {
        if (connection.timezone) {
          timezonesArray.push({
            accountId: connection.accountId,
            timezone: connection.timezone,
          });
        }
      });

      return timezonesArray;
    } catch (error) {
      this.myLogger.error({
        message: error.message,
      });
      throw new Error(error.message);
    }
  }
}
