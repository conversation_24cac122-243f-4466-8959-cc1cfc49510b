import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { HttpStatus } from '@nestjs/common/enums';
import { Response } from 'express';
// import { ApiclientService } from 'src/api-client/services/apiclient.service';
import { PROVIDERS } from 'src/lib/constant';
import { getGoogleSheet, getSheetCellVal } from 'src/lib/google_funcs';
import { SheetUpdateWebhookDto, SheetVectorOperationDto } from 'src/organization/dto/requests/post.dto';
import { SheetTaskPayload } from 'src/organization/dto/sheet.dto';
import { PineconeService } from 'src/vector/services/pinecone/pinecone.service';
import { VectorService } from 'src/vector/services/vector.service';
import { OrganizationService } from '../../organization.service';
import { QueryRequest } from '@pinecone-database/pinecone';
import { MongoOrganizationService } from 'src/mongo/service/organization/mongo-organization.service';
import { generateRange, parseSheetId } from 'src/lib/utils';
import { GoogleSpreadsheetWorksheet } from 'google-spreadsheet';
import { TaskService } from 'src/gcp/services/task.service';
import { MongoCredentialsService } from 'src/mongo/service/credentials/mongo-credentials.service';
import { handleException } from 'helpers/handleException';
import { MyLogger } from 'src/logger/logger.service';

@Injectable()
export class SheetService {
    constructor(
        // private readonly apiclientService: ApiclientService,
        private readonly organizationService: OrganizationService,
        private readonly vectorService: VectorService,
        private readonly pineconeService: PineconeService,
        private readonly mongoOrganizationService: MongoOrganizationService,
        private readonly taskService: TaskService,
        private readonly mongoCredentialService: MongoCredentialsService,
        private readonly myLogger: MyLogger
    ) { }

    async handleSheetData(body: SheetVectorOperationDto, res?: Response) {
        try {
            const { orgId, sheetId, headers, accountId } = body;
            const org = await this.organizationService.getOrganization({ _id: orgId});
            if (!org) throw new Error(`No organization ${orgId} found for sheetId: ${sheetId}}`);

            const indexToUpdate = org.connections.dataSources.findIndex(dataSource => dataSource.accountId === accountId);
            if (indexToUpdate === -1) {
                if (res) throw new NotFoundException("No sheet found");
                else throw new Error("No sheet found");
            }

            const sheetConnection = org.connections.dataSources[indexToUpdate];
            let sheetName = sheetConnection.channeluId;

            // get the sheet data
            const sheetDoc = await getGoogleSheet(sheetId);
            let sheet: GoogleSpreadsheetWorksheet;
            if (body.sheetTitle){
                sheet = sheetDoc.sheetsByTitle[(body?.sheetTitle).trim()]; // or use doc.sheetsById[id] or doc.sheetsByTitle[title]
            } else {
                if (sheetName)
                    sheet = sheetDoc.sheetsByTitle[sheetName];
                else {
                    sheet = sheetDoc.sheetsByIndex[0];
                    sheetName = sheet.title;
                }
            }
            if (!sheet) {
                if (res) throw new NotFoundException(`Sheet with name ${sheetName} not found`);
                else throw new Error(`Sheet with name ${sheetName} not found`);
            }
            let [startColumn, endColumn] = headers.replace(/\d+/g, '').split(':');
            let headerRow = parseInt(headers.split(':')[0].replace(/\D+/g, '')) || 1;
            // load rows
            const rows = await sheet.getRows({ offset: headerRow - 1 });
            await sheet.loadHeaderRow(headerRow);
            const columns = sheet.headerValues;
            const sheetIndex = sheet?.index ?? 0;
            if (columns.length == 0) {
                throw new Error("No columns found");
            }

            const [startIdx, endIdx] = [startColumn.charCodeAt(0) - 'A'.charCodeAt(0), endColumn.charCodeAt(0) - 'A'.charCodeAt(0)];

            await sheet.loadCells({
                startRowIndex: 0,
                startColumnIndex: startIdx,
            })

            // const ex = await sheet.getCellByA1(`C10`);
            let batchCount = Math.ceil(rows.length / 50);
            let knowledgeCollection = [];
            // Example : {"batchCount":1,"startIdx":0,"endIdx":1,"columns":["user","assistant"],"headerRow":1,"startColumn":"A","endColumn":"B","length":35}
            for (let b = 0; b < batchCount; b++) {
                let batch = rows.slice(b * 50, (b + 1) * 50);
                let batchCollection = [];
                for (let c = 0; c < batch.length; c++) {
                    let prompt: string = "";
                    let rowData = {};
                    let skip: boolean = false;
                    // create prompt from cells
                    for (let k = startIdx; k <= endIdx; k++) {
                        let column = columns[k];
                        let value = await getSheetCellVal(batch[c], column, sheet, /* columns */);
                        if (value) {
                            prompt += `${column}: ${value}\n`;/// header : value
                        } /* else
                            skip = true; */
                    }
                    // if (skip) continue;

                    rowData['id'] = `${sheetId}:${b * 50 + c}:${sheetIndex}`;
                    rowData['metadata'] = {
                        sheetId,
                        accountId,
                        row: b * 50 + c,
                        type: PROVIDERS.GOOGLE_SHEET,
                        content: ''
                    };

                    if ((prompt || "").trim() !== "") {
                        rowData['metadata']['content'] = prompt;
                        batchCollection.push(rowData);
                    }
                }
                //schedule batch task
                this.myLogger.log({
                    message: { 'batchCollection length': batchCollection.length }
                  });
                if (batchCollection.length > 0) await this.schedule_batch_sheet_task(batchCollection, accountId, sheetId, orgId, b);
            }
            if (knowledgeCollection.length > 0) {
                // convert the batch to embedding
                const pineconeRecords: any = await this.createEmbedding(knowledgeCollection);//SheetEmbeddingDto : {id: string; metaData: any; values: number[];}
                // save the embedding to pinecone
                const namespace = orgId;
                const indexName = await this.pineconeService.createIndex(process.env.PINECONE_INDEX);
                const index = await this.pineconeService.connectIndex(indexName);
                const result = await this.pineconeService.upsertVectors(index, pineconeRecords, namespace);
                this.myLogger.log({
                    message: {result}
                  });
            }
            this.myLogger.log({
                message: '@Sheet data processed successfully'
              });
            // update the sheet record in mongo
            if (res) res.status(HttpStatus.OK).json({ message: 'Sheet data processed successfully' });
        } catch (err) {
            this.myLogger.log({
                message: `@error while handling sheet data: ` + err.message
              });
            if (res) handleException(res, err);
        }
    }

    async processBatchTask(batchData, res: Response) {
        const { sheetId, accountId, orgId, rows, batch } = batchData;
        this.myLogger.log({
            message: `Received batch ${batch + 1} for sheet ${sheetId} with ${rows.length} rows`
          });
        const embeddings = await this.createEmbedding(rows);
        // save the embedding to pinecone
        const namespace = orgId;
        const indexName = await this.pineconeService.createIndex(process.env.PINECONE_INDEX);
        const index = await this.pineconeService.connectIndex(indexName);
        const result = await this.pineconeService.upsertVectors(index, embeddings, namespace);
        this.myLogger.log({
            message: {result}
          });
        // update the sheet record in mongo
    }

    async processRowUpdate(payload: { [key: string]: any }, orgId: string) {
        let rowData = {};
        const sheetId = payload.spreadsheetId;
        const org = await this.mongoOrganizationService.getOrganization({
          _id: orgId,
        });
        if (!(org)) throw new Error("No organization found");

        const updatedRow = payload.rowNumber;
        const endRow = payload.endRow;
        let updateIndex = updatedRow - 1;
        let updateEndIndex = endRow - 1;
        const allRows: [Object] = payload.data;
        let rowsLength = allRows.length;
        let newValue = payload.newValue;
        let sheetName = payload?.sheetName;
        const deletedRow = payload?.deletedRow;

        try {
          let sheetDatasource = (org?.connections?.dataSources ?? []).find(
            (ds) => ds.keyId === sheetId && ds.channeluId === sheetName,
          );
          if (!sheetDatasource) {
            sheetDatasource = (org?.connections?.dataSources ?? []).find(
              (ds) => ds.keyId === sheetId,
            );
          }
          const headers = sheetDatasource?.headers || 'A1:B1';
          const accountId = sheetDatasource?.accountId;
          if (!sheetName) sheetName = sheetDatasource?.channeluId;
          
          // get sheet infos
          let [columnI, columnF] = (headers || '')
            .replace(/\d+/g, '')
            .split(':');
          const [startColumn, endColumn] = [
            columnI.charCodeAt(0) - 'A'.charCodeAt(0),
            columnF.charCodeAt(0) - 'A'.charCodeAt(0),
          ];

          let headerRow =
            parseInt(headers.split(':')[0].replace(/\D+/g, '')) || 1;
          // load rows
          const sheetDoc = await getGoogleSheet(sheetId);
          let sheet: GoogleSpreadsheetWorksheet;
          if (sheetName) {
            sheet = sheetDoc.sheetsByTitle[sheetName];
          } else {
            sheet = sheetDoc.sheetsByIndex[0];
          }
          await sheet.loadHeaderRow(headerRow);
          const sheetIndex = sheet?.index ?? 0;
          const columns = sheet.headerValues;

          if (columns.length == 0) {
            throw new Error('No columns found');
          }

          // let updatedIndex = updateIndex - headerRow;
          // let updatedRowData = allRows[updateIndex];
          let { collectionLength = allRows.length } = JSON.parse(
            sheetDatasource?.data?.[0] || '{}',
          );
          let updateType = await this.determineSheetUpdateType(
            sheetId,
            allRows,
            updateIndex,
            updateEndIndex,
            headerRow,
            startColumn,
            endColumn,
            collectionLength,
            columns,
            newValue,
          );
          if (updateType?.taskType !== 'partial')
            await this.organizationService.updateOrganisation(
              {
                _id: org.id,
                'connections.dataSources.keyId': payload.spreadsheetId,
              },
              {
                'connections.dataSources.$.data': [
                  JSON.stringify({
                    collectionLength: allRows.length - (headerRow - 1),
                  }),
                ],
              },
            );
          // if (updateType?.taskType == 'delete' && updateType?.index.length == 0) {
          //     const indexName = await this.pineconeService.createIndex(process.env.PINECONE_INDEX);
          //     const index = await this.pineconeService.connectIndex(indexName);
          //     await this.pineconeService.deleteSpecificVector([`${payload.spreadsheetId}:${updatedIndex}`], index, org._id.toString());
          //     return;
          // })
          if (updateType?.index.length !== 0) {
            // iterate updateType.collection
            let knowledgeCollection = [];
            for (const index of updateType.index) {
              let prompt: string = '';
              let calcIdx = index - (headerRow - 1);
              for (let k = startColumn; k <= endColumn; k++) {
                let column = columns[k];
                let value = allRows[index][k] || '';
                if (value) {
                  value = `${value}`.replace(/(\r\n|\n|\r)/gm, ' ');
                  if (value) prompt += `${column}: ${value}\n`; /// header : value
                } /* else
                                skip = true; */
              }
              // if (skip) continue;

              rowData['id'] = `${sheetId}:${calcIdx}:${sheetIndex}`;
              rowData['metadata'] = {
                sheetId,
                accountId,
                row: calcIdx,
                type: PROVIDERS.GOOGLE_SHEET,
                content: '',
              };

              if (prompt !== '') {
                rowData['metadata']['content'] = prompt;
                knowledgeCollection.push(rowData);
              }
            }


            this.myLogger.log({
              message: `Updating sheet ${sheetId} with index range ${updateType?.index[0]} to ${updateType?.index?.[updateType?.index?.length - 1]}`,
            })

            //TODO: introduce tasks
            knowledgeCollection = await this.createEmbedding(
              knowledgeCollection,
            ); //SheetEmbeddingDto : {id: string; metaData: any; values: number[];}
            // save the embedding to pinecone
            const namespace = org._id.toString();
            const indexName = await this.pineconeService.createIndex(
              process.env.PINECONE_INDEX,
            );
            const index = await this.pineconeService.connectIndex(indexName);
            const result = await this.pineconeService.upsertVectors(
              index,
              knowledgeCollection,
              namespace,
            );
            this.myLogger.log({
              message: `Sheet update webhook result :` + JSON.stringify(result),
              context: 'sheet_update_webhook',
            });
          }
        } catch (err) {
          this.myLogger.error({
            message:
              `@error while handling sheet data for orgId ${org?.id || ''}: ` +
              err.message,
          });
        }
    }

    async retrieveSheetData(orgId: string, sheetId: string, accountId?: string, sheetName?: string) {
        const sheetDoc = await getGoogleSheet(sheetId);
        let sheet: GoogleSpreadsheetWorksheet; // or use doc.sheetsById[id] or doc.sheetsByTitle[title]
        if (sheetName) {
            sheet = sheetDoc.sheetsByTitle[sheetName];
        } else {
            throw new Error("Sheet name not provided");
        }
        const rows = await sheet.getRows();
        const length = rows.length || 10;
        // create ids array such that each id = sheetId:rowNumber
        const ids = Array.from({ length }, (_, i) => `${sheetId}:${i}`);
        let records = await this.pineconeService.fetchVectors(process.env.PINECONE_INDEX, orgId, ids);
        let type = (records['vectors'][ids[0]] || {})?.metadata['type'];
        let sheeId = (records['vectors'][ids[0]] || {})?.metadata['sheetId'];
        let chunks = Object.keys(records.vectors || []).map((key) => {
            return {
                id: records['vectors'][key].id
                , metadata: {
                    content: records['vectors'][key]?.metadata['content'],
                    row: records['vectors'][key]?.metadata['row'],
                }
            }
        });
        return { chunks, accountId, type, sheeId };
    }

    async querySheetVectors(orgId: string, sheetId: string, content: string) {
        const openaiApiKey = process.env.OPENAI_API_KEY;
       let embeddingProvider = PROVIDERS.OPENAI_PROVIDER;
       const embeddings = await this.vectorService.createEmbedding(
         embeddingProvider,
         openaiApiKey,
         [content],
       );
        this.myLogger.log({
            message: { embeddings }
          });
        const queryRequest: QueryRequest = {
            topK: 10,
            includeMetadata: true,
            includeValues: false,
            namespace: orgId,
            vector: embeddings[0].embedding,
            filter: {
                type: PROVIDERS.GOOGLE_SHEET,
                sheetId: sheetId
            }
        }
        const indexName = await this.pineconeService.createIndex(process.env.PINECONE_INDEX);
        const index = await this.pineconeService.connectIndex(indexName);
        const res = await this.pineconeService.query(index, queryRequest);
        return res;
    }

    // ----------------- Helper Functions ----------------- //

    async createEmbedding(knowledgeCollection: Object[]): Promise<Object[]> {
        //create array of just the prompts
        let prompts = knowledgeCollection.map((item) => item['metadata']['content']);
        const openaiApiKey = process.env.OPENAI_API_KEY;
         let embeddingProvider = PROVIDERS.OPENAI_PROVIDER;
         let embeddings = await this.vectorService.createEmbedding(
           embeddingProvider,
           openaiApiKey,
           prompts,
         );
        //add the embeddings to the knowledge collection
        for (let e = 0; e < embeddings.length; e++) {
            let index = embeddings[e].index;
            let embedding = embeddings[e].embedding;
            knowledgeCollection[index]['values'] = embedding;
        }
        return knowledgeCollection;
    }

    async determineSheetUpdateType(sheetId: string, allRows, updateIndex: number, updateEndIndex: number, headerRow, startColumn: number, endColumn: number, collectionLength, columns: string[], newValue) {
        if (updateIndex > allRows.length) {
            return {
                'taskType': 'DELETE',
                'index': []//generateRange(updateIndex, updateEndIndex),
            }
        }
        if (newValue) {
            return {
                'taskType': 'SINGLE_ROW',
                'index': [updateIndex],
            }
        } else {
            if (updateEndIndex == updateIndex) {
                return {
                    'taskType': 'partial',
                    'index': [],
                }
            }
            return {
                'taskType': 'MULTIPLE_ROW',
                'index': generateRange(updateIndex, allRows.length - 1),
            }
        }
    }

    async schedule_batch_sheet_task(collection, accountId, sheetId, orgId, batchIndex = 0) {
        try {
            const queue = "v3-migration-tasks";
            const project = "dwy-master";
            const location = "us-central1";
            const payload = {
                sheetId,
                accountId,
                'rows': collection,
                'batch': batchIndex,
                'orgId': orgId,
            }
            let timeInSeconds = 1 + batchIndex;
            const response = await this.taskService.createHttpTask(project, location, queue, `${process.env.BACKEND_URL}/google-sheet/batch`, payload, timeInSeconds);
            return {
                'taskId': response.name,
                'accountId': accountId,
            }
        } catch (err) {
            this.myLogger.error({
                message: 'Error scheduling vector database refresh task for sheet. Error message: ' +  err.message
              }); 
        }
    }

    async getSheetTitles(sheetId: string, orgId: string, res?: Response) {
        try {
            sheetId = parseSheetId(sheetId);
            const sheetDoc = await getGoogleSheet(sheetId);
            const sheetCount = sheetDoc.sheetCount;
            const titles: string[] = []
            let sheet: GoogleSpreadsheetWorksheet;
            for (let index = 0; index < sheetCount; index++) {
                sheet = sheetDoc.sheetsByIndex[index];
                titles.push(sheet.title);
            }
            let org: any = await this.mongoOrganizationService.getOrganization({
                _id: orgId,
                'connections.dataSources.keyId': sheetId
            }, {
                'connections.dataSources': 1,
            });

            let titlesInOrg = (org?.connections?.dataSources || []).filter(dataSource => {
                if (dataSource?.keyId === sheetId) return dataSource?.channeluId;
            });
            titlesInOrg = titlesInOrg.map(
              (dataSource) => dataSource?.channeluId,
            );
            if (res) {
                res.status(HttpStatus.ACCEPTED).send({ titles: titles });
            } else return titles;
        } catch (err) {
            this.myLogger.error({
                message: `@error in getting sheet titles :` + err?.message
              });
            if (res) {
                handleException(res, err)
            }
        }
    }

    async deleteSheetVectors(orgId: string, sheetId: string, accountId: string, sheetName?: string) {
        try {
            const sheetDoc = await getGoogleSheet(sheetId);
            let sheet: GoogleSpreadsheetWorksheet; // or use doc.sheetsById[id] or doc.sheetsByTitle[title]
            if (sheetName) {
                sheet = sheetDoc.sheetsByTitle[sheetName];
            } else {
                sheet = sheetDoc.sheetsByIndex[0];
            }
            const rows = await sheet.getRows();
            const length = rows.length || 10;
            // create ids array such that each id = sheetId:rowNumber
            const ids = Array.from({ length }, (_, i) => `${sheetId}:${i}`);
            const indexName = await this.pineconeService.createIndex(process.env.PINECONE_INDEX);
            const index = await this.pineconeService.connectIndex(indexName);
            await this.pineconeService.deleteSpecificVector(ids, index, orgId);
            return { message: `Successfully deleted ${length} vectors` };
        } catch (err) {
            this.myLogger.error({
                message: `@error in deleting sheet vectors :` + err?.message
              });
            throw new Error(err?.message);
        }
    }

    async refreshConnection(orgId: string, accountId: string) {
        const org = await this.mongoOrganizationService.getOrganization({
            _id: orgId,
        });

        if (!org) {
            throw new NotFoundException('Organization not found');
        }
        const orgConnections = org.connections.dataSources;
        const connection = orgConnections.find((connection) => connection.accountId === accountId);
        if (!connection) {
            this.myLogger.log({
                message: 'Connection not found'
            });
        } else {
            await this.handleSheetData({
                    orgId,
                    sheetId: connection.keyId,
                    headers: connection.headers,
                    sheetTitle: connection.channeluId,
                    accountId: connection.accountId,
                })
        }
    }
}
