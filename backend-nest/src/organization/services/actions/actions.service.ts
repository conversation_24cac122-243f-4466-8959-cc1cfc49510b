import { Injectable } from '@nestjs/common';
import { IghlSendMessage } from 'src/api-client/dto/ghl.dto';
import { GhlApisService } from 'src/api-client/services/ghl-apis/ghl-apis.service';
import { sendTwilioMessage } from 'src/api-client/services/twilio/twilio-api.service';
import { GhlToken } from 'src/lib/global-interfaces';
import { MyLogger } from 'src/logger/logger.service';
import { MongoVoiceNumbersService } from 'src/mongo/service/voiceNumbers/voiceNumbers.service';

@Injectable()
export class ActionsService {
    constructor(
        private readonly mongoVoiceNumbersService: MongoVoiceNumbersService,
        private readonly logger: MyLogger,
        private readonly ghlApiService: GhlApisService,
    ) {}

    async sendSMSAction(tokens: GhlToken, locationId: string, phoneId: string, contactId: string, message: string) {
        const number = await this.mongoVoiceNumbersService.getPhoneById(
          phoneId,
        );

        const c = await this.ghlApiService.getContact(
            tokens,
            locationId,
            contactId,
          );

        const to = c?.contact?.phone;
        
        if (!number) throw new Error('User phone number not found for phoneId: ' + phoneId);
        if (!to){
            this.logger.error({
                message: `Phone SMS | Phone number for Contact ${contactId} not found`,
                context: 'SMS ACTION',
            });
            return;
        }
        
        const creds = number?.creds;
        const result = await sendTwilioMessage({
            accountSid: creds.twilioAccountSid,
            authToken: creds.twilioAuthToken,
            message: {
                from: number.phoneNumber, to: to, body: message
            }
        })
        
        this.logger.log({
            message: `Phone SMS | Sent SMS to ${contactId} with message ${message} - sid: ${result.sid}`,
            context: 'SMS ACTION',
        })

        return result;
    }

    async sendGHLEmailAction(tokens: GhlToken, locationId: string, params: IghlSendMessage){
        const { contactId, html, subject } = params;

        const result = await this.ghlApiService.sendMessage(tokens, locationId, {
            type: 'Email',
            contactId,
            html,
            subject,
            attachments: []
        });

        this.logger.log({
            message: `GHL Email action | Sent Email to contactId : ${contactId} - emailMessageId : ${result?.emailMessageId}`,
            context: 'GHL EMAIL ACTIION',
        })
    }
}
