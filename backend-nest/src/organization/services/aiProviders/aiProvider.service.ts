import { Injectable, HttpStatus, BadRequestException } from '@nestjs/common';
import { Response } from 'express';
import { handleException } from 'helpers/handleException';
import { KINDS, PROVIDERS } from 'src/lib/constant';
import { CreateCredentialDto } from 'src/mongo/dto/credentials/create.dto';
import { MongoAgentService } from 'src/mongo/service/agent/mongo-agent.service';
import { MongoCredentialsService } from 'src/mongo/service/credentials/mongo-credentials.service';
import { MongoOrganizationService } from 'src/mongo/service/organization/mongo-organization.service';
import { MongoUserService } from 'src/mongo/service/user/mongo-user.service';
import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class AiProviderService {
  constructor(
    private readonly mongoOrganizationService: MongoOrganizationService,
    private readonly mongoCredentialsService: MongoCredentialsService,
    private readonly mongoUserService: MongoUserService,
    private readonly mongoAgentService: MongoAgentService,
  ) {}

  async getAiProviders(orgId: string, response: Response) {
    try {
      const org = await this.mongoOrganizationService.getOrganization({
        _id: orgId,
      });

      const accounts = org.connections.aiProvider.reduce((acc, curr) => {
        let key = curr.providerName;
        if (!acc[key]) {
          acc[key] = {
            companyId: key,
            accounts: [],
          };
        }
        const { credentialId, providerName, ...rest } = curr;
        acc[key].accounts.push(rest);
        return acc;
      }, {});

      return response.status(HttpStatus.OK).json({
        aiProviderAccounts: Object.values(accounts),
      });
    } catch (error) {
      return response
        .status(HttpStatus.INTERNAL_SERVER_ERROR)
        .json('Internal server error');
    }
  }
  async createAiProvider(createCredentialDto: any, response?: Response) {
    try {
      let credential: CreateCredentialDto;

      if (createCredentialDto.kind == KINDS.OPENAI_CREDENTIAL) {
        credential = {
          kind: KINDS.OPENAI_CREDENTIAL,
          organizationId: createCredentialDto.organizationId,
          type: KINDS.OPENAI_CREDENTIAL,
          ...createCredentialDto,
        };
      } else if (createCredentialDto.kind === KINDS.CLAUDE_CREDENTIAL) {
        credential = {
          kind: KINDS.CLAUDE_CREDENTIAL,
          organizationId: createCredentialDto.organizationId,
          type: KINDS.CLAUDE_CREDENTIAL,
          ...createCredentialDto,
        };
      } else if (createCredentialDto.kind === KINDS.GROQ_CREDENTIAL) {
        credential = {
          kind: KINDS.GROQ_CREDENTIAL,
          organizationId: createCredentialDto.organizationId,
          type: KINDS.GROQ_CREDENTIAL,
          ...createCredentialDto,
        };
      } else if (createCredentialDto.kind === KINDS.FIREWORKS_CREDENTIAL) {
        credential = {
          kind: KINDS.FIREWORKS_CREDENTIAL,
          organizationId: createCredentialDto.organizationId,
          type: KINDS.FIREWORKS_CREDENTIAL,
          ...createCredentialDto,
        };
      } else if (createCredentialDto.kind === KINDS.GEMINI_CREDENTIAL) {
        credential = {
          kind: KINDS.GEMINI_CREDENTIAL,
          organizationId: createCredentialDto.organizationId,
          type: KINDS.GEMINI_CREDENTIAL,
          ...createCredentialDto,
        };
      }
      if (
        credential?.creds?.secret === '' ||
        credential?.creds?.secret === undefined
      ) {
        throw new BadRequestException('Missing secret key');
      }

      const createdCredential =
        await this.mongoCredentialsService.createCredentials(credential);

      const { _id } = createdCredential;
      const { kind, organizationId, userId, name } = createCredentialDto;
      let providerName;
      if (createCredentialDto.kind === KINDS.OPENAI_CREDENTIAL) {
        providerName = PROVIDERS.OPENAI_PROVIDER;
      } else if (createCredentialDto.kind === KINDS.CLAUDE_CREDENTIAL) {
        providerName = PROVIDERS.CLAUDE_PROVIDER;
      } else if (createCredentialDto.kind === KINDS.GROQ_CREDENTIAL) {
        providerName = PROVIDERS.GROQ_PROVIDER;
      } else if (createCredentialDto.kind === KINDS.FIREWORKS_CREDENTIAL) {
        providerName = PROVIDERS.FIREWORKS_PROVIDER;
      } else if (createCredentialDto.kind === KINDS.GEMINI_CREDENTIAL) {
        providerName = PROVIDERS.GOOGLE_GEMINI;
      }

      //Add the credential id to the corresponding kind in the organization document
      const accountId: string = await uuidv4();

      const organization =
        await this.mongoOrganizationService.updateOrganization(
          { _id: organizationId },
          {
            $push: {
              'connections.aiProvider': {
                accountId,
                name,
                providerName,
                userId,
                credentialId: _id.toString(),
              },
            },
          },
        );

      let user;
      //Add the credntial id to the userId
      if (userId) {
        user = await this.mongoUserService.updateUser(
          { _id: userId },
          { $push: { credentials: { kind, credentialId: _id.toString() } } },
        );
      }
      const returnedData = {
        accountId,
        name,
        providerName,
        credentialId: _id.toString(),
      };
      if (response) {
        return response.status(HttpStatus.CREATED).json(returnedData);
      } else {
        return returnedData;
      }
    } catch (error) {
      if (response) {
        handleException(response, error);
      } else {
        return { accountId: '', name: '', providerName: '', credentialId: '' };
      }
    }
  }

  async updateAiProvider(
    orgId: string,
    accountId: string,
    body: any,
    response: Response,
    userId?: string,
    isAdmin = false,
  ) {
    try {
      const aiProvider = await this.mongoOrganizationService.getOrganization(
        { _id: orgId },
        { 'connections.aiProvider': 1 },
      );
      const aiProviderIndex = aiProvider.connections.aiProvider.findIndex(
        (ai) => ai.accountId === accountId,
      );
      if (aiProviderIndex === -1) {
        return response
          .status(HttpStatus.NOT_FOUND)
          .json({ message: 'No records found' });
      }

      if (
        !(
          isAdmin ||
          userId == aiProvider.connections.aiProvider[aiProviderIndex].userId
        )
      ) {
        return response.status(HttpStatus.UNAUTHORIZED).json({
          message: 'You are not authorized to update this AI provider',
        });
      }

      const agents = await this.mongoAgentService.getAgents(
        {
          $or: [
            { 'aiProvider.accountId': accountId },
            { 'actions.advancedSettings.aiProvider.accountId': accountId },
          ],
        },
        { _id: 1, agentName: 1 },
      );


      const organization =
        await this.mongoOrganizationService.updateOrganization(
          {
            _id: orgId,
            'connections.aiProvider.accountId': accountId,
          },
          {
            $set: {
              'connections.aiProvider.$': {
                accountId,
                ...body,
              },
            },
          },
          { new: true },
        );

      if (organization) {
        return response
          .status(HttpStatus.ACCEPTED)
          .json({ message: 'Successfully updated' });
      } else {
        return response
          .status(HttpStatus.NOT_FOUND)
          .json({ message: 'No records found' });
      }
    } catch (error) {
      return response
        .status(HttpStatus.INTERNAL_SERVER_ERROR)
        .json({ message: 'Internal server error' });
    }
  }

  async deleteAiAccount(
    orgId: string,
    accountId: string,
    response: Response,
    userId?: string,
    isAdmin = false,
  ) {
    try {
      const aiProvider = await this.mongoOrganizationService.getOrganization(
        { _id: orgId },
        { 'connections.aiProvider': 1 },
      );
      const aiProviderIndex = aiProvider.connections.aiProvider.findIndex(
        (ai) => ai.accountId === accountId,
      );
      if (aiProviderIndex === -1) {
        return response
          .status(HttpStatus.NOT_FOUND)
          .json({ message: 'No records found' });
      }

      if (
        !(
          isAdmin ||
          userId == aiProvider.connections.aiProvider[aiProviderIndex].userId
        )
      ) {
        return response.status(HttpStatus.UNAUTHORIZED).json({
          message: 'You are not authorized to delete this AI provider',
        });
      }

      // Check if any agent is using this provider
      const agents = await this.mongoAgentService.getAgents(
        {
          $or: [
            { 'aiProvider.accountId': accountId },
            { 'actions.advancedSettings.aiProvider.accountId': accountId },
          ],
        },
      );

       let providerName =
           aiProvider?.connections?.aiProvider?.[aiProviderIndex]?.providerName,
         kindName = '';
         
      if (agents.length > 0) {
          for (const agent of agents) {
            const updateData: any = {
              disabled: true,
              aiProvider: {
                accountId: '',
                accountName: '',
                companyId: '',
                modelName: '',
                isAdvancedSettings: false,
              },
            };

            if (agent.actions) {
              updateData.actions = agent.actions.map((action: any) => {
                if (
                  action.advancedSettings?.aiProvider?.accountId === accountId
                ) {
                  action.advancedSettings.aiProvider = {
                    accountId: "",
                    companyId: "",
                    modelName: "",
                    accountName: "",
                  };
                  action.isAdvancedSettings = false;
                }
                return action;
              });
            }

            await this.mongoAgentService.updateAgent(
              { _id: agent._id },
              { $set: updateData },
            );
        }
      }
     
      if (providerName === PROVIDERS.OPENAI_PROVIDER) {
        kindName = KINDS.OPENAI_CREDENTIAL;
      } else if (providerName === PROVIDERS.CLAUDE_PROVIDER) {
        kindName = KINDS.CLAUDE_CREDENTIAL;
      } else if (providerName === PROVIDERS.GROQ_PROVIDER) {
        kindName = KINDS.GROQ_CREDENTIAL;
      } else if (providerName === PROVIDERS.FIREWORKS_PROVIDER) {
        kindName = KINDS.FIREWORKS_CREDENTIAL;
      } else if (providerName === PROVIDERS.GOOGLE_GEMINI) {
        kindName = KINDS.GOOGLE_CREDENTIAL;
      }

      const deletedCredential =
        await this.mongoCredentialsService.deleteCredential({
          _id: aiProvider.connections.aiProvider[aiProviderIndex].credentialId,
          organizationId: orgId,
          kind: kindName,
        });

      const organization =
        await this.mongoOrganizationService.updateOrganization(
          { _id: orgId, 'connections.aiProvider.accountId': accountId },
          { $pull: { 'connections.aiProvider': { accountId: accountId } } },
          { new: true },
        );


      if (organization) {
        return response
          .status(HttpStatus.ACCEPTED)
          .json({ message: 'Successfully removed' });
      } else {
        return response
          .status(HttpStatus.NOT_FOUND)
          .json({ message: 'No records found' });
      }
    } catch (error) {
      return response
        .status(HttpStatus.INTERNAL_SERVER_ERROR)
        .json({ message: 'Internal server error' });
    }
  }

  async checkAiAccount(
    orgId: string,
    accountId: string,
    response: Response,
    userId?: string,
    isAdmin = false,
  ) {
    try {
      const aiProvider = await this.mongoOrganizationService.getOrganization(
        { _id: orgId },
        { 'connections.aiProvider': 1 },
      );
      const aiProviderIndex = aiProvider.connections.aiProvider.findIndex(
        (ai) => ai.accountId === accountId,
      );
      if (aiProviderIndex === -1) {
        return response
          .status(HttpStatus.NOT_FOUND)
          .json({ message: 'No records found' });
      }

      if (
        !(
          isAdmin ||
          userId == aiProvider.connections.aiProvider[aiProviderIndex].userId
        )
      ) {
        return response.status(HttpStatus.UNAUTHORIZED).json({
          message: 'You are not authorized to delete this AI provider',
        });
      }

      //find if any agent is using this provider
      // Check if any agent is using this provider
      const agents = await this.mongoAgentService.getAgents(
        {
          $or: [
            { 'aiProvider.accountId': accountId },
            { 'actions.advancedSettings.aiProvider.accountId': accountId },
          ],
        },
        { _id: 1, agentName: 1 },
      );

      if (agents.length > 0) {
        const agentNames = agents.map((agent) => agent.agentName).join(', ');
        return response.status(HttpStatus.OK).json({
          safeToDelete: false,
          message: `This AI provider is currently being used by the following agent(s): ${agentNames}`,
        });
      }else{
        return response.status(HttpStatus.OK).json({
          safeToDelete: true,
          message: 'This AI provider is not being used by any agent',
        });
      }
    } catch (error) {
      return response
        .status(HttpStatus.INTERNAL_SERVER_ERROR)
        .json({ message: 'Internal server error' });
    }
  }

  async getAllAiProviders(orgId: string) {
    const org = await this.mongoOrganizationService.getOrganization(
      { _id: orgId },
      { 'connections.aiProvider': 1 },
    );
    if (!org)
      throw new Error(
        JSON.stringify({
          message: 'Organization not found',
          code: HttpStatus.NOT_FOUND,
        }),
      );
    return org?.connections?.aiProvider;
  }

  async getAiProvider(orgId: string, accountId: string) {
    const org = await this.mongoOrganizationService.getOrganization(
      { _id: orgId, 'connections.aiProvider.accountId': accountId },
      { 'connections.aiProvider.$': 1 },
    );
    if (!org) throw new BadRequestException('Organization not found');
    return org?.connections?.aiProvider[0];
  }
}
