// Cost variables (in cents)
const BASE_COST_PER_MINUTE = 20;
const GPT_4O_COST_PER_1K_TOKENS = 5;
const GPT_4O_MINI_COST_PER_1K_TOKENS = 2;
const GROQ_COST_PER_1K_TOKENS = 2;
const COST_PER_ACTION = 10;
const KNOWLEDGE_SOURCE_COST = 5;
const COSTS_PER_MINUTE = true;

/**
 * Calculates the cost of voice usage based on various metrics.
 *
 * @param {Object} metrics - The metrics object containing usage details.
 * @param {string} metrics.voiceProvider - The voice provider (currently only 'vapi' is supported).
 * @param {string} metrics.model - The model used for processing (e.g., 'gpt-4o', 'gpt-4o-mini').
 * @param {string} metrics.provider - The provider of the service.
 * @param {number} metrics.numOfActions - The number of actions performed.
 * @param {number} metrics.numOfSeconds - The duration of voice usage in seconds.
 * @param {number} metrics.numOfTokensUsed - The number of tokens used during processing.
 * @param {number} metrics.numOfKnowledgeSource - The number of knowledge sources used.
 *
 * @returns {number} The calculated cost in cents, rounded to 2 decimal places.
 *
 * @description
 * This function calculates the total cost of voice usage based on several factors:
 * - Base cost per 1000 tokens
 * - Additional cost based on the specific model used (GPT-4O or GPT-4O-Mini)
 * - Cost per action
 * - Cost for using knowledge sources
 *
 * The calculation takes into account whether costs are applied per minute or for the entire duration.
 * If costs are per minute (COSTS_PER_MINUTE = true), the total is multiplied by the number of minutes used.
 *
 * @example
 * const metrics = {
 *   voiceProvider: 'vapi',
 *   model: 'gpt-4o',
 *   provider: 'openai',
 *   numOfActions: 5,
 *   numOfSeconds: 300,
 *   numOfTokensUsed: 1000,
 *   numOfKnowledgeSource: 1
 * };
 * const cost = getVoiceCost(metrics);
 *  // Outputs the calculated cost in cents as a whole number
 */
function getVoiceCost(metrics: {
  voiceProvider: 'vapi';
  model: string;
  provider: string;
  numOfActions: number;
  numOfSeconds: number;
  numOfTokensUsed: number;
  numOfKnowledgeSource: number;
}): number {
  const {
    model,
    numOfActions,
    numOfSeconds,
    numOfTokensUsed,
    numOfKnowledgeSource,
    provider,
  } = metrics;

  // Convert seconds to minutes
  const numOfMinutes = numOfSeconds / 60;

  /* ( base(0.20) + (number of 1k tokens) * provider cost + no. of action * 0.10 + no.of knowledge * 0.05 ) * number of minutes */

  // Calculate base cost
  let totalCost = BASE_COST_PER_MINUTE;

  // Add model-specific cost
  if (model.toLowerCase() === 'gpt-4o-mini') {
    totalCost += (GPT_4O_MINI_COST_PER_1K_TOKENS * numOfTokensUsed) / 1000;
  } else if (model.toLowerCase() === 'gpt-4o') {
    totalCost += (GPT_4O_COST_PER_1K_TOKENS * numOfTokensUsed) / 1000;
  } else if (provider === 'groq') {
    totalCost += (GROQ_COST_PER_1K_TOKENS * numOfTokensUsed) / 1000;
  }

  // Add cost for actions
  totalCost += COST_PER_ACTION * numOfActions;

  // Add cost for knowledge source (if any)
  if (numOfKnowledgeSource > 0) {
    totalCost += KNOWLEDGE_SOURCE_COST;
  }

  // If costs are per minute, multiply by the number of minutes
  if (COSTS_PER_MINUTE) {
    totalCost *= numOfMinutes;
  }

  // Round to 2 decimal places
  return Math.round(totalCost);
}

export default getVoiceCost;
