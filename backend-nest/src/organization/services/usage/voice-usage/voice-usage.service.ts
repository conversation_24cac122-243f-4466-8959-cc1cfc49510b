import { Injectable, NotFoundException } from '@nestjs/common';
import { Types } from 'mongoose';
import { VoiceUsage } from 'src/mongo/schemas/voiceUsage/voice-usage.schema';
import { MongoAgentService } from 'src/mongo/service/agent/mongo-agent.service';
import { MongoOrganizationService } from 'src/mongo/service/organization/mongo-organization.service';
import { MongoVoiceUsageService } from 'src/mongo/service/voiceUsage/voice-usage.service';
import getVoiceCost from './utils';
import { vapiApiService } from 'src/api-client/services/vapi/vapi-api.service';
import { numberOfTokens } from 'src/lib/utils';

@Injectable()
export class VoiceUsageService {
  constructor(
    private readonly mongoAgentService: MongoAgentService,
    private readonly mongoOrganizationService: MongoOrganizationService,
    private readonly mongoVoiceUsageService: MongoVoiceUsageService,
    private readonly vapiApiService: vapiApiService,
  ) {}

  async addUsage(
    agentId: string,
    usage: {
      usageSeconds: number;
    },
    callId?: string,
  ): Promise<VoiceUsage> {
    const { usageSeconds } = usage;
    const agent = await this.mongoAgentService.getAgent({ _id: agentId });
    if (!agent) {
      throw new NotFoundException(`Agent with ID ${agentId} does not exist`);
    }
    const { voiceConfig, actions, orgId } = agent;
    const { assistantId } = voiceConfig;
    const assistant = await this.vapiApiService.getAssistant(
      process.env.VAPI_TOKEN,
      { assistantId },
    )

    const vapiSystemPrompt = ((assistant?.model?.messages ?? []).find(m => m.role === 'system')).content;
    const systemTokens = await numberOfTokens(vapiSystemPrompt);
    const systemTokensCount = systemTokens.length;
    
    const voiceMetrics = {
      voiceProvider: voiceConfig.provider,
      model: voiceConfig.model.model,
      provider: voiceConfig.model.provider,
      numOfActions: (voiceConfig?.tools ?? []).map((t) => t.active).length,
      numOfSeconds: usageSeconds,
      numOfTokensUsed: systemTokensCount,
      numOfKnowledgeSource: (voiceConfig?.files ?? []).length,
    };
    const cost = getVoiceCost(voiceMetrics); //cents
    const cost_in_dollars = cost / 100;
    const organization = await this.mongoOrganizationService.updateOrganization(
      {
        _id: orgId,
      },
      {
        $inc: {
          'billing.addonAmount': -1 * cost_in_dollars,
        },
      },
    );
    if (!organization) {
      throw new NotFoundException(
        `Organization with ID ${orgId} does not exist`,
      );
    }
    await this.mongoOrganizationService.updateVoiceUsage(orgId, {
      cost: cost_in_dollars,
    });
    return await this.mongoVoiceUsageService.create({
      agent: new Types.ObjectId(agentId),
      organization: new Types.ObjectId(orgId),
      ...voiceMetrics,
      cost,
      callId,
    });
  }

  async predictVoiceUsageCost(
    agentId: string,
  ): Promise<{cost: number; unit: string}> {
    const usageSeconds = 60;
    const agent = await this.mongoAgentService.getAgent({ _id: agentId });
    if (!agent) {
      throw new NotFoundException(`Agent with ID ${agentId} does not exist`);
    }
    const { voiceConfig, actions, orgId } = agent;
    const { assistantId } = voiceConfig;
    const assistant = await this.vapiApiService.getAssistant(
      process.env.VAPI_TOKEN,
      { assistantId },
    )

    const vapiSystemPrompt = ((assistant?.model?.messages ?? []).find(m => m.role === 'system')).content;
    const systemTokens = await numberOfTokens(vapiSystemPrompt);
    const systemTokensCount = systemTokens.length;
    const voiceMetrics = {
      voiceProvider: voiceConfig.provider,
      model: voiceConfig.model.model,
      provider: voiceConfig.model.provider,
      numOfActions: (voiceConfig?.tools ?? []).map((t) => t.active).length,
      numOfSeconds: usageSeconds,
      numOfTokensUsed: systemTokensCount,
      numOfKnowledgeSource: (voiceConfig?.files ?? []).length,
    };
    const cost = getVoiceCost(voiceMetrics); //cents
    const cost_in_dollars = cost / 100;
    return {cost: cost_in_dollars, unit: 'min'};
  }
}
