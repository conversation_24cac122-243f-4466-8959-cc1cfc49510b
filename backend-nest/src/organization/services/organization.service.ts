import {
  HttpException,
  HttpStatus,
  Injectable,
  Res,
  RawBodyRequest,
  NotFoundException,
  ForbiddenException,
  BadRequestException,
  InternalServerErrorException,
} from '@nestjs/common';
import { Response } from 'express';
import { CONNECTION_TYPES, CUSTOM_LLM_6, CUSTOM_LLM_7, CUSTOM_LLM_8, KINDS } from 'src/lib/constant';
import { CreateOrganizationDto, CreateOrganizationUphexDto } from 'src/mongo/dto/organizations/createOrg.dto';
import { MongoCredentialsService } from 'src/mongo/service/credentials/mongo-credentials.service';
import { MongoOrganizationService } from 'src/mongo/service/organization/mongo-organization.service';
import { MongoUserService } from 'src/mongo/service/user/mongo-user.service';
import { AddNewMemberDto } from '../dto/requests/addNewMemberDto';
import { v4 as uuidv4 } from 'uuid';
import { BillingService } from 'src/billing/billing.service';
import { ChoosePlanBasicDto } from 'src/billing/dto/ChoosePlanDto';
import { PLANS, TPlanName, planMap } from 'src/billing/billing.map';
import { MongoWaitListService } from 'src/mongo/service/waitlist/mongo-waitlist.service';
import { MongoAgentService } from 'src/mongo/service/agent/mongo-agent.service';
import { MyLogger } from 'src/logger/logger.service';
import { FoldersService } from 'src/folders/folders.service';
import { UserService } from 'src/user/user.service';
import axios from 'axios';
import { OrganizationDocument } from 'src/mongo/schemas/organization/organization.schema';
import { ExternalApplicationService } from 'src/externals/services/application/v1-application.service';
import { MongoVoiceNumbersService } from 'src/mongo/service/voiceNumbers/voiceNumbers.service';
import { UpdateVisibilityCodesDto } from '../dto/update-visibility-codes';
import { NotificationService } from 'src/notification/notification.service';
import { generateWelcomeEmail } from 'src/lib/emails';

@Injectable()
export class OrganizationService {
  constructor(
    private readonly mongoOrganizationService: MongoOrganizationService,
    private readonly mongoUserService: MongoUserService,
    private readonly mongoCredentialsService: MongoCredentialsService,
    private readonly mongoWaitListService: MongoWaitListService,
    private readonly mongoAgentService: MongoAgentService,
    private readonly billingService: BillingService,
    private readonly myLogger: MyLogger,
    private readonly foldersService: FoldersService,
    private readonly userService: UserService,
    private readonly externalApplicationService: ExternalApplicationService,
    private readonly mongoVoiceNumbersService: MongoVoiceNumbersService,
    private readonly notificationService: NotificationService,
  ) {}
  async createOrganization(
    createOrganizationDto: CreateOrganizationDto,
    @Res() response?: Response,
  ) {
    /* 
    Create a new organization
    - Add the userId to the list of admins and members
    - Add the orgId to the User.organization list and also set User.organizationSetup to true
    */
    try {
      const { userId, email, name, summary, userName, fpr } =
        createOrganizationDto;
      const savedOrganization =
        await this.mongoOrganizationService.createOrganization({
          name,
          summary,
          fpr,
          userId,
          admin: [userId],
          members: [
            {
              userId,
              access: {
                fallbacks: { accessVal: 'all' },
                emulator: { accessVal: 'all' },
                agents: { accessVal: 'all' },
                settings: { accessVal: 'all' },
                billing: { accessVal: 'all' },
              },
            },
          ],
        });

      const orgId = savedOrganization._id.toString();
      const updateUser = await this.mongoUserService.InsertOrganizationIdInUser(
        orgId,
        userId,
      );
      //Start an agent folder for the organization
      await this.foldersService.startFolder(orgId);

      //Start a Stripe billing
      const billingUpdate = await this.billingService.startPlan({
        email,
        name: userName,
        orgId,
        isCustomPlan1: createOrganizationDto.isCustomPlan1 || false
      });

      const orgUpdated = await this.updateOrganisation(
        { _id: orgId },
        billingUpdate,
      );
      if (!orgUpdated) {
        throw new HttpException(
          'Error updating the organization.',
          HttpStatus.NOT_IMPLEMENTED,
        );
      }
      if(response) {
        const dateInTheFuture = new Date();
        dateInTheFuture.setFullYear(dateInTheFuture.getFullYear() + 100);
        response.cookie('orgId', orgUpdated._id.toString(), {
          httpOnly: true,
          secure: process.env.NODE_ENV !== 'DEVELOPMENT',
          expires: dateInTheFuture,
          domain: process.env.DOMAIN,
          sameSite: process.env.NODE_ENV === 'DEVELOPMENT' ? 'lax' : 'none',
        });
        return response.status(HttpStatus.CREATED).json({
          organizationSetup: updateUser.organizationSetup,
          savedOrganization: orgUpdated,
        });
      } else {
        return orgUpdated;
      }      
    } catch (error) {
      this.myLogger.error({ message: 'Error is ' + error.message });
      return response
        .status(HttpStatus.INTERNAL_SERVER_ERROR)
        .json({ error: 'Internal Server Error' });
    }
  }

  private async findToken({token} : {token: string}) {
    const URL = `${process.env.BACKEND_URL}/tokens`
    const response = await axios.get(URL, {
      params: {
        tokenVal: token
      },
      headers: {
        'Content-Type': 'application/json'
      }
    })
    return response.data;
  }

  private async createToken(orgId: string, name: string) {
    const URL = `${process.env.BACKEND_URL}/tokens/${orgId}`;
    const response = await fetch(URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ name })
    });
  
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const data = await response.json();
    return { token: data.data.token };
  }

  async createOrganizationForUphex(createOrganizationForUphex: CreateOrganizationUphexDto) {
    const { ...payload } = createOrganizationForUphex;
    
    this.myLogger.log({
      message: `Starting process for email: ${payload.email} | payload: ${JSON.stringify(payload)}`,
      context: "createOrganizationForUphex"
    });

    const existingUser = await this.mongoUserService.getUser({ "email" : payload.email});
    this.myLogger.log({
      message: `Existing user check: ${existingUser ? 'Found' : 'Not found'}`,
      context: "createOrganizationForUphex"
    });
    
    let orgId: string;

    if (existingUser) {
      const userId = existingUser._id.toString();
      this.myLogger.log({
        message: `Processing existing user: ${userId}`,
        context: "createOrganizationForUphex"
      });

      if(payload.capriOrgApiKey) {
        this.myLogger.log({
          message: "Validating capriOrgApiKey",
          context: "createOrganizationForUphex"
        });
        const token = await this.findToken({ token : payload.capriOrgApiKey });
        if(!token) {
          this.myLogger.error({
            message: "Invalid organization token",
            context: "createOrganizationForUphex"
          });
          throw new NotFoundException("Invalid Organization Token.")
        }
        orgId = token.orgId;
        this.myLogger.log({
          message: `Using token-based orgId: ${orgId}`,
          context: "createOrganizationForUphex"
        });
      } else {
        // Search and link the user's billing org
        this.myLogger.log({
          message: "Fetching organization by email",
          context: "createOrganizationForUphex"
        });
        const orgOfUser = await this.getOrganization({"billing.email" : payload.email});
        if(!orgOfUser) {
          throw new BadRequestException('User should create a Capri organization before attempting to connect.');
        }
        orgId = orgOfUser._id.toString();
        this.myLogger.log({
          message: `Using email-based orgId: ${orgId}`,
          context: "createOrganizationForUphex"
        });
      }

      const updatedOrg = await this.updateOrganisation(
        {"_id": orgId}, 
        {"billing.eligibleForCustomPlan1": true}
      );
      this.myLogger.log({
        message: `Organization updated: ${updatedOrg._id}`,
        context: "createOrganizationForUphex"
      });

      await this.externalApplicationService.addExternalApplication(orgId, {
        account:{
          id: payload.accountUid,
          type: 'uid'
        },
        appName: 'uphex',
        capriOrgId: orgId,
        capriUserId: userId,
      });

      return {
        user: {
          email: payload.email,
        },
        organization: {
          orgId: updatedOrg._id,
          name: updatedOrg.name,
          billingEmail: updatedOrg.billing.email,
        } 
      }
    }
    else {
      if(payload.capriOrgApiKey) {
        throw new BadRequestException('New users should not contain a capri token');
      }
      this.myLogger.log({
        message: "Creating new Auth0 user",
        context: "createOrganizationForUphex"
      });
      const user = await this.userService.createAuth0User(payload);
      this.myLogger.log({
        message: `Auth0 user created: ${user}`,
        context: "createOrganizationForUphex"
      });

      this.myLogger.log({
        message: "Saving user to MongoDB",
        context: "createOrganizationForUphex"
      });
      const userSaved = await this.mongoUserService.createUser({
        email: payload.email,
        name: payload.name,
        profilePic: payload.picture || "",
        organizationSetup: false,
        userSessionId: "",
        summary: {
          location: "",
          telephone: "",
          website: ""
        },
        requests: {
          orgRequests: []
        }
      });
      this.myLogger.log({
        message: `User saved to MongoDB: ${userSaved._id}`,
        context: "createOrganizationForUphex"
      });

      // Send an email with the email and creds
      const welcomeMail = generateWelcomeEmail({
        appName: "Capri AI",
        onboardingUrl: "https://beta.capriai.us/",
        supportMail: "<EMAIL>",
        temporaryPassword: user.password,
        userName: userSaved.name
      })
      const mailSent = await this.notificationService.create({
        to: payload.email,
        subject: "Welcome to Capri AI",
        text: welcomeMail,
      })

      this.myLogger.log({
        message: {mailSent},
        context: "createOrganizationForUphex"
      })

      this.myLogger.log({
        message: "Creating new organization",
        context: "createOrganizationForUphex"
      });
      const newOrg = await this.createOrganization({
        email: payload.email,
        fpr: "",
        name: payload.orgName || "Uphex Generated Account",
        isCustomPlan1: true,
        userId: userSaved._id.toString(),
        userName: payload.name,
        summary: {
          description: "",
          howDidYouHear: "uphex",
          location: "",
          phone: "",
          usingFor: [],
          website: "",
        }
      }) as OrganizationDocument; 

      // create a relation between the uphex and capri user, org
      await this.externalApplicationService.addExternalApplication(orgId, {
        account:{
          id: payload?.accountUid,
          type: 'uid'
        },
        appName: 'uphex',
        capriOrgId: newOrg?._id?.toString(),
        capriUserId: userSaved?._id?.toString(),
      });

      // Create a new token called uphex
      const { token } = await this.createToken(newOrg._id.toString(), "Uphex");
      if(!token) {
        throw new InternalServerErrorException("Error creating a Capri token."); 
      }
      this.myLogger.log({
        message: `New organization created: ${newOrg._id}`,
        context: "createOrganizationForUphex"
      });

      return {
        user: {
          email: payload.email,
          password: user.password,
        },
        organization: {
          orgId: newOrg._id,
          name: newOrg.name,
          billingEmail: newOrg.billing.email,
          token
        }
      }
    }
  }

  async checkIfUserHasOrg(email: string) {
    const user = await this.mongoUserService.getUser({ email });
    if (!user) {
      throw new NotFoundException('User not found');
    }
    if (user.organizationSetup) {
      throw new ForbiddenException(
        `User ${email} already has an organization. Use a different email.`,
      );
    }
    return;
  }

  async getOrganization(
    query: object,
    projection = {},
    options = { lean: true },
  ) {
    const org = await this.mongoOrganizationService.getOrganization(
      query,
      projection,
      options,
    );
    return org;
  }

  async updateOrganisation(
    query: object,
    updateBody: any,
    options: any = { new: true, upsert: false },
  ) {
    const updatedOrg = await this.mongoOrganizationService.updateOrganization(
      query,
      updateBody,
      options,
    );
    return updatedOrg;
  }

  async getOrganizationDoc(id: string) {
    const org = await this.mongoOrganizationService.getOrganizationDoc(id);
    if (!org) throw new Error('Organization not found');
    return org;
  }

  private async switchCustomAiProvidersToCapriHostedModels(orgId: string) {
    // fetch all agents
    const agentIds = (await this.getOrganization({  _id: orgId },{
        agents: 1
      }))?.agents ?? [];
    
    // update ai providers to capri hosted models in all the agents
    const agents = await this.mongoAgentService.getAgents({ _id: { $in: agentIds } });
    const agentUpdatePromise = agents.map(async (agent) => {
      const updateBody: object = {}
      const hasHostedLlmAiProvider = (agent.aiProvider?.accountId ?? "").includes("capriHosted");
      if (!hasHostedLlmAiProvider) {
        updateBody['aiProvider'] = {
            accountId: CUSTOM_LLM_7.NAME,
            accountName: CUSTOM_LLM_7.FRONTEND_NAME,
            companyId:  CUSTOM_LLM_7.PROVIDERS,
            isAdvancedSettings: false,
            modelName: CUSTOM_LLM_7.MODEL,
            advancedSettings: {
              frequencyPenalty: 0,
              maxLength: 1000,
              optimize: 'accuracy',
              temperature: 0.3,
            },
          }
      }
      
      const hasHostedFailsafeAiProvider = (agent.failsafeAiProvider?.accountId ?? "").includes("capriHosted");
      if (!hasHostedFailsafeAiProvider) {
        updateBody['failsafeAiProvider'] = {
          failsafe: true,
          companyId: CUSTOM_LLM_6.PROVIDERS,
          modelName: CUSTOM_LLM_6.MODEL,
          accountName: CUSTOM_LLM_6.FRONTEND_NAME,
          accountId: CUSTOM_LLM_6.NAME
        };
      }


      const updatedActions = []
      for ( const action of (agent.actions ?? [])){
        const currAction = action;
        if (!((currAction.advancedSettings?.aiProvider?.accountId ?? "").includes("capriHosted"))) {
          
          currAction['advancedSettings']['aiProvider'] = {
            accountId: CUSTOM_LLM_7.NAME,
            accountName: CUSTOM_LLM_7.FRONTEND_NAME,
            companyId:  CUSTOM_LLM_7.PROVIDERS,
            modelName: CUSTOM_LLM_7.MODEL
          }
        }
        updatedActions.push(currAction);
      }

      updateBody['actions'] = updatedActions;

      return this.mongoAgentService.updateAgent({ _id: agent._id.toString() }, {
        '$set': updateBody
      });
    });


    const updatedAgents = await Promise.all(agentUpdatePromise);

    return updatedAgents
  }

  async choosePlan(payload: ChoosePlanBasicDto) {
    const { orgId, planName, billingCycle } = payload;
    this.myLogger.log({
      message: `OrgId plan changed ${orgId} with planName ${planName} and billingCycle ${billingCycle} }`,
    });
    const org = await this.getOrganization(
      { _id: orgId },
      { billing: 1, agents: 1 },
    );
    const { billing } = org;
    const activeAgents = await this.mongoAgentService.getAgents({
      orgId: orgId,
      disabled: false,
    });
    const activeAgentsLength = activeAgents?.length ?? 0;
    const allowedAgentCount = planMap[planName].agents;
    if (!billing) {
      this.myLogger.error({
        message: `Organization with id ${orgId} doesnt have billing instance.`,
      });
      throw new HttpException(
        'Organization doesnt have billing instance. Contact the admin.',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
    const { customerId, havePaymentInfo, allowedAgents } = billing;
    if (activeAgentsLength > allowedAgentCount) {
      this.myLogger.error({
        message: `Please disable ${
          activeAgentsLength - allowedAgentCount
        } agent first`,
      });
      throw new HttpException(
        `Please disable ${activeAgentsLength - allowedAgentCount} agent first`,
        HttpStatus.FORBIDDEN,
      );
    }
    const data = await this.billingService.choosePlan({
      ...payload,
      customerId,
      havePaymentInfo,
      orgId,
      billingCycle,
    });
    if(planName === PLANS.CUSTOMPLAN1) {
      await this.switchCustomAiProvidersToCapriHostedModels(orgId);
    }
    
    this.myLogger.log({
      message: `Billing plan chosen for organization with id ${orgId}`,
    });
    if (!data) {
      throw new HttpException(
        'Error updating the billing. Please contact admin.',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
    if (data.subscriptionId) {
      const orgUpdate = await this.updateOrganisation(
        { _id: orgId },
        {
          $set: {
            'billing.plan': planName,
            'billing.startDate': Date.now(),
            'billing.billingCycle': billingCycle,
            'billing.havePaymentInfo': planName !== PLANS.CUSTOMPLAN1 ? true : billing.havePaymentInfo,
            'billing.allowedAgents': planMap[planName].agents,
            'billing.status': 'active',
          },
        },
      );
      this.myLogger.log({
        message: `Organization with id ${orgId} updated`,
      });
    }
    return data;
  }

  async getBillingDetails(payload: { orgId: string }) {
    const { orgId } = payload;
    const response = await this.getOrganization(
      { _id: orgId },
      { billing: 1, agents: 1 },
    );
    const { customerId } = response.billing;
    const stripeBillingDetails = await this.billingService.getStripeDetails({
      customerId,
    });
    this.myLogger.log({ message: { stripeBillingDetails } });

    const activeAgentsArr = await this.mongoAgentService.getAgents({
      orgId,
      disabled: false,
    });
    const agentCount = activeAgentsArr.length;
    const { billing, agents } = response;
    // const agentCount = agents.length;
    return { billing, agentCount, ...stripeBillingDetails };
  }

  async cancelSubscription(payload: { orgId: string }) {
    const { orgId } = payload;
    const org = await this.getOrganization({ _id: orgId }, { billing: 1 });
    const { billing } = org;
    if (!billing) {
      throw new HttpException(
        'Organization doesnt have billing instance. Please contact admin.',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
    const { subscriptions } = billing;
    const subscriptionId = subscriptions[0].subscriptionId;
    if (!subscriptionId) {
      throw new HttpException(
        'Could not find a stripe subscription linked to the organization. Please contact admin.',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
    const response = await this.billingService.cancelPlan({
      orgId,
      subscriptionId,
    });
    const orgUpdated = await this.updateOrganisation(
      { _id: orgId },
      { 'billing.status': 'cancelled' },
    );
    return;
  }

  async checkChannelUnderOrgs({
    locationId = null,
    exceptOrgId = null,
    orgId = null,
    type = undefined,
    kind = undefined,
  }) {
    try {
      let orgQuery: string | object;
      if (exceptOrgId) orgQuery = { $ne: exceptOrgId };
      else if (orgId) orgQuery = orgId;

      const credObj = {
        keyId: locationId,
        kind: kind ?? KINDS.GHL_CREDENTIAL,
      };

      if (orgQuery) credObj['organizationId'] = orgQuery;
      if (type) credObj['type'] = type;

      const creds = await this.mongoCredentialsService.getCredential(credObj);
      return creds;
    } catch (e) {
      this.myLogger.error({ message: e.message });
      return undefined;
    }
  }

  async checkGhlDatasourcesUnderOrgs({
    locationId = null,
    exceptOrgId = null,
    orgId = null,
  }) {
    let orgQuery: string | object;
    if (exceptOrgId) orgQuery = { $ne: exceptOrgId };
    else if (orgId) orgQuery = orgId;

    const creds = await this.mongoCredentialsService.getCredential({
      keyId: locationId,
      organizationId: orgQuery,
      kind: KINDS.GHL_CREDENTIAL,
      type: CONNECTION_TYPES.DATASOURCES,
    });
    return creds;
  }

  filterChannel(organization: any, filterKey: string, filterValue: string) {
    return organization.connections.channels.find(
      (channel) => channel[filterKey] === filterValue,
    );
  }

  async addNewMember(payload: AddNewMemberDto, reqSenderUserId: string) {
    try {
      const { email, orgId, isAdmin, ...access } = payload;

      const accessObj =
        await this.mongoOrganizationService.memberAccessModelClass(access);
      const body = {
        reqId: uuidv4(),
        orgId,
        isAdmin,
        access: accessObj,
      };
      const user = await this.mongoUserService.getUser({ email });

      // Check if user does not exist
      if (!user) {
        //find the person who send the request
        const { name } = await this.mongoUserService.getUser(
          { _id: reqSenderUserId },
          { name: 1 },
        );
        await this.mongoWaitListService.pushOrgReq(email, body, name);
        throw new HttpException(
          'User does not have an account with Capri. Ask them to create one first.',
          HttpStatus.CONFLICT,
        );
      }

      // Check if join request already sent
      if (
        user.requests &&
        user.requests.orgRequests &&
        user.requests.orgRequests.some((request) => request.orgId === orgId)
      ) {
        throw new HttpException(
          'Join request already sent',
          HttpStatus.CONFLICT,
        );
      }

      // Check if user is already part of the organization
      if (user.organizations && user.organizations.includes(orgId)) {
        throw new HttpException(
          'User is already part of the organization',
          HttpStatus.CONFLICT,
        );
      }

      // If none of the above checks fail, then update user to send the join request

      await this.mongoUserService.updateUser(
        { email },
        {
          $push: {
            'requests.orgRequests': body,
          },
        },
      );

      return;
    } catch (error) {
      throw error;
    }
  }

  async getOrganizationMembers(payload: { _id: string }) {
    const { _id } = payload;
    const orgMembers = await this.mongoOrganizationService.getOrganization(
      { _id },
      { admin: 1, members: 1 },
    );

    const res = {
      admin: [],
      members: [],
    };

    const adminIds = new Set();

    if (orgMembers.admin) {
      for (let i = 0; i < orgMembers.admin.length; i++) {
        const userDetails = await this.mongoUserService.getUser({
          _id: orgMembers.admin[i],
        });

        res.admin.push({
          userId: orgMembers.admin[i],
          userName: userDetails.name,
          email: userDetails.email,
          profilePic: userDetails.profilePic,
        });

        adminIds.add(orgMembers.admin[i]);
      }
    }

    if (orgMembers.members) {
      for (let i = 0; i < orgMembers.members.length; i++) {
        const userId = orgMembers.members[i].userId;
        if (adminIds.has(userId)) {
          // Skip if this user is an admin
          continue;
        }

        const userDetails = await this.mongoUserService.getUser({
          _id: userId,
        });
        if (!userDetails) {
          continue;
        }
        res.members.push({
          userId: userDetails._id,
          userName: userDetails.name,
          email: userDetails.email,
          profilePic: userDetails.profilePic,
        });
      }
    }

    return res;
  }
  async handleStripeSuccess(payload: { session_id: string; price_id: string }) {
    const { session_id, price_id } = payload;
    const billingCycle = [
      process.env.STRIPE_PRICE_ID_PRO_YEARLY,
      process.env.STRIPE_PRICE_ID_PRO_YEARLY,
      process.env.STRIPE_PRICE_ID_ENTERPRISE_YEARLY,
      process.env.STRIPE_PRICE_ID_CUSTOMPLAN1_YEARLY
    ].includes(price_id)
      ? 'yearly'
      : 'monthly';
    const { subscription_id, orgId, customer_id } =
      await this.billingService.handleStripeSuccess({ session_id });

    //set the have havePaymentInfo to true
    //Update the billing.subscription to {price_id, subscription_id}
    const planEntry = Object.values(planMap).find(
      (plan) => plan.price_id === price_id || plan.price_id_yearly === price_id,
    );

    const planName = planEntry
      ? Object.keys(planMap).find((key) => planMap[key] === planEntry)
      : null;
    const orgUpdate = await this.updateOrganisation(
      { _id: orgId },
      {
        $set: {
          'billing.plan': planName,
          'billing.billingCycle': billingCycle,
          'billing.startDate': Date.now(),
          'billing.havePaymentInfo': true,
          'billing.allowedAgents': planMap[planName].agents,
          'billing.status': 'active',
          'billing.customerId': customer_id,
        },
        $push: {
          'billing.subscriptions': {
            priceId: price_id,
            subscriptionId: subscription_id,
          },
        },
      },
    );
    return;
  }
  async handleStripeV2toV3Migration(payload: { email: string; orgId: string }) {
    const { email, orgId } = payload;
    const v2Data = await this.billingService.findV2Users({ email });
    if (!v2Data) {
      this.myLogger.log({
        message: 'No user found with a paid plan' + { email, orgId },
      });
    } else {
      const { customerId, key, priceId, allowedAgents } = v2Data;
      //update the orgId with these data
      const org = await this.mongoOrganizationService.updateOrganization(
        { _id: orgId },
        {
          'billing.plan': key,
          'billing.customerId': customerId,
          'billing.havePaymentInfo': true,
          'billing.allowedAgents': allowedAgents,
        },
      );
    }
  }

  async getOrgs({ query = {}, projection = {}, options = {} }) {
    const orgs = await this.mongoOrganizationService.getOrganizations(
      query,
      projection,
      options,
    );
    return orgs;
  }

  async checkBillingStatus() {
    const cursor = this.mongoOrganizationService.getOrganizationsCursor(
      {},
      { billing: 1 },
    );

    for await (const orgCursor of cursor) {
      const org = await this.mongoOrganizationService.getOrganization({
        _id: orgCursor._id,
      });
      const { billing } = org;

      // If billing is not defined, skip this iteration
      if (!billing) {
        this.myLogger.log({
          message: `Billing not defined. orgId: ${org._id}`,
        });
        continue;
      }

      const { customerId } = billing;
      if (!customerId) {
        continue;
      }

      const billingStatus = await this.billingService.checkBillingStatus({
        customerId,
      });
      this.myLogger.log({
        message: `orgId: ${org._id}. billingExpired: ${billingStatus?.billingExpired}`,
      });

      // const orgUpdate = await this.updateOrganisation(
      //   { _id: org._id },
      //   {
      //     'billing.status': billingStatus.billingExpired
      //       ? 'suspended'
      //       : 'active',
      //   },
      // );
    }
  }
  async validatePlans() {
    const date14DaysAgo = new Date(Date.now() - 15 * 24 * 60 * 60 * 1000);

    // Fetch organizations that match the criteria
    const organizations =
      await this.mongoOrganizationService.getManyOrganizations({
        'billing.plan': 'trial',
        'billing.status': { $ne: 'suspended' },
        'billing.startDate': { $lt: date14DaysAgo.getTime() },
      });

    // Extract the _id of each organization
    const organizationIds = organizations.map((org) => org._id);

    // Perform the update operation
    await this.mongoOrganizationService.updateManyOrganizations(
      {
        'billing.plan': 'trial',
        'billing.startDate': { $lt: date14DaysAgo.getTime() },
        'billing.status': { $ne: 'suspended' },
      },
      {
        $set: { 'billing.status': 'suspended' },
      },
    );

    // Return the _id of the updated organizations
    return organizationIds;
  }

  async checkOrganizationExists(query) {
    const org = await this.mongoOrganizationService.checkIfOrganizationExists(
      query,
    );
    return org;
  }
  async billingExpiredAftermath(customerId: string) {
    try {
      //get the org from the customerId and get the list of agents
      this.myLogger.log({
        message: `Billing expired aftermath for customerId ${customerId}`,
      });

      const org = await this.mongoOrganizationService.getOrganization({
        'billing.customerId': customerId,
      });
      const { agents } = org;
      if (!!!agents) {
        this.myLogger.log({
          message: `No agents found for organization with customerId ${customerId}`,
        });
        return;
      }
      for (const agentId of agents) {
        const agent = await this.mongoAgentService.updateAgent(
          { _id: agentId },
          { disabled: true },
        );
      }
      this.myLogger.log({
        message: `Agents in organization with customerId ${customerId} disabled. The agents are ${agents}`,
      });
    } catch (error) {
      this.myLogger.log({
        message: `Error in billingExpiredAftermath: ${error.message}`,
      });
    }
  }
  async handleBillingExpired(req: RawBodyRequest<Request>, sig: string) {
    const { customerId, status, allowedAgents, priceId } =
      await this.billingService.SubscriptionExpired(req, sig);
    this.myLogger.log({
      message: { customerId, status, allowedAgents, priceId },
    });
    const freePriceIds = [
      process.env.STRIPE_PRICE_ID_TRIAL,
      process.env.STRIPE_PRICE_ID_CUSTOMPLAN1,
      process.env.STRIPE_PRICE_ID_CUSTOMPLAN1_YEARLY
    ]
    if (status === 'active' && freePriceIds.includes(priceId)) {
      this.myLogger.log({
        message:
          `Subscription with priceId ${priceId} renewed. No action required. CustomerId: ` +
          customerId,
      });
      return;
    }
    if (customerId) {
      if (status === 'suspended') {
        const afterMath = await this.billingExpiredAftermath(customerId);
      }
      const updateObject = { 'billing.status': status };
      if (allowedAgents !== undefined) {
        updateObject['billing.allowedAgents'] = allowedAgents;
      }
      const orgUpdate = await this.mongoOrganizationService.updateOrganization(
        { 'billing.customerId': customerId },
        updateObject,
      );
      if (!orgUpdate) {
        this.myLogger.error({
          message: `Error updating the organization with customerId ${customerId}`,
        });
        // throw new Error('Error updating the organization');
      } else {
        this.myLogger.log({
          message: {
            message: `Organization billing updated with orgId ${orgUpdate?._id.toString()}. This is the updated object ${updateObject}`,
            orgUpdate,
          },
        });
      }
    }
  }

  async handleLlmCredsAdded(req: RawBodyRequest<Request>, sig: string) {
    const { customerId, amount } = await this.billingService.LlmCredsAdded(
      req,
      sig,
    );
    if (!!!customerId || !!!amount) {
      this.myLogger.error({
        message: `Empty response in handleLlmCredsAdded. customerId: ${customerId}, amount: ${amount}`,
      });
      return;
    }
    const updatedOrg = await this.mongoOrganizationService.updateOrganization(
      { 'billing.customerId': customerId },
      {
        $inc: { 'billing.addonAmount': amount },
      },
    );
    return;
  }

  async getNumOfSuspendedActiveAgents() {
    const orgs = await this.mongoOrganizationService.getManyOrganizations(
      { 'billing.status': 'suspended', 'billing.plan': 'trial' },
      { agents: 1, 'billing.email': 1 },
    );
    let count = 0;
    const orgsList = new Set();
    const emailList = new Set();
    const disabledAgentsList = new Set();
    

    for (const org of orgs) {
      
      for (const agentId of org.agents) {
        const agent = await this.mongoAgentService.getAgent({ _id: agentId });
        

        if (!agent.disabled) {
          count++;
          orgsList.add(org._id.toString());
          emailList.add(org.billing.email);
          
          const agent = await this.mongoAgentService.updateAgent(
            { _id: agentId },
            { disabled: true },
          );
          disabledAgentsList.add(agentId);
          
        }
      }
    }
    
    return {
      count,
      orgsList: Array.from(orgsList),
      emailList: Array.from(emailList),
      disabledAgentsList: Array.from(disabledAgentsList),
    };
  }

  async revalidatePlans() {
    for (const plan in planMap) {
      const maxAgents = planMap[plan as TPlanName].limits?.maxAgents || 1;
      if (maxAgents !== undefined) {
        

        await this.mongoOrganizationService.updateManyOrganizations(
          { 'billing.plan': plan },
          { $set: { 'billing.allowedAgents': maxAgents } },
        );
      }
    }
  }

  async getOrganizationSetupStatus(orgId: string) {
    const org = await this.getOrganization(
      { _id: orgId },
      { connections: 1, agents: 1, sessions: 1 },
    );
    const { agents, sessions } = org;

    let agentSetup = false;
    for (const agentId of agents) {
      const agent = await this.mongoAgentService.getAgent({ _id: agentId });
      if (agent && agent.prompts?.prompt.length > 0 && agent.aiProvider) {
        agentSetup = true;
        break;
      }
    }

    const setupStatus = {
      agent: agents.length > 0,
      agentSetup: agentSetup,
      session: sessions.length > 0,
    };

    return setupStatus;
  }

  async listPhoneNumbersForOrganization(orgId: string){
      const p = await this.mongoVoiceNumbersService.getPhones({ orgId });
      return p;
  }

  async updateIframeVisibilityCodes(orgId: string, visibilityCodesBody: UpdateVisibilityCodesDto) {
    const { visibilityCodes } = visibilityCodesBody;
    
    const updatedOrg = await this.mongoOrganizationService.updateOrganization(
      { _id: orgId },
      { $set: { "iframeConfig.visibilityCodes": visibilityCodes } }
    );
    
    return updatedOrg?.iframeConfig?.visibilityCodes || [];
  }
}
