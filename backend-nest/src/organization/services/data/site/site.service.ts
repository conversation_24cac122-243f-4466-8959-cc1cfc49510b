import { HttpStatus, Injectable } from '@nestjs/common';
import { ApiclientService } from 'src/api-client/services/apiclient.service';
import { methods } from 'src/api-client/utils/channel.constants';
import { OrganizationService } from '../../organization.service';
import e, { Response, Request, NextFunction } from 'express';
import { PROVIDERS, STATUS } from 'src/lib/constant';
import {
  convertToValidURL,
  formatUserAssistantString,
  getDomainFromUrl,
  isValidURL,
} from 'src/lib/utils';
import { v4 as uuidv4 } from 'uuid';
import { IscrapedData } from 'src/organization/dto/responses/get.dto';
import { VectorService } from 'src/vector/services/vector.service';
import { PineconeService } from 'src/vector/services/pinecone/pinecone.service';
import { addPrefixesToSentences, isValidDomain } from './helpers';
import { TiktokenService } from 'src/vector/services/tiktoken.service';
import { MyLogger } from 'src/logger/logger.service';

@Injectable()
export class SiteService {
  constructor(
    private readonly apiclientService: ApiclientService,
    private readonly organizationService: OrganizationService,
    private readonly vectorService: VectorService,
    private readonly pineconeService: PineconeService,
    private readonly tiktokenService: TiktokenService,
    private readonly logger: MyLogger
  ) {}

  async scrapeSites(
    orgId: string,
    urls: string,
    userName: string,
    domain: string,
    res?: Response,
    userId?: string,
  ) {
    let accountId: string;
    try {
      this.logger.log({
        message: `Received scrape request for site -> ${urls}`
      })
      if (res) {
        res.setHeader('Cache-Control', 'no-cache');
        res.setHeader('Content-Type', 'text/event-stream');
        res.setHeader('Connection', 'keep-alive');
        res.flushHeaders(); // flush the headers to establish SSE with client
        res.on('close', () => {
          this.logger.log({
            message: 'client dropped me'
          });
          res.end();
        });
      }
      let org;
      accountId = uuidv4();
      let validatedDomain = domain;

      if (!isValidURL(domain)) {
        validatedDomain = convertToValidURL(domain);
      }

      // create a staticData entity
      var staticData = {
        accountId,
        name: (urls || validatedDomain).split(' ')[0],
        providerName: PROVIDERS.WEBSITE,
        status: STATUS.PENDING,
        reference: validatedDomain,
        data: [],
        author: userName,
      };
      if (userId) {
        staticData['userId'] = userId;
      }
      try {
        org = await this.organizationService.updateOrganisation(
          { _id: orgId },
          { $push: { 'connections.staticData': staticData } },
        );
        if (!org) throw new Error('No organization found');
        if (res) {
          res.write(
            `data: ${JSON.stringify({
              accountId,
              name: staticData.name,
              author: staticData.author,
              providerName: PROVIDERS.WEBSITE,
              status: STATUS.PENDING,
              data: staticData.data,
            })}\n\n`,
          );
        }
      } catch (err) {
        this.logger.error({
          message: `Failed to update organization for scraping initiation -> ` + err.message,
          context: `SiteService.scrapSites`
        });
        if (res) {
          res.write(
            `data: ${JSON.stringify({
              error: err.message,
            })}\n\n`,
          );
        }
      }

      // call the scraper function
      const scrapedData = await this.apiclientService.apiRequest<IscrapedData>(
        methods.GET,
        process.env.SCRAPER_FUNC_URL + `/scrape?urls=${urls}`,
      );
      this.logger.log({
        message: `Scraped content from site -> length : ${scrapedData.data.length} , first result in scraped content : ${JSON.stringify(scrapedData.data?.[0]?.result?.[0], null, 2)}`,
        context: `SiteService.scrapSites`
      })
      if (!scrapedData) {
        // update the staticData entity in the mongo organization record
        await this.organizationService.updateOrganisation(
          {
            _id: orgId,
            'connections.staticData.accountId': accountId,
          },
          {
            $set: {
              'connections.staticData.$.status': STATUS.FAILED,
              'connections.staticData.$.data': [],
            },
          },
        );
        if (res) res.end();
        return;
      }

      let chunks = [];
      scrapedData.data.forEach((site) => {
        (site.result || []).map((qa) => {
          if (qa?.answer && qa?.answer)
            chunks.push({
              content: `user: ${qa.question}\nassistant: ${qa.answer}`,
            });
        });
      });

      await this.saveSiteEmbedding(
        orgId,
        accountId,
        validatedDomain,
        chunks,
        res,
      );
      this.logger.log({
        message: `Scrapped  successfully | ${urls}`,
        context: `SiteService.scrapSites`
      })
      if (!res) return staticData;
      return;
    } catch (e) {
      try {
        if (accountId) {
          await this.organizationService.updateOrganisation(
            {
              _id: orgId,
              'connections.staticData.accountId': accountId,
            },
            {
              $set: {
                'connections.staticData.$.status': STATUS.FAILED,
                'connections.staticData.$.data': [],
              },
            },
          );
        }
      } finally {
        this.logger.log({
          message: `@error while scraping site for domain ${domain} | orgId: ${orgId} :` + e.message,
          context: `SiteService.scrapSites`
        });
        if (res) res.end();
        else return null;
      }
    }
  }

  async updateScrapedData(
    orgId: string,
    accountId: string,
    domain: string,
    contents: Object[],
    res: Response,
  ) {
    try {
      // contents = contents.map((chunk) => {
      //     return {
      //         ...chunk,
      //         content: formatUserAssistantString(chunk['content']),
      //     }
      // });
      await this.saveSiteEmbedding(
        orgId,
        accountId,
        domain,
        contents,
        res,
        false,
      );
    } catch (e) {
      this.logger.log({
        message: e.message
      });
      res.end();
    }
  }

  async saveSiteEmbedding(
    orgId: string,
    accountId: string,
    domain: string,
    chunks: Object[],
    res?: Response,
    sse: boolean = true,
  ) {
    // create vector embeddings for each chunk
    const openaiApiKey = process.env.OPENAI_API_KEY;
    let contents = chunks.map((chunk) => chunk['content']);

    const embeddings = await this.vectorService.createEmbedding(
      PROVIDERS.OPENAI_PROVIDER,
      openaiApiKey,
      contents,
    );
    // save the embeddings to pinecone
    const pineconeRecord = [];
    for (let i = 0; i < embeddings.length; i++) {
      let accId = uuidv4();
      // determine the index of the chunk

      pineconeRecord.push({
        id: chunks[embeddings[i].index]['id'] || accId,
        values: embeddings[i].embedding,
        metadata: {
          content: chunks[embeddings[i].index]['content'],
          type: PROVIDERS.WEBSITE,
          name: domain,
          accountId,
        },
      });
    }
    const ids = pineconeRecord.map((record) => {
      let [question, answer] = record.metadata.content.split('\n');
      return {
        _id: record.id,
        question,
        answer,
      };
    });
    const namespace = orgId;
    // // save the embeddings to pinecone
    const indexName = await this.pineconeService.createIndex(
      process.env.PINECONE_INDEX,
    );
    const index = await this.pineconeService.connectIndex(indexName);
    await this.pineconeService.batchUpsertVectors(
      index,
      pineconeRecord,
      namespace,
    );

    // update the staticData entity in the mongo organization record
    await this.organizationService.updateOrganisation(
      {
        _id: orgId,
        'connections.staticData.accountId': accountId,
      },
      {
        $set: {
          'connections.staticData.$.status': STATUS.SUCCESS,
        },
      },
    );

    if (ids) {
      await this.organizationService.updateOrganisation(
        {
          _id: orgId,
          'connections.staticData.accountId': accountId,
        },
        {
          $addToSet: {
            'connections.staticData.$.data': { $each: ids },
          },
        },
      );
    }

    this.logger.log({
      message: `First five ids of the embedding created : ${ids.slice(0,5)}`,
      context: `site.saveEmbeddding`
    })

    const responseObj = {
      accountId,
      name: domain,
      providerName: PROVIDERS.WEBSITE,
      status: STATUS.SUCCESS,
      data: ids,
    };

    this.logger.log({
      message: `Saving site embedding successful`,
      context: `site.saveEmbeddding`
    })

    if (!res) return;
    if (sse) {
      res.write(`data: ${JSON.stringify(responseObj)}\n\n`);
      res.end();
    } else {
      res.json(responseObj);
    }
  }

  async retrieveEmbeddedScrapedData(
    orgId: string,
    ids: string[],
    accountId?: string,
  ) {
    let org;
    this.logger.log({
      message: { ids, accountId }
    });
    if (ids.length === 0) {
      org = await this.organizationService.getOrganization(
        { _id: orgId },
        { 'connections.staticData': 1 },
      );
      if (!org) throw new Error('No organization found');
      const staticData = org.connections.staticData.find(
        (site) => site.accountId === accountId,
      );
      if (!staticData)
        throw new Error(
          'No website data found in database. Please scrape the website again.',
        );
      ids = staticData.data;
    }
    let records = await this.pineconeService.fetchVectors(
      process.env.PINECONE_INDEX,
      orgId,
      ids,
    );
    let name = records['vectors'][ids[0]].metadata['name'];
    let type = records['vectors'][ids[0]].metadata['type'];
    let chunks = Object.keys(records.vectors || []).map((key) => {
      return {
        id: records['vectors'][key].id,
        metadata: {
          content: records['vectors'][key]?.metadata['content'],
          index: records['vectors'][key]?.metadata['index'],
        },
      };
    });
    return { chunks, accountId, name, type };
  }

  async retrieveEmbeddedText(orgId: string, ids: string[]) {
    let { chunks, accountId, name, type } =
      await this.retrieveEmbeddedScrapedData(orgId, ids);
    return {
      chunks: chunks
        .map((chunk) =>
          (chunk.metadata['content'] || '').replace(/user:|assistant:/g, ''),
        )
        .join('\n\n'),
      accountId,
      name,
      type,
    };
  }
}
