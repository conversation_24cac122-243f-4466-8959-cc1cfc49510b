export function addPrefixesToSentences(modifiedString) {
  try {
    let sentences = modifiedString.split('\n');
    let originalString = '';
    sentences.forEach((sentence) => {
      if (sentence.trim() !== '') {
        if (sentence.startsWith('What') || sentence.startsWith('Capri')) {
          originalString += 'user: ' + sentence.trim() + '\n';
        } else {
          originalString += 'assistant: ' + sentence.trim() + '\n';
        }
      }
    });
    return originalString.trim();
  } catch (err) {
    return modifiedString;
  }
}

// a funuction to check if a domain argument is a valid domain
export function isValidDomain(domain: string): boolean {
  const domainRegex = /^(https?|ftp):\/\/[^\s/$.?#].[^\s]*$/;
  return domainRegex.test(domain);
}

export function encryptStripeApiKey(apiKey: string): string {
  return `${apiKey.slice(0, 7)}***********${apiKey.slice(-3)}`;
}
