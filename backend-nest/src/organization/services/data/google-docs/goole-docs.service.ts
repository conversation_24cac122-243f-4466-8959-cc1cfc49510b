import { Injectable, NotFoundException } from '@nestjs/common';
import getId from 'get-short-id';
import { Configuration, OpenAIApi } from 'openai';
import { PROVIDERS } from '../../../../lib/constant';
import { getGoogleDoc } from '../../../../lib/google_funcs';
import { PineconeService } from '../../../../vector/services/pinecone/pinecone.service';
import { IntegrationService } from '../../integrations/integration.service';
import { OrganizationService } from '../../organization.service';
import { v4 as uuidv4 } from 'uuid';
import { MyLogger } from 'src/logger/logger.service';
@Injectable()
export class GoogleDocsService {
  private openai;

  constructor(
    private readonly pineconeService: PineconeService,
    private readonly organizationService: OrganizationService,
    private readonly integrationService: IntegrationService,
    private readonly myLogger: MyLogger,
  ) {
    const configuration = new Configuration({
      apiKey: process.env.OPENAI_API_KEY,
    });
    this.openai = new OpenAIApi(configuration);
  }

  async connectDoc({
    orgId,
    docId,
    userId,
    docLink,
    userName,
  }: {
    orgId: string;
    docId: string;
    userId: string;
    docLink: string;
    userName: string;
  }) {
    let getDocData = await this.getDocData({ docId });

    let qaPairs = getDocData.qaPairs;

    if (qaPairs.length === 0) {
      this.myLogger.log({
        message: `No QA pairs found for docId: ${docId}, orgId: ${orgId}`,
      });
      throw new Error('No QA pairs found');
    }

    let qaPairIds = qaPairs.map(() => uuidv4());

    let connections = await this.saveDocData(
      orgId,
      docId,
      userId,
      qaPairs,
      docLink,
      userName,
      getDocData.title,
      qaPairIds,
    );

    this.saveDocInPineCone({ docId, orgId, qaPairs, qaPairIds }).catch(
      (error) => {
        
      },
    );

    return {
      isSuccess: true,
      title: getDocData.title,
      connections,
    };
  }

  async saveDocData(
    orgId: string,
    docId: string,
    userId: string,
    qaPairs: any,
    docLink: string,
    userName: string,
    name: string,
    qaPairIds: string[],
  ) {
    let accountId = getId({
      count: 15,
    });

    qaPairs.forEach((pair, index) => {
      pair._id = qaPairIds[index];
    });

    const data = {
      providerName: PROVIDERS.GOOGLE_DOCS,
      accountId,
      keyId: docId,
      reference: docLink,
      author: userName,
      name: name,
      userId,
      data: qaPairs,
    };

    await this.integrationService.saveStaticData(orgId, data);

    return {
      providerName: data.providerName,
      accountId: data.accountId,
      keyId: data.keyId,
      reference: data.reference,
      author: data.author,
      name: data.name,
      userId: data.userId,
      data: qaPairIds,
    };
  }

  async updateDocData({
    orgId,
    accountId,
    name,
  }: {
    orgId: string;
    accountId: string;
    userId: string;
    name: string;
  }) {
    let data = {
      name,
    };

    const org = await this.organizationService.getOrganizationDoc(orgId);
    if (org) {
      const indexToUpdate = org.connections.staticData.findIndex(
        (staticData) => staticData.accountId === accountId,
      );
      if (indexToUpdate === -1) {
        this.myLogger.log({
          message: `Datasource not found: ${indexToUpdate}, accountId: ${accountId}, orgId: ${orgId}`,
        });
        throw new Error('Datasource Id not found: ' + indexToUpdate);
      }
    }

    await this.integrationService.updateStaticData(orgId, accountId, name);

    return { ...data, accountId };
  }

  async getStaticData({
    orgId,
    accountId,
    userId,
  }: {
    orgId: string;
    accountId: string;
    userId: string;
  }) {
    const org = await this.organizationService.getOrganizationDoc(orgId);
    if (org) {
      const indexToUpdate = org.connections.staticData.findIndex(
        (staticData) => staticData.accountId === accountId,
      );
      if (indexToUpdate === -1) {
        this.myLogger.log({
          message: `Datasource not found: ${indexToUpdate}, accountId: ${accountId}, orgId: ${orgId}`,
        });
        throw new Error('Datasource Id not found: ' + indexToUpdate);
      }
    }

    return await this.integrationService.getStaticData(orgId, accountId);
  }

  async disconnectDoc({ orgId, docId }: { orgId: string; docId: string }) {
    await this.deleteDocFromPineCone({
      docId,
      orgId,
    });

    return {
      isSuccess: true,
    };
  }

  async getDocData({ docId }: { docId: string }) {
    const doc = await getGoogleDoc(docId);
    const extractedDoc = this.extractTextContent(doc);
    const qaPairs = await this.generateQAPairs(extractedDoc.content);

    return {
      title: extractedDoc.title,
      qaPairs,
    };
  }

  async saveDocInPineCone({
    docId,
    orgId,
    qaPairs,
    qaPairIds,
  }: {
    docId: string;
    orgId: string;
    qaPairs: any;
    qaPairIds;
  }) {
    try {
      const org = await this.organizationService.getOrganizationDoc(orgId);

      let accountId = org.connections.staticData.find(
        (staticData) => staticData.keyId === docId,
      )?.accountId;

      const embeddings = await this.getDocEmbedded({ qaPairs });
      const pineconeRecord = [];
      for (let i = 0; i < embeddings.length; i++) {
        const embeddingData = embeddings[i][0];
        pineconeRecord.push({
          id: qaPairIds[i],
          values: embeddingData.embedding,
          metadata: {
            question: qaPairs[i].question,
            answer: qaPairs[i].answer,
            type: PROVIDERS.GOOGLE_DOCS,
            accountId,
          },
        });
      }

      const namespace = orgId;
      const indexName = await this.pineconeService.createIndex(
        process.env.PINECONE_INDEX,
      );

      const index = await this.pineconeService.connectIndex(indexName);

      await this.pineconeService.upsertVectors(
        index,
        pineconeRecord,
        namespace,
      );
    } catch (error) {
      this.myLogger.error({
        message: `Error saving doc in pinecone: ${error} for docId: ${docId} and orgId: ${orgId}`,
      });
      throw new Error(error);
    }
  }

  async deleteDocFromPineCone({
    docId,
    orgId,
  }: {
    docId: string;
    orgId: string;
  }) {
    try {
      const org = await this.organizationService.getOrganizationDoc(orgId);
      const namespace = orgId;

      const indexName = await this.pineconeService.createIndex(
        process.env.PINECONE_INDEX,
      );

      const index = await this.pineconeService.connectIndex(indexName);

      let qaPairs: any = org.connections.staticData.find(
        (staticData) => staticData.keyId === docId,
      )?.data;

      let ids = qaPairs.map((pair) => pair._id);

      //delete the ids
      await this.pineconeService.deleteVectors(index, namespace, ids);
    } catch (error) {
      this.myLogger.log({
        message: `Error deleting doc from pinecone: ${error}`,
      });
      throw new Error(error);
    }
  }

  async getDocEmbedded({ qaPairs }) {
    const embeddings = [];
    try {
      for (const pair of qaPairs) {
        const res = await this.openai.createEmbedding({
          input: pair.question,
          model: 'text-embedding-ada-002',
        });

        const embedding = res?.data?.data;
        if (embedding) {
          embeddings.push(embedding);
        } else {
          // Handle case where no embedding is returned
          
        }
      }
    } catch (error) {
      throw new Error(error.message);
    }
    return embeddings;
  }

  private extractTextContent(doc) {
    if (!doc.body || !doc.body.content) {
      this.myLogger.error({
        message: `Document content not found: ${doc}`,
      });
      throw new NotFoundException('Document content not found.');
    }

    let fullText = '';
    for (const contentElement of doc.body.content) {
      if (contentElement.paragraph) {
        for (const element of contentElement.paragraph.elements) {
          if (element.textRun) {
            fullText += element.textRun.content;
          }
        }
      }
    }

    return { title: doc.title, content: fullText.trim() };
  }

  private async generateQAPairs(content: string) {
    try {
      const segments = this.splitIntoSegments(content);

      let qaPairs = [];
      for (const segment of segments) {
        let messages = [
          {
            role: 'system',
            content: `Task: Extract important information from the jumbled content passed in by user and present the information as a set of Question and answers
            **Instructions:**
            1. Only use the content passed in by user to create the question and answer pairs
            2. Please generate a minimum of 3 unique question and answer pairs
            3. Do not use numbering
            4. Keep answers concise and to the point
            5. Start questions with 'user:' and answers with 'assistant:'
            6. There should not be any question or answer that is longer than 300 characters
            7. No question or answer should be empty
            8. All questions and answers should be unique
            9. No question or answer should be repeated
            10. All questions should end with a question mark
            12. Max number of questions and answers should be 50,
            13. Maintain a newline between questions and answers, and use two newlines to separate pairs:\n\n`,
          },
        ];

        messages.push({
          role: 'user',
          content: segment,
        });

        const response = await this.openai.createChatCompletion({
          messages,
          model: 'gpt-4o',
        });

        const segmentQAPairs = this.parseQAPairs(
          response.data.choices[0].message.content,
        );
        qaPairs = [...qaPairs, ...segmentQAPairs];
      }
      return qaPairs;
    } catch (error) {
      this.myLogger.error({
        message: `Error generating QA pairs: ${error}`,
      });
      throw new Error(error);
    }
  }

  private splitIntoSegments(content: string) {
    return content.split('\n\n');
  }

  private parseQAPairs(responseText: string) {
    const qaPairs = [];
    const lines = responseText.trim().split('\n');

    let currentQuestion = '';
    let currentAnswer = '';

    for (const line of lines) {
      if (line.startsWith('user:')) {
        if (currentQuestion && currentAnswer) {
          qaPairs.push({
            question: `user: ${currentQuestion.trim()}`,
            answer: `assistant: ${currentAnswer.trim()}`,
          });
        }
        currentQuestion = line.slice(5).trim();
        currentAnswer = '';
      } else if (line.startsWith('assistant:')) {
        currentAnswer = line.slice(11).trim();
      } else {
        currentAnswer += ' ' + line.trim();
      }
    }

    if (currentQuestion && currentAnswer) {
      qaPairs.push({
        question: `user: ${currentQuestion.trim()}`,
        answer: `assistant: ${currentAnswer.trim()}`,
      });
    }

    return qaPairs;
  }
}
