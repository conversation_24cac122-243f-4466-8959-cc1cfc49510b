import { Injectable } from '@nestjs/common';
import { Storage } from '@google-cloud/storage';
import * as pdfParse from 'pdf-parse';
import { ChatOpenAI } from '@langchain/openai';
import { HumanMessage } from '@langchain/core/messages';
import { ChatMistralAI } from '@langchain/mistralai';
import { ChatPromptTemplate } from '@langchain/core/prompts';
import { PROVIDERS } from '../../../../lib/constant';
import getId from 'get-short-id';
import { PineconeService } from '../../../../vector/services/pinecone/pinecone.service';
import { OrganizationService } from '../../organization.service';
import { IntegrationService } from '../../integrations/integration.service';
import { Configuration, OpenAIApi } from 'openai';
import { MyLogger } from 'src/logger/logger.service';

@Injectable()
export class PdfService {
  private storage = new Storage({
    projectId: process.env.PROJECT_ID,
    credentials: {
      type: process.env.GOOGLE_TYPE,
      project_id: process.env.GOOGLE_PROJECT_ID,
      private_key_id: process.env.GOOGLE_PRIVATE_KEY_ID,
      private_key: process.env.GOOGLE_PDF_PRIVATE_KEY.replace(/\\n/g, '\n'),
      client_email: process.env.GOOGLE_PDF_CLIENT_EMAIL,
      client_id: process.env.GOOGLE_PDF_CLIENT_ID,
      universe_domain: process.env.GOODLE_UNIVERSE_DOMAIN,
    },
  });
  private bucketName = process.env.BUCKET_NAME_PDF;

  private openai;

  constructor(
    private readonly pineconeService: PineconeService,
    private readonly organizationService: OrganizationService,
    private readonly integrationService: IntegrationService,
    private readonly myLogger: MyLogger
  ) {
    const configuration = new Configuration({
      apiKey: process.env.OPENAI_API_KEY,
    });
    this.openai = new OpenAIApi(configuration);
  }

  public async uploadToGoogleStorage(
    file: Express.Multer.File,
    orgId: string,
  ): Promise<string> {
    const bucket = this.storage.bucket(this.bucketName);
    const filename = `${orgId}/${file.originalname}`;
    const fileUpload = bucket.file(filename);

    const stream = fileUpload.createWriteStream({
      metadata: { contentType: file.mimetype },
    });

    return new Promise((resolve, reject) => {
      stream.on('error', (error) => reject(error));
      stream.on('finish', () => {
        fileUpload.makePublic().then(() => {
          const publicUrl = `https://storage.googleapis.com/${this.bucketName}/${filename}`;
          resolve(publicUrl); // Return the public URL here
        });
      });
      stream.end(file.buffer);
    });
  }

  public async extractTextFromPdf(file: Express.Multer.File) {
    try {
      const data = await pdfParse(file.buffer);
      return data.text;
    } catch (error) {
      throw new Error(`Error extracting text from PDF: ${error.message}`);
    }
  }

  public async generateQnAPairsWithMistralAI(text: string) {
    try {
      const model = new ChatMistralAI({
        apiKey: process.env.MISTRAL_API_KEY,
        modelName: 'mistral-medium',
        temperature: 0.0,
      });
      const prompt = ChatPromptTemplate.fromMessages([
        [
          'system',
          'You are a helpful assistant that generates questions and answers from text.',
        ],
        [
          'human',
          'here is the text: {input} /n/n Can you provide me with questions and answers based on the above text?',
        ],
      ]);

      const chain = prompt.pipe(model);
      const response = await chain.invoke(
        {
          input: text,
        },
        {},
      );
      return response.content;
    } catch (error) {
      throw new Error(`Error generating QnA pairs: ${error.message}`);
    }
  }

  public async generateQnAPairsWithOpenAI(text: string) {
    try {
      const model = new ChatOpenAI({
        openAIApiKey: process.env.OPENAI_API_KEY,
        modelName: 'gpt-4-1106-preview',
        temperature: 0.0,
      });

      const response = await model.invoke([
        [
          'system',
          `Task: Extract important information from the jumbled content passed in by user and present the information as a set of Question and answers
            **Instructions:**
            1. Only use the content passed in by user to create the question and answer pairs
            2. Please generate a minimum of 3 unique question and answer pairs
            3. Do not use numbering
            4. Keep answers concise and to the point
            5. Start questions with 'user:' and answers with 'assistant:'
            6. There should not be any question or answer that is longer than 300 characters
            7. No question or answer should be empty
            8. All questions and answers should be unique
            9. No question or answer should be repeated
            10. All questions should end with a question mark
            12. Max number of questions and answers should be 50,
            13. Maintain a newline between questions and answers, and use two newlines to separate pairs:\n\n`,
        ],
        [
          'human',
          `here is the text: ${text} /n/n Can you provide me with questions and answers based on the above text?`,
        ],
      ]);

      return response.content;
    } catch (error) {
      throw new Error(`Error generating QnA pairs: ${error.message}`);
    }
  }

  extractQAPairs(content: any): { question: string; answer: string }[] {
    const qaPairs = [];
    const regex = /Question: (.*?)\nAnswer: (.*?)\n\n/g;
    let match;

    while ((match = regex.exec(content)) !== null) {
      qaPairs.push({
        question: match[1].trim(),
        answer: match[2].trim(),
      });
    }

    return qaPairs;
  }

  async savePdfData(
    orgId: string,
    userId: string,
    qaPairs: any,
    pdfLink: string,
    userName: string,
    name: string,
    qaPairIds: string[],
  ) {
    let accountId = getId({
      count: 15,
    });

    qaPairs.forEach((pair, index) => {
      pair._id = qaPairIds[index];
    });

    const data = {
      providerName: PROVIDERS.PDF,
      accountId,
      reference: pdfLink,
      author: userName,
      name: name,
      keyId: pdfLink,
      userId,
      data: qaPairs,
    };

    await this.integrationService.saveStaticData(orgId, data);

    return {
      providerName: data.providerName,
      accountId: data.accountId,
      reference: data.reference,
      author: data.author,
      name: data.name,
      keyId: data.keyId,
      userId: data.userId,
      data: qaPairIds,
    };
  }

  async savePdfInPineCone({
    pdfLink,
    orgId,
    qaPairs,
    qaPairIds,
  }: {
    pdfLink: string;
    orgId: string;
    qaPairs: any;
    qaPairIds;
  }) {
    try {
      const org = await this.organizationService.getOrganizationDoc(orgId);

      let accountId = org.connections.staticData.find(
        (staticData) => staticData.keyId === pdfLink,
      )?.accountId;

      const embeddings = await this.getDocEmbedded({ qaPairs });
      const pineconeRecord = [];
      for (let i = 0; i < embeddings.length; i++) {
        const embeddingData = embeddings[i][0];
        pineconeRecord.push({
          id: qaPairIds[i],
          values: embeddingData.embedding,
          metadata: {
            question: qaPairs[i].question,
            answer: qaPairs[i].answer,
            type: PROVIDERS.PDF,
            accountId,
          },
        });
      }

      const namespace = orgId;
      const indexName = await this.pineconeService.createIndex(
        process.env.PINECONE_INDEX,
      );

      const index = await this.pineconeService.connectIndex(indexName);

      await this.pineconeService.upsertVectors(
        index,
        pineconeRecord,
        namespace,
      );
    } catch (error) {
      this.myLogger.error({
        message: `Error saving doc in pinecone: ${error} for fileName: ${pdfLink} and orgId: ${orgId}`,
      });
      throw new Error(error);
    }
  }

  async deleteDocFromPineCone({
    pdfLink,
    orgId,
  }: {
    pdfLink: string;
    orgId: string;
  }) {
    try {
      const org = await this.organizationService.getOrganizationDoc(orgId);
      const namespace = orgId;

      const indexName = await this.pineconeService.createIndex(
        process.env.PINECONE_INDEX,
      );

      const index = await this.pineconeService.connectIndex(indexName);

      let qaPairs: any = org.connections.staticData.find(
        (staticData) => staticData.keyId === pdfLink,
      )?.data;

      let ids = qaPairs.map((pair) => pair._id);

      //delete the ids
      await this.pineconeService.deleteVectors(index, namespace, ids);
    } catch (error) {
      this.myLogger.log({
        message: `Error deleting doc from pinecone: ${error}`
      });
      throw new Error(error);
    }
  }

  async parseQnAPairs(responseText) {
    const pairs = responseText.split('\n\n');

    let qnaArray = [];

    pairs.forEach((pair) => {
      const [questionPart, answerPart] = pair.split('\n');
      const question = questionPart.replace('user: ', '').trim();
      const answer = answerPart.replace('assistant: ', '').trim();

      qnaArray.push({ question, answer });
    });

    return qnaArray;
  }

  async getDocEmbedded({ qaPairs }) {
    const embeddings = [];
    try {
      for (const pair of qaPairs) {
        const res = await this.openai.createEmbedding({
          input: pair.question,
          model: 'text-embedding-ada-002',
        });

        const embedding = res?.data?.data;
        if (embedding) {
          embeddings.push(embedding);
        } else {
          // Handle case where no embedding is returned
          
        }
      }
    } catch (error) {
      throw new Error(error.message);
    }
    return embeddings;
  }
}
