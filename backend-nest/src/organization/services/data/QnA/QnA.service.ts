import { Injectable } from '@nestjs/common';
import { CreateQnADto } from '../../../dto/qna.dto';
import { IntegrationService } from '../../integrations/integration.service';
import getId from 'get-short-id';
import { PROVIDERS } from '../../../../lib/constant';
import { v4 as uuidv4 } from 'uuid';
import { MongoOrganizationService } from 'src/mongo/service/organization/mongo-organization.service';
import { PineconeService } from '../../../../vector/services/pinecone/pinecone.service';
import { OrganizationService } from '../../organization.service';
import { Configuration, OpenAIApi } from 'openai';

@Injectable()
export class QnAService {
  private openai;
  constructor(
    private readonly integrationService: IntegrationService,

    private readonly organizationService: OrganizationService,

    private readonly pineconeService: PineconeService,
    private readonly mongoOrganizationService: MongoOrganizationService,
  ) {
    const configuration = new Configuration({
      apiKey: process.env.OPENAI_API_KEY,
    });
    this.openai = new OpenAIApi(configuration);
  }

  async create(createQnADto: CreateQnADto, orgId: string, userId: string) {
    const accountId = getId({ count: 15 });

    const qaPairs = createQnADto.qna.map((pair) => {
      return { _id: uuidv4(), ...pair };
    });

    const staticData = {
      providerName: PROVIDERS.QnA,
      accountId,
      author: createQnADto.userName,
      name: createQnADto.name,
      userId,
      data: qaPairs,
    };

    await this.integrationService.saveStaticData(orgId, staticData);

    // Return a response with the Q&A pair IDs
    return {
      providerName: staticData.providerName,
      accountId: staticData.accountId,
      author: staticData.author,
      name: staticData.name,
      userId: staticData.userId,
      data: qaPairs.map((pair) => pair._id), // Returning only the IDs
    };
  }

  async addQnAPair(
    orgId: string,
    accountId: string,
    question: string,
    answer: string,
  ) {
    const qnaPair = { _id: uuidv4(), question, answer };
    const org = await this.mongoOrganizationService.findByIdAndUpdate(
      orgId,
      {
        $push: {
          'connections.staticData.$[elem].data': qnaPair,
        },
      },
      {
        new: true,
        arrayFilters: [{ 'elem.accountId': accountId }],
      },
    );

    if (!org) {
      throw new Error('Organization not found.');
    }

    // Get the embedding for the new QnA pair
    const { questionEmbedding } = await this.getDocEmbedding(question);

    const questionEmbeddingArray = questionEmbedding[0].embedding;

    const pineconeRecord = [
      {
        id: qnaPair._id,
        values: questionEmbeddingArray,
        metadata: {
          question: question,
          answer: answer,
          type: PROVIDERS.QnA,
          accountId,
        },
      },
    ];

    const namespace = orgId;
    const indexName = await this.pineconeService.createIndex(
      process.env.PINECONE_INDEX,
    );

    const index = await this.pineconeService.connectIndex(indexName);

    await this.pineconeService.upsertVectors(index, pineconeRecord, namespace);

    return { message: 'QnA pair added successfully.', data: qnaPair };
  }

  async updateQnAPair(
    orgId: string,
    accountId: string,
    qnaId: string,
    question?: string,
    answer?: string,
  ) {
    const update: any = {};
    if (question) {
      update['connections.staticData.$[outer].data.$[inner].question'] =
        question;
    }
    if (answer) {
      update['connections.staticData.$[outer].data.$[inner].answer'] = answer;
    }

    const org = await this.mongoOrganizationService.findByIdAndUpdate(
      orgId,
      { $set: update },
      {
        new: true,
        arrayFilters: [
          { 'outer.accountId': accountId },
          { 'inner._id': qnaId },
        ],
      },
    );

    if (!org) {
      throw new Error('Organization not found.');
    }

    // Get the embedding for the new QnA pair
    const { questionEmbedding } = await this.getDocEmbedding(question);
    const questionEmbeddingArray = questionEmbedding[0].embedding;

    const pineconeRecord = [
      {
        id: qnaId,
        values: questionEmbeddingArray,
        metadata: {
          question: question,
          answer: answer,
          type: PROVIDERS.QnA,
          accountId,
        },
      },
    ];

    const namespace = orgId;
    const indexName = await this.pineconeService.createIndex(
      process.env.PINECONE_INDEX,
    );

    const index = await this.pineconeService.connectIndex(indexName);

    let result = await this.pineconeService.upsertVectors(
      index,
      pineconeRecord,
      namespace,
    );

    return { message: 'QnA pair updated successfully.' };
  }

  async deleteQnAPair(orgId: string, accountId: string, qnaId: string) {
    try {
      const update = {
        $pull: {
          'connections.staticData.$[outer].data': { _id: qnaId },
        },
      };

      const options = {
        new: true,
        arrayFilters: [{ 'outer.accountId': accountId }],
      };

      let org = await this.mongoOrganizationService.findByIdAndUpdate(
        orgId,
        update,
        options,
      );

      if (!org) {
        throw new Error('Organization not found or QnA pair not deleted.');
      }

      // Delete the QnA pair from Pinecone
      const namespace = orgId;

      const indexName = await this.pineconeService.createIndex(
        process.env.PINECONE_INDEX,
      );

      const index = await this.pineconeService.connectIndex(indexName);

      await this.pineconeService.deleteSpecificVector(
        [qnaId],
        index,
        namespace,
      );

      return { message: 'QnA pair deleted successfully.' };
    } catch (error) {
      // Handle any errors
      
      throw error;
    }
  }

  async getDocEmbedding(question) {
    try {
      const questionEmbeddingRes = await this.openai.createEmbedding({
        input: question,
        model: 'text-embedding-ada-002',
      });

      const questionEmbedding = questionEmbeddingRes?.data?.data;

      if (questionEmbedding) {
        return { questionEmbedding };
      } else {
        
        return null;
      }
    } catch (error) {
      throw new Error(error.message);
    }
  }
}
