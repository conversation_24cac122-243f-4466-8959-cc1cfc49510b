import { BadRequestException, Injectable } from '@nestjs/common';
import { DeleteStaticDataDto } from 'src/organization/dto/requests/delete.dto';
import { PineconeService } from 'src/vector/services/pinecone/pinecone.service';
import { OrganizationService } from '../organization.service';
import { EditDataSourceDto } from 'src/organization/dto/requests/post.dto';
import { PROVIDERS } from '../../../lib/constant';
import { GoogleDocsService } from './google-docs/goole-docs.service';
import { SiteService } from './site/site.service';
import { QnAService } from './QnA/QnA.service';
import { MongoAgentService } from 'src/mongo/service/agent/mongo-agent.service';

@Injectable()
export class DataService {
  constructor(
    private readonly qnaService: QnAService,
    private readonly organizationService: OrganizationService,
    private readonly pineconeService: PineconeService,
    private readonly googleDocsService: GoogleDocsService,
    private readonly mongoAgentService: MongoAgentService,
  ) {}

  async listStaticData(orgId: string) {
    const obj = await this.organizationService.getOrganization(
      { _id: orgId },
      { 'connections.staticData': 1 },
    );
    return {
      id: obj._id,
      staticData: obj.connections.staticData,
    };
  }

  async deleteStaticData(
    params: DeleteStaticDataDto,
    ids: string,
    userId?: string,
    isAdmin = false,
  ) {
    const existingAgent = await this.mongoAgentService.checkDataSources(
      params.orgId,
      params.accountId,
    );
    if (existingAgent.length > 0) {
      const agentNames = existingAgent.map((agent) => agent.agentName);
      throw new BadRequestException(
        'Cannot delete static data instance as it is being used by an agent',
      );
    }
    let recordIds = ids ? ids.split(' ') : [];
    const { accountId, orgId } = params;
    const org = await this.organizationService.getOrganization(
      { _id: orgId },
      { 'connections.staticData': 1 },
    );
    const siteData = org.connections.staticData.find(
      (site) => site.accountId === accountId,
    );
    if (!siteData) throw new Error('No site found');

    if (!(isAdmin || userId == siteData?.userId)) {
      throw new Error(`You do not have permission to delete this site`);
    }

    let siteIds = siteData?.data.map((item) => item._id);

    let provider = siteData?.providerName;

    if (provider === PROVIDERS.WEBSITE) {
      // delete the index from pinecone
      if (siteIds.length > 0) {
        const indexName = await this.pineconeService.createIndex(
          process.env.PINECONE_INDEX,
        );
        const index = await this.pineconeService.connectIndex(indexName);
        const result = await this.pineconeService.deleteVectors(
          index,
          orgId,
          siteIds,
        );
      }

      await this.organizationService.updateOrganisation(
        {
          _id: orgId,
          'connections.staticData.accountId': accountId,
        },
        {
          $pull: {
            'connections.staticData': {
              accountId: accountId,
            },
          },
        },
      );
    } else if (provider === PROVIDERS.GOOGLE_DOCS) {
      const docId = siteData?.keyId;

      //deleting from pinecone
      await this.googleDocsService.disconnectDoc({
        orgId,
        docId,
      });

      //deleting from organization (Database)
      await this.organizationService.updateOrganisation(
        {
          _id: orgId,
          'connections.staticData.accountId': accountId,
        },
        {
          $pull: {
            'connections.staticData': {
              accountId: accountId,
            },
          },
        },
      );
    } else if (provider === PROVIDERS.QnA) {
      //deleting from organization (Database)
      await this.organizationService.updateOrganisation(
        {
          _id: orgId,
          'connections.staticData.accountId': accountId,
        },
        {
          $pull: {
            'connections.staticData': {
              accountId: accountId,
            },
          },
        },
      );
    } else if(provider === PROVIDERS.PDF) {
      // delete the index from pinecone
      if (siteIds.length > 0) {
        const indexName = await this.pineconeService.createIndex(
          process.env.PINECONE_INDEX,
        );
        const index = await this.pineconeService.connectIndex(indexName);
        const result = await this.pineconeService.deleteVectors(
          index,
          orgId,
          siteIds,
        );
      }

      await this.organizationService.updateOrganisation(
        {
          _id: orgId,
          'connections.staticData.accountId': accountId,
        },
        {
          $pull: {
            'connections.staticData': {
              accountId: accountId,
            },
          },
        },
      );
    }
  }

  async editStaticDataSource(
    orgId: string,
    body: EditDataSourceDto,
    accountId: string,
  ) {
    try {
      await this.organizationService.updateOrganisation(
        {
          _id: orgId,
          'connections.staticData.accountId': accountId,
        },
        {
          $set: {
            'connections.staticData.$.name': body.name,
          },
        },
      );
      return { message: 'Resource updated successfully' };
    } catch (err) {
      throw new Error(err.message);
    }
  }

  async getStaticData(orgId: string, accountId: string) {
    const org = await this.organizationService.getOrganization(
      { _id: orgId },
      { 'connections.staticData': 1 },
    );
    const siteData = org.connections.staticData.find(
      (site) => site.accountId === accountId,
    );

    if (!siteData) throw new Error(`No static data found`);
    const transformedData: any = siteData.data.map((item: any) => ({
      id: item._id,
      metadata: {
        content: `${item.question}\n${item.answer}`,
      },
    }));

    const newResponse = {
      ...siteData,
      data: transformedData,
    };
    return newResponse;
  }
}
