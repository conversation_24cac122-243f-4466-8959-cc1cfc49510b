/* 
contains the code for save conversation, get conversation, and message response public API
*/
import { BadRequestException, Injectable, InternalServerErrorException, NotFoundException, UnauthorizedException } from '@nestjs/common';
import { AxiosError, AxiosRequestConfig, AxiosResponse } from 'axios';
import { Response, query } from 'express';
import { HttpStatus } from '@nestjs/common/enums';
import { KINDS } from 'src/lib/constant';
import { MongoAgentService } from 'src/mongo/service/agent/mongo-agent.service';
import { V1EmailConversationDto, V1InboundConversationDto } from 'src/organization/dto/customInbound.dto';
import { MongoConversationService } from 'src/mongo/service/conversations/mongo-conversations.service';
import { CreateConversationDto } from 'src/mongo/dto/conversations/create.dto';
import { SessionService } from 'src/session/session.service';
import { MongoOrganizationService } from 'src/mongo/service/organization/mongo-organization.service';
import { TokensService } from 'src/tokens/tokens.service';
import { TiktokenService } from 'src/vector/services/tiktoken.service';
import { ObjectId } from 'mongodb';
import { MyLogger } from 'src/logger/logger.service';

interface rolloverEventDto {
  reason: string;
  date: Date | string;
  rollover: boolean;
  contactId: string;
  responded?: boolean;
}

@Injectable()
export class Publicv1Service {
  constructor(
    private readonly mongoAgentService: MongoAgentService,
    private readonly mongoConversationService: MongoConversationService,
    private readonly sessionService: SessionService,
    private readonly mongoOrganizationService: MongoOrganizationService,
    private readonly tokenService: TokensService,
    private readonly tiktokenService: TiktokenService,
    private readonly logger: MyLogger,
  ) { }

  async processConversation(
    reqId: string,
    capriToken: string,
    body: V1InboundConversationDto & V1EmailConversationDto,
  ) {
    this.logger.log({
      message: `Public API | reqId: ${reqId} | body: ${JSON.stringify(
        body,
      )}`,
      context: `publicv1Service.processConversation`,
    });

    var rolloverEvent: rolloverEventDto = {
      rollover: false,
      reason: '',
      date: new Date().toISOString(),
      contactId: body.sessionId,
      responded: false,
    };

    try {
      const {
        agent: agentId,
        sessionId,
        message,
        type = 'inbound',
        history = undefined,
      } = body;

      const token = await this.tokenService.getTokenByToken({
        token: capriToken,
      });
      if (!token?.token) throw new UnauthorizedException('Invalid token');
      const orgId = token?.orgId;

      // const { orgId, agentName } = agent;
      const isOutbound = type === 'outbound';
      let aiProviderName = '';
      if (isOutbound) {
        // case : outbound message
        await this.saveConversation(reqId, {
          orgId,
          sessionId,
          message,
          type,
          agentId,
          aiProviderName,
          saveToken: false,
        });
        return {
          finalResponse: '',
          actions: [],
        };
      }
      // case : inbound message
      const agent = await this.mongoAgentService.getAgent({
        _id: agentId,
        orgId,
      });
      if (!agent) {
        throw new BadRequestException(
          `Agent Id ${agentId} not found in your organization ${orgId}`,
        );
      }

      aiProviderName = agent.aiProvider.accountName;

      // get conversation history
      const conversations = await this.getConversation(reqId, {
        orgId,
        sessionId,
        history,
      });
      // save conversation history
      await this.saveConversation(reqId, {
        orgId,
        sessionId,
        message,
        type,
        agentId,
        aiProviderName,
      });

      const organization = await this.mongoOrganizationService.getOrganization({
        _id: orgId,
      });
      if (!organization)
        throw new BadRequestException(
          'Organization not found for the provided token',
        );

      const {
        prompt = undefined,
        promptId = undefined,
        training = undefined,
        outreach = undefined,
        knowledge = undefined,
        task = undefined,
      } = body;

      // pass to the bot
      const botResponse: any = await this.sessionService.ghlWebhook({
        conversations,
        query: message,
        agentId: agentId,
        agentDetails: agent,
        organizationDetails: organization,
        contactId: sessionId,
        capriToken,
        prompt_id: promptId,
        prompt,
        task,
        knowledge,
        training,
        outreach,
        contactDetails: {
          contactTimezone: undefined,
          tags: [],
          customFields: [],
          customValues: [],
        },
        reqId,
      });

      let visibleActions = [];
      let tokensCount: number = botResponse?.usage_tokens_total || 0;
      if ((botResponse?.botResponseForEachDataSource || []).length > 0) {
        visibleActions = botResponse.botResponseForEachDataSource.map(
          (action) => {
            const actionElem = {
              accountName: action.accountName,
              action: action.action,
              kind: action.kind,
              silent: action?.silent || false,
            };
            if (action.calendarId) actionElem['calendarId'] = action.calendarId;
            if (action.locationId) actionElem['locationId'] = action.locationId;
            if (action.eventData?.tag?.name)
              actionElem['tagName'] = action.eventData.tag.name;
            if (action.eventData?.startDate)
              actionElem['startDate'] = action.eventData.startDate;
            if (action.eventData?.endDate)
              actionElem['endDate'] = action.eventData.endDate;
            if (action?.errors) actionElem['error'] = action.errors;
            return actionElem;
          },
        );
      }

      let botResponseForRollover = botResponse?.rolloverDetails;

      if (botResponseForRollover?.rollover && !botResponse?.finalResponse) {
        rolloverEvent.rollover = true;
        rolloverEvent.reason = botResponseForRollover?.rolloverReason || '';
      }

      if (botResponse?.finalResponse) {
        rolloverEvent.responded = true;
        await this.saveConversation(reqId, {
          orgId,
          sessionId,
          message: botResponse.finalResponse,
          type: 'outbound',
          agentId,
          aiProviderName,
          visibleActions,
          tokensCount,
        });
      }

      // return the message to the client
      if (!botResponse) {
        throw new InternalServerErrorException(
          'Error encountered while generating bot response',
        );
      }
      return {
        finalResponse: botResponse?.finalResponse || '',
        actions: botResponse?.botResponseForEachDataSource || [],
      };
    } catch (error) {
      if (rolloverEvent.rollover === true) {
        if (rolloverEvent?.reason === '') {
          rolloverEvent.reason = error.message;
        }
        await this.handleRolloverEvent(rolloverEvent, rolloverEvent.contactId);
      }
      this.logger.log({
        message: `Error in public api function : ` + error.message,
        context: 'publicv1Service.processConversation',
      });
      throw error;
    }
  }

  // ------------------ helper methods ------------------
  async getConversation(reqId, { orgId, sessionId, history = undefined }) {
    try {
      const conversationDocs =
        await this.mongoConversationService.getConversations({
          query: {
            contactId: sessionId,
            resourceId: orgId,
          },
        });
      let conversationString = '';
      let prevDirection = '';
      let date;
      if (history) {
        date = new Date();
        if (history < 1) date.setMinutes(date.getMinutes() - 1);
        else date.setHours(date.getHours() - history);
      }
      for (const conversationDoc of conversationDocs) {
        let prevDirection = '';
        let checkFirstMessage = true;
        for (const conversation of conversationDoc.conversation) {
          if (history) {
            if (conversation.dateAdded < date) continue;
            else if (
              checkFirstMessage &&
              conversation.direction === 'outbound'
            ) {
              conversationString += `assistant: ` + conversation?.body + '\n\n';
              prevDirection = 'outbound';
              checkFirstMessage = false;
            }
          }
          if (conversation.direction === 'inbound') {
            if (prevDirection === 'inbound') conversationString += '\n';
            conversationString += `user: ` + conversation.body + '\n';
            prevDirection = 'inbound';
          } else if (conversation.direction === 'outbound') {
            conversationString += `assistant: ` + conversation.body + '\n\n';
            prevDirection = 'outbound';
          }
        }
      }
      if (prevDirection === 'inbound') conversationString += `\n`;
      return conversationString;
    } catch (err) {
      this.logger.log({
        message:
          `error > getconversation | reqId ${reqId} | message: ` + err.message,
      });
      return '';
    }
  }

  async saveConversation(
    reqId,
    {
      orgId,
      sessionId,
      message,
      type,
      agentId,
      aiProviderName,
      visibleActions = [],
      errors = [],
      status = 'delivered',
      saveToken = true,
      tokensCount = 0,
    },
  ) {
    try {
      if (message) {
        const existingConvoRecord =
          await this.mongoConversationService.isAlreadySaved({
            contactId: sessionId,
            resourceId: orgId,
          });

        if (!(type == 'outbound' && saveToken)) tokensCount = 0;
        if (existingConvoRecord) {
          const updateConversation = {
            $addToSet: {
              conversation: {
                body: message,
                dateAdded: new Date(),
                direction: type,
                aiProvider: aiProviderName,
                tokens: tokensCount,
                actions: visibleActions || [],
                status: status,
              },
            },
          };
          updateConversation['$set'] = {
            agentId,
            channel: 'Public API',
          };
          if (tokensCount) {
            updateConversation['$inc'] = {
              totalTokens: tokensCount,
            };
          }
          await this.mongoConversationService.updateConversation({
            query: {
              _id: existingConvoRecord._id.toString(),
            },
            updateBody: updateConversation,
          });
          // save to pinecone
        } else {
          const createConversation: CreateConversationDto = {
            conversation: [
              {
                body: message,
                dateAdded: new Date(),
                direction: type,
                tokens: tokensCount,
                aiProvider: aiProviderName,
                actions: visibleActions || [],
                status: 'delivered',
              },
            ],
            contactId: sessionId,
            resourceId: orgId,
            orgId,
            agentId,
            totalTokens: tokensCount,
            updatedAt: new Date(),
            channel: 'Public API',
          };
          await this.mongoConversationService.createConversation(
            createConversation,
          );
        }
      }
    } catch (error) {
      this.logger.log({
        message:
          `@error | reqId: ${reqId} | saving conversation : ` + error.message,
      });
    }
  }

  async handleRolloverEvent(
    rolloverEvent: rolloverEventDto,
    locationId?: string,
  ) {
    try {
      let query = {};
      if (locationId) query['resourceId'] = locationId;
      query['contactId'] = rolloverEvent.contactId;
      const res = await this.mongoConversationService.updateConversation({
        query,
        updateBody: {
          $set: {
            rollover: rolloverEvent.rollover,
            rolloverReason: rolloverEvent.reason,
            rolloverDate: rolloverEvent.date,
          },
        },
      });
      this.logger.log({
        message: `Rollover event handled for contactId: ${rolloverEvent.contactId
          } | result: ${JSON.stringify(res)}`,
      });
    } catch (error) {
      this.logger.error({
        message: `error in handling RolloverEvent: ${error.message}`,
        context: 'handleRolloverEvent',
      });
    }
  }

  async getToken(capriToken: string, res: Response) {
    try {
      const token = await this.tokenService.getTokenByToken({
        token: capriToken,
      });
      if (!token?.token) throw new UnauthorizedException('Invalid token');
      const orgId = token?.orgId;

      let data = {
        orgId,
        apiKey: capriToken,
      };
      res.status(HttpStatus.OK).json({ data });
    } catch (error) {
      res.status(HttpStatus.BAD_REQUEST).json({ message: 'Invalid token' });
    }
  }
}
