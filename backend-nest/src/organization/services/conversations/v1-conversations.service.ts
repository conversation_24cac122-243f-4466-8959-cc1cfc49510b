import { BadRequestException, Injectable } from '@nestjs/common';
import { OrganizationService } from '../organization.service';
import { HttpStatus } from '@nestjs/common/enums';
import { RequestWithUser } from 'src/auth/auth.interface';
import { MyLogger } from 'src/logger/logger.service';
import { v4 as uuidv4 } from 'uuid';
import { GhlApisService } from 'src/api-client/services/ghl-apis/ghl-apis.service';
import { CONNECTION_TYPES, KINDS, PROVIDERS } from 'src/lib/constant';
import { ApiclientService } from 'src/api-client/services/apiclient.service';
import { handleException } from 'helpers/handleException';
import { GetRolloverQueryParams, getSingleRolloverConversationDto } from 'src/organization/dto/conversations/conversations-params.dto';
import { MongoConversationService } from 'src/mongo/service/conversations/mongo-conversations.service';
import { MongoCredentialsService } from 'src/mongo/service/credentials/mongo-credentials.service';
import { IghlSendMessage } from 'src/api-client/dto/ghl.dto';
import { AgentService } from 'src/agent/agent.service';

@Injectable()
export class V1ConversationsService {
    constructor(
        private readonly ghlApiService: GhlApisService,
        private readonly logger: MyLogger,
        private readonly mongoConversationService: MongoConversationService,
        private readonly mongoCredentialService: MongoCredentialsService,
        private readonly agentService: AgentService,
    ){}

    private async getGhlTokens({ location }: { location: string }) {
    
        const credential = await this.mongoCredentialService.getCredential({
          keyId: location,
          kind: KINDS.GHL_CREDENTIAL,
        });
    
        const ghlToken = {
          access_token: credential?.creds?.accessToken,
          refresh_token: credential?.creds?.refreshToken,
        };
    
        if ((ghlToken?.access_token ?? ghlToken?.refresh_token) === undefined)
          throw new BadRequestException(`Invalid leadconnector connection detected. Please refresh your Capri leadconnector connection.`)
    
        return { ghlToken };
    }

    async getRolloverConversations(query: GetRolloverQueryParams) {
        try {
            let { page = 1, limit = 10, sortBy= 'rolloverDate', sortOrder = 'desc' } = query;
            page = parseInt(page.toString());
            limit = parseInt(limit.toString(), 10);
            let sortObj={}

            const token = query?.channelAccountId ? await this.getGhlTokens({ location: query?.channelAccountId }) : undefined;
            let ghlToken = token?.ghlToken;

            const searchedContactIds = []

            if (query.search && query.channelAccountId){
              const contactNameQuery = query?.search;
              const firstNameLowerCase = contactNameQuery?.split(" ")?.[0]
              // assuming search only works for searching a contact name
              const c = await this.ghlApiService.searchContacts(ghlToken, query.channelAccountId, {
                query: firstNameLowerCase
              });
              (c?.contacts ?? []).map(contact => searchedContactIds.push(contact.id))
            }


            if (['channel', 'status', 'updatedAt'].includes(sortBy) && sortOrder){
                sortObj[sortBy] = (sortOrder==="asc" ? 1 : -1);
            }else{
                sortObj['rolloverDate'] = -1;
            }

            let queryBody: Object = {
            }
            
            if (query?.status === 'replied') {
                queryBody['rollover'] = false;
            } else if (query?.status === 'rollover') {
                queryBody = {
                  rollover: true,
                }
            }
            if (['replied', 'rollover'].includes(query?.status)){
              delete sortObj['status'];
            }
            if (!query?.orgId) throw new BadRequestException('Organization ID is required');
            else queryBody['orgId'] = query?.orgId;
            if (query?.agentId) queryBody['agentId'] = query?.agentId;
            if (query?.channelAccountId) queryBody['resourceId'] = query?.channelAccountId;
            if (query.channels) {
              const ch = query.channels.split(',');
              queryBody['type'] = {
                $in: ch,
              };
            }
            if (query?.channel) queryBody['channel'] = {
              $regex: new RegExp(query?.channel), $options: 'i',
            };
            if (searchedContactIds.length > 0) {
              queryBody['contactId'] = {
                $in: searchedContactIds,
              };

              this.logger.log({
                message: `Found ${searchedContactIds.length} contacts from the search query : ${query?.search}`,
                context: `v1ConversationService.getRolloverConversations`
              })
            }
            else if (query?.contactId) queryBody['contactId'] = query?.contactId;

            if (Object.keys(queryBody).length === 0) {
              this.logger.log({
                message: `No parameter passed, At least one parameter is required'`,
                context: `v1ConversationService.getRolloverConversations`,
              });
              throw new BadRequestException('No parameter passed');
            }

            if (query.dateFrom && query.dateTo){
              queryBody['updatedAt'] = {
                  $gte: new Date(query.dateFrom), // Start date
                  $lte: new Date(query.dateTo)  // End date
              }
            }

            
            const allConversations =
              await this.mongoConversationService.getPaginatedConversations(
                {
                  query: queryBody,
                  projection: {
                    conversation: { $slice: -1 },
                    type: 1,
                    agentId: 1,
                    channel: 1,
                    rollover: 1,
                    rolloverReason: 1,
                    rolloverDate: 1,
                    resourceId: 1,
                    conversationId: 1,
                    contactId: 1,
                    disconnectBot: 1,
                    addedAsSavedExample: 1,
                  },
                },
                {
                  page,
                  limit,
                  sort: sortObj,
                },
              );

            const searchConversationsPromise = [];
            const totalConversations = await this.mongoConversationService.getConversationsCount(queryBody);
            let result = [];
            // for (let i = 0; i < allConversations.length; i++) {
            for(const conversation of allConversations){
              // let l = allConversations.length - 1;
              let agentName = conversation?.agentId
                ? ((
                    await this.agentService.getAgents(
                      {
                        _id: conversation.agentId,
                      },
                      {
                        agentName: 1,
                      },
                    )
                  )?.[0])?.agentName || ''
                : '';
              
              if (query?.channelAccountId) searchConversationsPromise.push(this.ghlApiService.searchConversations(ghlToken, conversation.resourceId, { contactId: conversation.contactId}))

              result.push({
                agentId: conversation.agentId,
                agentName,
                channelName: conversation.channel,
                lastMessage: conversation.conversation[0]?.body || '',
                lastMessageAt: conversation.conversation[0]?.dateAdded,
                lastMessageStatus: conversation.conversation[0]?.status,
                rollover: conversation.rollover,
                rolloverReason: conversation.rolloverReason,
                rolloverDate: conversation.rolloverDate,
                contactId: conversation.contactId,
                channelAccountId: conversation.resourceId,
                addedAsSavedExample: conversation?.addedAsSavedExample ?? false,
                disconnectBot: conversation?.disconnectBot ?? false,
                channel: conversation.type,
                conversationId: conversation.conversationId,
                refUrl: `https://app.gohighlevel.com/v2/location/${conversation.resourceId}/contacts/detail/${conversation.contactId}`,
              });
            }

            if (query.channelAccountId){
              const searchedConversations = await Promise.all(searchConversationsPromise);
              for (let index = 0; index < searchedConversations.length; index++) {
                const c = searchedConversations[index];
                const currGhlConversation = c?.conversations?.[0];
                if (!currGhlConversation) continue;
                const contactName = currGhlConversation?.fullName ?? currGhlConversation?.contactName ?? 'Unknown Contact';
                result[index] = {
                  ...result[index],
                  contactName,
                  conversationId: currGhlConversation?.id
                };
              }
            }

            if (sortBy === 'agentName'){
              if (sortOrder == 'asc') {
                result.sort((a, b) =>
                  (a?.agentName || '').localeCompare((b?.agentName || ''))
                );
              } else {
                result.sort((a, b) => 
                  (b?.agentName || '').localeCompare((a?.agentName || ''))
                );
              }
            }

            if (limit == 0) throw new BadRequestException('Limit cannot be 0');
            let totalPage = Math.ceil(totalConversations / limit);
            if (totalPage == 0) totalPage = 1;
            return {
                totalPage,
                currentPage: page,
                result,
            }
        } catch (error) {
            this.logger.error({
                message: `Failed to retrieve rollover conversations for channel account ID ${query?.channelAccountId} | reason : ${error.message}`,
                context: `v1ConversationService.getRolloverConversations`,
            });
            throw error;
        }
    }

    async getSingleRolloverConversation(
        req: RequestWithUser,
        query: getSingleRolloverConversationDto,
        contactId: string,
    ) {
        try {
            
            const queryBody = {
                // 'rollover': true,
            }
            if (query?.agentId) queryBody['agentId'] = query?.agentId;
            if (contactId) queryBody['contactId'] = contactId;
            if (query?.channelAccountId) queryBody['resourceId'] = query?.channelAccountId;
            if (!contactId)
              throw new BadRequestException('Contact ID is required');
            const allConversations =
            await this.mongoConversationService.getConversations({
                query: queryBody,
            });
            if (query?.channelAccountId) query.channelAccountId = allConversations?.[0]?.resourceId;
            const agentId = allConversations?.[0]?.agentId;
            const result = [];
            let inboundCounts = 0, outboundCounts = 0;
            let rolloverDate = allConversations?.[0]?.rolloverDate;
            let agentName = agentId ? ((await this.agentService.getAgents({
                _id: agentId,
            },{
                agentName: 1,
            }))?.[0])?.agentName || '' : '';
            const conversation = allConversations?.[0]?.conversation ?? [];
            for (let i = 0; i < conversation.length; i++) {
                if (conversation?.[i]?.direction == 'inbound') inboundCounts++;
                if (conversation?.[i]?.direction == 'outbound') outboundCounts++;
            }
            let channelName = allConversations?.[0]?.channel || '';
            let refurl = `https://app.gohighlevel.com/v2/location/${query?.channelAccountId}/contacts/detail/${contactId}`;
            let j = conversation.length - 2;
            if (query?.showAll === 'true') {
                j = 0;
            }
            for (let i = j; i < conversation.length; i++) {
                if (conversation?.[i]) {
                    result.push({
                        message: conversation?.[i]?.body || '',
                        dateAdded: conversation?.[i].dateAdded,
                        direction: conversation?.[i].direction || '',
                        status: conversation?.[i].status || '',
                        messageType: conversation?.[i].messageType || '',
                    });
                }
            }
            return {
              contactId,
              rollover: allConversations?.[0]?.rollover,
              locationId: allConversations?.[0]?.resourceId,
              agentName,
              agentId: agentId,
              channelName,
              refurl,
              rolloverDate,
              inboundCounts,
              outboundCounts,
              conversation: result,
            };
        } catch (error) {
            this.logger.error({
                message: `Failed to retrieve rollover conversation for contactId: ${contactId} | reason : ${error.message}`,
                context: `v1ConversationService.getaRolloverConversation`,
            })
            throw error;
        }
    }


    async sendMessage( type: 'ghl', locationId:string, body: IghlSendMessage, orgId: string) {
        try {
            if (body.type === 'Custom') throw new BadRequestException('Custom message type is not supported', 'CustomMessageTypeNotAllowed');
            const cred = await this.mongoCredentialService.getCredential({
                keyId: locationId,
                kind: KINDS.GHL_CREDENTIAL,
                organizationId: orgId,
                });
            if (!locationId) throw new BadRequestException("No locationId passed from frontend")
            if (!cred?.creds) throw new BadRequestException('Channel not connected to the organization', 'CredentialNotFound');
    
            const tokens = {
                access_token: cred?.creds?.accessToken,
                refresh_token: cred?.creds?.refreshToken,
            };
    
            const sentConversation = await this.ghlApiService.sendMessage(tokens, locationId, body);
            if (sentConversation?.error) throw new BadRequestException(sentConversation?.error, 'FailedToSendMessage');
            await this.mongoConversationService.updateConversation({
              query: {
                orgId,
                resourceId: locationId,
                contactId: body.contactId,
              },
              updateBody: {
                $set: {
                  rollover: false,
                  rolloverReason: '',
                  rolloverDate: '',
                },
              },
            });
    
            return {
                message: 'Message sent successfully',
            };
        } catch (error) {
            this.logger.error({
                message: `Failed to send GHL message for locationId: ${locationId} from rollovers page | reason : ${error.message}`,
                context: `v1ConversationService.sendGhlMessage`,
            });
            throw error;
        }
    }

    async markRolloverStatus(contactId: string, body: {set: boolean}){
      try {
        await this.mongoConversationService.updateConversation({
          query: {
            contactId
          },
          updateBody: {
            $set: {
              rollover: body.set,
              rolloverReason: '',
              rolloverDate: ''
            }
          },
        })
      } catch (error) {
        this.logger.error({
          message: `error in changing rollover status: ${error.message}`,
          context: `v1ConversationService.markRolloverStatus`,
        });
      }
    }
}
