const SCOPES = 'crm.schemas.quotes.read%20cms.functions.read%20crm.schemas.contacts.write%20cms.functions.write%20cms.knowledge_base.articles.read%20cms.knowledge_base.settings.read%20crm.schemas.subscriptions.read%20crm.schemas.orders.read%20communication_preferences.read%20cms.knowledge_base.settings.write%20oauth%20settings.users.teams.read%20conversations.read%20conversations.write%20account-info.security.read%20crm.objects.users.read%20crm.objects.contacts.write%20crm.objects.users.write%20settings.currencies.read%20crm.schemas.custom.read%20crm.objects.custom.read%20crm.objects.custom.write%20communication_preferences.read_write%20crm.lists.write%20crm.lists.read%20settings.users.read%20crm.schemas.contacts.read%20crm.objects.contacts.read%20crm.schemas.companies.read';

export const getHubspotConnectionURL = (clientId, redirectUri?, state?): string =>
  `https://app.hubspot.com/oauth/authorize?client_id=${clientId}&redirect_uri=${
    redirectUri ?? `http://localhost`
  }&scope=crm.schemas.quotes.read%20crm.schemas.contacts.write%20crm.objects.subscriptions.read%20crm.schemas.subscriptions.read%20crm.schemas.orders.read%20communication_preferences.read%20oauth%20settings.users.teams.read%20conversations.read%20conversations.write%20account-info.security.read%20crm.objects.users.read%20crm.objects.contacts.write%20crm.objects.users.write%20settings.currencies.read%20communication_preferences.read_write%20crm.lists.write%20crm.lists.read%20settings.users.read%20crm.schemas.contacts.read%20crm.objects.contacts.read%20crm.schemas.companies.read&optional_scope=cms.functions.read%20cms.functions.write%20crm.schemas.custom.read%20cms.knowledge_base.articles.read%20cms.knowledge_base.settings.read%20crm.objects.custom.read%20crm.objects.custom.write%20cms.knowledge_base.settings.write&state=${state}`;

  /* 
  crm.objects.subscriptions.read%20
  */