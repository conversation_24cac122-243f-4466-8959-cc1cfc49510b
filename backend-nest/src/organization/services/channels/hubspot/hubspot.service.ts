import { Injectable, Inject } from '@nestjs/common';
import { OrganizationService } from '../../organization.service';
import { PROVIDERS, KINDS, GHL_MESSAGE_TYPE_MAP } from 'src/lib/constant';
import { ObjectId } from 'mongodb';
import { MyLogger } from 'src/logger/logger.service';
import { HubspotAPIService } from 'src/api-client/services/hubspot/hubspot-api.service';
import { MongoCredentialsService } from 'src/mongo/service/credentials/mongo-credentials.service';
import { v4 as uuidv4 } from 'uuid';
import { HydratedCredentialDocument } from 'src/mongo/schemas/credential/credentials.schema';

@Injectable()
export class HubspotService {
  constructor(
    private readonly hubspotAPIservice: HubspotAPIService,
    private readonly organizationService: OrganizationService,
    private readonly mongoCredentialsService: MongoCredentialsService,
    private readonly logger: MyLogger,
  ) {}

  async connectHubspot(orgId: string, code: string) {
    const org = await this.organizationService.getOrganization({ _id: orgId });
    if (!org) throw new Error('Organization not found');
    const newConnection = await this.hubspotAPIservice.exchangeCode(code);
    let channelData: object = {};
    if (newConnection) {
      
      const accountInfo = await this.hubspotAPIservice.getAccountDetails(
        newConnection,
        undefined,
      );
      var keyId = accountInfo?.portalId?.toString();
      const underDiffCred = await this.mongoCredentialsService.ifExists({
        kind: KINDS.HUBSPOT_CREDENTIAL,
        keyId,
        organizationId: { $ne: orgId },
      });
      if (underDiffCred) {
        throw new Error(
          'Connection failed! The channel is already connected to another organization.',
        );
      }

      const credentialExists = await this.mongoCredentialsService.getCredential(
        {
          kind: KINDS.HUBSPOT_CREDENTIAL,
          keyId: accountInfo?.portalId?.toString(),
          organizationId: orgId,
        },
      );
      let credentials: HydratedCredentialDocument;
      if (!credentialExists) {
        credentials = await this.mongoCredentialsService.createCredentials({
          kind: 'HubspotCredential',
          keyId: accountInfo?.portalId?.toString(),
          creds: { ...newConnection },
          timezone: accountInfo?.timeZone,
          organizationId: orgId,
          type: 'channel',
        });
        channelData = {
          credentialId: credentials._id,
          providerName: PROVIDERS.HUBSPOT,
          accountId: uuidv4(),
          timezone: accountInfo?.timeZone,
          name: 'Hubspot Channel',
          keyId,
        };
        await this.organizationService.updateOrganisation(
          { _id: orgId },
          { $push: { 'connections.channels': channelData } },
        );
      } else {
        credentials = credentialExists;
        await this.mongoCredentialsService.updateCredentials({
          query: {
            _id: credentialExists._id,
          },
          updateBody: {
            creds: { ...newConnection },
            timezone: accountInfo?.timeZone,
          },
        });
        await this.organizationService.updateOrganisation(
          {
            _id: orgId,
            'connections.channels.keyId': accountInfo?.portalId?.toString(),
          },
          {
            $set: { 'connections.channels.$.timezone': accountInfo?.timeZone },
          },
        );
        const org = await this.organizationService.getOrganization({
          _id: orgId,
          'connections.channels.keyId': accountInfo?.portalId?.toString(),
        });
        const channel = org?.connections?.channels.find(
          (channel) => channel.keyId === accountInfo?.portalId?.toString(),
        );
        channelData = {
          credentialId: credentials._id,
          name: channel.name,
          providerName: channel.providerName,
          accountId: channel.accountId,
          timezone: channel.timezone,
          keyId: channel.keyId,
        };
      }
      return channelData;
    } else {
      throw new Error('Connection failed!');
    }
  }

  async updateHubspotChannelName(orgId: string, accountId: string, name: string){
    const org =  await this.organizationService.updateOrganisation(
      { _id: orgId, 'connections.channels.accountId': accountId },
      { $set: { 'connections.channels.$.name': name } },
    );
    return org?.connections?.channels.find(channel => channel.accountId === accountId);
  }
}
