import { Injectable, Res } from '@nestjs/common';
import { MongoCredentialsService } from 'src/mongo/service/credentials/mongo-credentials.service';
import { SCOPES, USER_SCOPES } from './slack.constants';
import { Request, Response } from 'express';
import { ApiclientService } from 'src/api-client/services/apiclient.service';
import { MyLogger } from 'src/logger/logger.service';
import { SlackApiService } from 'src/api-client/services/slack-api/slack-api.service';

@Injectable()
export class SlackService {
  constructor(
    private readonly mongoCredentialsService: MongoCredentialsService,
    private readonly logger: MyLogger,
    private readonly apiClientService: ApiclientService,
    private readonly slackApiService: SlackApiService,
  ) {}

  async verifySlackSignature(Request: Request, res: Response) {
    const signature = Request.headers['x-slack-signature'];
    const timestamp = Request.headers['x-slack-request-timestamp'];
    // The request timestamp is more than five minutes from local time.
    // It could be a replay attack, so let's ignore it.
    if (Date.now() - Number(timestamp) > 5 * 60 * 1000) {
      return res.sendStatus(403);
    }
  }

  getRedirectURI(state?: string) {
    const REDIRECT_URI =
      `https://slack.com/oauth/v2/authorize?scope=${SCOPES}&user_scope=${USER_SCOPES}&redirect_uri=${
        process.env.BACKEND_URL + '/channels/slack/connect'
      }&client_id=${process.env.SLACK_CLIENT_ID}` +
      (state ? `&state=${state}` : '');
    return REDIRECT_URI;
  }

  async connectSlack(code?: string) {
    const tokens = await this.slackApiService.getAccessToken(code);
    if (!tokens.ok) {
      throw new Error(
        `Error while connecting Slack | tokens - ${JSON.stringify(tokens)}`,
      );
    }
    return tokens;
  }

  async deleteSlackConnection(tokens) {
    try {
      await this.slackApiService.revokeTokens(tokens);
    } catch (err) {
      this.logger.log({
        message: err.message,
      });
    }
  }

  async joinChannel(tokens, channel: string) {
    try {
      const res = await this.slackApiService.joinChannel(tokens, channel);
      return res;
    } catch (error) {
      this.logger.error({
        message: `Error when inviting to channel ` + error.message,
        context: 'SLACK CONNECTION',
      });
      throw error;
    }
  }

  async inviteToChannel(tokens, channel: string, user: string) {
    try {
      const res = await this.slackApiService.inviteToChannel(
        tokens,
        channel,
        user,
      );
      return res;
    } catch (error) {
      this.logger.error({
        message: `Error when inviting to channel ` + error.message,
        context: 'SLACK CONNECTION',
      });
      if (error.message === 'method_not_supported_for_channel_type') {
        throw new Error(
          'Bots cannot be added to conversations and DMs in slack. For conversations you can add Capri bot by turning the conversation into a slack channel by heading into the slack channel setting',
        );
      }
    }
  }
}
