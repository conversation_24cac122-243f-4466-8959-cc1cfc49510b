import { BadRequestException, Injectable } from '@nestjs/common';
import { MongoCredentialsService } from 'src/mongo/service/credentials/mongo-credentials.service';
import { OrganizationService } from '../organization.service';
import { Response, query } from 'express';
import { HttpStatus } from '@nestjs/common/enums';
import { KINDS, PROVIDERS } from 'src/lib/constant';
import { AgentService } from 'src/agent/agent.service';
import { RequestWithUser } from 'src/auth/auth.interface';
import { ChatHqService } from 'src/organization/services/channels/chat-hq/chat-hq.service';
import { MyLogger } from 'src/logger/logger.service';
import { PodiumService } from './podium/podium.service';
import { SlackService } from './slack/slack.service';
import { v4 as uuidv4 } from 'uuid';
import { handleException } from 'helpers/handleException';

@Injectable()
export class ChannelsService {
  constructor(
    private readonly organizationService: OrganizationService,
    private readonly mongoCredentialsService: MongoCredentialsService,
    private readonly chathqService: ChatHqService,
    private readonly agentService: AgentService,
    private readonly podiumService: PodiumService,
    private readonly myLogger: MyLogger,
    private readonly slackService: SlackService,
  ) {}

  async deleteConnectionfromOrg(
    params,
    res: Response,
    userId?: string,
    isAdmin = false,
  ) {
    try {
      const { connections } = await this.organizationService.getOrganization({
        _id: params.orgId,
      });
      if (!connections) {
        throw new Error(`Organization with ID ${params.orgId} not found`);
      }
      if ((connections?.channels || []).length > 0)
        var channel = (connections.channels || []).find(
          (channel) => channel.accountId === params.accountId,
        );
      if (!channel) {
        this.myLogger.log({
          message: `Channel with ID ${params.accountId} not found`,
        });
      }

      const deletedChannel = await this.organizationService.updateOrganisation(
        {
          _id: params.orgId,
          'connections.channels.accountId': params.accountId,
        },
        {
          $pull: {
            'connections.channels': {
              accountId: params.accountId,
            },
          },
        },
      );

      res
        .status(HttpStatus.OK)
        .json({ message: 'Deleted', data: { accountId: params.accountId } });

      const deletedCredential =
        await this.mongoCredentialsService.deleteCredential({
          _id: channel.credentialId,
        });

      if (channel.providerName === PROVIDERS.CHATHQ_CHANNEL) {
        // if (deletedCredential?.kind === KINDS.CHATHQ_CREDENTIAL) {
        //     await this.chathqService.deleteWebhook(
        //         deletedCredential?.creds?.accessToken,
        //         deletedCredential?.keyId,
        //         deletedCredential?.webhookId
        //     )
        // }
      } else if (deletedCredential.kind === KINDS.PODIUM_CREDENTIAL) {
        await this.podiumService.deletePodiumConnection(
          undefined,
          params.orgId,
          deletedCredential?.creds,
          deletedCredential?.webhookId,
          deletedCredential?.podiumOrgId,
        );
      } else if (deletedCredential.kind === KINDS.SLACK_CREDENTIAL) {
        await this.slackService.deleteSlackConnection(deletedCredential?.creds);
      }
    } catch (err) {
      this.myLogger.log({
        message: err.message,
      });
      res
        .status(HttpStatus.INTERNAL_SERVER_ERROR)
        .json({ message: err.message });
    }
  }

  async mapChannelToAgents(
    orgId: string,
    accountId: string,
    agentIds: string[],
  ) {
    const updated = await this.organizationService.updateOrganisation(
      {
        _id: orgId,
        'connections.channels.accountId': accountId,
      },
      {
        $set: {
          'connections.channels.$.agentIds': agentIds,
        },
      },
    );
    const updatedIdx = (updated?.connections?.channels ?? []).findIndex(
      (channel) => channel?.accountId === accountId,
    );

    const channel = updated?.connections?.channels[updatedIdx];
    return {
      message:
        `agents linked with the channel ` +
        (channel.name ?? '') +
        `successfully`,
    };
  }


  async getMappedAgentId(reqId: string, orgId: string, locationId: string) {
    const exists = await this.organizationService.getOrganization(
      {
        _id: orgId,
        'connections.channels.keyId': locationId,
      },
      {
        'connections.channels.$': 1,
      },
    );

    if (!exists) {
      throw new BadRequestException(`Channel with ID ${locationId} not found`);
    }

    const channel = exists.connections.channels[0];
    return {
      agentIds: channel.agentIds,
      channelName: channel.name,
      accountId: channel.accountId,
      locationId,
    };
  }

  async getAllChannels(orgId: string) {
    let {
      connections: { channels },
    } = await this.organizationService.getOrganization(
      { _id: orgId },
      { 'connections.channels': 1 },
    );

    // Flatten all agentIds arrays and remove duplicates
    const agentIds = new Set<string>(
      channels
        .flatMap(channel => channel.agentIds || [])
        .filter(id => id)
    );

    let agents = await this.agentService.getAgents(
      { _id: { $in: Array.from(agentIds) } },
      { agentName: 1 },
    );

    let agentMap = agents.map((agent) => ({
      [agent._id.toString()]: agent.agentName,
    }));

    const agentLookup = Object.assign({}, ...agentMap);

    return {
      id: orgId,
      channels: channels.map((channel) => ({
        ...channel,
        // Map each agentId to its name, if agentIds exists
        agentNames: channel.agentIds?.map(id => agentLookup[id]) || [],
        // Keep the original agentIds array
        agentIds: channel.agentIds || [],
      })),
    };
  }

  async connectSlack(res: Response, code?: string, orgId?: string) {
    try {
      if (!orgId) throw new BadRequestException('Invalid orgId received');
      if (!code) {
        const redirect_uri = this.slackService.getRedirectURI(orgId);
        res.redirect(redirect_uri);
      } else {
        const tokens = await this.slackService.connectSlack(code);
        const inviteToChannel = await this.slackService.inviteToChannel(
          tokens,
          tokens.incoming_webhook.channel_id,
          tokens.bot_user_id,
        );
        let accountId = uuidv4();
        // const encodedObj = decodeBase64ToObject<{orgId: string, userId: string}>(state);
        const existingCredential =
          await this.mongoCredentialsService.getCredential({
            keyId: tokens.incoming_webhook.channel_id,
            kind: 'SlackCredential',
            organizationId: orgId,
            type: 'channel',
          });
        let credentialId: string;
        let newChannel: any = {};
        if (existingCredential) {
          credentialId = existingCredential._id.toString();
          const credUpdateResult =
            await this.mongoCredentialsService.updateCredential(
              {
                _id: existingCredential._id.toString(),
              },
              {
                kind: 'SlackCredential',
                creds: tokens,
              },
            );
          if (existingCredential?.organizationId !== orgId) {
            throw new BadRequestException(
              "This slack channel is already connected under a different organization. Note that capri doesn't allow the same channel under different organizations",
            );
          }
          newChannel = {
            credentialId: credentialId,
            name: tokens.incoming_webhook.channel,
            providerName: PROVIDERS.SLACK,
            author: tokens.team.name,
            accountId,
            keyId: tokens.incoming_webhook.channel_id,
          };
        } else {
          const c = await this.mongoCredentialsService.createCredentials({
            organizationId: orgId,
            keyId: tokens.incoming_webhook.channel_id,
            providerName: PROVIDERS.SLACK,
            name: tokens.incoming_webhook.channel,
            creds: tokens,
            kind: 'SlackCredential',
            type: 'channel',
          });
          credentialId = c._id.toString();
          newChannel = {
            credentialId: credentialId,
            name: tokens.incoming_webhook.channel,
            providerName: PROVIDERS.SLACK,
            author: tokens.team.name,
            accountId,
            keyId: tokens.incoming_webhook.channel_id,
          };
          await this.organizationService.updateOrganisation(
            { _id: orgId },
            {
              $push: {
                'connections.channels': newChannel,
              },
            },
          );
        }

        const script = `
          <script>
            window.opener.postMessage(${JSON.stringify({
              message: 'Slack channel connected successfully!',
              ...newChannel,
            })}, '*');
            window.close();
          </script>
        `;
        return res.send(script);
      }
    } catch (error) {
      this.myLogger.error({
        context: 'SLACK CONNECTION',
        message: `Error while connecting Slack | ${error.message}`,
      });
      return res.send(
        `<body style="background-color: black; color: #000;">
            <h2 style="font-size: large; color: white;">
            Oops! Encountered an error while connecting Slack to your organization.
            <br>
            Reason - ${error.message}</h2>
        </body>`,
      );
    }
  }
}
