import { BadRequestException, HttpStatus, Injectable } from '@nestjs/common';
import { Request, Response } from 'express';
import { PodiumAPIService } from 'src/api-client/services/podium/podium-api.service';
import { decodeCode } from 'src/lib/utils';
import { MongoCredentialsService } from 'src/mongo/service/credentials/mongo-credentials.service';
import { OrganizationService } from '../../organization.service';
import { v4 as uuidv4 } from 'uuid';
import { CONNECTION_TYPES, KINDS, PROVIDERS } from 'src/lib/constant';
import { MyLogger } from 'src/logger/logger.service';

interface authDto {
  accessToken: string;
  refreshToken: string;
}
@Injectable()
export class PodiumService {
  constructor(
    private readonly podiumApiService: PodiumAPIService,
    private readonly mongoCredentialsService: MongoCredentialsService,
    private readonly organizationService: OrganizationService,
    private readonly myLogger: MyLogger,
  ) {}

  async connectPodium(code: string, orgId: string) {
    try {
      let { accessToken, refreshToken } =
        await this.podiumApiService.exchangeCode(code);
      const tokens = {
        accessToken,
        refreshToken,
      };
      const organization = await this.organizationService.getOrganizationDoc(
        orgId,
      );
      if (!organization) {
        throw new Error('Organization not found');
      }
      let locations = await this.podiumApiService.getPodiumLocation(
        undefined,
        tokens,
      );
      if ((locations || []).length === 0)
        throw new Error('No Podium location found');
      let podiumOrgId = locations?.[0]?.organizationUid;
      this.myLogger.log({
        message: `${podiumOrgId}`,
      });
      locations = locations.map((location) => {
        return {
          ...location,
          displayName: location.displayName || location.name,
        };
      });
      return {
        locations,
        podiumOrgId,
        tokens,
      };
    } catch (error) {
      this.myLogger.error({
        message: error.message,
      });
      return null;
    }
  }

  async connectPodiumLocation(
    orgId: string,
    locationId: string,
    tokens: any,
    locationName: string,
    podiumOrgId?: string,
  ) {
    let accountId = uuidv4();

    let cred;
    let matchedOrgs = await this.organizationService.getOrgs({
      query: { 'connections.channels.keyId': locationId },
      projection: {
        _id: 1,
        'connections.channels.$': 1,
      },
    });
    let otherOrgs = matchedOrgs.filter((org) => org._id.toString() !== orgId);
    if (otherOrgs.length > 0) {
      throw new BadRequestException(
        `This location is already connected with another organization`,
      );
    }
    matchedOrgs = matchedOrgs.filter((org) => org._id.toString() === orgId);
    const existingCredId =
      matchedOrgs?.[0]?.connections?.channels?.[0]?.credentialId;

    let connData;
    if (!matchedOrgs || matchedOrgs.length === 0) {
      // create new credential
      const webhook = await this.podiumApiService.createWebhook(
        tokens,
        locationId,
        podiumOrgId,
      );

      cred = await this.mongoCredentialsService.createCredentials({
        kind: 'PodiumCredential',
        type: 'channel',
        organizationId: orgId,
        podiumOrgId,
        keyId: locationId,
        creds: { ...tokens },
        webhookId: webhook?.uid,
      });
      await this.mongoCredentialsService.updateCredentials({
        query: {
          kind: 'PodiumCredential',
          type: 'channel',
          podiumOrgId,
        },
        updateBody: {
          creds: { ...tokens },
        },
      });
      await this.organizationService.updateOrganisation(
        { _id: orgId },
        {
          $push: {
            'connections.channels': {
              accountId,
              name: locationName,
              providerName: PROVIDERS.PODIUM,
              credentialId: cred._id.toString(),
              keyId: locationId,
            },
          },
        },
      );
      connData = {
        accountId,
        name: locationName,
        providerName: PROVIDERS.PODIUM,
        credentialId: cred._id.toString(),
        keyId: locationId,
        podiumOrgId,
      };
    } else {
      // update existing credential
      await this.mongoCredentialsService.updateCredentials({
        query: { kind: 'PodiumCredential',
        type: 'channel',
        podiumOrgId },
        updateBody: {
          creds: { ...tokens },
        },
      });
      const accId = matchedOrgs?.[0]?.connections?.channels?.[0]?.accountId;
      connData = {
        accountId: accId,
        name: locationName,
        providerName: PROVIDERS.PODIUM,
        credentialId: existingCredId,
        keyId: locationId,
        podiumOrgId,
      };
    }

    return connData;
  }

  async deletePodiumConnection(
    reqId: string,
    orgId: string,
    tokens,
    webhookId: string,
    podiumOrgId?: string,
  ) {
    try {
      await this.podiumApiService.deleteWebhook(tokens, webhookId, podiumOrgId);
    } catch (error) {
      this.myLogger.error({
        message:
          `reqId ${reqId} | Error in deleting podium webhook: ` + error.message,
      });
    }
  }
}
