import {
  IBotActionResponse,
  IRolloverDetails,
} from 'src/interfaces/interfaces';

export interface IRawbotResponse {
  finalResponse: string;
  usage_tokens_total: number;
  botResponseForEachDataSource?: IBotActionResponse[];
  rolloverDetails?: IRolloverDetails;
  finalMessage?: string;
}

export interface IArgContactDetails {
  tags: string[];
  customFields: {
    id: string;
    value: string;
  }[];
}
