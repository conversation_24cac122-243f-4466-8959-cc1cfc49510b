import { BadRequestException, Injectable } from '@nestjs/common';
import { MongoCredentialsService } from 'src/mongo/service/credentials/mongo-credentials.service';
import { Response, query } from 'express';
import { HttpStatus } from '@nestjs/common/enums';
import { KINDS, PROVIDERS } from 'src/lib/constant';
import { RequestWithUser } from 'src/auth/auth.interface';
import { MyLogger } from 'src/logger/logger.service';
import { OrganizationService } from '../../organization.service';
import { ApiclientService } from 'src/api-client/services/apiclient.service';
import { GhlAPIs } from 'src/api-client/lib/ghlApi';
import { MongoConversationService } from 'src/mongo/service/conversations/mongo-conversations.service';
import { MongoAgentService } from 'src/mongo/service/agent/mongo-agent.service';
import { CreateConversationDto } from 'src/mongo/dto/conversations/create.dto';
import {
  Actions,
  AppointmentAction,
} from 'src/organization/controllers/channels/ghl/actions';
import { GhlApisService } from 'src/api-client/services/ghl-apis/ghl-apis.service';
import { SessionService } from 'src/session/session.service';
import { IGhlTrigger } from 'src/mongo/schemas/agents/agents.schema';
import { IRawbotResponse } from './botresponse';
import { UserService } from 'src/user/user.service';
import { TiktokenService } from 'src/vector/services/tiktoken.service';
import { IBotActionResponse } from 'src/interfaces/interfaces';
import { ObjectId } from 'mongodb';
import { MongoContextDataService } from 'src/mongo/service/contextData/contextData.service';
import { TaskService } from 'src/gcp/services/task.service';
import { CacheService } from 'src/utility/services/cache.service';
import { SchedulerService } from 'src/utility/services/scheduler.service';
import { handleException } from 'helpers/handleException';

@Injectable()
export class GhlHelperService {
  constructor(
    private readonly organizationService: OrganizationService,
    private readonly mongoConversationService: MongoConversationService,
    private readonly mongoCredentialService: MongoCredentialsService,
    private readonly mongoAgentService: MongoAgentService,
    private readonly ghlApiService: GhlApisService,
    private readonly sessionService: SessionService,
    private readonly userService: UserService, // @Inject(CACHE_MANAGER) private cacheManager: Cache
    private readonly logger: MyLogger,
    private readonly mongoContextDataService: MongoContextDataService,
    private readonly schedulerService: SchedulerService,
  ) {}

  async addGHLTagByEmail(locationId: string, email: string, tags: string[]) {
    try {
      const credential = await this.mongoCredentialService.getCredential({
        kind: KINDS.GHL_CREDENTIAL,
        keyId: locationId,
      });
      const token = {
        access_token: credential.creds.accessToken,
        refresh_token: credential.creds.refreshToken,
      };
      const contact = await this.ghlApiService.createContact(
        token,
        locationId,
        { email, locationId },
      );
      const contactId = contact.id;
      await this.ghlApiService.addTag(token, locationId, contactId, ...tags);
    } catch (error) {
      this.logger.error({
        message: `Error while adding GHL tag by Email for ${email} ${error?.message}`,
        context: 'GhlHelperService.addGHLTagByEmail',
      });
    }
  }

  async updateGhlContactTag({
    locationId,
    contactId,
    tags,
  }: {
    locationId: string;
    contactId: string;
    tags: string[];
  }) {
    try {
      const credential = await this.mongoCredentialService.getCredential({
        kind: KINDS.GHL_CREDENTIAL,
        keyId: locationId,
      });
      const token = {
        access_token: credential.creds.accessToken,
        refresh_token: credential.creds.refreshToken,
      };
      const updatedContact = await this.ghlApiService.addTag(
        token,
        locationId,
        contactId,
        ...tags,
      );
      return updatedContact;
    } catch (error) {
      this.logger.error({
        message: `Error while adding GHL tag by contactId for contactId ${contactId} ${error?.message}`,
        context: 'GhlHelperService.addGHLTagByEmail',
      });
    }
  }
}
