import { BadRequestException, Injectable } from '@nestjs/common';
import { MongoCredentialsService } from 'src/mongo/service/credentials/mongo-credentials.service';
import { Response, query } from 'express';
import { HttpStatus } from '@nestjs/common/enums';
import { KINDS, PROVIDERS } from 'src/lib/constant';
import { RequestWithUser } from 'src/auth/auth.interface';
import { MyLogger } from 'src/logger/logger.service';
import { OrganizationService } from '../../organization.service';
import { ApiclientService } from 'src/api-client/services/apiclient.service';
import { GhlAPIs } from 'src/api-client/lib/ghlApi';
import { MongoConversationService } from 'src/mongo/service/conversations/mongo-conversations.service';
import { MongoAgentService } from 'src/mongo/service/agent/mongo-agent.service';
import { CreateConversationDto } from 'src/mongo/dto/conversations/create.dto';
import {
  Actions,
  AppointmentAction,
} from 'src/organization/controllers/channels/ghl/actions';
import { GhlApisService } from 'src/api-client/services/ghl-apis/ghl-apis.service';
import { SessionService } from 'src/session/session.service';
import { FollowUpSchema, IGhlTrigger, TriggerDocument } from 'src/mongo/schemas/agents/agents.schema';
import { IRawbotResponse } from './botresponse';
import { UserService } from 'src/user/user.service';
import { TiktokenService } from 'src/vector/services/tiktoken.service';
import { IBotActionResponse } from 'src/interfaces/interfaces';
import { ObjectId } from 'mongodb';
import { MongoContextDataService } from 'src/mongo/service/contextData/contextData.service';
import { TaskService } from 'src/gcp/services/task.service';
import { CacheService } from 'src/utility/services/cache.service';
import { SchedulerService } from 'src/utility/services/scheduler.service';
import { ConversationDocument } from 'src/mongo/schemas/conversation/conversation.schema';
import { isEventAllowedInScheduledTime } from 'src/lib/utils';
import { Schedule } from 'src/lib/types';

interface TriggerOverrideData {
  task: string;
  history: number;
  prompt: string;
  include: string[];
  exclude: string[];
  include_knowledge: string[];
  exclude_knowledge: string[];
}

interface TriggerFollowupConfigData {
  duration: number;
  maxAttempts: number;
  promptId: string;
  isFollowupEnabled: boolean;
  conditionPrompt?: string;
  schedule?: Schedule;
  timezone?: string;
}

@Injectable()
export class ghlWebhookHelperService {
  constructor(
    private readonly organizationService: OrganizationService,
    private readonly mongoConversationService: MongoConversationService,
    private readonly mongoCredentialService: MongoCredentialsService,
    private readonly mongoAgentService: MongoAgentService,
    private readonly ghlApiService: GhlApisService,
    private readonly sessionService: SessionService,
    private readonly userService: UserService, // @Inject(CACHE_MANAGER) private cacheManager: Cache
    private readonly myLogger: MyLogger,
    private readonly mongoContextDataService: MongoContextDataService,
    private readonly schedulerService: SchedulerService,
  ) { }

  async addHumanTakeoverTag(tokens, channelLocationId, contactId, agentsData) {
    let tagsToAdd = [];
    if (agentsData.length === 0) {
      const { agentId = undefined } = await this.mongoConversationService.getConversation({
        query: {
          contactId
        },
        projection: {
          agentId: 1
        }
      }) ?? {};
      if (agentId) {
        const agentData = await this.mongoAgentService.getAgent({ _id: agentId });
        agentsData = [agentData];
      }
    }
    for (const agent of agentsData) {
      if (agent?.humanTakeover?.takeoverType === 'Tag' && agent?.humanTakeover?.tagToAdd) {
        tagsToAdd.push(agent?.humanTakeover?.tagToAdd);
      }
    }
    if (tagsToAdd.length > 0) {
      await this.ghlApiService.addTag(
        tokens,
        channelLocationId,
        contactId,
        ...tagsToAdd,
      );
      this.myLogger.log({
        message: `Tag ${JSON.stringify(
          tagsToAdd,
        )}added to contact ${contactId}`,
      });
    } else {
      this.myLogger.log({
        message: `No tags to add to contact ${contactId}`,
      });
    }
    return;
  }

  async processFollowUpOnInbound(
    { payload, followUpConfig, maxAttempts, currAgentId, schedule, userTimeZone }
      :
      { payload: any, followUpConfig: ConversationDocument['followUp'], maxAttempts: number, currAgentId: string, schedule: Schedule, userTimeZone: string }
  ) {
    let isFollowUp = payload?.type === 'followUp';

    try {
      let attemptCount = followUpConfig?.attemptCount ?? 0
      if (!payload?.contactId) throw new Error(`Contact ID not found in payload -> aborting`);

      if (!isFollowUp && payload?.direction === 'inbound' && !payload?.correlationId) {
        // not payload?.correlationId => server sent inbound webhook and not multiple inbounds request
        // On inbound without type === 'followUp' -> server originated inbound, set attemptCount to 0 and let it process
        await this.mongoConversationService.updateFollowUpAttemptCount({ contactId: payload?.contactId, count: 0 });
        attemptCount = 0
        this.myLogger.log({
          message: `contactId: ${payload?.contactId} | Updated attempt count to 0 as inbound webhook from server has been received`,
          context: 'FOLLOW UP'
        })
      } else if (payload?.type === 'followUp' && payload?.direction === 'inbound') {
        // if curr agentId is not equal to the follow up agent id then abort
        if (currAgentId !== payload?.followupAgent) {
          throw new Error(`The agent being processed is not the follow up agent Id | currAgentId: ${currAgentId} | followUpAgentId: ${payload?.followupAgent}`)
        }
        if (schedule && userTimeZone) {
          const followupIsAllowedBySchedule = await isEventAllowedInScheduledTime(new Date(), schedule, userTimeZone);
          if (!followupIsAllowedBySchedule) {
            throw new Error(`Follow up event is not allowed by the schedule configuration | contactId ${payload?.contactId}`)
          }
        } else {
          this.myLogger.log({
            message: `No schedule or userTimeZone found for follow up event | timezone - ${userTimeZone} | contactId ${payload?.contactId} | proceeding without schedule check`,
            context: 'FOLLOW UP'
          })
        }
        // follow up inbound
        if ((followUpConfig?.attemptCount ?? 0) + 1 > maxAttempts) {
          // check if attempt count + 1 > max attempt -> set it to 0 if so and abort the processing
          await this.mongoConversationService.updateFollowUpAttemptCount({ contactId: payload?.contactId, count: 0 });
          // attemptCount = 0
          throw new Error(`Max attempt count reached`)
        } else {
          // let it process and update the attempt count
          attemptCount += 1
          await this.mongoConversationService.updateFollowUpAttemptCount({ contactId: payload?.contactId, count: attemptCount });
          this.myLogger.log({
            message: `Updated follow up attempt count to ${attemptCount} for contact ${payload?.contactId}`,
            context: 'Process FollowUp On Inbound'
          })
        }
      }
      return {
        abort: false, error: "", attemptCount,
        isFollowUp
      }
    } catch (error) {
      this.myLogger.log({
        message: `Error while processing follow up on inbound | contactId ${payload?.contactId} | error - ${error?.message}`,
        context: 'Process FollowUp On Inbound'
      })
      return {
        abort: true,
        error: error?.message ?? "Error while processing follow up",
        isFollowUp
      }
    }
  }

  checkTagCondition(tags: string[], condition: { tagOption: string; tagValue: string }): boolean {
    if (condition.tagOption === 'hasTag') {
      return tags.includes(condition.tagValue);
    } else if (condition.tagOption === 'doesntHaveTag') {
      return !tags.includes(condition.tagValue);
    }
    return false; // Invalid tagOption
  }

  isTriggerMatching(
    trigger: TriggerDocument,
    accountId: string,
    channel: string,
    tags: string[]
  ): boolean {
    const triggerData = trigger.data as IGhlTrigger;
    const providerName = trigger.providerName;
    let allTagConditionsMet = false;

    if (providerName === 'ghl') {
      allTagConditionsMet = triggerData.tagConditions.every((condition) =>
        this.checkTagCondition(tags, condition)
      );
    } else {
      allTagConditionsMet = this.checkTagCondition(tags, {
        tagOption: triggerData.tagOption,
        tagValue: triggerData.tagValue,
      });
    }

    return (
      triggerData.subaccount === accountId &&
      triggerData.channel === channel &&
      allTagConditionsMet &&
      trigger.active === true
    );
  }

  extractTriggerData(trigger: any, accountId: string) {
    const triggerData = trigger.data as IGhlTrigger;
    if (triggerData.subaccount !== accountId) {
      return { override: null, followup: null };
    }

    const override = {
      task: triggerData.task,
      history: triggerData.history,
      prompt: triggerData.prompt,
      include: triggerData.include,
      exclude: triggerData.exclude,
      include_knowledge: triggerData.include_knowledge,
      exclude_knowledge: triggerData.exclude_knowledge,
    };

    const followup: TriggerFollowupConfigData | null = trigger?.followUp?.isFollowupEnabled ? {
      duration: trigger.followUp.duration,
      maxAttempts: trigger.followUp.maxAttempts,
      promptId: trigger.followUp.promptId,
      isFollowupEnabled: trigger.followUp.isFollowupEnabled,
      conditionPrompt: trigger.followUp.conditionPrompt,
      timezone: trigger?.followUp?.timezone,
      schedule: trigger?.followUp?.schedule,
    } : null;

    return { override, followup };
  }

  async eventAllowedBySchedule({ eventTime, scheduleConfigurations, userTimeZone }: {
    eventTime: Date,
    scheduleConfigurations: Schedule,
    userTimeZone: string
  }) {
    try {
      const isAllowed = isEventAllowedInScheduledTime(eventTime, scheduleConfigurations, userTimeZone);
      return isAllowed;
    } catch (error) {
      this.myLogger.error({
        message: `Error in filtering the event for the schedule configuration -> ${error?.message}`,
        context: `filter date time`
      })
      return true;
    }
  }
}
