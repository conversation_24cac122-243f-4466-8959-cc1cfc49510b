/**
 * Contains the code to generate a response for an incoming GHL message via webhook or via trigger. 
 * Contains the multiple inbound logic, the follow-up logic.
 */
import { Injectable, Inject, UnauthorizedException } from '@nestjs/common';
import { ApiclientService } from 'src/api-client/services/apiclient.service';
import { GhlAPIs } from 'src/api-client/lib/ghlApi';
import { OrganizationService } from '../../organization.service';
import { MongoConversationService } from 'src/mongo/service/conversations/mongo-conversations.service';
import { MongoAgentService } from 'src/mongo/service/agent/mongo-agent.service';
import { CreateConversationDto } from 'src/mongo/dto/conversations/create.dto';
import {
  PROVIDERS,
  KINDS,
  GHL_MESSAGE_TYPE_MAP,
  CUSTOM_LLM,
} from 'src/lib/constant';
import {
  Actions,
  AppointmentAction,
} from 'src/organization/controllers/channels/ghl/actions';
import { MongoCredentialsService } from 'src/mongo/service/credentials/mongo-credentials.service';
import { GhlApisService } from 'src/api-client/services/ghl-apis/ghl-apis.service';
import { SessionService } from 'src/session/session.service';
import { IGhlTrigger } from 'src/mongo/schemas/agents/agents.schema';
import { IRawbotResponse } from './botresponse';
import { UserService } from 'src/user/user.service';
import { TiktokenService } from 'src/vector/services/tiktoken.service';
import { IBotActionResponse } from 'src/interfaces/interfaces';
import { ObjectId } from 'mongodb';
import { MyLogger } from 'src/logger/logger.service';
import { MongoContextDataService } from 'src/mongo/service/contextData/contextData.service';
import { TaskService } from 'src/gcp/services/task.service';
import { CacheService } from 'src/utility/services/cache.service';
import { SchedulerService } from 'src/utility/services/scheduler.service';
import { ghlWebhookHelperService } from './ghl-webhook-helpers.service';
import { GhlConversationService } from './ghl-webhook-conversation.service';
import { TokensService } from 'src/tokens/tokens.service';
import { SlackApiService } from 'src/api-client/services/slack-api/slack-api.service';
import { createExternalAppContextPrompt } from 'src/organization/webhooks/webhook_utils';
import { json } from 'stream/consumers';
import { updateAgentVariables } from 'src/session/utils';
// import { sleep } from 'src/lib/utils';

interface IObject {
  [key: string]: any;
}

interface rolloverEventDto {
  reason: string;
  date: Date | string;
  rollover: boolean;
  contactId: string;
  responded?: boolean;
}

export const standardFields = [
  'name',
  'firstName',
  'lastName',
  'email',
  'timezone',
  'companyName',
  'phone',
  'address1',
  'city',
  'state',
  'country',
  'postalCode',
  'website',
  'dateOfBirth',
  'gender',
];

function createActionElement(actions: IBotActionResponse) {
  const actionElem = {
    accountName: actions.accountName,
    action: actions.action,
    kind: actions.kind,
    silent: actions?.silent || actions?.eventData?.tag?.silent || false,
  };

  if (actions.calendarId) actionElem['calendarId'] = actions.calendarId;
  if (actions.locationId) actionElem['locationId'] = actions.locationId;
  if (actions.eventData?.tag?.name)
    actionElem['tagName'] = actions?.eventData?.tag?.name;
  if (actions.eventData?.startDate)
    actionElem['startDate'] = actions.eventData.startDate;
  if (actions.eventData?.endDate)
    actionElem['endDate'] = actions.eventData.endDate;
  if (actions.errors) actionElem['error'] = actions.errors;
  return actionElem;
}

@Injectable()
export class GhlService {
  constructor(
    private readonly organizationService: OrganizationService,
    private readonly mongoConversationService: MongoConversationService,
    private readonly mongoCredentialService: MongoCredentialsService,
    private readonly mongoAgentService: MongoAgentService,
    private readonly ghlApiService: GhlApisService,
    private readonly sessionService: SessionService,
    private readonly userService: UserService, // @Inject(CACHE_MANAGER) private cacheManager: Cache
    private readonly myLogger: MyLogger,
    private readonly mongoContextDataService: MongoContextDataService,
    private readonly schedulerService: SchedulerService,
    private readonly ghlWebhookHelperService: ghlWebhookHelperService, // private readonly ghlConversationService: GhlConversationService,
    private readonly apiClientService: ApiclientService,
    private readonly tokenService: TokensService,
    private readonly slackApiService: SlackApiService,
    private readonly ghlApisService: GhlApisService,
    private readonly cacheService: CacheService,
    private readonly taskService: TaskService,
    private readonly ghlWebhookConversationService: GhlConversationService
  ) { }

  async performAction(
    reqId: string,
    payload: IObject,
    overrides?: IObject,
    overrideTags?: string[],
    type?: string,
  ) {
    var rolloverEvent: rolloverEventDto = {
      rollover: false,
      reason: '',
      date: new Date().toISOString(),
      contactId: payload.contactId,
      responded: false,
    };
    try {
      let customPrompt: string;
      const originalMessageType = payload?.messageType;
      if (typeof payload.messageType !== 'string') {
        if (typeof payload.messageType !== 'number') {
          this.myLogger.log({
            message: `MessageType not valid for contact ID: ${payload.contactId} | location ID: ${payload.locationId}: ${payload.messageType}`,
            context: `${payload.contactId}`,
          });
        } else {
          payload.messageType = GHL_MESSAGE_TYPE_MAP?.[payload.messageType];
        }
      } else if (payload.messageType === 'Email') return;

      if (payload?.messageType === undefined)
        throw new Error(
          `MessageType not valid for contact ID: ${payload.contactId} | location ID: ${payload.locationId}: ${originalMessageType}`,
        );

      if (overrides?.agent) overrides.agent = overrides.agent.trim();
      // if (overrides.task) overrides.task = overrides.task.trim()
      if (overrides?.knowledge)
        overrides.knowledge = overrides.knowledge.trim();
      if (overrides?.prompt_id)
        overrides.prompt_id = overrides.prompt_id.trim();

      let isTrigger = overrides.agent ? false : true;
      let isOutreach = overrides.task === 'outreach' ? true : false;
      const isNotEvaluateTask = !['evaluate', 'Evaluate'].includes(overrides?.task ?? '');
      const isEmptyMessage = (payload?.body || '').trim() === '';
      const isSensitiveEmptyMessage = isNotEvaluateTask && isEmptyMessage;

      const {
        contactId,
        body,
        messageType,
        locationId,
        direction = 'inbound',
      } = payload;

      let aiProviderName = '';
      let channelName = 'ghl';

      let isOutbound = direction === 'outbound';
      if (isOutbound) {
        await this.processOutbound({
          message: body,
          payload,
          direction,
          channelName,
          agentId: overrides?.agent,
          aiProviderName,
          saveToken: !!overrides?.proxied,
          messageType,
          source: overrides.source ?? payload.source,
          conversationId: overrides?.conversationId ?? payload.conversationId,
        });
        if (overrides.source !== 'app') return;
      }
      const cred = await this.mongoCredentialService.getCredential({
        keyId: locationId,
        kind: KINDS.GHL_CREDENTIAL,
      });
      if (cred?.creds?.accessToken == undefined) {
        this.myLogger.error({
          message: `Your leadconnector account with locationId: ${locationId} couldn't be found in our database`,
          context: `${contactId}`,
        });
        return;
      }
      const tokens = {
        access_token: cred.creds.accessToken,
        refresh_token: cred.creds.refreshToken,
      };

      let location;
      let botErrors = [];
      try {
        location = await this.ghlApiService.getGhlLocation(tokens, locationId);
      } catch (err) {
        if (err.response && err.response.status >= 500) {
          this.myLogger.error({
            message: `Server error while fetching location | reqId ${reqId} | ${err.response.data.message}`,
            context: `${contactId}`,
          });
          await this.taskService.createGhlWorkflowTaskWithExponentialBackoff(
            payload,
            contactId, // Pass the contactId for logging
            300, // 5 minutes from now
          );
          return; // Stop execution
        }
        if (err) botErrors.push(err.response.data.message);
        this.myLogger.error({
          message: `reqId ${reqId} | ${err.response.data.message}`,
          context: contactId,
        });
      }



      // get Ghl contact & tags
      let contact;
      try {
        let rawContact = await this.ghlApiService.getContact(
          tokens,
          locationId,
          contactId,
        );
        contact = rawContact?.contact;
      } catch (err) {
        if (err.response && err.response.status >= 500) {
          this.myLogger.error({
            message: `Server error while fetching contact | reqId ${reqId} | ${err.response.data.message}`,
            context: `${contactId}`,
          });
          await this.taskService.createGhlWorkflowTaskWithExponentialBackoff(
            payload,
            contactId, // Pass the contactId for logging
            300, // 5 minutes from now
          );
          return; // Stop execution
        }
        if (err) botErrors.push(err.response.data.message);
        this.myLogger.error({
          message: `reqId ${reqId} | ${err.response.data.message}`,
          context: `${contactId}`,
        });
      }

      const contactTags = contact?.tags || [];

      this.myLogger.log({
        message: `contactTags:  ${contactTags}`,
        context: `${contactId}`
      })
      let contactTimezone = contact?.timezone ? contact?.timezone : location?.location?.timezone;
      let contactCustomFields: {
        id: string;
        value: string;
        name?: string;
        fieldKey?: string;
      }[] = contact?.customFields || [];

      //get custom values for variables
      const customValues = [];
      try {
        const customValuesFromGHL = await this.ghlApiService.getCustomValues({
          tokens: tokens,
          locationId: locationId,
        });

        customValues.push(...customValuesFromGHL);
      } catch (err) {
        if (err.response && err.response.status >= 500) {
          this.myLogger.error({
            message: `Server error while fetching custom values | reqId ${reqId} | ${err.response.data.message}`,
            context: `${contactId}`,
          });
          await this.taskService.createGhlWorkflowTaskWithExponentialBackoff(
            payload,
            contactId, // Pass the contactId for logging
            300, // 5 minutes from now
          );
          return; // Stop execution
        }
        if (err) botErrors.push(err.response.data.message);
        this.myLogger.error({
          message: `reqId ${reqId} | ${err.response.data.message}`,
          context: `${contactId}`,
        });
      }

      try {
        contactCustomFields = await this.createGHLCustomFieldObject(
          reqId,
          locationId,
          contactCustomFields,
          contact,
        );
      } catch (err) {
        if (err) botErrors.push(err.response.data.message);
        this.myLogger.error({
          message: `reqId ${reqId} | ${err.response.data.message}`,
          context: `${contactId}`,
        });
      }

      channelName = location?.name || 'ghl';

      const organization = await this.organizationService.getOrganization(
        {
          'connections.channels.keyId': locationId,
        },
        {
          connections: 1,
          name: 1,
          agents: 1,
          billing: 1,
        },
      );
      let channelLocationId = locationId;

      const connection = (organization?.connections?.channels || []).find(
        (channel) => channel.keyId === locationId,
      );
      if (!connection) {
        throw new Error(
          `No channel with locationId ${locationId} found for your organization. Please connect your leadconnector account again to your organization to continue using the channel.`,
        );
      }

      // refrence: https://capriai-public.slack.com/archives/C05C6CNGZUK/p1741037601309039
      // check if agent is already set as an active trigger
      // if (overrides?.agent && ObjectId.isValid(overrides?.agent) && overrides?.task !== 'outreach') {
      //   const [agentAsTrigger, filterAgentsError] = await this.filterAgents(
      //     organization._id.toString(),
      //     [overrides.agent],
      //     messageType,
      //     connection.accountId,
      //     contactTags,
      //     reqId,
      //   );
      //   if ((agentAsTrigger || []).length > 0) {
      //     this.myLogger.log({
      //       message: `Agent ${overrides.agent} is already set as an active trigger. Skipping webhook...`,
      //       context: `${contactId}`,
      //     });
      //     return;
      //   }
      //   if (filterAgentsError) botErrors.push(filterAgentsError);
      // }

      let agent, triggerOverrides, agentFollowupConfig, agentFollowupConfigOverride;
      if (overrides?.agent) {
        agent = await this.mongoAgentService.getAgent(
          {
            _id: overrides?.agent,
          },
          { 'aiProvider.accountName': 1 },
        );
        aiProviderName = agent?.aiProvider?.accountName ? agent?.aiProvider?.accountName : CUSTOM_LLM.NAME;
        agentFollowupConfigOverride = {
          duration: overrides?.followup_duration ? +(overrides?.followup_duration) : undefined,
          maxAttempts: overrides?.followup_maxattempts ? +(overrides.followup_maxattempts) : undefined,
          promptId: overrides?.followup_promptid,
          conditionPrompt: overrides?.condition_prompt,
          isFollowupEnabled: overrides?.followup_enabled === "true",
        };
      }

      if (overrides?.task !== 'outreach') {
        // await sleep(1500);
        const isDuplicate = await this.mongoConversationService.isAlreadySaved({
          contactId: payload.contactId,
          'conversation.body': body,
        });
        if (!isDuplicate) {
          await this.saveGhlConversation({
            message: body,
            payload,
            direction,
            aiProviderName,
            agentId: overrides?.agent,
            channelName,
            status: 'delivered',
            messageType,
            orgId: organization._id.toString(),
            source: overrides.source ?? payload.source,
          });
        }
      } else
        this.myLogger.log({
          message: ` Not saving conversation for task: ${overrides?.task} | contactId: ${contactId} | locationId: ${locationId} | direction ${direction}`,
          context: `${contactId}`,
        });

      // if (isOutbound) return;

      const agentIds = organization?.agents;
      const accountId = connection?.accountId;
      const userId = connection?.userId;
      let email = '';
      try {
        email = (userId ? await this.userService.getUser(userId) : undefined)
          ?.email;
      } catch (error) {
        this.myLogger.log({
          message: ` Error getting user email`,
          context: `${contactId}`,
        });
      }

      let currActionKind;
      let agentsData = [];

      customPrompt = overrides?.prompt;


      if (overrides?.agent) {
        this.myLogger.log({
          message: `WORKFLOW WEBHOOK EVENT - Received webhook event from workflow. For agent - ${overrides?.agent}.`,
          context: `${contactId}`,
        });
        agentsData = await this.mongoAgentService.getAgents({
          _id: { $in: [overrides?.agent] },
          $or: [{ disabled: false }, { disabled: { $exists: false } }],
        });
        if (agentsData.length === 0) {
          const orgNameText = organization?.name ? ` and organization named: ${organization.name}` : '';
          this.myLogger.error({
            message: `Your agent with id ${overrides?.agent}${orgNameText} is Inactive. Consider activating your agent to continue using this agent.`,
            context: `${contactId}`,
          });
          botErrors.push(
            `Your agent with id ${overrides?.agent}${orgNameText} is Inactive. Consider activating your agent to continue using this agent.`,
          );
        }
      } else {
        let filterAgentsError;
        [agentsData, filterAgentsError, triggerOverrides, agentFollowupConfig] =
          await this.filterAgents(
            organization._id.toString(),
            agentIds,
            messageType,
            accountId,
            contactTags,
            reqId,
          );
        this.myLogger.log({
          message: `TRIGGER WEBHOOK EVENT -  Found ${agentsData.length} active agents for the trigger.`,
          context: `${contactId}`,
        });
        if (filterAgentsError) {
          botErrors.push(filterAgentsError);
        }
      }

      if (agentsData?.length === 0) {
        this.myLogger.log({
          message: `No active agent found, skipping the flow`,
          context: `${contactId}`,
        });
        return;
      }

      if (isOutbound) {
        await this.ghlWebhookHelperService.addHumanTakeoverTag(
          tokens,
          locationId,
          contactId,
          agentsData,
        );
        return;
      }

      if(!overrides?.task){
          agentsData = await this.schedulerService.handleMultipleInboundTask(
            contactId,
            agentsData,
            overrides ?? payload,
            triggerOverrides,
          );
      } else {
        this.myLogger.log({
          message: `Multiple inbound processing skipped since task type detected on the incoming webhook.`,
          context: `${contactId}`
        })
      }

      let history = undefined;

      if (overrides?.history) {
        history = overrides?.history;
      } else if (isTrigger && agentsData?.length > 0) {
        history = triggerOverrides?.[agentsData?.[0]._id.toString()]?.history;
      }

      let {
        conversationString: conversations,
        conversationUpdatedAt,
        lastConversationSource,
        contactFollowUpInfo,
        disconnectBot
      } = await this.getConversationString(locationId, contactId, history);

      if (disconnectBot){
        this.myLogger.log({
          message: `Skipping - Not generating a reply since the contact is marked to not be replied by the user.`,
          context: `contactId ${contactId}`
        });
        return;
      }

      let isFollowUpRequest = (overrides.type == "followUp");

      //getUphexContextData
      let uphexData = await this.mongoContextDataService.getUphexContextData(contactId);

      if (uphexData) {
        this.myLogger.log({
          message: `Uphex context data found`,
          context: `${contactId}`,
        });
      } else {
        this.myLogger.log({
          message: `Uphex context data not found, continuing with the flow`,
          context: `${contactId}`,
        });
      }


      (async () => {
        for (const agent of agentsData || []) {
          try {

            let agentId = agent?._id?.toString();
            aiProviderName = agent?.aiProvider.accountName ? agent?.aiProvider?.accountName : CUSTOM_LLM.NAME;
            // check if webhook should be processed or not
            const { abort = undefined, error = undefined, attemptCount: currAttemptCount, isFollowUp } = await this.ghlWebhookHelperService.processFollowUpOnInbound({
              payload: overrides,
              followUpConfig: contactFollowUpInfo,
              maxAttempts: agentFollowupConfigOverride?.maxAttempts ?? agentFollowupConfig?.[agent?._id.toString()]?.maxAttempts,
              schedule: agentFollowupConfigOverride?.schedule ?? agentFollowupConfig?.[agent?._id.toString()]?.schedule,
              userTimeZone: agentFollowupConfigOverride?.timezone ?? agentFollowupConfig?.[agent?._id.toString()]?.timezone,
              currAgentId: agent?._id?.toString(),
            });

            let followupConditionPassed = true;
            let followupConditionPrompt = agentFollowupConfigOverride?.conditionPrompt ?? agentFollowupConfig?.[agentId]?.conditionPrompt;
            if (!!!abort && isFollowUpRequest && followupConditionPrompt) {

              let agentVariables = agent?.variables;

              let contactVariables = [
                contactCustomFields,
                customValues,
              ];

              let variables = updateAgentVariables(agentVariables, contactVariables);

              followupConditionPassed = (await this.sessionService.evaluateFollowUp({
                agent,
                conditions: followupConditionPrompt,
                conversations,
                variables: variables,
                contactTimezone,
                contactDetails: {
                  uphexData,
                  contactId,
                  locationId,
                },
              })).shouldFollowUp;
              this.myLogger.log({
                message: `Should follow up prompt evaluation result - ${followupConditionPassed}`,
                context: `SHOULD FOLLOW UP - ${contactId}`
              })
            }

            if (isFollowUpRequest && (abort || !followupConditionPassed)) {
              this.myLogger.error({
                message: `Aborted follow Up inbound as defined by the follow up condition | evaluate | message - ${error}`,
                context: `contactId ${contactId}`,
              })
              continue;
            }

            if (
              agent?.humanTakeover?.takeoverType === 'Wait' &&
              agent?.humanTakeover?.timeToWait
            ) {
              if (lastConversationSource === 'app') {
                const elapsedTime = conversationUpdatedAt
                  ? new Date().getTime() - conversationUpdatedAt.getTime()
                  : 100000;
                if (
                  conversationUpdatedAt &&
                  conversationUpdatedAt instanceof Date &&
                  elapsedTime <= agent?.humanTakeover?.timeToWait * 1000
                ) {
                  this.myLogger.log({
                    message: `HUMAN TAKE OVER: Not processing query since the last conversation was created less than ${agent?.humanTakeover?.timeToWait} seconds ago within the App.`,
                    context: `${contactId}`,
                  });
                  continue;
                }
              }
            }

            if (isSensitiveEmptyMessage && !isOutreach) {
              if (agent?.emptyMessageConfig?.enabled) {
                if (agent?.emptyMessageConfig?.tagToAdd) {
                  try {
                    await this.ghlApiService.addTag(
                      tokens,
                      locationId,
                      contactId,
                      agent?.emptyMessageConfig?.tagToAdd,
                    );
                    this.myLogger.log({
                      message: `Incoming message is an empty message. Adding tag ${agent?.emptyMessageConfig?.tagToAdd}.`,
                      context: `${contactId}`,
                    });
                  } catch (err) {
                    if (err.response && err.response.status >= 500) {
                      this.myLogger.error({
                        message: `Server error while adding tag | reqId ${reqId} | ${err.response.data.message}`,
                        context: `${contactId}`,
                      });
                      await this.taskService.createGhlWorkflowTaskWithExponentialBackoff(
                        payload,
                        contactId, // Pass the contactId for logging
                        300, // 5 minutes from now
                      );
                      return; // Stop execution
                    }
                    botErrors.push(err.response.data.message);
                    this.myLogger.error({
                      message: `reqId ${reqId} | ${err.response.data.message}`,
                      context: `${contactId}`,
                    });
                  }
                }
                if (agent?.emptyMessageConfig?.setSilent) {
                  this.myLogger.log({
                    message: `Incoming message is an empty message. Setting it as silent.`,
                    context: `${contactId}`,
                  });
                  return;
                }
              }
            }

            const attachmentIsPresent =
              (payload?.attachments ?? overrides?.attachments ?? []).length > 0;
            const d = body?.trim();
            const incomingMessageIsPureAttachment =
              !isOutbound &&
              attachmentIsPresent &&
              (body ?? '')
                .trim()
                .replace(/> .*? </g, '')
                .replace(/\n/g, '') === '';
            // handle attachment without message
            if (
              attachmentIsPresent &&
              agent?.attachmentConfig?.withoutMessage?.enabled &&
              incomingMessageIsPureAttachment
            ) {
              const addTag =
                agent?.attachmentConfig?.withoutMessage?.tagToAdd ?? '';
              if (addTag) {
                try {
                  await this.ghlApiService.addTag(
                    tokens,
                    locationId,
                    contactId,
                    addTag,
                  );
                  this.myLogger.log({
                    message: `Incoming message is an attachment without message. Adding tag ${addTag}.`,
                    context: `${contactId}`,
                  });
                } catch (err) {
                  if (err.response && err.response.status >= 500) {
                    this.myLogger.error({
                      message: `Server error while adding tag | reqId ${reqId} | ${err.response.data.message}`,
                      context: `${contactId}`,
                    });
                    await this.taskService.createGhlWorkflowTaskWithExponentialBackoff(
                      payload,
                      contactId, // Pass the contactId for logging
                      300, // 5 minutes from now
                    );
                    return; // Stop execution
                  }
                  botErrors.push(err.response.data.message);
                  this.myLogger.error({
                    message: `reqId ${reqId} | ${err.response.data.message}`,
                    context: `${contactId}`,
                  });
                }
              }
              if (!!agent?.attachmentConfig?.withoutMessage?.setSilent) {
                this.myLogger.log({
                  message: `Incoming message is an attachment without message. Setting the agent to silent as per preference set.`,
                  context: `${contactId}`,
                });
                return;
              }
            }
            if (
              attachmentIsPresent &&
              agent?.attachmentConfig?.withMessage?.enabled &&
              !incomingMessageIsPureAttachment
            ) {
              const addTag =
                agent?.attachmentConfig?.withMessage?.tagToAdd ?? '';
              if (addTag) {
                try {
                  await this.ghlApiService.addTag(
                    tokens,
                    locationId,
                    contactId,
                    addTag,
                  );
                  this.myLogger.log({
                    message: `Incoming message is an attachment with message. Adding tag ${addTag}.`,
                    context: `${contactId}`,
                  });
                } catch (err) {
                  if (err.response && err.response.status >= 500) {
                    this.myLogger.error({
                      message: `Server error while adding tag | reqId ${reqId} | ${err.response.data.message}`,
                      context: `${contactId}`,
                    });
                    await this.taskService.createGhlWorkflowTaskWithExponentialBackoff(
                      payload,
                      contactId, // Pass the contactId for logging
                      300, // 5 minutes from now
                    );
                    return; // Stop execution
                  }
                  botErrors.push(err.response.data.message);
                  this.myLogger.error({
                    message: `reqId ${reqId} | ${err.response.data.message}`,
                    context: `${contactId}`,
                  });

                }
              }
              if (agent?.attachmentConfig?.withMessage?.setSilent) {
                this.myLogger.log({
                  message: `Incoming message is an attachment with message. Setting the agent to silent as per preference set.`,
                  context: `${contactId}`,
                });
                return;
              }
            }
            let triggerTask = triggerOverrides?.[agent?._id?.toString()]?.task;
            if (triggerTask === 'respond') {
              triggerTask = '';
            }




            let rawBotResponse: any = await this.sessionService.ghlWebhook({
              conversations,
              query: body,
              agentId: agent._id.toString(),
              agentDetails: agent,
              organizationDetails: organization,
              contactId,
              locationId,
              prompt_id: (isTrigger && overrides.type !== "followUp")
                ? triggerOverrides?.[agent?._id?.toString()]?.prompt
                : overrides?.prompt_id,
              prompt: customPrompt,
              task: (isTrigger && overrides.type !== "followUp")
                ? triggerOverrides?.[agent?._id?.toString()]?.task
                : overrides?.task,
              includeActionIds: isTrigger
                ? triggerOverrides?.[agent?._id?.toString()]?.include
                : overrides?.include,
              excludeActionIds: isTrigger
                ? triggerOverrides?.[agent?._id?.toString()]?.exclude
                : overrides?.exclude,
              knowledge: overrides?.knowledge,
              includeKnowledgeIds: isTrigger
                ? triggerOverrides?.[agent?._id?.toString()]?.include_knowledge
                : overrides?.include_knowledge,
              excludeKnowledgeIds: isTrigger
                ? triggerOverrides?.[agent?._id?.toString()]?.exclude_knowledge
                : overrides?.exclude_knowledge,
              training: overrides?.training,
              contactDetails: {
                contactTimezone,
                tags: contactTags,
                customFields: contactCustomFields,
                customValues,
                externalAppContextPrompt: '',
                uphexData,
                locationId,
                agentId,
                contactId,
              },
              reqId,
            });

            let botResponse: IRawbotResponse = rawBotResponse,
              tokensCount = rawBotResponse?.usage_tokens_total;
            let processedQueryMessage = botResponse?.finalMessage;
            let errorHandledMessage = '';
            let botResponseForRollover = botResponse?.rolloverDetails;
            if (botResponseForRollover?.rollover) {
              rolloverEvent.rollover = true;
              rolloverEvent.reason =
                botResponseForRollover?.rolloverReason || '';
            }
            let tagsToAdd = [],
              visibleActions = [],
              isSilent = false;

            let customFieldsToUpdate: {
              fieldKey: string;
              field_value: string;
            }[] = [];

            for (const actions of botResponse?.botResponseForEachDataSource ||
              []) {
              try {

                // create action object to save
                const actionElem = createActionElement(actions);
                visibleActions.push(actionElem);

                // process the actions
                if (actions?.action === 'read') continue;

                if ((actions?.errors || []).length > 0) {
                  // displayedErrors.push(actions?.errors ?? []);
                  const errorString = (actions.errors || []).join('\n');
                  if (errorString) {
                    botErrors.push(errorString);
                  }
                }

                let { calendarId, timestamp, accountId, eventId } = actions;
                let actionLocationId = actions?.locationId;
                currActionKind = actions?.kind;

                if (
                  !actionLocationId &&
                  currActionKind === 'ghlCalendar' &&
                  !(actions?.eventData?.startDate || '')
                    .toUpperCase()
                    .includes('TBD')
                )
                  this.myLogger.log({
                    message: `@webhook:  No locationId from action found for action: ${actions.action} | contactId: ${contactId} | locationId: ${locationId}`,
                    context: `${contactId}`,
                  });

                if (actions?.eventData?.tag?.name ?? actions?.eventData?.tag) {
                  tagsToAdd.push(actions?.eventData?.tag);
                }

                if (actions?.eventData?.customFieldData?.fieldKey) {
                  customFieldsToUpdate.push({
                    fieldKey:
                      actions?.eventData?.customFieldData.fieldKey.replace(
                        /\{|\}/g,
                        '',
                      ),
                    field_value:
                      actions?.eventData?.customFieldData.field_value,
                  });
                }

                if (actions?.eventData?.standardFieldData?.field) {
                  if (Array.isArray(actions.eventData.standardFieldData.field)) {
                    // Handle array of standard fields
                    actions.eventData.standardFieldData.field.forEach(field => {
                      customFieldsToUpdate.push({
                        fieldKey: field.fieldKey,
                        field_value: field.field_value,
                      });
                    });
                  }
                }

                // Remove duplicates from customFieldsToUpdate, keeping only the latest entry for each fieldKey
                customFieldsToUpdate = Object.values(
                  customFieldsToUpdate.reduce((acc, field) => {
                    acc[field.fieldKey] = field;
                    return acc;
                  }, {})
                );

                let calendarAccToken: any = { ...tokens };
                let channelNotEqActionLocation =
                  channelLocationId &&
                  actionLocationId &&
                  channelLocationId !== actionLocationId;
                if (channelNotEqActionLocation && actionLocationId) {
                  calendarAccToken =
                    await this.mongoCredentialService.getCredential({
                      keyId: actionLocationId,
                      kind: KINDS.GHL_CREDENTIAL,
                    });

                  calendarAccToken = {
                    access_token: calendarAccToken?.creds?.accessToken,
                    refresh_token: calendarAccToken?.creds?.refreshToken,
                  };
                }
                if (!calendarAccToken?.access_token)
                  botErrors.push(
                    `Your leadconnector account with locationId: ${actionLocationId} couldn't be found in our database`,
                  );
                if (actions.kind === PROVIDERS.GHL_CALENDAR) {
                  var actionContactId: string;
                  let { startDate = '', endDate = '' } = actions.eventData;
                  if (
                    !(
                      startDate == '' || startDate.toUpperCase().includes('TBD')
                    )
                  ) {
                    // let { contact: actionContact } = await this.ghlApiService.getContact(
                    //     calendarAccToken,
                    //     actionLocationId,
                    //     contactId,
                    // );
                    if (channelNotEqActionLocation) {
                      const contactData = {
                        locationId: actionLocationId,
                      };
                      if (contact?.fullNameLowerCase)
                        contactData['name'] = contact.fullNameLowerCase;
                      if (contact?.email) contactData['email'] = contact.email;
                      if (contact?.phone) contactData['phone'] = contact.phone;
                      try {
                        let actionContact =
                          await this.ghlApiService.createContact(
                            calendarAccToken,
                            actionLocationId,
                            contactData,
                            email,
                          );
                        actionContactId = actionContact?.id;
                      } catch (err) {
                        if (err.response && err.response.status >= 500) {
                          this.myLogger.error({
                            message: `Server error while creating contact | reqId ${reqId} | ${err.response.data.message}`,
                            context: `${contactId}`,
                          });
                          await this.taskService.createGhlWorkflowTaskWithExponentialBackoff(
                            payload,
                            contactId, // Pass the contactId for logging
                            300, // 5 minutes from now
                          );
                          return; // Stop execution
                        }
                        botErrors.push(err.response.data.message);
                        this.myLogger.error({
                          message: `reqId ${reqId} | ${err.response.data.message}`,
                          context: `${contactId}`,
                        });
                      }
                    } else {
                      actionContactId = contactId;
                    }
                    if (endDate == '' || endDate.toUpperCase().includes('TBD'))
                      endDate = undefined;

                    let ghlEvaluateOn = 'everyTurn';
                    let ghlEvaluateOn_endDate =
                      Date.now() + 60 * 24 * 60 * 60 * 1000;
                    const writeAction = agent.actions.filter(
                      (action) => action.activity === 'write',
                    )[0];

                    let cancelEvent = false;
                    let rescheduleEvent = false;

                    if (
                      writeAction &&
                      writeAction.metaData &&
                      writeAction.metaData.ghlCalendarMetaData
                    ) {
                      ghlEvaluateOn =
                        writeAction.metaData.ghlCalendarMetaData.evaluateOn;
                      ghlEvaluateOn_endDate =
                        Date.now() +
                        24 *
                        60 *
                        60 *
                        1000 *
                        writeAction.metaData.ghlCalendarMetaData.dayRange;
                      cancelEvent = writeAction.metaData.ghlCalendarMetaData.cancelEvent;
                      rescheduleEvent = writeAction.metaData.ghlCalendarMetaData.rescheduleEvent;
                    }

                    let future_events_ghl;
                    try {
                      future_events_ghl =
                        await this.ghlApiService.getFutureCalendarEvents(
                          tokens,
                          locationId,
                          calendarId,
                          null,
                          ghlEvaluateOn_endDate,
                        );
                    } catch (err) {
                      if (err.response && err.response.status >= 500) {
                        this.myLogger.error({
                          message: `Server error while fetching future calendar events | reqId ${reqId} | ${err.response.data.message}`,
                          context: `${contactId}`,
                        });
                        await this.taskService.createGhlWorkflowTaskWithExponentialBackoff(
                          payload,
                          contactId, // Pass the contactId for logging
                          300, // 5 minutes from now
                        );
                        return; // Stop execution
                      }
                      botErrors.push(err.response.data.message);
                      this.myLogger.error({
                        message: `reqId ${reqId} | ${err.response.data.message}`,
                        context: `${contactId}`,
                      });
                    }



                    if (ghlEvaluateOn == 'isEmpty') {
                      if (future_events_ghl.events.length > 0) {
                        const moment = require('moment');

                        // Filter future events for the contact and check if any event is in the future
                        const filteredEvents = future_events_ghl.events.filter(
                          (event) => {
                            return (
                              event.contactId === actionContactId &&
                              moment(event.startTime).isAfter(moment())
                            );
                          },
                        );

                        if (filteredEvents.length > 0) {
                          await this.myLogger.log({
                            message:
                              'Future events available in selected range. Skipping ghl calendar write evaluation.',
                            context: actionContactId,
                          });

                          isSilent = false;
                        } else {
                          await this.myLogger.log({
                            message: 'No future events found for the contact.',
                            context: actionContactId,
                          });


                          let result =
                            await this.ghlApiService.makeGhlCalendarAppointment(
                              calendarAccToken,
                              actionLocationId,
                              calendarId,
                              actionContactId,
                              startDate,
                              endDate,
                            );


                          if (result?.error) {
                            await this.myLogger.log({
                              message: `Generated Error in GHL Calendar write action: ${JSON.stringify(
                                result,
                              )}`,
                              context: actionContactId,
                            });  
                            if (
                              (result?.error ?? '').includes(
                                'no longer available',
                              )
                            ) {
                              errorHandledMessage =
                                await this.sessionService.handleWriteActionFailure(
                                  agent,
                                  organization,
                                  processedQueryMessage,
                                  body,
                                  contactId,
                                );
                            }

                            //log the error handled message
                            await this.myLogger.log({
                              message: `Error handled in GHL Calendar write action with new message: ${errorHandledMessage}`,
                              context: actionContactId,
                            });   


                          
                            if (!errorHandledMessage) {
                              rolloverEvent.rollover = true;
                              rolloverEvent.reason = result?.error;
                              if (result?.error) {
                                botErrors.push(result?.error);
                              }
                            } else {
                              isSilent = false;
                            }
                          }
                
                        }
                      } else {
                        await this.myLogger.log({
                          message: 'No future events found for the contact.',
                          context: actionContactId,
                        });

                        let result =
                          await this.ghlApiService.makeGhlCalendarAppointment(
                            calendarAccToken,
                            actionLocationId,
                            calendarId,
                            actionContactId,
                              startDate,
                              endDate,
                            );

                            if (result?.error) {
                              await this.myLogger.log({
                                message: `Generated Error in GHL Calendar write action: ${JSON.stringify(
                                  result,
                                )}`,
                                context: actionContactId,
                              });  
                              if (
                                (result?.error ?? '').includes(
                                  'no longer available',
                                )
                              ) {
                                errorHandledMessage =
                                  await this.sessionService.handleWriteActionFailure(
                                    agent,
                                    organization,
                                    processedQueryMessage,
                                    body,
                                    contactId,
                                  );
                              }
  
                              //log the error handled message
                              await this.myLogger.log({
                                message: `Error handled in GHL Calendar write action with new message: ${errorHandledMessage}`,
                                context: actionContactId,
                              });   
  
  
                            
                              if (!errorHandledMessage) {
                                rolloverEvent.rollover = true;
                                rolloverEvent.reason = result?.error;
                                if (result?.error) {
                                  botErrors.push(result?.error);
                                }
                              } else {
                                isSilent = false;
                              }
                            }
                      }
                    } else if (ghlEvaluateOn == 'everyTurn') {
                      if (actions?.eventData?.tag?.silent || actions?.silent) {
                        isSilent =
                          actions?.eventData?.tag?.silent || actions?.silent;
                      }

                      let result;
                      try {
                        if (cancelEvent && future_events_ghl.events.length > 0 && actions.action === 'cancel') {
                          let eventBelongsToContact = future_events_ghl.events.filter(event => event.contactId === actionContactId);

                          let eventId = eventBelongsToContact[0].id;

                          result =
                            await this.ghlApiService.deleteCalendarEvents({
                              tokens: calendarAccToken,
                              locationId: actionLocationId,
                              eventId: eventId,
                            });

                          actions.silent = false;

                          this.myLogger.log({
                            message: `Canceled GHL calendar event contact ${actionContactId} with event ${JSON.stringify(result)}`,
                            context: `${contactId}`
                          })
                        } else if (rescheduleEvent && future_events_ghl.events.length > 0 && actions.action === 'reschedule') {

                          //need to check if the event belongs to the contact
                          let eventBelongsToContact = future_events_ghl.events.filter(event => event.contactId === actionContactId);

                          let eventId = eventBelongsToContact[0].id;

                          result =
                            await this.ghlApiService.updateCalendarEvents({
                              tokens: calendarAccToken,
                              locationId: actionLocationId,
                              calendarId: calendarId,
                              eventId: eventId,
                              startDate: startDate,
                              endDate: endDate,
                            });

                          actions.silent = false;

                          this.myLogger.log({
                            message: `Rescheduled GHL calendar event contact ${actionContactId} with event ${JSON.stringify(result)}`,
                            context: `${contactId}`
                          })
                        } else {
                            result =
                              await this.ghlApiService.makeGhlCalendarAppointment(
                                calendarAccToken,
                                actionLocationId,
                                calendarId,
                                actionContactId,
                              startDate,
                              endDate,
                            );

                            if (result?.error) {
                              await this.myLogger.log({
                                message: `Generated Error in GHL Calendar write action: ${JSON.stringify(
                                  result,
                                )}`,
                                context: actionContactId,
                              });  
                              if (
                                (result?.error ?? '').includes(
                                  'no longer available',
                                )
                              ) {
                                errorHandledMessage =
                                  await this.sessionService.handleWriteActionFailure(
                                    agent,
                                    organization,
                                    processedQueryMessage,
                                    body,
                                    contactId,
                                  );
                              }
  
                              //log the error handled message
                              await this.myLogger.log({
                                message: `Error handled in GHL Calendar write action with new message: ${errorHandledMessage}`,
                                context: actionContactId,
                              });   
                            
                              if (!errorHandledMessage) {
                                rolloverEvent.rollover = true;
                                rolloverEvent.reason = result?.error;
                                if (result?.error) {
                                  botErrors.push(result?.error);
                                }
                              } else {
                                isSilent = false;
                              }
                            }
                        }
                      } catch (err) {
                        botErrors.push(err.response.data.message);
                        this.myLogger.error({
                          message: `reqId ${reqId} | ${err.response.data.message}`,
                          context: `${contactId}`,
                        });
                      }
                    }
                  } else {
                    this.myLogger.log({
                      message: `Calendar date is TBD , not creating appointment`,
                      context: `${contactId}`,
                    });
                  }
                }
                
                if (
                  tagsToAdd.some(tag =>
                    (typeof tag === 'object' && tag.name === 'fallback_reached')
                  ) &&
                  agent?.fallbackConfig?.enabled
                ) {
                  const fallbackTagIndex = tagsToAdd.findIndex(tag =>
                    (typeof tag === 'object' && tag.name === 'fallback_reached')
                  );

                  if (fallbackTagIndex !== -1) {
                    // Remove the fallback_reached tag
                    tagsToAdd.splice(fallbackTagIndex, 1);

                    this.myLogger.log({
                      message: `Adding fallback tag: ${agent?.fallbackConfig?.tagToAdd} | contactId: ${contactId} | locationId: ${locationId}`,
                      context: `${contactId}`,
                    });

                    tagsToAdd.push({
                      name: agent?.fallbackConfig?.tagToAdd || "fallback_reached",
                      silent: agent?.fallbackConfig?.setSilent
                    });
                  }
                }


                // Check if any tag has silent: true and set isSilent accordingly
                isSilent = tagsToAdd.some(tag => tag.silent === true) || actions?.silent
              } catch (err) {
                this.myLogger.error({
                  message:
                    `webhook action type ${actions?.kind} | location Id: ${channelLocationId}. Error: ` +
                    err.message,
                  context: `${actionContactId}`,
                });
              }
            }

            let finalResponse = errorHandledMessage ? errorHandledMessage : botResponse?.finalResponse 

            //if the finalResponse contains any string like assistant: or user: remove it
            if (typeof finalResponse === 'string') {
              finalResponse = finalResponse
                .replace(/(assistant:|user:)/gi, '') // remove anywhere (case-insensitive)
                .trim();
            }

           
            if (finalResponse) {
              // Check if the task is 'evaluate' or 'Evaluate'
              if (
                overrides?.task === 'evaluate' ||
                overrides?.task === 'Evaluate' ||
                triggerTask === 'evaluate'
              ) {
                this.myLogger.log({
                  message: `@webhook Task is 'evaluate' or 'Evaluate', no message sent | contactId: ${contactId} | locationId: ${locationId}`,
                  context: `${contactId}`,
                });
              } else {
                // Task is something other than 'evaluate', proceed to send message
              

                if (!isSilent || errorHandledMessage) {
                  try {
                    let result;
                    try {
                      result = await this.ghlApiService.sendMessage(
                        tokens,
                        channelLocationId,
                        {
                          type: messageType,
                          contactId: contactId,
                          message: finalResponse,
                        },
                        email,
                      );

                      //if the message is not silent, save the conversation
                      await this.saveGhlConversation({
                        message: finalResponse,
                        payload,
                        direction: 'outbound',
                        channelName,
                        agentId: agent._id.toString() || '',
                        aiProviderName,
                        status: 'pending',
                        visibleActions,
                        tokensCount,
                          messageType,
                          source: overrides.source ?? payload.source,
                      });
                      
                      if (result?.messageId && agent?.markUnreadAfterReply) {
                        await this.ghlApiService.markConversationUnread(tokens, overrides.conversationId, locationId, contactId)
                      }
                    } catch (err) {
                      if (err.response && err.response.status >= 500) {
                        this.myLogger.error({
                          message: `Server error while sending GHL message | reqId ${reqId} | ${err.response.data.message}`,
                          context: `${contactId}`,
                        });
                        await this.taskService.createGhlWorkflowTaskWithExponentialBackoff(
                          payload,
                          contactId, // Pass the contactId for logging
                          300, // 5 minutes from now
                        );
                        return; // Stop execution
                      }
                      botErrors.push(err.response.data.message);
                      this.myLogger.error({
                        message: `reqId ${reqId} | ${err.response.data.message}`,
                        context: `${contactId}`,
                      });
                    }

                    if (result?.error) {
                      botErrors.push(result?.error);
                      rolloverEvent.rollover = true;
                      rolloverEvent.reason = result?.error;
                      rolloverEvent.date = new Date();
                      this.myLogger.error({
                        message: `@webhook Error sending message | contactId: ${contactId} | locationId: ${locationId} | error: ${result?.error}`,
                        context: `${contactId}`,
                      });
                    } else {
                      rolloverEvent.responded = true;
                      const followUpConfig = agentFollowupConfigOverride ?? agentFollowupConfig?.[agent?._id?.toString()]
                      if (followUpConfig?.isFollowupEnabled && currAttemptCount!== undefined && currAttemptCount < (followUpConfig?.maxAttempts ?? 0) ) {
                        const scheduledFollowUpResponse = await this.schedulerService.scheduleFollowUp({
                          followUpConfig,
                          agentId: agent._id.toString(),
                          contactFollowUpInfo: {
                            attemptCount: currAttemptCount,
                            lastAttemptAt: contactFollowUpInfo?.lastAttemptAt,
                            contactId,
                            resourceId: locationId,
                            followUpTaskId: contactFollowUpInfo?.followUpTaskId,
                          },
                          payload: overrides
                        });

                        await this.ghlWebhookConversationService.updateContactFollowupRecord({
                          contactId,
                          followUpTaskId: scheduledFollowUpResponse?.followUpTaskId,
                          lastAttemptAt: scheduledFollowUpResponse?.lastAttemptAt,
                        })
                      } else {
                        this.myLogger.log({ message: `Follow Up not Enabled | ${payload?.contactId}`, context: 'FOLLOW UP' });
                      }
                      this.myLogger.log({
                        message: `@webhook Message sent successfully | contactId: ${contactId} | locationId: ${locationId}`,
                        context: `${contactId}`,
                      });
                    }
                  } catch (err) {
                    this.myLogger.error({
                      message: `@webhook Exception while sending message | contactId: ${contactId} | locationId: ${locationId} | error: ${err.message}`,
                      context: `${contactId}`,
                    });
                  }
                } else {
                  this.myLogger.log({
                    message: `@webhook Marked as silent by bot | contactId: ${contactId} | locationId: ${locationId}`,
                    context: `${contactId}`,
                  });
                }
              }
            } else {
              this.myLogger.log({
                message: `@webhook No bot response generated for contactId: ${contactId} | locationId: ${locationId}`,
                context: `${contactId}`,
              });
            }

            if (customFieldsToUpdate.length > 0) {
              for (const customField of customFieldsToUpdate) {
                try {
                  await this.updateCustomField(
                    locationId,
                    contactId,
                    customField,
                  );
                } catch (error) {
                  this.myLogger.error({
                    message: `@error updating custom field: ${error.message}`,
                    context: 'updateCustomField.performAction',
                  });
                  botErrors.push(
                    `Failed to update custom field: ${error.message}`,
                  );
                }
              }
            }
            if (tagsToAdd.length > 0)
              try {
                await this.ghlApiService.addTag(
                  tokens,
                  channelLocationId,
                  contactId,
                  ...tagsToAdd,
                );
              } catch (err) {
                if (err.response && err.response.status >= 500) {
                  this.myLogger.error({
                    message: `@error adding tags | reqId ${reqId} : ${err?.message}`,
                    context: 'performAction',
                  });
                  await this.taskService.createGhlWorkflowTaskWithExponentialBackoff(
                    payload,
                    contactId, // Pass the contactId for logging
                    300, // 5 minutes from now
                  );
                  return; // Stop execution
                }
                botErrors.push(
                  `Error | reqId ${reqId} encountered while adding tags : ${err?.message} for location Id: ${channelLocationId} | contactId: ${contactId}`,
                );

                this.myLogger.error({
                  message: `@error adding tags | reqId ${reqId} : ${err?.message}`,
                  context: 'performAction',
                });
              }
          } catch (err) {
            this.myLogger.error({
              message: `@error sending outbound message | reqId ${reqId} : ${err?.message}`,
              context: 'performAction',
            });
            botErrors.push(
              `Error | reqId ${reqId} encountered while sending message : ${err?.message} for location Id: ${channelLocationId} | contactId: ${contactId}`,
            );
          }
        }
        this.myLogger.log({
          message: `WEBHOOK EXECUTED | locationId ${channelLocationId} & contactId: ${contactId}...`,
          context: 'performAction',
        });

        this.myLogger.log({
          message: `Bot Errors: ${botErrors.length} | locationId ${channelLocationId} & contactId: ${contactId}...`,
          context: contactId,
        });

        if (botErrors.length > 0) {
          await this.handleGhlErrors(
            botErrors,
            channelLocationId,
            contactId,
            type,
            tokens,
          );
        }
        if (rolloverEvent.rollover && payload.direction === 'inbound') {
          await this.handleRolloverEvent(rolloverEvent, payload.locationId);
        }
      })();
    } catch (err) {
      if (rolloverEvent.responded === false) {
        rolloverEvent.rollover = true;
        rolloverEvent.reason = err.message;
        rolloverEvent.date = new Date();
      }
      this.myLogger.error({
        message: `@error ghl webhook | reqId ${reqId} | locationId ${payload.locationId
          }, contactId ${payload.contactId} : ${err?.response?.data?.message ?? err?.message
          }`,
        context: 'performAction',
      });
      if (rolloverEvent.rollover && payload.direction === 'inbound') {
        await this.handleRolloverEvent(rolloverEvent, payload.locationId);
      }
    }
  }
  async saveGhlConversation({
    message = '',
    payload,
    direction = 'inbound',
    channelName,
    agentId,
    aiProviderName,
    errors = [],
    status = 'pending',
    visibleActions = [],
    saveToken = true,
    tokensCount = 0,
    messageType = '',
    orgId = '',
    source = 'workflow',
    conversationId = undefined,
  }) {
    try {
      if (payload?.body || message) {
        const existingConvoRecord =
          await this.mongoConversationService.isAlreadySaved({
            resourceId: payload.locationId,
            contactId: payload.contactId,
          });
        // let vectorTokens: any[] | Uint32Array = [];
        if (!(saveToken && direction == 'outbound')) tokensCount = 0;
        if (existingConvoRecord) {
          const updateConversation = {
            $addToSet: {
              conversation: {
                body: message,
                dateAdded: new Date(),
                direction: direction,
                aiProvider: aiProviderName,
                tokens: tokensCount,
                actions: visibleActions,
                messageType,
                status,
              },
            },
          };
          updateConversation['$set'] = {
            channel: channelName,
            source,
          };
          if (agentId) updateConversation['$set']['agentId'] = agentId;
          if (orgId) updateConversation['$set']['orgId'] = orgId;
          if (conversationId)
            updateConversation['$set']['conversationId'] = conversationId;
          if (tokensCount) {
            updateConversation['$inc'] = {
              totalTokens: tokensCount,
            };
          }
          await this.mongoConversationService.updateConversation({
            query: {
              _id: existingConvoRecord._id.toString(),
            },
            updateBody: updateConversation,
          });
          // save to pinecone
          this.myLogger.log({
            message: `@conversation updated: direction '${direction}' | locationId '${payload.locationId}' | contactId '${payload.contactId}'`,
          });
        } else {
          const createConversation: CreateConversationDto = {
            conversation: [
              {
                body: payload.body,
                dateAdded: new Date(),
                direction: direction,
                tokens: tokensCount,
                aiProvider: aiProviderName,
                actions: visibleActions,
                messageType,
                status,
              },
            ],
            type: payload.messageType,
            contactId: payload.contactId,
            resourceId: payload.locationId,
            agentId,
            orgId,
            updatedAt: new Date(),
            totalTokens: tokensCount,
            channel: channelName,
            source,
            conversationId,
          };

          await this.mongoConversationService.createConversation(
            createConversation,
          );
          this.myLogger.log({
            message: `@conversation saved: direction '${direction}' | locationId '${payload.locationId}' | contactId '${payload.contactId}'`,
          });
        }
      }
    } catch (error) {
      this.myLogger.error({
        message: `error encountered while saving conversation for resourceId ${payload.locationId} | contactId '${payload.contactId} : ${error.message}`,
      });
    }
  }

  async filterAgents(
    orgId: string,
    agentIds: string[],
    channel: string,
    accountId: string,
    tags: string[],
    reqId?: string,
    proxied = false,
  ) {
    try {
      if (channel === 'Custom') {
        throw new Error(`Custom channel not supported yet`);
      }


      const agents = await this.mongoAgentService.getAgents({
        _id: { $in: agentIds },
        disabled: false,
        triggers: {
          $elemMatch: {
            'data.subaccount': accountId,
            'data.channel': channel,
          },
        },
      });

      if (agents.length === 0) {
        this.myLogger.error({
          message: `No agents found for this leadconnector subaccount | orgId: ${orgId} | accountId: ${accountId} | channel: ${channel} | tags: ${tags}`,
          context: 'GHL FILTER AGENTS',
        });
      }

      let activeTriggerIds: {
        triggerId: string;
        agentId: string;
      }[] = []

      let { filteredAgents, triggerFollowupConfig, triggerOverrides } = agents
        .map((agent) => {
          const { triggers } = agent;
          const filteredTriggers = triggers.filter((trigger) =>
          {
            const triggerMatched = this.ghlWebhookHelperService.isTriggerMatching(trigger, accountId, channel, tags);
            activeTriggerIds.push({
              triggerId: trigger.triggerId,
              agentId: agent._id.toString()
            });
            return triggerMatched;
          }
          );
          return { agent, filteredTriggers };
        })
        .filter(({ filteredTriggers }) => filteredTriggers.length > 0)
        .reduce((acc, { agent, filteredTriggers }) => {
          const agentId = agent._id.toString();
          const agentTriggerData = filteredTriggers.map((trigger) => this.ghlWebhookHelperService.extractTriggerData(trigger, accountId)).filter(data => data.override);

          agentTriggerData.forEach((data) => {
            acc.triggerOverrides[agentId] = data.override;
            if (data.followup) {
              acc.triggerFollowupConfig[agentId] = data.followup;
            }
          });
          acc.filteredAgents.push(agent);
          return acc;
        }, { filteredAgents: [], triggerOverrides: {}, triggerFollowupConfig: {} });


      this.myLogger.log({
        message: `filtered agents count - ${filteredAgents.length
          } | orgId: ${orgId} | accountId: ${accountId} | channel: ${channel} | tags: ${tags} | trigger overrides: ${JSON.stringify(
            triggerOverrides,
          )} | triggers - ${JSON.stringify(activeTriggerIds)}`,
        context: 'GHL FILTER AGENTS',
      });
      return [filteredAgents, '', triggerOverrides, triggerFollowupConfig];
    } catch (err) {
      this.myLogger.error({
        message: `reqId ${reqId} | error filterAgents: ${err.message}`,
        context: 'GHL Service - FilterAgents',
      });
      return [[], err.message.includes('Custom') ? err.message : '', undefined, undefined];
    }
  }

  async getConversationString(
    locationId?: string,
    contactId?: string,
    history?: number,
  ) {
    try {
      let query = {
        contactId: contactId,
      };
      if (locationId) {
        query['resourceId'] = locationId;
      }
      const conversationDoc =
        await this.mongoConversationService.getConversation({
          query: query,
        });
      let date;
      if (history) {
        date = new Date();
        if (history < 1) date.setMinutes(date.getMinutes() - 1);
        else date.setHours(date.getHours() - history);
      }

      let conversationString = '';

      let prevDirection = '';
      for (const conversation of conversationDoc?.conversation || []) {
        if (conversation?.messageType === 'Email') continue;
        if (history) {
          if (conversation.dateAdded < date) continue;
        }
        if (conversation.direction === 'inbound') {
          if (prevDirection === 'inbound') conversationString += '\n';
          conversationString += `user: ` + conversation.body + '\n';
          prevDirection = 'inbound';
        } else if (conversation.direction === 'outbound') {
          conversationString += `assistant: ` + conversation.body + '\n\n';
          prevDirection = 'outbound';
        }
      }
      if (prevDirection === 'inbound') conversationString += '\n';

      let lastConversationSource = conversationDoc?.source;
      let conversationUpdatedAt = conversationDoc?.updatedAt;
      let contactFollowUpInfo = conversationDoc?.followUp;
      let disconnectBot = conversationDoc?.disconnectBot;
      return {
        conversationString,
        lastConversationSource,
        conversationUpdatedAt,
        contactFollowUpInfo,
        disconnectBot
      };
    } catch (error) {
      this.myLogger.error({
        message:
          `error encountered while getting conversation string for location ${locationId} | contactId ${contactId} :` +
          error.message,
          context: `Get conversation string`
      });
      return {
        conversationString: '',
        lastConversationSource: '',
        conversationUpdatedAt: undefined,
        followUpConfig: undefined,
        disconnectBot: false
      };
    }
  }

  async addTokenToCredential(
    locationId: string,
    accessToken: string,
    refreshToken: string,
  ) {
    try {
      await this.mongoCredentialService.updateCredential(
        {
          keyId: locationId,
          kind: KINDS.GHL_CREDENTIAL,
        },
        {
          creds: {
            accessToken: accessToken,
            refreshToken: refreshToken,
          },
        },
      );
    } catch (error) {
      this.myLogger.error({
        message:
          'error encountered while adding token to credential: ' +
          error.message,
      });
      throw error;
    }
  }

  async handleGhlErrors(
    errors: string[],
    locationId: string,
    contactId: string,
    type: string,
    tokens?,
  ) {
    try {
      const errString = errors.join('\n');

      this.myLogger.error({
        message: `Error encountered while GHL operation: ${errString} | locationId: ${locationId} | contactId: ${contactId}`,
        context: contactId,
      });

      if (type == 'ghl') {
        if (!tokens) {
          const cred = await this.mongoCredentialService.getCredential({
            keyId: locationId,
            kind: KINDS.GHL_CREDENTIAL,
          });
          if (cred?.creds?.accessToken == undefined)
            throw new Error(`LocationId ${locationId} not connected to V3`);
          tokens = {
            access_token: cred.creds?.accessToken,
            refresh_token: cred.creds?.refreshToken,
          };
        }

        await this.ghlApiService.addNoteToContact(
          tokens,
          locationId,
          contactId,
          `error: ${errString}`,
        );
        this.myLogger.error({
          message: `Errors added to contact note : ${JSON.stringify(
            errString,
          )} | locationId: ${locationId} | contactId: ${contactId}`,
          context: contactId,
        });
      }

      // Fetch organization data
      const organization = await this.organizationService.getOrganization(
        {
          'connections.channels.keyId': locationId,
        },
        {
          connections: 1,
          name: 1,
          errorReport: 1,
        },
      );
      let emails = organization?.errorReport?.emails || [];
      let slackChannelIds = organization?.errorReport?.slackChannelIds || [];
      let errorReportDisabled = organization?.errorReport?.disabled || false;

      // Check if error reporting is disabled
      if (errorReportDisabled) {
        this.myLogger.log({
          message: `Reporting is disabled, no notifications will be sent. ContactId: ${contactId} LocationId: ${locationId}`,
          context: contactId,
        });
        return;
      }

      // HTML version for email (uses HTML tags)
      const errorMessageForEmail = `
  <p>Dear Team,</p>
  <p>An error occurred during the operation:</p>
  <p><strong>Location ID:</strong> ${locationId}</p>
  <p><strong>Contact ID:</strong> ${contactId}</p>
  <p><strong>Error Status:</strong> ${errString || 'N/A'}</p>
  <p><strong>Error Message:</strong> ${errString || 'No further details available'
        }</p>

  <p>Please investigate the issue or contact us at your earliest convenience.</p>
  <p>Regards,<br/>Capri AI Support Team</p>
`;

      // Text version for Slack (plain text)
      const errorMessageForSlack = `
  Error Report:
  - Location ID: ${locationId}
  - Contact ID: ${contactId}
  - Error Message: ${errString || 'No further details available'}
  
  
  Please investigate the issue or contact us at your earliest convenience.
  - Capri AI Support Team
`;

      // Now use errorMessageForEmail for email notifications
      // and errorMessageForSlack for Slack notifications

      // Proceed only if there are emails or slack channels to notify
      if (emails.length > 0 || slackChannelIds.length > 0) {
        if (emails.length > 0 && process.env.GHL_CAPRI_CLIENT_ACQUISITION_CREDENTIAL_ID) {
          // Fetch GHL credentials for email notifications
          const credentialData =
            await this.mongoCredentialService.getCredential({
              _id: process.env.GHL_CAPRI_CLIENT_ACQUISITION_CREDENTIAL_ID,
              kind: KINDS.GHL_CREDENTIAL,
            });

          const tokens = {
            access_token: credentialData['creds'].accessToken,
            refresh_token: credentialData['creds'].refreshToken,
          };

          const contactIds = await Promise.all(
            emails.map((email) =>
              this.ghlApisService
                .upsertContact(tokens, credentialData.keyId, email)
                .then((contact) => {
                  this.myLogger.log({
                    message: `Contact created/upserted for email ${email}`,
                    context: contactId,
                  });
                  return contact;
                }),
            ),
          );

          // Send email to each contact
          for (const contact of contactIds) {
            try {
              this.myLogger.log({
                message: `sending email notification to contact ${contact.contact.id} for locationId ${locationId}`,
                context: contactId,
              });

              const cacheKey = `error_channel_${errString}_${locationId}_${contact.contact.id}`;
              const cacheEntry = await this.cacheService.getData(cacheKey);

              if (!cacheEntry) {
                // Store in cache for 24 hours
                await this.cacheService.postData(cacheKey, {
                  value: true,
                  ttl: 86400000, // 24 hrs
                });

                let emailResult = await this.ghlApisService.sendMessage(
                  tokens,
                  locationId,
                  {
                    type: 'Email',
                    html: errorMessageForEmail,
                    contactId: contact.contact.id,
                    emailFrom: '<EMAIL>',
                    subject: `CAPRI AI - ATTENTION REQUIRED`,
                  },
                  '<EMAIL>',
                );

                this.myLogger.log({
                  message: `Result email notification to contact ${contact.contact.id
                    } for locationId ${locationId}: ${JSON.stringify(emailResult) || emailResult
                    }`,
                  context: contactId,
                });
              } else {
                this.myLogger.log({
                  message: `Skipping email notification to contact ${contact.contact.id} for locationId ${locationId}`,
                  context: contactId,
                });
              }
            } catch (emailError) {
              this.myLogger.error({
                message: `Error sending email notification to contact ${contact.contact.id}`,
                context: contactId,
              });
            }
          }
        }

        // Send Slack notifications if slackChannelIds are available
        if (slackChannelIds.length > 0) {
          for (const slackChannelId of slackChannelIds) {
            try {
              // Find corresponding dataSourceData for Slack channel
              const dataSourceData = organization.connections.channels.find(
                (dataSource) => dataSource.keyId === slackChannelId,
              );

              if (dataSourceData) {
                const credentialId = dataSourceData.credentialId;

                const credentialDatForSlack =
                  await this.mongoCredentialService.getCredential({
                    _id: credentialId,
                    kind: KINDS.SLACK_CREDENTIAL,
                  });

                // Send Slack message
                const slackResponse = await this.slackApiService.sendMessage(
                  credentialDatForSlack.creds,
                  slackChannelId,
                  errorMessageForSlack,
                );
                this.myLogger.log({
                  message: `Slack response for channel ${slackChannelId}: ${JSON.stringify(
                    slackResponse,
                  )}`,
                  context: contactId,
                });
              }
            } catch (slackError) {
              this.myLogger.error({
                message: `Error sending Slack notification to channel ${slackChannelId}`,
                context: contactId,
              });
            }
          }
        }
      }
    } catch (error) {
      this.myLogger.error({
        message:
          '@error encountered while handling ghl errors: ' + error.message,
        context: contactId,
      });
    }
  }

  async deleteContacts(payload: IObject) {
    try {
      await this.mongoConversationService.deleteConversations({
        query: {
          contactId: payload.id,
          // 'resourceId': payload.locationId,
        },
      });

      this.myLogger.log({
        message: `contact deleted | locationId ${payload.locationId} | contactId ${payload.id}`,
        context: `${payload.id}`,
      });
    } catch (err) {
      this.myLogger.error({
        message:
          `@error encountered while deleting contact ${payload.id} | locationId ${payload.locationId} : ` +
          err.message,
        context: `${payload.id}`,
      });
    }
  }

  async processOutbound({
    message = '',
    payload,
    direction,
    channelName = '',
    agentId = '',
    aiProviderName = '',
    saveToken = true,
    tokensCount = 0,
    messageType = '',
    orgId = '',
    source = 'workflow',
    conversationId = undefined,
  }) {
    if (message === '') return;
    try {
      // already existing conversation entry
      const updated = await this.mongoConversationService.updateConversation({
        query: { contactId: payload.contactId, 'conversation.body': message },
        updateBody: {
          $set: {
            'conversation.$.status': 'delivered',
            direction: direction,
            rollover: false,
            rolloverReason: '',
            source: source,
          },
        },
      });
      if (updated.modifiedCount == 0) throw new Error('new outbound');
    } catch (err) {
      if (err.message === 'new outbound') {
        const existingContact =
          await this.mongoConversationService.isAlreadySaved({
            contactId: payload.contactId,
          });
        // let vectorTokens: any[] | Uint32Array = [];
        if (!(saveToken && direction == 'outbound')) tokensCount = 0;
        if (existingContact) {
          // new conversation entry for an existing contact
          await this.mongoConversationService.updateConversation({
            query: { contactId: payload.contactId },
            updateBody: {
              $push: {
                conversation: {
                  body: message,
                  dateAdded: new Date(),
                  status: 'delivered',
                  direction,
                  tokens: tokensCount,
                  messageType,
                  aiProvider: aiProviderName,
                  actions: [],
                },
              },
              $set: {
                rollover: false,
                rolloverReason: '',
                source: source,
                conversationId: conversationId,
              },
            },
          });
        } else {
          // create new contact
          await this.mongoConversationService.createConversation({
            conversation: [
              {
                body: payload.body,
                dateAdded: new Date(),
                direction: direction,
                tokens: tokensCount,
                aiProvider: aiProviderName,
                actions: [],
                messageType,
                status: 'delivered',
              },
            ],
            type: payload.messageType,
            contactId: payload.contactId,
            resourceId: payload.locationId,
            conversationId,
            agentId,
            orgId,
            totalTokens: tokensCount,
            updatedAt: new Date(),
            channel: channelName,
            rollover: true,
            rolloverReason: '',
            source: source,
          });
        }
      }
    }
  }

  async getCalendarsList(reqId: string, locationId: string) {
    const creds = await this.mongoCredentialService.getCredential({
      keyId: locationId,
      kind: KINDS.GHL_CREDENTIAL,
    });

    if (!creds)
      throw new Error(
        `reqId ${reqId} | No credential found for locationId ${locationId}`,
      );
    const tokens = {
      access_token: creds.creds.accessToken,
      refresh_token: creds.creds.refreshToken,
    };

    const { calendars = [] } = await this.ghlApiService.getGhlCalendars(
      tokens,
      locationId,
    );
    return calendars;
  }

  async getAllLocationTags(reqId: string, locationId: string) {
    const creds = await this.mongoCredentialService.getCredential({
      keyId: locationId,
      kind: KINDS.GHL_CREDENTIAL,
    });

    if (!creds)
      throw new Error(
        `reqId ${reqId} | No credential found for locationId ${locationId}`,
      );
    const tokens = {
      access_token: creds.creds.accessToken,
      refresh_token: creds.creds.refreshToken,
    };

    const { tags = [] } = await this.ghlApiService.getLocationTags(
      tokens,
      locationId,
    );
    return tags;
  }

  async createContact(
    reqId: string,
    locationId: string,
    name: any,
    email?: string,
    tags?: string[],
  ) {
    const creds = await this.mongoCredentialService.getCredential({
      keyId: locationId,
      kind: KINDS.GHL_CREDENTIAL,
    });

    if (!creds)
      throw new Error(
        `reqId ${reqId} | No credential found for locationId ${locationId}`,
      );
    const tokens = {
      access_token: creds.creds.accessToken,
      refresh_token: creds.creds.refreshToken,
    };

    let data = {
      locationId,
      name,
      email: email || '',
      tags,
    };

    const contact = await this.ghlApiService.createContact(
      tokens,
      locationId,
      data,
      email,
    );
    return contact?.id ? contact : contact.contact;
  }

  async updateCustomField(
    locationId: string,
    contactId: string,
    field: {
      fieldKey: string;
      field_value: string;
    },
  ) {
    try {
      const creds = await this.mongoCredentialService.getCredential({
        keyId: locationId,
        kind: KINDS.GHL_CREDENTIAL,
      });

      if (!creds)
        throw new Error(`No credential found for locationId ${locationId}`);
      const tokens = {
        access_token: creds.creds.accessToken,
        refresh_token: creds.creds.refreshToken,
      };

      let fieldName = field.fieldKey.replace('contact.', '');
      if (fieldName === 'postal_code') {
        fieldName = 'postalCode';
      } else if (fieldName === 'first_name') {
        fieldName = 'firstName';
      } else if (fieldName === 'last_name') {
        fieldName = 'lastName';
      } else if (fieldName === 'date_of_birth') {
        fieldName = 'dateOfBirth';
      } else if (fieldName === 'company_name') {
        fieldName = 'companyName';
      }

      if (standardFields.includes(fieldName)) {
        const updateData = { [fieldName]: field.field_value };
        const updatedContact = await this.ghlApiService.updateContact(
          tokens,
          contactId,
          updateData,
        );

        this.myLogger.log({
          message: `Standard field ${fieldName} updated for locationId ${locationId} `,
        });

        return updatedContact;
      } else {
        // Proceed with custom field update logic
        let customFieldsList = await this.ghlApiService.listCustomFields(
          tokens,
          locationId,
        );

        let matchingCustomField = customFieldsList.customFields.find(
          (customField) => customField.fieldKey === field.fieldKey,
        );

        if (!matchingCustomField) {
          this.myLogger.log({
            message: `No custom field ${field.fieldKey} found for locationId ${locationId}`,
          });
          return null;
        } else {
          const customFields = [
            {
              id: matchingCustomField.id,
              field_value: field.field_value,
            },
          ];

          const { contact = {} } =
            await this.ghlApiService.updateContactCustomFields(
              tokens,
              contactId,
              customFields,
            );

          this.myLogger.log({
            message: `Custom field ${field.fieldKey} updated for locationId ${locationId} `,
          });

          return contact;
        }
      }
    } catch (error) {
      this.myLogger.error({
        message: `Error updating custom field for locationId ${locationId}: ${error.message}`,
        context: this.updateCustomField.name,
      });
      throw error;
    }
  }

  async getContact(
    reqId: string,
    locationId: string,
    contactId: string,
    email?: string,
  ) {
    const creds = await this.mongoCredentialService.getCredential({
      keyId: locationId,
      kind: KINDS.GHL_CREDENTIAL,
    });

    if (!creds)
      throw new Error(
        `reqId ${reqId} | No credential found for locationId ${locationId}`,
      );
    const tokens = {
      access_token: creds.creds.accessToken,
      refresh_token: creds.creds.refreshToken,
    };

    const { contact } = await this.ghlApiService.getContact(
      tokens,
      locationId,
      contactId,
      email,
    );
    return contact;
  }

  async createGHLCustomFieldObject(
    reqId: string,
    locationId: string,
    contactCustomFields: {
      id: string;
      value: string;
    }[],
    contact,
  ) {
    try {
      const creds = await this.mongoCredentialService.getCredential({
        keyId: locationId,
        kind: KINDS.GHL_CREDENTIAL,
      });

      if (!creds)
        throw new Error(
          `reqId ${reqId} | No credential found for locationId ${locationId}`,
        );
      const tokens = {
        access_token: creds.creds.accessToken,
        refresh_token: creds.creds.refreshToken,
      };

      let { customFields } = await this.ghlApiService.listCustomFields(
        tokens,
        locationId,
      );

      let customFieldsObj = {};
      for (const field of contactCustomFields) {
        customFieldsObj[field.id] = field.value;
      }

      let customFieldsList = [];
      for (const field of customFields) {
        customFieldsList.push({
          id: field.id,
          value: customFieldsObj[field.id] || '',
          name: field.name,
          fieldKey: `{{` + field.fieldKey + `}}`,
        });
      }
      for (const field of standardFields) {
        customFieldsList.push({
          id: '',
          value: contact[field] || '',
          name: field,
          fieldKey: `{{contact.` + field + `}}`,
        });
      }

      return customFieldsList;
    } catch (error) {
      this.myLogger.error({
        message: `@error createGHLCustomFieldObject: ${error.message}`,
      });
    }
  }

  async handleRolloverEvent(
    rolloverEvent: rolloverEventDto,
    locationId?: string,
  ) {
    try {
      let query = {};
      if (locationId) query['resourceId'] = locationId;
      query['contactId'] = rolloverEvent.contactId;
      const res = await this.mongoConversationService.updateConversation({
        query,
        updateBody: {
          $set: {
            rollover: rolloverEvent.rollover,
            rolloverReason: rolloverEvent.reason,
            rolloverDate: rolloverEvent.date,
          },
        },
      });
      this.myLogger.log({
        message: `Rollover event handled for contactId: ${rolloverEvent.contactId
          } | locationId: ${locationId} | result: ${JSON.stringify(res)}`,
        context: rolloverEvent.contactId,
      });
    } catch (error) {
      this.myLogger.error({
        message: `error handle RolloverEvent: ${error.message}`,
        context: 'handleRolloverEvent',
      });
    }
  }

  async searchConversations(
    reqId: string,
    locationId: string,
    params: {
      assignedTo?: string;
      contactId?: string;
      id?: string;
      lastMessageAction?: string;
      lastMessageDirection?: string;
      lastMessageType?: string;
      limit?: number;
      query?: string;
      scoreProfile?: string;
      scoreProfileMax?: number;
      scoreProfileMin?: number;
      sort?: string;
      sortBy?: string;
      sortScoreProfile?: string;
      startAfterDate?: string;
      status?: string;
    },
  ) {
    try {
      const creds = await this.mongoCredentialService.getCredential({
        keyId: locationId,
        kind: KINDS.GHL_CREDENTIAL,
      });

      if (!creds) {
        this.myLogger.error({
          message: `reqId ${reqId} | No credential found for locationId ${locationId}`,
          context: 'ghl.service.ts',
        });
        throw new Error(
          `reqId ${reqId} | No credential found for locationId ${locationId}`,
        );
      }

      const tokens = {
        access_token: creds.creds.accessToken,
        refresh_token: creds.creds.refreshToken,
      };

      const response = await this.ghlApiService.searchConversations(
        tokens,
        locationId,
        params,
      );

      return response.conversations;
    } catch (error) {
      this.myLogger.error({
        message: `@error | reqId ${reqId} | searchConversations: ${error.message}`,
      });
      throw error;
    }
  }

  async getMessagesByConversationId(
    locationId: string,
    conversationId: string,
    params: { lastMessageId?: string; limit?: number },
  ) {
    try {
      const creds = await this.mongoCredentialService.getCredential({
        keyId: locationId,
        kind: KINDS.GHL_CREDENTIAL,
      });

      if (!creds) {
        throw new Error(`No credential found for locationId ${locationId}`);
      }

      const tokens = {
        access_token: creds.creds.accessToken,
        refresh_token: creds.creds.refreshToken,
      };

      const response = await this.ghlApiService.getMessagesByConversationId(
        tokens,
        conversationId,
        params,
      );

      return response;
    } catch (error) {
      throw error;
    }
  }

  async getConversations({
    contactId,
    locationId,
    contactData,
    locationData,
    url = '',
    capriToken,
  }) {
    try {
      const token = await this.tokenService.getTokenByToken({
        token: capriToken,
      });
      if (!token?.token)
        throw new UnauthorizedException('Invalid token passed');
      const orgId = token?.orgId;
      const creds = await this.mongoCredentialService.getCredential({
        keyId: locationId,
        kind: KINDS.GHL_CREDENTIAL,
      });
      if (!creds) {
        throw new Error(`No credential found for locationId ${locationId}`);
      }

      const tokens = {
        access_token: creds.creds.accessToken,
        refresh_token: creds.creds.refreshToken,
      };

      const c = await this.ghlApiService.searchConversations(
        tokens,
        locationId,
        {
          contactId,
          locationId,
        },
      );
      const conversations = c.conversations;
      if (conversations?.[0]) {
        const conversationId = conversations?.[0]?.id;
        const m = await this.ghlApiService.getMessagesByConversationId(
          tokens,
          conversationId,
          {},
        );
        let message = '';

        m?.messages?.messages.sort((a, b) => {
          return  new Date(a.dateAdded).getTime() - new Date(b.dateAdded).getTime()
        })
        
        
        for (const msg of m?.messages?.messages ?? []) {
          message +=
            (msg?.direction === 'inbound' ? 'User:' : 'Assistant:') +
            ' ' +
            msg?.body +
            '\n';
        }
        if (url) {
          const res = await this.apiClientService.apiRequest('POST', url, {
            contact: contactData,
            location: locationData,
            message: message,
            rawMessage: m?.messages ?? {},
          });
          this.myLogger.log({
            message: `Response from the request API: ${JSON.stringify(res)}`,
          });
        }

        return {
          conversationId,
          message,
          rawMessage: m?.messages ?? {},
        }
      } else {
        this.myLogger.log({
          message: `GET CONVERSATIONS ERROR | No conversations found for contactId: ${contactId} | locationId: ${locationId}`,
        });
        const res = await this.apiClientService.apiRequest('POST', url, {
          contact: contactData,
          location: locationData,
          error: 'No conversations found',
        });
        this.myLogger.log({
          message: `GET CONVERSATIONS ERROR | Response from the request API: ${JSON.stringify(
            res,
          )}`,
        });
      }
    } catch (error) {
      this.myLogger.error({
        message: `GET CONVERSATIONS ERROR | Contact Id ${contactId} | locationId ${locationId} | getConversations: ${error.message}`,
        context: 'Get conversation public endpoint',
      });
    }
  }
}
