import { BadRequestException, Injectable } from '@nestjs/common';
import { MongoCredentialsService } from 'src/mongo/service/credentials/mongo-credentials.service';
import { Response, query } from 'express';
import { HttpStatus } from '@nestjs/common/enums';
import { KINDS, PROVIDERS } from 'src/lib/constant';
import { RequestWithUser } from 'src/auth/auth.interface';
import { MyLogger } from 'src/logger/logger.service';
import { OrganizationService } from '../../organization.service';
import { ApiclientService } from 'src/api-client/services/apiclient.service';
import { GhlAPIs } from 'src/api-client/lib/ghlApi';
import { MongoConversationService } from 'src/mongo/service/conversations/mongo-conversations.service';
import { MongoAgentService } from 'src/mongo/service/agent/mongo-agent.service';
import { CreateConversationDto } from 'src/mongo/dto/conversations/create.dto';
import {
  Actions,
  AppointmentAction,
} from 'src/organization/controllers/channels/ghl/actions';
import { GhlApisService } from 'src/api-client/services/ghl-apis/ghl-apis.service';
import { SessionService } from 'src/session/session.service';
import { IGhlTrigger } from 'src/mongo/schemas/agents/agents.schema';
import { IRawbotResponse } from './botresponse';
import { UserService } from 'src/user/user.service';
import { TiktokenService } from 'src/vector/services/tiktoken.service';
import { IBotActionResponse } from 'src/interfaces/interfaces';
import { ObjectId } from 'mongodb';
import { MongoContextDataService } from 'src/mongo/service/contextData/contextData.service';
import { TaskService } from 'src/gcp/services/task.service';
import { CacheService } from 'src/utility/services/cache.service';
import { SchedulerService } from 'src/utility/services/scheduler.service';

@Injectable()
export class GhlConversationService {
  constructor(
    private readonly mongoConversationService: MongoConversationService,
    private readonly myLogger: MyLogger,
  ) {}

  async updateContactFollowupRecord({contactId, lastAttemptAt, followUpTaskId}:{
    lastAttemptAt: Date,
    contactId: string,
    followUpTaskId: string
  }){
    try {
      await this.mongoConversationService.updateConversation({
        query: { contactId },
        updateBody: {
          '$set': {
            'followUp.lastAttemptAt': new Date(),
            'followUp.followUpTaskId': followUpTaskId
          }
        }
      });
    } catch (error) {
      this.myLogger.error({
        message: `Error while updating contact followup record | message - ${error?.message}`,
        context: 'UPDATE CONTACT FOLLOWUP RECORD'
      });
    }
  }
}
