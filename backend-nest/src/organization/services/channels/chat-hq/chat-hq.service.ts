import { BadRequestException, Injectable } from '@nestjs/common';
import { ChatHQAppClient } from '@chathq-oss/app-sdk-node';
import { MongoCredentialsService } from 'src/mongo/service/credentials/mongo-credentials.service';
import { OrganizationService } from 'src/organization/services/organization.service';
import { Response } from 'express';
import { HttpStatus } from '@nestjs/common/enums';
import { CONNECTION_TYPES, KINDS, PROVIDERS } from 'src/lib/constant';
import { ConnectChathqQueryDto } from 'src/organization/dto/requests/post.dto';
import { v4 as uuidv4 } from 'uuid';
import { RequestWithUser } from 'src/auth/auth.interface';
import { MyLogger } from 'src/logger/logger.service';

const CHATHQ_WEBHOOK_SCOPES = [
  'account.livechat.visitor_message',
  'account.livechat.agent_message',
];
@Injectable()
export class ChatHqService {
  client:any = {}/* new ChatHQAppClient({
    appClientId: process.env.CHATHQ_CLIENT_ID,
    appClientSecret: process.env.CHATHQ_CLIENT_SECRET,
  }); */

  constructor(
    private readonly mongoCredentialService: MongoCredentialsService,
    private readonly organizationService: OrganizationService,
    private readonly myLogger: MyLogger
  ) {}

  async getAccessToken(userSsoToken: string) {
    const { accessToken, accountId } = await this.client.generateAccessToken(
      userSsoToken,
    );
    return { accessToken, accountId };
  }

  async createWebhooks(accessToken, accountId, url){
    const scopes = CHATHQ_WEBHOOK_SCOPES;
    const createChatHqwebhook = async (accessToken, accountId, scope, url) => {
      const webhook = await this.client.createWebhook(
        accessToken,
        accountId,
        scope,
        url,
      );
      return webhook;
    }
    const [visitorWebhook, agentWebhook] = await Promise.all([
      createChatHqwebhook(accessToken, accountId, scopes[0], url),
      createChatHqwebhook(accessToken, accountId, scopes[1], url),
    ]);
    return {
      vistorWebhookId: visitorWebhook.id,
      agentWebhookId: agentWebhook.id,
    }
  }

  async deleteWebhook(accessToken: string, accountId: string, webhookId: string[]){
    for (const id of webhookId) {
      await this.client.deleteWebhook(accessToken, accountId, id);
    }
    return;
  }

  async sendMessage(accessToken: string, accountId: string, roomId: string, message: string){
    await this.client.sendMessage(accessToken, accountId, {
      roomId,
      message,
    });
  }
  // ----------------- controller service methods -----------------

    async connectChatHq(req: RequestWithUser, query: ConnectChathqQueryDto, res: Response) {
        const { orgId, username, uniqueCode, userId } = req;
        const { accessToken, accountId } = await this.getAccessToken(
            query.ssoToken,
        );
        const { ssoToken } = query;
        const underDiffCred = await this.mongoCredentialService.ifExists({
            kind: KINDS.CHATHQ_CREDENTIAL,
            keyId: accountId,
            organizationId: { $ne: orgId },
        });
        if (underDiffCred) {
            throw new Error(
            'Connection failed ! The account is already connected to another organization.',
            );
        }

        let credentials;
        let existingCred = await this.mongoCredentialService.ifExists({
            kind: KINDS.CHATHQ_CREDENTIAL,
            keyId: accountId,
            organizationId: orgId,
        });

        if (existingCred) {
            credentials = {
            id: existingCred._id.toString(),
            };
            await this.mongoCredentialService.updateCredentials({
            query: {
                _id: credentials.id,
            },
            updateBody: {
                creds: {
                accessToken,
                accountId,
                },
            },
            });
        } else {
            credentials = await this.mongoCredentialService.createCredentials(
            {
                kind: 'ChathqCredential',
                keyId: accountId,
                creds: {
                accessToken,
                accountId,
                },
                userId,
                ssoToken,
                organizationId: orgId,
            },
            );
        }
        const connData = {
            credentialId: credentials.id,
            name: username,
            providerName: PROVIDERS.CHATHQ_CHANNEL,
            accountId: uuidv4(),
            keyId: accountId,
        };
        await this.organizationService.updateOrganisation(
            { _id: orgId },
            { $push: { 'connections.channels': connData } },
        );

        const widgetList = await this.getWidgetList(accountId, accessToken);
        const widgets = widgetList.items || [];

        res.render(
            'chathq',
            {
            message: 'Connection successful',
            title: 'success',
            success: true,
            widgets,
            orgId,
            accountId,
            accessToken,
            username,
            accId: connData.accountId,
            widgetsStr: JSON.stringify({ widgets }),
            },
            (err, html) => {
            if (err) {
                this.myLogger.error({
                message: `${err.message}`,
                });
                return res
                .status(HttpStatus.INTERNAL_SERVER_ERROR)
                .json({ message: 'Error in rendering page' });
            }
            return res.status(HttpStatus.OK).send(html);
            },
        );
    }

    async getWidgetList(accountId: string, accessToken: string) {
        const paginatedResult = await this.client.listEngagementWidgets(accessToken, {
            accountId,
        });
        return paginatedResult;
    }

    async setWidget(orgId: string, accountId: string, widgetId: string, widgetName: string, author: string, accId: string, accessToken: string) {
        const organization = await this.organizationService.getOrganization({ 
          _id: orgId,
          'connections.channels.accountId': accountId,
        }, {
            'connections.channels.$': 1,
        });
        if (organization.connections.channels?.[0]) {
            // delete the channel
            await this.organizationService.updateOrganisation({ _id: orgId, 'connections.channels.accountId': accountId },
                { $pull: { 'connections.channels': { accountId: accountId } } });
            throw new BadRequestException('Chat HQ channel already exists');
        } else {
            await this.organizationService.updateOrganisation({ _id: orgId, 'connections.channels.accountId': accountId }, {
                $set: {
                    'connections.channels.$.channeluId': widgetId,
                    'connections.channels.$.name': widgetName,
                }
            });
            await this.createWebhooks(
              accessToken,
              accId,
              `${process.env.CHATHQ_WEBHOOK_URL}/webhook/chathq/${orgId}`,
            )
            return {
                author: author,
                providerName: PROVIDERS.CHATHQ_CHANNEL,
                accountId: accountId,
                name: widgetName,
            }
        }
    }

}
