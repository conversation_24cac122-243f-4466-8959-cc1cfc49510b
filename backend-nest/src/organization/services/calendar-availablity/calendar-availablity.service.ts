import {
  HttpException,
  HttpStatus,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { MongoOrganizationService } from '../../../mongo/service/organization/mongo-organization.service';
import { OrgAvailability } from '../../../mongo/schemas/organization/org-availablity/org-availablity.schema';


@Injectable()
export class calendarAvailabilityService {
  constructor(
    private readonly mongoOrganizationService: MongoOrganizationService,
  ) {}

  async findOrgAvailability({
    orgId,
    accountId,
  }: {
    orgId: string;
    accountId?: string;
  }) {
    const orgAvailabilityGet =
      await this.mongoOrganizationService.getOrganization({
        _id: orgId,
      });

    if (!orgAvailabilityGet) {
      throw new NotFoundException('Organization not found');
    }

    if (accountId) {
      return orgAvailabilityGet.availability.filter(
        (availability) => availability.accountId === accountId,
      );
    }
  }

  async create(
    orgId: string,
    orgAvailability: OrgAvailability,
  ): Promise<OrgAvailability> {
    try {
      const orgAvailabilityGet =
        await this.mongoOrganizationService.getOrganization({ _id: orgId });
      if (!orgAvailabilityGet) {
        throw new HttpException('Organization not found', HttpStatus.NOT_FOUND);
      }

      const updatedAvailability =
        await this.mongoOrganizationService.updateOrganization(
          { _id: orgId },
          { $push: { availability: orgAvailability } },
          { new: true },
        );

      return updatedAvailability.availability.find(
        (availability) => availability.accountId === orgAvailability.accountId,
      );
    } catch (error) {
      
    }
  }
  async update(
    orgId: string,
    accountId: string,
    orgAvailability: OrgAvailability,
  ): Promise<OrgAvailability> {
    try {
      const orgAvailabilityGet =
        await this.mongoOrganizationService.getOrganization({ _id: orgId });
      if (!orgAvailabilityGet) {
        throw new HttpException('Organization not found', HttpStatus.NOT_FOUND);
      }

      const existingAvailabilities = orgAvailabilityGet.availability || [];
      const existingAvailabilityIndex = existingAvailabilities.findIndex(
        (availability) => availability.accountId === accountId,
      );

      if (existingAvailabilityIndex !== -1) {
        // Update the existing availability for the given calendarId
        const updatedAvailabilities = [...existingAvailabilities];
        updatedAvailabilities[existingAvailabilityIndex] = {
          ...existingAvailabilities[existingAvailabilityIndex],
          ...orgAvailability,
        };

        const updatedAvailability =
          await this.mongoOrganizationService.updateOrganization(
            { _id: orgId },
            { $set: { availability: updatedAvailabilities } },
            { new: true },
          );
        return updatedAvailability.availability.find(
          (availability) => availability.accountId === accountId,
        );
      }
    } catch (error) {
      
    }
  }

  async delete(orgId: string, accountId: string): Promise<void> {
    try {
      const orgAvailabilityGet =
        await this.mongoOrganizationService.getOrganization({
          _id: orgId,
        });
      if (!orgAvailabilityGet) {
        throw new HttpException('Organization not found', HttpStatus.NOT_FOUND);
      }
      const existingAvailabilities = orgAvailabilityGet.availability || [];
      const updatedAvailabilities = existingAvailabilities.filter(
        (availability) => availability.accountId !== accountId,
      );
      await this.mongoOrganizationService.updateOrganization(
        { _id: orgId },
        { $set: { availability: updatedAvailabilities } },
        { new: true },
      );
    } catch (error) {
      
    }
  }
}
