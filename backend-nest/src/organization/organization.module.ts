import { HttpModule } from '@nestjs/axios';
import { Module, forwardRef } from '@nestjs/common';
import { AgentModule } from 'src/agent/agent.module';
import { BillingModule } from 'src/billing/billing.module';
import { BillingService } from 'src/billing/billing.service';
import { MigrationModule } from 'src/migration/migration.module';
import { SessionModule } from 'src/session/session.module';
import { TokensModule } from 'src/tokens/tokens.module';
import { UserService } from 'src/user/user.service';
import { AiProviderController } from './controllers/aiProvider/aiProvider.controller';
import { calendarAvailabilityController } from './controllers/calendar-availablity/org-availablity.controller';
import { ChannelsController } from './controllers/channels/channels.controller';
import { ChatHqController } from './controllers/channels/chat-hq/chat-hq.controller';
import { GhlController } from './controllers/channels/ghl/ghl.controller';
import { PodiumController } from './controllers/channels/podium/podium.controller';
import { QnAController } from './controllers/data/QnA/QnA.controller';
import { DataController } from './controllers/data/data.controller';
import { GoogleDocsController } from './controllers/data/google-docs/google-docs.controller';
import { PdfController } from './controllers/data/pdf/pdf.controller';
import { SiteController } from './controllers/data/site/site.controller';
import { GhlDataController } from './controllers/ghl_data.controller';
import { AcuityController } from './controllers/integrations/acutiy/acuity.controller';
import { CalendlyController } from './controllers/integrations/calendly/calendly.controller';
import { GhlCalendarController } from './controllers/integrations/ghl-calendar/ghl-calendar.controller';
import { GoogleCalendarController } from './controllers/integrations/google-calendar/google-calendar.controller';
import { GoogleSheetController } from './controllers/integrations/google-sheet/google-sheet.controller';
import { IntegrationsController } from './controllers/integrations/integrations.controller';
import { OrganizationController } from './controllers/organization.controller';
import { Publicv1Controller } from './controllers/public/v1.controller';
import { AiProviderService } from './services/aiProviders/aiProvider.service';
import { calendarAvailabilityService } from './services/calendar-availablity/calendar-availablity.service';
import { ChannelsService } from './services/channels/channels.service';
import { ChatHqService } from './services/channels/chat-hq/chat-hq.service';
import { GhlService } from './services/channels/ghl/ghl.service';
import { PodiumService } from './services/channels/podium/podium.service';
import { QnAService } from './services/data/QnA/QnA.service';
import { DataService } from './services/data/data.service';
import { GoogleDocsService } from './services/data/google-docs/goole-docs.service';
import { PdfService } from './services/data/pdf/pdf.service';
import { SiteService } from './services/data/site/site.service';
import { AcuityService } from './services/integrations/acuity/acuity.service';
import { CalendlyService } from './services/integrations/calendly/calendly.service';
import { GhlCalendarService } from './services/integrations/ghl-calendar/ghl-calendar.service';
import { GoogleCalendarService } from './services/integrations/google-calendar/google-calendar.service';
import { IntegrationService } from './services/integrations/integration.service';
import { SheetService } from './services/integrations/sheet/sheet.service';
import { OrganizationService } from './services/organization.service';
import { Publicv1Service } from './services/public/v1.service';
import { ActionsService as WebhookActionsService } from './webhooks/actions.service';
import { WebhookChannelService } from './webhooks/webhookChannel.service';
import { WebhooksService } from './webhooks/webhooks.service';
import { FoldersModule } from 'src/folders/folders.module';
import { LoggerModule } from '../logger/logger.module';
import { V1ConversationsController } from './controllers/conversations/v1-conversations.controller';
import { V1ConversationsService } from './services/conversations/v1-conversations.service';
import { HubspotController } from './controllers/channels/hubspot/hubspot.controller';
import { HubspotService } from './services/channels/hubspot/hubspot.service';
import { WebhookController } from './webhooks/webhook/webhook.controller';
import { UtilityModule } from 'src/utility/utility.module';
import { SlackService } from './services/channels/slack/slack.service';
import { ghlWebhookHelperService } from './services/channels/ghl/ghl-webhook-helpers.service';
import { HttpRequestModule } from 'src/http-request/http-request.module';
import { HttpGetController } from './controllers/integrations/http-get/http-get.controller';
import { HttpGetService } from './services/integrations/http-get/http-get.service';
import { HttpPostController } from './controllers/integrations/http-post/http-post.controller';
import { HttpPostService } from './services/integrations/http-post/http-post.service';
import { GhlHelperService } from './services/channels/ghl/ghl-helpers.service';
import { VoiceUsageService } from './services/usage/voice-usage/voice-usage.service';
import { MongoModule } from 'src/mongo/mongo.module';
import { ExternalsModule } from 'src/externals/externals.module';
import { ActionsService } from './services/actions/actions.service';
import { NotificationService } from 'src/notification/notification.service';
import { NotificationModule } from 'src/notification/notification.module';
import { GhlConversationService } from './services/channels/ghl/ghl-webhook-conversation.service';

@Module({
  imports: [
    HttpModule,
    forwardRef(() => MigrationModule),
    AgentModule,
    SessionModule,
    BillingModule,
    TokensModule,
    LoggerModule,
    FoldersModule,
    UtilityModule,
    HttpRequestModule,
    MongoModule,
    NotificationModule,
    forwardRef(() => ExternalsModule),
  ],
  controllers: [
    OrganizationController,
    ChannelsController,
    IntegrationsController,
    AiProviderController,
    ChatHqController,
    GoogleCalendarController,
    GhlCalendarController,
    PodiumController,
    DataController,
    SiteController,
    GoogleSheetController,
    GhlController,
    calendarAvailabilityController,
    GoogleCalendarController,
    CalendlyController,
    AcuityController,
    Publicv1Controller,
    GoogleDocsController,
    QnAController,
    GhlDataController,
    PdfController,
    WebhookController,
    V1ConversationsController,
    HubspotController,
    HttpGetController,
    HttpPostController,
  ],
  providers: [
    OrganizationService,
    ChannelsService,
    IntegrationService,
    AiProviderService,
    GoogleCalendarService,
    GhlCalendarService,
    PodiumService,
    ChatHqService,
    DataService,
    SiteService,
    SheetService,
    UserService,
    GhlService,
    ghlWebhookHelperService,
    GhlConversationService,
    BillingService,
    WebhooksService,
    WebhookChannelService,
    ActionsService,
    calendarAvailabilityService,
    GoogleCalendarService,
    CalendlyService,
    AcuityService,
    Publicv1Service,
    GoogleDocsService,
    QnAService,
    PdfService,
    V1ConversationsService,
    HubspotService,
    GhlHelperService,
    HttpGetService,
    HttpPostService,
    SlackService,
    VoiceUsageService,
    WebhookActionsService,
    NotificationService
  ],
  exports: [
    SheetService,
    IntegrationService,
    AiProviderService,
    OrganizationService,
    SiteService,
    GhlService,
    VoiceUsageService,
    WebhooksService,
    ChannelsService,
    GoogleCalendarService,
    V1ConversationsService
  ],
})
export class OrganizationModule {}
