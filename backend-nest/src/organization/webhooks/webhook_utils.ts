import { HydratedContextData } from "src/mongo/schemas/contextData/contextData.schema";
import { IChatHQPayload, IWebhookPayload, PROVIDERS, IPodiumPayload } from "./dto/payload.dto";

function structureWebhookPayload(
  payload: any,
  provider: string,
  eventType
): IWebhookPayload {
  let direction;
  if (eventType === 'message.received') {
    direction = 'inbound'
  } else if (eventType === 'message.sent'){
    direction = 'outbound'
  }
  switch (provider) {
    case PROVIDERS.PODIUM:
      return {
        resourceId: payload?.data?.location?.uid,
        channelOrgId: payload?.data?.location?.organizationUid,
        body: payload['data']['body'],
        contactId: payload['data']['contact']?.uid,
        messageType: payload['data']['conversation']?.channel?.type,
        conversationId: payload['data']['conversation']?.uid,
        contactPhone: payload['data']['conversation']?.channel?.identifier,
        conversationType: 'PodiumConversation',
        provider: 'podium',
        direction
      };
    case PROVIDERS.CHATHQ:
      return {
        resourceId: payload?.data?.channel?.id,
        body: payload['data']['message'],
        contactId: payload['data']['contact']?.id,
        messageType: payload['data']['type'],
        conversationId: payload['data']['channel']?.id,
        conversationType: 'ChathqConversation',
        provider: 'chathq',
        direction,
      };
    case PROVIDERS.HUBSPOT:
      return {
        resourceId: payload?.portalId?.toString(),
        appId: payload?.appId?.toString(),
        subscriptionId: payload?.subscriptionId?.toString(),
        body: payload?.message,
        contactId: payload?.thread?.associatedContactId,
        senderActorId: payload?.thread?.assignedTo,
        inboxId: payload?.thread?.inboxId,
        conversationId: payload?.objectId?.toString(),// thread ID
        messageId: payload?.messageId,
        messageType: payload?.channelName,
        channelId: payload?.channelId,
        channelAccountId: payload?.channelAccountId,
        conversationType: 'HubspotConversation',
        provider: 'hubspot',
        replyTo: payload?.inReplyToId,
        direction: payload?.direction === 'INCOMING' ? 'inbound' : 'outbound',
      }
    default:
      throw new Error('Invalid webhook type');
  }
}

function getPayloadType(provider: string, payload: any) {
  if (provider === PROVIDERS.PODIUM) return payload as IPodiumPayload;
  if (provider === PROVIDERS.CHATHQ) return payload as IChatHQPayload;
  throw new Error('Invalid webhook payload');
}

const createExternalAppContextPrompt = (data?: HydratedContextData) => {
  let contextPrompt = '';
  for (const d of (data?.data ?? [])) {
    if (d.source === 'uphex') {
      contextPrompt += `This contacts conversation has started with the following context: \
This lead was interested in or opted in to an ad titled "${d.adTitle}" \
the body/copy of the ad is as follows "${d?.adMessage}". \
Use this information as additional context as you continue the conversation.\n\n`;
    }
  }
  return contextPrompt;
}

export {
  structureWebhookPayload,
  getPayloadType,
  createExternalAppContextPrompt
};