import { Injectable } from '@nestjs/common';
import { PodiumAPIService } from 'src/api-client/services/podium/podium-api.service';
import { GhlApisService } from 'src/api-client/services/ghl-apis/ghl-apis.service';
import { MongoCredentialsService } from 'src/mongo/service/credentials/mongo-credentials.service';
import { KINDS, PROVIDERS } from 'src/lib/constant';
import { IContactDetailsDto } from './dto/resources.dto';
import { IBotActionResponse } from 'src/interfaces/interfaces';
import { MyLogger } from 'src/logger/logger.service';
import { HubspotAPIService } from 'src/api-client/services/hubspot/hubspot-api.service';
import { IHubspotPayload, IWebhookPayload } from './dto/payload.dto';
import { getHubspotDeliveryIdentifierType } from './utils/structure.util';

@Injectable()
export class ActionsService {
    constructor(
        private readonly podiumApiService: PodiumAPIService,
        private readonly hubspotApiService: HubspotAPIService,
        private readonly ghlApiService: GhlApisService,
        private readonly mongoCredentialService: MongoCredentialsService,
        private readonly myLogger: MyLogger
    ){}

    async performGhlCalenderWrite(reqId: string, actions:IBotActionResponse, contact): Promise<any> {
        try {
            let botErrors = [];
            let calendarAccToken: any = await this.mongoCredentialService.getCredential({
                      keyId: actions.locationId,
                      kind: KINDS.GHL_CREDENTIAL,
                  });

                  calendarAccToken = {
                    access_token: calendarAccToken?.creds?.accessToken,
                    refresh_token: calendarAccToken?.creds?.refreshToken,
                  };
                if (!calendarAccToken?.access_token)
                  botErrors.push(
                    `Your leadconnector account with locationId: ${actions.locationId} couldn't be found in our database`,
                  );
                if (actions.kind === PROVIDERS.GHL_CALENDAR) {
                  let { startDate = '', endDate = '' } = actions.eventData;
                  if (!(startDate == '' || startDate.toUpperCase().includes('TBD'))) {
                    var actionContactId: string;
                      const contactData = {
                        locationId: actions.locationId,
                      };
                      if (contact?.contactName)
                        contactData['name'] = contact.contactName;
                      if (contact?.contactEmail) contactData['email'] = contact.contactEmail;
                      if (contact?.contactPhone) contactData['phone'] = contact.contactPhone;
                      let actionContact=
                        await this.ghlApiService.createContact(
                          calendarAccToken,
                          actions.locationId,
                          contactData,
                          null,
                        );
                      actionContactId = actionContact?.id;
                    }
                    if (endDate == '' || endDate.toUpperCase().includes('TBD'))
                      endDate = undefined;
                    const result =
                      await this.ghlApiService.makeGhlCalendarAppointment(
                        calendarAccToken,
                        actions.locationId,
                        actions.calendarId,
                        actionContactId,
                        startDate,
                        endDate,
                      );

                     await this.myLogger.log({
                       message: `GHL Calendar write action result: ${JSON.stringify(
                         result,
                       )}`,
                       context: actionContactId,
                     });
                    if (result?.error) {
                      botErrors.push(result?.error);
                    }
                    return botErrors;
                  } else {
                    this.myLogger.log({
                      message: `reqId ${reqId} | Calendar date is TBD , not creating appointment`,
                    });
                  }
        } catch (err) {
          this.myLogger.error({
            message: `ReqId ${reqId} | error in action service - ghl calendar write | message` + err.message
          });
        }
    }

    async addTags({provider, channelCreds, tags, conversationUid=undefined, organizationUid}){
      if (provider === PROVIDERS.PODIUM) {
        for(const tag of tags){
          const newTag = await this.podiumApiService.createContactTag(channelCreds, tag, organizationUid );
          await this.podiumApiService.addContactTag(channelCreds, conversationUid, newTag.data.uid, organizationUid );
          await new Promise(resolve => setTimeout(resolve, 1500));
        }
      }
    }



    async sendMessage(provider:string, contact:IContactDetailsDto, message:string, channelCreds, organizationUid?: string, payload?) {
        try {
            if (provider === PROVIDERS.PODIUM){
                const channel = contact?.contactEmails?.length>0 ? 'email' : 'phone'
                await this.podiumApiService.sendMessage(
                  channelCreds,
                  channel,
                  contact.contactPhones[0] ?? contact.contactEmails?.[0],
                  message,
                  payload.resourceId,
                  organizationUid,
                );
            } else if (provider === PROVIDERS.HUBSPOT) {
                const hubspotPayload = payload as IHubspotPayload;
                await this.hubspotApiService.sendHubspotMessage(channelCreds, payload.conversationId, hubspotPayload.channelId, hubspotPayload.channelAccountId,
                  message,
                  [{
                    "deliveryIdentifiers": [
                      {
                        "type": getHubspotDeliveryIdentifierType(hubspotPayload.messageType),
                        "value": hubspotPayload.channelAccountId
                      }
                    ],
                    "deliveryIdentifier": {}
                  }],
                  hubspotPayload.senderActorId,
                  undefined,
                  hubspotPayload.resourceId
                )
            }
        } catch (err) {
          this.myLogger.error({
            message: `error in sending message for ${provider} provider | contactId ${contact.contactId} | resourceId ${contact.resourceId}`
          });
          return {
            error: err.message
          }
        }
    }
}
