export const webhookEventMaps = {
    'podium': {
        'message.sent': 'message.sent',
        'contact.deleted': 'contact.delete',
        'message.received':'message.received'
    },
    'hubspot': {
        'message.sent': 'message.sent',
        'message.received': 'message.received',
        'contact.deletion': 'contact.delete',
    }
}

// export const functionalWebhookMapping = (provider: string, eventType: string, payload) => {
//     switch (provider) {
//         case 'hubspot':
//             const e = {
//                 'contact.deletion': 'contact.delete',
//                 "conversation.newMessage": payload
//             }[eventType]
//             break;
    
//         default:
//             break;
//     }
// }