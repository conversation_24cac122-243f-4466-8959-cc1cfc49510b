export interface IStructured {
    resourceId: string; // resourceId is the primary reference for all the conversations and credentials
    channelOrgId?: string;
    body: string;
    contactId: string;
    contactName?: string;
    conversationId?: string;
    conversationIdentifier?: string;// eg- conversation name
    channelType: string; // sms, email, chat, etc
    error?: string;
    conversationType?: 'GhlConversation' | 'PodiumConversation'; // 
}

export const structureWebhookPayload = (payload: any, type: string): IStructured => {
    switch (type) {
        case 'podium':
            return {
                resourceId: payload?.data?.location?.uid,
                body: payload['data']['body'],
                contactId: payload['data']['contact']?.uid,
                channelType: payload['data']['conversation']?.channel?.type,
                conversationId: payload['data']['conversation']?.uid,
                conversationIdentifier: payload['data']['conversation']?.channel?.identifier,
                conversationType: 'PodiumConversation',
            }
        default:
            throw new Error('Invalid webhook type');
    }
}


export const getHubspotDeliveryIdentifierType = (messageType: string) => {
    switch (messageType) {
        case 'EMAIL':
            return '"HS_EMAIL_ADDRESS"';
        case 'LIVE_CHAT':
            return 'CHANNEL_SPECIFIC_OPAQUE_ID'
        default:
            return 'SMS';
    }
}