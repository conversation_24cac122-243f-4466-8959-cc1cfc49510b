export interface IContactDetailsDto {
    contactId: string;
    contactName?: string;
    contactEmails?: string[];
    contactPhones?: string[];
    contactTimezone?: Date | string;
    contactTags?: {
        tagId: string;
        tagName: string;
    }[];
    resourceId?: string;
    channelOrgId?: string;
    conversationId?: string;
    metadata?:any;
    contactFields?: string[];
}

export interface IResourceDetailsDto {
    resourceId: string;
    channelOrgId?: string;
    resourceName?: string;
    resourceEmail?: string;
    resourcePhone?: string;
}