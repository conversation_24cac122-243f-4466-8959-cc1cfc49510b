export enum PROVIDERS {
  CHATHQ = 'chathq',
  PODIUM = 'podium',
  HUBSPOT = 'hubspot',
}

interface IWebhook {
  webhookId?: string;
  webhookToken?: string;
}

interface IProviderApp {
  appId?: string;
  subscriptionId?: string;
}

interface IContactDetails {
  contactId: string;
  contactName?: string;
  contactPhone?: string;
  contactEmail?: string;
}

interface IMessage {
  body: string;
  subject?: string;
  messageId?: string;
  messageType: string;
  direction?: string; //'inbound' | 'outbound';
  replyTo?: string;
}

interface IConversation {
  conversationId?: string;
  conversationType?:
    | 'GhlConversation'
    | 'PodiumConversation'
    | 'ChathqConversation'
    | 'HubspotConversation';
}

interface ISenderDetails {
  senderIdentifier?: string;
  senderName?: string;
  senderId?: string;
}

interface IStructuredPayload
  extends IWebhook,
    IContactDetails,
    IMessage,
    IConversation,
    IProviderApp,
    ISenderDetails {
  resourceId: string;
  channelOrgId?: string;
}

export interface IPodiumPayload extends IStructuredPayload {
  provider: 'podium';
}

export interface IChatHQPayload extends IStructuredPayload {
    provider: 'chathq';
}

export interface IHubspotPayload extends IStructuredPayload {
  provider: 'hubspot';
  channelId: string;
  senderActorId: string;
  inboxId: string;
  channelAccountId: string;
}

export type IWebhookPayload = IPodiumPayload | IChatHQPayload | IHubspotPayload;