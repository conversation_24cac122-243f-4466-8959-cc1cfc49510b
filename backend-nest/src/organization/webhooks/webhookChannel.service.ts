import { Injectable } from '@nestjs/common';
import { KINDS, PROVIDERS } from 'src/lib/constant';
import { IWebhookPayload } from './dto/payload.dto';
import { PodiumAPIService } from 'src/api-client/services/podium/podium-api.service';
import { IContactDetailsDto, IResourceDetailsDto } from './dto/resources.dto';
import { MongoCredentialsService } from 'src/mongo/service/credentials/mongo-credentials.service';
import { MyLogger } from 'src/logger/logger.service';
import { HubspotAPIService } from 'src/api-client/services/hubspot/hubspot-api.service';

@Injectable()
export class WebhookChannelService {
    constructor(
        private readonly podiumApiservice: PodiumAPIService,
        private readonly logger: MyLogger,
        private readonly hubspotApiService: HubspotAPIService,
        private readonly mongoCredentialService: MongoCredentialsService,
    ){}

    async populatePayload(provider: string, payload){
        try {
            let oldPayload = payload
            if (provider === PROVIDERS.HUBSPOT) {
                payload = payload?.filter(p => p?.messageType === "MESSAGE")?.[0];
                if (['contact.deletion', 'conversation.newMessage'].includes(payload?.subscriptionType ?? "") ) {
                    const credentialExists = await this.mongoCredentialService.getCredential(
                        {
                          kind: KINDS.HUBSPOT_CREDENTIAL,
                          keyId: payload?.portalId?.toString(),
                          type: 'channel'
                        },
                    );
                    const tokens = credentialExists.creds;
                    let context: any={}, thread : any = {}, channelAccount: any = {};
                    if (payload?.messageId){
                        context = await this.hubspotApiService.getSingleMessage(tokens, payload.objectId, payload.messageId, payload.portalId?.toString());
                    }
                    if (payload?.subscriptionType === "conversation.newMessage" && payload?.objectId){
                        thread = await this.hubspotApiService.getAThread(tokens, payload.objectId, payload.portalId?.toString());
                    }
                    if (context?.channelAccountId){
                        channelAccount = await this.hubspotApiService.getASingleChannelAccount(tokens, context?.channelAccountId, payload.portalId?.toString());
                    }
                    if (context?.channelId) {
                        context['channelName'] = await this.hubspotApiService.getChannelName(tokens, context?.channelId, payload.portalId?.toString());
                    }
                    return {
                        ...payload,
                        ...context,
                        thread,
                        channelAccount
                    }
                }
            }
            return oldPayload;
        } catch (error) {
            this.logger.error({
                message: error?.message,
                context: 'WebhookChannelService.populatePayload',
            })
            return payload;
        }
    }
  
    
    async getInfos(provider: string, structuredPayload: IWebhookPayload, channelCreds): Promise<{
        contact: IContactDetailsDto;
        resource: IResourceDetailsDto;
        contactTags: {
            tagId: string;
            tagName: string;
        }[];
    }>{
        const { resourceId, contactId, conversationId, messageType, contactEmail, contactPhone } = structuredPayload;
        let contact, resource;
        if (provider === PROVIDERS.PODIUM) {
            contact = await this.podiumApiservice.getContact(undefined, channelCreds, conversationId, contactEmail, contactPhone,structuredPayload.channelOrgId);
            let fmtContact: IContactDetailsDto;
            let fmtResource: IResourceDetailsDto;
            if (contact?.data) {
                fmtContact = {
                    contactId: contact['data']?.uid,
                    contactName: contact['data'].name,
                    contactPhones: contact['data'].phoneNumbers,
                    contactEmails: contact['data'].emails,
                    contactTags: (contact['data'].tags ?? []).map(tag => {
                        return {
                            tagId: tag.uid,
                            tagName: tag.label,
                        }
                    }),
                    resourceId: contact['data'].locations.uid,
                    channelOrgId: contact['data'].organization.uid,// organizationId
                    conversationId: contact['data'].conversations.uid,
                }
            }
            resource = await this.podiumApiservice.getPodiumLocation(undefined, channelCreds, undefined, undefined, resourceId, structuredPayload.channelOrgId);
            if (resource?.data) {
                fmtResource = {
                    resourceId: resource['data'].uid,
                    channelOrgId: resource['data'].organizationUid,
                    resourceName: resource['data'].displayName,
                    resourceEmail: '',
                    resourcePhone: resource['data'].podiumPhoneNumber,
                }
            }
            return {
                contact: fmtContact,
                resource: fmtResource,
                contactTags: fmtContact?.contactTags,
            };
        }
    }
    async getChannelCredential(provider: string, resourceId: string){
        if (provider === PROVIDERS.PODIUM){
            let credential = await this.mongoCredentialService.getCredential({
                kind: KINDS.PODIUM_CREDENTIAL,
                keyId: resourceId
            });
            return {
                creds: credential?.creds,
                channelOrgId: credential?.podiumOrgId
            }
        } else if (provider === PROVIDERS.HUBSPOT) {
            let credential = await this.mongoCredentialService.getCredential(
                {
                  kind: KINDS.HUBSPOT_CREDENTIAL,
                  keyId: resourceId,
                  type: 'channel'
                },
            );
            return {
                creds: credential?.creds
            }
        }
        return null;
    }

    async getContactDetails(provider: string, structuredPayload: IWebhookPayload, channelCreds?: any){
        let contact: IContactDetailsDto, rawContact;
        if (provider === PROVIDERS.PODIUM){
            let podiumContact = await this.podiumApiservice.getContact(undefined, channelCreds, structuredPayload.conversationId, structuredPayload.contactEmail, structuredPayload.contactPhone, structuredPayload.channelOrgId);
            if (podiumContact){
                contact = {
                  contactId: podiumContact?.uid,
                  contactName: podiumContact?.name,
                  contactPhones: podiumContact?.phoneNumbers,
                  contactEmails: podiumContact?.emails,
                  contactTags: podiumContact?.tags?.map((t) => {
                    return {
                      tagId: t.uid,
                      tagName: t.label,
                    };
                  }),
                  resourceId: structuredPayload.resourceId,
                  channelOrgId: podiumContact?.organization?.uid, // organiaztion uid
                  conversationId: podiumContact?.conversations?.[0]?.uid,
                  contactFields: ['name'],
                };
                
                rawContact = podiumContact;
            }
        } else if (provider === PROVIDERS.HUBSPOT) {
            let hubspotContact = await this.hubspotApiService.getContact(channelCreds, structuredPayload.contactId, structuredPayload.resourceId);
            contact = {
                contactId: structuredPayload?.contactId,
                contactName: hubspotContact?.properties?.firstname ? hubspotContact?.properties?.firstname + ' ' + hubspotContact?.properties?.lastname : '',
                contactPhones: hubspotContact?.properties?.phone ? [hubspotContact?.properties?.phone] : [],
                contactEmails: hubspotContact?.properties?.email ? [hubspotContact?.properties?.email] : [],
                contactTags: typeof hubspotContact?.properties?.tags === 'string' ? hubspotContact?.properties?.tags.split(',') : (hubspotContact?.properties?.tags ?? []),
                resourceId: structuredPayload.resourceId,
                channelOrgId: structuredPayload.channelOrgId,// organiaztion uid
                conversationId: structuredPayload.conversationId,
                contactFields: Object.keys(hubspotContact?.properties || {}) ?? [],
            }
            if (hubspotContact?.properties?.mobilephone) {
                contact.contactPhones.push(hubspotContact?.properties?.mobilephone);
            } else if (hubspotContact?.properties?.hs_whatsapp_phone_number) {
                contact.contactPhones.push(hubspotContact?.properties?.hs_whatsapp_phone_number);
            }
            rawContact = hubspotContact;
        }
        return {contact, rawContact};
    }

    async getResourceLocationDetails(provider: string, structuredPayload: IWebhookPayload, channelCreds?: any){
        let resource: IResourceDetailsDto, rawResourceData;
        if (provider === PROVIDERS.PODIUM){
            let podiumResource = await this.podiumApiservice.getPodiumLocation(undefined, channelCreds, undefined,undefined, structuredPayload.resourceId, structuredPayload.channelOrgId);
            if (podiumResource){
                resource = {
                    resourceId: podiumResource?.uid,
                    channelOrgId: podiumResource?.organizationUid,
                    resourceName: podiumResource?.displayName,
                    resourceEmail: '',
                    resourcePhone: podiumResource?.podiumPhoneNumber,
                }
                rawResourceData = podiumResource;
            }
        } else if (provider === PROVIDERS.HUBSPOT) {
            resource = {
                resourceId: structuredPayload?.resourceId,
                channelOrgId: structuredPayload.channelOrgId,// organiaztion uid
                resourceName: '',
                resourceEmail: '',
                resourcePhone: '',
            }
            rawResourceData = resource;
        }
        return {resource, rawResourceData};
    }
}
