import {
  Controller,
  Get,
  Post,
  Req,
  Res,
  Body,
  HttpStatus,
  Render,
  Delete,
  Param,
  Query,
  UseGuards,
} from '@nestjs/common';
import { Request, Response } from 'express';
import {
  ApiOperation,
  ApiQuery,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiParam,
} from '@nestjs/swagger';
import { WebhooksService } from '../webhooks.service';
import { RequestWithUser } from 'src/auth/auth.interface';
import { handleException } from 'helpers/handleException';
import { structureWebhookPayload } from '../webhook_utils';
import { IWebhookParamsDto } from '../dto/params.dto';
import { WebhookChannelService } from '../webhookChannel.service';
import { MyLogger } from 'src/logger/logger.service';

@Controller('webhook')
export class WebhookController {
    constructor(
      private readonly webhookService: WebhooksService,
      private readonly webhookChannelService: WebhookChannelService,
      private readonly logger: MyLogger
    ) {}
  
  @Post(':provider/:orgId?')
  @ApiOperation({ summary: 'Process webhook' })
  async processWebhook(
    @Req() req: RequestWithUser,
    @Res() res: Response,
    @Param() params: IWebhookParamsDto,
    @Body() payload: any,
  ) {
    res.sendStatus(HttpStatus.OK);


    const { provider, orgId = undefined } = params;

    try {
      payload = await this.webhookChannelService.populatePayload(params.provider, payload);

      // Map each channel's event to the predetermined events list
      const eventType = this.webhookService.ifWebhookTypeSupported(provider, payload) || '';

      // restructure payload here for each new channel
      const structuredPayload = structureWebhookPayload(payload, provider, eventType);

      if (eventType === 'contact.delete')
        await this.webhookService.contactDeleteHandler(provider, payload);

      else if (['message.received', 'message.sent'].includes(eventType)) {

        this.logger.log({
          message: `webhook message | provider ${provider} | eventType ${eventType} | Payload: ${JSON.stringify(payload)}`,
          context: `WEBHOOK | ${provider} | ${orgId}`,
        })

        const isNotSupported = this.webhookService.isUnsupportedMessageType(
          structuredPayload.messageType,
          provider,
        );


        // add each new channel's support here
        if (isNotSupported)
          throw new Error(
            `${provider} does not support ${structuredPayload.messageType} messages`,
          );


        // add each new channel's support here)
        await this.webhookService.messageHandler(
          provider,
          structuredPayload,
          payload,
          orgId,
        );
      }

    } catch (error) {
      this.logger.error({
        message: `error | reqId ${req.uniqueCode} | provider ${provider} |error: ${error.message} | stack: ${error.stack}`,
        context: `WEBHOOK | ${provider} | ${orgId}`,
      }
      );
    }
  }
}
