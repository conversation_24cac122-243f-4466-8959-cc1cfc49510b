import { Injectable } from '@nestjs/common';
import { MongoConversationService } from 'src/mongo/service/conversations/mongo-conversations.service';
import { CreateConversationDto } from 'src/mongo/dto/conversations/create.dto';
import { OrganizationService } from '../services/organization.service';
import { MongoAgentService } from 'src/mongo/service/agent/mongo-agent.service';
import { MongoCredentialsService } from 'src/mongo/service/credentials/mongo-credentials.service';
import { SessionService } from 'src/session/session.service';
import { WebhookChannelService } from './webhookChannel.service';
import { ActionsService } from './actions.service';
import { createExternalAppContextPrompt, getPayloadType } from './webhook_utils';
import { IWebhookPayload } from './dto/payload.dto';
import { IRawbotResponse } from '../services/channels/ghl/botresponse';
import { KINDS, PROVIDERS } from 'src/lib/constant';
import { isEmail } from 'src/utils/validator';
import { IGhlTrigger } from 'src/agent/dto/add-trigger-dto';
import { IBotActionResponse } from 'src/interfaces/interfaces';
import { MyLogger } from 'src/logger/logger.service';
import { webhookEventMaps } from './utils/eventMaps';
import { MongoContextDataService } from 'src/mongo/service/contextData/contextData.service';
import { GhlApisService } from 'src/api-client/services/ghl-apis/ghl-apis.service';
import { HubspotAPIService } from 'src/api-client/services/hubspot/hubspot-api.service';
import { createActionElement } from 'src/lib/utils';

interface rolloverEventDto {
  reason: string;
  date: Date | string;
  rollover: boolean;
  contactId: string;
  responded?: boolean;
}

@Injectable()
export class WebhooksService {
  constructor(
    private readonly mongoConversationService: MongoConversationService,
    private readonly organizationService: OrganizationService,
    private readonly mongoAgentService: MongoAgentService,
    private readonly mongoCredentialService: MongoCredentialsService,
    private readonly sessionService: SessionService,
    private readonly webhookChannelService: WebhookChannelService,
    private readonly actionService: ActionsService,
    private readonly myLogger: MyLogger,
    private readonly mongoContextDataService: MongoContextDataService,
    private readonly hubspotApiService: HubspotAPIService,
    private readonly ghlApiService: GhlApisService,
  ) {}

  
  ifWebhookTypeSupported(
    provider: string,
    payload,
  ): 'message.received' | 'message.sent' | 'contact.delete' | void {
    let eventType: string;
    if (provider === 'podium') {
      eventType = payload['metadata']['eventType'];
    } else if (provider === 'hubspot') {
      if (payload['subscriptionType'] === 'conversation.newMessage'){
        if (payload.direction === 'OUTGOING')
          eventType = 'message.sent'
        else if (payload.direction === 'INCOMING') eventType = 'message.received';
      }
      else eventType =  payload['subscriptionType'];
    }

    
    if (webhookEventMaps[provider] && Object.keys(webhookEventMaps[provider]).includes(eventType)) {
      return webhookEventMaps?.[provider]?.[eventType];
    } else {
      this.myLogger.error({
        message:
          'No supported event for determining webhook event found' +
          ' ' +
          JSON.stringify({ provider, eventType }),
        context: 'webhookService.ifWebhookTypeSuppported',
      });
    }
  }

  async contactDeleteHandler(provider: string, payload) {
    try {
      let contactId;
      switch (provider) {
        case 'podium':
          contactId = payload['data']['before']['uid'];
          break;
        case 'hubspot':
          contactId = payload?.objectId?.toString()
        default:
          break;
      }
      if (contactId) {
        await this.mongoConversationService.deleteConversations({
          query: { contactId },
        });
        this.myLogger.log({
          message: `contactId ${contactId} | conversations deleted`,
          context: 'webhook service . contactDeleteHandler',
        });
      }
      return;
    } catch (err) {
      this.myLogger.log({
        message: `error in contact delete webhook : ` + err.message,
        context: 'webhook service . contactDeleteHandler',
      });
    }
  }

  isUnsupportedMessageType(messageType: string, provider: string): boolean {
    if (provider === PROVIDERS.PODIUM) {
      if (['email', 'fallback_email'].includes(messageType)) {
        return true;
      }
    }
    return false;
  }

  async messageHandler( provider: string, payload: IWebhookPayload, overrides, orgId?: string,) {
    let tokens: any;
    var rolloverEvent = {
      rollover: false,
      reason: '',
      date: new Date().toISOString(),
      contactId: payload.contactId,
      responded: false,
    };
    try {
      let { body, resourceId, contactId, direction, messageType } = payload;

      let aiProviderName = '';
      let channelName = provider;

      let isOutbound = direction === 'outbound';
      if (isOutbound) {
          await this.processOutbound({
            message: body,
            payload,
            direction,
            channelName,
            agentId: '',
            aiProviderName,
            messageType,
          });
          return;
      }

      let { creds } = await this.webhookChannelService.getChannelCredential(
        provider,
        resourceId,
      );
      
      if (!creds) throw new Error('channel creds not found');

      const {contact=undefined} = await this.webhookChannelService.getContactDetails(provider, payload, creds);



      const {resource=undefined } = await this.webhookChannelService.getResourceLocationDetails(provider, payload, creds);
      
      channelName = resource?.resourceName ?? channelName;

      const contactTags = contact?.contactTags;
      const contactTimezone = contact?.contactTimezone;
      const contactFields = contact?.contactFields;
      const botErrors = [];

      let conversationHistory = await this.getConversationString(
        resourceId,
        contactId,
        overrides?.history,
      );

      const org = await this.organizationService.getOrganization(
        {
          'connections.channels.keyId': resourceId,
        },
      );

      const connection = (org?.connections?.channels || []).find(
        (channel) => channel.keyId === resourceId,
      );

      if (!connection)
        throw new Error(
          `No channel with resourceId ${
            resourceId + (channelName ? ' | channel name : ' + channelName : '')
          } found for your organization.`,
        );
      const agentIds = org?.agents;
      const channelAccountId = connection?.accountId;
      const channelUserId = connection?.userId;
      
      

      const isDuplicate = await this.mongoConversationService.isAlreadySaved({ contactId: payload.contactId, 'conversation.body': body })
      if (!isDuplicate) {
        await this.saveConversations({
          message: body,
          resourceId: payload.resourceId,
          contactId: payload.contactId,
          direction,
          aiProviderName,
          agentId: '',
          channelName: resource?.resourceName || channelName,
          status: 'delivered',
          messageType,
          orgId: org._id.toString(),
        });
      }
     
      let agentsData = [];
      let filterAgentsError;
      let customPrompt;
      let tags = contactTags.map(t => t.tagName);
      [agentsData, filterAgentsError] = await this.filterAgents(
          org._id.toString(),
          agentIds,
          messageType,
          channelAccountId,
          tags,
          overrides
        );
        botErrors.push(filterAgentsError);

      if (!agentsData || agentsData.length === 0) {
        this.myLogger.error({
          message: ` No active agent found`,
          context: `webhookService.messageHandler: retrieving agent from trigger`,
        });
        return;
      }

      customPrompt = overrides?.prompt;

      let externalAppContext = await this.mongoContextDataService.getContext({
        orgId: org?._id?.toString(),
        externalContactId: payload?.contactId,
      });

      let externalAppContextPrompt = createExternalAppContextPrompt(externalAppContext);

      (async () => {
        for (const agent of agentsData ?? []) {
          try {
            let rawBotResponse: any = await this.sessionService.ghlWebhook({
              conversations: conversationHistory,
              query: body,
              agentId: agent._id.toString(),
              agentDetails: agent,
              organizationDetails: org,
              contactId,
              locationId: payload?.resourceId,
              prompt_id: overrides?.prompt_id,
              prompt: customPrompt,
              task: overrides?.task,
              knowledge: overrides?.knowledge,
              training: overrides?.training,
              contactDetails: {
                contactTimezone: contactTimezone,
                tags: contactTags,
                customFields: contactFields,
                customValues: [],
                externalAppContextPrompt,
              },
            });
            let botResponse: IRawbotResponse = rawBotResponse,
            tokensCount = rawBotResponse?.usage_tokens_total;
            
            let botResponseForRollover = botResponse?.rolloverDetails;

            if (botResponseForRollover?.rollover) {
              rolloverEvent.rollover = true;
              rolloverEvent.reason =
                botResponseForRollover?.rolloverReason || '';
            }
            let tagsToAdd = [],
              visibleActions = [],
              isSilent = false, currActionKind;

            let customFieldsToUpdate: {
              fieldKey: string;
              field_value: string;
            }[] = [];

            for (const actions of botResponse?.botResponseForEachDataSource ||
              []) {
              try {
                // create action object to save
                const actionElem = createActionElement(actions);
                visibleActions.push(actionElem);

                // process the actions
                if (actions?.action === 'read') continue;

                if ((actions?.errors || []).length > 0) {
                  // displayedErrors.push(actions?.errors ?? []);
                  const errorString = (actions.errors || []).join('\n');
                  botErrors.push(errorString);
                }

                let { calendarId, timestamp, accountId, eventId } = actions;
                let actionResourceId = actions?.locationId;
                currActionKind = actions?.kind;

                if (
                  !actionResourceId &&
                  currActionKind === 'ghlCalendar' &&
                  !(actions?.eventData?.startDate || '')
                    .toUpperCase()
                    .includes('TBD')
                )
                  this.myLogger.log({
                    message: `@webhook: | No resourceId from action found for action: ${actions.action} | contactId: ${contactId} | resourceId: ${resourceId}`,
                  });

                if (actions?.eventData?.tag?.name ?? actions?.eventData?.tag) {
                  tagsToAdd.push(
                    actions?.eventData?.tag?.name ?? actions?.eventData?.tag,
                  );
                }

                if (actions?.eventData?.customFieldData?.fieldKey) {
                  customFieldsToUpdate.push({
                    fieldKey:
                      actions?.eventData?.customFieldData.fieldKey.replace(
                        /\{|\}/g,
                        '',
                      ),
                    field_value:
                      actions?.eventData?.customFieldData.field_value,
                  });
                }
                let calendarAccToken: any = { ...tokens };
                let channelNotEqActionLocation = true;
                if (channelNotEqActionLocation && actionResourceId) {
                  calendarAccToken =
                    await this.mongoCredentialService.getCredential({
                      keyId: actionResourceId,
                      kind: KINDS.GHL_CREDENTIAL,
                    });

                  calendarAccToken = {
                    access_token: calendarAccToken?.creds?.accessToken,
                    refresh_token: calendarAccToken?.creds?.refreshToken,
                  };
                }
                if (!calendarAccToken?.access_token)
                  botErrors.push(
                    `Your leadconnector account with locationId: ${actionResourceId} couldn't be found in our database`,
                  );
                if (actions.kind === PROVIDERS.GHL_CALENDAR) {
                  let { startDate = '', endDate = '' } = actions.eventData;
                  if (!(startDate == '' || startDate.toUpperCase().includes('TBD'))) {
                    // let { contact: actionContact } = await this.ghlApiService.getContact(
                    //     calendarAccToken,
                    //     actionResourceId,
                    //     contactId,
                    // );
                    var actionContactId: string;
                    if (channelNotEqActionLocation) {
                      const contactData = {
                        locationId: actionResourceId,
                      };
                      if (contact?.contactName)
                        contactData['name'] = contact.contactName;
                      if (contact?.contactEmails?.length > 0) contactData['email'] = contact.contactEmails?.[0];
                      if (contact?.contactPhones?.length > 0) contactData['phone'] = contact.contactPhones?.[0];
                      let actionContact =
                        await this.ghlApiService.createContact(
                          calendarAccToken,
                          actionResourceId,
                          contactData,
                          contact.contactEmails?.[0] || '',
                        );
                      actionContactId = actionContact?.id;
                    } else {
                      actionContactId = contactId;
                    }
                    if (endDate == '' || endDate.toUpperCase().includes('TBD'))
                      endDate = undefined;
                    
                    const result =
                      await this.ghlApiService.makeGhlCalendarAppointment(
                        calendarAccToken,
                        actionResourceId,
                        calendarId,
                        actionContactId,
                        startDate,
                        endDate,
                      );

                      await this.myLogger.log({
                        message: `GHL Calendar write action result: ${JSON.stringify(
                          result,
                        )}`,
                        context: actionContactId,
                      });
                    if (result?.error) {
                      rolloverEvent.rollover = true;
                      rolloverEvent.reason = result?.error;
                      botErrors.push(result?.error);
                    }
                  } else {
                    this.myLogger.log({
                      message:  `Calendar date is TBD , not creating appointment`,
                    });
                  }
                }

                 if (actions?.eventData?.tag?.silent || actions?.silent) {
                   isSilent =
                     actions?.eventData?.tag?.silent || actions?.silent;
                 }
              } catch (err) {
                this.myLogger.error({
                  message:
                    `@error | webhook action type ${actions?.kind} | resource Id: ${resourceId}. Error: ` +
                    err.message,
                });
              }
            }

            await this.saveConversations({
              message: botResponse?.finalResponse || '',
              contactId: payload.contactId,
              resourceId: payload.resourceId,
              direction: 'outbound',
              channelName,
              agentId: agent._id.toString() || '',
              aiProviderName,
              status: 'pending',
              visibleActions,
              tokensCount,
              messageType,
            });
            if (tagsToAdd.length > 0)
              await this.actionService.addTags({provider, channelCreds: creds, tags: tagsToAdd, conversationUid: payload.conversationId, organizationUid: payload.resourceId})
            if (botResponse?.finalResponse) {
              // if (customFieldsToUpdate.length > 0) {
              //   // TODO await this.actionService.
              // }
              if (!isSilent) {
                const result = await this.actionService.sendMessage(provider, contact, botResponse?.finalResponse, creds, undefined, payload);
                if (result?.error) {
                  botErrors.push(result?.error);
                  rolloverEvent.rollover = true;
                  rolloverEvent.reason = result?.error;
                  rolloverEvent.date = new Date().toISOString();
                } else {
                  rolloverEvent.responded = true;
                }
              } else {
                this.myLogger.log({
                  message: `@webhook |  marked as silent by bot | contactId: ${contactId} | resourceId: ${resourceId}`,
                });
              }
            } else {
              this.myLogger.error({
                message: ` no bot response generated | provider ${provider} | resourceId ${resourceId} | contactId ${contactId}`,
              });
            }
          } catch (err) {
            this.myLogger.log({
              message:
                `@error in action for resource Id: ${resourceId}` + err.message,
                context: 'webhookService.messageHandler'
            });
            botErrors.push(
              err?.message ||
                `Performing action failed for resource Id: ${resourceId}, contactId: ${contactId}`,
            );
          }
        }
        if (rolloverEvent.rollover) {
          await this.handleRolloverEvent(rolloverEvent, payload.resourceId);
        }
      })();
    } catch (err) {
      // handle channel specific error if any
      this.myLogger.log({
        message: `@Error while processing webhook: ` + err.message,
        context: 'webhookService.messageHandler'
      });
      // await this.handleErrors(tokens, err?.message, payload);
    }
    try{
      if (rolloverEvent.rollover) {
        await this.handleRolloverEvent(rolloverEvent, payload.resourceId);
      }
    }catch(err){
      this.myLogger.error({
        message: err?.message,
        context: 'handle rollover event for channel webhooks'
      })
    }
  }

  async getConversationString(
    resourceId: string,
    contactId: string,
    history?: number,
  ) {
    try {
      let query = {
        contactId: contactId,
      };
      if (resourceId) {
        query['resourceId'] = resourceId;
      }
      const conversationDocs =
        await this.mongoConversationService.getConversations({
          query: query,
        });
      let date;
      if (history) {
        date = new Date();
        if (history < 1) date.setMinutes(date.getMinutes() - 1);
        else date.setHours(date.getHours() - history);
      }

      let conversationString = '';
      for (const conversationDoc of conversationDocs) {
        let prevDirection = '';
        for (const conversation of conversationDoc?.conversation || []) {
          if (conversation?.messageType === 'Email') continue;
          if (history) {
            if (conversation.dateAdded < date) continue;
          }
          if (conversation.direction === 'inbound') {
            if (prevDirection === 'inbound') conversationString += '\n';
            conversationString += `user: ` + conversation.body + '\n';
            prevDirection = 'inbound';
          } else if (conversation.direction === 'outbound') {
            conversationString += `assistant: ` + conversation.body + '\n\n';
            prevDirection = 'outbound';
          }
        }
        if (prevDirection === 'inbound') conversationString += '\n';
      }
      return conversationString;
    } catch (error) {
      this.myLogger.error({
        message:
          `error encountered while getting conversation string for resource ${resourceId} | contactId ${contactId} :` +
          error.message,
          context: 'Webhook service get conversation string'
      });
      return '';
    }
  }

  async saveConversations({
    message = '',
    direction = 'inbound',
    channelName,
    agentId,
    aiProviderName,
    errors = [],
    status = 'pending',
    visibleActions = [],
    saveToken = true,
    tokensCount = 0,
    messageType = '',
    orgId = '',
    resourceId,
    contactId,
  }) {
    try {
      if (message) {
        const existingConvoRecord =
          await this.mongoConversationService.isAlreadySaved({
            resourceId: resourceId,
            contactId: contactId,
          });
        // don't save tokens if outbound and saveToken is set to false
        if (!(saveToken && direction == 'outbound')) tokensCount = 0;
        if (existingConvoRecord) {
          const updateConversation = {
            $addToSet: {
              conversation: {
                body: message,
                dateAdded: new Date(),
                direction: direction,
                aiProvider: aiProviderName,
                tokens: tokensCount,
                actions: visibleActions,
                messageType,
                status,
              },
            },
          };
          updateConversation['$set'] = {
            channel: channelName,
          };
          if (agentId) updateConversation['$set']['agentId'] = agentId;
          if (orgId) updateConversation['$set']['orgId'] = orgId;
          if (tokensCount) {
            updateConversation['$inc'] = {
              totalTokens: tokensCount,
            };
          }
          await this.mongoConversationService.updateConversation({
            query: {
              _id: existingConvoRecord._id.toString(),
            },
            updateBody: updateConversation,
          });
          // save to pinecone
          this.myLogger.log({
            message: `@conversation updated: direction '${direction}' | resourceId '${resourceId}' | contactId '${contactId}'`,
            context: 'save channel webhook conversation'
          });
        } else {
          const createConversation: CreateConversationDto = {
            conversation: [
              {
                body: message,
                dateAdded: new Date(),
                direction: direction,
                tokens: tokensCount,
                aiProvider: aiProviderName,
                actions: visibleActions,
                messageType,
                status,
              },
            ],
            contactId: contactId,
            resourceId: resourceId,
            type: messageType,
            agentId,
            orgId,
            updatedAt: new Date(),
            totalTokens: tokensCount,
            channel: channelName,
          };

          await this.mongoConversationService.createConversation(
            createConversation,
          );
          this.myLogger.log({
            message: `@conversation saved: direction '${direction}' | resourceId '${resourceId}' | contactId '${contactId}'`,
            context: 'save channel webhook conversation'
          });
        }
      }
    } catch (error) {
      this.myLogger.log({
        message:
          `error encountered while saving conversation for resourceId ${resourceId} | contactId '${contactId} : ` +
          error.message,
          context: 'save channel webhook conversation'
      });
    }
  }

  // ----------------- Outbound -----------------
  async processOutbound({
    message = '',
    payload,
    direction,
    channelName = '',
    agentId = '',
    aiProviderName = '',
    saveToken = true,
    tokensCount = 0,
    messageType,
    orgId = '',
  }:{
    [key: string]: any;
    payload: IWebhookPayload
  }) {
    if (message === '') return;
    try {
      // already existing conversation entry
      const updated = await this.mongoConversationService.updateConversation({
        query: { contactId: payload.contactId, 'conversation.body': message },
        updateBody: {
          $set: {
            'conversation.$.status': 'delivered',
            direction: direction,
            rollover: false,
            rolloverReason: '',
          },
        },
      });
      if (updated.modifiedCount == 0) throw new Error('new outbound');
    } catch (err) {
      if (err.message === 'new outbound') {
        const existingContact =
          await this.mongoConversationService.isAlreadySaved({
            contactId: payload.contactId,
          });
        // let vectorTokens: any[] | Uint32Array = [];
        if (!saveToken && direction == 'outbound') tokensCount = 0;
        if (existingContact) {
          // new conversation entry for an existing contact
          await this.mongoConversationService.updateConversation({
            query: { contactId: payload.contactId },
            updateBody: {
              $push: {
                conversation: {
                  body: message,
                  dateAdded: new Date(),
                  status: 'delivered',
                  direction,
                  tokens: tokensCount,
                  aiProvider: aiProviderName,
                  actions: [],
                  messageType,
                },
              },
              $set: {
                rollover: false,
                rolloverReason: '',
              },
            },
          });
        } else {
          // create new contact
          await this.mongoConversationService.createConversation({
            conversation: [
              {
                body: payload.body,
                dateAdded: new Date(),
                direction: direction,
                tokens: tokensCount,
                aiProvider: aiProviderName,
                actions: [],
                messageType,
                status: 'delivered',
              },
            ],
            type: payload.messageType,
            contactId: payload.contactId,
            resourceId: payload.resourceId,
            agentId,
            orgId,
            totalTokens: tokensCount,
            updatedAt: new Date(),
            channel: channelName,
            rollover: true,
            rolloverReason: '',
          });
        }
      }
    }
  }

  async filterAgents(
    orgId: string,
    agentIds: string[],
    channel: string,
    accountId: string,
    tags: string[],
    rawPayload?: any
  ) {
    try {
      const agents = await this.mongoAgentService.getAgents({
        _id: { $in: agentIds },
        disabled: false,
        'triggers.data.subaccount': accountId,
        'triggers.data.channel': channel,
        'triggers.active': true,
      });

      const filteredAgents = agents.filter((agent) => {
        let { triggers } = agent;
        triggers = triggers.filter((trigger) => {
          let otherConditionsMet = true;
          if ((trigger.data as IGhlTrigger).sourceIdentifier !== undefined ) {
            if (trigger.providerName === PROVIDERS.HUBSPOT){
              const triggerSourceIdentifier = (trigger.data as IGhlTrigger).sourceIdentifier;
              this.myLogger.log({
                message: `source identifier set for hubspot - ${triggerSourceIdentifier} | channel account id from the webhook - ${rawPayload?.channelAccount?.channelId} | agent id - ${agent._id.toString()}`,
                context: `webhook service . filterAgents`
              })
              otherConditionsMet = triggerSourceIdentifier === rawPayload?.channelAccount?.inboxId;
            }
          }
          return (
            (trigger.data as IGhlTrigger).subaccount === accountId &&
            (trigger.data as IGhlTrigger).channel === channel &&
            (trigger.data as IGhlTrigger).tagOption !== undefined && 
            otherConditionsMet
          );
        });
        for (const trigger of triggers) {
          if ((trigger.data as IGhlTrigger)?.tagOption === 'hasTag') {
            if (tags.includes((trigger.data as IGhlTrigger)?.tagValue)) {
              return true;
            }
          } else if (
            (trigger.data as IGhlTrigger).tagOption === 'doesntHaveTag'
          ) {
            if (!tags.includes((trigger.data as IGhlTrigger).tagValue)) {
              return true;
            }
          }
        }
        return false;
      });

      this.myLogger.log({
        message: `@agents filtered found: ${filteredAgents.length} | orgId: ${orgId} | accountId: ${accountId} | channel: ${channel} | tags: ${tags}`,
        context: 'filter agents in webhook service'
      });
      return [filteredAgents, ''];
    } catch (err) {
      this.myLogger.error({
        message:  `error filterAgents: ${err.message}`,
        context: 'filter agents in webhook service'
      });
      return [[], err.message.includes('Custom') ? err.message : ''];
    }
  }

  async handleRolloverEvent(
    rolloverEvent: rolloverEventDto,
    locationId?: string,
  ) {
    try {
      let query = {};
      if (locationId) query['resourceId'] = locationId;
      query['contactId'] = rolloverEvent.contactId;
      const res = await this.mongoConversationService.updateConversation({
        query,
        updateBody: {
          $set: {
            rollover: rolloverEvent.rollover,
            rolloverReason: rolloverEvent.reason,
            rolloverDate: rolloverEvent.date,
          },
        },
      });
      this.myLogger.log({
        message: `Rollover event handled for contactId: ${
          rolloverEvent.contactId
        } | resourceId: ${locationId} | result: ${JSON.stringify(res)}`,
        context:  rolloverEvent.contactId
      });
    } catch (error) {
      this.myLogger.error({
        message: `error handle RolloverEvent: ${error.message}`,
        context: 'channel webhooks handleRolloverEvent',
      });
    }
  }
}