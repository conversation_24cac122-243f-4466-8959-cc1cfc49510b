import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
  Req,
} from '@nestjs/common';
import { AgentSnapshotsService } from './agent-snapshots.service';
import { UpdateAgentSnapshotDto } from './dto/update-agent-snapshot.dto';
import { CreateAgentSnapshotDto } from './dto/create-agent-snapshot.dto';
import { CreateSnapshotFromAgentDto } from './dto/create-snapshot-from-agent.dto';
import { CreateAgentFromSnapshotDTO } from './dto/create-agent-from-snapshot.dto';
import { SyncSnapshotWithParentAgentDto } from './dto/sync-snapshot-with-parent-agent.dto';
import { SyncAgentsWithSnapshotDto } from './dto/sync-agents-with-snapshot.dto';
import { LinkExistingAgentToSnapshotDto } from './dto/link-exisiting-agent-to-snapshot.dto';
import { ShareSnapshotDto } from './dto/share-snapshot.dto';
import { GenerateShareLinkDto } from './dto/generate-share-link.dto';
import { SetSnapshotPublicDto } from './dto/set-snapshot-public.dto';
import { HandleInviteDto } from './dto/handle-invite.dto';
import { RequestWithUser } from 'src/auth/auth.interface';

@Controller('agent-snapshots')
export class AgentSnapshotsController {
  constructor(private readonly agentSnapshotsService: AgentSnapshotsService) {}

  @Get()
  getAgentSnapshotsByOrgId(@Query('orgId') orgId: string) {
    return this.agentSnapshotsService.getAgentSnapshotsByOrgId(orgId);
  }

  @Get('requests')
  getSnapshotRequests(@Query('orgId') orgId: string) {
    return this.agentSnapshotsService.getSnapshotRequests(orgId);
  }

  @Post()
  createAgentSnapshot(@Body() body: CreateAgentSnapshotDto) {
    return this.agentSnapshotsService.createAgentSnapshot(body.orgId, body);
  }

  @Post('create-from-agent')
  createSnapshotFromAgent(
    @Body()
    body: CreateSnapshotFromAgentDto,
  ) {
    return this.agentSnapshotsService.createSnapshotFromAgent(body);
  }

  @Post('use-snapshot')
  createAgentFromSnapshot(@Req() req: RequestWithUser, @Body() body: CreateAgentFromSnapshotDTO) {
    let orgId = req.orgId;
    return this.agentSnapshotsService.createAgentFromSnapshot(body, orgId);
  }

  @Post('sync-with-parent-agent')
  syncSnapshotWithParentAgent(@Body() body: SyncSnapshotWithParentAgentDto) {
    return this.agentSnapshotsService.syncSnapshotWithParentAgent(
      body.snapshotId,
    );
  }

  @Post('sync-agents-with-snapshot')
  async syncAgentsWithSnapshot(@Body() body: SyncAgentsWithSnapshotDto) {
    return this.agentSnapshotsService.syncAgentsWithSnapshot(
      body.snapshotId,
      body.agentIds,
      body.propertiesToSync,
    );
  }

  @Post('link-existing-agent-to-snapshot')
  async linkExistingAgentToSnapshot(
    @Body() body: LinkExistingAgentToSnapshotDto,
  ) {
    return this.agentSnapshotsService.linkExistingAgentToSnapshot(body);
  }

  @Get('check-unique-name')
  checkUniqueName(@Query('name') name: string) {
    return this.agentSnapshotsService.checkUniqueName(name);
  }

  @Get('agents')
  getAgentsBySnapshotId(@Query('snapshotId') snapshotId: string) {
    return this.agentSnapshotsService.getAgentsBySnapshotId(snapshotId);
  }

  @Get('agents/:agentId/snapshots')
  getSnapshotsByAgentId(@Param('agentId') agentId: string) {
    return this.agentSnapshotsService.getSnapshotsByAgentId(agentId);
  }

  @Put(':snapshotId')
  updateAgentSnapshot(
    @Param('snapshotId') snapshotId: string,
    @Body() body: UpdateAgentSnapshotDto,
  ) {
    return this.agentSnapshotsService.updateAgentSnapshot(snapshotId, body);
  }

  @Delete(':snapshotId')
  deleteAgentSnapshot(@Param('snapshotId') snapshotId: string) {
    return this.agentSnapshotsService.deleteAgentSnapshot(snapshotId);
  }

  // New sharing endpoints
  @Post(':snapshotId/share')
  shareSnapshot(@Body() shareDto: ShareSnapshotDto) {
    return this.agentSnapshotsService.shareSnapshot(shareDto);
  }

  @Post(':snapshotId/generate-link')
  generateShareLink(@Body() generateLinkDto: GenerateShareLinkDto) {
    return this.agentSnapshotsService.generateShareLink(generateLinkDto);
  }

  @Post(':snapshotId/set-public')
  setSnapshotPublic(@Body() setPublicDto: SetSnapshotPublicDto) {
    return this.agentSnapshotsService.setSnapshotPublic(setPublicDto);
  }

  @Delete(':snapshotId/share/:orgId')
  removeSharing(
    @Param('snapshotId') snapshotId: string,
    @Param('orgId') orgId: string,
  ) {
    return this.agentSnapshotsService.removeSharing(snapshotId, orgId);
  }

  @Delete(':snapshotId/link')
  removePublicSharing(@Param('snapshotId') snapshotId: string) {
    return this.agentSnapshotsService.removePublicSharing(snapshotId);
  }

  @Get('shared')
  getSnapshotByShareLink(
    @Query('shareLink') shareLink: string,
    @Query('orgId') orgId: string,
  ) {
    return this.agentSnapshotsService.getSnapshotByShareLink(shareLink, orgId);
  }

  @Post(':snapshotId/handle-invite')
  handleInvite(@Body() handleInviteDto: HandleInviteDto) {
    return this.agentSnapshotsService.handleInvite(handleInviteDto);
  }
}
