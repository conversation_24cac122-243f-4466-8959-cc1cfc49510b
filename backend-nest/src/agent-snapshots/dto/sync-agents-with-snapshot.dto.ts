import { IsString, <PERSON><PERSON><PERSON>Empt<PERSON>, <PERSON><PERSON><PERSON><PERSON>, IsIn } from 'class-validator';

export const SYNCABLE_PROPERTIES = [
  'prompts',
  'actions',
  'multipleInbound',
  'multipleInboundConfig',
  'humanTakeover',
  'attachmentConfig',
  'emptyMessageConfig',
  'fallbackConfig',
  'contextLength',
  'triggers',
] as const;

export type SyncableProperty = (typeof SYNCABLE_PROPERTIES)[number];

export class SyncAgentsWithSnapshotDto {
  @IsString()
  @IsNotEmpty()
  snapshotId: string;

  @IsArray()
  @IsNotEmpty()
  agentIds: string[];

  @IsArray()
  @IsNotEmpty()
  @IsIn(SYNCABLE_PROPERTIES, { each: true })
  propertiesToSync: SyncableProperty[];
}
