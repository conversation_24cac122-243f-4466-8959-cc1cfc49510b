import {
  IsString,
  IsOptional,
  IsBoolean,
  ValidateNested,
  <PERSON>N<PERSON>ber,
  IsArray,
} from 'class-validator';
import { Type } from 'class-transformer';

class PromptItemDto {
  @IsString()
  @IsOptional()
  snapshotPromptId?: string;

  @IsString()
  @IsOptional()
  originalPromptId?: string;

  @IsString()
  name: string;

  @IsString()
  promptContent: string;

  @IsString()
  @IsOptional()
  fallbackPrompt?: string;

  @IsBoolean()
  @IsOptional()
  fallbackPromptActive?: boolean;

  @IsBoolean()
  @IsOptional()
  isFallbackPrompt?: boolean;

  @IsString()
  @IsOptional()
  customFallbackPrompt?: string;
}

class SnapshotPromptDto {
  @IsString()
  currentActive: string;

  @ValidateNested({ each: true })
  @Type(() => PromptItemDto)
  prompt: PromptItemDto[];
}

class IncludeMainPromptDetailsDto {
  @IsBoolean()
  includeMainPrompt: boolean;

  @IsString()
  mainPromptId: string;
}

class ActionDto {
  @IsString()
  @IsOptional()
  snapshotActionId?: string;

  @IsString()
  @IsOptional()
  originalActionId?: string;

  @IsString()
  providerName: string;

  @IsString()
  promptContent: string;

  @IsString()
  activity: string;

  //   @IsString()
  //   accountName: string;

  @IsBoolean()
  silent: boolean;

  @ValidateNested()
  @Type(() => IncludeMainPromptDetailsDto)
  includeMainPromptDetails: IncludeMainPromptDetailsDto;

  @IsBoolean()
  isAdvancedSettings: boolean;

  @IsOptional()
  advancedSettings?: {
    maxTokensAllowed: number;
    aiProvider: {
      companyId: string;
      modelName: string;
      accountName: string;
      accountId: string;
    };
  };

  @IsOptional()
  jsonObjects?: {
    name?: string;
    generateCondition?: string;
    properties?: any[];
  };
}

class TriggerDataDto {
  @IsString()
  @IsOptional()
  subaccount?: string;

  @IsString()
  @IsOptional()
  channel?: string;

  @IsString()
  @IsOptional()
  tagOption?: string;

  @IsString()
  @IsOptional()
  tagValue?: string;

  @IsString()
  @IsOptional()
  liveChatWidget?: string;

  @IsString()
  @IsOptional()
  task?: string;

  @IsString()
  @IsOptional()
  sourceIdentifier?: string;

  @IsString()
  @IsOptional()
  prompt?: string;

  @IsNumber()
  @IsOptional()
  history?: number;

  @IsString()
  @IsOptional()
  include?: string;

  @IsString()
  @IsOptional()
  exclude?: string;

  @IsString()
  @IsOptional()
  include_knowledge?: string;

  @IsString()
  @IsOptional()
  exclude_knowledge?: string;

  @IsArray()
  @IsOptional()
  tagConditions?: any[];
}

class TriggerFollowUpDto {
  @IsBoolean()
  isFollowupEnabled: boolean;

  @IsNumber()
  @IsOptional()
  duration?: number;

  @IsNumber()
  @IsOptional()
  maxAttempts?: number;

  @IsString()
  @IsOptional()
  promptId?: string;

  @IsArray()
  @IsOptional()
  tagConditions?: any[];
}

class TriggerDto {
  @IsString()
  @IsOptional()
  triggerId?: string;

  @IsString()
  triggerName: string;

  @IsString()
  providerName: string;

  @ValidateNested()
  @Type(() => TriggerDataDto)
  data: TriggerDataDto;

  @ValidateNested()
  @Type(() => TriggerFollowUpDto)
  followUp: TriggerFollowUpDto;

  @IsBoolean()
  active: boolean;
}

export class CreateAgentSnapshotDto {
  @IsString()
  orgId: string;

  @IsString()
  @IsOptional()
  parentAgentId: string;

  @IsString()
  name: string;

  @IsString()
  @IsOptional()
  description: string;

  @ValidateNested()
  @Type(() => SnapshotPromptDto)
  prompts: SnapshotPromptDto;

  @ValidateNested({ each: true })
  @Type(() => ActionDto)
  actions: ActionDto[];

  @IsBoolean()
  multipleInbound: boolean;

  @ValidateNested()
  multipleInboundConfig: {
    initialWait: number;
    maxWait: number;
    incrementBy: number;
  };

  @ValidateNested()
  humanTakeover: {
    takeoverType: string;
    tagToAdd: string;
    timeToWait: number;
  };

  @ValidateNested()
  attachmentConfig: {
    withoutMessage: {
      enabled: boolean;
      tagToAdd: string;
      setSilent: boolean;
    };
    withMessage: {
      enabled: boolean;
      tagToAdd: string;
      setSilent: boolean;
    };
  };

  @ValidateNested()
  emptyMessageConfig: {
    enabled: boolean;
    tagToAdd: string;
    setSilent: boolean;
  };

  @ValidateNested()
  fallbackConfig: {
    enabled: boolean;
    tagToAdd: string;
    setSilent: boolean;
  };

  @ValidateNested({ each: true })
  @Type(() => TriggerDto)
  @IsOptional()
  triggers?: TriggerDto[];

  @IsNumber()
  @IsOptional()
  contextLength?: number;
}
