import {
  IsNotEmpty,
  <PERSON><PERSON>ptional,
  <PERSON>S<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  IsIn,
} from 'class-validator';
import {
  SYNCABLE_PROPERTIES,
  SyncableProperty,
} from './sync-agents-with-snapshot.dto';

export class CreateAgentFromSnapshotDTO {
  @IsString()
  @IsNotEmpty()
  snapshotId: string;

  @IsString()
  @IsNotEmpty()
  agentName: string;

  @IsString()
  @IsOptional()
  folderId: string;

  @IsArray()
  @IsOptional()
  @IsIn(SYNCABLE_PROPERTIES, { each: true })
  propertiesToSync?: SyncableProperty[];
}
