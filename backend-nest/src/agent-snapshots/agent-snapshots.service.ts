import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { v4 as uuidv4 } from 'uuid';
import {
  AgentSnapshots,
  AgentSnapshotsDocument,
} from '../mongo/schemas/agent-snapshots/agent-snapshots.schema';
import { Agent, AgentDocument } from '../mongo/schemas/agents/agents.schema';
import { CreateAgentSnapshotDto } from './dto/create-agent-snapshot.dto';
import { UpdateAgentSnapshotDto } from './dto/update-agent-snapshot.dto';
import { MongoAgentService } from 'src/mongo/service/agent/mongo-agent.service';
import { CreateSnapshotFromAgentDto } from './dto/create-snapshot-from-agent.dto';
import { CreateAgentFromSnapshotDTO } from './dto/create-agent-from-snapshot.dto';
import { CUSTOM_LLM_7, OPTIMIZEOPTIONS, PROVIDERS } from 'src/lib/constant';
import { CreateAgentDto } from 'src/mongo/dto/agent/create.dto';
import { LinkExistingAgentToSnapshotDto } from './dto/link-exisiting-agent-to-snapshot.dto';
import { FoldersService } from 'src/folders/folders.service';
import { FollowUpSchema } from 'src/mongo/schemas/agents/agents.schema';
import { MongoOrganizationService } from 'src/mongo/service/organization/mongo-organization.service';
import { ShareSnapshotDto } from './dto/share-snapshot.dto';
import { GenerateShareLinkDto } from './dto/generate-share-link.dto';
import { SetSnapshotPublicDto } from './dto/set-snapshot-public.dto';
import { HandleInviteDto } from './dto/handle-invite.dto';

@Injectable()
export class AgentSnapshotsService {
  constructor(
    @InjectModel(AgentSnapshots.name)
    private readonly agentSnapshotsModel: Model<AgentSnapshotsDocument>,
    private readonly mongoOrganizationService: MongoOrganizationService,
    @InjectModel(Agent.name)
    private readonly agentModel: Model<AgentDocument>,
    private readonly mongoAgentService: MongoAgentService,
    private readonly foldersService: FoldersService,
  ) { }

  // Helper method to map agent data to snapshot format
  private mapAgentToSnapshotData(
    agent: any,
    existingSnapshot?: AgentSnapshots,
  ): Required<
    Omit<
      CreateAgentSnapshotDto,
      'orgId' | 'name' | 'description' | 'parentAgentId'
    >
  > {
    // Store all prompts from the agent
    const allPrompts = agent.prompts?.prompt || [];
    const currentActivePromptId = agent.prompts?.currentActive;

    // Create a map to store promptId to snapshotPromptId mapping
    const promptIdToSnapshotId = new Map<string, string>();

    // Map all prompts and store their IDs
    const mappedPrompts = allPrompts.map((p) => {
      const snapshotPromptId = uuidv4();
      promptIdToSnapshotId.set(p.promptId, snapshotPromptId);
      return {
        snapshotPromptId,
        originalPromptId: p.promptId,
        name: p.name,
        promptContent: p.promptContent,
        fallbackPrompt: p.fallbackPrompt || '',
        fallbackPromptActive: !!p.fallbackPromptActive,
        isFallbackPrompt: !!p.isFallbackPrompt,
        customFallbackPrompt: p.customFallbackPrompt || '',
      };
    });

    return {
      prompts: {
        currentActive:
          promptIdToSnapshotId.get(currentActivePromptId) ||
          mappedPrompts[0]?.snapshotPromptId ||
          '',
        prompt: mappedPrompts,
      },
      actions:
        agent.actions?.map((action) => {
          const existingAction = existingSnapshot?.actions.find(
            (sa) =>
              sa.activity === action.activity &&
              sa.providerName === action.providerName,
          );

          // For GHL provider, preserve accountId and accountName
          const shouldPreserveGhlDetails = action.providerName === 'ghl';

          return {
            snapshotActionId: existingAction?.snapshotActionId || uuidv4(),
            originalActionId: action.actionId, // Store original action ID
            providerName: action.providerName,
            promptContent: action.promptContent,
            activity: action.activity,
            // Only include accountId and accountName for GHL provider
            ...(shouldPreserveGhlDetails && {
              accountId: action.accountId || existingAction?.accountId || '',
              accountName:
                action.accountName || existingAction?.accountName || '',
            }),
            silent: !!action.silent,
            includeMainPromptDetails: {
              includeMainPrompt:
                !!action.includeMainPromptDetails?.includeMainPrompt,
              mainPromptId: String(
                action.includeMainPromptDetails?.mainPromptId || '',
              ),
            },
            isAdvancedSettings: !!action.isAdvancedSettings,
            advancedSettings: action.advancedSettings
              ? {
                maxTokensAllowed: action.advancedSettings.maxTokensAllowed,
                aiProvider: {
                  companyId: action.advancedSettings.aiProvider?.companyId,
                  modelName: action.advancedSettings.aiProvider?.modelName,
                  accountName:
                    action.advancedSettings.aiProvider?.accountName,
                  accountId: action.advancedSettings.aiProvider?.accountId,
                },
              }
              : undefined,
            // Add metaData from parent agent only if it has meaningful data
            metaData: action.metaData ? (() => {
              const metaDataObj: any = {};
              
              // Only add tagData if it has meaningful values
              if (action.metaData.tagData) {
                const tagData = action.metaData.tagData;
                if (tagData.tagValue || tagData.evaluateOn || tagData.actionSelection || tagData.reply) {
                  metaDataObj.tagData = {
                    tagValue: tagData.tagValue || '',
                    evaluateOn: tagData.evaluateOn || '',
                    actionSelection: tagData.actionSelection || '',
                    reply: !!tagData.reply,
                  };
                }
              }
              
              // Only add customFieldData if it has meaningful values
              if (action.metaData.customFieldData) {
                const customFieldData = action.metaData.customFieldData;
                if (customFieldData.fieldKey || customFieldData.evaluateOn || customFieldData.reply) {
                  metaDataObj.customFieldData = customFieldData;
                }
              }
              
              // Only add ghlCalendarMetaData if it has meaningful values
              if (action.metaData.ghlCalendarMetaData) {
                const ghlData = action.metaData.ghlCalendarMetaData;
                if (ghlData.evaluateOn || ghlData.dayRange || ghlData.maxRange || ghlData.cancelEvent || ghlData.rescheduleEvent) {
                  metaDataObj.ghlCalendarMetaData = {
                    evaluateOn: ghlData.evaluateOn || '',
                    dayRange: ghlData.dayRange || 0,
                    maxRange: ghlData.maxRange || 0,
                    cancelEvent: !!ghlData.cancelEvent,
                    rescheduleEvent: !!ghlData.rescheduleEvent,
                  };
                }
              }
              
              // Only add googleCalendarMetaData if it has meaningful values
              if (action.metaData.googleCalendarMetaData) {
                const googleData = action.metaData.googleCalendarMetaData;
                if (googleData.cancelEvent || googleData.rescheduleEvent) {
                  metaDataObj.googleCalendarMetaData = {
                    cancelEvent: !!googleData.cancelEvent,
                    rescheduleEvent: !!googleData.rescheduleEvent,
                  };
                }
              }
              
              // Only add standardFieldData if it has meaningful values
              if (action.metaData.standardFieldData) {
                const fieldData = action.metaData.standardFieldData;
                if (fieldData.fieldKeys?.length || fieldData.reply || (fieldData.evaluateOn && fieldData.evaluateOn !== 'isEmpty')) {
                  metaDataObj.standardFieldData = {
                    fieldKeys: fieldData.fieldKeys || [],
                    reply: !!fieldData.reply,
                    evaluateOn: fieldData.evaluateOn || 'isEmpty',
                  };
                }
              }
              
              // Return undefined if no meaningful data was found
              return Object.keys(metaDataObj).length > 0 ? metaDataObj : undefined;
            })() : undefined,
            jsonObjects: action.jsonObjects
              ? {
                name: action.jsonObjects.name,
                generateCondition: action.jsonObjects.generateCondition,
                properties: action.jsonObjects.properties,
              }
              : undefined,
          };
        }) || [],
      triggers:
        agent.triggers?.map((trigger) => {
          // Create mappings for include/exclude action IDs
          const includeActions = (trigger.data?.include || '')
            .split(',')
            .filter(Boolean);
          const excludeActions = (trigger.data?.exclude || '')
            .split(',')
            .filter(Boolean);

          return {
            triggerId: uuidv4(), // Generate a unique triggerId
            parentTriggerId: trigger.triggerId || '', // Store the parent agent's triggerId
            triggerName: trigger.triggerName || '',
            providerName: trigger.providerName || '',
            data: {
              subaccount: trigger.data?.subaccount || '',
              channel: trigger.data?.channel || '',
              tagOption: trigger.data?.tagOption || '',
              tagValue: trigger.data?.tagValue || '',
              liveChatWidget: trigger.data?.liveChatWidget || '',
              task: trigger.data?.task || '',
              sourceIdentifier: trigger.data?.sourceIdentifier || '',
              prompt: trigger.data?.prompt || '', // Store original prompt ID
              history: trigger.data?.history || 0,
              include: includeActions.join(','), // Store original action IDs
              exclude: excludeActions.join(','), // Store original action IDs
              include_knowledge: trigger.data?.include_knowledge || '',
              exclude_knowledge: trigger.data?.exclude_knowledge || '',
              tagConditions: trigger.data?.tagConditions || [],
            },
            followUp: {
              isFollowupEnabled: trigger.followUp?.isFollowupEnabled || false,
              duration: trigger.followUp?.duration || 15,
              maxAttempts: trigger.followUp?.maxAttempts || 2,
              promptId: trigger.followUp?.promptId || '', // Store original prompt ID
              conditionPrompt: trigger.followUp?.conditionPrompt || '', // Include the conditionPrompt field
              tagConditions: trigger.followUp?.tagConditions || [],
              timezone: trigger.followUp?.timezone || '',
              schedule: trigger.followUp?.schedule || {},
            },
            active: trigger.active || false,
          };
        }) || [],
      multipleInbound: !!agent.multipleInbound,
      multipleInboundConfig: {
        initialWait: Number(agent.multipleInboundConfig?.initialWait) || 0,
        maxWait: Number(agent.multipleInboundConfig?.maxWait) || 10,
        incrementBy: Number(agent.multipleInboundConfig?.incrementBy) || 0,
      },
      humanTakeover: {
        takeoverType: agent.humanTakeover?.takeoverType || 'None',
        tagToAdd: agent.humanTakeover?.tagToAdd || '',
        timeToWait: agent.humanTakeover?.timeToWait || 0,
      },
      attachmentConfig: {
        withoutMessage: {
          enabled: agent.attachmentConfig?.withoutMessage?.enabled || false,
          tagToAdd: agent.attachmentConfig?.withoutMessage?.tagToAdd || '',
          setSilent: agent.attachmentConfig?.withoutMessage?.setSilent || false,
        },
        withMessage: {
          enabled: agent.attachmentConfig?.withMessage?.enabled || false,
          tagToAdd: agent.attachmentConfig?.withMessage?.tagToAdd || '',
          setSilent: agent.attachmentConfig?.withMessage?.setSilent || false,
        },
      },
      emptyMessageConfig: {
        enabled: agent.emptyMessageConfig?.enabled || false,
        tagToAdd: agent.emptyMessageConfig?.tagToAdd || '',
        setSilent: agent.emptyMessageConfig?.setSilent || false,
      },
      // Add fallbackConfig
      fallbackConfig: {
        enabled: agent.fallbackConfig?.enabled || false,
        tagToAdd: agent.fallbackConfig?.tagToAdd || '',
        setSilent: agent.fallbackConfig?.setSilent || false,
      },
      // Add contextLength
      contextLength: agent.contextLength || 1000,
    };
  }

  // Helper method to map snapshot data to agent format
  private mapSnapshotToAgentData(
    snapshot: AgentSnapshots,
    existingAgent?: any,
  ): Pick<CreateAgentDto, 'prompts' | 'actions'> & {
    multipleInbound: boolean;
    multipleInboundConfig: {
      initialWait: number;
      maxWait: number;
      incrementBy: number;
    };
    humanTakeover: {
      takeoverType: string;
      tagToAdd: string;
      timeToWait: number;
    };
    attachmentConfig: {
      withoutMessage: {
        enabled: boolean;
        tagToAdd: string;
        setSilent: boolean;
      };
      withMessage: {
        enabled: boolean;
        tagToAdd: string;
        setSilent: boolean;
      };
    };
    emptyMessageConfig: {
      enabled: boolean;
      tagToAdd: string;
      setSilent: boolean;
    };
    fallbackConfig: {
      enabled: boolean;
      tagToAdd: string;
      setSilent: boolean;
    };
    contextLength: number;
    disabled: boolean;
    triggers?: {
      triggerId: string;
      triggerName: string;
      providerName: string;
      parentTriggerId: string;
      data: {
        subaccount: string;
        channel: string;
        tagOption: string;
        tagValue: string;
        liveChatWidget: string;
        task: string;
        sourceIdentifier: string;
        prompt: string;
        history: number;
        include: string;
        exclude: string;
        include_knowledge: string;
        exclude_knowledge: string;
        tagConditions: any[];
      };
      followUp: FollowUpSchema;
      active: boolean;
    }[];
  } {
    // Create ID mappings for actions and prompts
    const actionIdMap = new Map<string, string>();
    const promptIdMap = new Map<string, string>();

    // Create a map of existing promptIds from the agent (if it exists)
    const existingPromptIdMap = new Map<string, string>();
    if (existingAgent?.prompts?.prompt) {
      // Map both by name and originalPromptId for better matching
      existingAgent.prompts.prompt.forEach((prompt) => {
        existingPromptIdMap.set(prompt.name, prompt.promptId);
        if (prompt.originalPromptId) {
          existingPromptIdMap.set(prompt.originalPromptId, prompt.promptId);
        }
      });
    }

    // Generate new prompt IDs and create mapping, preserving existing IDs if available
    const allPrompts = snapshot.prompts.prompt.map((prompt) => {
      // Try to find an existing promptId by name or original ID
      const existingId =
        existingPromptIdMap.get(prompt.name) ||
        existingPromptIdMap.get(prompt.originalPromptId);

      // Use existing ID if available, otherwise generate a new one
      const newPromptId = existingId || uuidv4();
      promptIdMap.set(prompt.originalPromptId, newPromptId);

      return {
        promptId: newPromptId,
        name: prompt.name,
        promptContent: prompt.promptContent,
        fallbackPrompt: prompt.fallbackPrompt || '',
        fallbackPromptActive: prompt.fallbackPromptActive,
        isFallbackPrompt: prompt.isFallbackPrompt,
        customFallbackPrompt: prompt.customFallbackPrompt || '',
      };
    });

    // Find the current active prompt's new ID
    const currentActivePrompt = snapshot.prompts.prompt.find(
      (p) => p.snapshotPromptId === snapshot.prompts.currentActive,
    );
    const newCurrentActiveId = currentActivePrompt
      ? promptIdMap.get(currentActivePrompt.originalPromptId)
      : allPrompts[0].promptId;

    // Create a map of existing actionIds from the agent (if it exists)
    const existingActionIdMap = new Map<string, string>();
    if (existingAgent?.actions) {
      existingAgent.actions.forEach((action) => {
        // Create a composite key using activity and providerName
        const key = `${action.activity}:${action.providerName}`;
        existingActionIdMap.set(key, action.actionId);
      });
    }

    // Map actions and create ID mapping
    const actions = snapshot.actions.map((snapshotAction) => {
      const existingAction = existingAgent?.actions?.find(
        (aa) =>
          aa.activity === snapshotAction.activity &&
          aa.providerName === snapshotAction.providerName,
      );

      // Use existing actionId if available, otherwise generate a new one
      const compositeKey = `${snapshotAction.activity}:${snapshotAction.providerName}`;
      const newActionId =
        existingAction?.actionId ||
        existingActionIdMap.get(compositeKey) ||
        uuidv4();
      actionIdMap.set(snapshotAction.originalActionId, newActionId);

      // For GHL provider, use the accountId and accountName from the snapshot
      const shouldPreserveGhlDetails = snapshotAction.providerName === 'ghl';

      return {
        actionId: newActionId,
        accountId: shouldPreserveGhlDetails
          ? snapshotAction.accountId
          : existingAction?.accountId || '',
        accountName: shouldPreserveGhlDetails
          ? snapshotAction.accountName
          : existingAction?.accountName || '',
        providerName: snapshotAction.providerName,
        promptContent: snapshotAction.promptContent,
        activity: snapshotAction.activity,
        silent: snapshotAction.silent,
        includeMainPromptDetails: {
          includeMainPrompt:
            snapshotAction.includeMainPromptDetails.includeMainPrompt,
          mainPromptId: snapshotAction.includeMainPromptDetails.mainPromptId,
        },
        isAdvancedSettings: snapshotAction.isAdvancedSettings,
        advancedSettings: snapshotAction.advancedSettings,
        // Add metaData from snapshot to child agent only if it has meaningful data
        metaData: snapshotAction.metaData ? (() => {
          const metaDataObj: any = {};
          
          // Only add tagData if it has meaningful values
          if (snapshotAction.metaData.tagData) {
            const tagData = snapshotAction.metaData.tagData;
            if (tagData.tagValue || tagData.evaluateOn || tagData.actionSelection || tagData.reply) {
              metaDataObj.tagData = {
                tagValue: tagData.tagValue || '',
                evaluateOn: tagData.evaluateOn || '',
                actionSelection: tagData.actionSelection || '',
                reply: !!tagData.reply,
              };
            }
          }
          
          // Only add customFieldData if it has meaningful values
          if (snapshotAction.metaData.customFieldData) {
            const customFieldData = snapshotAction.metaData.customFieldData;
            if (customFieldData.fieldKey || customFieldData.evaluateOn || customFieldData.reply) {
              metaDataObj.customFieldData = customFieldData;
            }
          }
          
          // Only add ghlCalendarMetaData if it has meaningful values
          if (snapshotAction.metaData.ghlCalendarMetaData) {
            const ghlData = snapshotAction.metaData.ghlCalendarMetaData;
            if (ghlData.evaluateOn || ghlData.dayRange || ghlData.maxRange || ghlData.cancelEvent || ghlData.rescheduleEvent) {
              metaDataObj.ghlCalendarMetaData = {
                evaluateOn: ghlData.evaluateOn || '',
                dayRange: ghlData.dayRange || 0,
                maxRange: ghlData.maxRange || 0,
                cancelEvent: !!ghlData.cancelEvent,
                rescheduleEvent: !!ghlData.rescheduleEvent,
              };
            }
          }
          
          // Only add googleCalendarMetaData if it has meaningful values
          if (snapshotAction.metaData.googleCalendarMetaData) {
            const googleData = snapshotAction.metaData.googleCalendarMetaData;
            if (googleData.cancelEvent || googleData.rescheduleEvent) {
              metaDataObj.googleCalendarMetaData = {
                cancelEvent: !!googleData.cancelEvent,
                rescheduleEvent: !!googleData.rescheduleEvent,
              };
            }
          }
          
          // Only add standardFieldData if it has meaningful values
          if (snapshotAction.metaData.standardFieldData) {
            const fieldData = snapshotAction.metaData.standardFieldData;
            if (fieldData.fieldKeys?.length || fieldData.reply || (fieldData.evaluateOn && fieldData.evaluateOn !== 'isEmpty')) {
              metaDataObj.standardFieldData = {
                fieldKeys: fieldData.fieldKeys || [],
                reply: !!fieldData.reply,
                evaluateOn: fieldData.evaluateOn || 'isEmpty',
              };
            }
          }
          
          // Return undefined if no meaningful data was found
          return Object.keys(metaDataObj).length > 0 ? metaDataObj : undefined;
        })() : undefined,
        jsonObjects: snapshotAction.jsonObjects,
      };
    });

    // Map triggers with updated IDs
    const triggers = snapshot.triggers?.map((trigger) => {
      // Map include/exclude action IDs
      const includeActions = (trigger.data.include || '')
        .split(',')
        .filter(Boolean)
        .map((id) => actionIdMap.get(id) || '')
        .filter(Boolean);

      const excludeActions = (trigger.data.exclude || '')
        .split(',')
        .filter(Boolean)
        .map((id) => actionIdMap.get(id) || '')
        .filter(Boolean);

      // Find existing trigger in agent with matching parentTriggerId
      const existingTrigger = existingAgent?.triggers?.find(
        (t) => t.parentTriggerId === trigger.parentTriggerId,
      );

      return {
        triggerId: uuidv4(), // Generate new triggerId
        parentTriggerId: trigger.parentTriggerId || '', // Preserve the parent triggerId
        triggerName: trigger.triggerName,
        providerName: trigger.providerName,
        data: {
          ...trigger.data,
          // Preserve subaccount from existing trigger if it exists
          subaccount: existingTrigger?.data?.subaccount || '',
          // Use existing prompt ID if available, otherwise map to new ID
          prompt:
            existingTrigger?.data?.prompt ||
            promptIdMap.get(trigger.data.prompt) ||
            '',
          include: includeActions.join(','), // Map action IDs
          exclude: excludeActions.join(','), // Map action IDs
        },
        followUp: {
          ...trigger.followUp,
          // Use existing promptId if available, otherwise map to new ID
          promptId:
            existingTrigger?.followUp?.promptId ||
            promptIdMap.get(trigger.followUp.promptId) ||
            '',
          conditionPrompt: trigger.followUp.conditionPrompt || '', // Preserve the conditionPrompt
        },
        // Preserve existing trigger's active status if it exists, otherwise use snapshot's value
        active: existingTrigger ? existingTrigger.active : trigger.active,
      };
    });

    return {
      prompts: {
        currentActive: newCurrentActiveId,
        prompt: allPrompts,
      },
      actions,
      triggers,
      // Preserve existing agent's disabled status if it exists
      disabled: existingAgent ? existingAgent.disabled : true,
      multipleInbound: snapshot.multipleInbound,
      multipleInboundConfig: snapshot.multipleInboundConfig,
      humanTakeover: snapshot.humanTakeover,
      attachmentConfig: snapshot.attachmentConfig,
      emptyMessageConfig: snapshot.emptyMessageConfig,
      // Add fallbackConfig
      fallbackConfig: snapshot.fallbackConfig,
      // Add contextLength
      contextLength: snapshot.contextLength,
    };
  }

  async getAgentSnapshotsByOrgId(orgId: string) {
    const ownSnapshots = await this.agentSnapshotsModel.find({
      orgId,
    });

    // Find snapshots shared with this organization where the invite has been accepted
    const sharedSnapshots = await this.agentSnapshotsModel.find({
      sharedWith: {
        $elemMatch: {
          orgId,
          acceptedSnapshotInvite: true,
        },
      },
    });

    // Combine own snapshots and shared snapshots
    return [...ownSnapshots, ...sharedSnapshots];
  }

  async getSnapshotRequests(orgId: string) {
    const snapshotRequests = await this.agentSnapshotsModel.find({
      sharedWith: {
        $elemMatch: {
          orgId,
          acceptedSnapshotInvite: false,
        },
      },
    });


    return snapshotRequests;
  }

  async createAgentSnapshot(orgId: string, createDto: CreateAgentSnapshotDto) {
    try {
      // Add snapshotActionId to each action
      const actionsWithIds = createDto.actions.map((action) => ({
        ...action,
        snapshotActionId: uuidv4(),
      }));
      
      // Add snapshotPromptId to each prompt
      const promptsWithIds = {
        ...createDto.prompts,
        prompt: createDto.prompts.prompt.map((prompt) => ({
          ...prompt,
          snapshotPromptId: prompt.snapshotPromptId || uuidv4(),
        })),
      };

      // Create model instance and save to trigger pre-save hooks
      const newSnapshot = new this.agentSnapshotsModel({
        ...createDto,
        orgId,
        actions: actionsWithIds,
        prompts: promptsWithIds,
      });
      
      const savedSnapshot = await newSnapshot.save();
      return savedSnapshot;
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  async updateAgentSnapshot(
    snapshotId: string,
    updateDto: UpdateAgentSnapshotDto,
  ) {
    const snapshot = await this.agentSnapshotsModel.findById(snapshotId);
    if (!snapshot) {
      throw new NotFoundException(`Snapshot with ID ${snapshotId} not found`);
    }

    // If actions are being updated, ensure each action has a snapshotActionId
    if (updateDto.actions) {
      updateDto.actions = updateDto.actions.map((action) => ({
        ...action,
        snapshotActionId: action.snapshotActionId || uuidv4(),
      }));
    }

    // Update the document fields
    Object.assign(snapshot, updateDto);
    
    // Save to trigger pre-save hooks
    const updatedSnapshot = await snapshot.save();
    return updatedSnapshot;
  }

  async deleteAgentSnapshot(snapshotId: string) {
    const snapshot = await this.agentSnapshotsModel.findById(snapshotId);
    if (!snapshot) {
      throw new NotFoundException(`Snapshot with ID ${snapshotId} not found`);
    }

    return this.agentSnapshotsModel.findByIdAndDelete(snapshotId).exec();
  }

  async checkUniqueName(name: string) {
    const snapshot = await this.agentSnapshotsModel.findOne({ name });
    return {
      uniqueNameExists: !snapshot,
      snapshotId: snapshot?._id,
    };
  }

  async createSnapshotFromAgent(body: CreateSnapshotFromAgentDto) {
    try {
      const { agentId, orgId, name, description = '' } = body;
      const agent = await this.mongoAgentService.getAgent({ _id: agentId });
      if (!agent) {
        throw new NotFoundException(`Agent with ID ${agentId} not found`);
      }

      // Create snapshot with essential agent data
      const snapshotData: CreateAgentSnapshotDto = {
        name,
        parentAgentId: agentId,
        description,
        orgId,
        ...this.mapAgentToSnapshotData(agent),
      };

      return this.createAgentSnapshot(agent.orgId, snapshotData);
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  async createAgentFromSnapshot(body: CreateAgentFromSnapshotDTO, orgId: string) {
    const { agentName, snapshotId, folderId, propertiesToSync } = body;

    // Find the snapshot
    const snapshot = await this.agentSnapshotsModel.findById(snapshotId).lean().exec();
    if (!snapshot) {
      throw new NotFoundException(`Snapshot with ID ${snapshotId} not found`);
    }

    const snapshotData = this.mapSnapshotToAgentData(snapshot);

    // Create agent data following CreateAgentDto structure
    const agentData: CreateAgentDto = {
      agentName,
      orgId: orgId,
      snapshotId,
      // Set default aiProvider
      aiProvider: {
        companyId: PROVIDERS.OPENAI_HOSTED,
        modelName: CUSTOM_LLM_7.MODEL,
        accountName: CUSTOM_LLM_7.PUBLIC_MODEL_NAME,
        accountId: CUSTOM_LLM_7.NAME,
        isAdvancedSettings: false,
        advancedSettings: {
          temperature: 0.3,
          maxLength: 1000,
          frequencyPenalty: 0,
          optimize: OPTIMIZEOPTIONS.ACCURACY,
        },
      },
      // Always include required properties
      prompts: snapshotData.prompts,
      actions: snapshotData.actions,
      // If propertiesToSync is provided, only include those optional properties
      ...(propertiesToSync
        ? Object.fromEntries(
          Object.entries(snapshotData)
            .filter(([key]) => propertiesToSync.includes(key as any))
            // Exclude prompts and actions as they're already included above
            .filter(([key]) => !['prompts', 'actions'].includes(key)),
        )
        : snapshotData),
      // Set optional configurations
      disabled: true, // Set disabled to true for new agents
      faqs: [], // Initialize empty FAQs array
      // Ensure all triggers start as inactive
      triggers: snapshotData.triggers?.map((trigger) => ({
        ...trigger,
        active: false,
      })),
    };

    const newAgent = await this.mongoAgentService.createAgent('', agentData);
    //Push new agent to org
    await this.mongoOrganizationService.updateOrganization(
      { _id: orgId },
      { $push: { agents: newAgent._id } },
    );
    //Add the agent to the folder
    await this.foldersService.addResourceToFolder({
      resourceId: newAgent._id?.toString(),
      type: 'agents',
      folderId: folderId,
      organization: orgId,
    });

    return newAgent;
  }

  async syncAgentsWithSnapshot(
    snapshotId: string,
    agentIds: string[],
    propertiesToSync: string[],
  ) {
    const snapshot = await this.agentSnapshotsModel.findById(snapshotId);
    if (!snapshot) {
      throw new NotFoundException(`Snapshot with ID ${snapshotId} not found`);
    }

    // Find all specified agents that use this snapshot
    const agents = await this.mongoAgentService.getAgents({
      _id: { $in: agentIds },
      snapshotId,
    });

    if (!agents || agents.length === 0) {
      return {
        message: 'No matching agents found for the provided IDs and snapshot',
      };
    }

    // Update each agent with snapshot data while preserving their account details
    const updatePromises = agents.map(async (agent) => {
      // Start with basic required fields
      const updatedAgentData: any = {
        agentName: agent.agentName,
        orgId: agent.orgId,
        snapshotId: agent.snapshotId,
      };

      // Only map the properties that were specified to be synced
      const snapshotData = this.mapSnapshotToAgentData(snapshot, agent);
      propertiesToSync.forEach((property) => {
        updatedAgentData[property] = snapshotData[property];
      });

      return this.mongoAgentService.updateAgent(
        { _id: agent._id },
        updatedAgentData,
      );
    });

    // Wait for all updates to complete
    const results = await Promise.all(updatePromises);
    return {
      message: `Successfully synced ${results.length} agents with snapshot`,
      updatedAgents: results,
    };
  }

  async linkExistingAgentToSnapshot(body: LinkExistingAgentToSnapshotDto) {
    const { agentId, snapshotId } = body;
    const snapshot = await this.agentSnapshotsModel.findById(snapshotId);
    if (!snapshot) {
      throw new NotFoundException(`Snapshot with ID ${snapshotId} not found`);
    }

    const agent = await this.mongoAgentService.updateAgent(
      { _id: agentId },
      { snapshotId },
    );
    if (!agent) {
      throw new NotFoundException(`Agent with ID ${agentId} not found`);
    }

    return agent;
  }

  async syncSnapshotWithParentAgent(snapshotId: string) {
    const snapshot = await this.agentSnapshotsModel.findById(snapshotId);
    if (!snapshot) {
      throw new NotFoundException(`Snapshot with ID ${snapshotId} not found`);
    }
    const { parentAgentId } = snapshot;
    if (!parentAgentId) {
      throw new BadRequestException('Snapshot does not have a parent agent');
    }
    const parentAgent = await this.mongoAgentService.getAgent({
      _id: parentAgentId,
    });
    if (!parentAgent) {
      throw new NotFoundException(
        `Parent agent with ID ${parentAgentId} not found`,
      );
    }

    // Create updated snapshot data from parent agent
    const updatedSnapshotData: UpdateAgentSnapshotDto = {
      // Preserve required fields
      orgId: snapshot.orgId,
      name: snapshot.name,
      description: snapshot.description,
      parentAgentId: snapshot.parentAgentId,
      ...this.mapAgentToSnapshotData(parentAgent, snapshot),
    };

    // Update the snapshot with new data
    return this.updateAgentSnapshot(snapshotId, updatedSnapshotData);
  }

  async getAgentsBySnapshotId(snapshotId: string) {
    return this.mongoAgentService.getAgents({ snapshotId });
  }

  async getSnapshotsByAgentId(agentId: string) {
    return this.agentSnapshotsModel.find({ parentAgentId: agentId }).exec();
  }

  // New sharing methods
  async shareSnapshot(shareDto: ShareSnapshotDto) {
    const { snapshotId, organizationIds, sharedBy } = shareDto;

    // Find the snapshot
    const snapshot = await this.agentSnapshotsModel.findById(snapshotId);
    if (!snapshot) {
      throw new NotFoundException(`Snapshot with ID ${snapshotId} not found`);
    }

    // Get existing shared orgs to avoid duplicates
    const existingOrgIds = snapshot.sharedWith.map((share) => share.orgId);

    // Filter out orgs that are already shared with or are the owner
    const newOrgIds = organizationIds.filter(
      (orgId) => !existingOrgIds.includes(orgId) && orgId !== snapshot.orgId,
    );

    // Prepare update operations
    const updateOperations: any = { isShared: true };

    // Only add to sharedWith if there are new orgs to share with
    if (newOrgIds.length > 0) {
      updateOperations.$push = {
        sharedWith: {
          $each: newOrgIds.map((orgId) => ({
            orgId,
            sharedBy,
            sharedAt: new Date(),
            acceptedSnapshotInvite: false,
          })),
        },
      };
    }

    // Update the document
    const updatedSnapshot = await this.agentSnapshotsModel.findByIdAndUpdate(
      snapshotId,
      updateOperations,
      { new: true },
    );

    return updatedSnapshot;
  }

  async generateShareLink(generateLinkDto: GenerateShareLinkDto) {
    const { snapshotId, expiryDate } = generateLinkDto;

    // Prepare update object
    const updateObj: any = {
      isShared: true,
    };

    // Find current value to check if we need to generate a new link
    const snapshot = await this.agentSnapshotsModel.findById(snapshotId);
    if (!snapshot) {
      throw new NotFoundException(`Snapshot with ID ${snapshotId} not found`);
    }

    // Generate a unique link if not already present
    if (!snapshot.shareLink) {
      updateObj.shareLink = uuidv4();
    }

    // Set expiry date if provided
    if (expiryDate) {
      updateObj.shareLinkExpiry = expiryDate;
    }

    // Update the document
    const updatedSnapshot = await this.agentSnapshotsModel.findByIdAndUpdate(
      snapshotId,
      updateObj,
      { new: true },
    );

    if (!updatedSnapshot) {
      throw new NotFoundException(`Snapshot with ID ${snapshotId} not found`);
    }

    return {
      shareLink: updatedSnapshot.shareLink,
      expiry: updatedSnapshot.shareLinkExpiry,
    };
  }

  async setSnapshotPublic(setPublicDto: SetSnapshotPublicDto) {
    const { snapshotId, isPublic } = setPublicDto;

    // Find the snapshot first to check current state
    const snapshot = await this.agentSnapshotsModel.findById(snapshotId);
    if (!snapshot) {
      throw new NotFoundException(`Snapshot with ID ${snapshotId} not found`);
    }

    // Prepare update object
    const updateObj: any = { isPublic };

    // If setting to public, ensure isShared is also set
    if (isPublic) {
      updateObj.isShared = true;
    }

    // If removing public status and no other sharing, update isShared
    if (!isPublic && snapshot.sharedWith.length === 0 && !snapshot.shareLink) {
      updateObj.isShared = false;
    }

    // Update the document
    const updatedSnapshot = await this.agentSnapshotsModel.findByIdAndUpdate(
      snapshotId,
      updateObj,
      { new: true },
    );

    return updatedSnapshot;
  }

  async removeSharing(snapshotId: string, orgId: string) {
    // Find the snapshot first to check if we need to update isShared
    const snapshot = await this.agentSnapshotsModel.findById(snapshotId);
    if (!snapshot) {
      throw new NotFoundException(`Snapshot with ID ${snapshotId} not found`);
    }

    // Remove the organization from sharedWith using pull operator
    const updateObj: any = {
      $pull: { sharedWith: { orgId } },
    };

    // Update isShared flag if necessary (this will be the last sharing and it's not public)
    if (
      snapshot.sharedWith.length === 1 &&
      snapshot.sharedWith[0].orgId === orgId &&
      !snapshot.isPublic
    ) {
      updateObj.isShared = false;
    }

    // Update the document
    const updatedSnapshot = await this.agentSnapshotsModel.findByIdAndUpdate(
      snapshotId,
      updateObj,
      { new: true },
    );

    return updatedSnapshot;
  }

  async removePublicSharing(snapshotId: string) {
    // Find the snapshot first to check if we need to update isShared
    const snapshot = await this.agentSnapshotsModel.findById(snapshotId);
    if (!snapshot) {
      throw new NotFoundException(`Snapshot with ID ${snapshotId} not found`);
    }

    // Prepare update object to remove public sharing
    const updateObj: any = {
      isPublic: false,
      $unset: { shareLink: '', shareLinkExpiry: '' },
    };

    // Update isShared flag if no more sharing
    if (snapshot.sharedWith.length === 0) {
      updateObj.isShared = false;
    }

    // Update the document
    const updatedSnapshot = await this.agentSnapshotsModel.findByIdAndUpdate(
      snapshotId,
      updateObj,
      { new: true },
    );

    return updatedSnapshot;
  }

  // Helper method to check if a user has access to a snapshot
  async checkSnapshotAccess(
    snapshotId: string,
    orgId: string,
  ): Promise<boolean> {
    const snapshot = await this.agentSnapshotsModel.findById(snapshotId);
    if (!snapshot) {
      return false;
    }

    // Access is granted if:
    // 1. The user's organization owns the snapshot
    // 2. The snapshot is shared with the user's organization
    // 3. The snapshot is public
    return (
      snapshot.orgId === orgId ||
      snapshot.sharedWith.some((share) => share.orgId === orgId) ||
      snapshot.isPublic
    );
  }

  // Get a snapshot by its share link
  async getSnapshotByShareLink(shareLink: string, orgId: string) {
    const snapshot = await this.agentSnapshotsModel.findOne({
      shareLink,
    });

    if (!snapshot) {
      throw new Error('Snapshot not found or link expired');
    }

    // Check if the share link has expired
    if (snapshot.shareLinkExpiry && new Date() > snapshot.shareLinkExpiry) {
      throw new Error('Share link has expired');
    }

    const sharedWithOrg = snapshot.sharedWith.find(
      (share) => share.orgId === orgId,
    );


    let requestStatus: 'accepted' | 'notAccepted' | 'notFound';

    if (!sharedWithOrg && !snapshot.isPublic) {

      requestStatus = 'notFound';
    } else {
      requestStatus = sharedWithOrg?.acceptedSnapshotInvite
        ? 'accepted'
        : 'notAccepted';
    }

    return { snapshot, requestStatus };
  }

  async handleInvite(handleInviteDto: HandleInviteDto) {
    const { snapshotId, orgId, action } = handleInviteDto;

    // Validate snapshot and share link
    const snapshot = await this.agentSnapshotsModel.findOne({
      _id: snapshotId,
    });

    if (!snapshot) {
      throw new Error('Invalid snapshot or share link');
    }

    // Check if share link has expired
    if (snapshot.shareLinkExpiry && new Date() > snapshot.shareLinkExpiry) {
      throw new Error('Share link has expired');
    }

    if (action === 'accept') {
      // Check if this org is already in sharedWith
      const existingShare = snapshot.sharedWith.find(
        (share) => share.orgId === orgId,
      );

      if (existingShare) {
        // Update existing share entry
        await this.agentSnapshotsModel.updateOne(
          { _id: snapshotId, 'sharedWith.orgId': orgId },
          {
            $set: {
              'sharedWith.$.acceptedSnapshotInvite': true,
            },
          },
        );
      } else if (snapshot.isPublic) {
        // For public snapshots, create a new sharedWith entry
        await this.agentSnapshotsModel.updateOne(
          { _id: snapshotId },
          {
            $addToSet: {
              // Using $addToSet to prevent duplicates
              sharedWith: {
                orgId,
                sharedBy: 'public', // Indicating this was from a public share
                sharedAt: new Date(),
                acceptedSnapshotInvite: true,
              },
            },
          },
        );
      }
    } else {
      // If rejecting, remove the entry from sharedWith
      await this.agentSnapshotsModel.updateOne(
        { _id: snapshotId },
        {
          $pull: {
            sharedWith: { orgId },
          },
        },
      );
    }

    // Get the updated snapshot
    const updatedSnapshot = await this.agentSnapshotsModel.findById(snapshotId);

    return {
      success: true,
      message: `Snapshot invite ${action}ed`,
      snapshot: updatedSnapshot,
    };
  }
}
