import { <PERSON>du<PERSON> } from '@nestjs/common';
import { AgentSnapshotsService } from './agent-snapshots.service';
import { AgentSnapshotsController } from './agent-snapshots.controller';
import { MongooseModule } from '@nestjs/mongoose';
import { AuthModule } from 'src/auth/auth.module';
import { TokensModule } from 'src/tokens/tokens.module';
import {
  AgentSnapshots,
  AgentSnapshotsSchema,
} from 'src/mongo/schemas/agent-snapshots/agent-snapshots.schema';
import { FoldersModule } from 'src/folders/folders.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: AgentSnapshots.name, schema: AgentSnapshotsSchema },
    ]),
    AuthModule,
    TokensModule,
    FoldersModule,
  ],
  providers: [AgentSnapshotsService],
  controllers: [AgentSnapshotsController],
  exports: [AgentSnapshotsService],
})
export class AgentSnapshotsModule {}
