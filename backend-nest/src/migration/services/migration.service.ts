import { Injectable, Inject, forwardRef } from '@nestjs/common';
import { ObjectId } from 'mongodb';
import { AgentService } from 'src/agent/agent.service';
import { GhlApisService } from 'src/api-client/services/ghl-apis/ghl-apis.service';
import { V2migrationService } from 'src/api-client/services/v2migration/v2migration.service';
// import { CredentialsService } from 'src/credentials/credentials.service';
import { TaskService } from 'src/gcp/services/task.service';
import { KINDS, PROVIDERS, modelMaps } from 'src/lib/constant';
import { GhlToken } from 'src/lib/global-interfaces';
import { removeImportedSessionPrefix } from 'src/lib/utils';
import { MyLogger } from 'src/logger/logger.service';
// import { CreateConversationDto } from 'src/mongo/dto/conversations/create.dto';
import { MongoCredentialsService } from 'src/mongo/service/credentials/mongo-credentials.service';
import { ICheckLocationRequest, ICheckLocationResponse } from 'src/organization/dto/v2migration.dto';
import { AiProviderService } from 'src/organization/services/aiProviders/aiProvider.service';
import { IntegrationService } from 'src/organization/services/integrations/integration.service';
import { SheetService } from 'src/organization/services/integrations/sheet/sheet.service';
import { OrganizationService } from 'src/organization/services/organization.service';
import { SessionService } from 'src/session/session.service';
// import { UserService } from 'src/user/user.service';
import { PineconeService } from 'src/vector/services/pinecone/pinecone.service';
import { v4 as uuidv4 } from 'uuid';
// import { locres } from './reesponse';

@Injectable()
export class MigrationService {
    constructor(
        private readonly v2migrationApiService: V2migrationService,
        private readonly agentService: AgentService,
        private readonly sheetService: SheetService,
        private readonly itegrationService: IntegrationService,
        private readonly pineconeService: PineconeService,
        private readonly aiProviderService: AiProviderService,
        private readonly taskService: TaskService,
        private readonly ghlapiService: GhlApisService,
        private readonly mongocredentialService: MongoCredentialsService,
        private readonly organizationService: OrganizationService,
        private readonly sessionService: SessionService,
        private readonly logger: MyLogger,
        // private readonly userService: UserService,
    ) { }

    async checkLocationInV2(body: ICheckLocationRequest): Promise<ICheckLocationResponse> {
        try {
            const response = await this.v2migrationApiService.checkLocation(body);
            if (response?.status === 'success') {
                return response;
            } else {
                throw new Error(response.message || 'V2 failed to return a successful response')
            }
        } catch (err) {
            this.logger.error({ message: err?.message, context: 'MigrationService.checkLocationInV2' });
            return {
                status: 'error',
                message: err?.message || "server error",
            }
        }
    }


    async migrateLocationToV2(orgId: string, locationId: string, userId?: string, reqId?: string) {
        try {
            this.logger.log({ message: `migration | reqId ${reqId} | locationId ${locationId} | orgId ${orgId} | user ${userId}`, context: 'MigrationService.migrateLocationToV2' })
            const response = await this.v2migrationApiService.migrateLocation(locationId);
            if (response.status == 'error') throw new Error(response.message || "V2 failed to return a successful response");

            let { prompt, openAi_key, model_type, calendar_id=undefined, googleSheet=undefined, name, sessions=undefined } = response.location;

            // create ghl calendar connection
            let calendarAccountId, calendarName;
            try {
                // const user = await this.userService.findUserBySessionId(usid);
                // var userId = (user as any)._id.toString();

                const cred = await this.mongocredentialService.getCredential({
                    keyId: locationId,
                    kind: KINDS.GHL_CREDENTIAL,
                })
                const tokens: GhlToken = {
                    access_token: cred?.creds?.accessToken,
                    refresh_token: cred?.creds?.refreshToken,
                }
                const credentialId = cred?._id.toString();
                if (!(tokens?.access_token)) throw new Error('No tokens found');
                var { location } = await this.ghlapiService.getGhlLocation(tokens, locationId);
                if (!calendar_id) throw new Error("No calendar Id provided");
                const calendar = await this.ghlapiService.getGhlCalendars(tokens, locationId, calendar_id);
                calendarName = calendar?.calendar?.name || calendar?.name;
                if (!calendar) throw new Error(`No GHL calendar found for the calendar ID ${calendar_id}`);
                calendarAccountId = uuidv4();

                // check if the ghl calendar already exists
                const existing = await this.organizationService.getOrganization({
                    _id: orgId,
                    'connections.dataSources.calendarId': calendar_id,
                },{
                    'connections.dataSources.$': 1,
                });

                let calendarCredentialId='';

                if (existing) {
                    calendarAccountId = existing?.connections?.dataSources[0]?.accountId;
                    this.logger.log({ message: `reqId ${reqId} | migration | calendar already exists | calendarId ${calendar_id} | accountId ${calendarAccountId}`, context: 'MigrationService.migrateLocationToV2' });
                    calendarCredentialId = existing.connections?.dataSources?.[0]?.credentialId;
                    throw new Error("GHL Calendar already exists");
                } else {
                    calendarCredentialId = (
                      await this.mongocredentialService.createCredentials({
                        kind: 'GhlCredential',
                        keyId: locationId,
                        creds: {
                          accessToken: cred?.creds?.accessToken,
                          refreshToken: cred?.creds?.refreshToken,
                        },
                        userId: userId,
                        organizationId: orgId,
                      })
                    ).id;
                }



                await this.organizationService.updateOrganisation(
                  { _id: orgId },
                  {
                    $push: {
                      'connections.dataSources': {
                        credentialId: calendarCredentialId,
                        providerName: PROVIDERS.GHL_CALENDAR,
                        accountId: calendarAccountId,
                        author: location?.name,
                        userId,
                        keyId: locationId,
                        timezone: location?.timezone,
                        calendarId: calendar_id,
                        name: calendar?.calendar?.name || calendar?.name,
                      },
                    },
                  },
                );
            } catch (err) {
                this.logger.error({ message: err?.message, context: 'MigrationService.migrateLocationToV2' });
                // throw new Error(err?.message || 'Error creating GHL calendar connection');
            }

            // create ai provider
            const aiConn: any = await this.aiProviderService.createAiProvider({
                organizationId: orgId,
                kind: 'OpenaiCredential',
                name: `OpenAI API key(v2): ${location?.name || locationId}`,
                type: 'OpenaiCredential',
                userId,
                creds: {
                    secret: openAi_key,
                }
            })
            // create agents from prompt, openAi_key, model_type
            let agent;
            let sheetAccId;
            let sheetName;
            // create sheet connection in organization using sheetId, orgId, headers, userName
            try {
                if (googleSheet) {
                    const conn = await this.itegrationService.connectSheet({
                        sheetId: googleSheet,
                        orgId,
                        userName: name,
                        headers: "A1:B1",
                    }, undefined, userId);
                    sheetName = conn?.name;
                } else {
                    this.logger.log({
                        message: `reqId ${reqId} | migration | no google sheet found | locationId ${locationId} | orgId ${orgId}`,
                        context: 'MigrationService.migrateLocationToV2'
                    })
                }
            } catch (err) {
                this.logger.error({ message: err?.message, context: 'MigrationService.migrateLocationToV2' });
            }

            let uuid = uuidv4();
            let agentData = {
                agentName: `${location?.name || locationId}`,
                aiProvider: {},
                prompts: {
                    currentActive: uuid,
                    prompt: [{
                        promptId: uuid,
                        name: 'main prompt',
                        promptContent: `I want you to act as a virtual assistant for a business. As the assistant, it is your responsibility to follow guidance provided to you by the business you are representing, and only answer questions asked directly by the contact. You should only answer questions that are relevant to the business or offerings that the business has. If the contact asks you any questions or says anything that is not relevant to the business, you should respond with "Sorry, I don\'t think I\'m the best person to answer that for you".\nThe business you represent has provided the following additional information:\n\n` + prompt,
                    }]
                },
                orgId,
                actions: [],
                disabled: true
            }
            if (aiConn.accountId) {
                agentData['aiProvider'] = {
                    accountId: aiConn.accountId,
                    accountName: 'OpenAI API key(v2)',
                    companyId: 'openai',
                    modelName: modelMaps[model_type] || 'gpt-3.5-turbo',
                }
            }
            if (calendar_id) {
                agentData['actions'].push({
                    actionId: uuidv4(),
                    accountId: calendarAccountId,
                    accountName: calendarName,
                    providerName: PROVIDERS.GHL_CALENDAR,
                    promptContent: `Use this calendar to read all the appointment and date related information.`,
                    activity: 'read',
                })
            }
            if (googleSheet) {
                agentData['actions'].push({
                    actionId: uuidv4(),
                    accountId: sheetAccId,
                    accountName: sheetName,
                    providerName: PROVIDERS.GOOGLE_SHEET,
                    promptContent: `Use this sheet as a reference for all the infos related to the service`,
                    activity: 'read',
                })
            }
            agent = await this.agentService.createAgent(userId, agentData)
            // this.logger.log(agent.message);

            // save sessions
            if (sessions?.length) {
                sessions = await Promise.all(sessions.map(async session => {
                    if (!(session.id)) {
                        session.id = uuidv4();
                    }
                    session.metadata.content = session.metadata.prompt;
                    delete session.metadata.prompt;
                    session.metadata.type = "session";
                    session.metadata.agentId = (agent._id || agent.id || "").toString();


                    const createSessionDto = {
                        orgId,
                        agentId: (agent._id || new ObjectId()).toString(),
                        userId: userId,
                        saved: true,
                    }
                    const newSession: any = await this.sessionService.createSession(createSessionDto);

                    // save session to mongo
                    session.id = (newSession?._id || newSession?.id || new ObjectId()).toString();

                    let messages = removeImportedSessionPrefix(session.metadata.content || "");
                    let events = [];
                    messages.forEach((message) => {
                        const [human, bot] = message;
                        events.push({
                            eventId: uuidv4(),
                            sender: 'human',
                            message: human,
                            botResponse: [
                                {
                                    eventId: uuidv4(),
                                    sender: 'bot',
                                    kind: 'message',
                                    action: 'read',
                                    eventData: {
                                        message: bot,
                                    },
                                    timestamp: new Date().getTime(),
                                }
                            ],
                            timestamp: new Date().getTime(),
                        })
                    });

                    await this.sessionService.saveMigratedSession(session.id, events);
                    return session;
                }));

                // save the sessions content as events
                const namespace = orgId;
                const indexName = await this.pineconeService.createIndex(process.env.PINECONE_INDEX);
                const index = await this.pineconeService.connectIndex(indexName);
                const result = await this.pineconeService.upsertVectors(index, sessions, namespace);
                // this.logger.log(`reqId ${reqId} | migration :`, { result });
                this.logger.log({ message: `reqId ${reqId} | migration :`, context: 'MigrationService.migrateLocationToV2' });
            } else {
                this.logger.log({message:`reqId ${reqId} | migration | no sessions received | location ${locationId} | org ${orgId}`, context: 'MigrationService.migrateLocationToV2'});   
            }
            this.logger.log({message:`reqId ${reqId} | migration successfull | locationId ${locationId} | orgId ${orgId}`, context: 'MigrationService.migrateLocationToV2'})
            await this.v2migrationApiService.migrateFinish(locationId, orgId);
        } catch (err) {
            this.logger.error({ message: err?.message, context: 'MigrationService.migrateLocationToV2' });
        }
    }

    /**
     * Schedule migration tasks at an interval of 2 minutes
     */
    async scheduleMigration(orgId: string, locationIds: string[], userId: string) {
        try {
            const queue = "v3-migration-tasks";
            const project = "dwy-master";
            const location = "us-central1";
            let timeInSeconds;
            for (let i = 0; i < locationIds.length; i++) {
                const locationId = locationIds[i];
                timeInSeconds = (3 * (i + 1));
                await this.taskService.createHttpTask(project, location, queue, `${process.env.BACKEND_URL}/migration/location/${orgId}`, { locationId, userId }, timeInSeconds)
            }
            this.logger.log({message: `migration | task scheduled | orgId ${orgId} | locationIds : ${ JSON.stringify(locationIds)}`, context: 'MigrationService.scheduleMigration'});
        } catch (err) {
            this.logger.error({ message: err?.message, context: 'MigrationService.scheduleMigration' });
        }
    }
}
