import { Module, forwardRef } from '@nestjs/common';
import { MigrationService } from './services/migration.service';
import { MigrationController } from './controllers/migration/migration.controller';
import { AgentModule } from 'src/agent/agent.module';
import { AgentService } from 'src/agent/agent.service';
import { OrganizationModule } from 'src/organization/organization.module';
import { SheetService } from 'src/organization/services/integrations/sheet/sheet.service';
import { OrganizationService } from 'src/organization/services/organization.service';
import { IntegrationService } from 'src/organization/services/integrations/integration.service';
import { VectorModule } from 'src/vector/vector.module';
import { PineconeService } from 'src/vector/services/pinecone/pinecone.service';
import { AiProviderService } from 'src/organization/services/aiProviders/aiProvider.service';
import { CredentialsModule } from 'src/credentials/credentials.module';
import { CredentialsService } from 'src/credentials/credentials.service';
import { SessionModule } from 'src/session/session.module';
import { UserModule } from 'src/user/user.module';
import { LoggerModule } from 'src/logger/logger.module';

@Module({
    imports: [
        forwardRef(() => AgentModule),
        forwardRef(() => OrganizationModule),
        VectorModule,
        CredentialsModule,
        SessionModule,
        UserModule,
        LoggerModule,
    ],
    controllers: [
        MigrationController
    ],
    providers: [
        MigrationService,
        PineconeService,
        CredentialsService,
    ],
    exports: [
        MigrationService,
    ]
})
export class MigrationModule { }
