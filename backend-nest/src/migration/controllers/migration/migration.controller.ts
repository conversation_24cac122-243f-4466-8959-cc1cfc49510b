import { Controller, Get, Post, Req, <PERSON>s, Body, HttpStatus, Render, Delete, Param, Query, UseGuards } from '@nestjs/common';
import { Request, Response } from 'express';
import { ApiOperation, ApiQuery, ApiNotFoundResponse, ApiOkResponse, ApiParam } from "@nestjs/swagger";
import { MigrationService } from 'src/migration/services/migration.service';
import { IMigrateLocationsTask } from 'src/migration/dto/post.dto';
import { ICheckLocationRequest } from 'src/organization/dto/v2migration.dto';
import { handleException } from 'helpers/handleException';
import { RequestWithUser } from 'src/auth/auth.interface';
import { MyLogger } from 'src/logger/logger.service';

@Controller('migration')
export class MigrationController {
    constructor(
        private readonly migrationService: MigrationService,
        private readonly logger: MyLogger,
    ) { }

    @ApiOperation({ summary: 'Check if location is in V2' })
    @Post('check')
    async checkV2Location(@Body() body: ICheckLocationRequest, @Res() res: Response) {
        try {
            const response = await this.migrationService.checkLocationInV2(body);
            if (response.status === "error") {
                throw new Error(response.message || "V2 failed to return a successful response");
            }
            res.status(HttpStatus.OK).send(response);
        } catch (err) {
            this.logger.error({ message: err?.message, context: 'MigrationController.checkV2Location'});
            res.status(HttpStatus.INTERNAL_SERVER_ERROR).send(err?.message);
        }
    }

    @ApiOperation({ summary: 'Schedule the locations for migration' })
    @Post('schedule')
    async startMigrationTask(@Req() req: RequestWithUser, @Body() body: IMigrateLocationsTask, @Res() res: Response) {
        try {
            await this.migrationService.scheduleMigration(body.orgId, body.locationIds, body.userId);
            res.sendStatus(200);
        } catch (err) {
            this.logger.error({ message: err?.message, context: 'MigrationController.startMigrationTask'});
            handleException(res, err);
        }
    }

    @ApiOperation({ summary: 'Migrate location' })
    @Post('location/:orgId')
    async migrateLocation(@Req() req: RequestWithUser, @Param('orgId') orgId: string, @Body('locationId') locationId: string, @Body('userId') userId?: string, @Res() res?: Response) {
        res.sendStatus(HttpStatus.OK);
        this.logger.log({message:`@info migration started | reqId ${req.uniqueCode} | locationId: ${locationId} | orgId: ${orgId} `, context: 'MigrationController.migrateLocation'});
        try {
            if (!userId) this.logger.log({message:`reqId ${req.uniqueCode} | migration | userId not found | locationId ${locationId} | orgId ${orgId}`, context: 'MigrationController.migrateLocation'});
            
            await this.migrationService.migrateLocationToV2(orgId, locationId, userId, req.uniqueCode);
        } catch (err) {
            this.logger.error({ message: err?.message, context: 'MigrationController.migrateLocation'});
        }
    }
}
