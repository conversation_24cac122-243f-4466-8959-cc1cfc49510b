import { HttpException, HttpStatus, Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { CreateAiModelDto } from './dto/create-ai-model.dto';
import { UpdateAiModelDto } from './dto/update-ai-model.dto';
import { AiModel, AiModelDocument } from './ai-models.schema';
import axios from 'axios';

@Injectable()
export class AiModelsService {
  constructor(
    @InjectModel(AiModel.name) private aiModelModel: Model<AiModelDocument>
  ) {}

  async create(createAiModelDto: CreateAiModelDto): Promise<AiModel> {
    createAiModelDto.companyLogo = this.getCompanyLogo(createAiModelDto.provider);
    const createdModel = new this.aiModelModel(createAiModelDto);
    return createdModel.save();
  }

  async findAll(): Promise<AiModel[]> {
    return this.aiModelModel.find().exec();
  }

  async findOne(id?: string, companyId?: string, modelName?: string): Promise<AiModel> {
    if (!id && !(companyId && modelName)) {
      throw new HttpException(
        'Either id or both companyId and modelName must be provided',
        HttpStatus.BAD_REQUEST
      );
    }

    let model: AiModelDocument | null;

    if (id) {
      model = await this.aiModelModel.findById(id).lean().exec();
    } else {
      model = await this.aiModelModel.findOne({ 
        provider: companyId, 
        model: modelName 
      }).lean().exec();
    }

    if (!model) {
      const errorMessage = id 
        ? `AI Model with ID ${id} not found`
        : `AI Model with companyId ${companyId} and modelName ${modelName} not found`;
      throw new NotFoundException(errorMessage);
    }

    return model;
  }

  async update(id: string, updateAiModelDto: UpdateAiModelDto): Promise<AiModel> {
    if (updateAiModelDto.provider) {
      updateAiModelDto.companyLogo = this.getCompanyLogo(updateAiModelDto.provider);
    }
    const updatedModel = await this.aiModelModel
      .findByIdAndUpdate(id, updateAiModelDto, { new: true })
      .exec();
    
    if (!updatedModel) {
      throw new NotFoundException(`AI Model with ID ${id} not found`);
    }
    return updatedModel;
  }

  async remove(id: string): Promise<AiModel> {
    const deletedModel = await this.aiModelModel.findByIdAndDelete(id).exec();
    if (!deletedModel) {
      throw new NotFoundException(`AI Model with ID ${id} not found`);
    }
    return deletedModel;
  }

  async fetchAvailableModels() {
    try {
      const response = await axios.get('https://openrouter.ai/api/v1/models', {
        headers: {
          'Authorization': `Bearer ${process.env.OPENROUTER_API_KEY}`,
          'Content-Type': 'application/json'
        }
      });

      return response.data;
    } catch (error) {
      throw new HttpException(
        error.response?.data?.message || 'Failed to fetch AI models',
        error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  private getCompanyLogo(provider: string): string {
    switch (provider.toLowerCase()) {
      case 'fireworks':
        return '/capriLogo.png';
      case 'openai-hosted':
        return '/fireworksLogo.png';  
      case 'openai':
        return '/openai.png';
      case 'anthropic':
        return '/anthropic.png';
      case 'groq':
        return '/groq.png';
      case 'gemini':
        return '/geminiLogo.png';
      case 'openrouter':
        return '/capriLogo.png';;  
      default:
        return '/defaultLogo.png'; 
    }
  }
}
