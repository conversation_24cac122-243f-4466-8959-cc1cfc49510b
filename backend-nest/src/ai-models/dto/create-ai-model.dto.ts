import { Is<PERSON><PERSON>, <PERSON><PERSON><PERSON>ber, IsNotEmpty, Min, <PERSON><PERSON><PERSON>al, IsBoolean } from 'class-validator';

export class CreateAiModelDto {
    @IsString()
    @IsNotEmpty()
    name: string;
  
    @IsString()
    @IsNotEmpty()
    provider: string;

  
    @IsString()
    @IsNotEmpty()
    model: string;
  
    @IsNumber()
    @IsOptional()
    @Min(0)
    costPerMillion: number;


    @IsString()
    @IsOptional()
    companyLogo: string;

    @IsBoolean()
    @IsOptional()
    isCapriHosted: boolean;
}
