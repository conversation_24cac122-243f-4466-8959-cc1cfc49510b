import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export type AiModelDocument = AiModel & Document;

@Schema({ timestamps: true })
export class AiModel {
  @Prop({ required: true })
  name: string;

  @Prop({ required: true })
  provider: string;

  @Prop({ required: true })
  model: string;

  @Prop({ required: false })
  costPerMillion?: number;

  @Prop({ required: true })
  companyLogo: string;

  @Prop({ required: false })
  isCapriHosted: boolean;
}

export const AiModelSchema = SchemaFactory.createForClass(AiModel);

AiModelSchema.index({ provider: 1 });

