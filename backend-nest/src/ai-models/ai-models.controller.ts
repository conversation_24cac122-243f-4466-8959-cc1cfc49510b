import { Controller, Get, Post, Body, Patch, Param, Delete, HttpStatus, Res } from '@nestjs/common';
import { AiModelsService } from './ai-models.service';
import { CreateAiModelDto } from './dto/create-ai-model.dto';
import { UpdateAiModelDto } from './dto/update-ai-model.dto';
import { handleException } from 'helpers/handleException';
import { Response } from 'express';

@Controller('ai-models')
export class AiModelsController {
  constructor(private readonly aiModelsService: AiModelsService) {}

  @Post()
  async create(@Body() createAiModelDto: CreateAiModelDto) {
    return this.aiModelsService.create(createAiModelDto);
  }

  @Get()
  async findAll(@Res() response: Response) {
    try {
      let models = await this.aiModelsService.findAll();
      return response.status(HttpStatus.OK).json(models);
    } catch (error) {
      handleException(response, error);
    }
  }

  @Get(':id')
  async findOne(@Param('id') id: string) {
    return this.aiModelsService.findOne(id);
  }

  @Patch(':id')
  async update(@Param('id') id: string, @Body() updateAiModelDto: UpdateAiModelDto) {
    return this.aiModelsService.update(id, updateAiModelDto);
  }

  @Delete(':id')
  async remove(@Param('id') id: string) {
    return this.aiModelsService.remove(id);
  }

  @Get('available/models/openrouter')
  async getAvailableModels() {
    return this.aiModelsService.fetchAvailableModels();
  }
}
