import { Module } from '@nestjs/common';
import { AiModelsService } from './ai-models.service';
import { AiModelsController } from './ai-models.controller';
import { AiModelSchema } from './ai-models.schema';
import { MongooseModule } from '@nestjs/mongoose';
import { AiModel } from './ai-models.schema';

@Module({
  imports: [
    MongooseModule.forFeature([{ name: AiModel.name, schema: AiModelSchema }])
  ],
  controllers: [AiModelsController],
  providers: [AiModelsService],
  exports: [AiModelsService]
})
export class AiModelsModule {}
