import { MessagingTypes } from "src/lib/constant";
import { HydratedCredentialDocument } from "src/mongo/schemas/credential/credentials.schema";

export type WebhookDirection = 'inbound' | 'outbound';

export type ProcessWebhookArgsMetadata = {
  provider: string;
  credential: HydratedCredentialDocument;
  direction: WebhookDirection;
  userEmail?: string;
  userName?: string;
  resourceId: string;
  conversationHistory: string;
  contact: {
    email?: string;
    name?: string;
    phone?: string;
    tags?: string[];
    id: string;
  };
  messageType: MessagingTypes;
};

export interface rolloverEventDto {
  reason: string;
  date: Date | string;
  rollover: boolean;
  contactId: string;
  responded?: boolean;
}