interface EmailObject {
    attachments: any[];
    bcc: any[];
    body: string;
    cc: any[];
    date: number;
    folders: string[];
    from: { email: string; name: string }[];
    grant_id: string;
    id: string;
    object: string;
    reply_to: any[];
    snippet: string;
    starred: boolean;
    subject: string;
    thread_id: string;
    to: { email: string; name: string }[];
    unread: boolean;
  }
  
  interface Data {
    application_id: string;
    object: EmailObject;
  }
  
  export interface NylasOutlookWebhooksDto {
    data: Data;
    id: string;
    source: string;
    specversion: string;
    time: number;
    type: string;
    webhook_delivery_attempt: number;
  }