import { Module } from '@nestjs/common';
import { V2WebhooksService } from './v2-webhooks.service';
import { V2WebhooksController } from './v2-webhooks.controller';
import { OrganizationModule } from 'src/organization/organization.module';
import { OutlookWebhookService } from './outlook/outlook-webhooks.service';
import { SessionModule } from 'src/session/session.module';

@Module({
  imports: [OrganizationModule, SessionModule],
  controllers: [V2WebhooksController],
  providers: [V2WebhooksService, OutlookWebhookService],
  exports: [V2WebhooksService],
})
export class WebhookV2Module {}
