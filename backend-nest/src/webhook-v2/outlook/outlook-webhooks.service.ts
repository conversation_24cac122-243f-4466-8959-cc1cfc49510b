/**
 * Contains the code to process the Outlook Outbound Webbook.
 */
import { MyLogger } from 'src/logger/logger.service';
import { Injectable } from '@nestjs/common';
import { KINDS, MessagingTypes, PROVIDERS } from 'src/lib/constant';
import { NylasOutlookWebhooksDto } from '../dto/v2-outlook-webhooks.dto';
import { MongoCredentialsService } from 'src/mongo/service/credentials/mongo-credentials.service';
import {
  ProcessWebhookArgsMetadata,
  WebhookDirection,
} from '../dto/v2-webhooks.dto';
import { V2WebhooksService } from '../v2-webhooks.service';

@Injectable()
export class OutlookWebhookService {
  constructor(
    private readonly logger: MyLogger,
    private readonly mongoCredentialService: MongoCredentialsService,
    private readonly v2WebhooksService: V2WebhooksService,
  ) {}

  /**
   * takes a payload from nylas and process it if it is an outbound webhook else it returns the values required for processing the inbound
   * @param orgId
   * @param payload payload specific to outlook
   * @returns the values required for processing the inbound
   */
  async processOutlookWebhook(
    orgId: string,
    payload: NylasOutlookWebhooksDto,
  ): Promise<ProcessWebhookArgsMetadata> {
    let credential = await this.mongoCredentialService.getCredential({
      kind: KINDS.OUTLOOK_NYLAS_CREDENTIAL,
      keyId: payload.data.object.grant_id,
    });

    if (!credential) {
      throw new Error(
        `No connection found for outlook grantId - ${payload.data.object.grant_id} in database`,
      );
    }

    // --- get required values ---

    let connectionEmail = credential.creds.email;
    let direction: WebhookDirection;

    let userEmailEntity = payload.data.object.to.find(
      (mail) => mail.email === connectionEmail,
    );

    let contactName, contactEmail;

    if (userEmailEntity) {
      direction = 'inbound';
      contactName = (payload.data.object?.to ?? [])?.[0].name;
      contactEmail = (payload.data.object?.to ?? [])?.[0].email;
    } else {
      direction = 'outbound';
      contactName = (payload.data.object?.from ?? [])?.[0]?.name;
      contactEmail = (payload.data.object?.from ?? [])?.[0]?.email;

      userEmailEntity = (payload.data.object?.to ?? []).find(
        (mail) => mail.email === connectionEmail,
      );
    }

    let resourceId = payload.data.object.grant_id;
    let contactId =
      direction === 'outbound'
        ? payload.data.object.to?.[0].email
        : payload.data.object.from?.[0].email;

    // --- ends ---

    if (direction === 'outbound') {
      // process outbound and return
      await this.v2WebhooksService.processOutbound({
        orgId,
        message: payload.data.object.body,
        channelName: connectionEmail,
        saveToken: true,
        messageType: '',
        agentId: '',
        tokensCount: 0,
        aiProviderName: '',
        resourceId: resourceId,
        direction: direction,
        contactId: PROVIDERS.OUTLOOK_NYLAS + '_' + contactId,
      });
      return;
    }

    // get conversation history
    let conversationHistory = await this.v2WebhooksService.getConversation({
      resourceId,
      contactId,
    });

    // return the required values for processing the inbound
    return {
      provider: PROVIDERS.OUTLOOK_NYLAS,
      conversationHistory,
      credential,
      direction,
      resourceId,
      userEmail: connectionEmail,
      userName: userEmailEntity?.name ?? '',
      contact: {
        email: contactEmail,
        name: contactName,
        phone: '',
        tags: [],
        id: PROVIDERS.OUTLOOK_NYLAS + '_' + contactId,
      },
      messageType: MessagingTypes.EMAIL,
    };
  }
}
