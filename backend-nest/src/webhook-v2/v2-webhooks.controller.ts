/**
 * This module contains the code to handle the webhook for HubSpot, Podium and Outlook and implements the processes that process the queries coming in from these channels and responds to them.
 * Note. GHL webhook is not implemented here. It is implemented inside ghl.controller.ts.
 */

import {
  Controller,
  Get,
  Post,
  Req,
  Res,
  Body,
  HttpStatus,
  Render,
  Delete,
  Param,
  Query,
  UseGuards,
} from '@nestjs/common';
import { Request, Response } from 'express';
import {
  ApiOperation,
  ApiQuery,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiParam,
} from '@nestjs/swagger';
import { RequestWithUser } from 'src/auth/auth.interface';
import { handleException } from 'helpers/handleException';
import { MyLogger } from 'src/logger/logger.service';
import { V2WebhooksService } from './v2-webhooks.service';
import { PROVIDERS } from 'src/lib/constant';
import { SessionService } from 'src/session/session.service';
import { OrganizationService } from '../organization/services/organization.service';
import { WebhookTriggers } from 'nylas';
import { OutlookWebhookService } from './outlook/outlook-webhooks.service';

@Controller('v2/webhooks')
export class V2WebhooksController {
  constructor(
    private readonly v2WebhooksService: V2WebhooksService,
    private readonly logger: MyLogger,
    private readonly outlookWebhookService: OutlookWebhookService,
  ) {}

  @Get(':provider/:orgId?')
  async validateWebhook(
    @Req() req: Request,
    @Query() query: any,
    @Res() res: Response,
  ) {
    // validate webhook
    try {
      if (query.challenge && query.provider === PROVIDERS.OUTLOOK_NYLAS) {
        this.logger.log({
          message: `webhook validation | provider ${
            query.provider
          } | Payload: ${JSON.stringify(query)}`,
          context: `VALIDATE WEBHOOK V2 | ${query.provider}`,
        });
        return res.send(query.challenge);
      }
    } catch (error) {
      this.logger.error({
        message: `Error in validating webhook : ` + error.message,
        context: `VALIDATE WEBHOOK V2 | ${query.provider}`,
      });
      return res.sendStatus(HttpStatus.BAD_REQUEST);
    }
  }

  @Post(':provider/:orgId')
  async processWebhook(
    @Req() req: RequestWithUser,
    @Res() res: Response,
    @Param() params: any,
    @Body() payload: any,
  ) {
    res.sendStatus(HttpStatus.OK);

    const { provider, orgId } = params;
    this.logger.log({
      message: `webhook message | provider ${provider} | Payload: ${JSON.stringify(
        payload,
      )}`,
      context: `PROCESS V2 WEBHOOK | provider: ${provider} | orgId : ${orgId}`,
    });

    // filter out unsupported webhook type
    if (!this.v2WebhooksService.isSupportedWebhook(provider, payload)) {
      this.logger.error({
        message: `Unsupported webhook type for provider ${provider} : ${JSON.stringify(
          payload,
        )}`,
        context: `PROCESS V2 WEBHOOK | provider: ${provider} | orgId : ${orgId}`,
      });
      return;
    }

    try {
      if (provider === PROVIDERS.OUTLOOK_NYLAS) {
        const outlookWebhookMetadata =
          await this.outlookWebhookService.processOutlookWebhook(
            orgId,
            payload,
          );

        if (outlookWebhookMetadata) {
          await this.v2WebhooksService.processInboundWebhook(
            orgId,
            payload,
            outlookWebhookMetadata,
          );

        } else {
          this.logger.log({
            message: `Processed Oulook webhook metadata`,
            context: `PROCESS V2 WEBHOOK | provider: ${provider} | orgId : ${orgId}`,
          })
        }
      }
    } catch (error) {
      let errMessage = error.Response?.data?.message ?? error.message;
      this.logger.error({
        message:
          `Error in processing webhook for provider ${provider} : ` +
          errMessage,
        context: `PROCESS V2 WEBHOOK | provider: ${provider} | orgId : ${orgId}`,
      });
    }
  }
}
