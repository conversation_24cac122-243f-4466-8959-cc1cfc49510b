/**
 * This module contains the code to handle the webhook for HubSpot, Podium and Outlook and implements the processes that process the queries coming in from these channels and responds to them.
 */
import { Injectable } from '@nestjs/common';
import { MongoConversationService } from 'src/mongo/service/conversations/mongo-conversations.service';
import { CreateConversationDto } from 'src/mongo/dto/conversations/create.dto';
import { OrganizationService } from '../organization/services/organization.service';
import { MongoAgentService } from 'src/mongo/service/agent/mongo-agent.service';
import { MongoCredentialsService } from 'src/mongo/service/credentials/mongo-credentials.service';
import { SessionService } from 'src/session/session.service';
import { IRawbotResponse } from '../organization/services/channels/ghl/botresponse';
import {
  CUSTOM_LLM,
  KINDS,
  MessagingTypes,
  PROVIDERS,
  STATUS,
  TriggerTasks,
} from 'src/lib/constant';
import { isEmail } from 'src/utils/validator';
import { IGhlTrigger } from 'src/agent/dto/add-trigger-dto';
import { IBotActionResponse } from 'src/interfaces/interfaces';
import { MyLogger } from 'src/logger/logger.service';
import { NylasOutlookWebhooksDto } from './dto/v2-outlook-webhooks.dto';
import { WebhookTriggers } from 'nylas';
import {
  ProcessWebhookArgsMetadata,
  rolloverEventDto,
  WebhookDirection,
} from './dto/v2-webhooks.dto';
import { WebhooksService } from '../organization/webhooks/webhooks.service';
import { HydratedCredentialDocument } from 'src/mongo/schemas/credential/credentials.schema';
import { AgentDocument } from 'src/mongo/schemas/agents/agents.schema';
import { createActionElement } from 'src/lib/utils';
import { GhlService } from 'src/organization/services/channels/ghl/ghl.service';
import { sendNylasEmail } from 'src/api-client/services/nylas/nylas-api';

@Injectable()
export class V2WebhooksService {
  constructor(
    private readonly mongoConversationService: MongoConversationService,
    private readonly logger: MyLogger,
    private readonly organizationService: OrganizationService,
    private readonly v1WebhookService: WebhooksService,
    private readonly mongoAgentService: MongoAgentService,
    private readonly sessionService: SessionService,
    private readonly mongoCredentialService: MongoCredentialsService,
    private readonly ghlService: GhlService,
  ) {}

  isSupportedWebhook(provider: string, payload: NylasOutlookWebhooksDto) {
    //
    if (provider === PROVIDERS.OUTLOOK_NYLAS) {
      if (
        [WebhookTriggers.MessageCreated].includes(
          payload.type as WebhookTriggers,
        )
      ) {
        return true;
      } else return false;
    }
    return false;
  }

  /**
   * process the inbound webhooks
   * @param orgId capri org id
   * @param payload the incoming payload from the original channel's servers as it is
   * @param channelMetadata the metadata constructed for processing the ibound
   */
  async processInboundWebhook(
    orgId: string,
    payload: NylasOutlookWebhooksDto,
    channelMetadata: ProcessWebhookArgsMetadata,
  ) {
    const org = await this.organizationService.getOrganization({
      'connections.channels.keyId': channelMetadata.resourceId,
    });

    if (!org) {
      throw new Error(`Organization not found for orgId ${orgId}`);
    }

    const connection = (org?.connections?.channels || []).find(
      (channel) => channel.keyId === channelMetadata.resourceId,
    );

    if (!connection) {
      throw new Error(
        `No channel with resourceId ${
          channelMetadata.resourceId || ''
        } found for your organization.`,
      );
    }

    const agentIds = org?.agents;
    const channelAccountId = connection?.accountId;
    const channelUserId = connection?.userId;
    const message = payload.data.object.body;
    const channelName = connection?.name;

    const isDuplicate = await this.mongoConversationService.isAlreadySaved({
      contactId: channelMetadata.contact.id,
      'conversation.body': message,
    });

    if (!isDuplicate) {
      // save the incoming message
      await this.v1WebhookService.saveConversations({
        message,
        contactId: channelMetadata.contact.id,
        resourceId: channelMetadata.resourceId,
        direction: channelMetadata.direction,
        aiProviderName: '',
        agentId: '',
        channelName: connection?.name,
        status: STATUS.DELIVERED,
        messageType: channelMetadata.messageType,
        orgId: org._id.toString(),
      });
    }

    this.logger.log({
      message: `Processing ${channelMetadata.direction} webhook for contactId : ${channelMetadata.contact.id} \
      | channelName - ${channelName} | channelAccountId - ${channelAccountId} | incoming message: ${message} | messageType - ${ channelMetadata.messageType}`,
      context: `PROCESS V2 WEBHOOK | INBOUND WEBHOOK`,
    })

    // filter the agent by trigger
    const { agents, filterAgentError } = await this.filterAgents({
      agentIds,
      orgId: org._id.toString(),
      accountId: channelAccountId,
      channel:  channelMetadata.messageType,
    });

    this.logger.log({
      message: `Filtered agents for contactId : ${channelMetadata.contact.id} | agents - ${JSON.stringify(
        agents,
      )}`,
      context: `PROCESS V2 WEBHOOK | FILTER AGENTS`,
    });

    var rolloverEvent: rolloverEventDto = {
      rollover: false,
      reason: '',
      date: new Date().toISOString(),
      contactId: channelMetadata.contact.id,
      responded: false,
    };

    (async () => {
      for (const a of agents) {
        const { agent, task } = a;

        try {
          let botResponse = await this.sessionService.nylasWebhook({
            conversations: channelMetadata.conversationHistory,
            html: message,
            agentId: agent._id.toString(),
            agentData: agent,
            organizationData: org,
            contactId: channelMetadata.contact.id,
            locationId: channelMetadata.resourceId,
            task,
          });

          this.logger.log({
            message: `Response from bot for contactId : ${channelMetadata.contact.id} | response - ${JSON.stringify(
              botResponse,
            )}`,
            context: `PROCESS V2 WEBHOOK | RESPONSE FROM BOT`,
          })

          let tokensCount = botResponse?.usage_tokens_total;
          let htmlBodyResponse = botResponse?.email?.body;
          let subjectResponse = botResponse?.email?.subject;

          let aiProviderName = agent.aiProvider.accountName || '';

          let isSilent = false;

          let botResponseForRollover = botResponse?.rolloverDetails 

          if (botResponseForRollover?.rollover) {
            rolloverEvent.rollover = true;
            rolloverEvent.reason = botResponseForRollover?.rolloverReason || '';
          }

          //TODO: check if this action is silent through agentData
          //if rollover is true, then also we make it silent
          if (rolloverEvent.rollover) {
            isSilent = true;

            this.logger.log({
              message: `Rollover is true for channel, setting outgoing message as silent \ncontactId : ${channelMetadata.contact.id}`,
              context: `PROCESS V2 WEBHOOK | ROLLOVER CHECK`,
            })
          }

          // save the generated conversation in database before sending the message
          await this.saveConversation({
            message: `Subject: ${subjectResponse} | HTML Body: ${htmlBodyResponse}`,
            payload,
            direction: 'outbound',
            channelName,
            agentId: agent._id.toString() || '',
            aiProviderName,
            status: STATUS.PENDING,
            visibleActions: [],
            tokensCount: tokensCount,
            messageType: MessagingTypes.EMAIL,
            source: 'app',
          });

          if (htmlBodyResponse && htmlBodyResponse !== '' && subjectResponse && subjectResponse !== '') {
            if (task === TriggerTasks.EVALUATE) {
              
              this.logger.log({
                message: `Task type for the response is ${TriggerTasks.EVALUATE}. Hence Skipping the evaluation of the response message`,
                context: `V2 WEBHOOK`,
              });

            } else {

              this.logger.log({
                message: `Sending message to contactId : ${channelMetadata.contact.id} | response - ${JSON.stringify(
                  htmlBodyResponse
                )} | isSilent - ${isSilent} | provider - ${channelMetadata.provider}`,
                context: `PROCESS V2 WEBHOOK | SENDING MESSAGE`,
              });

              if (!isSilent) {
                // send message
                if (channelMetadata.provider === PROVIDERS.OUTLOOK_NYLAS) {
                  await sendNylasEmail(channelMetadata.resourceId, {
                    to: payload.data.object.from,
                    subject: subjectResponse,
                    body: htmlBodyResponse,
                    from: [
                      {
                        email: channelMetadata.userEmail,
                        name: channelMetadata.userName,
                      },
                    ],
                    replyToMessageId: payload.data.object.id,
                  });
                }
              }
            }
          }
        } catch (error) {
          if (rolloverEvent.responded === false) {
            rolloverEvent.rollover = true;
            rolloverEvent.reason = error.message;
            rolloverEvent.date = new Date();
          }
          if (
            rolloverEvent.rollover &&
            channelMetadata.direction === 'inbound'
          ) {
            await this.ghlService.handleRolloverEvent(
              rolloverEvent,
              channelMetadata.resourceId,
            );
          }

          this.logger.error({
            message: `Error in processing webhook | contactId : ${channelMetadata.contact.id} | resourceId : ${channelMetadata.resourceId} : ${error.message}`,
            context: `V2 WEBHOOK`,
          });
        }
      }
    })();
  }

  // helper functions

  async processOutbound({
    orgId = '',
    message = '',
    channelName = '',
    saveToken = true,
    messageType,
    agentId = '',
    tokensCount = 0,
    aiProviderName = '',
    resourceId,
    direction,
    contactId,
  }) {
    if (message === '') return;
    try {
      // already existing conversation entry
      const updated = await this.mongoConversationService.updateConversation({
        query: { contactId, 'conversation.body': message },
        updateBody: {
          $set: {
            'conversation.$.status': 'delivered',
            direction: direction,
            rollover: false,
            rolloverReason: '',
          },
        },
      });
      if (updated.modifiedCount == 0) throw new Error('new outbound');
    } catch (err) {
      if (err.message === 'new outbound') {
        const existingContact =
          await this.mongoConversationService.isAlreadySaved({
            contactId,
          });

        if (!saveToken && direction == 'outbound') tokensCount = 0;

        const conversationEntry = {
          body: message,
          dateAdded: new Date(),
          direction: direction,
          tokens: tokensCount,
          aiProvider: aiProviderName,
          actions: [],
          messageType,
          status: 'delivered',
        };

        if (existingContact) {
          // new conversation entry for an existing contact
          await this.mongoConversationService.updateConversation({
            query: { contactId },
            updateBody: {
              $push: {
                conversation: conversationEntry,
              },
              $set: {
                rollover: false,
                rolloverReason: '',
              },
            },
          });
        } else {
          // create new contact
          await this.mongoConversationService.createConversation({
            conversation: [conversationEntry],
            type: messageType,
            contactId: contactId,
            resourceId,
            agentId,
            orgId,
            totalTokens: tokensCount,
            updatedAt: new Date(),
            channel: channelName,
            rollover: true,
            rolloverReason: '',
          });
        }
      }
    }
  }

  async filterAgents({
    agentIds,
    orgId,
    accountId,
    channel,
    task,
  }: {
    task?: TriggerTasks;
    agentIds: string[];
    orgId: string;
    accountId: string;
    channel: MessagingTypes;
  }): Promise<{
    agents: { agent: AgentDocument; task: TriggerTasks }[];
    filterAgentError: string;
  }> {
    try {
      const agents = await this.mongoAgentService.getAgents({
        _id: { $in: agentIds },
        disabled: false,
        'triggers.data.subaccount': accountId,
        'triggers.data.channel': channel,
        'triggers.active': true,
      });

      const filteredAgents = agents.map((agent) => {
        let { triggers } = agent;
        let task: TriggerTasks;

        triggers = triggers.filter((trigger) => {
          if (
            trigger.active &&
            trigger.data.subaccount === accountId &&
            trigger.data.channel === channel
          ) {
            task = trigger.data.task as TriggerTasks;
            return true;
          }
          return false;
        });

        return {
          agent,
          task,
        };
      });

      return { agents: filteredAgents, filterAgentError: '' };
    } catch (error) {
      this.logger.error({
        message: `Error while filtering agents - ${error?.message}`,
        context: `WEBHOOK V2 | filterAgents | orgId - ${orgId} | accountId - ${accountId} | channel - ${channel} | task - ${task}`,
      });
      return { agents: [], filterAgentError: error?.message ?? '' };
    }
  }

  async getConversation({ resourceId, contactId }) {
    return await this.v1WebhookService.getConversationString(
      resourceId,
      contactId,
    );
  }

  async saveConversation({
    message = '',
    payload,
    direction = 'inbound',
    channelName,
    agentId,
    aiProviderName,
    errors = [],
    status = 'pending',
    visibleActions = [],
    saveToken = true,
    tokensCount,
    messageType = '',
    orgId = '',
    source = 'workflow',
    conversationId = undefined,
  }) {
    try {
      if (payload?.body) {
        const existingConvoRecord =
          await this.mongoConversationService.isAlreadySaved({
            resourceId: payload.locationId,
            contactId: payload.contactId,
          });
        // let vectorTokens: any[] | Uint32Array = [];
        if (!(saveToken && direction == 'outbound')) tokensCount = 0;
        if (existingConvoRecord) {
          const updateConversation = {
            $addToSet: {
              conversation: {
                body: message,
                dateAdded: new Date(),
                direction: direction,
                aiProvider: aiProviderName,
                tokens: tokensCount,
                actions: visibleActions,
                messageType,
                status,
              },
            },
          };
          updateConversation['$set'] = {
            channel: channelName,
            source,
          };
          if (agentId) updateConversation['$set']['agentId'] = agentId;
          if (orgId) updateConversation['$set']['orgId'] = orgId;
          if (conversationId)
            updateConversation['$set']['conversationId'] = conversationId;
          if (tokensCount) {
            updateConversation['$inc'] = {
              totalTokens: tokensCount,
            };
          }

          await this.mongoConversationService.updateConversation({
            query: {
              _id: existingConvoRecord._id.toString(),
            },
            updateBody: updateConversation,
          });

          this.logger.log({
            message: `conversation updated: direction '${direction}' | locationId '${payload.locationId}' | contactId '${payload.contactId}'`,
            context: `WEBHOOK V2`,
          });
        } else {
          const createConversation: CreateConversationDto = {
            conversation: [
              {
                body: payload.body,
                dateAdded: new Date(),
                direction: direction,
                tokens: tokensCount,
                aiProvider: aiProviderName,
                actions: visibleActions,
                messageType,
                status,
              },
            ],
            type: payload.messageType,
            contactId: payload.contactId,
            resourceId: payload.locationId,
            agentId,
            orgId,
            updatedAt: new Date(),
            totalTokens: tokensCount,
            channel: channelName,
            source,
            conversationId,
          };

          await this.mongoConversationService.createConversation(
            createConversation,
          );
          this.logger.log({
            message: `conversation saved: direction '${direction}' | locationId '${payload.locationId}' | contactId '${payload.contactId}'`,
            context: `WEBHOOK V2`,
          });
        }
      }
    } catch (error) {
      this.logger.error({
        message: `error encountered while saving conversation for resourceId ${payload.locationId} | contactId '${payload.contactId} : ${error.message}`,
        context: `WEBHOOK V2`,
      });
    }
  }
}
