import {
  CanActivate,
  ExecutionContext,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { UserService } from 'src/user/user.service';

@Injectable()
export class AuthGuard implements CanActivate {
  constructor(private readonly userService: UserService) {}

  canActivate(
    context: ExecutionContext,
  ): boolean | Promise<boolean> | Observable<boolean> {
    const request = context.switchToHttp().getRequest();
    const sessionId = request.cookies['usid'];
    
    return true;
    return this.validateSession(sessionId, request);
  }
  async validateSession(sessionId: string, request): Promise<boolean> {
    if (!sessionId) {
      throw new UnauthorizedException();
    }
    const user = await this.userService.findUserBySessionId(sessionId);

    if (!user) {
      throw new UnauthorizedException();
    }

    // Attach the user's id to the request object
    request.userId = (user as any)._id.toString();
    request.username = (user as any)?.name;
    request.email = (user as any)?.email;

    return true;
  }
}
