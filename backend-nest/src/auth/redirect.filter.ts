import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  UnauthorizedException,
} from '@nestjs/common';
import { Response } from 'express';

@Catch(UnauthorizedException)
export class RedirectFilter implements ExceptionFilter {
  catch(exception: UnauthorizedException, host: ArgumentsHost) {
    

    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();

    // return response.redirect('http://localhost:3000/api/auth/logout');
    return response
      .status(401)
      .json({ error: 'Unauthorized', message: 'You need to login again.' });
  }
}
