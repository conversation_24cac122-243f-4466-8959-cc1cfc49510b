import Anthropic from "@anthropic-ai/sdk";
import { Injectable } from '@nestjs/common';
import Groq from "groq-sdk";
import { DateTime } from 'luxon';
import { Configuration, OpenAIApi } from "openai";
import { GhlApisService } from "src/api-client/services/ghl-apis/ghl-apis.service";
import { Action, IBotActionResponse, IRolloverDetails } from "src/interfaces/interfaces";
import { CUSTOM_LLM, KINDS, PROVIDERS } from "src/lib/constant";
import { MyLogger } from "src/logger/logger.service";
import { MongoCredentialsService } from 'src/mongo/service/credentials/mongo-credentials.service';
import { structure_aiHistory_action } from "src/session/aiHistory_structure";
import { PineconeService } from "src/vector/services/pinecone/pinecone.service";
import { VectorService } from "src/vector/services/vector.service";
import { Tiktoken } from "tiktoken";
import { v4 as uuidv4 } from 'uuid';
import { MongoOrganizationService } from "../mongo/service/organization/mongo-organization.service";
const cl100k_base = require("tiktoken/encoders/cl100k_base.json");

@Injectable()
export class LangChainWriteService {
    constructor(
        private readonly mongoCredentialService: MongoCredentialsService,
        private readonly myLogger: MyLogger,
        private readonly vectorService: VectorService,
        private readonly ghlApisService: GhlApisService,
        private readonly pineconeService: PineconeService,
        private readonly mongoOrganizationService: MongoOrganizationService
    ) {}

    async errorMe(variableString, dynamicString, context){
        this.myLogger.error({
        message: `${variableString}: ${dynamicString}`,
        context: `${context}`
        })
    }
    async logMe(variableString, dynamicString, context){
        this.myLogger.log({
            message: `${variableString}: ${dynamicString}`,
            context: `${context}`
        })
    }

    async getTagSamples(agentId, embedding, orgid, activity){
        const index = await this.pineconeService.connectIndex(process.env.PINECONE_INDEX);
        try {
            
          const query = {
            namespace: orgid,
            //id: actionId,
            vector: embedding,
            topK: 10,
            includeMetadata: true,
            filter: {
              agentId: agentId,
              type: PROVIDERS.GHL_CHANNEL,
              activity: activity
            }
          };
    
          const queryResponse = await this.pineconeService.query( 
            index,  
            query
          );
    
          return queryResponse;
        } catch (error) {
          
        }
    }

    async pineconeSamples(agentId, orgid, embedding, type, filterId){
        const index = await this.pineconeService.connectIndex(process.env.PINECONE_INDEX);
        try {
            if(type === "session"){ 
    
            const query = {
                namespace: orgid,
                vector: embedding,
                topK: 10,
                includeMetadata: true,
                filter: {
                agentId: agentId, 
                type: "session"
                }
            };
        
            const queryResponse = await this.pineconeService.query(
                index, 
                query
            );
        
            return queryResponse;
    
            }else if(type == PROVIDERS.GOOGLE_SHEET){ 
            
            const query = {
                namespace: orgid,
                vector: embedding,
                topK: 10,
                includeMetadata: true,
                filter: {
                accountId: {$eq: filterId},
                // agentId: agentId,
                type: {$eq: type} 
                }
            };
        
            const queryResponse = await this.pineconeService.query( 
                index,  
                query
            );
        
            return queryResponse; 
            
            }else if(type == PROVIDERS.WEBSITE){
            
            const query = {
                namespace: orgid,
                vector: embedding, 
                topK: 10,
                includeMetadata: true,
                filter: {
                accountId: { $eq: filterId }, 
                type: { $eq: type },
                // name: filterId
                }
            };
        
            const queryResponse = await this.pineconeService.query( 
                index, 
                query
            );
        
            return queryResponse;
            
            }else if(type == PROVIDERS.GHL_CALENDAR){
            
            const query = {
                namespace: orgid,
                vector: embedding,
                topK: 10,
                includeMetadata: true,
                filter: {
                agentId: agentId,
                // accountId: { $eq: filterId }, 
                type: { $eq: type },
                resourceId: filterId
                }
            };
        
            const queryResponse = await this.pineconeService.query( 
                index, 
                query
            );
        
            return queryResponse;
            
            }else if(type == PROVIDERS.PDF){
            const query = {
                namespace: orgid,
                vector: embedding,
                topK: 10,
                includeMetadata: true,
                filter: {
                accountId: {$eq: filterId},
                // agentId: agentId,
                type: {$eq: type} 
                }
            };
        
            const queryResponse = await this.pineconeService.query( 
                index,  
                query
            );
        
            return queryResponse; 
            }else if(type == PROVIDERS.GOOGLE_DOCS){
            const query = {
                namespace: orgid,
                vector: embedding,
                topK: 10,
                includeMetadata: true,
                filter: {
                accountId: {$eq: filterId},
                // agentId: agentId,
                type: {$eq: type} 
                }
            };
        
            const queryResponse = await this.pineconeService.query( 
                index,  
                query
            );
        
            return queryResponse; 
            }else if(type == PROVIDERS.Generate_JSON){
                const query = {
                    namespace: orgid,
                    vector: embedding,
                    topK: 10,
                    includeMetadata: true,
                    filter: {
                    accountId: {$eq: filterId},
                    // agentId: agentId,
                    type: {$eq: type}
                    }
                };
            
                const queryResponse = await this.pineconeService.query( 
                    index,  
                    query
                );
            
                return queryResponse; 
            }
        } catch (error) {
            
        }  
    }

    async collectPineconeSemanticSamples(agentId, orgid, embedding, type, filterId, actionPrompt, systemPrompt, action, sessionId){
        try {
            const sessionIndexSheet = await this.pineconeSamples(
                agentId,
                orgid,
                embedding,
                type,
                filterId,
            );
    
            if (sessionIndexSheet) {
                if (sessionIndexSheet.matches.length > 0) {
                let totalSemanticSheetContent: string = '';
                actionPrompt +='\n\n' + 'Use the following data from this data source as context to continue the conversation:\n\n';
                systemPrompt += actionPrompt;
                for (const content of sessionIndexSheet.matches) {
                    // 
                    if (content.metadata['content'] !== undefined) {
                    let nestedSheetPrompt = content.metadata['content'];
                    if (typeof nestedSheetPrompt == 'string') {
                        nestedSheetPrompt = nestedSheetPrompt.replace(
                        /\n{3,}(?=(user:|Contact:))/g,
                        '\n\n',
                        );
                        totalSemanticSheetContent += nestedSheetPrompt;
                        const checkSheetLength = await this.numberOfTokens(
                        totalSemanticSheetContent,
                        );
                        if (
                        action.advancedSettings &&
                        action.advancedSettings.hasOwnProperty(
                            'maxTokensAllowed',
                        )
                        ) {
                        if (
                            checkSheetLength.length <
                            action.advancedSettings.maxTokensAllowed
                        ) {
                            systemPrompt += nestedSheetPrompt;
                            systemPrompt += '\n\n';
                        }
                        } else {
                        if (checkSheetLength.length < 450) {
                            systemPrompt += nestedSheetPrompt;
                            systemPrompt += '\n\n';
                        }
                        }
                    }
                    }
                }
                } else {
                await this.logMe("Zero matches for sheet samples in pinecone for ", action.providerName, sessionId);
                }
            } else {
                
            }
            return systemPrompt;
        } catch (error) {
            await this.errorMe('Error collecting pinecone samples', error, sessionId);
        }
    }

    async collectPineconeSemantic_TagSamples(agentId, orgid, embedding, actionPrompt, systemPrompt, action, sessionId){
        try {
            const sessionIndexSheet = await this.getTagSamples(
                agentId,
                orgid,
                embedding,
                action.activity
            );

            if (sessionIndexSheet) {
                if (sessionIndexSheet.matches.length > 0) {
                let totalSemanticSheetContent: string = '';
                actionPrompt +='\n\n' + 'Use the following data from this data source as context to continue the conversation:\n\n';
                systemPrompt += actionPrompt;
                for (const content of sessionIndexSheet.matches) {
                    // 
                    if (content.metadata['content'] !== undefined) {
                    let nestedSheetPrompt = content.metadata['content'];
                    if (typeof nestedSheetPrompt == 'string') {
                        nestedSheetPrompt = nestedSheetPrompt.replace(
                        /\n{3,}(?=(user:|Contact:))/g,
                        '\n\n',
                        );
                        totalSemanticSheetContent += nestedSheetPrompt;
                        const checkSheetLength = await this.numberOfTokens(
                        totalSemanticSheetContent,
                        );
                        if (
                        action.advancedSettings &&
                        action.advancedSettings.hasOwnProperty(
                            'maxTokensAllowed',
                        )
                        ) {
                        if (
                            checkSheetLength.length <
                            action.advancedSettings.maxTokensAllowed
                        ) {
                            systemPrompt += nestedSheetPrompt;
                            systemPrompt += '\n\n';
                        }
                        } else {
                        if (checkSheetLength.length < 450) {
                            systemPrompt += nestedSheetPrompt;
                            systemPrompt += '\n\n';
                        }
                        }
                    }
                    }
                }
                } else {
                await this.logMe("Zero matches for sheet samples in pinecone for ", action.providerName, sessionId);
                }
            } else {
                
            }
            return systemPrompt;
        } catch (error) {
            await this.errorMe('Error collecting pinecone samples', error, sessionId);
        }
        return systemPrompt;
    }

    async numberOfTokens(completeString){

        const encoding = new Tiktoken(
        cl100k_base.bpe_ranks,
        cl100k_base.special_tokens,
        cl100k_base.pat_str
        );
        const length = encoding.encode(completeString);
        encoding.free();
        return length;
    }

    async validateMessage(validatedMessages: any) {
        
        const rearrangedMessages = [];
        let lastRole = "user";
        let concatenatedMessage = '';

        validatedMessages.forEach((message, index) => {
        if (message.role !== lastRole) {
            if (concatenatedMessage !== '') {
            rearrangedMessages.push({ role: lastRole, content: concatenatedMessage.trim() });
            concatenatedMessage = '';
            }
            lastRole = message.role;
        }
        concatenatedMessage += message.content + ' ';

        // Check if it's the last message
        if (index === validatedMessages.length - 1) {
            rearrangedMessages.push({ role: message.role, content: concatenatedMessage.trim() });
        }
        });
        
        return rearrangedMessages;
    }

    async yesNoGenerateBotResponse(action_eventId, credentialId, action, agentData, messages, maxTokens, modelObj, aiProvAdvSett, retryCount=3) {

        try {
          let response;
          let advAiProvSett = {
            temperature: 0.4,
            maxLength: maxTokens,
            frequencyPenalty: 0,
          };
          if (aiProvAdvSett) {
            advAiProvSett.temperature = aiProvAdvSett.temperature;
            advAiProvSett.maxLength = aiProvAdvSett.maxLength;
            advAiProvSett.frequencyPenalty = aiProvAdvSett.frequencyPenalty;
          }
          let companyId = modelObj.companyId;
          if (companyId === PROVIDERS.OPENAI_PROVIDER) {
            const configuration = new Configuration({
              apiKey: modelObj.key,
            });
            const openai = new OpenAIApi(configuration);
             response = await openai.createChatCompletion({
              model: modelObj.model_name,
              messages: messages,
              max_tokens: advAiProvSett.maxLength,
              temperature: advAiProvSett.temperature,
              frequency_penalty: advAiProvSett.frequencyPenalty,
            });
          } else if (companyId === PROVIDERS.CLAUDE_PROVIDER) {
             const anthropic = new Anthropic({
               apiKey: modelObj.key,
             });
    
            let prompt = messages.shift();
    
            const validatedMessages = messages.filter(
              (message) => message.content.trim() !== '',
            );
    
            while (validatedMessages.length > 0 && validatedMessages[0].role === 'assistant') {
              prompt.content += validatedMessages.shift().content;
            }
    
            response = await anthropic.messages.create({
              model: modelObj.model_name,
              max_tokens: advAiProvSett.maxLength,
              temperature: advAiProvSett.temperature,
              messages: messages,
              system: prompt.content,
            });
          } else if (companyId === PROVIDERS.GROQ_PROVIDER) {
            const groq = new Groq({
              apiKey: modelObj.key,
            });

            messages = messages.filter(
              (message) =>
                !(
                  message.role === 'user' &&
                  (!message.content || message.content.trim() === '')
                ),
            );

            response = await groq.chat.completions.create({
              model: modelObj.model_name,
              max_tokens: 2,
              temperature: advAiProvSett.temperature,
              messages: messages,
              stop: null,
              stream: false,
            });
          } else if (companyId === PROVIDERS.FIREWORKS_PROVIDER) {
            const url =
              'https://api.fireworks.ai/inference/v1/chat/completions';
            const body = {
              model: CUSTOM_LLM.MODEL,
              messages: messages.map((message) => ({
                role: message.role,
                content: message.content,
              })),
            };

            const headers = {
              Authorization: `Bearer ${CUSTOM_LLM.API_KEY}`,
              'Content-Type': 'application/json',
            };

            response = await fetch(url, {
              method: 'POST',
              headers: headers,
              body: JSON.stringify(body),
            }).then((res) => res.json());
          } else {
            
          }
          const message_aiHistory_structure = await structure_aiHistory_action(agentData, modelObj, response, messages, action, credentialId, action_eventId);
          return {response, message_aiHistory_structure};
        } catch (error) {
          if(retryCount == 0){
            
          }else {
            if(error.response){
              if(error.response.status == 401){
                
                
              }else if(error.status == 429){
                
                
              }else{
                
                // 
                await new Promise((resolve) => setTimeout(resolve, 3000));
                return this.yesNoGenerateBotResponse(
                  agentData,credentialId,action,
                  messages,
                  maxTokens,
                  modelObj,
                  aiProvAdvSett,
                  retryCount - 1,
                );
              }
            }else if(error.status){
              if(error.status == 401){
                
                
              }else if(error.status == 429){
                
                
              }else{
                
                // 
                await new Promise((resolve) => setTimeout(resolve, 3000));
                return this.yesNoGenerateBotResponse(
                  agentData,credentialId, action,
                  messages,
                  maxTokens,
                  modelObj,
                  aiProvAdvSett,
                  retryCount - 1,
                );
              }
            }else{
              
              // 
              await new Promise((resolve) => setTimeout(resolve, 3000));
              return this.yesNoGenerateBotResponse(
                agentData, credentialId, action,
                messages,
                maxTokens,
                modelObj,
                aiProvAdvSett,
                retryCount - 1,
              );
            }
          }
        }
    }

    async generateBotResponse(action_eventId, credentialId, action, agentData, messages, maxTokens, modelObj, aiProvAdvSett, retryCount=3) { 

        try {
        let response;

        let advAiProvSett = {
            temperature: 0.4,
            maxLength: maxTokens,
            frequencyPenalty: 0,
        };

        if (aiProvAdvSett) {
            advAiProvSett.temperature = aiProvAdvSett.temperature;
            advAiProvSett.maxLength = aiProvAdvSett.maxLength;
            advAiProvSett.frequencyPenalty = aiProvAdvSett.frequencyPenalty;
        }

        let companyId = modelObj.companyId;

        if (companyId === PROVIDERS.OPENAI_PROVIDER){
            const configuration = new Configuration({
            apiKey: modelObj.key,
            });
            const openai = new OpenAIApi(configuration);
            response = await openai.createChatCompletion({
            model: modelObj.model_name,
            messages: messages,
            max_tokens: advAiProvSett.maxLength || maxTokens,
            temperature: advAiProvSett.temperature,
            frequency_penalty: advAiProvSett.frequencyPenalty,
            });
        } else if(companyId === PROVIDERS.CLAUDE_PROVIDER) {
            let prompt = messages. shift();
            const anthropic = new Anthropic({
            apiKey: modelObj.key,
            });
            const validatedMessages = messages.filter(
            (message) => message.content.trim() !== '',
            );
            while (validatedMessages.length > 0 && validatedMessages[0].role === 'assistant') {
            prompt.content += validatedMessages.shift().content;
            }
            let finalMessage = await this.validateMessage(validatedMessages);
            response = await anthropic.messages.create({
            model: modelObj.model_name,
            max_tokens: advAiProvSett.maxLength || maxTokens,
            messages: finalMessage,
            temperature: advAiProvSett.temperature,
            system: prompt.content,
            });
        }else if (companyId === PROVIDERS.GROQ_PROVIDER) {
          const groq = new Groq({
            apiKey: modelObj.key,
          });

          messages = messages.filter(
            (message) =>
              !(
                message.role === 'user' &&
                (!message.content || message.content.trim() === '')
              ),
          );

          response = await groq.chat.completions.create({
            model: modelObj.model_name,
            max_tokens: advAiProvSett.maxLength || maxTokens,
            temperature: advAiProvSett.temperature,
            messages: messages,
            stop: null,
            stream: false,
          });
        } else if (companyId === PROVIDERS.FIREWORKS_PROVIDER) {
          const url = 'https://api.fireworks.ai/inference/v1/chat/completions';
          const body = {
            model: CUSTOM_LLM.MODEL,
            messages: messages.map((message) => ({
              role: message.role,
              content: message.content,
            })),
          };

          const headers = {
            Authorization: `Bearer ${CUSTOM_LLM.API_KEY}`,
            'Content-Type': 'application/json',
          };

          response = await fetch(url, {
            method: 'POST',
            headers: headers,
            body: JSON.stringify(body),
          }).then((res) => res.json());
        } else {
          
        }
        const action_aiHistory_structure = await structure_aiHistory_action(agentData, modelObj, response, messages, action, credentialId, action_eventId);

        return {response, action_aiHistory_structure};
        } catch (error) {
        
        if (error && (error.response.status !== 401 || error.status !== 401) && retryCount > 0) {
            
            await new Promise(resolve => setTimeout(resolve, 3000));
            return this.generateBotResponse(action_eventId, credentialId, action, agentData, messages, maxTokens, modelObj, aiProvAdvSett, retryCount-1);
        } else {
            
            return error;
        }
        }
    }

    async ghlCalendar(action, sessionId, organizationDetails, conversationHistoryString, agentData, maxTokens, aiProvAdvSett, modelObj, contactDetails){
        try {
            let rolloverDetails: IRolloverDetails = {
                rollover: false,
                rolloverReason: '',
                rolloverDate: ''
            }
            let action_aiHistory_write: Action[] = [];
            let errorSpecific: string[] = [];
            const botResponseForEachDataSource : IBotActionResponse[] = [];
            await this.logMe(
                'Yes for writing: ',
                action.providerName,
                sessionId,
            );
            // 
            let dataSourceData;
    
            const accountId = action.accountId;
            let orgId = action.orgId;
    
            var timezone, ghlReference;
            for (const dataSource of organizationDetails.connections
                .dataSources) {
                if (dataSource.accountId === accountId) {
                dataSourceData = dataSource;
                ghlReference = dataSource.reference;
                if(dataSource.useContactTz && dataSource.useContactTz == true && contactDetails && contactDetails.contactTimezone){
                    timezone = contactDetails.contactTimezone;
                    await this.logMe('write timezone: ', timezone,sessionId)
                }else{
                    timezone = dataSource.timezone;
                    await this.logMe('write timezone: ', timezone,sessionId)
                }
                }
            }
    
            const calendarId = dataSourceData.calendarId;
            const locationId = dataSourceData.keyId;
    
            if (calendarId === undefined) {
                
            }
    
            const credentialId = dataSourceData.credentialId;
    
            const credentialData =
                await this.mongoCredentialService.getCredential({
                _id: credentialId,
                kind: KINDS.GHL_CREDENTIAL,
                });
    
            const ghlAccessToken = credentialData['creds'].accessToken;
            const ghlRefreshToken = credentialData['creds'].refreshToken;
    
            const tokens = {
                access_token: ghlAccessToken,
                refresh_token: ghlRefreshToken,
            };
    
            var date = new Date();
            var epochDate = date.getTime();
            var tomorrowEpoch = epochDate + 86400000;
            var in72Hours = epochDate + 259200000;
            var long72Hours = new Date(in72Hours).toLocaleDateString(
                'en-US',
                {
                weekday: 'long',
                month: 'long',
                day: 'numeric',
                timeZone: timezone,
                },
            );
            var in72DayOfWeek = new Date(in72Hours).toLocaleDateString(
                'en-US',
                { weekday: 'long', timeZone: timezone },
            );
            var in48Hours = epochDate + 172800000;
            var in96Hours = epochDate + 345600000;
            var in96Date = new Date(in96Hours);
            var in96DayOfWeek = in96Date.toLocaleDateString('en-US', {
                weekday: 'long',
                timeZone: timezone,
            });
            var in96LongDate = new Date(in96Hours).toLocaleDateString(
                'en-US',
                {
                weekday: 'long',
                month: 'long',
                day: 'numeric',
                timeZone: timezone,
                },
            );
            var longDate = date.toLocaleDateString('en-US', {
                weekday: 'long',
                month: 'long',
                day: 'numeric',
                timeZone: timezone,
            });
            var longTomorrow = new Date(tomorrowEpoch).toLocaleDateString(
                'en-US',
                {
                weekday: 'long',
                month: 'long',
                day: 'numeric',
                timeZone: timezone,
                },
            );
            var long48Hours = new Date(in48Hours).toLocaleDateString(
                'en-US',
                {
                weekday: 'long',
                month: 'long',
                day: 'numeric',
                timeZone: timezone,
                },
            );
            var in48HoursDayOfWeek = new Date(in48Hours).toLocaleDateString(
                'en-US',
                { weekday: 'long', timeZone: timezone },
            );
            //get the end of this week and beginning of next week
            var numericDay = date.getDay();
            var endOfWeekDiff = 6 - numericDay;
            if (endOfWeekDiff == 0) {
                endOfWeekEpoch = in48Hours;
                var endOfWeek = 'today';
                var endOfWeekDayofWeek = 'today';
                var nextWeekEpoch = tomorrowEpoch;
            } else {
                var endOfWeekEpoch = epochDate + endOfWeekDiff * 86400000;
                var endOfWeek = new Date(endOfWeekEpoch).toLocaleDateString(
                'en-US',
                {
                    weekday: 'long',
                    month: 'long',
                    day: 'numeric',
                    timeZone: timezone,
                },
                );
                var endOfWeekDayofWeek = new Date(
                endOfWeekEpoch,
                ).toLocaleDateString('en-US', {
                weekday: 'long',
                timeZone: timezone,
                });
                var nextWeekEpoch = endOfWeekEpoch + 86400000;
            }
    
            var nextWeek = new Date(nextWeekEpoch).toLocaleDateString(
                'en-US',
                {
                weekday: 'long',
                month: 'long',
                day: 'numeric',
                timeZone: timezone,
                },
            );
            var endOfNextWeekEpoch = nextWeekEpoch + 86400000 * 6;
            var endOfNextWeek = new Date(
                endOfNextWeekEpoch,
            ).toLocaleDateString('en-US', {
                weekday: 'long',
                month: 'long',
                day: 'numeric',
                timeZone: timezone,
            });
    
            var messageLog = [];
    
            var systemPromptForWrite =
                'You are a scheduling assistant for a business. You are speaking with a contact who is interested in booking an appointment with the business.\n';
            systemPromptForWrite +=
                'As a booking assistant, you specialize in determining the appropriate date and time for the appointment to be booked, and representing that date and time as a JSON object as shown below:\n\n';
            systemPromptForWrite += `{"date" : "MM-DDTHH:MM"}` + '\n';
            systemPromptForWrite +=
                'Where MM is the month, DD is the day, HH is the hour, and MM is the minute.\n';
            systemPromptForWrite +=
                '\nYou are only responsible for generating this JSON object, to allow a human member of the team to book the appointment.\n';
            systemPromptForWrite +=
                'You are not responsible for booking the appointment yourself.';
    
            messageLog.push({
                role: 'system',
                content: systemPromptForWrite,
            });
    
            var exampleUserString1 = 'Here is the conversation so far:\n\n';
            exampleUserString1 +=
                'assistant: Hey there! Can we go ahead and get you scheduled?\n';
            exampleUserString1 += 'user: Sure\n';
            exampleUserString1 +=
                'assistant: Perfect, lets go ahead and get you scheduled for our first meeting. What day works best for you?\n';
            exampleUserString1 +=
                'user: Any day this week after 3 is fine\n';
            exampleUserString1 +=
                'assistant: How about ' + long48Hours + ' at 3:00?\n';
            exampleUserString1 += 'user: That works\n';
            exampleUserString1 +=
                'assistant: Great, I will go ahead and put you in the schedule for ' +
                long48Hours +
                ' at 3:00pm.';
            exampleUserString1 +=
                '\nGenerate the JSON object appropriate for the conversation above. If the conversation does not yet contain enough information to determine the appointment date and time, set the date value to "TBD"';
            var completionExample1 =
                'The JSON object for this conversation should be:\n\n';
            let dateobject1: Date = new Date(in48Hours);
            let completion1Month: string = (
                dateobject1.getMonth() + 1
            ).toString();
            if (parseInt(completion1Month) < 10) {
                completion1Month = '0' + completion1Month;
            }
            let completion1Day: string = dateobject1.getDate().toString();
            if (parseInt(completion1Day) < 10) {
                completion1Day = '0' + completion1Day;
            }
            completionExample1 +=
                '```\n\
            {\n\
                "date" : "' +
                completion1Month +
                '-' +
                completion1Day +
                'T15:00"\n\
            }```\n';
    
            messageLog.push({ role: 'user', content: exampleUserString1 });
            messageLog.push({
                role: 'assistant',
                content: completionExample1,
            });
    
            var exampleUserString2 = 'Here is the conversation so far:\n\n';
            exampleUserString2 +=
                'assistant: Would you like me to go ahead and schedule a call?\n';
            exampleUserString2 += 'user: Sure sounds good\n';
            exampleUserString2 +=
                'assistant: Great, what day works best for you?\n';
            exampleUserString2 +=
                'user: I have some time today or tomorrow\n';
            exampleUserString2 +=
                'assistant: How about ' + longTomorrow + ' at 1:00?\n';
            exampleUserString2 += 'user: Ok sure\n';
            exampleUserString2 +=
                'assistant: Great, we have you scheduled for tomorrow at 1:00pm!';
            const dateobject2: Date = new Date(tomorrowEpoch);
            let completion2Month: string = (
                dateobject2.getMonth() + 1
            ).toString();
            if (parseInt(completion2Month) < 10) {
                completion2Month = '0' + completion2Month;
            }
            let completion2Day: string = dateobject2.getDate().toString();
            if (parseInt(completion2Day) < 10) {
                completion2Day = '0' + completion2Day;
            }
            var completionExample2 =
                'The JSON object for this conversation should be:\n\n';
            completionExample2 +=
                '```\n\
            {\n\
                "date" : "' +
                completion2Month +
                '-' +
                completion2Day +
                'T13:00"\n\
            }```\n';
    
            messageLog.push({ role: 'user', content: exampleUserString2 });
            messageLog.push({
                role: 'assistant',
                content: completionExample2,
            });
    
            //TODO: add examples
    
            var userPrompt =
                'Here is the conversation so far:\n\n' +
                conversationHistoryString;
            userPrompt +=
                '\nFor reference, today is ' +
                longDate +
                ', tomorrow is ' +
                longTomorrow +
                ', and ' +
                endOfWeekDayofWeek +
                ' is ' +
                endOfWeek +
                '.\n';
            userPrompt +=
                "\nGenerate the JSON object appropriate for the conversation above. If the conversation does not yet contain enough information to determine both the appointment date and the time, simply return 'TBD'";
            messageLog.push({ role: 'user', content: userPrompt });
    
            let ghlCalendar_write_training_token_length = 0;
            let ghlCalendar_write_training_input_token = 0;
            let totalTokens = 0;
            const ghlCalendar_write_eventId = uuidv4();
            try {
                // messageLog.push({role: "user", content: query});
                var dateObjectResponse_response_response =
                await this.yesNoGenerateBotResponse(ghlCalendar_write_eventId, credentialId, action, agentData, messageLog, maxTokens, modelObj, aiProvAdvSett);
                const dateObjectResponse_response = dateObjectResponse_response_response.response;
                const actions = action_aiHistory_write.push(dateObjectResponse_response_response.message_aiHistory_structure);
                var dateObjectResponse = '';
                let companyId = modelObj.companyId;
                if (companyId === PROVIDERS.OPENAI_PROVIDER) {
                  dateObjectResponse =
                    dateObjectResponse_response.data.choices[0].message.content;
                  totalTokens +=
                    dateObjectResponse_response.data.usage.total_tokens;
                  ghlCalendar_write_training_token_length +=
                    dateObjectResponse_response.data.usage.completion_tokens;
                  ghlCalendar_write_training_input_token +=
                      dateObjectResponse_response.data.usage.prompt_tokens;
                } else if (companyId === PROVIDERS.CLAUDE_PROVIDER) {
                  dateObjectResponse =
                    dateObjectResponse_response.content[0].text;
                  totalTokens +=
                    dateObjectResponse_response.usage.input_tokens +
                    dateObjectResponse_response.usage.output_tokens;
                  ghlCalendar_write_training_token_length +=
                    dateObjectResponse_response.usage.output_tokens;
                  ghlCalendar_write_training_input_token +=
                      dateObjectResponse_response.usage.input_tokens;
                } else if (companyId === PROVIDERS.GROQ_PROVIDER) {
                  dateObjectResponse =
                    dateObjectResponse_response.choices[0].message.content;
                  totalTokens += dateObjectResponse_response.usage.total_tokens;
                  ghlCalendar_write_training_token_length +=
                    dateObjectResponse_response.usage.completion_tokens;
                  ghlCalendar_write_training_input_token +=
                      dateObjectResponse_response.usage.prompt_tokens;
                } else if (companyId === PROVIDERS.FIREWORKS_PROVIDER) {
                  dateObjectResponse =
                    dateObjectResponse_response.choices[0].message.content;
                  totalTokens += dateObjectResponse_response.usage.total_tokens;
                  ghlCalendar_write_training_token_length +=
                    dateObjectResponse_response.usage.completion_tokens;
                  ghlCalendar_write_training_input_token +=
                      dateObjectResponse_response.usage.prompt_tokens;
                    
                    const pricePerMillion = CUSTOM_LLM.COST_PER_MILLION;
                    const usageInMillions = totalTokens / 1000000;
                    const cost = usageInMillions * pricePerMillion;

                    await this.mongoOrganizationService.updateHostedAiUsage(
                      orgId,
                      agentData._id.toString(),
                      locationId,
                      {
                        numOfTokensUsed: totalTokens,
                        cost: cost,
                        provider: companyId,
                        model: CUSTOM_LLM.PUBLIC_MODEL_NAME,
                      },
                    );
                }

                // const messages_as_string = messages.map(item=>`${item.role}:${item.content}`).join('');
                // let requestDetails_aiHistory_sheet: RequestDetails_aiHistory = {
                // query: messages_as_string,
                // totalTokens_req: ghlCalendar_write_training_input_token
                // };
                // let responseDetails_aiHistory_sheet: ResponseDetail_aiHistory = {
                //     actionType: action.providerName,
                //     kind: action.activity,
                //     totalTokens_res: ghlCalendar_write_training_token_length,
                //     eventData: {
                //     date: dateObjectResponse
                //     }
                // };
                // 
                // var dateObject = dateObjectResponse.data.choices[0].message.content;
                // 
            } catch (err) {
                
            }
    
            try {
                var jsonBody = dateObjectResponse.replace(
                /(\r\n|\n|\r)/gm,
                '',
                );
                var completionVars = jsonBody.split('```');
                if (completionVars.length < 3) {
                
                await this.logMe(
                    'invalid date object response',
                    '',
                    sessionId,
                );
                rolloverDetails = {
                    rollover: true,
                    rolloverReason: 'invalid date object response',
                    rolloverDate: new Date().toISOString(),
                };
                // errorSpecific.push("Invalid Object Response");
                } else {
                var dateJson = JSON.parse(completionVars[1]);
                var dateString = dateJson.date;
                if (
                    dateString == 'TBD' ||
                    dateString == 'tbd' ||
                    dateString == 'Tbd'
                ) {
                    
                    await this.logMe('date string is TBD', '', sessionId);
                    rolloverDetails = {
                    rollover: true,
                    rolloverReason: 'date string is TBD',
                    rolloverDate: new Date().toISOString(),
                    };
                    // errorSpecific.push("Date String Is TBD");
                } else {
                    try {
                    var tzMoment = DateTime.local().setZone(timezone);
                    var tzOffset = tzMoment.offset;
                    // Check if the current date is within the DST period
                    // var isDST = tzMoment >= DateTime.fromObject({ year: 2024, month: 3, day: 10 }) &&
                    //             tzMoment <= DateTime.fromObject({ year: 2024, month: 11, day: 3 });
    
                    // // If DST is in effect, adjust the offset by 1 hour
                    // var tzOffset = tzMoment.offset + (isDST ? 60 : 0);
                    var offsetHours: string = (tzOffset / 60).toString();
                    if (Math.abs(parseInt(offsetHours)) < 10) {
                        if (parseInt(offsetHours) < 0) {
                        offsetHours =
                            '-0' + Math.abs(parseInt(offsetHours));
                        } else {
                        offsetHours = '+0' + offsetHours;
                        }
                    } else {
                        if (parseInt(offsetHours) < 0) {
                        offsetHours = '-' + Math.abs(parseInt(offsetHours));
                        } else {
                        offsetHours = '+' + offsetHours;
                        }
                    }
                    //if offset half hour, add :30 to offset
                    //make sure to do math on absolute value of offset
                    if (Math.abs(parseInt(offsetHours)) % 1 !== 0) {
                        offsetHours = Math.floor(
                        parseInt(offsetHours),
                        ).toString();
    
                        offsetHours = offsetHours + ':30';
                    } else {
                        offsetHours = offsetHours + ':00';
                    }
                    //get the current year in the respective timezone
                    var tzYear = tzMoment.year;
                    } catch (err) {
                    // 
                    await this.errorMe(
                        'error getting timezone offset: ',
                        err,
                        sessionId,
                    );
                    rolloverDetails = {
                        rollover: true,
                        rolloverReason: `error getting timezone offset: ${err}`,
                        rolloverDate: new Date().toISOString(),
                    };
                    errorSpecific.push('Error Getting Timezone Offset');
                    }
    
                    var fullDateString = `${tzYear}-${dateString}:00${offsetHours}`;
                    var containsUndefined = /undefined/i.test(fullDateString);
                    const pattern = /\bTBD\b|\bTbd\b|\btbd\b/;
                    if (containsUndefined) {
                    
                    await this.errorMe(
                        'Booking date string is undefined. executionId: ',
                        '',
                        sessionId,
                    );
                    rolloverDetails = {
                        rollover: true,
                        rolloverReason: 'Booking date string is undefined.',
                        rolloverDate: new Date().toISOString(),
                    };
                    } else if (pattern.test(fullDateString)) {
                    
                    await this.errorMe(
                        'Booking date string is TBD. executionId',
                        '',
                        sessionId,
                    );
                    rolloverDetails = {
                        rollover: true,
                        rolloverReason: 'date string is TBD',
                        rolloverDate: new Date().toISOString(),
                    };
                    } else {
                    // 
                    await this.logMe(
                        'Booking appointment date: ',
                        fullDateString,
                        sessionId,
                    );
                    const actionSilent = action.silent;
                    // fill in a new botResponse
                    const newBotResponse: IBotActionResponse = {
                        eventId: ghlCalendar_write_eventId,
                        sender: 'bot',
                        kind: PROVIDERS.GHL_CALENDAR,
                        accountName: action.accountName,
                        accountId: accountId,
                        deleted: false,
                        action: 'write',
                        eventData: {
                        startDate: fullDateString,
                        },
                        reference: ghlReference,
                        calendarId: calendarId,
                        locationId: locationId,
                        silent: actionSilent,
                        timestamp: Math.floor(Date.now() / 1000),
                    };
                    // push the new bot response to the botResponse array
                    botResponseForEachDataSource.push(newBotResponse);
                    }
                }
                }
            } catch (err) {
                
                // errorSpecific.push("Error Parsing Date Response");
            }
            return {botResponseForEachDataSource, action_aiHistory_write};
        } catch (error) {
            await this.errorMe("Write Langchain ghl calendar error", error, sessionId);
        }
    }
}
