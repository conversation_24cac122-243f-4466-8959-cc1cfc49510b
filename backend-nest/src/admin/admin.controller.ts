import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
} from '@nestjs/common';
import { AdminService } from './admin.service';
import { ExtendTrailDto } from './dto/extend-trial.dto';

@Controller('admin')
export class AdminController {
  constructor(private readonly adminService: AdminService) {}

  @Post('billing/extend-trial')
  async extendTrial(@Body() payload: ExtendTrailDto) {
    try {
      await this.adminService.extendTrial(payload);
      return { message: 'Trial extended successfully' };
    } catch (error) {
      throw new Error(error);
    }
  }
}
