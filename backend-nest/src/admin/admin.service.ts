import {
  Injectable,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { ExtendTrailDto } from './dto/extend-trial.dto';
import { InjectModel } from '@nestjs/mongoose';
import {
  Organization,
  OrganizationModel,
} from 'src/mongo/schemas/organization/organization.schema';
import { Model } from 'mongoose';
import { MyLogger } from 'src/logger/logger.service';

@Injectable()
export class AdminService {
  constructor(
    @InjectModel(Organization.name)
    private readonly organizationModel: Model<OrganizationModel>,
    private readonly myLogger: MyLogger,
  ) {}
  async extendTrial(payload: ExtendTrailDto) {
    try {
      const { customerId, extendTo, password } = payload;

      if (password !== process.env.ADMIN_PASSWORD) {
        throw new UnauthorizedException('Invalid password');
      }
      const organization = await this.organizationModel.findOne({
        'billing.customerId': customerId,
      });
      if (!organization) {
        throw new NotFoundException('Organization not found');
      }

      const dateToEpoch = new Date(extendTo).getTime();
      const newStartDate = dateToEpoch - 15 * 24 * 60 * 60 * 1000;
      

      const updatedOrg = await this.organizationModel.findByIdAndUpdate(
        organization._id,
        {
          'billing.startDate': newStartDate,
          'billing.status': 'active',
        },
      );
    } catch (error) {
      this.myLogger.error(error);
      throw error;
    }
  }
}
