import {
  HttpException,
  HttpStatus,
  Injectable,
  RawBodyRequest,
} from '@nestjs/common';
import { OrganizationService } from 'src/organization/services/organization.service';
import { ChoosePlanDto } from './dto/ChoosePlanDto';
import { PLANS, planMap } from './billing.map';
import { StripeService } from './stripe.service';
import { ChangeBillingEmailDto } from './dto/ChangeBillingEmailDto';
import { CancelPlanDto } from './dto/CancelPlanDto';
import { StartPlanDto } from './dto/StartPlanDto';
import { MyLogger } from 'src/logger/logger.service';

@Injectable()
export class BillingService {
  constructor(
    private readonly stripeService: StripeService,
    private readonly myLogger: MyLogger,
  ) {}

  //loop over all the organizations in our production DB
  // // for each organization, run updatePlanForOldAccounts({email: billing.email})
  // // if updatePlanForOldAccounts({email: billing.email}) returns something, update the organization
  async updatePlanForOldAccounts(payload: { email: string }) {
    const { email } = payload;
    const v2StripeData = await this.findV2Users({ email });
    if (v2StripeData) {
      const mongooseSchema = {
        $set: {
          // 'billing.email': email,
          // 'billing.userName': name,
          'billing.plan': v2StripeData.key,
          'billing.customerId': v2StripeData.customerId,
          'billing.allowedAgents': v2StripeData.allowedAgents,
          'billing.havePaymentInfo': true,
        },
        $push: {
          'billing.subscriptions': {
            priceId: v2StripeData.priceId,
            subscriptionId: v2StripeData.subscription_id,
          },
        },
      };
      return mongooseSchema;
    }
  }
  async migrateScript() {}
  async startPlan(payload: StartPlanDto) {
    const { email, name, orgId, isCustomPlan1 } = payload;
    //check for v2 subscription first
    const v2StripeData = await this.findV2Users({ email });
    this.myLogger.log({
      message: 'v2SripeData for ' + email + { v2StripeData },
    });

    if (v2StripeData) {
      this.myLogger.log({
        message:
          'User already has a v2 account, these are the details ' +
          v2StripeData,
      });
      const planName = isCustomPlan1 ? PLANS.CUSTOMPLAN1 : v2StripeData.key;
      const isEligibleForCustomPlan1 = isCustomPlan1 ? true : false; 
      const updateObj = {
        $set: {
          'billing.email': email,
          'billing.userName': name,
          'billing.plan': planName,
          'billing.billingCycle': 'monthly',
          'billing.havePaymentInfo': true,
          'billing.customerId': v2StripeData.customerId,
          'billing.allowedAgents': v2StripeData.allowedAgents,
          'billing.eligibleForCustomPlan1': isEligibleForCustomPlan1,
        },
        $push: {
          'billing.subscriptions': {
            priceId: v2StripeData.priceId,
            subscriptionId: v2StripeData.subscription_id,
          },
        },
      };
      return updateObj;
    }
    //if user has no paid billing in v2, create a normal subscription
    const stripeDetails = await this.stripeService.startPlan(payload);
    const { customerId, subscriptionId } = stripeDetails;

    const subscriptionObj = {
      priceId: planMap.trial.price_id,
      subscriptionId,
    };

    const planName = isCustomPlan1 ? PLANS.CUSTOMPLAN1 : PLANS.TRIAL;
    const allowedAgents = isCustomPlan1 ? planMap.customPlan1.agents : planMap.trial.agents;
    const isEligibleForCustomPlan1 = isCustomPlan1 ? true : false; 
    const updateObj = {
      $set: {
        'billing.email': email,
        'billing.userName': name,
        'billing.plan': planName,
        'billing.customerId': customerId,
        'billing.allowedAgents': allowedAgents,
        'billing.eligibleForCustomPlan1': isEligibleForCustomPlan1,
      },
      $push: {
        'billing.subscriptions': subscriptionObj,
      },
    };
    return updateObj;
  }
  async choosePlan(payload: ChoosePlanDto) {
    try {
      const { planName, orgId, havePaymentInfo, customerId, billingCycle } =
        payload;
      const { price, agents, price_id, price_id_yearly } = planMap[planName];
      const price_id_final =
        billingCycle === 'monthly' || planName === 'trial'
          ? price_id
          : price_id_yearly;
      const data = await this.stripeService.choosePlan({
        havePaymentInfo,
        price_id_final,
        customerId,
        orgId,
        bypassStripeCheckout: planName === PLANS.CUSTOMPLAN1
      });
      if (!data) {
        throw new Error('Problem updating the plan');
      }
      if (data.subscriptionId) {
        //update the org billing details accordingly
      }
      return data;
    } catch (error) {
      throw new Error('Problem choosing a plan');
    }
  }
  async cancelPlan(payload: { orgId: string; subscriptionId: string }) {
    try {
      const { orgId, subscriptionId } = payload;
      const status = 'cancelled';
      const response = await this.stripeService.cancelPlan({
        orgId,
        subscriptionId,
      });
      if (!response) {
        throw new Error('Problem canceling the plan');
      }
      return response;
    } catch (error) {
      throw new Error('Problem canceling a plan');
    }
  }
  async setPlan(payload: {
    orgId: string;
    agents: number;
    price: number;
    planName: string;
  }) {
    try {
      const { orgId, agents, price, planName } = payload;
      // const orgUpdate = await this.organizationService.updateOrganisation(
      //   { _id: orgId },
      //   {
      //     'billing.plan': planName,
      //     'billing.allowedAgents': agents,
      //     'billing.billingAmount': price,
      //   },
      // );
      // if (!orgUpdate) {
      //   throw new Error('Error updating the organization');
      // }
      return;
    } catch (error) {
      throw new Error('Problem setting a new plan');
    }
  }

  async updateEmail(payload: ChangeBillingEmailDto) {
    try {
      const { email, orgId } = payload;
      // const res = await this.stripeService.changeUserEmail({ email });
      // if (!res) {
      //   throw new Error('Problem updating the email');
      // }
      // const emailChanged = await this.organizationService.updateOrganisation(
      //   { _id: orgId },
      //   { 'billing.email': email },
      // );
      return;
    } catch (error) {
      throw new Error('Problem updating the email');
    }
  }

  async handleStripeSuccess(payload: { session_id: string }) {
    const response = await this.stripeService.handleStripeSuccess(payload);
    return response;
  }

  async getStripeCustomerPortalLink(customerId: string) {
    return await this.stripeService.getStripeCustomerPortalLink(customerId);
  }

  async createCheckoutForLlm(customerId: string, amount: number) {
    return await this.stripeService.createCustomerChoosesPriceCheckout(
      customerId,
      amount,
    );
  }

  async addPaymentMethod(customerId: string) {
    return await this.stripeService.addPaymentMethod(customerId);
  }

  async findV2Users(payload: { email: string }) {
    const response = await this.stripeService.findCustomerSubscriptionsByEmail(
      payload,
    );
    return response;
  }

  async getStripeDetails(payload: { customerId: string }) {
    const response = await this.stripeService.getStripeDetails(payload);
    return response;
  }

  async checkBillingStatus(payload: { customerId: string }) {
    const { customerId } = payload;
    const response = await this.stripeService.getCustomerCurrentStatus({
      customerId,
    });
    return response;
  }

  async SubscriptionExpired(req: RawBodyRequest<Request>, sig: string) {
    return await this.stripeService.SubscriptionExpired(req, sig);
  }

  async LlmCredsAdded(req: RawBodyRequest<Request>, sig: string) {
    return await this.stripeService.LlmCredsAdded(req, sig);
  }

  async getStripePaymentMethods(customerId: string) {
    return await this.stripeService.getStripePaymentMethods(customerId);
  }

  async setDefaultPaymentMethod(customerId: string, paymentMethodId: string) {
    return await this.stripeService.setDefaultPaymentMethod(
      customerId,
      paymentMethodId,
    );
  }
}
