import {
  Body,
  Controller,
  Get,
  HttpException,
  Param,
  Patch,
  Post,
  Query,
  Res,
} from '@nestjs/common';
import { Response } from 'express';
import { BillingService } from './billing.service';
import { OrganizationService } from 'src/organization/services/organization.service';
import { ApiOperation } from '@nestjs/swagger';
import { ChoosePlanDto } from './dto/ChoosePlanDto';
import { ChangeBillingEmailDto } from './dto/ChangeBillingEmailDto';
import { CancelPlanDto } from './dto/CancelPlanDto';
import { StartPlanDto } from './dto/StartPlanDto';
import { HttpStatusCode } from 'axios';

@Controller('billing')
export class BillingController {
  constructor(
    private readonly billingService: BillingService,
    private readonly organizationService: OrganizationService,
  ) {}

  @Post('plan/start')
  @ApiOperation({
    summary:
      'Start a stripe instance. This is performed only once that is during the creation of an organization.',
  })
  async startPlan(@Body() payload: StartPlanDto, @Res() response: Response) {
    try {
      const plan = await this.billingService.startPlan(payload);
      return response.json({
        message: 'Added the organization to Stripe successfully.',
      });
    } catch (error) {
      if (error instanceof HttpException) {
        return response
          .status(error.getStatus())
          .json({ message: error.getResponse() });
      } else {
        return response
          .status(HttpStatusCode.InternalServerError)
          .json({ error: 'Internal Server Error' });
      }
    }
  }

  @Post('choose-plan')
  @ApiOperation({ summary: 'Choose a billing plan' })
  async choosePlan(
    @Body() choosePlanDto: ChoosePlanDto,
    @Res() response: Response,
  ) {
    try {
      const res = await this.billingService.choosePlan(choosePlanDto);
      return response
        .status(200)
        .json({ message: 'Plan updated successfully' });
    } catch (error) {
      return response.json({ message: 'Internal server error' });
    }
  }
  @Post('cancel-plan')
  @ApiOperation({ summary: 'Cancel the billing plan' })
  async cancelPlan(
    @Body() cancelPlanDto: CancelPlanDto,
    @Res() response: Response,
  ) {
    try {
      const res = await this.billingService.cancelPlan(cancelPlanDto);
      return response
        .status(200)
        .json({ message: 'Plan cancelled successfully' });
    } catch (error) {
      return response.json({ message: 'Internal server error' });
    }
  }
  @Post('update-email')
  @ApiOperation({ summary: 'Change the billing email' })
  async updateEmail(
    @Body() choosePlanDto: ChangeBillingEmailDto,
    @Res() response: Response,
  ) {
    try {
      const res = await this.billingService.updateEmail(choosePlanDto);
      return response
        .status(200)
        .json({ message: 'Billing email updated successfully' });
    } catch (error) {
      return response.json({ message: 'Internal server error' });
    }
  }

  @Post('test/email')
  @ApiOperation({ summary: 'Testing the stripe customer finder service' })
  async updateStripe(
    @Body() payload: { email: string },
    @Res() response: Response,
  ) {
    try {
      const res = await this.billingService.findV2Users(payload);
      return response
        .status(200)
        .json({ message: 'Test performed successfully' });
    } catch (error) {
      return response.json({ message: 'Internal server error' });
    }
  }

  @Get('stripe_customer_portal_link')
  @ApiOperation({ summary: 'Get the stripe customer portal link' })
  async getStripeCustomerPortalLink(
    @Query('orgId') orgId: string,
    @Res() response: Response,
  ) {
    try {
      const org = await this.organizationService.getOrganization(
        { _id: orgId },
        { billing: 1 },
      );
      const { billing } = org;
      const { customerId } = billing;
      const link = await this.billingService.getStripeCustomerPortalLink(
        customerId,
      );
      return response.status(200).json({ link });
    } catch (error) {
      return response.json({ message: 'Internal server error' });
    }
  }

  @Post('payment-method')
  @ApiOperation({ summary: 'Add a payment method' })
  async addPaymentMethod(
    @Body() payload: { orgId: string },
    @Res() response: Response,
  ) {
    try {
      const { billing } = await this.organizationService.getOrganization({
        _id: payload.orgId,
      });

      const res = await this.billingService.addPaymentMethod(
        billing.customerId,
      );
      return response.status(200).json(res);
    } catch (error) {
      return response.json({ message: 'Internal server error' });
    }
  }

  @Post('llm')
  @ApiOperation({ summary: 'Accept payments for LLM' })
  async paymentForLlm(
    @Body() payload: { orgId: string; amount: number },
    @Res() response: Response,
  ) {
    const org = await this.organizationService.getOrganization(
      { _id: payload.orgId },
      { billing: 1 },
    );
    const { billing } = org;
    const { customerId } = billing;
    const link = await this.billingService.createCheckoutForLlm(
      customerId,
      payload.amount,
    );
    return response.status(200).json({ link });
  }

  @Get('payment-methods/:orgId')
  @ApiOperation({ summary: 'Get the payment methods' })
  async getPaymentMethods(
    @Param('orgId') orgId: string,
    @Res() response: Response,
  ) {
    try {
      const { billing } = await this.organizationService.getOrganization(
        { _id: orgId },
        { billing: 1 },
      );

      const res = await this.billingService.getStripePaymentMethods(
        billing.customerId,
      );
      return response.status(200).json(res);
    } catch (error) {
      return response.json({ message: 'Internal server error' });
    }
  }

  @Patch('payment-methods/set-default')
  @ApiOperation({ summary: 'Set the default payment method' })
  async setDefaultPaymentMethod(
    @Body() payload: { orgId: string; paymentMethodId: string },
    @Res() response: Response,
  ) {
    try {
      const { billing } = await this.organizationService.getOrganization(
        { _id: payload.orgId },
        { billing: 1 },
      );

      const res = await this.billingService.setDefaultPaymentMethod(
        billing.customerId,
        payload.paymentMethodId,
      );
      return response.status(200).json(res);
    } catch (error) {
      return response.json({ message: 'Internal server error' });
    }
  }

  /* @Post('script')
  async script(@Body() payload: Object, @Res() response: Response) {
    try {
      let orgs = await this.organizationService.getOrgs({ query: { "billing.customerId": "cus_Na2tG8KTeIKGwf" }, projection: { 'billing': 1 } });
      for (const org of orgs) {
        if (org.billing.email) {
          const res = await this.billingService.updatePlanForOldAccounts({ email: org.billing.email });
          if (res) {
            const result = await this.organizationService.updateOrganisation({ _id: org._id }, res);
          }
        }
      }

      orgs = await this.organizationService.getOrgs({ query: {}, projection: { 'billing': 1 } });
      for (const org of orgs) {
        if (org.billing.email) {
          const res = await this.billingService.updatePlanForOldAccounts({ email: org.billing.email });
          if (res) {
            const result = await this.organizationService.updateOrganisation({ _id: org._id }, res);
          }
        }
      }
      return response
        .status(200)
        .json({ message: 'Script performed successfully' });
    } catch (error) {
      return response.json({ message: 'Internal server error' });
    }
  } */
}
