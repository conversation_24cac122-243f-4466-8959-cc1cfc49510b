export const PLANS = {
  TRIAL: 'trial',
  BASIC: 'basic',
  PRO: 'pro',
  ENTERPRISE: 'enterprise',
  CUSTOMPLAN1: 'customPlan1', //This is the plan for Extendly and Uphex users
};

export type TPlanName =
  | typeof PLANS.TRIAL
  | typeof PLANS.BASIC
  | typeof PLANS.PRO
  | typeof PLANS.ENTERPRISE
  | typeof PLANS.CUSTOMPLAN1;

type TPlanDetails = {
  price: number;
  agents: number;
  name: string;
  price_id: string;
  price_id_yearly?: string;
  limits?: {
    maxAgents: number;
  };
  excludedFeatures?: string[];
};

export const planMap: Record<TPlanName, TPlanDetails> = {
  trial: {
    price: 0,
    agents: 1,
    name: 'Trial',
    price_id: process.env.STRIPE_PRICE_ID_TRIAL,
    limits: {
      maxAgents: 1,
    },
    excludedFeatures: [],
  },
  basic: {
    price: 99,
    agents: 1,
    name: 'Basic',
    price_id: process.env.STRIPE_PRICE_ID_BASIC,
    price_id_yearly: process.env.STRIPE_PRICE_ID_BASIC_YEARLY,
    limits: {
      maxAgents: 1,
    },
    excludedFeatures: [],
  },
  pro: {
    price: 149,
    agents: 10,
    name: 'Pro',
    price_id: process.env.STRIPE_PRICE_ID_PRO,
    price_id_yearly: process.env.STRIPE_PRICE_ID_PRO_YEARLY,
    limits: {
      maxAgents: 10,
    },
    excludedFeatures: [],
  },
  enterprise: {
    price: 297,
    agents: 200,
    name: 'Enterprise',
    price_id: process.env.STRIPE_PRICE_ID_ENTERPRISE,
    price_id_yearly: process.env.STRIPE_PRICE_ID_ENTERPRISE_YEARLY,
    limits: {
      maxAgents: 200,
    },
    excludedFeatures: [],
  },
  customPlan1: {
    price: 0,
    agents: 10000,
    name: 'Custom Plan for Extendly and Uphex Users',
    price_id: process.env.STRIPE_PRICE_ID_CUSTOMPLAN1,
    price_id_yearly: process.env.STRIPE_PRICE_ID_CUSTOMPLAN1_YEARLY,
    limits: {
      maxAgents: 10000,
    },
    excludedFeatures: [],
  },
};
