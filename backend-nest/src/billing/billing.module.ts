import { Module, forwardRef } from '@nestjs/common';
import { BillingController } from './billing.controller';
import { BillingService } from './billing.service';
import { OrganizationModule } from 'src/organization/organization.module';
import { StripeService } from './stripe.service';

@Module({
  imports: [forwardRef(() => OrganizationModule)],
  controllers: [BillingController],
  providers: [BillingService, StripeService],
  exports: [StripeService, BillingService],
})
export class BillingModule {}
