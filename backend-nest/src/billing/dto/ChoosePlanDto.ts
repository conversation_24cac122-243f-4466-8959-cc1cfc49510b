import { IsString, <PERSON>NotEmpty, IsBoolean, IsIn } from 'class-validator';
import { TPlanName } from '../billing.map';

export class ChoosePlanBasicDto {
  @IsString()
  @IsNotEmpty()
  readonly planName: TPlanName;

  @IsString()
  @IsNotEmpty()
  readonly orgId: string;

  @IsString()
  @IsNotEmpty()
  @IsIn(['monthly', 'yearly'])
  readonly billingCycle: string;
}

export class ChoosePlanDto extends ChoosePlanBasicDto {
  @IsBoolean()
  @IsNotEmpty()
  readonly havePaymentInfo: boolean;

  @IsString()
  @IsNotEmpty()
  readonly customerId: string;

  @IsString()
  @IsNotEmpty()
  readonly orgId: string;
}
