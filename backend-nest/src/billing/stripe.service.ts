import {
  BadRequestException,
  HttpException,
  HttpStatus,
  Injectable,
  RawBodyRequest,
} from '@nestjs/common';
import { planMap } from './billing.map';
import Stripe from 'stripe';
import { StartPlanDto } from './dto/StartPlanDto';
import { getMaxAgents } from 'src/lib/utils';
import { MyLogger } from 'src/logger/logger.service';
import { LLM_PAYMENT_MESSAGE } from './billing.constant';
import axios from 'axios';

@Injectable()
export class StripeService {
  private stripe: Stripe;
  constructor(private readonly myLogger: MyLogger) {
    this.stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {
      apiVersion: '2023-08-16',
    });
  }
  getStripeInstance() {
    return this.stripe;
  }
  async startPlan(payload: StartPlanDto) {
    const { email, name, orgId } = payload;
    const stripe = this.getStripeInstance();

    const customer = await stripe.customers.create({
      email,
      name,
      metadata: {
        orgId,
      },
    });
    const subscriptionObj = payload.isCustomPlan1 ? {
      customer: customer.id,
      items: [{ plan: planMap.customPlan1.price_id }]
    } : {
      customer: customer.id,
      items: [{ plan: planMap.trial.price_id }],
      trial_period_days: 14,
    }
    const subscription = await stripe.subscriptions.create(subscriptionObj);

    return {
      customerId: customer.id,
      subscriptionId: subscription.id,
    };
  }
  async choosePlan(payload: {
    havePaymentInfo: boolean;
    price_id_final: string;
    customerId: string;
    orgId: string;
    bypassStripeCheckout?: boolean;
  }) {
    try {
      const { havePaymentInfo, price_id_final, customerId, orgId, bypassStripeCheckout } = payload;
      this.myLogger.log({
        message: { havePaymentInfo, price_id_final, customerId, orgId },
      });
      const createStripeCheckoutSession = !bypassStripeCheckout && !havePaymentInfo;
      if (createStripeCheckoutSession) {
        const session = await this.stripe.checkout.sessions.create({
          payment_method_types: ['card'],
          line_items: [
            {
              price: price_id_final,
              quantity: 1,
            },
          ],
          mode: 'subscription',
          allow_promotion_codes: true,
          metadata: { orgId },
          cancel_url: `${process.env.FRONTEND_URL}/home/<USER>/upgrade?success=false`,
          success_url: `${process.env.BACKEND_URL}/organization/billing/success?session_id={CHECKOUT_SESSION_ID}&price_id=${price_id_final}`,
          customer: customerId,
        });
        return { checkoutSessionId: session.id };
      } else {
        const subscriptions = await this.stripe.subscriptions.list({
          customer: customerId,
        });
        this.myLogger.log({
          message:
            'Subscriptions \n' +
            { subscriptions: JSON.stringify(subscriptions) },
        });
        if (subscriptions.data.length > 0) {
          const subscription = await this.stripe.subscriptions.update(
            subscriptions.data[0].id,
            {
              items: [
                {
                  id: subscriptions.data[0].items.data[0].id,
                  price: price_id_final,
                },
              ],
            },
          );
          this.myLogger.log({
            message:
              'Updated subscription \n' +
              { subscription: JSON.stringify(subscription) },
          });
          return { subscriptionId: subscription.id };
        } else {
          const newSubscription = await this.stripe.subscriptions.create({
            customer: customerId,
            items: [
              {
                price: price_id_final,
              },
            ],
          });
          this.myLogger.log({
            message:
              'Created new subscription \n' +
              { subscription: JSON.stringify(newSubscription) },
          });
          return { subscriptionId: newSubscription.id };
        }
      }
    } catch (error) {
      this.myLogger.log({
        message: `Error in choosePlan: ${error.message}`,
      });
      throw error;
    }
  }
  async cancelPlan(payload: { orgId: string; subscriptionId: string }) {
    const { subscriptionId } = payload;
    const response = await this.stripe.subscriptions.cancel(subscriptionId);
    return response;
  }
  async changeUserEmail(payload: { email: string }) {
    return true;
  }
  async viewInvoiceHistory(payload: { orgId: string }) {
    return true;
  }
  async findCustomerSubscriptionsByEmail(payload: { email: string }) {
    const { email } = payload;
    const customers = await this.stripe.customers.list({ email });
    if (!customers) {
      // throw new Error('Customer not found.');
      return null;
    }
    for (const customer of customers.data) {
      const customerId = customer.id;
      const subscriptions = await this.stripe.subscriptions.list({
        customer: customerId,
      });
      for (const subscription of subscriptions.data) {
        for (const item of subscription.items.data) {
          if (item.price) {
            const priceId = item.price.id;
            // We only want to migrate the customer_id if they are on a paid plan
            if (priceId === planMap.trial.price_id) {
              this.myLogger.log({
                message:
                  'Found a trial price ID \n' +
                  {
                    subscription_id: subscription.id,
                  },
              });
              continue;
            }
            //check if the priceId is among the ones in the stripe plan IDs (expect trial)
            for (const key of Object.keys(planMap)) {
              if (planMap[key].price_id === priceId) {
                const body = {
                  key,
                  priceId,
                  customerId,
                  allowedAgents: planMap[key].agents,
                  subscription_id: subscription.id,
                };
                this.myLogger.log({
                  message: 'Found an exisiting non-trial plan \n' + body,
                });
                return body;
              }
            }
          }
        }
      }
    }
    return null;
  }
  async handleStripeSuccess(payload: { session_id: string }) {
    const { session_id } = payload;
    const session = await this.stripe.checkout.sessions.retrieve(session_id);
    // const priceId = session.display_items[0].price.id;
    // const price_id = session.
    const orgId = session.metadata.orgId;
    const customer_id = session.customer;
    const subscription_id = session.subscription;
    this.myLogger.log({
      message: { subscription_id, orgId, customer_id },
    });

    return { subscription_id, orgId, customer_id };
  }
  async getStripeCustomerPortalLink(customerId: string) {
    const customerPortalLink = await this.stripe.billingPortal.sessions.create({
      customer: customerId,
      return_url: `${process.env.FRONTEND_URL}/home/<USER>
    });
    this.myLogger.log({
      message: { customerPortalLink },
    });
    return customerPortalLink.url;
  }

  async getStripeDetails(payload: { customerId: string }) {
    const { customerId } = payload;

    // Retrieve the customer's subscriptions
    const subscriptions = await this.stripe.subscriptions.list({
      customer: customerId,
    });
    this.myLogger.log({
      message: 'Inside subscription\n' + subscriptions,
    });

    // Loop through the subscriptions to find the next payment date or expiration date
    for (let subscription of subscriptions.data) {
      if (
        subscription.status === 'active' ||
        subscription.status === 'trialing'
      ) {
        this.myLogger.log({
          message:
            'Next payment for subscription ' +
            subscription.id +
            ' is due on ' +
            new Date(subscription.current_period_end * 1000),
        });
        return {
          nextPaymentDate: new Date(subscription.current_period_end * 1000),
          billingExpired: false,
        };
      } else if (
        subscription.status === 'canceled' ||
        subscription.status === 'past_due'
      ) {
        this.myLogger.log({
          message:
            'Subscription ' +
            subscription.id +
            ' expired on ' +
            new Date(subscription.ended_at * 1000),
        });
      }
      return { nextPaymentDate: null, billingExpired: true };
    }
  }

  async getCustomerCurrentStatus(payload: { customerId: string }) {
    const { customerId } = payload;
    let subscriptions;
    try {
      subscriptions = await this.stripe.subscriptions.list({
        customer: customerId,
      });
    } catch (error) {
      this.myLogger.error({
        message: `Customer with ID ${customerId} not found on Stripe.`,
      });
      return { billingExpired: true }; // Continue with the rest of your code
    }
    let flag = 'suspended';
    for (let subscription of subscriptions.data) {
      this.myLogger.log({
        message: {
          customerId,
          subscription_status: subscription.status,
          subscription_id: subscription.id,
          due: new Date(
            subscription.current_period_end * 1000,
          ).toLocaleString(),
          plan_name: subscription.plan,
        },
      });
      if (subscription.status === 'active') {
        flag = 'active';
      } else if (
        subscription.status === 'canceled' ||
        subscription.status === 'past_due'
      ) {
        this.myLogger.log({
          message:
            'Subscription ' +
            subscription.id +
            ' expired on ' +
            new Date(subscription.ended_at * 1000),
        });
      }
      if (flag === 'active') {
        return { billingExpired: false };
      } else {
        return { billingExpired: true };
      }
    }
  }

  async SubscriptionExpired(req: RawBodyRequest<Request>, sig: string) {
    let event;
    try {
      const rawReqBody = req.rawBody;
      event = await this.stripe.webhooks.constructEventAsync(
        rawReqBody,
        sig,
        process.env.STRIPE_WEBHOOK_SECRET_PLAN_UPDATE,
      );
      this.myLogger.log({
        message: { stripeSubscriptionUpdateRequestBody: event },
      });
    } catch (err) {
      this.myLogger.error({
        message: `⚠️  Webhook signature verification failed.` + err,
      });
      throw new HttpException(
        'Webhook signature verification failed.',
        HttpStatus.BAD_REQUEST,
      );
    }
    if (event.type === 'customer.subscription.deleted') {
      // handle subscription cancelled automatically based upon your subscription settings. Or if the user cancels it.
      const subscription = event.data.object;
      this.myLogger.log({
        message: 'Subscription deleted \n' + { subscription },
      });

      // Get the customer ID
      const customerId = subscription.customer;
      return { customerId, status: 'suspended' };
    } else if (event.type === 'customer.subscription.updated') {
      const subscription = event.data.object;

      if (subscription.cancel_at_period_end) {
        this.myLogger.log({
          message: "Subscription will be canceled at period's end.",
        });

        const customerId = subscription.customer;
        return { customerId, status: 'cancelled' };
      } else if (
        !subscription.cancel_at_period_end &&
        subscription.status === 'active'
      ) {
        const priceId = subscription.items.data[0].price.id;
        this.myLogger.log({
          message: 'Subscription reactivated. Price_id: ' + priceId,
        });
        const allowedAgents = getMaxAgents(priceId);
        const customerId = subscription.customer;
        return { customerId, status: 'active', allowedAgents, priceId };
      }
    }
    return null;
  }

  async LlmCredsAdded(req: RawBodyRequest<Request>, sig: string) {
    let event;
    try {
      const rawReqBody = req.rawBody;
      event = await this.stripe.webhooks.constructEventAsync(
        rawReqBody,
        sig,
        process.env.STRIPE_WEBHOOK_SECREt_LLM_CREDS_ADDED,
      );
      this.myLogger.log({
        message: { event },
      });
    } catch (err) {
      this.myLogger.error({
        message: `⚠️  Webhook signature verification failed.` + err,
      });
      throw new HttpException(
        'Webhook signature verification failed.',
        HttpStatus.BAD_REQUEST,
      );
    }

    if (
      event.type === 'payment_intent.succeeded' &&
      event.data.object.status === 'succeeded'
    ) {
      const paymentIntent = event.data.object;
      this.myLogger.log({
        message: 'Payment intent succeeded \n' + { paymentIntent },
      });

      // Check if the payment description matches 'Capri Hosted LLM Payment'
      if (paymentIntent.description === LLM_PAYMENT_MESSAGE) {
        // Get the customer ID and the amount
        const customerId = paymentIntent.customer;
        const amount = paymentIntent.amount_received / 100; // Convert to dollars
        

        return { customerId, amount };
      } else {
        this.myLogger.log({
          message: 'Payment succeeded but was not for Capri Hosted LLM Payment',
        });
      }
    }
    return { customerId: null, amount: null };
  }

  async getAllProducts(stripe_api_key) {
    const stripe = new Stripe(stripe_api_key, {
      apiVersion: '2023-08-16',
    });
    const products = await stripe.products.list();
    return products;
  }

  async getPriceIds(stripe_api_key: string, product_id: string) {
    const stripe = new Stripe(stripe_api_key, {
      apiVersion: '2023-08-16',
    });
    const prices = await stripe.prices.list({ product: product_id });
    return prices;
  }

  async getCustomerDetails(customerId: string, stripe_api_key: string) {
    const stripe = new Stripe(stripe_api_key, {
      apiVersion: '2023-08-16',
    });
    const customer = await stripe.customers.retrieve(customerId);
    return customer;
  }

  async createSubscription(
    stripe_api_key: string,
    stripe_customer_id: string,
    stripe_price_id: string,
  ): Promise<{
    stripe_subscription_item_id: string;
    stripe_unit_price_amount: number;
  } | null> {
    const stripe = new Stripe(stripe_api_key, {
      apiVersion: '2023-08-16',
    });
    const subscription = await stripe.subscriptions.create({
      customer: stripe_customer_id,
      items: [{ price: stripe_price_id }],
    });

    const price = await stripe.prices.retrieve(stripe_price_id);
    const stripe_unit_price_amount = price.unit_amount;

    return {
      stripe_subscription_item_id: subscription.items.data[0].id,
      stripe_unit_price_amount,
    };
  }

 

  async getMeteredSubscriptionEarnings(
    stripe_api_key: string,
    stripe_subscription_item_id: string,
  ) {
    const stripe = new Stripe(stripe_api_key, {
      apiVersion: '2023-08-16',
    });
    const subscriptionItem = await stripe.subscriptionItems.retrieve(
      stripe_subscription_item_id,
    );
    const price = subscriptionItem.price.unit_amount;

    const usageRecords =
      await stripe.subscriptionItems.listUsageRecordSummaries(
        stripe_subscription_item_id,
      );
    const totalUsage = usageRecords.data.reduce(
      (total, record) => total + record.total_usage,
      0,
    );

    const totalEarnings = totalUsage * price;

    return totalEarnings;
  }

  async getStripeAccount(stripe_api_key: string) {
    const stripe = new Stripe(stripe_api_key, {
      apiVersion: '2023-08-16',
    });
    const account = await stripe.accounts.retrieve();
    return {
      account_id: account.id,
    };
  }

  async checkSameStripeAccount(
    stripe_api_key_old: string,
    stripe_api_key_new: string,
  ) {
    const stripeOld = new Stripe(stripe_api_key_old, {
      apiVersion: '2023-08-16',
    });
    const stripeNew = new Stripe(stripe_api_key_new, {
      apiVersion: '2023-08-16',
    });
    

    const accountOld = await stripeOld.accounts.retrieve();
    
    const accountNew = await stripeNew.accounts.retrieve();
    
    

    return accountOld.id === accountNew.id;
  }

  // async createCustomerChoosesPriceCheckout(customerId: string, amount: number) {
  //   try {
  //     const priceId = process.env.STRIPE_PRICE_ID_LLM;
  

  //     const session = await this.stripe.checkout.sessions.create({
  //       payment_method_types: ['card'],
  //       line_items: [
  //         {
  //           quantity: 1,
  //           price_data: {
  //             currency: 'usd',
  //             product_data: {
  //               name: 'Capri Hosted LLM',
  //             },
  //             unit_amount: amount * 100,
  //           },
  //         },
  //       ],
  //       mode: 'payment',
  //       customer: customerId,
  //       success_url: `${process.env.FRONTEND_URL}/home/<USER>
  //       cancel_url: `${process.env.FRONTEND_URL}/home/<USER>
  //     });
  //     

  //     return session;
  //   } catch (error) {
  //     
  //   }
  // }

  async createCustomerChoosesPriceCheckout(customerId: string, amount: number) {
    if (!!!customerId) {
      

      throw new BadRequestException('Customer ID is required');
    }
    if (!!!amount) {
      throw new BadRequestException('Amount is required');
    }

    // Create a SetupIntent to save a payment method for the customer
    const setupIntent = await this.stripe.setupIntents.create({
      customer: customerId,
    });
    

    // Once the card details have been saved and you have a payment method ID,
    // you can create a PaymentIntent to confirm the payment
    const paymentMethod = await this.getDefaultPaymentMethods(customerId);
    

    const paymentMethodId = paymentMethod.id;
    const paymentIntent = await this.stripe.paymentIntents.create({
      amount: amount * 100,
      currency: 'usd',
      customer: customerId,
      payment_method: paymentMethodId,
      off_session: true,
      confirm: true,
      description: LLM_PAYMENT_MESSAGE,
    });

    // Return the PaymentIntent's client secret to your client-side application
    return { paymentIntentClientSecret: paymentIntent.client_secret };
  }
  async getDefaultPaymentMethods(customerId: string) {
    const customer = (await this.stripe.customers.retrieve(
      customerId,
    )) as Stripe.Customer;
    const defaultPaymentMethodId =
      customer.invoice_settings.default_payment_method;

    if (defaultPaymentMethodId) {
      const defaultPaymentMethod = await this.stripe.paymentMethods.retrieve(
        defaultPaymentMethodId as string,
      );
      return defaultPaymentMethod;
    }

    return null;
  }

  async createPaymentIntent(customerId: string, amount: number) {
    try {
      // Create a SetupIntent to save a payment method for the customer
      const setupIntent = await this.stripe.setupIntents.create({
        customer: customerId,
      });

      // Send the SetupIntent's client secret to your client-side application
      // Your client-side application will use this to collect and save card details
      

      // Once the card details have been saved and you have a payment method ID,
      // you can create a PaymentIntent to confirm the payment
      const paymentMethod = await this.getDefaultPaymentMethods(customerId);
      const paymentMethodId = paymentMethod.id;
      const paymentIntent = await this.stripe.paymentIntents.create({
        amount: amount * 100,
        currency: 'usd',
        customer: customerId,
        payment_method: paymentMethodId,
        off_session: true,
        confirm: true,
        description: LLM_PAYMENT_MESSAGE,
      });

      // Check the status of the PaymentIntent
      if (paymentIntent.status === 'requires_action') {
        // The payment requires additional steps (like 3D Secure authentication)
        // Send the PaymentIntent's client secret to your client-side application
        
        this.myLogger.log({
          message: 'Payment requires additional steps' + { paymentIntent },
        });
        throw new Error('Payment requires additional steps');
      } else if (paymentIntent.status === 'succeeded') {
        // The payment has been processed successfully
        
        return paymentIntent;
      } else {
        // The payment failed
        
        throw new Error('Payment failed');
      }
    } catch (error) {
      
      throw error;
    }
  }

  async getStripePaymentMethods(customerId: string) {
    const paymentMethods = await this.stripe.paymentMethods.list({
      customer: customerId,
      type: 'card',
    });

    if (paymentMethods.data.length === 0) {
      return [];
    }

    const customer = (await this.stripe.customers.retrieve(
      customerId,
    )) as Stripe.Customer;
    const defaultPaymentMethodId =
      customer.invoice_settings.default_payment_method;

    const data = paymentMethods.data.map((paymentMethod) => {
      return {
        id: paymentMethod.id,
        brand: paymentMethod.card?.brand,
        last4: paymentMethod.card?.last4,
        exp_month: paymentMethod.card?.exp_month,
        exp_year: paymentMethod.card?.exp_year,
        isDefault: paymentMethod.id === defaultPaymentMethodId,
      };
    });
    return data;
  }

  async setDefaultPaymentMethod(customerId: string, paymentMethodId: string) {
    const customer = await this.stripe.customers.update(customerId, {
      invoice_settings: {
        default_payment_method: paymentMethodId,
      },
    });

    return customer;
  }

  async addPaymentMethod(customerId: string) {
    const session = await this.stripe.checkout.sessions.create({
      payment_method_types: ['card'],
      mode: 'setup',
      customer: customerId,
      cancel_url: `${process.env.FRONTEND_URL}/home/<USER>
      success_url: `${process.env.FRONTEND_URL}/home/<USER>
    });

    return session;
  }

  async addUsageToSubscription(
    stripe_api_key: string,
    stripe_subscription_item_id: string,
    quantity: number,
    stripe_customer_id,
    stripe_price_id
  ) {
    try {
      if (quantity <= 0) {
        return null;
      }
      const stripe = new Stripe(stripe_api_key, {
        apiVersion: '2023-08-16',
      });
      const usageRecord = await stripe.subscriptionItems.createUsageRecord(
        stripe_subscription_item_id,
        {
          quantity,
          timestamp: Math.floor(Date.now() / 1000),
          action: 'increment',
        },
      );
      return usageRecord;
    } catch (error) {
      // Get the event_name dynamically from the price and meter
      const eventName = await this.getEventNameFromPrice(stripe_api_key, stripe_price_id);
      await this.sendMeterEvent(stripe_api_key, eventName, quantity, stripe_customer_id);
    }
   }

  async getStripePrice(stripe_api_key: string, price_id: string) {
    try {
      const stripe = new Stripe(stripe_api_key, {
        apiVersion: '2023-08-16',
      });
      const price = await stripe.prices.retrieve(price_id);
      return price;
    } catch (error) {
      this.myLogger.error({
        message: `Error retrieving Stripe price: ${error.message}`,
      });
      throw error;
    }
  }

  async getStripeMeter(stripe_api_key: string, meter_id: string) {
    try {
      const response = await axios.get(
        `https://api.stripe.com/v1/billing/meters/${meter_id}`,
        {
          auth: {
            username: stripe_api_key,
            password: '',
          },
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        },
      );
      return response.data;
    } catch (error) {
      this.myLogger.error({
        message: `Error retrieving Stripe meter: ${error.message}`,
      });
      throw error;
    }
  }

  async getEventNameFromPrice(stripe_api_key: string, price_id: string): Promise<string> {
    try {
      // Get price details to extract meter ID
      const price = await this.getStripePrice(stripe_api_key, price_id);
      
      // Type assertion to access meter property
      const recurring = price.recurring as any;
      let event_name = 'unit';
      if (recurring?.meter) {
        // Get meter details to extract event_name
        const meter = await this.getStripeMeter(stripe_api_key, recurring.meter);
        event_name = meter.event_name;
      }
      return event_name;
    } catch (error) {
      this.myLogger.error({
        message: `Error getting event name from price ${price_id}: ${error.message}`,
      });
      throw error;
    }
  }

  async sendMeterEvent(
    stripe_api_key: string,
    event_name: string,
    value: number,
    stripe_customer_id: string,
    identifier?: string,
  ) {
    try {
      // Create form data in the correct format for Stripe's API
      const params = new URLSearchParams();
      params.append('event_name', event_name);
      params.append('payload[value]', value.toString());
      params.append('payload[stripe_customer_id]', stripe_customer_id);
      if (identifier) {
        params.append('identifier', identifier);
      }
      
      const response = await axios.post(
        'https://api.stripe.com/v1/billing/meter_events',
        params,
        {
          auth: {
            username: stripe_api_key,
            password: '',
          },
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        },
      );
      
      this.myLogger.log({
        message: `Successfully sent meter event: ${event_name} for customer: ${stripe_customer_id}`,
      });
      
      return response.data;
    } catch (error) {
      this.myLogger.error({
        message: `Error sending meter event: ${error.message}, details: ${JSON.stringify(error.response?.data || {})}`,
      });
      throw error;
    }
  }
}
