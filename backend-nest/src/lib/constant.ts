interface IObjects {
  [key: string]: string;
}

const BASE_URL = 'https://services.leadconnectorhq.com';

let pricing = {
  models: {
    OpenAI: [
      {
        name: 'GPT-4',
        input_cost_per_million_tokens: 30,
        output_cost_per_million_tokens: 60,
      },
      {
        name: 'GPT-3.5 Turbo',
        input_cost_per_million_tokens: 0.5,
        output_cost_per_million_tokens: 1.5,
      },
      {
        name: 'GPT-3.5 Turbo 16k',
        input_cost_per_million_tokens: 3,
        output_cost_per_million_tokens: 4,
      },
      {
        name: 'o1-preview',
        input_cost_per_million_tokens: 15,
        output_cost_per_million_tokens: 60,
      },
      {
        name: 'o1-mini',
        input_cost_per_million_tokens: 3,
        output_cost_per_million_tokens: 12,
      },
      {
        name: 'GPT-4o',
        input_cost_per_million_tokens: 2.5,
        output_cost_per_million_tokens: 10,
      },
      {
        name: 'GPT-4o Mini',
        input_cost_per_million_tokens: 0.15,
        output_cost_per_million_tokens: 0.6,
      },
    ],
    An<PERSON><PERSON>_<PERSON>: [
      {
        name: 'Claude 3.5 Sonnet',
        input_cost_per_million_tokens: 3,
        output_cost_per_million_tokens: 15,
      },
      {
        name: '<PERSON> 3 Opus',
        input_cost_per_million_tokens: 15,
        output_cost_per_million_tokens: 75,
      },
      {
        name: 'Claude 3 Haiku',
        input_cost_per_million_tokens: 0.25,
        output_cost_per_million_tokens: 1.25,
      },
      {
        name: '<PERSON> 3 Sonnet',
        input_cost_per_million_tokens: 3,
        output_cost_per_million_tokens: 15,
      },
      {
        name: 'Claude 2.1',
        input_cost_per_million_tokens: 8,
        output_cost_per_million_tokens: 24,
      },
      {
        name: 'Claude 2.0',
        input_cost_per_million_tokens: 8,
        output_cost_per_million_tokens: 24,
      },
      {
        name: 'Claude Instant',
        input_cost_per_million_tokens: 0.8,
        output_cost_per_million_tokens: 2.4,
      },
    ],
    Google_Gemini: [
      {
        name: 'Gemini 1.5 Pro',
        input_cost_description: 'Varies by type',
        output_cost_description: 'Varies by type',
      },
      {
        name: 'Gemini 1.0 Pro',
        input_cost_per_million_tokens: 2.5,
        output_cost_per_million_tokens: 0.375,
      },
    ],
    Groq_Models: [
      {
        name: 'Llama 3.1 70B Versatile',
        input_cost_per_million_tokens: 0.59,
        output_cost_per_million_tokens: 0.79,
      },
      {
        name: 'Llama 3.1 8B Instant',
        input_cost_per_million_tokens: 0.05,
        output_cost_per_million_tokens: 0.08,
      },
      {
        name: 'Llama 3 70B',
        input_cost_per_million_tokens: 0.59,
        output_cost_per_million_tokens: 0.79,
      },
      {
        name: 'Llama 3 8B',
        input_cost_per_million_tokens: 0.05,
        output_cost_per_million_tokens: 0.08,
      },
      {
        name: 'Mixtral 8x7B Instruct',
        input_cost_per_million_tokens: 0.24,
        output_cost_per_million_tokens: 0.24,
      },
      {
        name: 'Gemma 7B Instruct',
        input_cost_per_million_tokens: 0.07,
        output_cost_per_million_tokens: 0.07,
      },
    ],
    Capri_Hosted_LLMs: [
      {
        name: 'llama-v3-70b-instruct',
        COST_PER_MILLION: 1,
      },
      {
        name: 'llama-v3_1-70b-instruct',
        COST_PER_MILLION: 2,
      },
      {
        name: 'llama-v3_2-1b-instruct',
        COST_PER_MILLION: 0.15,
      },
      {
        name: 'llama-v3_2-3b-instruct',
        COST_PER_MILLION: 0.15,
      },
      {
        name: 'llama-v3_2-11b-vision-instruct',
        COST_PER_MILLION: 0.3,
      },
      {
        name: 'llama-v3_2-90b-vision-instruct',
        COST_PER_MILLION: 2,
      },
    ],
  },
};

export const CUSTOM_LLM = {
  NAME: 'capriHostedLlm',
  FRONTEND_NAME: 'llama-v3-70b-instruct',
  PROVIDERS: 'fireworks',
  MODEL: 'accounts/fireworks/models/llama-v3-70b-instruct-hf',
  PUBLIC_MODEL_NAME: 'Llama 3-70B - Capri Hosted',
  API_KEY: process.env.FIREWORKS_API_KEY || '',
  COST_PER_MILLION: 1, // $1 per million tokens
};

export const CUSTOM_LLM_2 = {
  NAME: 'capriHostedLlm2',
  FRONTEND_NAME: 'llama-v3_1-70b-instruct',
  PROVIDERS: 'fireworks',
  MODEL: 'accounts/fireworks/models/llama-v3p1-70b-instruct',
  PUBLIC_MODEL_NAME: 'Llama 3.1-70B - Capri Hosted',
  API_KEY: process.env.FIREWORKS_API_KEY || '',
  COST_PER_MILLION: 2, // $2 per million tokens
};

export const CUSTOM_LLM_3 = {
  NAME: 'capriHostedLlm3',
  FRONTEND_NAME: 'llama-v3_2-1b-instruct',
  PROVIDERS: 'fireworks',
  MODEL: 'accounts/fireworks/models/llama-v3p2-1b-instruct',
  PUBLIC_MODEL_NAME: 'Llama 3.2-1B - Capri Hosted',
  API_KEY: process.env.FIREWORKS_API_KEY || '',
  COST_PER_MILLION: 0.15, // $0.15 per million tokens
};

export const CUSTOM_LLM_4 = {
  NAME: 'capriHostedLlm4',
  FRONTEND_NAME: 'llama-v3_2-3b-instruct',
  PROVIDERS: 'fireworks',
  MODEL: 'accounts/fireworks/models/llama-v3p2-3b-instruct',
  PUBLIC_MODEL_NAME: 'Llama 3.2-3B - Capri Hosted',
  API_KEY: process.env.FIREWORKS_API_KEY || '',
  COST_PER_MILLION: 0.15, // $0.15 per million tokens
};

export const CUSTOM_LLM_5 = {
  NAME: 'capriHostedLlm5',
  FRONTEND_NAME: 'llama-v3_2-11b-vision-instruct',
  PROVIDERS: 'fireworks',
  MODEL: 'accounts/fireworks/models/llama-v3p2-11b-vision-instruct',
  PUBLIC_MODEL_NAME: 'Llama 3.2-11B - Capri Hosted',
  API_KEY: process.env.FIREWORKS_API_KEY || '',
  COST_PER_MILLION: 0.3, // $0.3 per million tokens
};

export const CUSTOM_LLM_6 = {
  NAME: 'capriHostedLlm6',
  FRONTEND_NAME: 'llama-v3_2-90b-vision-instruct',
  PROVIDERS: 'fireworks',
  MODEL: 'accounts/fireworks/models/llama-v3p2-90b-vision-instruct',
  PUBLIC_MODEL_NAME: 'Llama 3.2-90B - Capri Hosted',
  API_KEY: process.env.FIREWORKS_API_KEY || '',
  COST_PER_MILLION: 2, // $2 per million tokens
};

export const CUSTOM_LLM_7 = {
  NAME: 'capriHostedLlm7',
  FRONTEND_NAME: 'gpt-4o',
  PROVIDERS: 'openai-hosted',
  MODEL: 'gpt-4o',
  PUBLIC_MODEL_NAME: '4O - Capri Hosted',
  API_KEY: process.env.OPENAI_API_KEY || '',
  COST_PER_MILLION: 9.68, // $9.68 per million tokens
};

export const CUSTOM_LLM_8 = {
  NAME: 'capriHostedLlm8',
  FRONTEND_NAME: 'gpt-4o-mini',
  PROVIDERS: 'openai-hosted',
  MODEL: 'gpt-4o-mini',
  PUBLIC_MODEL_NAME: '4O Mini - Capri Hosted',
  API_KEY: process.env.OPENAI_API_KEY || '',
  COST_PER_MILLION: 0.56, // $0.56 per million tokens
};

export const CUSTOM_LLM_9 = {
  NAME: 'capriHostedLlm9',
  FRONTEND_NAME: 'deepseek/deepseek-chat',
  PROVIDERS: 'openrouter',
  MODEL: 'deepseek/deepseek-chat',
  PUBLIC_MODEL_NAME: 'DeepSeek Chat - Capri Hosted',
  API_KEY: process.env.OPENAI_API_KEY || '',
  COST_PER_MILLION: 0.35, // $0.35 per million tokens
};

export const customLlmNames = [
  CUSTOM_LLM.NAME,
  CUSTOM_LLM_2.NAME,
  CUSTOM_LLM_3.NAME,
  CUSTOM_LLM_4.NAME,
  CUSTOM_LLM_5.NAME,
  CUSTOM_LLM_6.NAME,
  CUSTOM_LLM_7.NAME,
  CUSTOM_LLM_8.NAME,
  CUSTOM_LLM_9.NAME,
];

export const customLlmProviders = [
  'fireworks',
  'openai-hosted',
  'openrouter',
  'groq-hosted',
];

export const isCustomLlmProvider = (providerName: string) => {
  return customLlmProviders.includes(providerName);
};

export const getCustomLlmApiKey = (providerName: string) => {
  let apiKey = '';
  if (providerName === 'fireworks') {
    apiKey = process.env.FIREWORKS_API_KEY;
  } else if (providerName === 'openai-hosted') {
    apiKey = process.env.OPENAI_API_KEY;
  } else if (providerName === 'openrouter') {
    apiKey = process.env.OPENROUTER_API_KEY;
  } else if (providerName === 'groq-hosted') {
    apiKey = process.env.GROQ_API_KEY;
  }

  return apiKey;
};

export const llmModelMap = [
  { frontendName: CUSTOM_LLM.FRONTEND_NAME, modelName: CUSTOM_LLM.MODEL },
  { frontendName: CUSTOM_LLM_2.FRONTEND_NAME, modelName: CUSTOM_LLM_2.MODEL },
  { frontendName: CUSTOM_LLM_3.FRONTEND_NAME, modelName: CUSTOM_LLM_3.MODEL },
  { frontendName: CUSTOM_LLM_4.FRONTEND_NAME, modelName: CUSTOM_LLM_4.MODEL },
  { frontendName: CUSTOM_LLM_5.FRONTEND_NAME, modelName: CUSTOM_LLM_5.MODEL },
  { frontendName: CUSTOM_LLM_6.FRONTEND_NAME, modelName: CUSTOM_LLM_6.MODEL },
  { frontendName: CUSTOM_LLM_7.FRONTEND_NAME, modelName: CUSTOM_LLM_7.MODEL },
  { frontendName: CUSTOM_LLM_8.FRONTEND_NAME, modelName: CUSTOM_LLM_8.MODEL },
];

export const GHL_API: IObjects = {
  GET_ACCESS_TOKEN_URL: BASE_URL + '/oauth/token',
  GET_BUSINESS_BY_LOCATION_URL: BASE_URL + '/businesses/',
  GET_CALENDARS_URL: BASE_URL + '/calendars/',
};

export const PROVIDERS = {
  GHL_CHANNEL: 'ghl',
  GHL_CALENDAR: 'ghlCalendar',
  GOOGLE_SHEET: 'googleSheet',
  CHATHQ_CHANNEL: 'chathq',
  OPENAI_PROVIDER: 'openai',
  CLAUDE_PROVIDER: 'claude',
  FIREWORKS_PROVIDER: 'fireworks',
  GROQ_PROVIDER: 'groq',
  VOGAGEAI_PROVIDER: 'voyageai',
  GOOGLE_CALENDAR: 'googleCalendar',
  GOOGLE_GEMINI: 'gemini',
  CALENDLY: 'calendly',
  WEBSITE: 'website',
  PODIUM: 'podium',
  ACUITY: 'acuity',
  GOOGLE_DOCS: 'googleDocs',
  Generate_JSON: 'generateJson',
  QnA: 'qna',
  PDF: 'pdf',
  CustomField: 'customField',
  uphex: 'uphex',
  HUBSPOT: 'hubspot',
  GET: 'get',
  POST: 'post',
  SLACK: 'slack',
  OPENAI_HOSTED: 'openai-hosted',
  FIREWORKS: 'fireworks',
  SMS: 'sms',
  GHL_EMAIL: 'ghlEmail',
  OUTLOOK_NYLAS: 'outlook',
  OPENROUTER_PROVIDER: 'openrouter',
  GROQ_HOSTED: 'groq-hosted',
  STANDARD_FIELD: 'standardField',
};

export const KINDS = {
  GHL_CREDENTIAL: 'GhlCredential',
  GOOGLE_CREDENTIAL: 'GoogleCredential',
  OPENAI_CREDENTIAL: 'OpenaiCredential',
  CLAUDE_CREDENTIAL: 'ClaudeCredential',
  GROQ_CREDENTIAL: 'GroqCredential',
  GEMINI_CREDENTIAL: 'GeminiCredential',
  CALENDLY_CREDENTIAL: 'CalendlyCredential',
  ACUITY_CREDENTIAL: 'AcuityCredential',
  CHATHQ_CREDENTIAL: 'ChathqCredential',
  UPHEX_CREDENTIAL: 'UphexCredential',
  PODIUM_CREDENTIAL: 'PodiumCredential',
  HUBSPOT_CREDENTIAL: 'HubspotCredential',
  FIREWORKS_CREDENTIAL: 'FireworksCredential',
  SLACK_CREDENTIAL: 'SlackCredential',
  OUTLOOK_NYLAS_CREDENTIAL: 'NylasCredential',
} as const;

export type KindType = (typeof KINDS)[keyof typeof KINDS];

export const CONNECTION_TYPES = {
  CHANNEL: 'channel',
  DATASOURCES: 'datasources',
  AIPROVIDER: 'aiProvider',
};

export const STATUS = {
  ACTIVE: 'active',
  INACTIVE: 'inactive',
  PENDING: 'pending',
  FAILED: 'failed',
  SUCCESS: 'success',
  DELIVERED: 'delivered',
};

export const CHANNELS = [PROVIDERS.GHL_CHANNEL, PROVIDERS.PODIUM];

export const modelMaps = {
  chatgpt: 'gpt-3.5-turbo',
  'gpt-4': 'gpt-4',
  gpt4: 'gpt-4',
  'gpt-3.5-turbo': 'gpt-3.5-turbo',
};

export const claudeModelMaps: Record<string, string> = {
  'Claude 3 - Opus': 'claude-3-opus-20240229',
  'Claude 3 - Sonnet': 'claude-3-sonnet-20240229',
  'Claude 2.1': 'claude-2.1',
  'Claude 2.0': 'claude-2.0',
  'Claude Instant 1.2': 'claude-instant-1.2',
};

export const groqModelMaps: Record<string, string> = {
  'llama2 - 70b': 'llama2-70b-4096',
  'mixtral - 8x7b': 'mixtral-8x7b-32768',
  'gemma - 7b': 'gemma-7b-it',
};

export const GHL_MESSAGE_TYPE_MAP = {
  2: 'SMS',
  3: 'Email',
  15: 'GMB',
  18: 'IG',
  11: 'FB',
  19: 'WhatsApp',
  29: 'Live_Chat',
};

export enum MessagingTypes {
  SMS = 'SMS',
  EMAIL = 'Email',
  GMB = 'GMB',
  IG = 'IG',
  FB = 'FB',
  WhatsApp = 'WhatsApp',
  Live_Chat = 'Live_Chat',
}

export enum TriggerTasks {
  OUTREACH = 'outreach',
  RESPOND = 'respond',
  EVALUATE = 'evaluate',
}

export const GHL_ALL_MESSAGE_TYPES = {
  TYPE_CALL: 1,
  TYPE_SMS: 2,
  TYPE_EMAIL: 3,
  TYPE_SMS_REVIEW_REQUEST: 4,
  TYPE_WEBCHAT: 29,
  TYPE_SMS_NO_SHOW_REQUEST: 6,
  TYPE_CAMPAIGN_SMS: 7,
  TYPE_CAMPAIGN_CALL: 8,
  TYPE_CAMPAIGN_EMAIL: 9,
  TYPE_CAMPAIGN_VOICEMAIL: 10,
  TYPE_FACEBOOK: 11,
  TYPE_CAMPAIGN_FACEBOOK: 12,
  TYPE_CAMPAIGN_MANUAL_CALL: 13,
  TYPE_CAMPAIGN_MANUAL_SMS: 14,
  TYPE_GMB: 15,
  TYPE_CAMPAIGN_GMB: 16,
  TYPE_REVIEW: 17,
  TYPE_INSTAGRAM: 18,
};

export const CRMTYPE = {
  EMULATOR: 'emulator',
  GHL: 'ghl',
};

export const billing = {
  openai: {
    'gpt-3.5-turbo-0125': {
      inputTokens: 0.0000005,
      outputTokens: 0.0000015,
    },
    'gpt-3.5-turbo-0613': {
      inputTokens: 0.0000015,
      outputTokens: 0.000002,
    },
    'gpt-3.5-turbo-instruct': {
      inputTokens: 0.0000015,
      outputTokens: 0.000002,
    },
    'gpt-4-turbo': {
      inputTokens: 0.00001,
      outputTokens: 0.00003,
    },
    'gpt-4-1106-preview': {
      inputTokens: 0.00001,
      outputTokens: 0.00003,
    },
    'gpt-4-1106-vision-preview': {
      inputTokens: 0.00001,
      outputTokens: 0.00003,
    },
    'gpt-4': {
      inputTokens: 0.00003,
      outputTokens: 0.00006,
    },
    'gpt-3.5-turbo-16k': {
      inputTokens: 0.000003,
      outputTokens: 0.000004,
    },
    'gpt-4-32k': {
      inputTokens: 0.00006,
      outputTokens: 0.00012,
    },
    'gpt-4o': {
      inputTokens: 0.000005,
      outputTokens: 0.000015,
    },
    'gpt-4o-2024-05-13': {
      inputTokens: 0.000005,
      outputTokens: 0.000015,
    },
    'gpt-3.5-turbo': {
      inputTokens: 0.000003,
      outputTokens: 0.000006,
    },
    'gpt-4o-mini': {
      inputTokens: 0.********,
      outputTokens: 0.0000006,
    },
    o1: {
      inputTokens: 0.0000525,
      outputTokens: 0.0000525,
    },
    'o3-mini': {
      inputTokens: 0.00000385,
      outputTokens: 0.00000385,
    },
  },
  anthropic: {
    'claude-2.1': {
      inputTokens: 0.000008,
      outputTokens: 0.000024,
    },
    'claude-2.0': {
      inputTokens: 0.000008,
      outputTokens: 0.000024,
    },
    'claude-instant-1.2': {
      inputTokens: 0.0000008,
      outputTokens: 0.0000024,
    },
    'claude-3-sonnet-20240229': {
      inputTokens: 0.000003,
      outputTokens: 0.000015,
    },
    'claude-3-opus-20240229': {
      inputTokens: 0.000015,
      outputTokens: 0.000075,
    },
    'claude-3-5-sonnet-20240620': {
      inputTokens: 0.000003,
      outputTokens: 0.000015,
    },
    'claude-3-7-sonnet-20250219': {
      inputTokens: 0.000003,
      outputTokens: 0.000015,
    },
    'claude-3-5-haiku-20241022': {
      inputTokens: 0.00000025,
      outputTokens: 0.00000125,
    },
    'claude-3-5-sonnet-latest': {
      inputTokens: 0.000003,
      outputTokens: 0.000015,
    },
  },
  groq: {
    'gemma-7b-it': {
      inputTokens: 0.00000007,
      outputTokens: 0.00000007,
    },
    'llama3-70b-8192': {
      inputTokens: 0.00000059,
      outputTokens: 0.00000079,
    },
    'llama3-8b-8192': {
      inputTokens: 0.00000005,
      outputTokens: 0.00000008,
    },
    'mixtral-8x7b-32768': {
      inputTokens: 0.00000024,
      outputTokens: 0.00000024,
    },
    'gemma2-9b-it': {
      inputTokens: 0.00000007,
      outputTokens: 0.00000007,
    },
    'llama-3.3-70b-versatile': {
      inputTokens: 0.00000059,
      outputTokens: 0.00000079,
    },
  },
  google: {
    'gemini-1.5-pro': {
      inputTokens: 0.0000035,
      outputTokens: 0.0000105,
    },
    'gemini-1.5-flash': {
      inputTokens: 0.********,
      outputTokens: 0.0000003,
    },
    'gemini-1.0-pro': {
      inputTokens: 0.0000005,
      outputTokens: 0.0000015,
    },
    'gemini-2.0-flash': {
      inputTokens: 0.0000004,
      outputTokens: 0.0000004,
    },
  },
  openrouter: {
    'deepseek/deepseek-chat': {
      inputTokens: 0.00000096,
      outputTokens: 0.00000096,
    },
    'deepseek/deepseek-r1': {
      inputTokens: 0.0000077,
      outputTokens: 0.0000077,
    },
    'openai/o3-mini': {
      inputTokens: 0.00000385,
      outputTokens: 0.00000385,
    },
    'openai/o1': {
      inputTokens: 0.0000525,
      outputTokens: 0.0000525,
    },
    'google/gemini-2.0-flash-001': {
      inputTokens: 0.0000004,
      outputTokens: 0.0000004,
    },
    'anthropic/claude-3.7-sonnet': {
      inputTokens: 0.0000126,
      outputTokens: 0.0000126,
    },
    'perplexity/r1-1776': {
      inputTokens: 0.000007,
      outputTokens: 0.000007,
    },
  },
  'groq-hosted': {
    'qwen-2.5-32b': {
      inputTokens: 0.000001,
      outputTokens: 0.000001,
    },
  },
  'openai-hosted': {
    'gpt-4o': {
      inputTokens: 0.********,
      outputTokens: 0.********,
    },
    'gpt-4o-mini': {
      inputTokens: 0.********,
      outputTokens: 0.********,
    },
  },
  fireworks: {
    'accounts/fireworks/models/llama-v3p1-70b-instruct': {
      inputTokens: 0.000002,
      outputTokens: 0.000002,
    },
    'accounts/fireworks/models/llama-v3p2-1b-instruct': {
      inputTokens: 0.********,
      outputTokens: 0.********,
    },
    'accounts/fireworks/models/llama-v3p2-3b-instruct': {
      inputTokens: 0.********,
      outputTokens: 0.********,
    },
    'accounts/fireworks/models/llama-v3p2-11b-vision-instruct': {
      inputTokens: 0.0000003,
      outputTokens: 0.0000003,
    },
    'accounts/fireworks/models/llama-v3p2-90b-vision-instruct': {
      inputTokens: 0.000002,
      outputTokens: 0.000002,
    },
  },
};

export const OPTIMIZEOPTIONS = {
  COST: 'cost',
  ACCURACY: 'accuracy',
};

export const CAPRIAIPROVIDER = {
  COMPANYID: 'fireworks',
  MODELNAME: 'llama-v3-70b-instruct',
  ACCOUNTNAME: 'Capri Hosted LLM',
  ACCOUNTID: 'capriHostedLlm',
};

export const MIN_BALANCE_FOR_VOICE = 5; //dollars

export const MAP_PROVIDER_TO_KINDS = {
  [PROVIDERS.GHL_CALENDAR]: KINDS.GHL_CREDENTIAL,
  [PROVIDERS.GHL_CHANNEL]: KINDS.GHL_CREDENTIAL,
  [PROVIDERS.GOOGLE_CALENDAR]: KINDS.GOOGLE_CREDENTIAL,
  [PROVIDERS.GOOGLE_DOCS]: KINDS.GOOGLE_CREDENTIAL,
  [PROVIDERS.GOOGLE_SHEET]: KINDS.GOOGLE_CREDENTIAL,
  [PROVIDERS.OPENAI_PROVIDER]: KINDS.OPENAI_CREDENTIAL,
  [PROVIDERS.CLAUDE_PROVIDER]: KINDS.CLAUDE_CREDENTIAL,
  [PROVIDERS.FIREWORKS_PROVIDER]: KINDS.FIREWORKS_CREDENTIAL,
  [PROVIDERS.GROQ_PROVIDER]: KINDS.GROQ_CREDENTIAL,
  [PROVIDERS.GOOGLE_GEMINI]: KINDS.GEMINI_CREDENTIAL,
  [PROVIDERS.ACUITY]: KINDS.ACUITY_CREDENTIAL,
  [PROVIDERS.CALENDLY]: KINDS.CALENDLY_CREDENTIAL,
  [PROVIDERS.PODIUM]: KINDS.PODIUM_CREDENTIAL,
};

export enum THIRD_PARTY_APPS {
  UPHEX = 'uphex',
}

export const VISIBILITY_CODES = {
  AGENT: {
    CREATE: 'agent.create',
    CONVERSATION: 'agent.conversation',
  },
  PLAYGROUND: {
    CHAT: 'playground.chat',
    UPDATE_RESPONSE: 'playground.updateResponse',
  },
  KNOWLEDGE: {
    FILES: 'knowledge.files',
    FAQS: 'knowledge.faqs',
    TEXT: 'knowledge.text',
    WEBSITE: 'knowledge.website',
  },
  ACTIONS: {
    CRM_CALENDAR: 'actions.crmCalendar',
    CONTACT_ACTIONS: 'action.contactActions',
  },
  TRIGGERS: 'triggers',
  SETTINGS: {
    GENERAL: 'settings.general',
    CAN_ACTIVATE: 'settings.canActivate',
    AI: 'settings.ai',
  },
} as const;

export type VisibilityCode =
  (typeof VISIBILITY_CODES)[keyof typeof VISIBILITY_CODES];
