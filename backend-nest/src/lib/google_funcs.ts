import { GoogleSpreadsheet, GoogleSpreadsheetRow, GoogleSpreadsheetWorksheet } from 'google-spreadsheet';
import { JWT } from 'google-auth-library'
import { BadRequestException, ForbiddenException, NotFoundException } from '@nestjs/common';
import { google } from 'googleapis';


export const getGoogleDoc = async (docId: string) => {
  try {

     const auth = new google.auth.GoogleAuth({
       keyFile: 'google-credentials.json',
       scopes: ['https://www.googleapis.com/auth/documents.readonly'],
     });
    const docs = google.docs({ version: 'v1', auth });
    const doc = await docs.documents.get({
      documentId: docId,
    });

    if (!doc.data || !doc.data.title) {
      throw new NotFoundException('No document found with the given id');
    }
    return doc.data;
  } catch (err) {
    
    if (err.message.includes('The caller does not have permission')) {
      throw new BadRequestException(
        `The service account doesn't have access to this document. Please grant access.`,
      );
    } else if (err.message.includes(`400`)) {
      throw new BadRequestException(err.message);
    }
    else {
      throw new Error(err.message);
    }
  }
};



export const getGoogleSheet = async (sheetId: string): Promise<GoogleSpreadsheet> => {
    try {
        const auth = new JWT({
            email: process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL,
            key: process.env.GOOGLE_PRIVATE_KEY.replace(/\\n/g, "\n"),
            scopes: ['https://www.googleapis.com/auth/spreadsheets'],
        });
        const doc = new GoogleSpreadsheet(sheetId, auth);
        await doc.loadInfo();
        if (doc && doc.title == null || doc.sheetCount == 0) {
            throw new NotFoundException('No sheet found with the given id');
        }
        return doc;
    } catch (err) {
        
        if ((err.message || "").includes("The caller does not have permission")) throw new BadRequestException(`Capri doesn't have access to this sheet. Please grant access to access this sheet with capri`);
        else if (err.message.includes(`not supported`)){
            throw new BadRequestException(`File formats like xlsx are not supported in Google Sheets import. Please convert the file to Google Sheets and try again.`);
        }
        else throw new Error(err.message);
        // throw new BadRequestException(`Capri doesn't have access to this sheet`);
    }
}

/**
 * @example convertSheetRange('sheet1!A5:D5')
 * @returns range = { rowNumber: 5, columns: ['A', 'D'] }
 */
export const convertSheetRange = (range: string) => {
    const rowNumber = parseInt(range.match(/\d+/)[0]);// Output: 2
    const [startColumn, endColumn] = (range.split('!')[1] || "").replace(/\d+/g, '').split(':');
    const columns = [];
    for (let i = startColumn.charCodeAt(0); i <= endColumn.charCodeAt(0); i++) {
        columns.push(String.fromCharCode(i));
    }
    return { rowNumber, columns };
}

export const getSheetCellVal = async (row: GoogleSpreadsheetRow, column: string, sheet: GoogleSpreadsheetWorksheet/* , columns: string[] */) => {

    try {
        let v = row.get(column);
        v = (v ? `${v}` : "").replace(/(\r\n|\n|\r)/gm, " ");
        if (!v) {
            let range = row.a1Range;
            const rowNumber = parseInt(range.match(/\d+/)[0]);
            const colIdx = sheet.headerValues.indexOf(column);
            v = sheet.getCell(rowNumber - 1, colIdx).value;
            v = (v ? `${v}` : "").replace(/(\r\n|\n|\r)/gm, " ");
        }
        return v;
    } catch (err) {
        return "";
    }
}