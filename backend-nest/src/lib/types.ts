export interface TimeRange {
    start: string; // Format: "HH:mm" (24-hour format)
    end: string;   // Format: "HH:mm" (24-hour format)
}

export interface Schedule {
    monday?: TimeRange[];
    tuesday?: TimeRange[];
    wednesday?: TimeRange[];
    thursday?: TimeRange[];
    friday?: TimeRange[];
    saturday?: TimeRange[];
    sunday?: TimeRange[];
}

export const daysOfWeek = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
