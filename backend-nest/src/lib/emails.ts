export function generateWelcomeEmail({
    appName,
    userName,
    onboardingUrl,
    supportMail,
    temporaryPassword
  }: {
    appName: string,
    userName: string,
    onboardingUrl: string,
    supportMail: string,
    temporaryPassword: string
  }) {
    return `<!DOCTYPE html>
  <html>
  <head>
      <style>
          @media only screen and (max-width: 600px) {
              .container { width: 100% !important; }
          }
      </style>
  </head>
  <body style="margin: 0; padding: 20px; font-family: Arial, sans-serif;">
      <table width="100%" border="0" cellspacing="0" cellpadding="0">
          <tr>
              <td align="center">
                  <table class="container" width="600" border="0" cellspacing="0" cellpadding="0" style="border: 1px solid #e0e0e0; border-radius: 8px;">
                      <tr>
                          <td style="padding: 30px; background-color: #f8f9fa; border-bottom: 1px solid #e0e0e0;">
                              <h1 style="color: #2d3436; margin: 0;">Welcome to ${appName}! 🎉</h1>
                          </td>
                      </tr>
                      <tr>
                          <td style="padding: 30px;">
                              <p style="color: #636e72;">Hi ${userName},</p>
                              <p style="color: #636e72;">Thank you for joining ${appName}! We're excited to help you...</p>
                              
                              <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
                                  <p style="color: #636e72; margin: 0;">Your temporary password is: <strong>${temporaryPassword}</strong></p>
                                  <p style="color: #636e72; margin: 10px 0 0 0; font-size: 0.9em;">Please change this password when you first log in.</p>
                              </div>
  
                              <div style="margin: 30px 0;">
                                  <a href="${onboardingUrl}" 
                                     style="background-color: #0984e3; color: white; 
                                            padding: 12px 24px; 
                                            border-radius: 5px; 
                                            text-decoration: none;
                                            display: inline-block;">
                                      Start Your Journey →
                                  </a>
                              </div>
                          </td>
                      </tr>
                      <tr>
                          <td style="padding: 20px; background-color: #f8f9fa; text-align: center;">
                              <p style="color: #636e72; font-size: 0.9em;">
                                  Need help? Contact our support at 
                                  <a href="mailto:${supportMail}" style="color: #0984e3;">${supportMail}</a>
                              </p>
                          </td>
                      </tr>
                  </table>
              </td>
          </tr>
      </table>
  </body>
  </html>`;
  }