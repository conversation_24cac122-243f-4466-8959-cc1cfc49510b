import { planMap } from 'src/billing/billing.map';
import { Tiktoken } from "tiktoken";
import { daysOfWeek, Schedule } from './types';
const cl100k_base = require("tiktoken/encoders/cl100k_base.json");
const { DateTime } = require('luxon');
import * as moment from 'moment-timezone';

export const decodeCode = (
  code: string,
): { orgId: string; userName: string; usid: string; userId: string } => {
  const stateFromGhlRedirectUrl = code; // Replace with the actual state value received from the redirect URL
  const decodedState = Buffer.from(
    stateFromGhlRedirectUrl.replace(/-/g, '+').replace(/_/g, '/'),
    'base64',
  ).toString('utf8');
  const decoded = JSON.parse(decodedState);
  return decoded;
};

export function getDomainFromUrl(inputUrl: string) {
  try {
    // Parse the URL using the url module
    const parsedUrl = new URL(inputUrl);
    // Extract and return the domain
    return parsedUrl.hostname;
  } catch (error) {
    
    return null;
  }
}

/**
 * generate incluive range of numbers
 */
export function generateRange(start, end) {
  if (start > end) return [];
  return Array.from({ length: end - start + 1 }, (_, i) => start + i);
}

export function isValidURL(url) {
  // Regular expression for validating an Url
  var urlRegex = /^(https?|ftp):\/\/([^\s/$.?#].[^\s]*)$/;
  return urlRegex.test(url);
}

export function convertToValidURL(url) {
  // If the URL does not start with a valid scheme, add 'http://' in front of it
  if (!/^https?:\/\//i.test(url)) {
    url = 'http://' + url;
  }

  // If "www" is not present, add it after the scheme
  if (!/www\./i.test(url)) {
    url = url.replace(/^(https?:\/\/)/i, '$1www.');
  }

  return url;
}

export function parseSheetId(url: string) {
  const regex = /\/d\/([a-zA-Z0-9-_]+)\//;
  const match = url.match(regex);
  if (match && match[1]) {
    return match[1];
  } else {
    
  }
}

export const isValidHeaderRange = (inputValue: string): boolean => {
  //takes in a string and returns a boolean depending whether that is a valid range or not (for example: A1:B1)
  if (inputValue === '') return true;
  const regex = /^([A-Z]+)(\d+):([A-Z]+)(\d+)$/;
  const match = inputValue.match(regex);

  if (!match) return false;

  const [_, startColumn, startRow, endColumn, endRow] = match;
  return startColumn <= endColumn && parseInt(startRow) <= parseInt(endRow);
};

export const removeImportedSessionPrefix = (
  inputString: string,
): string[][] => {
  try {
    const messages = inputString.split('Contact: ').slice(1);
    const result = messages.map((message) => {
      const [contact, assistant] = message.split('Assistant: ');
      return [contact.trim(), assistant.trim()];
    });
    return result;
  } catch (err) {
    
    const m = inputString.split(/(?=Contact:)/g);
    const result = m.map((message) => {
      const [contact, assistant] = (message || ' \n ').split('\n');
      return [contact.trim(), assistant.trim()];
    });
    return result;
  }
};

export function formatUserAssistantString(sentence) {
  try {
    const words = sentence.split(' ');
    let firstColonIndex = -1;
    let secondColonIndex = -1;

    for (let i = 0; i < words.length; i++) {
      if (words[i].includes(':')) {
        if (firstColonIndex === -1) {
          firstColonIndex = i;
        } else {
          secondColonIndex = i;
          break;
        }
      }
    }

    if (firstColonIndex !== -1 && secondColonIndex !== -1) {
      words[firstColonIndex - 1] = 'user:';
      words[secondColonIndex - 1] = 'assistant:';
      return words.join(' ');
    } else {
      return sentence;
    }
  } catch (err) {
    return sentence;
  }
}


export function getMaxAgents(priceId: string): number | undefined {
  for (const plan of Object.values(planMap)) {
    if (plan.price_id === priceId || plan.price_id_yearly === priceId) {
      return plan.limits?.maxAgents;
    }
  }
}

export function sleep(ms) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}


export function epochToISOString(epochTimestamp, timeZoneOffset) {
  const date = moment.unix(epochTimestamp / 1000);
  const isoString = date.tz(timeZoneOffset).format('YYYY-MM-DDTHH:mm:ssZ');
  return isoString;
}

export function timeToISOString(
  time: string,
  timeZone: string,
  date: moment.Moment,
): string {
  const [hours, minutes] = time.split(':').map(Number);
  const utcOffset = moment.tz(timeZone).utcOffset();
  return date
    .clone()
    .set({ hours, minutes })
    .utcOffset(utcOffset)
    .toISOString();
}
export function isOverlap(start1, end1, start2, end2) {
  return !(end1 <= start2 || end2 <= start1);
}

export function generateSlots(
  startTime: moment.Moment,
  endTime: moment.Moment,
  increment: number,
  start: moment.Moment,
  end: moment.Moment,
): { start: string; end: string }[] {
  const slots: { start: string; end: string }[] = [];
  let currentTime = moment(startTime);

  while (currentTime.isBefore(endTime)) {
    const slotStart = currentTime.format('YYYY-MM-DDTHH:mm:ssZ');
    const slotEnd = currentTime
      .add(increment, 'minutes')
      .format('YYYY-MM-DDTHH:mm:ssZ');

    if (
      moment(slotStart).isBetween(start.format(), end.format(), null, '[]') &&
      moment(slotEnd).isBetween(start.format(), end.format(), null, '[]')
    ) {
      slots.push({ start: slotStart, end: slotEnd });
    }
  }

  return slots;
}


export function encodeObjectToBase64(obj) {
  const jsonString = JSON.stringify(obj);
  const base64String = btoa(jsonString);
  return base64String;
}

export function decodeBase64ToObject<T>(base64String): T {
  const jsonString = atob(base64String);
  const obj = JSON.parse(jsonString);
  return obj;
}

/**
 * @params text `
[system prompt]

[Task]
- tool name : tools info
` 
 * @returns  ["tool name, tool info"]
 */
export function extractBulletPoints(text: string) {
  const lines = text?.split('\n');
  // Filter lines that start with '- ' and trim extra spaces
  const bulletPoints = (lines ?? [])
    .filter(line => line.trim().startsWith('- '))
    .map(line => line.trim().substring(2)); // Remove the '- ' from the start
  return bulletPoints;
}

export function getCustomFieldSubstring(input) {
  const cleanedInput = input.replace(/{{|}}/g, '').trim();
  const parts = cleanedInput.split('.');
  if (parts.length >= 2) {
    return parts[1]; // Return the second substring
  } else {
    return input; // Return null if there is no second substring
  }
}

export function isIsoDate(str) {
  return /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z$/.test(str);
}

export async function numberOfTokens(completeString) {
  const encoding = new Tiktoken(
    cl100k_base.bpe_ranks,
    cl100k_base.special_tokens,
    cl100k_base.pat_str
  );
  const length = encoding.encode(completeString);
  encoding.free();
  return length;
}

export function createActionElement(actions) {
  const actionElem = {
    accountName: actions.accountName,
    action: actions.action,
    kind: actions.kind,
    silent: actions?.silent || false,
  };

  if (actions.calendarId) actionElem['calendarId'] = actions.calendarId;
  if (actions.locationId) actionElem['locationId'] = actions.locationId;
  if (actions.eventData?.tag?.name)
    actionElem['tagName'] = actions?.eventData?.tag?.name;
  if (actions.eventData?.startDate)
    actionElem['startDate'] = actions.eventData.startDate;
  if (actions.eventData?.endDate)
    actionElem['endDate'] = actions.eventData.endDate;
  if (actions.errors) actionElem['error'] = actions.errors;
  return actionElem;
}

export async function isEventAllowedInScheduledTime(eventTime: Date, schedule: Schedule, userTimeZone: string): Promise<boolean> {
  try {
    // Convert the event time to the user's timezone
    const eventMoment = moment(eventTime).tz(userTimeZone);
    // Get the day of the week in lowercase
    const dayOfWeek = eventMoment.format('dddd').toLowerCase();
    // Get the time ranges for the current day
    const daySchedule = schedule?.[dayOfWeek as keyof Schedule];

    // If no schedule is defined for this day, return false
    if (!daySchedule || daySchedule.length === 0) {
      return true;
    }

    // Get the current time in HH:mm format
    const currentTime = eventMoment?.format('HH:mm');

    // Check if the current time falls within any of the scheduled time ranges
    return daySchedule.some(timeRange => {
      // Parse the start and end times
      const startTime = timeRange?.start;
      const endTime = timeRange?.end;
      if (!startTime || !endTime) {
        return false;
      }
      // Check if current time is within the range
      // Need to handle cases where the end time might be on the next day
      if (endTime < startTime) {
        // Time range spans midnight
        return currentTime >= startTime || currentTime < endTime;
      } else {
        // Normal time range within the same day
        return currentTime >= startTime && currentTime < endTime;
      }
    });
  } catch (error) {
    this.myLogger.error({
      message: `Error in filtering the event for the schedule configuration -> ${error?.message}`,
      context: `filter date time`
    })
    return true;
  }
}