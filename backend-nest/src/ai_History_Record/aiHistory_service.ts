import { Injectable } from '@nestjs/common';
import { AiHistoryRecordDto } from 'src/interfaces/interfaces';
import { MongoAiHistoryService } from './aiHistory_mongo_service';

@Injectable()
export class AiHistoryService {
  constructor(
    public readonly mongoAiHistory: MongoAiHistoryService
  ) {}

  async createAiHistory(aiHistory:AiHistoryRecordDto) {
    try {
        const aiHistory_created = await this.mongoAiHistory.createAiHistory(aiHistory);
        return aiHistory_created;
    } catch (error) {
      
      return { success: false, error: 'Failed to create AI history' };
    }
  }
}
