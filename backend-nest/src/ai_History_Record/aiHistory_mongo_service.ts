import { Injectable } from '@nestjs/common';
import { AiHistory } from 'src/mongo/schemas/aiHistory/aiHistory_schema';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { MyLogger } from 'src/logger/logger.service';
import { AiHistoryRecordDto } from 'src/interfaces/interfaces';

@Injectable()
export class MongoAiHistoryService {
  constructor(
    @InjectModel(AiHistory.name) private readonly aiHistoryModel: Model<AiHistory>,
    private readonly logger: MyLogger
  ) {}

  async createAiHistory(aiHistoryData: AiHistoryRecordDto) {
    try {
      const savedAiHistory = await this.aiHistoryModel.create(aiHistoryData);
      if(savedAiHistory){
        this.logger.log({
          message: 'Ai history created.',
          context: `${aiHistoryData.channel.channelAccountId}`
        })
      }
      return savedAiHistory;
    } catch (error) {
      // Handle error appropriately
      this.logger.error({ message: `Error in creating AI history: ${error?.message}`, context: 'MongoAiHistoryService.createAiHistory' });
      throw new Error('Failed to create AI history');
    }
  }
}
