import { Injectable, Scope } from '@nestjs/common';
import { Billing } from './mongo/schemas/organization/organization.schema';

interface IUserReq {
  userId: string;
  userName: string;
  email: string;
  organizationSetup: boolean;
}

interface IOrgReq {
  orgId: string;
  billing: Billing;
}

@Injectable({ scope: Scope.REQUEST })
export class RequestService {
  private user: IUserReq;
  private billingDetails: Billing;
  private org: IOrgReq;
  private uniqueCode: string;

  constructor() {
    
  }
  setUniqueCode(code: string) {
    this.uniqueCode = code;
  }
  getUniqueCode() {
    return this.uniqueCode;
  }

  setUser(user: IUserReq) {
    this.user = user;
  }
  getUser() {
    return this.user;
  }
  setBillingDetails(details: Billing) {
    this.billingDetails = details;
  }
  getBillingDetails() {
    return this.billingDetails;
  }
  setOrgDetails(org: IOrgReq) {
    this.org = org;
  }
  getOrgDetails() {
    return this.org;
  }
}
