import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { CreateAuditLogDto } from './dto/create-audit-log.dto';
import { UpdateAuditLogDto } from './dto/update-audit-log.dto';
import { AuditLog } from './entities/audit-log.schema';

@Injectable()
export class AuditLogService {
  constructor(
    @InjectModel(AuditLog.name) private auditLogModel: Model<AuditLog>,
  ) {}

  async create(createAuditLogDto: CreateAuditLogDto): Promise<AuditLog> {
    const createdAuditLog = new this.auditLogModel(createAuditLogDto);
    return createdAuditLog.save();
  }

  async findAll(): Promise<AuditLog[]> {
    return this.auditLogModel.find().exec();
  }

  async findOne(id: string): Promise<AuditLog> {
    return this.auditLogModel.findById(id).exec();
  }

  async update(id: string, updateAuditLogDto: UpdateAuditLogDto): Promise<AuditLog> {
    return this.auditLogModel.findByIdAndUpdate(id, updateAuditLogDto, { new: true }).exec();
  }

  async remove(id: string): Promise<AuditLog> {
    return this.auditLogModel.findByIdAndRemove(id).exec();
  }

  async createNewConversation(contactId: string): Promise<AuditLog> {
    try {
      // Create a new empty conversation entry
      return await this.auditLogModel.findOneAndUpdate(
        { contactId },
        { $push: { conversationDetails: {
          query: '',
          prompt: '',
          finalResponse: '',
          model: '',
          actions: []
        }}},
        { new: true, upsert: true }
      ).exec();
    } catch (error) {
      
      throw error;
    }
  }
  
  async addActionByContactId(contactId: string, action: { 
    providerName: string;
    accountName: string;
    prompt: string; 
    finalResponse: string; 
    model: string 
  }): Promise<AuditLog> {
    try {
      // Get the current document to find the index of the last conversation
      const document = await this.auditLogModel.findOne({ contactId }).exec();
      if (!document || !document.conversationDetails || document.conversationDetails.length === 0) {
        throw new Error(`No conversations found for contact ${contactId}`);
      }
      
      const lastIndex = document.conversationDetails.length - 1;
      
      // Push to the actions array of the last conversation using a positional index
      return await this.auditLogModel.findOneAndUpdate(
        { contactId },
        { $push: { [`conversationDetails.${lastIndex}.actions`]: action } },
        { new: true }
      ).exec();
    } catch (error) {
      
      throw error;
    }
  }
  
  async updateLatestConversation(contactId: string, updates: {
    query?: string;
    prompt?: string;
    finalResponse?: string;
    model?: string;
  }): Promise<AuditLog> {
    try {
      // Get the current document to find the index of the last conversation
      const document = await this.auditLogModel.findOne({ contactId }).exec();
      if (!document || !document.conversationDetails || document.conversationDetails.length === 0) {
        throw new Error(`No conversations found for contact ${contactId}`);
      }
      
      const lastIndex = document.conversationDetails.length - 1;
      
      // Create update object with explicit indexes instead of arrayFilters
      const updateObj = {};
      if (updates.query !== undefined) {
        updateObj[`conversationDetails.${lastIndex}.query`] = updates.query;
      }
      if (updates.prompt !== undefined) {
        updateObj[`conversationDetails.${lastIndex}.prompt`] = updates.prompt;
      }
      if (updates.finalResponse !== undefined) {
        updateObj[`conversationDetails.${lastIndex}.finalResponse`] = updates.finalResponse;
      }
      if (updates.model !== undefined) {
        updateObj[`conversationDetails.${lastIndex}.model`] = updates.model;
      }
  
      return await this.auditLogModel.findOneAndUpdate(
        { contactId },
        { $set: updateObj },
        { new: true }
      ).exec();
    } catch (error) {
      
      throw error;
    }
  }
  
  async getAuditLogByContactId(contactId: string): Promise<AuditLog> {
    try {
      return await this.auditLogModel.findOne({ contactId }).exec();
    } catch (error) {
      
      throw error;
    }
  }
}
