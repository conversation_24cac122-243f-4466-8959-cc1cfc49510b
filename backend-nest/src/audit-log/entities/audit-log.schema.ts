import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

@Schema({ versionKey: false })
export class AuditLog extends Document {
  @Prop({ required: true })
  contactId: string;

  @Prop({
    type: [{
      _id: false,
      query: String,
      prompt: String,
      finalResponse: String,
      model: String,
      actions: {
        type: [{ providerName: String, accountName: String, prompt: String, finalResponse: String, model: String, _id: false }],
        default: [],
      },
    }],
    default: [],
  })
  conversationDetails: Array<{
    query: string;
    prompt: string;
    finalResponse: string;
    model: string;
    actions: Array<{
      providerName: string;
      accountName: string;
      prompt: string;
      finalResponse: string;
      model: string;
    }>;
  }>;
}

export const AuditLogSchema = SchemaFactory.createForClass(AuditLog);

AuditLogSchema.index({ contactId: 1 });
