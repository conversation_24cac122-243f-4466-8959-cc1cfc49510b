import { log } from 'console';
import { HttpService } from '@nestjs/axios';
import { Injectable, OnModuleInit } from '@nestjs/common';
import { CloudTasksClient, protos } from '@google-cloud/tasks';
import { MyLogger } from 'src/logger/logger.service';
import { v4 as uuidv4 } from 'uuid';

const WEBHOOK_MESSAGE_QUEUE = 'capri-webhook-messages';
const GHL_WORKFLOW_MESSAGE_QUEUE = 'capri-ghl-workflow';
const LOCATION = 'us-central1';
const PROJECT = 'dwy-master';

const GHL_WORKFLOW_URL = `${process.env.BACKEND_URL}/leadconnector/workflow/webhook`;
// service file for google cloud tasks
@Injectable()
export class TaskService {
  private client: CloudTasksClient = new CloudTasksClient({
    projectId: process.env.GCP_PROJECT_ID,
    credentials: {
      client_email: process.env.GCP_CLIENT_EMAIL,
      private_key: (process.env.GCP_PRIVATE_KEY || '')
        .split(String.raw`\n`)
        .join('\n'),
    },
  });

  constructor(
    private readonly httpService: HttpService,
    private readonly logger: MyLogger,
  ) {}

  async deleteQueue(project, location, queue) {
    // Get the fully qualified path to the queue
    const name = this.client.queuePath(project, location, queue);

    // Send delete queue request.
    await this.client.deleteQueue({ name });
    this.logger.log({
      message: `Deleted queue '${queue}'.`,
      context: 'TaskService.deleteQueue',
    });
  }

  async createQueue(project, location, queue) {
    // Construct the fully qualified location path.
    const parent = this.client.locationPath(project, location);

    // Construct the request body.
    const request = {
      parent: parent,
      queue: {
        name: this.client.queuePath(project, location, queue),
        rateLimits: {
          maxDispatchesPerSecond: 1,
        },
      },
    };

    // Send create queue request.
    const [response] = await this.client.createQueue(request);
    this.logger.log({
      message: `Created queue ${response.name}`,
      context: 'TaskService.createQueue',
    });
    return response;
  }

  async listQueues(project, location) {
    // Construct the fully qualified location path.
    const parent = this.client.locationPath(project, location);

    // Send list queues request.
    const [queues] = await this.client.listQueues({ parent });
    return queues;
  }

  async createGhlWorkflowTaskWithExponentialBackoff(
    payload,
    contactId,
    delayInSeconds = 300,
  ) {
    const parent = this.client.queuePath(
      PROJECT,
      LOCATION,
      GHL_WORKFLOW_MESSAGE_QUEUE,
    );

    const taskName = `task-${contactId}`;
    const task = {
      name: this.client.taskPath(
        PROJECT,
        LOCATION,
        GHL_WORKFLOW_MESSAGE_QUEUE,
        taskName,
      ),
      httpRequest: {
        httpMethod: 'POST',
        url: GHL_WORKFLOW_URL,
        body: Buffer.from(JSON.stringify(payload)).toString('base64'),
        headers: {
          'Content-Type': 'application/json',
        },
      },
      scheduleTime: {
        seconds: Date.now() / 1000 + delayInSeconds,
      },
      retryConfig: {
        maxAttempts: 5,
        maxRetryDuration: { seconds: 3600 },
        minBackoff: { seconds: 60 },
        maxBackoff: { seconds: 3600 },
        maxDoublings: 5,
      },
    } as protos.google.cloud.tasks.v2.ITask;

    const request: protos.google.cloud.tasks.v2.ICreateTaskRequest = {
      parent: parent,
      task: task,
    };

    try {
      const [response] = await this.client.createTask(request);
      this.logger.log({
        message: `Successfully created task ${
          response.name
        } for ${GHL_WORKFLOW_URL} with payload: ${JSON.stringify(payload)}`,
        context: contactId,
      });
      return response;
    } catch (error) {
      if (error.code === 6) {
        // ALREADY_EXISTS
        this.logger.log({
          message: `Task with name ${taskName} already exists for contactId: ${contactId}`,
          context: contactId,
        });
        return; // Skip task creation if it already exists
      }
      this.logger.error({
        message: `Failed to create task for ${GHL_WORKFLOW_URL}. Error: ${
          error.message
        }. Payload: ${JSON.stringify(payload)}`,
        context: contactId,
      });
      throw new Error(`Task creation failed: ${error.message}`);
    }
  }

  async createHttpTask(
    project,
    location,
    queue,
    url: string,
    payload,
    seconds: number,
  ) {
    // Construct the fully qualified queue path.
    const parent = this.client.queuePath(project, location, queue);

    // Construct the request body.
    const task: protos.google.cloud.tasks.v2.ITask = {
      httpRequest: {
        httpMethod: 'POST',
        url: url,
        body: Buffer.from(JSON.stringify(payload)).toString('base64'),
        headers: {
          'Content-Type': 'application/json',
        },
      },
      scheduleTime: {
        seconds: seconds + Date.now() / 1000,
      },
    };

    // Send create task request.
    const request = { parent: parent, task: task };
    const [response] = await this.client.createTask(request);
    this.logger.log({
      message: `Created task ${response.name}`,
      context: 'TaskService.createHttpTask',
    });
    return response;
  }

  async deleteTask(project, location, queue, task) {
    // Construct the fully qualified task path.
    const name = this.client.taskPath(project, location, queue, task);

    // Send delete task request.
    await this.client.deleteTask({ name });
    this.logger.log({
      message: `Deleted task ${name}`,
      context: 'TaskService.deleteTask',
    });
  }

  async listTasks(project, location, queue) {
    // Construct the fully qualified queue path.
    const parent = this.client.queuePath(project, location, queue);
    // list task with payload
    const [tasks] = await this.client.listTasks({ parent });
    return tasks;
  }

  async getTask(project, location, queue, task) {
    const name = this.client.taskPath(project, location, queue, task);
    const [response] = await this.client.getTask({ name });
    return response;
  }

  async createWebhookMessageTask(url: string, delay: number, payload) {
    const correlationId = uuidv4();
    payload.payload.correlationId = correlationId;
    const task = await this.createHttpTask(
      PROJECT,
      LOCATION,
      WEBHOOK_MESSAGE_QUEUE,
      url,
      { ...payload, correlationId },
      delay,
    );
    return { task, correlationId };
  }

  async deleteWebhookMessageTask(taskId) {
    await this.deleteTask(PROJECT, LOCATION, WEBHOOK_MESSAGE_QUEUE, taskId);
  }

  async deleteFollowUpTask(taskId) {
    try {
      await this.deleteTask(PROJECT, LOCATION, WEBHOOK_MESSAGE_QUEUE, taskId);
      this.logger.log({
        message: `Successfully deleted task for ${taskId}`,
        context: 'DELETE FOLLOW UP TASK',
      })
    } catch (error) {
      this.logger.error({
        message: `Failed to delete task for ${taskId}. Error: ${error.message}`,
        context: 'DELETE FOLLOW UP TASK',
      })
    }
  }
}
