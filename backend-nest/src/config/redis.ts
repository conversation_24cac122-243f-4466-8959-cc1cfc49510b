import { CacheModuleAsyncOptions } from '@nestjs/cache-manager';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { redisStore } from 'cache-manager-redis-store';

export const RedisOptions: CacheModuleAsyncOptions = {
  isGlobal: true,
  imports: [ConfigModule],
  useFactory: async (configService: ConfigService) => {
    try {
      const store = await redisStore({
        socket: {
          host: configService.get<string>('REDIS_HOST'),
          port: parseInt(configService.get<string>('REDIS_PORT')!),
        },
        password: configService.get<string>('REDIS_PASSWORD'),
        ttl: 15 * 60,
      });
      return {
        store: () => store,
      };
    } catch (error) {
      
      return {
        store: () => null,
      };
    }
  },
  inject: [ConfigService],
};
