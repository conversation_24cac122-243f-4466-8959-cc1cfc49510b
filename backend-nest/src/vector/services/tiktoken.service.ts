// Service for token counting/processing using Tiktoken
import { get_encoding, encoding_for_model } from "tiktoken";
import { Injectable } from '@nestjs/common';

@Injectable()
export class TiktokenService {
  constructor() {}

  async getEncoding(text: string, model = 'text-embedding-ada-002') {
    // assert(model in encoding_for_model, `Model ${model} not found`);
    const encoding = get_encoding('cl100k_base');
    const encoded = encoding.encode(text);
    encoding.free();
    return encoded;
  }

  async encodeArr(textArr: string[], model = 'text-embedding-ada-002') {
    // assert(model in encoding_for_model, `Model ${model} not found`);
    const encoding = get_encoding('cl100k_base');
    const encoded = [];
    for (const text of textArr) {
      encoded.push(encoding.encode(text));
    }
    return encoded;
  }
}