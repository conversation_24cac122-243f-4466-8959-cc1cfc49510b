// Service for Pinecone vector database integration
import { INestApplication, Injectable, OnModuleInit } from '@nestjs/common';
import { PineconeClient } from '@pinecone-database/pinecone';
import { DescribeIndexStatsResponse, VectorOperationsApi, Vector, QueryRequest, QueryResponse } from '@pinecone-database/pinecone/dist/pinecone-generated-ts-fetch';

@Injectable()
export class PineconeService implements OnModuleInit {
    pinecone = new PineconeClient();

    async onModuleInit() {
        await this.pinecone.init({
            apiKey: process.env.PINCONE_API_KEY,
            environment: process.env.PINCONE_ENVIRONMENT,
        });
    }

    constructor() { }

    async checkIndex(indexName: string) {
        const indexes = await this.pinecone.listIndexes();
        if (indexes.includes(indexName)) {
            return true;
        }
        return false;
    }

    async createIndex(indexName: string, dimension: number = 1536, metric: string = "euclidean", options: Object = {}) {
        if (await this.checkIndex(indexName)) {
            return indexName;
        }
        const createRequest = {
            name: indexName,
            dimension,
            metric,
            ...options,
        };
        return await this.pinecone.createIndex({ createRequest });
    }

    async connectIndex(indexName: string) {
        return this.pinecone.Index(indexName);
    }

    /**
     * @param vectors [{ id: uniqueId, values: embedding, metadata: { content, content_tokens }}]
     */
    async upsertVectors(index: VectorOperationsApi, vectors, namespace) {
        return await index.upsert({ upsertRequest: { vectors, namespace } });
    }

    async batchUpsertVectors(index: VectorOperationsApi, vectors, namespace: string) {
        const batchSize = 100;
        if (Array.isArray(vectors) && vectors.length > batchSize) {
            const batchPromises = [];
            
            for (let i = 0; i < vectors.length; i += batchSize) {
                const batch = vectors.slice(i, i + batchSize);
                batchPromises.push(this.upsertVectors(index, batch, namespace));
            }
            
            const result = await Promise.all(batchPromises);
            
        } else {
            return this.upsertVectors(index, vectors, namespace);
        }
    }

    async getStatistics(index: VectorOperationsApi): Promise<DescribeIndexStatsResponse> {
        return await index.describeIndexStats({
            describeIndexStatsRequest: {},
        });
    }

    async query(index: VectorOperationsApi, options: QueryRequest): Promise<QueryResponse> {
        return await index.query({ queryRequest: options });
    }

    async deleteIndex(indexName: string) {
        return await this.pinecone.deleteIndex({ indexName });
    }

    async deleteSpecificVector(ids: string[], index: VectorOperationsApi, namespace: string, deleteAll: boolean = false) {
        return await index.delete1({
            ids: ids,
            namespace: namespace,
            deleteAll,
        })
    }

    async deleteVectors(index: VectorOperationsApi, namespace: string, ids: string[] = [], deleteAll: boolean = false, filter?: Object) {
        const deleteBody = {
            namespace: namespace,
            deleteAll,
            ids
        }
        if (!ids && !deleteAll && filter) {
            deleteBody['filter'] = filter;
        }
        return await index._delete({
            deleteRequest: deleteBody
        })
    }

    async fetchVectors(index: string, namespace: string, ids: string[]) {
        return await this.pinecone.Index(index).fetch({
            ids,
            namespace
        })
    }
}
