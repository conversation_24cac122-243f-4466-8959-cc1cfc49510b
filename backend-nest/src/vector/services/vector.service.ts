// Main service handling logic for embedding storage, embedding creation, etc
import { Injectable } from '@nestjs/common';
import { Configuration, OpenAIApi } from 'openai';
import { IembeddingResponse } from '../dto/object.dto';
import { PineconeService } from './pinecone/pinecone.service';
import { v4 as uuidv4 } from 'uuid';
import { MyLogger } from 'src/logger/logger.service';
import axios from 'axios';

// services to implement the vector database operations
@Injectable()
export class VectorService {
  private openai;

  constructor(
    private readonly pineconeService: PineconeService,
    private readonly logger: MyLogger,
  ) {}

  async setOpenAi(openaiKey) {
    const configuration = new Configuration({ apiKey: openaiKey });
    this.openai = new OpenAIApi(configuration);
  }

  // async createEmbedding(openaiKey, prompt) {
  //     await this.setOpenAi(openaiKey);

  //     const res = await this.openai.createEmbedding({
  //         input: prompt,
  //         model: 'text-embedding-ada-002',
  //     });

  //     // embedding = [{ object: 'embedding', index: 0, embedding: [ 0.123, 0.456, ...1534 more items ] },{...}]
  //     const embedding: [IembeddingResponse] = res?.data?.data
  //     return embedding;
  // }

  async createEmbedding(
    provider: string,
    apiKey: string,
    input: any,
  ): Promise<any[]> {
    if (input === '' || !input) {
      return [];
    }
    
    if (provider === 'openai') {
      try {
        await this.setOpenAi(apiKey);

        const res = await this.openai.createEmbedding({
          input,
          model: 'text-embedding-ada-002',
        });

        // embedding = [{ object: 'embedding', index: 0, embedding: [ 0.123, 0.456, ...1534 more items ] },{...}]
        const embedding: [IembeddingResponse] = res?.data?.data;
        return embedding;
      } catch (error) {
        if (error.response) {
          if (error.response.status === 429) {
            // Rate limit error, fall back to VoyageAI
            this.logger.error({
              message: error.response.data.error.message,
              context: this.createEmbedding.name,
            });
          } else if (error.response.status === 401) {
            // Account deactivated error
            this.logger.error({
              message: error.response.data.error.message,
              context: this.createEmbedding.name,
            });
          } else if (error.response.status === 400) {
            // Bad request error
            this.logger.error({
              message: error.response.data.error.message,
              context: this.createEmbedding.name,
            });
          }
        }
        this.logger.error({
          message: `Falling to VoyageAI, Error creating embeddings through OpenAI: ${error.response.data.error.message}`,
          context: this.createEmbedding.name,
        });
        return this.voyageAIWithBackoff(input, apiKey);
      }
    } else if (provider === 'voyageai') {
      return this.voyageAIWithBackoff(input, apiKey);
    } else {
      this.logger.error({
        message: `Invalid provider: ${provider}`,
        context: this.createEmbedding.name,
      });
      return null;
    }
  }

  async createOpenAIEmbedding(apiKey: string, input: any): Promise<any[]> {
    try {
      await this.setOpenAi(apiKey);

      const res = await this.openai.createEmbedding({
        input,
        model: 'text-embedding-ada-002',
      });

      const embedding: [IembeddingResponse] = res?.data?.data;
      return embedding;
    } catch (error) {
      const errorMessage = error.response.data.error.message;
      if (error.response) {
        if (error.response.status === 429) {
          // Rate limit error
          this.logger.error({
            message: `Rate limit reached: ${errorMessage}`,
            context: this.createOpenAIEmbedding.name,
          });
        } else if (error.response.status === 401) {
          // Account deactivated error
          this.logger.error({
            message: `Unauthorized: ${errorMessage}`,
            context: this.createOpenAIEmbedding.name,
          });
        } else if (error.response.status === 400) {
          // Bad request error
          this.logger.error({
            message: `Bad request: ${errorMessage}`,
            context: this.createOpenAIEmbedding.name,
          });
        }
      } else {
        // General error logging if error.response is not present
        this.logger.error({
          message: `Error creating embeddings through OpenAI: ${errorMessage}`,
          context: this.createOpenAIEmbedding.name,
        });
      }

      throw error;
    }
  }

  private async voyageAIFallback(input: any, apiKey: string): Promise<any[]> {
    try {
      const fallbackUrl = 'https://api.voyageai.com/v1/embeddings';
      const headers: Record<string, string> = {
        Authorization: `Bearer ${process.env.VOGAYE_API_KEY}`,
      };

      const response = await axios.post(
        fallbackUrl,
        {
          input,
          model: 'voyage-large-2',
        },
        {
          headers: {
            ...headers,
            'Content-Type': 'application/json',
          },
        },
      );

      return response.data?.data;
    } catch (error) {
      if (error.response) {
        if (error.response.status === 429) {
          // Rate limit error
          this.logger.error({
            message: `Rate limit hit for VoyageAI fallback method: ${error.response.data.detail}`,
            context: this.voyageAIFallback.name,
          });
          throw new Error('Rate limit hit for VoyageAI fallback method');
        } else if (error.response.status === 401) {
          // Unauthorized error
          this.logger.error({
            message: `Unauthorized error from VoyageAI fallback method. Please check your API key. Error: ${error.response.data.detail}`,
            context: this.voyageAIFallback.name,
          });
          throw new Error('Unauthorized error from VoyageAI fallback method');
        } else if (error.response.status === 400) {
          // Bad request error
          this.logger.error({
            message: `Bad request error from VoyageAI fallback method: ${error.response.data.detail}`,
            context: this.voyageAIFallback.name,
          });
          throw new Error('Bad request error from VoyageAI fallback method');
        } else if (error.response.status === 500) {
          // Internal server error
          this.logger.error({
            message: `Internal server error from VoyageAI fallback method: ${error.response.data.detail}`,
            context: this.voyageAIFallback.name,
          });
          throw new Error(
            'Internal server error from VoyageAI fallback method',
          );
        } else {
          // Other errors
          this.logger.error({
            message: `Error from VoyageAI fallback method: ${error.response.data.detail}`,
            context: this.voyageAIFallback.name,
          });
          throw new Error(
            `Error from VoyageAI fallback method: ${error.response.data.detail}`,
          );
        }
      } else {
        this.logger.error({
          message: `Error in VoyageAI fallback method: ${error.response.data.detail}`,
          context: this.voyageAIFallback.name,
        });
        throw error;
      }
    }
  }

  private async voyageAIWithBackoff(
    input: any,
    apiKey: string,
    attempt: number = 1,
  ): Promise<any[]> {
    const maxAttempts = 5;
    const baseDelay = 1000; // 1 second

    try {
      const url = 'https://api.voyageai.com/v1/embeddings';
      const headers: Record<string, string> = {
        Authorization: `Bearer ${process.env.VOGAYE_API_KEY}`,
      };

      const response = await axios.post(
        url,
        {
          input,
          model: 'voyage-large-2',
        },
        {
          headers: {
            ...headers,
            'Content-Type': 'application/json',
          },
        },
      );

      return response.data?.data;
    } catch (error) {
      if (error.response) {
        if (error.response.status === 429) {
          // Rate limit error
          if (attempt < maxAttempts) {
            const delay =
              baseDelay * Math.pow(2, attempt - 1) + Math.random() * 1000;
            this.logger.error({
              message: `Rate limit hit for VoyageAI. Retrying in ${delay}ms. Attempt ${attempt} of ${maxAttempts}. Error: ${error.response.data.detail}`,
              context: this.voyageAIWithBackoff.name,
            });
            await new Promise((resolve) => setTimeout(resolve, delay));
            return this.voyageAIWithBackoff(input, apiKey, attempt + 1);
          } else {
            throw new Error(
              'Max retry attempts reached for VoyageAI rate limit',
            );
          }
        } else if (error.response.status === 401) {
          // Unauthorized error
          this.logger.error({
            message: `Unauthorized error from VoyageAI API. Please check your API key. Error: ${error.response.data.detail}`,
            context: this.voyageAIWithBackoff.name,
          });
          throw new Error('Unauthorized error from VoyageAI API');
        } else if (error.response.status === 400) {
          // Bad request error
          this.logger.error({
            message: `Bad request error from VoyageAI API: ${JSON.stringify(
              error.response.data.detail,
            )}`,
            context: this.voyageAIWithBackoff.name,
          });
          throw new Error('Bad request error from VoyageAI API');
        } else if (error.response.status === 500) {
          // Internal server error
          this.logger.error({
            message: `Internal server error from VoyageAI API: ${error.response.data.detail}`,
            context: this.voyageAIWithBackoff.name,
          });
          throw new Error('Internal server error from VoyageAI API');
        } else {
          // Other errors
          this.logger.error({
            message: `Error from VoyageAI API: ${error.response.data.detail}`,
            context: this.voyageAIWithBackoff.name,
          });
          throw new Error(
            `Error from VoyageAI API: ${error.response.data.detail}`,
          );
        }
      } else {
        this.logger.error({
          message: `FAILED to create embeddings to VoyageAI: ${error.response.data.detail}`,
          context: this.createEmbedding.name,
        });

        if (
          error.message === 'Max retry attempts reached for VoyageAI rate limit'
        ) {
          this.logger.error({
            message: error.response.data.detail,
            context: this.voyageAIWithBackoff.name,
          });
          return this.voyageAIFallback(input, apiKey);
        }

        this.logger.error({
          message: `Error creating embeddings through VoyageAI: ${error.response.data.detail}`,
          context: this.voyageAIWithBackoff.name,
        });

        if (attempt < maxAttempts) {
          const delay =
            baseDelay * Math.pow(2, attempt - 1) + Math.random() * 1000;
          this.logger.warn({
            message: `Error occurred. Retrying VoyageAI request in ${delay}ms. Attempt ${attempt} of ${maxAttempts}.`,
            context: this.voyageAIWithBackoff.name,
          });
          await new Promise((resolve) => setTimeout(resolve, delay));
          return this.voyageAIWithBackoff(input, apiKey, attempt + 1);
        }

        return this.voyageAIFallback(input, apiKey);
      }
    }
  }
  async createCompletion(openaiKey, prompt, context?) {
    await this.setOpenAi(openaiKey);

    const completion = await this.openai.createCompletion({
      model: 'text-embedding-ada-002',
      prompt: prompt,
      temperature: 0.7,
      max_tokens: 300,
      frequency_penalty: 0.8,
    });

    return completion?.data.choices?.[0]?.text;
  }

  // async generatePrompt(){
  //     const systemInstruction: string = `Task: Extract important information from the jumbled content passed in by user and present the information as a set of Question and answers
  //         **Instructions:**
  //         1. Only use the content passed in by user to create the question and answer pairs
  //         2. Please generate a minimum of 3 unique question and answer pairs
  //         3. Do not use numbering
  //         4. Keep answers concise and to the point
  //         5. Start questions with 'user:' and answers with 'assistant:'
  //         6. Maintain a newline between questions and answers, and use two newlines to separate pairs`;

  //     const qn_A: string[] = [];
  //     let prevResponse: string = "";
  //     const message: { role: string; content: string }[] = [{ role: "system", content: systemInstruction }];

  //     for (const chunk of data) {
  //         if (prevResponse) {
  //             message.push({ role: "assistant", content: prevResponse });
  //         }
  //         message.push({ role: "user", content: chunk });

  //         const response = openai.ChatCompletion.create({
  //             model: "gpt-3.5-turbo-16k",
  //             messages: message,
  //             top_p: 1,
  //         });

  //         qn_A.push(response.choices[0].message.content);
  //         prevResponse = response.choices[0].message.content;
  //     }
  //     return qn_A;
  // }

  // async saveQnAs(orgId: string, openaiSecret: string, type: string, qna: String[], additionalMetadata?: Object) {
  //     // create vector embeddings for each chunk
  //     const openaiApiKey = openaiSecret;

  //     const embeddings = await this.createEmbedding(openaiApiKey, qna);
  //      let embeddingProvider = PROVIDERS.OPENAI_PROVIDER;
  //      let embeddings = await this.vectorService.createEmbedding(
  //        embeddingProvider,
  //        openaiApiKey,
  //        prompts,
  //      );
  //     // save the embeddings to pinecone
  //     const pineconeRecord = []
  //     for (let i = 0; i < embeddings.length; i++) {
  //         pineconeRecord.push({
  //             id: embeddings[i].index,
  //             values: embeddings[i].embedding,
  //             metadata: {
  //                 content: qna[embeddings[i].index],
  //                 type: type,
  //                 ...additionalMetadata
  //             }
  //         })
  //     }
  //     const namespace = orgId;
  //     const indexName = await this.pineconeService.createIndex(process.env.PINECONE_INDEX);
  //     const index = await this.pineconeService.connectIndex(indexName);
  //     const result = await this.pineconeService.upsertVectors(index, pineconeRecord, namespace);
  //     this.logger.log({ message: {message: `Result from upserting vectors`+ JSON.stringify(result)}, context: 'VectorService.saveQnAs' });
  // }
}