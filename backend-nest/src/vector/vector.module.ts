// Contains the code to use the pinecone vector db and tiktoken.
import { Module, Global } from '@nestjs/common';
import { VectorService } from './services/vector.service';
import { PineconeService } from './services/pinecone/pinecone.service';
import { TiktokenService } from './services/tiktoken.service';
import { LoggerModule } from 'src/logger/logger.module';

@Global()
@Module({
  imports: [LoggerModule],
  providers: [VectorService, PineconeService, TiktokenService],
  exports: [VectorService, PineconeService, TiktokenService],
})
export class VectorModule { }
