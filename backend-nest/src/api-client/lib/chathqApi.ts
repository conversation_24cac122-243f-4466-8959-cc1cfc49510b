import { AxiosRequestConfig } from "axios";

const chatHqWorkspaceUrl = process.env.CHATHQ_WORKSPACE_URL

export const ChatHQConfigs = {
    getMessageHistory: (rId: string, token: string, ls?: string, end?: string, limit?: string): { url: string } => {
        const url = `${chatHqWorkspaceUrl}/api/v1/livechat/messages.history/${rId}?token=${token}`
        if (ls) url.concat(`&ls=${ls}`)
        if (end) url.concat(`&end=${end}`)
        if (limit) url.concat(`&limit=${limit}`)
        return { url }
    },
    getLivechatCustomFields: (token: string, userId: string) => {
        const url = `${chatHqWorkspaceUrl}/api/v1/livechat/custom-fields`
        const requestConfig: AxiosRequestConfig = {
            headers: {
                'X-Auth-Token': token,
                'X-User-Id': userId
            }
        }
        return { url, requestConfig }
    },
    getRoomsList: (token: string, userId: string, departmentId?: string, open?: 'true' | 'false', tags?: string, roomName?: string) => {
        const url = `${chatHqWorkspaceUrl}/api/v1/livechat/rooms`
        const requestConfig: AxiosRequestConfig = {
            headers: {
                'X-Auth-Token': token,
                'X-User-Id': userId
            },
            params: {
                departmentId,
                open,
                tags,
                roomName
            }
        }
        return { url, requestConfig }
    },
    getBusinessHours: (token: string, userId: string) => {
        const url = `${chatHqWorkspaceUrl}/api/v1/livechat/business-hours`
        const requestConfig: AxiosRequestConfig = {
            headers: {
                'X-Auth-Token': token,
                'X-User-Id': userId
            }
        }
        return { url, requestConfig }
    },
    getLiveChatTriggers: (token: string, userId: string) => {
        const url = `${chatHqWorkspaceUrl}/api/v1/livechat/triggers`
        const requestConfig: AxiosRequestConfig = {
            headers: {
                'X-Auth-Token': token,
                'X-User-Id': userId
            }
        }
        return { url, requestConfig }
    }
}