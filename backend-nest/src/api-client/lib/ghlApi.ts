import { AxiosRequestConfig } from "axios";
import { GhlApiURL } from "../utils/channel.constants";
import { GhlToken } from 'src/lib/global-interfaces';

export const GhlAPIs = {
    exchangeToken: (code?: string, tokens?: GhlToken) => {
        let data = {
            client_id: process.env.GHL_CLIENT_ID,
            client_secret: process.env.GHL_CLIENT_SECRET,
            user_type: 'Location'
        };

        if (code) {
            data['code'] = code;
            data['grant_type'] = 'authorization_code';
        } else if (tokens?.refresh_token) {
            data['refresh_token'] = tokens.refresh_token;
            data['grant_type'] = 'refresh_token';
        }

        const requestConfig: AxiosRequestConfig = {
            headers: { "Content-Type": 'application/x-www-form-urlencoded', Accept: 'application/json' }
        }
        return { url: GhlApiURL.GET_ACCESS_TOKEN_URL, data: new URLSearchParams(data), requestConfig };
    },
    getBusinessByLocation: (tokens: GhlToken, locationId: string) => {
        const requestConfig: AxiosRequestConfig = {
            headers: {
                'Authorization': `Bearer ${tokens?.access_token}`,
                'Version': process.env.GHL_API_VERSION,
                'Accept': 'application/json'
            },
            params: { locationId }
        }
        return { url: GhlApiURL.GET_BUSINESS_BY_LOCATION_URL, requestConfig };
    },
    getCalendars: (tokens: GhlToken, locationId: string, calendarId?: string) => {
        const requestConfig: AxiosRequestConfig = {
            headers: {
                'Authorization': `Bearer ${tokens?.access_token}`,
                'Version': process.env.GHL_API_VERSION,
                'Accept': 'application/json'
            },
            params: { locationId }
        }
        return { url: GhlApiURL.GET_CALENDARS_URL, requestConfig };
    },
    getLocationDetails: (tokens: GhlToken, locationId: string) => {
        const requestConfig: AxiosRequestConfig = {
            headers: {
                'Authorization': `Bearer ${tokens?.access_token}`,
                'Version': process.env.GHL_API_VERSION,
                'Accept': 'application/json'
            }
        }
        return { url: GhlApiURL.GET_LOCATION_URL + `/${locationId}`, requestConfig };
    },
}