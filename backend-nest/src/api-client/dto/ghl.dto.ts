export interface IghlSendMessage {
    type: 'SMS' | 'Email' | 'WhatsApp' | 'GMB' | 'IG' | 'FB' | 'Custom' | 'WebChat';
    appointmentId?: string;
    contactId?: string;
    attachments?: string[];
    emailFrom?: string;
    emailCc?: string[];
    emailBcc?: string[];
    html?: string;
    message?: string;
    subject?: string;
    templateId?: string;
    scheduledTimestamp?: number;
    conversationProviderId?: string;
    emailTo?: string;
}