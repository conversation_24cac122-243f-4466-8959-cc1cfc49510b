interface ITokenobj {
    accessToken: string;
    refreshToken: string
}

export class GhlCredTracker {
    _value: ITokenobj | null = null
    _prevValue: ITokenobj | null = null

    constructor(initialValue) {
        this._value = initialValue;
        this._prevValue = initialValue;
    }

    get val() {
        return this._value;
    }

    set val(newVal) {
        if (!this.isEqual(newVal, this._value)) {
            this._prevValue = this._value;
            this._value = newVal;
        }
    }

    save(callbackfn, args: { query: any, options: any }) {
        if (!this.isEqual(this._value, this._prevValue)) {
            const updateBody = {
                creds: {
                    accessToken: this._value.accessToken,
                    refreshToken: this._value.refreshToken,
                }
            }
            callbackfn(args.query, updateBody, args.options)
            this._prevValue = { ...this._value }; // Deep copy the current value
        }
    }

    isEqual(objA: ITokenobj, objB: ITokenobj) {
        const strA = objA ? JSON.stringify(objA) : "";
        const strB = objB ? JSON.stringify(objB) : "";
        return strA === strB;
    }
}