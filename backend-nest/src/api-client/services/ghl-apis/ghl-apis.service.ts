/**
 * Service for GoHighLevel API integration
 */
import { Injectable } from '@nestjs/common';
import { AxiosError, AxiosRequestConfig } from 'axios';
import { GhlAPIs } from '../../lib/ghlApi';
import { methods } from '../../utils/channel.constants';
import { ApiclientService } from '../apiclient.service';
import { GhlToken } from 'src/lib/global-interfaces';
import { MongoCredentialsService } from 'src/mongo/service/credentials/mongo-credentials.service';
import { KINDS } from 'src/lib/constant';
import { IghlSendMessage } from 'src/api-client/dto/ghl.dto';
import { NotificationService } from 'src/notification/notification.service';
import { MyLogger } from 'src/logger/logger.service';
// import { GhlCredTracker } from './credsTracker';

@Injectable()
export class GhlApisService {
  constructor(
    private readonly apiClientService: ApiclientService,
    private readonly mongoCredentialService: MongoCredentialsService,
    private readonly notificationService: NotificationService,
    private readonly logger: MyLogger,
  ) {}

  ghl_api_version: string = process.env.GHL_API_VERSION;

  async makeGhlApiCall(
    locationId: string,
    method: string,
    url: string,
    data?,
    config?: AxiosRequestConfig,
    tokens?: GhlToken,
    email?: string,
  ): Promise<any> {
    try {
      const res = await this.apiClientService.apiRequest(
        method,
        url,
        data,
        config,
      );
      return res;
    } catch (error) {
      let e: AxiosError = error;
      if (e.response.status === 401 || e.response.status === 403) {
        try {
          this.logger.log({
            message: `Refreshing token for GHL location ID: ${locationId}`,
            context: 'GhlApisService.makeGhlApiCall',
          });
          const configBody = GhlAPIs.exchangeToken(undefined, tokens);
          // get new tokens
          const { access_token, refresh_token } =
            await this.apiClientService.apiRequest<any>(
              'POST',
              configBody.url,
              configBody.data,
              configBody.requestConfig,
            );
          tokens['access_token'] = access_token;
          tokens['refresh_token'] = refresh_token;
          await this.mongoCredentialService
            .updateCredentials({
              query: { keyId: locationId, kind: KINDS.GHL_CREDENTIAL },
              updateBody: {
                creds: {
                  accessToken: access_token,
                  refreshToken: refresh_token,
                },
              },
            })
            .then((res) =>
              this.logger.log({
                message: `updated tokens for locationId: ${locationId}: matched: ${res?.matchedCount} | modified ${res?.modifiedCount}`,
                context: 'GhlApisService.makeGhlApiCall',
              }),
            );
          config.headers['Authorization'] = `Bearer ${access_token}`;
          tokens['access_token'] = access_token;
          tokens['refresh_token'] = refresh_token;
          const res = await this.apiClientService.apiRequest(
            method,
            url,
            data,
            config,
          );
          return res;
        } catch (err) {
          if (
            (
              err?.response?.data?.error_description ||
              err?.response?.data?.message ||
              ''
            ).includes('refresh token is invalid')
          ) {
            const credential = await this.mongoCredentialService.getCredential({
              keyId: locationId,
              kind: KINDS.GHL_CREDENTIAL,
            });
            if (email && credential?.alertedUser === false) {
              this.logger.error({
                message: `@error in refreshing token for locationId ${locationId} : ${
                  err?.response?.data?.error_description ||
                  err?.response?.data?.message ||
                  err?.message
                }`,
                context: 'GhlApisService.makeGhlApiCall',
              });
              const emailResponse = await this.notificationService.create({
                to: email,
                subject: 'Capri Ai - Action Required',
                text: `Your LeadConnector integration with Capri Ai is no longer valid! Please re-connect your subaccount with the Id: ${locationId} again as a channel inside Capri. To reconnect: 'capri organization > settings > channels' and click on add new and select the subaccount again.\nOr visit this link: https://beta.capriai.us/home/<USER>/channels`,
              });
              this.logger.log({
                message: `sending email to ${email} for locationId ${locationId} , asking to refresh the connection >> ${emailResponse}`,
                context: 'GhlApisService.makeGhlApiCall',
              });
              await this.mongoCredentialService.updateCredentials({
                query: { keyId: locationId, kind: KINDS.GHL_CREDENTIAL },
                updateBody: {
                  alertedUser: true,
                },
              });
            }
            throw new Error(
              'Your leadconnector connection is invalid. Please reconnect your leadconnector account to your capri organization to continue.',
            );
          } else {
            throw error;
          }
        }
      } else {
        throw error;
      }
    }
  }

  async exchangeGhlCodeForToken(
    code: string = '',
    tokens?: GhlToken,
  ): Promise<any> {
    try {
      const { url, data, requestConfig } = GhlAPIs.exchangeToken(code, tokens);
      return await this.apiClientService.apiRequest(
        methods.POST,
        url,
        data,
        requestConfig,
      );
    } catch (error) {
      this.logger.error({
        message: error.response.message,
        context: 'GhlApisService.exchangeGhlCodeForToken',
      });
      this.logger.error({
        message: error.response.data,
        context: 'GhlApisService.exchangeGhlCodeForToken',
      });
      // throw error;
    }
  }

  async getGhlBusinessByLocation(
    tokens: GhlToken,
    locationId: string,
    email?: string,
  ): Promise<any> {
    const { url, requestConfig } = GhlAPIs.getBusinessByLocation(
      tokens,
      locationId,
    );
    return await this.makeGhlApiCall(
      locationId,
      methods.GET,
      url,
      null,
      requestConfig,
      tokens,
      email,
    );
  }

  async getGhlCalendars(
    tokens: GhlToken,
    locationId: string,
    calendarId: string = '',
    email?: string,
  ): Promise<any> {
    const { url, requestConfig } = GhlAPIs.getCalendars(
      tokens,
      locationId,
      calendarId,
    );
    return await this.makeGhlApiCall(
      locationId,
      methods.GET,
      url + (calendarId ? calendarId : ''),
      null,
      requestConfig,
      tokens,
      email,
    );
  }

  async getGhlLocation(
    tokens: GhlToken,
    locationId: string,
    email?: string,
  ): Promise<any> {
    try {
      const { url, requestConfig } = GhlAPIs.getLocationDetails(
        tokens,
        locationId,
      );
      return await this.makeGhlApiCall(
      locationId,
      methods.GET,
      url,
      null,
      requestConfig,
      tokens,
      email,
      );
    } catch (err) {
      this.logger.error({
        message: `@error in getting location details for locationId ${locationId}: ${
          err?.response?.data?.message || err?.message
        }`,
        context: 'GhlApisService.getGhlLocation',
      });
      throw err;
    }
  }

  async getTimezones(tokens: GhlToken, locationId: string): Promise<string[]> {
    const c = await this.apiClientService.apiRequest<{
      timeZones: string[];
    }>(
      methods.GET,
      `https://services.leadconnectorhq.com/locations/${locationId}/timezones`,
      null,
      {
        headers: {
          Authorization: `Bearer ${tokens.access_token}`,
          Version: '2021-07-28',
          Accept: 'application/json',
        },
      },
    );
    return c.timeZones;
  }

  async getContact(
    tokens: GhlToken,
    locationId: string,
    contactId: string,
    email?: string,
  ) {
    const options = {
      method: 'GET',
      url: `https://services.leadconnectorhq.com/contacts/${contactId}`,
      headers: {
        Authorization: `Bearer ${tokens?.access_token}`,
        Version: process.env.GHL_API_VERSION,
        Accept: 'application/json',
      },
    };
    const rawContact = await this.makeGhlApiCall(
      locationId,
      options.method,
      options.url,
      null,
      { headers: options.headers },
      tokens,
      email,
    );
    return rawContact;
  }

  async getContacts({
    tokens,
    locationId,
    limit,
    query,
  }: {
    tokens: GhlToken;
    locationId: string;
    limit: string;
    query: string;
  }) {
    try {
      const options = {
        method: 'GET',
        url: `https://services.leadconnectorhq.com/contacts/`,
        params: { locationId: locationId, limit: limit, query: query },
        headers: {
          'Content-Type': 'application/json',
          Authorization: 'Bearer ' + tokens.access_token,
          Version: '2021-04-15',
        },
      };

      return await this.makeGhlApiCall(
        locationId,
        options.method,
        options.url,
        null,
        { headers: options.headers },
        tokens,
        null,
      );
    } catch (err) {
      this.logger.error({
        message: `@error in getting contacts for locationId ${locationId}: ${
          err?.response?.data?.message || err?.message
        }`,
        context: 'GhlApisService.getContacts',
      });
      throw err;
    }
  }

  async addNoteToContact(
    tokens: GhlToken,
    locationId: string,
    contactId: string,
    note: string,
    email?: string,
  ) {
    try {
      const options = {
        method: 'POST',
        url: `https://services.leadconnectorhq.com/contacts/${contactId}/notes`,
        headers: {
          Authorization: `Bearer ${tokens?.access_token}`,
          Version: process.env.GHL_API_VERSION,
          Accept: 'application/json',
        },
        data: {
          body: note,
        },
      };
      return await this.makeGhlApiCall(
        locationId,
        options.method,
        options.url,
        options.data,
        { headers: options.headers },
        tokens,
        email,
      );
    } catch (err) {
      this.logger.error({
        message: `@error in adding note to contact for locationID ${locationId} | contactId ${contactId} : ${
          err?.response?.data?.message || err?.message
        }`,
        context: 'GhlApisService.addNoteToContact',
      });
    }
  }

  async getCustomValues({
    tokens,
    locationId,
  }: {
    tokens: GhlToken;
    locationId: string;
  }) {
    try {
      // Options for fetching custom values
      const options = {
        method: 'GET',
        url: `https://services.leadconnectorhq.com/locations/${locationId}/customValues`,
        headers: {
          'Content-Type': 'application/json',
          Authorization: 'Bearer ' + tokens.access_token,
          Version: '2021-04-15',
        },
      };

      const customValuesResponse = await this.makeGhlApiCall(
        locationId,
        options.method,
        options.url,
        null,
        { headers: options.headers },
        tokens,
        null,
      );

      const customValues = customValuesResponse?.customValues || [];

      // Format custom values, rem
      const cleanedCustomValues = customValues.map((item) => ({
        ...item,
        fieldKey: item.fieldKey.replace(/\{\{\s*(.*?)\s*\}\}/g, '{{$1}}'), // Remove spaces inside curly braces
      }));
     
      return cleanedCustomValues;
    } catch (err) {
      this.logger.error({
        message: `@error in getting custom values for locationId ${locationId}: ${
          err?.response?.data?.message || err?.message
        }`,
        context: 'GhlApisService.getCustomValues',
      });
      throw err;
    }
  }

  async getTags(tokens: GhlToken, locationId: string) {
    try {
      const options = {
        method: 'GET',
        url: `https://services.leadconnectorhq.com/locations/${locationId}/tags`,
        headers: {
        Authorization: `Bearer ${tokens?.access_token}`,
        Version: process.env.GHL_API_VERSION,
        Accept: 'application/json',
      },
    };
      return await this.makeGhlApiCall(
        locationId,
        options.method,
        options.url,
        null,
        { headers: options.headers },
        tokens,
        null,
      );
    } catch (err) {
      this.logger.error({
        message: `@error in getting tags for locationId ${locationId}: ${err?.response?.data?.message || err?.message}`,
        context: 'GhlApisService.getTags',
      });
      throw err;
    }
  }

  async getVariables({
    tokens,
    locationId,
  }: {
    tokens: GhlToken;
    locationId: string;
  }) {
    try {
      let variables = [];

      // Options for fetching custom fields
      const options1 = {
        method: 'GET',
        url: `https://services.leadconnectorhq.com/locations/${locationId}/customFields`,
        params: { model: 'all' },
        headers: {
          'Content-Type': 'application/json',
          Authorization: 'Bearer ' + tokens.access_token,
          Version: '2021-04-15',
        },
      };

      // Options for fetching custom values
      const options2 = {
        method: 'GET',
        url: `https://services.leadconnectorhq.com/locations/${locationId}/customValues`,
        headers: {
          'Content-Type': 'application/json',
          Authorization: 'Bearer ' + tokens.access_token,
          Version: '2021-04-15',
        },
      };

      const [customFieldsResponse, customValuesResponse] = await Promise.all([
        this.makeGhlApiCall(
          locationId,
          options1.method,
          options1.url,
          null,
          { headers: options1.headers },
          tokens,
          null,
        ),
        this.makeGhlApiCall(
          locationId,
          options2.method,
          options2.url,
          null,
          { headers: options2.headers },
          tokens,
          null,
        ),
      ]);

      const customFields = customFieldsResponse?.customFields || [];
      const customValues = customValuesResponse?.customValues || [];

      const cleanedCustomValues = customValues.map((item) => ({
        ...item,
        fieldKey: item.fieldKey.replace(/^\{\{\s*|\s*\}\}$/g, ''), // Remove {{ and }} with spaces
      }));

      variables = [...customFields, ...cleanedCustomValues];

      return variables;
    } catch (err) {
      this.logger.error({
        message: `@error in getting custom fields/values for locationId ${locationId}: ${
          err?.response?.data?.message || err?.message
        }`,
        context: 'GhlApisService.getVariables',
      });
      throw err;
    }
  }

  async getCustomFields(
    tokens: GhlToken,
    locationId: string,
  ) {
    try {
      // Options for fetching custom fields
      const options = {
        method: 'GET',
        url: `https://services.leadconnectorhq.com/locations/${locationId}/customFields`,
        params: { model: 'all' },
        headers: {
          'Content-Type': 'application/json',
          Authorization: 'Bearer ' + tokens.access_token,
          Version: '2021-04-15',
        },
      };


      const customFieldsResponse = await this.makeGhlApiCall(
        locationId,
        options.method,
        options.url,
        null,
        { headers: options.headers },
        tokens,
        null,
      );

      return customFieldsResponse;
    } catch (err) {
      this.logger.error({
        message: `@error in getting custom fields for locationId ${locationId}: ${err?.response?.data?.message || err?.message}`,
        context: 'GhlApisService.getCustomFields',
      });
      throw err;
    }
  }

  async updateContactNote(
    tokens: GhlToken,
    contactId: string,
    noteId: string,
    note: string,
    locationId?: string,
    email?: string,
  ) {
    const options = {
      method: 'PUT',
      url: `https://services.leadconnectorhq.com/contacts/${contactId}/notes/${noteId}`,
      headers: {
        Authorization: `Bearer ${tokens?.access_token}`,
        Version: process.env.GHL_API_VERSION,
        Accept: 'application/json',
      },
      data: {
        body: note,
      },
    };
    return await this.makeGhlApiCall(
      locationId,
      options.method,
      options.url,
      options.data,
      { headers: options.headers },
      tokens,
      email,
    );
  }

  async makeGhlCalendarAppointment(
    tokens: GhlToken,
    locationId: string,
    calendarId: string,
    contactId: string,
    startTime: string,
    endTime?: string,
    email?: string,
  ) {
    try {
      const options = {
        method: 'POST',
        url: `https://services.leadconnectorhq.com/calendars/events/appointments`,
        headers: {
          Authorization: `Bearer ${tokens?.access_token}`,
          Version: process.env.GHL_API_VERSION,
          Accept: 'application/json',
        },
        data: {
          calendarId,
          contactId,
          startTime,
          locationId,
        },
      };
      if (endTime) {
        options.data['endTime'] = endTime;
      }
      return await this.makeGhlApiCall(
        locationId,
        options.method,
        options.url,
        options.data,
        { headers: options.headers },
        tokens,
        email,
      );
    } catch (error) {
      this.logger.error({
        message: `@error in booking calendar appointment for locationId ${locationId} : ${
          error?.response?.data?.message || error?.message
        }`,
        context: 'GhlApisService.makeGhlCalendarAppointment',
      });
      return {
        error: `Booking calendar appointment failed with error : ${
          error?.response?.data?.message || error?.message
        }`,
      };
    }
  }

  async addTag(
    tokens: GhlToken,
    locationId: string,
    contactId: string,
    ...tags: any
  ) {
    try {
      const formattedTags = tags.every(
        (tag) => typeof tag === 'object' && 'name' in tag,
      )
        ? tags.map((tag) => tag.name)
        : tags;
      const options = {
        method: 'POST',
        url: `https://services.leadconnectorhq.com/contacts/${contactId}/tags`,
        headers: {
          Authorization: `Bearer ${tokens?.access_token}`,
          Version: process.env.GHL_API_VERSION,
          Accept: 'application/json',
        },
        data: {
          tags: formattedTags,
        },
      };
     
      let result = await this.makeGhlApiCall(
        locationId,
        options.method,
        options.url,
        options.data,
        { headers: options.headers },
        tokens,
      );

      this.logger.log({
        message: `@result in adding tags to your contact for locationId ${locationId} : ${JSON.stringify(result)}`,
        context: contactId,
      });

      return result;
    } catch (err) {
      this.logger.error({
        message: `Adding tags to your contact failed with error for locationId ${locationId} : ${
          err?.response?.data?.message || err?.message
        }`,
        context: 'GhlApisService.addTag',
      });
      return {
        error: err?.response?.data?.message || err?.message,
      };
    }
  }

  async sendMessage(
    tokens: GhlToken,
    locationId: string,
    payload: IghlSendMessage,
    email?: string,
  ) {
    try {
      const options = {
        method: 'POST',
        url: `https://services.leadconnectorhq.com/conversations/messages`,
        headers: {
          Authorization: `Bearer ${tokens?.access_token}`,
          Version: process.env.GHL_API_VERSION,
          Accept: 'application/json',
        },
        data: payload as any,
      };
      if (payload?.type === 'WhatsApp') {
        options.data = {
          ...payload,
          whatsapp: {
            type: 'message',
          },
        };
      }
      return await this.makeGhlApiCall(
        locationId,
        options.method,
        options.url,
        options.data,
        { headers: options.headers },
        tokens,
        email,
      );
    } catch (err) {
      this.logger.error({
        message: `@error in sending GHL message for ${
          locationId
            ? `locationId: ${locationId}`
            : '' + 'with contactId: ' + payload?.contactId || ''
        }: ${err?.response?.data?.message || err?.message}`,
        context: 'GhlApisService.sendMessage',
      });
      return {
        error: `Sending message failed with error : ${
          err?.response?.data?.message || err?.message
        }`,
      };
    }
  }

  async createContact(
    tokens: GhlToken,
    locationId: string,
    contact: any = {},
    email?: string,
  ) {
    try {
      const options = {
        method: 'POST',
        url: `https://services.leadconnectorhq.com/contacts/`,
        headers: {
          Authorization: `Bearer ${tokens?.access_token}`,
          Version: `2021-07-28`,
          Accept: 'application/json',
        },
        data: contact,
      };
      const response = await this.makeGhlApiCall(
        locationId,
        options.method,
        options.url,
        options.data,
        { headers: options.headers },
        tokens,
        email,
      );
      return response.contact;
    } catch (error) {
      this.logger.log({
        message: `@error in GHL createContact API for locationId ${locationId} : ${error?.response?.data?.message}`,
        context: 'GhlApisService.createContact',
      });
      if (
        error?.response?.data?.statusCode == 400 &&
        error?.response?.data?.meta?.contactId
      ) {
        return {
          id: error?.response?.data?.meta?.contactId,
        };
      } else {
        throw error;
      }
    }
  }

  async createCustomField(tokens, locationId: string, name: string) {
    try {
      const url = `https://services.leadconnectorhq.com/locations/${locationId}/customFields`;

      //name = "contact.pan_card"

      //remove the contact from the name field
      name = name.replace('contact.', '');

      const options = {
        method: 'POST',
        url: url,
        headers: {
          Authorization: `Bearer ${tokens?.access_token}`,
          Version: `2021-07-28`,
          Accept: 'application/json',
        },
        data: {
          name,
          dataType: 'TEXT',
          model: 'contact',
        },
      };

      this.logger.log({
        message: 'Creating custom field with name: ' + name,
        context: 'GhlApisService.createCustomField',
      });

      const response = await this.makeGhlApiCall(
        locationId,
        options.method,
        options.url,
        options.data,
        { headers: options.headers },
        tokens,
      );

      this.logger.log({
        message: 'response: ' + response,
        context: 'GHL_CREATE_CUSTOM_FIELD',
      });

      return response;
    } catch (error) {
      this.logger.error({
        message: `@error in GHL create custom field API for locationId ${locationId} : ${error?.response?.data?.message}`,
        context: 'GhlApisService.createCustomField',
      });
      throw error;
    }
  }

  async listCustomFields(tokens, locationId: string) {
    try {
      const url = `https://services.leadconnectorhq.com/locations/${locationId}/customFields`;

      const options = {
        method: 'GET',
        url: url,
        params: { model: 'contact' },
        headers: {
          Authorization: `Bearer ${tokens?.access_token}`,
          Version: `2021-07-28`,
          Accept: 'application/json',
        },
      };

      const response = await this.makeGhlApiCall(
        locationId,
        options.method,
        options.url,
        null,
        { headers: options.headers },
        tokens,
      );

      return response;
    } catch (error) {
      this.logger.error({
        message: `@error in GHL listing custom field API for locationId ${locationId} : ${error?.response?.data?.message}`,
        context: 'GhlApisService.listCustomFields',
      });
      throw error;
    }
  }

  async updateContact(tokens: GhlToken, contactId: string, data: any) {
    try {
      const url = `https://services.leadconnectorhq.com/contacts/${contactId}`;

      const options = {
        method: 'PUT',
        url: url,
        headers: {
          Authorization: `Bearer ${tokens?.access_token}`,
          Version: `2021-07-28`,
          Accept: 'application/json',
        },
        data,
      };

      const response = await this.makeGhlApiCall(
        contactId,
        options.method,
        options.url,
        options.data,
        { headers: options.headers },
        tokens,
      );

      return response;
    } catch (error) {
      this.logger.error({
        message: `@error in GHL updateContact API for contactId ${contactId} : ${error?.response?.data?.message}`,
        context: 'GhlApisService.updateContact',
      });
    }
  }

  async upsertContact(tokens: GhlToken, locationId: string, email: string) {
    try {
      const url = `https://services.leadconnectorhq.com/contacts/upsert`;

      const options = {
        method: 'POST',
        url: url,
        headers: {
          Authorization: `Bearer ${tokens?.access_token}`,
          Version: `2021-07-28`,
          Accept: 'application/json',
        },
        data: {
          locationId,
          email,
        },
      };

      const response = await this.makeGhlApiCall(
        locationId,
        options.method,
        options.url,
        options.data,
        { headers: options.headers },
        tokens,
      );

      return response;
    } catch (error) {
      this.logger.error({
        message: `@error in GHL upsertContact API for locationId ${locationId} : ${error?.response?.data?.message}`,
        context: 'GhlApisService.updateContact',
      });
    }
  }

  async updateContactCustomFields(tokens, contactId, customFields) {
    try {
      const url = `https://services.leadconnectorhq.com/contacts/${contactId}`;

      const options = {
        method: 'PUT',
        url: url,
        headers: {
          Authorization: `Bearer ${tokens?.access_token}`,
          Version: `2021-07-28`,
          Accept: 'application/json',
        },
        data: {
          customFields,
        },
      };

      const response = await this.makeGhlApiCall(
        contactId,
        options.method,
        options.url,
        options.data,
        { headers: options.headers },
        tokens,
      );

      return response;
    } catch (error) {
      this.logger.error({
        message: `@error in GHL updateContactCustomFields API for contactId ${contactId} : ${error?.response?.data?.message}`,
        context: 'GhlApisService.updateContactCustomFields',
      });
    }
  }

  async createTag(
    tokens: GhlToken,
    locationId: string,
    tag: string,
    email?: string,
  ) {
    try {
      const options = {
        method: 'POST',
        url: `https://services.leadconnectorhq.com/locations/${locationId}/tags`,
        headers: {
          Authorization: `Bearer ${tokens?.access_token}`,
          Version: `2021-07-28`,
          Accept: 'application/json',
        },
        data: {
          name: tag,
        },
      };
    } catch (err) {
      this.logger.log({
        message: `@error in creating tag for locationId ${locationId} : ${
          err?.response?.data?.message || err?.message
        }`,
        context: 'GhlApisService.createTag',
      });
      return {
        error: err?.response?.data?.message || err?.message,
      };
    }
  }

  async getLocationTags(tokens: GhlToken, locationId: string) {
    try {
      const options = {
        method: 'GET',
        url: `https://services.leadconnectorhq.com/locations/${locationId}/tags`,
        headers: {
          Authorization: `Bearer ${tokens?.access_token}`,
          Version: `2021-07-28`,
          Accept: 'application/json',
        },
      };
      return await this.makeGhlApiCall(
        locationId,
        options.method,
        options.url,
        null,
        { headers: options.headers },
        tokens,
        null,
      );
    } catch (err) {
      this.logger.error({
        message: `@error in getting tags list for locationId ${locationId} : ${
          err?.response?.data?.message || err?.message
        }`,
        context: 'GhlApisService.getLocationTags',
      });
      return {
        error: err?.response?.data?.message || err?.message,
      };
    }
  }

  async searchConversations(tokens: GhlToken, locationId: string, params: any) {
    try {
      const queryParams = new URLSearchParams(params).toString();
      const url = `https://services.leadconnectorhq.com/conversations/search?${queryParams}`;

      const options = {
        method: 'GET',
        url,
        headers: {
          Authorization: `Bearer ${tokens?.access_token}`,
          Version: `2021-07-28`,
          Accept: 'application/json',
        },
      };

      return await this.makeGhlApiCall(
        locationId,
        options.method,
        options.url,
        null,
        { headers: options.headers },
        tokens,
        null,
      );
    } catch (err) {
      this.logger.error({
        message: `@error in searching conversations for locationId ${locationId} : ${
          err?.response?.data?.message || err?.message
        }`,
        context: 'GhlApisService.searchConversations',
      });
      return {
        error: err?.response?.data?.message || err?.message,
      };
    }
  }

  async getMessagesByConversationId(
    tokens: GhlToken,
    conversationId: string,
    params: any,
  ) {
    try {
      const queryParams = new URLSearchParams(params).toString();
      const url = `https://services.leadconnectorhq.com/conversations/${conversationId}/messages?${queryParams}`;

      const options: AxiosRequestConfig = {
        method: 'GET',
        url,
        headers: {
          Authorization: `Bearer ${tokens.access_token}`,
          Version: '2021-04-15',
          Accept: 'application/json',
        },
      };

      return await this.makeGhlApiCall(
        conversationId,
        options.method,
        options.url,
        null,
        { headers: options.headers },
        tokens,
        null,
      );
    } catch (err) {
      this.logger.error({
        message: `@error in getting messages by conversationId ${conversationId}: ${
          err?.response?.data?.message || err?.message
        }`,
        context: 'GhlApisService.getMessagesByConversationId',
      });
      throw err;
    }
  }

  async getFutureCalendarEvents(
    tokens: GhlToken,
    locationId: string,
    calendarId: string,
    startDate: number,
    endDate: number,
  ) {
    try {
      const startTime = startDate || Date.now();
      const endTime = endDate;
      const options = {
        method: 'GET',
        url: `https://services.leadconnectorhq.com/calendars/events?locationId=${locationId}&calendarId=${calendarId}&startTime=${startTime}&endTime=${endTime}`,
        headers: {
          Authorization: `Bearer ${tokens?.access_token}`,
          Version: `2021-04-15`,
          Accept: 'application/json',
        },
      };
      return await this.makeGhlApiCall(
        locationId,
        options.method,
        options.url,
        null,
        { headers: options.headers },
        tokens,
        null,
      );
    } catch (err) {
      this.logger.error({
        message: `@error in getting future calendar events for locationId ${locationId}: ${
          err?.response?.data?.message || err?.message
        }`,
        context: 'GhlApisService.getFutureCalendarEvents',
      });
      throw err;
    }
  }

  async updateCalendarEvents({
    tokens,
    locationId,
    calendarId,
    eventId,
    startDate,
    endDate,
    title,
    meetingLocationType,
    appointmentStatus,
    assignedUserId,
    address,
    ignoreDateRange,
    toNotify,
    ignoreFreeSlotValidation,
    rrule
  }: {
    tokens: GhlToken,
    locationId: string,
    calendarId: string,
    eventId: string,
    startDate: number,
    endDate: number,
    title?: string,
    meetingLocationType?: string,
    appointmentStatus?: string,
    assignedUserId?: string,
    address?: string,
    ignoreDateRange?: boolean,
    toNotify?: boolean,
    ignoreFreeSlotValidation?: string,
    rrule?: string
  }) {
    try {
      const startTime = startDate;
      const endTime = endDate;
      const options = {
        method: 'PUT',
        url: `https://services.leadconnectorhq.com/calendars/events/appointments/${eventId}`,
        headers: {
          Authorization: `Bearer ${tokens?.access_token}`,
          Version: `2021-04-15`,
          Accept: 'application/json',
          'Content-Type': 'application/json',
        },
        data: {
          calendarId,
          startTime,
          endTime,
          title,
          meetingLocationType,
          appointmentStatus,
          assignedUserId,
          address,
          ignoreDateRange,
          toNotify,
          ignoreFreeSlotValidation,
          rrule
        },
      };
      return await this.makeGhlApiCall(
        locationId,
        options.method,
        options.url,
        options.data,
        { headers: options.headers },
        tokens,
        null,
      );
    } catch (err) {
      this.logger.error({
        message: `@error in updating calendar events for locationId ${locationId}: ${
          err?.response?.data?.message || err?.message
        }`,
        context: 'GhlApisService.updateCalendarEvents',
      });
      throw err;
    }
  }

  async deleteCalendarEvents({
    tokens,
    locationId,
    eventId,
  }: {
    tokens: GhlToken,
    locationId: string,
    eventId: string,
  }) {
    try {
      const options = {
        method: 'DELETE',
        url: `https://services.leadconnectorhq.com/calendars/events/${eventId}`,
        headers: {
          Authorization: `Bearer ${tokens?.access_token}`,
          Version: `2021-04-15`,
          Accept: 'application/json',
        },
      };
      return await this.makeGhlApiCall(
        locationId,
        options.method,
        options.url,
        null,
        { headers: options.headers },
        tokens,
        null,
      );
    } catch (err) {
      this.logger.error({
        message: `@error in deleting calendar event for locationId ${locationId}: ${
          err?.response?.data?.message || err?.message
        }`,
        context: 'GhlApisService.deleteCalendarEvents',
      });
      throw err;
    }
  }

  async getCalendarFreeSlots(
    tokens: GhlToken,
    calendarId: string,
    timezone: string,
    start: string,
    end?: string,
  ) {
    try {
      const options = {
        method: 'GET',
        url: `https://services.leadconnectorhq.com/calendars/${calendarId}/free-slots?startDate=${start}&endDate=${end}&timezone=${timezone}`,
        headers: {
          'Content-Type': 'application/json',
          Authorization: 'Bearer ' + tokens.access_token,
          Version: '2021-04-15',
        },
      };

      return await this.makeGhlApiCall(
        calendarId,
        options.method,
        options.url,
        null,
        { headers: options.headers },
        tokens,
        null,
      );
    } catch (err) {
      this.logger.error({
        message: `@error in getting free slots for calendarId ${calendarId}: ${
          err?.response?.data?.message || err?.message
        }`,
        context: 'GhlApisService.getCalendarFreeSlots',
      });
      throw err;
    }
  }


  async updateMessageStatus(
    tokens: GhlToken,
    messageId: string,
    status: 'delivered' | 'failed' | 'pending' | 'read',
  ) {
    try {
      const options = {
        method: 'PUT',
        url: `https://services.leadconnectorhq.com/conversations/messages/${messageId}/status`,
        headers: {
          Authorization: `Bearer ${tokens?.access_token}`,
          Version: `2021-04-15`,
          Accept: 'application/json',
          'Content-Type': 'application/json',
        },
        data: {
          status,
        },
      };
      return await this.makeGhlApiCall(
        messageId,
        options.method,
        options.url,
        options.data,
        { headers: options.headers },
        tokens,
        null,
      );
    } catch (err) {
      this.logger.error({
        message: `@error in updating message status for messageId ${messageId}: ${err?.response?.data?.message || err?.message
          }`,
        context: 'GhlApisService.updateMessageStatus',
      });
      throw err;
    }
  }

  async markConversationUnread(
    tokens: GhlToken,
    conversationId: string,
    locationId: string,
    contactId?: string,
  ) {
    try {
      if (!conversationId) {
        const conversation = await this.searchConversations(tokens, locationId, { contactId });
        conversationId = conversation?.conversations[0]?.id;
        if (!conversationId) throw new Error(`No conversation found for contactId ${contactId}`)
      }
      const options = {
        method: 'PUT',
        url: `https://services.leadconnectorhq.com/conversations/${conversationId}`,
        headers: {
          Authorization: `Bearer ${tokens?.access_token}`,
          Version: `2021-04-15`,
          Accept: 'application/json',
          'Content-Type': 'application/json',
        },
        data: {
          locationId,
          unreadCount: 1,
        },
      };
      return await this.makeGhlApiCall(
        conversationId,
        options.method,
        options.url,
        options.data,
        { headers: options.headers },
        tokens,
        null,
      );
    } catch (err) {
      this.logger.error({
        message: `@error in marking conversation unread for conversationId ${conversationId}: ${err?.response?.data?.message || err?.message
          }`,
        context: 'GhlApisService markConversationUnread',
      });
      throw err;
    }
  }


  async getConversation({
    conversationId,
    tokens,
    locationId,
  }: {
    conversationId: string;
    tokens: GhlToken;
    locationId: string;
  }){
    try {
      const url = `https://services.leadconnectorhq.com/conversations/${conversationId}`;

      const options: AxiosRequestConfig = {
        method: 'GET',
        url,
        headers: {
          Authorization: `Bearer ${tokens.access_token}`,
          Version: '2021-04-15',
          Accept: 'application/json',
        },
      };

      return await this.makeGhlApiCall(
        locationId,
        options.method,
        options.url,
        null,
        { headers: options.headers },
        tokens,
        null
      );
    } catch (err) {
      this.logger.error({
        message: `@error in getting conversation by conversationId ${conversationId}: ${
          err?.response?.data?.message || err?.message
        }`,
        context: 'GhlApisService.getConversation',
      });
      throw err;
    }
  }

  async getEmailById(tokens: GhlToken, emailId: string) {
    try {
      const options = {
        method: 'GET',
        url: `https://services.leadconnectorhq.com/conversations/messages/email/${emailId}`,
        headers: {
          Authorization: `Bearer ${tokens?.access_token}`,
          Version: `2021-04-15`,
          Accept: 'application/json',
        },
      };
      return await this.makeGhlApiCall(
        emailId,
        options.method,
        options.url,
        null,
        { headers: options.headers },
        tokens,
        null,
      );
    } catch (err) {
      this.logger.error({
        message: `@error in getting email by id ${emailId}: ${
          err?.response?.data?.message || err?.message
        }`,
        context: 'GhlApisService.getEmailById',
      });
      throw err;
    }
  }

  async searchContacts(
    tokens: GhlToken,
    locationId: string,
    searchParams: { query?: string; } = {}
  ) {
    try {
      const options: AxiosRequestConfig = {
        method: 'POST',
        url: `https://services.leadconnectorhq.com/contacts/search`,
        headers: {
          Accept: 'application/json',
          Authorization: `Bearer ${tokens.access_token}`,
          'Content-Type': 'application/json',
          Version: '2021-07-28',
        },
        data: {
          locationId,
          pageLimit: 20,
          filters: [
            {
              "field": "firstNameLowerCase",
              "operator": "contains",
              "value": searchParams.query
            },
          ]
        },
      };

      return await this.makeGhlApiCall(
        locationId,
        options.method,
        options.url,
        options.data,
        { headers: options.headers },
        tokens,
        null,
      );
    } catch (err) {
      this.logger.error({
        message: `@error in searching contacts for locationId ${locationId}: ${
          err?.response?.data?.message || err?.message
        }`,
        context: 'GHL api searchContacts',
      });
      throw err;
    }
  }
}
