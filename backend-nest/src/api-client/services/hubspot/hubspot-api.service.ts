/**
 * Service for HubSpot API integration
 */
import { Injectable } from '@nestjs/common';
import { ApiclientService } from '../apiclient.service';
import { AxiosError, AxiosRequestConfig } from 'axios';
import { methods } from '../../utils/channel.constants';
import { KINDS, PROVIDERS } from 'src/lib/constant';
import { MyLogger } from 'src/logger/logger.service';
import { MongoCredentialsService } from 'src/mongo/service/credentials/mongo-credentials.service';

type ImakeHubspotAPIcall = AxiosRequestConfig & {
  portalId?: string;
};

type token = {
  accessToken: string;
  refreshToken: string;
};

const HUBSPOT_BASE_API_URL = 'https://api.hubapi.com';

@Injectable()
export class HubspotAPIService {
  constructor(
    private readonly apiclientService: ApiclientService,
    private readonly mongoCredentialsService: MongoCredentialsService,
    private readonly logger: MyLogger,
  ) {}

  async makeHubspotAPIcall(
    tokens: {
      accessToken: string;
      refreshToken: string;
    },
    props: ImakeHubspotAPIcall,
  ): Promise<any> {
    try {
      return await this.apiclientService.rawApiRequest({ ...props });
    } catch (error) {
      let e: AxiosError = error;
      if (e.response.status === 401 || e.response.status === 403) {
        const newTokens = await this.exchangeCode(
          undefined,
          tokens.refreshToken,
        );
        tokens['accessToken'] = newTokens.accessToken;
        tokens['refreshToken'] = newTokens.refreshToken;
        props.headers['Authorization'] = `Bearer ${newTokens.accessToken}`;
        await this.mongoCredentialsService.updateCredentials({
          query: {
            kind: KINDS.HUBSPOT_CREDENTIAL,
            type: 'channel',
            keyId: props.portalId,
          },
          updateBody: {
            creds: {
              accessToken: newTokens.accessToken,
              refreshToken: newTokens.refreshToken,
            },
          },
        });
        return await this.apiclientService.rawApiRequest({ ...props });
      } else {
        throw error;
      }
    }
  }

  async exchangeCode(
    code?: string,
    refreshToken: string = undefined,
  ): Promise<token> {
    let grant = 'authorization_code';
    const data = {
      grant_type: grant,
      client_id: process.env.HUBSPOT_CLIENT_ID,
      client_secret: process.env.HUBSPOT_CLIENT_SECRET,
      code: code,
      redirect_uri: process.env.BACKEND_URL + '/hubspot/connect',
    };
    if (refreshToken) {
      data.grant_type = 'refresh_token';
      data['refresh_token'] = refreshToken;
    }

    const options = {
      method: 'POST',
      url: `https://api.hubapi.com/oauth/v1/token`,
      data: new URLSearchParams(data),
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
    };
    let hubspotRes: any = await this.apiclientService.rawApiRequest({
      ...options,
    });
    if (hubspotRes)
      return {
        accessToken: hubspotRes?.access_token,
        refreshToken: hubspotRes?.refresh_token,
      };
    else return undefined;
  }

  async getAccountDetails(
    tokens: token,
    email?: string,
  ): Promise<{
    portalId: string;
    timeZone: string;
  }> {
    const options = {
      method: 'GET',
      url: HUBSPOT_BASE_API_URL + `/account-info/v3/details`,
      headers: {
        Authorization: `Bearer ${tokens.accessToken}`,
      },
    };
    const accountInfo: any = await this.makeHubspotAPIcall(tokens, options);
    return {
      portalId: accountInfo.portalId,
      timeZone: accountInfo.timeZone,
    };
  }

  async getSingleMessage(tokens: token, threadId: string, messageId: string, portalId: string) {
    const options = {
      method: 'GET',
      url:
        HUBSPOT_BASE_API_URL +
        `/conversations/v3/conversations/threads/${threadId}/messages/${messageId}`,
      headers: {
        Authorization: `Bearer ${tokens.accessToken}`,
      },
      portalId,
    };
    const rawMessage = await this.makeHubspotAPIcall(tokens, options);
    return {
      channelId: rawMessage.channelId,
      channelAccountId: rawMessage.channelAccountId,
      status: rawMessage.status.statusType,
      message: rawMessage.text,
      direction: rawMessage.direction,
    };
  }

  async sendHubspotMessage(
    tokens: token,
    threadId: string,
    channelId,
    channelAccountId,
    text: string,
    recipients=[],
    senderActorId?: string,
    type = 'MESSAGE',
    portalId: string=''
  ) {
    const options = {
      method: 'POST',
      url:
        HUBSPOT_BASE_API_URL +
        `/conversations/v3/conversations/threads/${threadId}/messages`,
      headers: {
        Authorization: `Bearer ${tokens.accessToken}`,
      },
      data: {
        type,
        attachments: [],
        channelId,
        channelAccountId,
        senderActorId,
        text,
        threadId,
      },
      portalId,
    };
    const messageResponse = await this.makeHubspotAPIcall(tokens, options);
    
  }

  async getChannelName(tokens: token, channelId: string, portalId: string): Promise<"LIVE_CHAT" | "FB_MESSENGER" | "EMAIL" | "WHATSAPP"> {
    const options = {
      method: 'GET',
      url: HUBSPOT_BASE_API_URL + `/conversations/v3/conversations/channels/${channelId}`,
      headers: {
        Authorization: `Bearer ${tokens.accessToken}`,
      },
      portalId
    }
    return (await this.makeHubspotAPIcall(tokens, options))?.name || "";
  }

  async getAThread(tokens: token, threadId: string, portalId: string) {
    const options = {
      method: 'GET',
      url: HUBSPOT_BASE_API_URL + `/conversations/v3/conversations/threads/${threadId}`,
      headers: {
        Authorization: `Bearer ${tokens.accessToken}`,
      },
      portalId
    };
    return await this.makeHubspotAPIcall(tokens, options);
  }

  async getContact(tokens: token, contactId: string, portalId: string) {
    try {
      const options = {
        method: 'GET',
        url: HUBSPOT_BASE_API_URL + `/crm/v3/objects/contacts/${contactId}`,
        headers: {
          Authorization: `Bearer ${tokens.accessToken}`,
        },
        portalId
      };
      const properties = [
        'city',
        'hs_whatsapp_phone_number',
        'email',
        'firstname',
        'tags',
        'mobilephone',
        'phone',
      ]
      options.url += `?properties=${properties.join('&properties=')}`;
      return await this.makeHubspotAPIcall(tokens, options);
    } catch (error: any) {
      return undefined;
    }
  }

  async getASingleChannelAccount(token: token, channelAccountId: string, portalId: string) {
    const options = {
      method: 'GET',
      url: HUBSPOT_BASE_API_URL + `/conversations/v3/conversations/channel-accounts/${channelAccountId}`,
      headers: {
        Authorization: `Bearer ${token.accessToken}`,
      },
      portalId
    }

    return await this.makeHubspotAPIcall(token, options);
  }
}
