/**
 * Service for Slack API integration
 */
import { Injectable } from '@nestjs/common';
import { ApiclientService } from '../apiclient.service';
import { SlackAccessTokenDto, SlackApiResponse } from './slack-api.dto';
import { AxiosRequestConfig } from 'axios';

const SLACK_URL = 'https://slack.com/api/';

@Injectable()
export class SlackApiService {
  constructor(private readonly apiClientService: ApiclientService) {}

  private async callSlackApi<T = any>(config: AxiosRequestConfig, tokens, userRequest?: boolean) {
   try {
    let token:string;
    if (userRequest) token = tokens.authed_user.access_token;
    else token = tokens.access_token;
    if (
      config.headers['Content-Type'] === 'application/x-www-form-urlencoded'
    ) {
      config.data['token'] =  token;
      config.data = new URLSearchParams(config.data);
    } else {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return await this.apiClientService.rawApiRequest<T>(config);
   } catch (error) {
    return {
      ok: false,
      error: error?.message,
    }
   }
  }

  async getAccessToken(code: string) {
    const res = await this.apiClientService.rawApiRequest<
      Partial<SlackAccessTokenDto>
    >({
      method: 'POST',
      url: SLACK_URL + 'oauth.v2.access',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      data: new URLSearchParams({
        client_id: process.env.SLACK_CLIENT_ID,
        client_secret: process.env.SLACK_CLIENT_SECRET,
        code,
        grant_type: 'authorization_code',
        redirect_uri: process.env.BACKEND_URL + '/channels/slack/connect',
      }),
    });
    return res;
  }

  async revokeTokens(tokens) {
    await this.callSlackApi(
      {
        method: 'GET',
        url: SLACK_URL + 'auth.revoke',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      },
      tokens,
    );
  }

  async sendMessage(tokens, channel: string, text: string) {
    const res = await this.callSlackApi<SlackApiResponse>(
      {
        method: 'POST',
        url: SLACK_URL + 'chat.postMessage',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        data: {
          channel,
          text,
        },
      },
      tokens,
    );
    if (!res.ok && res.error) {
      throw new Error(res.error);
    }
    return res;
  }

  async joinChannel(tokens, channel: string) {
    const res = await this.callSlackApi<SlackApiResponse>(
      {
        method: 'POST',
        url: SLACK_URL + 'conversations.join',
        data: {
          channel: channel,
        },
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      },
      tokens,
      true,
    );
    if (!res?.ok && res.error) {
      throw new Error(res.error);
    }
    return res;
  }

  async inviteToChannel(tokens, channel: string, user: string) {
    const res = await this.callSlackApi<SlackApiResponse>(
      {
        method: 'POST',
        url: SLACK_URL + 'conversations.invite',
        data: {
          channel: channel,
          users: user,
        },
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      },
      tokens,
      true,
    );
    if (!res?.ok && res.error) {
      throw new Error(res.error);
    }
    return res;
  }
}
