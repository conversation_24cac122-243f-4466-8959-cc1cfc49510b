/**
 * Service for twilio API integration
 */
import twilio from 'twilio';

export interface SendTwilioMessageDto {
  accountSid: string;
  authToken: string;
  message: {
    from: string; // number in E.164 format : [+][country code][phone number including area code]
    to: string; // number in E.164 format : [+][country code][phone number including area code]
    body: string;
  };
}

export async function sendTwilioMessage(params: SendTwilioMessageDto) {
  const client = twilio(params.accountSid, params.authToken);
  const message = await client.messages.create(params.message);
  return message;
}
