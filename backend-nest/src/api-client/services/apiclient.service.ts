/**
 * Contains individual service implementations for different API integrations
 */
import { HttpService } from '@nestjs/axios';
import { Injectable } from '@nestjs/common';
import { AxiosRequestConfig, AxiosResponse } from 'axios';
import { firstValueFrom, catchError } from 'rxjs';
import { methods, CHANNELS } from '../utils/channel.constants';
import { GhlAPIs } from '../lib/ghlApi';
import { MyLogger } from 'src/logger/logger.service';

interface Iobject {
    [key: string]: any;
}

@Injectable()
export class ApiclientService {
    constructor(private readonly httpService: HttpService, private readonly logger: MyLogger) { }

    async apiRequest<T>(method: string, url: string, data?, config?: AxiosRequestConfig): Promise<T> {
        
        try {
            let response: AxiosResponse<T>;
            if (method === 'POST') {
                response = await this.httpService.axiosRef.post(url, data, config);
            } else if (method === 'GET') {
                response = await this.httpService.axiosRef.get(url, config);
            } else if (method === 'PUT') {
                response = await this.httpService.axiosRef.put(url, data, config);
            } else if (method === 'DELETE') {
                response = await this.httpService.axiosRef.delete(url, config);
            }
            return response.data;
        } catch (error) {
            throw error;
        }
    }

    async rawApiRequest<T>({ method = undefined, url = undefined, data = null, headers = null }): Promise<T> {
        try {
            let response: AxiosResponse<T> = await this.httpService.axiosRef.request({
                method,
                url,
                data,
                headers
            });
            return response?.data;
        } catch (error) {
            this.logger.error({ message: error?.response?.data, context: 'RAW_API_REQUEST' });
            throw error;
        }
    }

    async axiosRequest<T=any>(config:AxiosRequestConfig & {body?: any}): Promise<T> {
        if (!config.data && config.body) {
            config.data = config.body;
            delete config.body;
        }
        const r = await this.httpService.axiosRef.request({...config});
        return r.data;
    }
}
