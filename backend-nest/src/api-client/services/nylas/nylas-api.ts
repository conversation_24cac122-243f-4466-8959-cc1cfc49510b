/**
 * Service for <PERSON>ylas Outlook API integration
 */
import axios, { AxiosRequestConfig } from 'axios';
import Nyla<PERSON>, { WebhookTriggers } from 'nylas';
import { SendMessageParams } from 'nylas/lib/types/resources/messages';
import {
  CreateWebhookParams,
  DestroyWebhookParams,
  UpdateWebhookParams,
} from 'nylas/lib/types/resources/webhooks';

const nylasConfig = {
  nylasApiKey: process.env.NYLAS_API_KEY,
  nylasClientId: process.env.NYLAS_CLIENT_ID,
  nylasClientSecret: process.env.NYLAS_CLIENT_SECRET,
};

const nylasRedirectURIs = {
  outlook: process.env.BACKEND_URL + '/v1/channel/outlook/connect',
};

const nylas = new Nylas({
  apiKey: process.env.NYLAS_API_KEY,
});

export const getNylasAuthUrl = (orgId: string) => {
  const url = nylas.auth.urlForOAuth2({
    clientId: nylasConfig.nylasClientId,
    redirectUri: nylasRedirectURIs.outlook,
    accessType: 'online',
    state: orgId,
    provider: 'microsoft',
  });

  return url;
};

export const getNylasAccessToken = async ({ code }: { code: string }) => {
  const response = await nylas.auth.exchangeCodeForToken({
    clientId: nylasConfig.nylasClientId,
    redirectUri: nylasRedirectURIs.outlook,
    codeVerifier: 'insert-code-challenge-secret-hash',
    code,
  });

  return response;
};

export const createNylasWebhook = async (param: CreateWebhookParams) => {
  const webhook = await nylas.webhooks.create(param);
  return webhook;
};

export async function deleteWebhook(params: DestroyWebhookParams) {
  return await nylas.webhooks.destroy(params);
}

export async function sendNylasEmail(
  grantId: string,
  emailParams: SendMessageParams['requestBody'],
) {
  return await nylas.messages.send({
    identifier: grantId,
    requestBody: emailParams,
  });
}