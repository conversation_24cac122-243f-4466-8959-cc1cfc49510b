/**
 * Service for ChatHQ API integration
 */
import { Injectable } from '@nestjs/common';
import { ApiclientService } from '../apiclient.service';
import { AxiosRequestConfig } from 'axios';

function getLiveChatHeaders(token, userId) {
    return {
        'X-Auth-Token': token,
        'X-User-Id': userId,
    }
}

@Injectable()
export class ChatHqService {
    constructor(
        private readonly apiClientService: ApiclientService
    ) { }

    async getRooms(token: string, userId: string) {
        const options = {
            'method': 'GET',
            'url': `${process.env.CHATHQ_WORKSPACE_URL}/api/v1/livechat/rooms`,
            'headers': getLiveChatHeaders(token, userId)
        };
        return await this.apiClientService.rawApiRequest({ ...options });
    }

    /**
     * Get a room Info or open a new room
     */
    async getRoomInfo(token: string, userId: string, roomId: string = "") {
        const options = {
            'method': 'GET',
            'url': `${process.env.CHATHQ_WORKSPACE_URL}/api/v1/livechat/room?token=${token}`,
        };
        if (roomId)
            options.url += `&roomId=${roomId}`;
        if (userId)
            options.url += `&userId=${userId}`;
        return await this.apiClientService.rawApiRequest({ ...options });
    }
}
