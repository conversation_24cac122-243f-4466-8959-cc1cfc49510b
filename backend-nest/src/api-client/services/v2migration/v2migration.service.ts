/**
 * Service handling API migration from v2 to v3
 */
import { Injectable } from '@nestjs/common';
import { ApiclientService } from '../apiclient.service';
import { methods } from 'src/api-client/utils/channel.constants';
import { ICheckLocationResponse, IMigrateLocationResponse } from 'src/organization/dto/v2migration.dto';
import { MyLogger } from 'src/logger/logger.service';

@Injectable()
export class V2migrationService {
    constructor(
        private readonly apiClientService: ApiclientService,
        private readonly logger: MyLogger,
    ) { }

    async checkLocation({ locationId = '', companyId = '', accessToken = '', refreshToken = '' }) {
        try {
            const options = {
                method: methods.POST,
                url: process.env.MIGRATION_URL,
                data: {
                    path : 'check_location',
                    locationId,
                    agencyId: companyId,
                    accessToken,
                    refreshToken,
                }
            }
            const response = await this.apiClientService.rawApiRequest<ICheckLocationResponse>({
                ...options
            });
            return response;
        } catch (err) {
            this.logger.error({ message: err?.message, context: 'V2migrationService.checkLocation'});
        }
    }

    async migrateLocation(locationId) {
        try {
            const options = {
                method: methods.POST,
                url: process.env.MIGRATION_URL,
                data: {
                    path : 'start_migration',
                    locationId
                }
            }
            const response = await this.apiClientService.rawApiRequest<IMigrateLocationResponse>({
                ...options
            });
            return response;
        } catch (err) {
            this.logger.error({ message: err?.message, context: 'v2migrationService.migrateLocation'});
        }
    }

    async migrateFinish(locationId: string, orgId: string) {
        try {
            const options = {
                method: methods.POST,
                url: process.env.MIGRATION_URL,
                data: {
                    path : 'finish_migration',
                    locationId,
                    orgId,
                }
            }
            await this.apiClientService.rawApiRequest({
                ...options
            });
        } catch (err) {
            this.logger.error({ message: err?.message, context: 'v2migrationService.migrateFinish'});
        }
    }
}
