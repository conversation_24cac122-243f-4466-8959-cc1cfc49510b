/**
 * Service for vapi api
 */
import { BadRequestException, Injectable } from '@nestjs/common';
import { ApiclientService } from '../apiclient.service';
import { AxiosError, AxiosRequestConfig } from 'axios';
import { methods } from '../../utils/channel.constants';
import { KINDS, PROVIDERS } from 'src/lib/constant';
import { PodiumCreateAppointmentAPI } from 'src/api-client/dto/podium.dto';
import { MyLogger } from 'src/logger/logger.service';
import { MongoCredentialsService } from 'src/mongo/service/credentials/mongo-credentials.service';
import { UpdateAssistantDto } from 'src/voice/vapi/assistants/update-assistant.dto';
import * as FormData from 'form-data';

function getVapiHeader(token) {
  return {
    'Content-Type': 'application/json',
    Authorization: `Bearer ${token.accessToken}`,
  };
}

@Injectable()
export class vapiApiService {
  constructor(
    private readonly apiClientService: ApiclientService,
    private readonly logger: MyLogger,
    private readonly mongoCredentialService: MongoCredentialsService,
  ) {}

  async createAssistant<T = any>(token: string, { body }) {
    try {
      const options = {
        method: 'POST',
        url: 'https://api.vapi.ai/assistant',
        headers: {
          Authorization: 'Bearer ' + token,
          'Content-Type': 'application/json',
        },
        body,
      };
      return await this.apiClientService.axiosRequest<T>(options);
    } catch (error) {
      this.logger.error({
        message: error.message + ' - ' + JSON.stringify(error?.response?.data?.message),
        context: 'VAPI CREATE ASSISTANT',
      }) 
    }
  }

  async getAssistant<T = any>(
    token: string,
    { assistantId }: { assistantId: string },
  ) {
    const options = {
      method: 'GET',
      url: `https://api.vapi.ai/assistant/${assistantId}`,
      headers: { Authorization: 'Bearer ' + token },
    };

    return await this.apiClientService.axiosRequest<T>(options);
  }

  async updateAssistant<T = any>(
    token: string,
    { body, assistantId }: { body: Partial<UpdateAssistantDto>; assistantId: string },
  ) {
    try {
      const options = {
        method: 'PATCH',
        url: `https://api.vapi.ai/assistant/${assistantId}`,
        headers: {
          Authorization: 'Bearer ' + token,
          'Content-Type': 'application/json',
        },
        body,
      };
  
      return await this.apiClientService.axiosRequest<T>(options);
    } catch (error) {
      throw new BadRequestException(`Update vapi assistant error - ${error?.response?.data?.message ?? error?.message}`);
    }
  }

  async deleteAssistant<T = any>(
    token: string,
    { assistantId }: { assistantId: string },
  ) {
    const options = {
      method: 'DELETE',
      url: `https://api.vapi.ai/assistant/${assistantId}`,
      headers: { Authorization: 'Bearer ' + token },
    };

    return await this.apiClientService.axiosRequest<T>(options);
  }

  async createPhoneNumber<T = any>(token: string, { body }: { body: any }) {
    try {
      const options = {
        method: 'POST',
        url: 'https://api.vapi.ai/phone-number',
        headers: {
          Authorization: 'Bearer ' + token,
          'Content-Type': 'application/json',
        },
        body,
      };
  
      return await this.apiClientService.axiosRequest<T>(options);
    } catch (error) {
      throw new BadRequestException(
        `Create vapi phone number error - ${error?.response?.data?.message ?? error?.message}`,
      );
    }
  }

  async getPhoneNumber<T = any>(
    token: string,
    { phoneId }: { phoneId: string },
  ) {
    const options = {
      method: 'GET',
      headers: {Authorization: 'Bearer ' + token},
      url: `https://api.vapi.ai/phone-number/${phoneId}`,
    };

    return await this.apiClientService.axiosRequest<T>(options);
  }

  async deletePhoneNumber<T = any>(
    token: string,
    { phoneId }: { phoneId: string },
  ) {
    const options = {
      method: 'DELETE',
      headers: {Authorization: 'Bearer ' + token},
      url: `https://api.vapi.ai/phone-number/${phoneId}`,
    };

    return await this.apiClientService.axiosRequest<T>(options);
  }

  async updatePhoneNumber<T = any>(
    token: string,
    { body, phoneId }: { body: any; phoneId: string },
  ) {
    try {
      const options = {
        method: 'PATCH',
        headers: {
          Authorization: 'Bearer ' + token,
          'Content-Type': 'application/json'
        },
        body,
        url: `https://api.vapi.ai/phone-number/${phoneId}`
      };
  
      return await this.apiClientService.axiosRequest<T>(options);
    } catch (error) {
      throw new BadRequestException(`Update vapi phone number error - ${error?.response?.data?.message ?? error?.message}`);
    }
  }

  async getTool<T = any>(token: string, toolId: string) {
    const options = {
      method: 'GET',
      headers: {Authorization: 'Bearer ' + token},
      url: 'https://api.vapi.ai/tool/'+ toolId,
    };
    
    return await this.apiClientService.axiosRequest<T>(options);
  }

  async createTool<T = any>(token: string, { body }) {
      const options = {
        url: 'https://api.vapi.ai/tool',
        method: 'POST',
        headers: {
          Authorization: 'Bearer ' + token,
          'Content-Type': 'application/json',
        },
        body: body,
      };
      return await this.apiClientService.axiosRequest<T>(options);
  }

  async listTools<T = any>(token: string) {
    const options = {
      url: 'https://api.vapi.ai/tool',
      method: 'GET',
      headers: {Authorization: 'Bearer ' + token},
    };
    return await this.apiClientService.axiosRequest<T>(options);
  }

  async updateTool<T = any>(token: string, { body, toolId }) {
    const options = {
      url: `https://api.vapi.ai/tool/${toolId}`,
      method: 'PATCH',
      headers: {
        Authorization: 'Bearer ' + token,
        'Content-Type': 'application/json',
      },
      body,
    };
    return await this.apiClientService.axiosRequest<T>(options);
  }

  async deleteTool<T = any>(token: string, { toolId }) {
    const options = {
      url: `https://api.vapi.ai/tool/${toolId}`,
      method: 'DELETE',
      headers: {Authorization: 'Bearer ' + token},
    };
    return await this.apiClientService.axiosRequest<T>(options);
  }

  async listCalls<T = any>(token: string, assistantId: string, limit?: number, createdAtGe?: string, createdAtLe?: string ) {
    try {
      const options = {
        method: 'GET',
        headers: {Authorization: 'Bearer ' + token},
        url: 'https://api.vapi.ai/call?assistantId=' + assistantId,
      };
  
      if (limit) options.url += `&limit=${limit}`
      if (createdAtGe) options.url += `&createdAtGe=${createdAtGe}`
      if (createdAtLe) options.url += `&createdAtLe=${createdAtLe}`
      
      return await this.apiClientService.axiosRequest<T>(options);

    } catch (err) {
      this.logger.error({
        message: `Call failed - ${err?.response?.data?.message ?? err?.message}`,
        context: `VAPI API GET CALL`
      })

      throw new Error(err?.response?.data?.message ?? err?.message);
    }
  }

  async getCall<T = any>(token: string, callId: string){
    try {
      const options = {
        method: 'GET',
        headers: {Authorization: 'Bearer ' + token},
        url: `https://api.vapi.ai/call/${callId}`,
      };
      
      return await this.apiClientService.axiosRequest<T>(options);
    } catch (error) {
      throw new Error(`Get vapi call error - ${error?.response?.data?.message ?? error?.message}`);
    }
  }

  async uploadFile<T = any>(token: string, { textContent, fileName }: {textContent: string, fileName: string}) {
    try{ 
      const form = new FormData();
      form.append('file', Buffer.from(textContent), {
        filename: fileName+'.txt',     // Name of the file
        contentType: 'text/plain',  // Content type of the file
      });
      
      const options: AxiosRequestConfig = {
        method: 'POST',
        headers: {Authorization: 'Bearer ' + token, 'Content-Type': 'multipart/form-data'},
        url: 'https://api.vapi.ai/file',
        data: form
      };
      return await this.apiClientService.axiosRequest<T>(options);
    }
    catch (error){
      throw new Error(`upload vapi file error - ${error?.response?.data?.message ?? error?.message}`);
    }
  }

  async deleteFile(token: string,id: string) {
    try {
      const options = {
        method: 'DELETE',
        headers: {Authorization: 'Bearer ' + token, 'Content-Type': 'multipart/form-data'},
        url: `https://api.vapi.ai/file/${id}`,
      };
      
      return await this.apiClientService.axiosRequest(options);
    } catch (error) {
      throw new Error(`vapi delete file error - ${error?.response?.data?.message ?? error?.message}`);
    }
  }
}
