/**
 * Service for Podium API integration
 */
import { Injectable } from '@nestjs/common';
import { ApiclientService } from '../apiclient.service';
import { AxiosError, AxiosRequestConfig } from 'axios';
import { methods } from '../../utils/channel.constants';
import { KINDS, PROVIDERS } from 'src/lib/constant';
import { PodiumCreateAppointmentAPI } from 'src/api-client/dto/podium.dto';
import { MyLogger } from 'src/logger/logger.service';
import { MongoCredentialsService } from 'src/mongo/service/credentials/mongo-credentials.service';

interface authDto {
    accessToken: string;
    refreshToken: string;
}

function getPodiumHeader(token: authDto) {
    return {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token.accessToken}`
    }
}

@Injectable()
export class PodiumAPIService {
    constructor(
        private readonly apiClientService: ApiclientService,
        private readonly logger: MyLogger,
        private readonly mongoCredentialService: MongoCredentialsService,
    ) { }

    makePodiumApiCall = async (method: string, url: string, data?, config?: AxiosRequestConfig, tokens: authDto = null, podiumOrgId?: string): Promise<any> => {
        try {
            const res = await this.apiClientService.apiRequest(method, url, data, config);
            return res;
        } catch (error) {
            let e: AxiosError = error;
            if (e.response.status === 401) {
                const { accessToken, refreshToken } = await this.exchangeCode(undefined, tokens.refreshToken);
                config.headers['Authorization'] = `Bearer ${accessToken}`;
                if (accessToken && podiumOrgId){
                    await this.mongoCredentialService.updateCredentials({
                        query: {kind: KINDS.PODIUM_CREDENTIAL,
                            podiumOrgId: podiumOrgId},
                            updateBody:{
                                creds: {
                                    accessToken, refreshToken
                                }
                            }
                    })
                }
                const res = await this.apiClientService.apiRequest(method, url, data, config);
                tokens['accessToken'] = accessToken;
                tokens['refreshToken'] = refreshToken;
                return res;
            } else {
                this.logger.error({ message: `Request to ${url} failed with message: ${error.response.message}`, context: 'PODIUM_API_CALL'});
                this.logger.error({ message: error.response.data, context: 'PodiumAPIService.makePodiumApiCall'});
                throw error;
            }
        }
    }

    exchangeCode = async (code?: string, refresh_token?: string) => {
        const configBody = {
            url: 'https://api.podium.com/oauth/token',
            data: {
                client_id: process.env.PODIUM_CLIENT_ID,
                client_secret: process.env.PODIUM_CLIENT_SECRET,
            },
        }
        if (code) {
            configBody.data['code'] = code;
            configBody.data['grant_type'] = 'authorization_code';
            configBody.data['redirect_uri'] = process.env.BACKEND_URL + '/podium/connect';
        } else {
            configBody.data['refresh_token'] = refresh_token;
            configBody.data['grant_type'] = 'refresh_token';
        }
        const res: any = await this.apiClientService.apiRequest(methods.POST, configBody.url, configBody.data);
        return {accessToken : res.access_token, refreshToken: res.refresh_token};
    }

    getPodiumLocation = async (reqId:string, tokens: authDto, limit: number = 20, cursor?: string, uid?: string, organizationUid?: string) => {
        try {
            const headers = getPodiumHeader(tokens);
            let url = `https://api.podium.com/v4/locations`;
            if (uid) url += `/${uid}`;
            else url += `?limit=${limit}&cursor=${cursor || ''}`;
            const res = await this.makePodiumApiCall(methods.GET, url, null, { headers }, tokens, organizationUid);
            return res.data;
        } catch (err) {
            this.logger.error({ message: `error | getting podium location | error: ${err.message}`, context: 'PodiumAPIService.getPodiumLocation'});
        }
    }

    createWebhook = async (tokens: authDto, locationUid?: string, organizationUid?: string) => {
        const url = `https://api.podium.com/v4/webhooks`;
        const headers = getPodiumHeader(tokens);
        const data = {
          url: `${process.env.BACKEND_URL}/webhook/${PROVIDERS.PODIUM}`,
          eventTypes: ['message.received', "message.sent", "contact.deleted"],
        };
        if (!(locationUid || organizationUid)) throw new Error("Either of locationUid or organizationUid must be provided");
        if (locationUid) data['locationUid'] = locationUid;
        else if (organizationUid) data['organizationUid'] = organizationUid;
        const res = await this.makePodiumApiCall(methods.POST, url, data, { headers }, tokens, organizationUid);
        return res.data;
    }

    /**
     * @param channel 'phone' | 'email'
     * @param identifier phone number or email Id
     */
    sendMessage = async (tokens: authDto, channel: string, identifier: string, body: string, locationUid: string, organizationUid: string) => {
        const url = `https://api.podium.com/v4/messages`;
        const headers = getPodiumHeader(tokens);
        const data = {
            channel: {
                type: channel,
                identifier,
            },
            body,
            locationUid,
        }
        const res = await this.makePodiumApiCall(methods.POST, url, data, { headers }, tokens, organizationUid);
        return res.data;
    }

    bookAppointment = async (tokens: authDto, locationUid: string, body: PodiumCreateAppointmentAPI, organizationUid: string) => {
        const url = `https://api.podium.com/v4/appointments`;
        const headers = getPodiumHeader(tokens);
        const data = {
            locationUid,
            ...body,
        }
        const res = await this.makePodiumApiCall(methods.POST, url, data, { headers }, tokens, organizationUid);
        return res.data;
    }

    deleteWebhook = async (tokens: authDto, webhookId: string, organizationUid: string) => {
        const url = `https://api.podium.com/v4/webhooks/${webhookId}`;
        const headers = getPodiumHeader(tokens);
        const res = await this.makePodiumApiCall(methods.DELETE, url, null, { headers }, tokens, organizationUid);
        return res.data;
    }

    getContact = async (reqId:string, tokens: authDto, conversationUid?: string, email?: string, phone?: string, organizationUid?: string) => {
        try {
            const url = `https://api.podium.com/v4/contacts/${phone || email || conversationUid}`;
            const headers = getPodiumHeader(tokens);
            const res = await this.makePodiumApiCall(methods.GET, url, null, { headers }, tokens, organizationUid);
            return res.data;
        } catch (err) {
            this.logger.error({ message: `error | getting podium contact| error: ${err.message}`, context: 'PodiumAPIService.getContact'});
        }
    }

    createContactTag = async (tokens: authDto, tag: string, organizationUid: string) =>{
        try {
            const url = `https://api.podium.com/v4/contact_tags`;
            const headers = getPodiumHeader(tokens);
            const data = {
                description: tag,
                label: tag
            }
            const res = await this.makePodiumApiCall(methods.POST, url, data, {headers}, tokens, organizationUid)
            return res;
        } catch (error) {
            this.logger.error({ message: `error | creating podium tag| error: ${error.message}`, context: 'PodiumAPIService.createContactTag'});
            return null
        }
    }

    addContactTag = async (tokens: authDto, conversationUid: string, tagUid: string, organizationUid: string) =>{
        try {
            const url = `https://api.podium.com/v4/contacts/${conversationUid}/tags/${tagUid}`;
            const headers = getPodiumHeader(tokens);
            const res = await this.makePodiumApiCall(methods.POST, url, undefined, {headers}, tokens, organizationUid);
            
        } catch (error) {
            this.logger.error({ message: `error | creating contact tag| error: ${error.message}`, context: 'PodiumAPIService.addContactTag'});
            return null
        }
    }

    updateContact = async (tokens: authDto, conversationUid, phone, email, name, organizationUid) => {
        try {
            let identifier, data=null;
            if (name) data['name']=name;
            if (conversationUid) identifier = conversationUid;
            else if (phone) identifier = phone;
            else identifier = email;
            
            if (!identifier) throw new Error('No podium identifier passed');
            if(!data) throw new Error("No fields passed")
            const url=`https://api.podium.com/v4/contacts/${identifier}`
            const headers = getPodiumHeader(tokens);
            const res = await this.makePodiumApiCall(methods.POST, url, data, {headers}, tokens, organizationUid);
        } catch (error) {
            this.logger.error({ message: `error | creating podium tag| error: ${error.message}`, context: 'PodiumAPIService.createContactTag'});
            return null
        }
    }
}
