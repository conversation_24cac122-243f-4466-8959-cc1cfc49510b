import { HttpModule } from '@nestjs/axios';
import { Global, Module } from '@nestjs/common';
import { ApiclientService } from './services/apiclient.service';
import { GhlApisService } from './services/ghl-apis/ghl-apis.service';
import { PodiumAPIService } from './services/podium/podium-api.service';
import { ChatHqService } from './services/chat-hq/chat-hq.service';
import { V2migrationService } from './services/v2migration/v2migration.service';
import { NotificationModule } from 'src/notification/notification.module';
import { LoggerModule } from 'src/logger/logger.module';
import { HubspotAPIService } from './services/hubspot/hubspot-api.service';
import { SlackApiService } from './services/slack-api/slack-api.service';
import { vapiApiService } from './services/vapi/vapi-api.service';

@Global()
@Module({
  imports: [HttpModule, NotificationModule, LoggerModule],
  providers: [ApiclientService, GhlApisService, PodiumAPIService, ChatHqService, V2migrationService, HubspotAPIService, SlackApiService, vapiApiService],
  exports: [ApiclientService, GhlApisService, PodiumAPIService, ChatHqService, V2migrationService, HubspotAPIService, SlackApiService, vapiApiService],
})
export class ApiClientModule { }
