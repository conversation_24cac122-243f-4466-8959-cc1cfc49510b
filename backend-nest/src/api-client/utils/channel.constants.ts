interface IObjects {
    [key: string]: string;
}

export const methods = {
    GET: 'GET',
    POST: 'POST',
    PUT: 'PUT',
    DELETE: 'DELETE',
    PATCH: 'PATCH'
}

export const CHANNELS = {
    GHL: 'ghl'
}

const GHL_V2_BASE_URL = 'https://services.leadconnectorhq.com'
const HUBSPOT_BASE_URL = 'https://api.hubapi.com'

export const GhlApiURL: IObjects = {
    GET_ACCESS_TOKEN_URL: GHL_V2_BASE_URL + '/oauth/token',
    GET_BUSINESS_BY_LOCATION_URL: GHL_V2_BASE_URL + '/businesses',
    GET_CALENDARS_URL: GHL_V2_BASE_URL + '/calendars/',
    GET_LOCATION_URL: GHL_V2_BASE_URL + '/locations',
}

export const HubspotApiURL: IObjects = {
    GET_ACCESS_TOKEN_URL: HUBSPOT_BASE_URL + '/oauth/v1/token',
}