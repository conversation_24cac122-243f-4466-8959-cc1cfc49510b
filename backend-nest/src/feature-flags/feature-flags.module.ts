import { Module } from '@nestjs/common';
import { FeatureFlagService } from './feature-flags.service';
import { WhitelistBlacklistService } from './whitelist-blacklist.service';
import { MongooseModule } from '@nestjs/mongoose';
import {
  Organization,
  OrganizationSchema,
} from 'src/mongo/schemas/organization/organization.schema';
import { FeatureAccessGuard } from 'src/guards/feature-access.guard';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Organization.name, schema: OrganizationSchema },
    ]),
  ],
  providers: [
    FeatureFlagService,
    WhitelistBlacklistService,
    FeatureAccessGuard,
  ],
  exports: [FeatureFlagService, WhitelistBlacklistService, FeatureAccessGuard],
})
export class FeatureFlagsModule {}
