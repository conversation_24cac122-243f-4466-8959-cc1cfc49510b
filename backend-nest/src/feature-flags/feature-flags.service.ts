import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Organization } from 'src/mongo/schemas/organization/organization.schema';

@Injectable()
export class FeatureFlagService {
  constructor(
    @InjectModel(Organization.name)
    private organizationModel: Model<Organization>,
  ) {}

  async isFeatureEnabled(orgId: string, featureName: string): Promise<boolean> {
    const org = await this.organizationModel.findById(orgId).exec();
    if (!org) return false;

    return (
      org.isWhitelisted && !org.isBlacklisted && org.featureFlags[featureName]
    );
  }

  async updateFeatureFlag(
    orgId: string,
    featureName: string,
    value: boolean,
  ): Promise<void> {
    await this.organizationModel
      .findByIdAndUpdate(orgId, {
        [`featureFlags.${featureName}`]: value,
      })
      .exec();
  }
}
