import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Organization } from 'src/mongo/schemas/organization/organization.schema';

@Injectable()
export class WhitelistBlacklistService {
  constructor(
    @InjectModel(Organization.name)
    private organizationModel: Model<Organization>,
  ) {}

  async addToWhitelist(orgId: string): Promise<void> {
    await this.organizationModel
      .findByIdAndUpdate(orgId, { isWhitelisted: true })
      .exec();
  }

  async removeFromWhitelist(orgId: string): Promise<void> {
    await this.organizationModel
      .findByIdAndUpdate(orgId, { isWhitelisted: false })
      .exec();
  }

  async addToBlacklist(orgId: string): Promise<void> {
    await this.organizationModel
      .findByIdAndUpdate(orgId, { isBlacklisted: true })
      .exec();
  }

  async removeFromBlacklist(orgId: string): Promise<void> {
    await this.organizationModel
      .findByIdAndUpdate(orgId, { isBlacklisted: false })
      .exec();
  }

  async getWhitelistedOrganizations(): Promise<Organization[]> {
    return this.organizationModel.find({ isWhitelisted: true }).exec();
  }

  async getBlacklistedOrganizations(): Promise<Organization[]> {
    return this.organizationModel.find({ isBlacklisted: true }).exec();
  }
}
