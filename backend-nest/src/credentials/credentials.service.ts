import { Injectable } from '@nestjs/common';
import { MongoCredentialsService } from 'src/mongo/service/credentials/mongo-credentials.service';

@Injectable()
export class CredentialsService {
  constructor(
    private readonly mongoCredentialsService: MongoCredentialsService,
  ) {}

  async createCredential(createCredentialDto: any) {
    const createdCredential = await this.mongoCredentialsService.createCredentials(
      createCredentialDto,
    );
    return createdCredential;
  }
}