import { IsString, IsNotEmpty, IsUrl, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

export class CreateCredentialDto {
  @IsString()
  @IsNotEmpty()
  readonly userId: string;
  @IsString()
  @IsNotEmpty()
  readonly kind:
    | 'GhlCredential'
    | 'GoogleCredential'
    | 'OpenaiCredential'
    | 'ClaudeCredential'
    | 'CalendlyCredential'
    | 'GroqCredential'
  
  @IsString()
  @IsNotEmpty()
  readonly organizationId: string;
  @IsString()
  @IsNotEmpty()
  readonly name: string;
  @IsString()
  @IsNotEmpty()
  readonly providerName: string;
  readonly locationId: string;
  readonly creds: Object;
}
