import { Body, Controller, Post } from '@nestjs/common';
import { CredentialsService } from './credentials.service';
import { CreateCredentialDto } from 'src/mongo/dto/credentials/create.dto';

@Controller('credentials')
export class CredentialsController {
  constructor(
    private readonly credentialService: CredentialsService
  ) { }

  @Post('/create')
  async createCredential(@Body() createCredentialDto: CreateCredentialDto) {
    return this.credentialService.createCredential(createCredentialDto);
  }
}
