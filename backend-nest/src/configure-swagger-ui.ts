import { INestApplication } from '@nestjs/common';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';

export type SwaggerUIOptions = {
    swaggerEnabled: boolean;
    swaggerPath: string;
    appName: string;
    appVersion?: string;
    apiUrl?: string;
    apiEnvironmentName?: string;
};

export function configureSwaggerUI(
    nestApp: INestApplication,
    options: SwaggerUIOptions
) {
    if (!options.swaggerEnabled) {
        return `SwaggerUI not enabled`;
    }

    // configure OpenAPI spec document
    const docBuilder = new DocumentBuilder()
        .setTitle(options.appName)
        .setVersion(options.appVersion)

    // add the appropriate server entry
    docBuilder.addServer(options.apiUrl, options.apiEnvironmentName);

    // build OpenAPI spec document
    const config = docBuilder.build();
    const document = SwaggerModule.createDocument(nestApp, config);

    // configure SwaggerUI
    SwaggerModule.setup(options.swaggerPath, nestApp, document, {
        swaggerOptions: {
            persistAuthorization: true,
        },
        customSiteTitle: options.appName,
    });
    return `${options.apiUrl}${options.swaggerPath}`;
}