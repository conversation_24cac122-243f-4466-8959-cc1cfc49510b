import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import {
  Organization,
  OrganizationModel,
} from 'src/mongo/schemas/organization/organization.schema';

@Injectable()
export class MongoCronService {
  constructor(
    @InjectModel(Organization.name)
    private readonly organizationModel: Model<OrganizationModel>,
  ) {}
  async getManyOrganizations(
    query: Object,
    projection: Object = {},
    options: Object = { lean: true },
  ) {
    const organizations = await this.organizationModel
      .find(query, projection, options)
      .exec();
    return organizations;
  }
  async updateManyOrganizations(
    query: Object,
    update: Object,
    options: Object = { new: true },
  ) {
    const org = await this.organizationModel
      .updateMany(query, update, options)
      .exec();
    return org;
  }
}
