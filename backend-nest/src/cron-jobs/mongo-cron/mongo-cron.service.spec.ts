import { Test, TestingModule } from '@nestjs/testing';
import { MongoCronService } from './mongo-cron.service';

describe('MongoCronService', () => {
  let service: MongoCronService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [MongoCronService],
    }).compile();

    service = module.get<MongoCronService>(MongoCronService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
