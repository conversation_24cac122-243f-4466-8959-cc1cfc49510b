import { Injectable } from '@nestjs/common';
import { MongoCronService } from './mongo-cron/mongo-cron.service';
import { InjectModel } from '@nestjs/mongoose';
import { Organization } from 'src/mongo/schemas/organization/organization.schema';
import { Model } from 'mongoose';
import {
  Credential,
  HydratedCredentialDocument,
} from 'src/mongo/schemas/credential/credentials.schema';

@Injectable()
export class CronJobsService {
  constructor(
    private readonly mongoCronService: MongoCronService,
    @InjectModel(Credential.name)
    private readonly credentailsModel: Model<HydratedCredentialDocument>,
  ) {}
  async validatePlans() {
    const date14DaysAgo = new Date(Date.now() - 15 * 24 * 60 * 60 * 1000);

    // Fetch organizations that match the criteria
    const organizations = await this.mongoCronService.getManyOrganizations({
      'billing.plan': 'trial',
      'billing.status': { $ne: 'suspended' },
      'billing.startDate': { $lt: date14DaysAgo.getTime() },
    });

    // Extract the _id of each organization
    const organizationIds = organizations.map((org) => ({
      _id: org._id,
      contactId: org.contactId,
    }));

    // Perform the update operation
    await this.mongoCronService.updateManyOrganizations(
      {
        'billing.plan': 'trial',
        'billing.startDate': { $lt: date14DaysAgo.getTime() },
        'billing.status': { $ne: 'suspended' },
      },
      {
        $set: { 'billing.status': 'suspended' },
      },
    );

    // Add a tag to the GHL contact
    for (let i = 0; i < organizationIds.length; i++) {
      const contactId = organizationIds[i].contactId;
      

      await this.updateGhlContactTag(contactId);
    }

    // Return the _id of the updated organizations
    return organizationIds;
  }

  async updateGhlContactTag(contactId: string) {
    const locationId = 'hqD2EpUwBJg1nEBWr4jT';
    const credentialDoc = await this.credentailsModel.findOne({
      $and: [{ kind: 'GhlCredential' }, { keyId: locationId }],
    });
    const bearerToken = credentialDoc.creds.accessToken;
    try {
      const response = await fetch(
        `https://services.leadconnectorhq.com/contacts/${contactId}/tags`,
        {
          method: 'POST',
          headers: {
            Accept: 'application/json',
            Authorization: `Bearer ${bearerToken}`,
            'Content-Type': 'application/json',
            Version: '2021-07-28',
          },
          body: JSON.stringify({
            tags: ['suspended'],
          }),
        },
      );
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      
      throw error;
    }
  }
}
