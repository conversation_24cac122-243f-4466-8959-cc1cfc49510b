import { Controller, Get } from '@nestjs/common';
import { CronJobsService } from './cron-jobs.service';
import { Cron, CronExpression } from '@nestjs/schedule';

@Controller('cron-jobs')
export class CronJobsController {
  constructor(private readonly cronJobsService: CronJobsService) {}

  @Cron(CronExpression.EVERY_DAY_AT_4AM)
  async checkBillingStatus() {
    try {
      
      const orgIds = await this.cronJobsService.validatePlans();
      
    } catch (error) {
      
    }
  }

  @Get()
  async test() {
    return { message: 'Cron Jobs Controller' };
  }
}
