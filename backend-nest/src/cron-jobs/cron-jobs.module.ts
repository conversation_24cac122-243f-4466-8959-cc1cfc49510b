import { Module } from '@nestjs/common';
import { CronJobsService } from './cron-jobs.service';
import { CronJobsController } from './cron-jobs.controller';
import { MongoCronService } from './mongo-cron/mongo-cron.service';
import { MongooseModule } from '@nestjs/mongoose';
import {
  Organization,
  OrganizationSchema,
} from 'src/mongo/schemas/organization/organization.schema';
import { MongoModule } from 'src/mongo/mongo.module';
import {
  Credential,
  CredentialsSchema,
} from 'src/mongo/schemas/credential/credentials.schema';

@Module({
  imports: [
    MongoModule,
    MongooseModule.forFeature([
      { name: Organization.name, schema: OrganizationSchema },
    ]),
    MongooseModule.forFeature([
      { name: Credential.name, schema: CredentialsSchema },
    ]),
  ],
  controllers: [CronJobsController],
  providers: [CronJobsService, MongoCronService],
  exports: [
    MongooseModule.forFeature([
      { name: Organization.name, schema: OrganizationSchema },
    ]),
    MongooseModule.forFeature([
      { name: Credential.name, schema: CredentialsSchema },
    ]),
  ],
})
export class CronJobsModule {}
