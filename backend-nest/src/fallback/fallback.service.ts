import {
  Injectable,
  Res,
  HttpStatus,
  Inject,
  forwardRef,
} from '@nestjs/common';
import { Response } from 'express';
import { MongoFallbackService } from 'src/mongo/service/fallback/mongo-fallback.service';
import { CreateFallbackDto } from './dto/create.fallback.dto';
import { GetAllFallbackDto } from './dto/get.all.fallback.dto';
import { AgentDocument } from 'src/mongo/schemas/agents/agents.schema';
import { MongoAgentService } from 'src/mongo/service/agent/mongo-agent.service';
import { EditAcknowledgementFallbackDto } from './dto/edit.fallback.acknowledge.dto';
import { PineconeService } from 'src/vector/services/pinecone/pinecone.service';
import { IFallbackStructure } from 'src/interfaces/interfaces';
import { VectorService } from 'src/vector/services/vector.service';
import { MongoOrganizationService } from 'src/mongo/service/organization/mongo-organization.service';
import { MongoCredentialsService } from 'src/mongo/service/credentials/mongo-credentials.service';
import { MongoSessionService } from 'src/mongo/service/session/mongo-session.service';
import { CRMTYPE, KINDS } from 'src/lib/constant';
import { GhlService } from 'src/organization/services/channels/ghl/ghl.service';
import { MyLogger } from '../logger/logger.service';

interface IEnrichedFallback {
  _id: string;
  providerName: string;
  dateTime: string;
  contactMessage: string;
  aiResponse: string;
  agentId: string;
  agentName: string;
}

@Injectable()
export class FallbackService {
  constructor(
    private readonly mongoFallbackService: MongoFallbackService,
    private readonly mongoAgentService: MongoAgentService,
    private readonly pineconeService: PineconeService,
    private readonly vectorService: VectorService,
    private readonly mongoOrganizationService: MongoOrganizationService,
    private readonly mongoCredentialService: MongoCredentialsService,
    private readonly mongoSessionService: MongoSessionService,
    private readonly logger: MyLogger
  ) {}
  async createFallback(createFallback, response?) {
    try {
      const newFallback = await this.mongoFallbackService.createFallback(
        createFallback,
      );

      if (response) {
        return response.status(HttpStatus.CREATED).json({
          message: 'Fallback Created Successfully',
          data: newFallback,
        });
      }
    } catch (error) {
      if (response) {
        this.logger.error({
          message: `Fallback Creation Failed with error: ${error.message}`,
          context: this.createFallback.name,
        });
        return response
          .status(HttpStatus.INTERNAL_SERVER_ERROR)
          .json({ error: 'Internal Server Error' });
      }
    }
  }

  async getOneFallback(fallbackId: string, @Res() response: Response) {
    try {
      const fallback = await this.mongoFallbackService.getOneFallback({ _id: fallbackId });
      return response.json({
        message: 'Fallback fetched successfully',
        data: fallback,
      });
    } catch (error) {
      this.logger.error({
        message: `Failed to fetch fallback with id: ${fallbackId} with error: ${error.message}`,
        context: this.createFallback.name,
      });
      return response.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        error: 'Internal Server Error',
      });
    }
  }

  async getAllFallback(
    payload: { offset: number; limit: number; orgId: string },
    response: Response,
  ) {
    try {      
      const { offset, limit, orgId } = payload;
      const allFallbackObj = await this.mongoFallbackService.findFallbacks({
        filter: { orgId },
        limit,
        offset,
      });
      
      const { data: allFallback, total } = allFallbackObj;
      const agentIdSet = new Set<string>();

      for (let fallback of allFallback) {
        agentIdSet.add(fallback.agentId);
      }
      const agents: AgentDocument[] = await this.mongoAgentService.getAgents({
        _id: { $in: Array.from(agentIdSet) },
      });
      const agentLookup = {};
      for (let agent of agents) {
        agentLookup[agent._id.toString()] = agent;
      }
      const resList: IEnrichedFallback[] = [];
      for (let fallback of allFallback) {
        const enrichedFallback: IEnrichedFallback = fallback.toObject();
        if (agentLookup[fallback.agentId]) {
          enrichedFallback.agentName = agentLookup[fallback.agentId].agentName;
        }
        resList.push(enrichedFallback);
      }
      if (response) {
        return response.status(HttpStatus.OK).json({
          message: 'All Fallback Of The Organization Fetched',
          data: resList,
          total,
        });
      }
    } catch (error) {
      if (response) {
        this.logger.error({
          message: `Failed to list fallbacks with error: ${error.message} for orgId: ${payload.orgId}`,
          context: this.getAllFallback.name,
        });
        return response
          .status(HttpStatus.INTERNAL_SERVER_ERROR)
          .json({ error: 'Internal Server Error' });
      }
    }
  }

  async updateFallbackStatus(editFallback, response?) {
    try {
      const newFallback = await this.mongoFallbackService.updateFallbackStatus(
        editFallback,
      );

      if (response) {
        return response.status(HttpStatus.CREATED).json({
          message: 'Fallback Status Updated Successfully',
          data: newFallback,
        });
      }
    } catch (error) {
      if (response) {
        return response
          .status(HttpStatus.INTERNAL_SERVER_ERROR)
          .json({ error: 'Internal Server Error' });
      }
    }
  }

  // async upsertFallback(event, orgid){
  //   const pineconeRecord = [];
  //   pineconeRecord.push(event);
  //   const namespace = orgid;
  //   // save the embeddings to pinecone
  //   const index = await this.pineconeService.connectIndex(process.env.PINECONE_INDEX);
  //   const createdIndex = await this.pineconeService.upsertVectors(index, pineconeRecord, namespace); 

  //   return createdIndex;
  // }

  // async getConversation(sessionId){
  //   const sessionData = await this.mongoSessionService.getSession(sessionId);
  //   //save the chat embeddings to Pinecone
  //   const conversationHistory = sessionData.events.flatMap((event) => {
  //     const messageHuman = event.message;
  //     const botResponses = event.botResponse.filter(
  //       (botResponse) => botResponse.sender === "bot" && botResponse.kind === "message"
  //     );
  //     const botMessages = botResponses.map((message) => message.eventData.message);
  //     if(messageHuman !== ''){
  //       return [
  //         `user: ${messageHuman}\nassistant: ${botMessages}`
  //       ];
  //     }else{
  //       return [
  //         `assistant: ${botMessages}\n`
  //       ];
  //     }
  //     // return [
  //     //   `user: ${messageHuman}\nassistant: ${botMessages}`
  //     // ];
  //   }).join('\n\n');
  //    this.logger.log({
  //      message: `session sample upserted for session id: ${sessionId} with conversation history: ${conversationHistory}`,
  //      context: this.getConversation.name,
  //    });
  //   return conversationHistory;
  // }

  // async upsertFallbackDetails(editFallback: EditAcknowledgementFallbackDto, fallbackData){

  //   try {
  //     const orgid = fallbackData.orgId;
  //     const agentId = fallbackData.agentId;
  //     const finalResponse = fallbackData.aiResponse;
  //     const agentData = await this.mongoAgentService.getAgent({_id: agentId});
  //     const executionId = fallbackData.executionDetails.executionId;

  //     // get credentialId from agentData
  //     const accountId = agentData.aiProvider.accountId;
  //     let credentialId: string;
  //     const organizationDetails = await this.mongoOrganizationService.getOrganization({ _id: orgid });
  //     organizationDetails.connections.aiProvider.forEach(aiProvider=>{
  //       if(aiProvider.accountId == accountId){
  //         credentialId = aiProvider.credentialId;
  //       }
  //     })
  //     const credentialData = await this.mongoCredentialService.getCredential({ _id: credentialId, kind: KINDS.OPENAI_CREDENTIAL });
  //     const secretKey = credentialData['creds'].secret;
  //     if(typeof secretKey == undefined){ 
  //        this.logger.error({
  //          message: `Undefined OpenAI Secret Key.`,
  //          context: this.upsertFallbackDetails.name,
  //        });
  //     }

  //     let conversationHistoryString: string = '';

  //     if(fallbackData.executionDetails.executionType == CRMTYPE.EMULATOR){
  //       conversationHistoryString = await this.getConversation(executionId);
  //     }else if(fallbackData.executionDetails.executionType == CRMTYPE.GHL){
  //       conversationHistoryString = await this.ghlService.getConversationString(undefined, fallbackData.executionDetails.executionId);
  //     }

  //     if(editFallback.status == 'acknowledged'){
  //       // upsert the fallback to pinecone with result yes
  //       const fallbackEmbeddings = await this.vectorService.createEmbedding(
  //         secretKey,
  //         finalResponse
  //       );
  //       const fallbackPinecone: IFallbackStructure = {
  //         id: editFallback.fallbackId,
  //         values: fallbackEmbeddings[0].embedding, //this embedding should be the response marked as fallback, not the entire conversation.
  //         metadata: {
  //           executionId: executionId,
  //           agentId: agentId,
  //           result: "yes",
  //           content: conversationHistoryString
  //         }
  //       }
  //       //upsert the fallback to pineccone
  //       await this.upsertFallback(fallbackPinecone, orgid);
  //     }else if(editFallback.status == 'rejected'){
  //       // upsert the fallback to pinecone with result no
  //       const fallbackEmbeddings = await this.vectorService.createEmbedding(
  //         secretKey,
  //         finalResponse
  //       );
  //       const fallbackPinecone: IFallbackStructure = {
  //         id: editFallback.fallbackId,
  //         values: fallbackEmbeddings[0].embedding, //this embedding should be the response marked as fallback, not the entire conversation.
  //         metadata: {
  //           executionId: executionId,
  //           agentId: agentId,
  //           result: "no",
  //           content: conversationHistoryString
  //         }
  //       }
  //       //upsert the fallback to pineccone
  //       await this.upsertFallback(fallbackPinecone, orgid);
  //     }

  //     return true;
  //   } catch (error) {
  //     this.logger.error({
  //       message: `Failed to upsert fallback with id: ${editFallback.fallbackId} with error: ${error.message}`,
  //       context: this.upsertFallbackDetails.name,
  //     });
  //     return false;
  //   }
  // }

  // async updateFallbackStatus(editFallback:EditAcknowledgementFallbackDto, response?) {
  //   try {
  //     const newFallback = await this.mongoFallbackService.updateFallbackStatus(
  //       editFallback,
  //     );

  //     if(newFallback.upsertedCount>0){
  //       const fallbackData = await this.mongoFallbackService.getOneFallback(editFallback.fallbackId);
  //       const upsertCase: boolean = await this.upsertFallbackDetails(editFallback, fallbackData);
  //       if(upsertCase == true){
  //         if (response) {
  //           return response.status(HttpStatus.CREATED).json({
  //             message: 'Fallback status updated successfully',
  //             data: newFallback,
  //           });
  //         }
  //       }else{
  //         if (response) {
  //            this.logger.error({
  //              message: `Fallback status updated successfully, but pinecone upsertion error for fallback id: ${editFallback.fallbackId}`,
  //              context: this.updateFallbackStatus.name,
  //            });
  //           return response.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
  //             message: 'Fallback status updated successfully, but pinecone upsertion error.',
  //             data: newFallback,
  //           });
  //         }
  //       }
  //     }
  //   } catch (error) {
  //     if (response) {
  //        this.logger.error({
  //          message: `Failed to update fallback status with id: ${editFallback.fallbackId} with error: ${error.message}`,
  //          context: this.updateFallbackStatus.name,
  //        });
  //       return response
  //         .status(HttpStatus.INTERNAL_SERVER_ERROR)
  //         .json({ error: 'Internal Server Error' });
  //     }
  //   }
  // }

  // async updateAllFallbackss(response){
  //   try {
  //     const fallback = await this.mongoFallbackService.addExecutionDetailsToAllFallbacks();
  //     return response.json({
  //       message: 'Fallback fetched successfully',
  //       data: fallback,
  //     });
  //   } catch (error) {
  //     
  //     return response.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
  //       error: 'Internal Server Error',
  //     });
  //   }
  // }

}
