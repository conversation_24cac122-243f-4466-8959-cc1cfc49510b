import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Res,
  Delete,
  UseGuards,
  Req,
  Query,
} from '@nestjs/common';
import { Response } from 'express';
import { CreateFallbackDto } from './dto/create.fallback.dto';
import { FallbackService } from './fallback.service';
import { EditAcknowledgementFallbackDto } from './dto/edit.fallback.acknowledge.dto';
import { GetAllFallbackDto } from './dto/get.all.fallback.dto';
import { GetOneFallbackDto } from './dto/get_one_fallback_dto';

@Controller('fallback')
// @UseGuards(AuthGuard)
export class FallbackController {
  constructor(private readonly fallbackService: FallbackService) {}


  @Post('/create')
  async createFallback(
    @Body() createFallbackDto: CreateFallbackDto,
    @Res() response?: Response,
  ) {
    const createdFallback = await this.fallbackService.createFallback(
      createFallbackDto,
      response,
    );
  }


  @Get('/getall')
  async getAllFallback(
    @Query('offset') offset: number,
    @Query('limit') limit: number,
    @Query('orgId') orgId: string,
    @Res() response?: Response,
    ) {
      
    const allFallback = await this.fallbackService.getAllFallback(
      { offset, limit, orgId },
      response,
    );
  }

  @Get('/:id')
  async getOneFallback(@Param('id') id: string, @Res() response: Response) {    
    const agent = await this.fallbackService.getOneFallback(id, response);
  }

  @Post('/updateStatus')
  async updateFallbackStatus(
    @Body() editFallbackDto: EditAcknowledgementFallbackDto,
    @Res() response?: Response,
  ) {
    const updatedFallback = await this.fallbackService.updateFallbackStatus(
      editFallbackDto,
      response,
    );
  }

  // @Post('/111')
  // async updateAllFallbackss( @Res() response: Response) {    
  //   const agent = await this.fallbackService.updateAllFallbackss( response);
  // }
}
