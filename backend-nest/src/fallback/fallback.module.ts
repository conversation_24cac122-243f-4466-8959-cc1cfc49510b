import { Module, forwardRef } from '@nestjs/common';
import { OrganizationService } from 'src/organization/services/organization.service';
import { UserService } from 'src/user/user.service';
import { SheetService } from 'src/organization/services/integrations/sheet/sheet.service';
import { OrganizationModule } from 'src/organization/organization.module';
import { FallbackController } from './fallback.controller';
import { FallbackService } from './fallback.service';
import { SessionController } from 'src/session/session.controller';
import { SessionService } from 'src/session/session.service';
import { Fallback } from 'src/mongo/schemas/fallback/fallback.schema';
import { FallbackSchema } from 'src/mongo/schemas/fallback/fallback.schema';
import { MongooseModule } from '@nestjs/mongoose';
import { MongoFallbackService } from 'src/mongo/service/fallback/mongo-fallback.service';
import { OutreachModule } from 'src/session/outreachService/outreach_module';
import { LoggerModule } from '../logger/logger.module';
import { AiHistoryModule } from 'src/ai_History_Record/aiHistory_module';
import { RebillingModule } from 'src/rebilling/rebilling.module';
import { RebillingService } from 'src/rebilling/rebilling.service';
import { StripeService } from 'src/billing/stripe.service';
import { LangchainReadModule } from 'src/langchain_read_action/langchain_read.module';
import { LangchainWriteModule } from 'src/langchain_write_action/langchain_write.module';
import { OpenaiService } from 'src/ai_response/openai';
import { CacheService } from 'src/utility/services/cache.service';
import { ActionsService } from 'src/organization/services/actions/actions.service';
import { AiModelsModule } from 'src/ai-models/ai-models.module';
import { AuditLogService } from 'src/audit-log/audit-log.service';
import { AuditLogModule } from 'src/audit-log/audit-log.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Fallback.name, schema: FallbackSchema },
    ]),
    OutreachModule,
    LoggerModule,
    AiHistoryModule,
    LangchainReadModule,
    LangchainWriteModule,
    AiModelsModule,
    AuditLogModule
  ],
  controllers: [FallbackController],
  providers: [
    UserService,
    SessionService,
    MongoFallbackService,
    FallbackService,
    RebillingService,
    StripeService,
    OpenaiService,
    CacheService,
    ActionsService,
  ],
  exports: [
    MongoFallbackService,
    MongooseModule.forFeature([
      { name: Fallback.name, schema: FallbackSchema },
    ]),
  ],
})
export class FallbackModule {}
