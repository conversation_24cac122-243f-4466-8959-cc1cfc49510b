import { IsString, IsNotEmpty, IsUrl, ValidateNested, IsOptional, IsObject } from 'class-validator';

export class CreateFallbackDto {

  @IsNotEmpty()
  @IsString()
  readonly agentId: string;

  @IsNotEmpty()
  @IsString()
  readonly orgId: string;

  @IsNotEmpty()
  @IsString()
  readonly providerName: string;

  @IsString()
  readonly dateTime?: Number;

  @IsNotEmpty()
  @IsString()
  readonly contactMessage: string;

  @IsString()
  readonly aiResponse: string;

  @IsObject()
  @IsOptional()
  readonly executionDetails?: {
    executionId: string;
    executionType: string;
  };

  // @IsString()
  // conversation: string;

  @IsString()
  readonly status: 'unread' | 'acknowledged' | 'rejected';

  @IsString()
  readonly refUrl: string;
}
