export interface IContactDetails {
  tags: string[];
  customFields: {
    id?: string;
    value?: string;
    name?: string;
    fieldKey?: string;
  }[];
  contactTimezone: string;
  customValues: {
    id?: string;
    value?: string;
    name?: string;
    fieldKey?: string;
  }[];
  externalAppContextPrompt : string;
  uphexData?: {
    adTitle?: string;
    adMessage?: string;
    adHeadline?: string;
  };
  locationId?: string;
  agentId?: string;
  contactId?: string;
}
  
  export interface IJsonResponseStructure {
    name: string;
    description: string;
    type: any;
    id: string;
    value: string;
  }
  
  export interface EventDataCalendar {
    id: string;
    values: any;
    metadata: {
      type: string;
      agentId: string;
      sessionId: string;
      resourceId: string;
      result: string;
      content: string;
    };
  }
  
  export interface IActionDocument {
    id: string;
    values: any;
    metadata: {
      type: string;
      agentId: string;
      sessionId: string;
      accountId: string;
      result: any;
      content: string;
      activity: string;
      deleted: string;
    };
  }
  
  export interface INonCalendarDocument {
    id: string;
    values: any;
    metadata: {
      type: string;
      agentId: string;
      sessionId: string;
      accountId?: string;
      result: any;
      content: string;
      activity: string;
    };
  }
  
  export interface ITagActionDocument {
    id: string;
    values: any;
    metadata: {
      type: string;
      agentId: string;
      sessionId: string;
      result: any;
      content: string;
      activity: string;
    };
  }
  
  export interface IBotActionResponse {
    eventId: string;
    sender: string;
    kind: string;
    accountName: string;
    accountId: string;
    action: string;
    deleted: boolean;
    eventData: {
      startDate?: any;
      endDate?: any;
      ghlCalendar?: {
        actionType: string;
      }
      tag?: {
        name?: string;
        silent?: boolean;
      };
      properties?: IJsonResponseStructure[];

      customFieldData?: {
        fieldKey: string;
        reply?: boolean;
        evaluateOn?: string;
        field_value: any;
      };
      standardFieldData?: {
        field: {
          fieldKey: string;
          field_value: any;
        }[];
        reply?: boolean;
        evaluateOn?: string;
      };
      slackResponse?: {
        type: string;
        text: string;
        channel_id: string;
      };
      sms?: {
        type: string;
        text: string;
        phoneId?: string;
      };
      ghlEmail?: {
        type: string;
        text?: string;
        subject?: string;
      };
      calendarEvents?: Array<{
        id: string;
        summary: string;
        description?: string;
        location?: string;
        startDateTime: string;
        endDateTime: string;
        attendees: Array<{
          email: string;
          responseStatus: string;
        }>;
        recurringEventId?: string;
      }>;
    };
    timestamp: Number;
    reference?: string;
    calendarId?: string;
    channelId?: string;
    locationId?: string;
    silent?: boolean;
    errors?: string[];
    jsonName?: string;
    jsonDescription?: string;
    jsonType?: string;
    jsonValue?: string;
  }
  
  export interface IAiProviderAdvSettings {
    temperature: Number, 
    maxLength: Number,
    frequencyPenalty: Number
  }
  
  export interface IFallbackStructure {
    id: string,
    values: any,
    metadata: {
      agentId: string,
      executionId: string, //sessionId for emulator and contactId for ghl crm.
      result: string,
      content: string
    }
  }

  export interface IRolloverDetails {
    rollover: boolean,
    rolloverReason: string,
    rolloverDate: string
  }

  export interface AIProvider_aiHistory {
    accountId: string;
    credentialId: string;
    modelType: string;
    providerName: string;
}

  export interface Channel_aiHistory {
      channelName: string; // account name | channel name
      channelAccountId: string; // specific to the channel
      resourceId: string; // specific to the contact or the session
  }

  export interface RequestDetails_aiHistory {
      query: string; // full prompt
      totalTokens_req: number;
  }

  export interface ResponseDetail_aiHistory {
      actionType: string; // eg. ghl calendar, sheet, etc.
      kind: 'read' | 'write' | 'ghl' | 'message' | 'outreach' | 'optimize';
      totalTokens_res: number;
      eventData: Object;
  }

  export interface Action {
    eventId: string;
    aiProvider: AIProvider_aiHistory;
    request: RequestDetails_aiHistory;
    response: ResponseDetail_aiHistory;
    cost: number;
  }

  export interface AiHistoryRecordDto {
    agentId: string;
    orgId: string;
    createdAt: Date;
    updatedAt: Date;
    channel: Channel_aiHistory;
    actions: {
        eventId: string;
        aiProvider: AIProvider_aiHistory;
        request: RequestDetails_aiHistory;
        response: ResponseDetail_aiHistory;
    }[];
  }

  export interface IUserValues {
    prompt: string;
    key: string;
    model_type: string;
    temperature: number;
    max_length: number;
    task: string;
    channel: string;
    knowledge: string;
    fallback: string;
    handoff: string;
    disqualify: string;
    tag: string;
    goal: string;
    booking_action: string;
    calendar: string;
    history: string;
    actions_model: string;
    actions_api_key: string;
    language: string;
    timezone: string;
    training: string;
    tokens: {
        access_token: string;
        refresh_token: string;
    };
    frequency_penalty: string;
    locationId: string;
    proxied: string;
    contactId: string;
}