import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { HttpRequest, HttpRequestSchema } from './http-request.schema';
import { HttpRequestGetController } from './GET/get.controller';
import { HttpRequestGetService } from './GET/get.service';
import { HttpRequestPostController } from './POST/post.controller';
import { HttpRequestPostService } from './POST/post.service';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: HttpRequest.name, schema: HttpRequestSchema },
    ]),
  ],
  controllers: [HttpRequestGetController, HttpRequestPostController],
  providers: [HttpRequestGetService, HttpRequestPostService],
  exports: [HttpRequestGetService, HttpRequestPostService],
})
export class HttpRequestModule {} 