import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export type HttpRequestDocument = HttpRequest & Document;

@Schema()
export class HttpRequest {
  @Prop()
  accountId: string;

  @Prop({ required: true })
  name: string;

  @Prop({ required: true })
  orgId: string;

  @Prop({ required: true })
  url: string;

  @Prop({ required: true })
  author: string;

  @Prop({ enum: ['GET', 'POST', 'PUT', 'DELETE'], default: 'GET' })
  requestType: string;

  @Prop({ type: [{ name: String, value: String, description: String }] })
  queryParameters: { name: string; value: string; description?: string }[];

  @Prop({ type: [{ name: String, value: String, description: String }] })
  pathVariables: { name: string; value: string; description?: string }[];

  @Prop({ type: [{ name: String, value: String, description: String }] })
  headers: { name: string; value: string; description?: string }[];

  // Body parameters for POST/PUT requests
  @Prop({ type: [{ name: String, value: String, description: String }] })
  bodyParameters: { name: string; value: string; description?: string }[];

  // Body content type for POST/PUT requests
  @Prop({ enum: ['application/json', 'application/x-www-form-urlencoded'], default: 'application/json' })
  bodyType: string;

  @Prop({ required: true, enum: ['No Auth', 'API Key', 'OAuth 2.0'] })
  authorizationType: 'No Auth' | 'API Key' | 'OAuth 2.0';

  @Prop({ type: { key: String, value: String, addTo: String } })
  apiKey?: { key: string; value: string; addTo: 'Header' | 'Query' };

  @Prop()
  responseBuilder: string;
}

export const HttpRequestSchema = SchemaFactory.createForClass(HttpRequest); 