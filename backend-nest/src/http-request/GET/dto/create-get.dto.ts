import { ApiProperty } from '@nestjs/swagger';

class QueryParameter {
  @ApiProperty()
  name: string;

  @ApiProperty()
  value: string;

  @ApiProperty({ required: false })
  description?: string;
}

class PathVariable {
  @ApiProperty()
  name: string;

  @ApiProperty()
  value: string;

  @ApiProperty({ required: false })
  description?: string;
}

class Header {
  @ApiProperty()
  name: string;

  @ApiProperty()
  value: string;

  @ApiProperty({ required: false })
  description?: string;
}

class BodyParameter {
  @ApiProperty()
  name: string;

  @ApiProperty()
  value: string;

  @ApiProperty({ required: false })
  description?: string;
}

class ApiKey {
  @ApiProperty()
  key: string;

  @ApiProperty()
  value: string;

  @ApiProperty({ enum: ['Header', 'Query'] })
  addTo: 'Header' | 'Query';
}

export class CreateHttpGetRequestDto {
  @ApiProperty()
  name: string;

  @ApiProperty()
  orgId: string;

  @ApiProperty()
  url: string;

  @ApiProperty()
  author: string;

  //add a default value
  @ApiProperty({
    default: 'GET',
    enum: ['GET', 'POST', 'PUT', 'DELETE'],
  })
  requestType: string;

  @ApiProperty({ type: [QueryParameter] })
  queryParameters: QueryParameter[];

  @ApiProperty({ type: [PathVariable] })
  pathVariables: PathVariable[];

  @ApiProperty({ type: [Header] })
  headers: Header[];

  @ApiProperty({ type: [BodyParameter], required: false })
  bodyParameters?: BodyParameter[];

  @ApiProperty({ 
    enum: ['application/json', 'application/x-www-form-urlencoded'], 
    required: false,
    default: 'application/json'
  })
  bodyType?: string;

  @ApiProperty({ enum: ['No Auth', 'API Key', 'OAuth 2.0'] })
  authorizationType: 'No Auth' | 'API Key' | 'OAuth 2.0';

  @ApiProperty({ required: false, type: ApiKey })
  apiKey?: ApiKey;

  @ApiProperty({ required: false })
  responseBuilder: string;
}
