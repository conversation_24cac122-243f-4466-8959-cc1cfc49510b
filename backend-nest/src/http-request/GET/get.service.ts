import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { HttpRequest, HttpRequestDocument } from './schema/get.schema';
import { CreateHttpGetRequestDto } from './dto/create-get.dto';
import { UpdateHttpGetRequestDto } from './dto/update-get.dto';

@Injectable()
export class HttpRequestGetService {
  constructor(
    @InjectModel(HttpRequest.name)
    private httpRequestModel: Model<HttpRequestDocument>,
  ) {}

  async create(createHttpRequestDto: CreateHttpGetRequestDto) {
    const createdHttpRequest = new this.httpRequestModel({
      ...createHttpRequestDto,
      requestType: 'GET'
    });
    let save = createdHttpRequest.save();

    //need the _id to be returned
    return save;
  }

  async findAll(): Promise<HttpRequest[]> {
    return this.httpRequestModel.find({ requestType: 'GET' }).exec();
  }

  async findOne(
    id?: string,
    orgId?: string,
    accountId?: string,
  ): Promise<HttpRequest | null> {
    const filter: {
      _id?: string;
      orgId?: string;
      accountId?: string;
      requestType: string;
    } = {
      requestType: 'GET'
    };

    if (id) filter._id = id;
    if (orgId) filter.orgId = orgId;
    if (accountId) filter.accountId = accountId;

    if (Object.keys(filter).length === 1) {
      throw new Error(
        'At least one parameter (id, orgId, or accountId) is required',
      );
    }

    return this.httpRequestModel.findOne(filter).exec();
  }

  async update(
    id?: string,
    orgId?: string,
    accountId?: string,
    updateHttpRequestDto?: UpdateHttpGetRequestDto,
  ): Promise<HttpRequest | null> {
    const filter: {
      _id?: string;
      orgId?: string;
      accountId?: string;
      requestType: string;
    } = {
      requestType: 'GET'
    };

    if (id) filter._id = id;
    if (orgId) filter.orgId = orgId;
    if (accountId) filter.accountId = accountId;

    if (Object.keys(filter).length === 1) {
      throw new Error(
        'At least one parameter (id, orgId, or accountId) is required',
      );
    }

    return this.httpRequestModel
      .findOneAndUpdate(filter, updateHttpRequestDto, { new: true })
      .exec();
  }

  async remove(
    id?: string,
    orgId?: string,
    accountId?: string,
  ): Promise<HttpRequest | null> {
    const filter: any = {
      requestType: 'GET'
    };

    if (id) filter._id = id;
    if (orgId) filter.orgId = orgId;
    if (accountId) filter.accountId = accountId;

    if (Object.keys(filter).length === 1) {
      throw new Error(
        'At least one parameter (id, orgId, or accountId) is required',
      );
    }

    return this.httpRequestModel.findOneAndDelete(filter).exec();
  }
}
