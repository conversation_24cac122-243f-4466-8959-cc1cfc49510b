import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
} from '@nestjs/common';

import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { CreateHttpGetRequestDto } from './dto/create-get.dto';
import { UpdateHttpGetRequestDto } from './dto/update-get.dto';
import { HttpRequestGetService } from './get.service';

@ApiTags('http-requests')
@Controller('http-requests')
export class HttpRequestGetController {
  constructor(private readonly httpRequestService: HttpRequestGetService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new HTTP request' })
  @ApiResponse({
    status: 201,
    description: 'The HTTP request has been successfully created.',
  })
  create(@Body() createHttpRequestDto: CreateHttpGetRequestDto) {
    return this.httpRequestService.create(createHttpRequestDto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all HTTP requests' })
  @ApiResponse({ status: 200, description: 'Return all HTTP requests.' })
  findAll() {
    return this.httpRequestService.findAll();
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a single HTTP request' })
  @ApiResponse({ status: 200, description: 'Return the HTTP request.' })
  @ApiResponse({ status: 404, description: 'HTTP request not found.' })
  findOne(@Param('id') id: string) {
    return this.httpRequestService.findOne(id);
  }

  // @Patch(':id')
  // @ApiOperation({ summary: 'Update a HTTP request' })
  // @ApiResponse({
  //   status: 200,
  //   description: 'The HTTP request has been successfully updated.',
  // })
  // @ApiResponse({ status: 404, description: 'HTTP request not found.' })
  // update(
  //   @Param('id') id: string,
  //   @Body() updateHttpRequestDto: UpdateHttpGetRequestDto,
  // ) {
  //   return this.httpRequestService.update(id, updateHttpRequestDto);
  // }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a HTTP request' })
  @ApiResponse({
    status: 200,
    description: 'The HTTP request has been successfully deleted.',
  })
  @ApiResponse({ status: 404, description: 'HTTP request not found.' })
  remove(@Param('id') id: string) {
    return this.httpRequestService.remove(id);
  }
}
