import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';;
import { HttpRequest, HttpRequestSchema } from './schema/get.schema';
import { HttpRequestGetController } from './get.controller';
import { HttpRequestGetService } from './get.service';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: HttpRequest.name, schema: HttpRequestSchema },
    ]),
  ],
  controllers: [HttpRequestGetController],
  providers: [HttpRequestGetService],
  exports: [HttpRequestGetService],
})
export class GetModule {}
