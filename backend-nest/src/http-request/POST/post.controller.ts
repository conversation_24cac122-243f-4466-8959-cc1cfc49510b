import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
} from '@nestjs/common';

import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { CreateHttpPostRequestDto } from './dto/create-post.dto';
import { UpdateHttpPostRequestDto } from './dto/update-post.dto';
import { HttpRequestPostService } from './post.service';

@ApiTags('http-post-requests')
@Controller('http-post-requests')
export class HttpRequestPostController {
  constructor(private readonly httpRequestPostService: HttpRequestPostService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new HTTP POST request' })
  @ApiResponse({
    status: 201,
    description: 'The HTTP POST request has been successfully created.',
  })
  create(@Body() createHttpPostRequestDto: CreateHttpPostRequestDto) {
    return this.httpRequestPostService.create(createHttpPostRequestDto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all HTTP POST requests' })
  @ApiResponse({ status: 200, description: 'Return all HTTP POST requests.' })
  findAll() {
    return this.httpRequestPostService.findAll();
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a single HTTP POST request' })
  @ApiResponse({ status: 200, description: 'Return the HTTP POST request.' })
  @ApiResponse({ status: 404, description: 'HTTP POST request not found.' })
  findOne(@Param('id') id: string) {
    return this.httpRequestPostService.findOne(id);
  }

  // @Patch(':id')
  // @ApiOperation({ summary: 'Update a HTTP POST request' })
  // @ApiResponse({
  //   status: 200,
  //   description: 'The HTTP POST request has been successfully updated.',
  // })
  // @ApiResponse({ status: 404, description: 'HTTP POST request not found.' })
  // update(
  //   @Param('id') id: string,
  //   @Body() updateHttpPostRequestDto: UpdateHttpPostRequestDto,
  // ) {
  //   return this.httpRequestPostService.update(id, updateHttpPostRequestDto);
  // }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a HTTP POST request' })
  @ApiResponse({
    status: 200,
    description: 'The HTTP POST request has been successfully deleted.',
  })
  @ApiResponse({ status: 404, description: 'HTTP POST request not found.' })
  remove(@Param('id') id: string) {
    return this.httpRequestPostService.remove(id);
  }
} 