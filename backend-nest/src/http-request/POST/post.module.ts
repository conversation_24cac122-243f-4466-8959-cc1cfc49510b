import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { HttpPostRequest, HttpPostRequestSchema } from './schema/post.schema';
import { HttpRequestPostController } from './post.controller';
import { HttpRequestPostService } from './post.service';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: HttpPostRequest.name, schema: HttpPostRequestSchema },
    ]),
  ],
  controllers: [HttpRequestPostController],
  providers: [HttpRequestPostService],
  exports: [HttpRequestPostService],
})
export class PostModule {} 