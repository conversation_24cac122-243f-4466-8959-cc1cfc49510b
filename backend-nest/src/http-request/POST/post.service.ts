import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { HttpPostRequest, HttpPostRequestDocument } from './schema/post.schema';
import { CreateHttpPostRequestDto } from './dto/create-post.dto';
import { UpdateHttpPostRequestDto } from './dto/update-post.dto';

@Injectable()
export class HttpRequestPostService {
  constructor(
    @InjectModel(HttpPostRequest.name)
    private httpPostRequestModel: Model<HttpPostRequestDocument>,
  ) {}

  async create(createHttpPostRequestDto: CreateHttpPostRequestDto) {
    const createdHttpPostRequest = new this.httpPostRequestModel({
      ...createHttpPostRequestDto,
      requestType: 'POST'
    });
    let save = createdHttpPostRequest.save();

    //need the _id to be returned
    return save;
  }

  async findAll(): Promise<HttpPostRequest[]> {
    return this.httpPostRequestModel.find({ requestType: 'POST' }).exec();
  }

  async findOne(
    id?: string,
    orgId?: string,
    accountId?: string,
  ): Promise<HttpPostRequest | null> {
    const filter: {
      _id?: string;
      orgId?: string;
      accountId?: string;
      requestType: string;
    } = {
      requestType: 'POST'
    };

    if (id) filter._id = id;
    if (orgId) filter.orgId = orgId;
    if (accountId) filter.accountId = accountId;

    if (Object.keys(filter).length === 1) {
      throw new Error(
        'At least one parameter (id, orgId, or accountId) is required',
      );
    }

    return this.httpPostRequestModel.findOne(filter).exec();
  }

  async update(
    id?: string,
    orgId?: string,
    accountId?: string,
    updateHttpPostRequestDto?: UpdateHttpPostRequestDto,
  ): Promise<HttpPostRequest | null> {
    const filter: {
      _id?: string;
      orgId?: string;
      accountId?: string;
      requestType: string;
    } = {
      requestType: 'POST'
    };

    if (id) filter._id = id;
    if (orgId) filter.orgId = orgId;
    if (accountId) filter.accountId = accountId;

    if (Object.keys(filter).length === 1) {
      throw new Error(
        'At least one parameter (id, orgId, or accountId) is required',
      );
    }

    return this.httpPostRequestModel
      .findOneAndUpdate(filter, updateHttpPostRequestDto, { new: true })
      .exec();
  }

  async remove(
    id?: string,
    orgId?: string,
    accountId?: string,
  ): Promise<HttpPostRequest | null> {
    const filter: any = {
      requestType: 'POST'
    };

    if (id) filter._id = id;
    if (orgId) filter.orgId = orgId;
    if (accountId) filter.accountId = accountId;

    if (Object.keys(filter).length === 1) {
      throw new Error(
        'At least one parameter (id, orgId, or accountId) is required',
      );
    }

    return this.httpPostRequestModel.findOneAndDelete(filter).exec();
  }
} 