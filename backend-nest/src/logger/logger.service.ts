import { Logger, Injectable, Scope, Inject } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { Request } from 'express'; 

@Injectable({ scope: Scope.REQUEST })
export class MyLogger extends Logger {
  constructor(@Inject(REQUEST) private request: Request) {
    super();
  }

  log({ message, context }: { message: any; context?: string }): void {
    const uniqueCode = this.request.uniqueCode;
    const logObj = { uniqueCode, message };

    // Existing behavior
    super.log(JSON.stringify(logObj), context);
  }

  error({ message, context }: { message: any; context?: string }): void {
    const uniqueCode = this.request.uniqueCode;
    const logObj = { uniqueCode, message };

    // Existing behavior
    super.error(JSON.stringify(logObj), context);
  }
}