import * as dotenv from 'dotenv';
import * as fs from 'fs';
import * as path from 'path';

export function loadEnvironmentVariables() {
  // First check for ENVIRONMENT in process.env (from system environment variables)
  const environment = process.env.ENVIRONMENT?.toLowerCase() || 'beta';
  
  // Path to the environment-specific file
  let envFilePath: string;
  
  if (environment === 'staging' || environment === "STAGING") {
    envFilePath = path.resolve(process.cwd(), '.env.staging');
    
  } else {
    // Default to production or use a specific production env file
    envFilePath = path.resolve(process.cwd(), '.env');
    
  }

  // Check if the file exists
  if (fs.existsSync(envFilePath)) {
    // Load ONLY the environment-specific variables, don't load .env first
    dotenv.config({ path: envFilePath });
    
  } else {
    console.warn(`Environment file not found: ${envFilePath}`);
    // Fall back to default .env if the specific one doesn't exist
    dotenv.config();
  }
}
