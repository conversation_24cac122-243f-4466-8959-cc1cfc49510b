import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Post,
  HttpException,
  Query,
} from '@nestjs/common';
import { TokensService } from './tokens.service';
import { CreateTokenDto } from './dto/create-token.dto';
import { ResponseTokenDto } from './dto/response-token.dto';
import { MyLogger } from '../logger/logger.service';

@Controller('tokens')
export class TokensController {
  constructor(private readonly tokensService: TokensService,
    private readonly logger: MyLogger) {}

  @Post('/:orgId')
  @HttpCode(HttpStatus.CREATED)
  async createToken(
    @Body() createTokenDto: CreateTokenDto,
    @Param('orgId') orgId: string,
  ): Promise<ResponseTokenDto> {
    const { name } = createTokenDto;

    this.logger.log({
      message: `Creating token for ${name} for org ${orgId}`,
      context: this.createToken.name,
    });

    const createdToken = await this.tokensService.createToken({
      name,
      orgId,
    });

    return new ResponseTokenDto(createdToken, 'Token created successfully');
  }

  @Get()
  async getToken(
    @Query() query: {tokenVal: string}
  ) {
    return await this.tokensService.getTokenByToken({
      token: query.tokenVal
    })
  }

  @Get('/:orgId')
  async getAllTokens(
    @Param('orgId') orgId: string,
  ) {
    const tokens = await this.tokensService.getAllTokens({
      orgId,
    });

    return tokens;
  }

  @Get('/:orgId/:id')
  async getTokenById(
    @Param('orgId') orgId: string,
    @Param('id') id: string,
  ): Promise<ResponseTokenDto> {
    const token = await this.tokensService.getTokenById({
      orgId,
      id,
    });
    if (!token) {
      this.logger.log({
        message: `Token not found for org ${orgId} and id ${id}`,
        context: this.getTokenById.name,
      });
      throw new HttpException('Token not found', HttpStatus.NOT_FOUND);
    }
    return new ResponseTokenDto(token, 'Token retrieved successfully');
  }

  @Delete('/:orgId/:id')
  async deleteTokenById(
    @Param('orgId') orgId: string,
    @Param('id') id: string,
  ): Promise<ResponseTokenDto> {
    const deletedToken = await this.tokensService.deleteTokenById({
      orgId,
      id,
    });
    if (!deletedToken) {
       this.logger.log({
         message: `Token not found for org ${orgId} and id ${id}`,
         context: this.deleteTokenById.name,
       });
      throw new HttpException('Token not found', HttpStatus.NOT_FOUND);
    }
    return new ResponseTokenDto(deletedToken, 'Token deleted successfully');
  }
}
