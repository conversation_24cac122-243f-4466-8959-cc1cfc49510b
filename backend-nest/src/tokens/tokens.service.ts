import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import getId from 'get-short-id';
import { Token, TokenDocument } from './schema/token.schema';

@Injectable()
export class TokensService {
  constructor(
    @InjectModel(Token.name) private tokenModel: Model<TokenDocument>,
  ) {}

  async createToken({
    name,
    orgId,
  }: {
    name: string;
    orgId: string;
  }): Promise<Token> {
    const token = getId({ count: 50 });
    return await this.tokenModel.create({ orgId, name, token });
  }

  async getAllTokens({ orgId }: { orgId: string }): Promise<Token[]> {
    return await this.tokenModel.find({ orgId });
  }

  async getTokenById({
    orgId,
    id,
  }: {
    orgId: string;
    id: string;
  }): Promise<Token | null> {
    return await this.tokenModel.findOne({ orgId, _id: id });
  }

  async deleteTokenById({
    orgId,
    id,
  }: {
    orgId: string;
    id: string;
  }): Promise<Token | null> {
    return await this.tokenModel.findOneAndDelete({ orgId, _id: id });
  }

  async getTokenByToken({ token }: { token: string }): Promise<Token | null> {
    return await this.tokenModel.findOne({ token });
  }

  async validateToken(token: string) {
    return await this.tokenModel.findOne({ token });
  }
}
