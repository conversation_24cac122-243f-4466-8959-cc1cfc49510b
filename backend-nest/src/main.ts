import 'dotenv/config';
import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { ValidationPipe } from '@nestjs/common';
import { configureSwaggerUI } from './configure-swagger-ui';
import * as session from 'express-session';
import * as passport from 'passport';
import { NestExpressApplication } from '@nestjs/platform-express';
import { resolve } from 'path';
import * as cookieParser from 'cookie-parser';
import { HttpExceptionFilter } from './filters/http-exception.filter';
import { RequestService } from './request.service';
import { MyLogger } from './utils/logger';
import { urlencoded } from 'express';
import { loadEnvironmentVariables } from './envConfig';

async function bootstrap() {
  try {
    // loadEnvironmentVariables();

    const app = await NestFactory.create<NestExpressApplication>(AppModule, {
      rawBody: true,
    });
    app.useBodyParser('json', { limit: '50mb' });
    app.use(urlencoded({ limit: '50mb', extended: true }));
    const requestService = await app.resolve(RequestService);
    const logger = await app.resolve(MyLogger);
    app.useGlobalFilters(new HttpExceptionFilter(requestService, logger));
    // Include FRONTEND_URL and the IFRAME_URL
    app.enableCors({
      origin: [process.env.FRONTEND_URL, process.env.IFRAME_URL, 'http://localhost:3000'],
      credentials: true,
    });
    app.use(cookieParser());
    app.useGlobalPipes(new ValidationPipe());
    // app.useGlobalFilters(new RedirectFilter());
    app.use(
      session({
        name: 'CAPRI_SESSION_ID',
        secret: 'aiolsncoasncoiqnwONLWCOEI',
        resave: false,
        saveUninitialized: false,
        cookie: {
          maxAge: 60000,
        },
      }),
    );
    app.use(passport.initialize());
    app.use(passport.session());
    // app.useStaticAssets(join(__dirname, '..', 'static'));
    // app.setBaseViewsDir(join(__dirname, '..', 'views'));
    app.useStaticAssets(resolve('./src/public'));
    app.setBaseViewsDir(resolve('./src/views'));
    app.setViewEngine('ejs');

    configureSwaggerUI(app, {
      swaggerEnabled: true,
      swaggerPath: '/swagger',
      appName: 'Capri V3',
      apiUrl: 'http://localhost:3333',
      apiEnvironmentName: process.env.NODE_ENV,
    });

    const port = process.env.PORT || 3333;
    await app.listen(port);
    console.log(`Application is running on port ${port}`);

    console.log("Testing again")

    // Handle graceful shutdown
    process.on('SIGTERM', async () => {
      console.log('SIGTERM received, shutting down gracefully');
      try {
        await app.close();
        process.exit(0);
      } catch (error) {
        console.error('Error during graceful shutdown:', error);
        process.exit(1);
      }
    });

    process.on('SIGINT', async () => {
      console.log('SIGINT received, shutting down gracefully');
      try {
        await app.close();
        process.exit(0);
      } catch (error) {
        console.error('Error during graceful shutdown:', error);
        process.exit(1);
      }
    });

  } catch (error) {
    console.error('Error starting the application:', error);
    process.exit(1);
  }
}

// Handle uncaught exceptions - exit with code 1 to trigger restart
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
  console.error('Stack:', error.stack);
  process.exit(1);
});

// Handle unhandled promise rejections - exit with code 1 to trigger restart
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

bootstrap();
