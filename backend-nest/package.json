{"name": "backend-nest", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "gcp-build": "npm run build", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@anthropic-ai/sdk": "^0.17.1", "@aws-sdk/client-bedrock-runtime": "^3.507.0", "@chathq-oss/app-sdk-node": "^0.4.0", "@google-ai/generativelanguage": "^1.1.0", "@google-cloud/storage": "^7.7.0", "@google-cloud/tasks": "^3.2.0", "@google/generative-ai": "^0.11.5", "@langchain/core": "^0.1.13", "@langchain/mistralai": "^0.0.6", "@langchain/openai": "^0.0.12", "@logtail/node": "^0.5.4", "@nestjs/axios": "^3.1.3", "@nestjs/bull": "^10.2.1", "@nestjs/cache-manager": "^2.2.2", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.3.0", "@nestjs/core": "^10.0.0", "@nestjs/jwt": "^10.1.0", "@nestjs/mapped-types": "*", "@nestjs/mongoose": "^10.0.0", "@nestjs/passport": "^10.0.0", "@nestjs/platform-express": "^10.4.1", "@nestjs/platform-socket.io": "^10.4.5", "@nestjs/schedule": "^4.0.0", "@nestjs/serve-static": "^4.0.0", "@nestjs/swagger": "^7.0.3", "@nestjs/throttler": "^5.1.1", "@nestjs/websockets": "^10.4.5", "@pinecone-database/doc-splitter": "^0.0.1", "@pinecone-database/pinecone": "^0.1.6", "@types/multer": "^1.4.11", "acuityscheduling": "^0.1.9", "auth0": "^4.14.0", "axios": "^1.7.9", "bull": "^4.16.0", "cache-manager": "^5.5.3", "cache-manager-redis-store": "^3.0.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "cookie-parser": "^1.4.6", "date-fns": "^2.30.0", "date-fns-tz": "^1.3.7", "ejs": "^3.1.9", "express": "^4.18.2", "express-session": "^1.17.3", "form-data": "^4.0.1", "get-short-id": "^3.0.10", "google-auth-library": "^8.8.0", "google-spreadsheet": "^4.0.2", "googleapis": "^120.0.0", "groq-sdk": "^0.3.2", "jsdom": "^24.0.0", "jsonwebtoken": "^9.0.0", "luxon": "^3.4.2", "mammoth": "^1.8.0", "md5": "^2.3.0", "moment": "^2.30.1", "moment-timezone": "^0.5.45", "mongodb": "^5.6.0", "mongoose": "^7.3.0", "nylas": "^7.7.1", "openai": "^3.3.0", "openai-v4": "npm:openai@^4.28.0", "passport": "^0.6.0", "passport-jwt": "^4.0.1", "pdf-parse": "^1.1.1", "pinecone-database-v3": "npm:@pinecone-database/pinecone@^3.0.0", "redis": "^4.7.0", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "socket.io": "^4.8.0", "stripe": "^13.8.0", "tiktoken": "^1.0.10", "twilio": "^5.3.5", "uuid": "^9.0.0", "xlsx": "^0.18.5"}, "devDependencies": {"@golevelup/ts-jest": "^0.3.8", "@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/bull": "^4.10.0", "@types/dotenv": "^8.2.0", "@types/express": "^4.17.21", "@types/express-session": "^1.17.7", "@types/jest": "^29.5.2", "@types/jsdom": "^21.1.6", "@types/md5": "^2.3.5", "@types/moment-timezone": "^0.5.30", "@types/node": "^20.3.1", "@types/passport": "^1.0.12", "@types/pdf-parse": "^1.1.4", "@types/supertest": "^2.0.12", "@typescript-eslint/eslint-plugin": "^5.59.11", "@typescript-eslint/parser": "^5.59.11", "dotenv": "^16.3.1", "eslint": "^8.42.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^4.2.1", "jest": "^29.5.0", "madge": "^6.1.0", "prettier": "^2.8.8", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}