#!/bin/bash

# Capri POST Request Test Script
# This script tests the POST request functionality

echo "🚀 Starting Capri POST Request Tests..."
echo "======================================="

# Configuration
BASE_URL="http://localhost:3000"
TEST_ENDPOINT="$BASE_URL/session/test/httpPost"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to test POST request
test_post_request() {
    local test_name="$1"
    local request_data="$2"
    
    echo -e "\n${BLUE}Testing: $test_name${NC}"
    echo "------------------------"
    
    response=$(curl -s -w "HTTPSTATUS:%{http_code}" -X POST "$TEST_ENDPOINT" \
        -H "Content-Type: application/json" \
        -d "$request_data")
    
    http_code=$(echo "$response" | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
    body=$(echo "$response" | sed -e 's/HTTPSTATUS:.*//g')
    
    if [ "$http_code" -eq 200 ]; then
        echo -e "${GREEN}✅ Test passed (HTTP $http_code)${NC}"
        echo "Response: $body" | jq '.' 2>/dev/null || echo "Response: $body"
    else
        echo -e "${RED}❌ Test failed (HTTP $http_code)${NC}"
        echo "Response: $body"
    fi
}

# Test 1: JSONPlaceholder API
echo -e "\n${YELLOW}Test 1: JSONPlaceholder Create Post${NC}"
test_post_request "JSONPlaceholder Create Post" '{
    "url": "https://jsonplaceholder.typicode.com/posts",
    "bodyParameters": [
        {"name": "title", "value": "Test Post from Capri", "description": "Post title"},
        {"name": "body", "value": "This is a test post created by Capri backend", "description": "Post content"},
        {"name": "userId", "value": "1", "description": "User ID"}
    ],
    "headers": [
        {"name": "Content-Type", "value": "application/json"}
    ],
    "queryParameters": [],
    "bodyType": "application/json"
}'

# Test 2: HTTPBin Echo
echo -e "\n${YELLOW}Test 2: HTTPBin Echo Test${NC}"
test_post_request "HTTPBin Echo" '{
    "url": "https://httpbin.org/post",
    "bodyParameters": [
        {"name": "message", "value": "Hello from Capri!", "description": "Test message"},
        {"name": "timestamp", "value": "'$(date -u +"%Y-%m-%dT%H:%M:%SZ")'", "description": "Current timestamp"}
    ],
    "headers": [
        {"name": "Content-Type", "value": "application/json"},
        {"name": "User-Agent", "value": "Capri-Test/1.0"}
    ],
    "queryParameters": [
        {"name": "test", "value": "automated", "description": "Test type"}
    ],
    "bodyType": "application/json"
}'

# Test 3: Form Data
echo -e "\n${YELLOW}Test 3: Form Data Test${NC}"
test_post_request "Form Data" '{
    "url": "https://httpbin.org/post",
    "bodyParameters": [
        {"name": "username", "value": "capri_test", "description": "Username"},
        {"name": "action", "value": "test_form_submission", "description": "Action type"}
    ],
    "headers": [
        {"name": "Content-Type", "value": "application/x-www-form-urlencoded"}
    ],
    "queryParameters": [],
    "bodyType": "application/x-www-form-urlencoded"
}'

# Test 4: Error handling (invalid URL)
echo -e "\n${YELLOW}Test 4: Error Handling (Invalid URL)${NC}"
test_post_request "Invalid URL" '{
    "url": "https://invalid-url-that-does-not-exist.com/api",
    "bodyParameters": [
        {"name": "test", "value": "error", "description": "Test parameter"}
    ],
    "headers": [
        {"name": "Content-Type", "value": "application/json"}
    ],
    "bodyType": "application/json"
}'

echo -e "\n${GREEN}🏁 All tests completed!${NC}"
echo "======================================="
echo -e "${BLUE}💡 Tips:${NC}"
echo "- Check server logs for detailed request information"
echo "- Successful tests should return success: true"
echo "- Failed tests will show error details"
echo "- Use 'jq' for better JSON formatting: brew install jq"
